"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[9879],{34735:(e,t,n)=>{n.d(t,{A:()=>I});var r=n(89379),o=n(58168),i=n(82284),a=n(5544),u=n(53986),l=n(96540),s=n(45062),c=n(62427),f=n(46942),d=n.n(f),h=n(30981),v=n(12533);function m(e){return null!==e&&"object"===(0,i.A)(e)}function g(e,t,n){if(!1===e||!1===t&&(!m(e)||!e.closeIcon))return null;var o,i="boolean"!=typeof t?t:void 0;return m(e)?(0,r.A)((0,r.A)({},e),{},{closeIcon:null!==(o=e.closeIcon)&&void 0!==o?o:i}):n||e||t?{closeIcon:i}:"empty"}var p=n(26956);var y=n(56855),A={fill:"transparent",pointerEvents:"auto"};const E=function(e){var t=e.prefixCls,n=e.rootClassName,a=e.pos,u=e.showMask,c=e.style,f=void 0===c?{}:c,h=e.fill,v=void 0===h?"rgba(0,0,0,0.5)":h,m=e.open,g=e.animated,p=e.zIndex,E=e.disabledInteraction,b=(0,y.A)(),w="".concat(t,"-mask-").concat(b),C="object"===(0,i.A)(g)?null==g?void 0:g.placeholder:g,N="undefined"!=typeof navigator&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent)?{width:"100%",height:"100%"}:{width:"100vw",height:"100vh"};return l.createElement(s.A,{open:m,autoLock:!0},l.createElement("div",{className:d()("".concat(t,"-mask"),n),style:(0,r.A)({position:"fixed",left:0,right:0,top:0,bottom:0,zIndex:p,pointerEvents:a&&!E?"none":"auto"},f)},u?l.createElement("svg",{style:{width:"100%",height:"100%"}},l.createElement("defs",null,l.createElement("mask",{id:w},l.createElement("rect",(0,o.A)({x:"0",y:"0"},N,{fill:"white"})),a&&l.createElement("rect",{x:a.left,y:a.top,rx:a.radius,width:a.width,height:a.height,fill:"black",className:C?"".concat(t,"-placeholder-animated"):""}))),l.createElement("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:v,mask:"url(#".concat(w,")")}),a&&l.createElement(l.Fragment,null,l.createElement("rect",(0,o.A)({},A,{x:"0",y:"0",width:"100%",height:a.top})),l.createElement("rect",(0,o.A)({},A,{x:"0",y:"0",width:a.left,height:"100%"})),l.createElement("rect",(0,o.A)({},A,{x:"0",y:a.top+a.height,width:"100%",height:"calc(100vh - ".concat(a.top+a.height,"px)")})),l.createElement("rect",(0,o.A)({},A,{x:a.left+a.width,y:"0",width:"calc(100vw - ".concat(a.left+a.width,"px)"),height:"100%"})))):null))};var b=[0,0],w={left:{points:["cr","cl"],offset:[-8,0]},right:{points:["cl","cr"],offset:[8,0]},top:{points:["bc","tc"],offset:[0,-8]},bottom:{points:["tc","bc"],offset:[0,8]},topLeft:{points:["bl","tl"],offset:[0,-8]},leftTop:{points:["tr","tl"],offset:[-8,0]},topRight:{points:["br","tr"],offset:[0,-8]},rightTop:{points:["tl","tr"],offset:[8,0]},bottomRight:{points:["tr","br"],offset:[0,8]},rightBottom:{points:["bl","br"],offset:[8,0]},bottomLeft:{points:["tl","bl"],offset:[0,8]},leftBottom:{points:["br","bl"],offset:[-8,0]}};function C(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t={};return Object.keys(w).forEach((function(n){t[n]=(0,r.A)((0,r.A)({},w[n]),{},{autoArrow:e,targetOffset:b})})),t}C();var N=n(60436),x=n(72065);function M(e){var t,n=e.prefixCls,r=e.current,i=e.total,a=e.title,u=e.description,s=e.onClose,c=e.onPrev,f=e.onNext,h=e.onFinish,v=e.className,m=e.closable,g=(0,x.A)(m||{},!0),p=null!==(t=null==m?void 0:m.closeIcon)&&void 0!==t?t:l.createElement("span",{className:"".concat(n,"-close-x")},"×"),y=!!m;return l.createElement("div",{className:d()("".concat(n,"-content"),v)},l.createElement("div",{className:"".concat(n,"-inner")},y&&l.createElement("button",(0,o.A)({type:"button",onClick:s,"aria-label":"Close"},g,{className:"".concat(n,"-close")}),p),l.createElement("div",{className:"".concat(n,"-header")},l.createElement("div",{className:"".concat(n,"-title")},a)),l.createElement("div",{className:"".concat(n,"-description")},u),l.createElement("div",{className:"".concat(n,"-footer")},l.createElement("div",{className:"".concat(n,"-sliders")},i>1?(0,N.A)(Array.from({length:i}).keys()).map((function(e,t){return l.createElement("span",{key:e,className:t===r?"active":""})})):null),l.createElement("div",{className:"".concat(n,"-buttons")},0!==r?l.createElement("button",{className:"".concat(n,"-prev-btn"),onClick:c},"Prev"):null,r===i-1?l.createElement("button",{className:"".concat(n,"-finish-btn"),onClick:h},"Finish"):l.createElement("button",{className:"".concat(n,"-next-btn"),onClick:f},"Next")))))}const R=function(e){var t=e.current,n=e.renderPanel;return l.createElement(l.Fragment,null,"function"==typeof n?n(e,t):l.createElement(M,e))};var k=["prefixCls","steps","defaultCurrent","current","onChange","onClose","onFinish","open","mask","arrow","rootClassName","placement","renderPanel","gap","animated","scrollIntoViewOptions","zIndex","closeIcon","closable","builtinPlacements","disabledInteraction"],S={left:"50%",top:"50%",width:1,height:1},P={block:"center",inline:"center"};const I=function(e){var t=e.prefixCls,n=void 0===t?"rc-tour":t,f=e.steps,m=void 0===f?[]:f,y=e.defaultCurrent,A=e.current,b=e.onChange,w=e.onClose,N=e.onFinish,x=e.open,M=e.mask,I=void 0===M||M,F=e.arrow,O=void 0===F||F,D=e.rootClassName,L=e.placement,z=e.renderPanel,_=e.gap,T=e.animated,B=e.scrollIntoViewOptions,H=void 0===B?P:B,q=e.zIndex,V=void 0===q?1001:q,j=e.closeIcon,Y=e.closable,X=e.builtinPlacements,U=e.disabledInteraction,W=(0,u.A)(e,k),G=l.useRef(),$=(0,v.A)(0,{value:A,defaultValue:y}),K=(0,a.A)($,2),J=K[0],Q=K[1],Z=(0,v.A)(void 0,{value:x,postState:function(e){return!(J<0||J>=m.length)&&(null==e||e)}}),ee=(0,a.A)(Z,2),te=ee[0],ne=ee[1],re=l.useState(te),oe=(0,a.A)(re,2),ie=oe[0],ae=oe[1],ue=l.useRef(te);(0,h.A)((function(){te&&(ue.current||Q(0),ae(!0)),ue.current=te}),[te]);var le=m[J]||{},se=le.target,ce=le.placement,fe=le.style,de=le.arrow,he=le.className,ve=le.mask,me=le.scrollIntoViewOptions,ge=void 0===me?P:me,pe=le.closeIcon,ye=function(e,t,n,r){return l.useMemo((function(){var o=g(e,t,!1),i=g(n,r,!0);return"empty"!==o?o:i}),[n,r,e,t])}(le.closable,pe,Y,j),Ae=te&&(null!=ve?ve:I),Ee=function(e,t,n,r){var o=(0,l.useState)(void 0),i=(0,a.A)(o,2),u=i[0],s=i[1];(0,h.A)((function(){var t="function"==typeof e?e():e;s(t||null)}));var c=(0,l.useState)(null),f=(0,a.A)(c,2),d=f[0],v=f[1],m=(0,p.A)((function(){if(u){o=u,i=window.innerWidth||document.documentElement.clientWidth,a=window.innerHeight||document.documentElement.clientHeight,s=(l=o.getBoundingClientRect()).top,c=l.right,f=l.bottom,d=l.left,!(s>=0&&d>=0&&c<=i&&f<=a)&&t&&u.scrollIntoView(r);var e=u.getBoundingClientRect(),n={left:e.left,top:e.top,width:e.width,height:e.height,radius:0};v((function(e){return JSON.stringify(e)!==JSON.stringify(n)?n:e}))}else v(null);var o,i,a,l,s,c,f,d})),g=function(e){var t;return null!==(t=Array.isArray(null==n?void 0:n.offset)?null==n?void 0:n.offset[e]:null==n?void 0:n.offset)&&void 0!==t?t:6};return(0,h.A)((function(){return m(),window.addEventListener("resize",m),function(){window.removeEventListener("resize",m)}}),[u,t,m]),[(0,l.useMemo)((function(){if(!d)return d;var e,t=g(0),r=g(1),o="number"!=typeof(e=null==n?void 0:n.radius)||Number.isNaN(e)?2:null==n?void 0:n.radius;return{left:d.left-t,top:d.top-r,width:d.width+2*t,height:d.height+2*r,radius:o}}),[d,n]),u]}(se,x,_,null!=ge?ge:H),be=(0,a.A)(Ee,2),we=be[0],Ce=be[1],Ne=function(e,t,n){var r;return null!==(r=null!=n?n:t)&&void 0!==r?r:null===e?"center":"bottom"}(Ce,L,ce),xe=!!Ce&&(void 0===de?O:de),Me="object"===(0,i.A)(xe)&&xe.pointAtCenter;(0,h.A)((function(){var e;null===(e=G.current)||void 0===e||e.forceAlign()}),[Me,J]);var Re=function(e){Q(e),null==b||b(e)},ke=(0,l.useMemo)((function(){return X?"function"==typeof X?X({arrowPointAtCenter:Me}):X:C(Me)}),[X,Me]);if(void 0===Ce||!ie)return null;var Se=function(){ne(!1),null==w||w(J)},Pe="boolean"==typeof Ae?Ae:!!Ae,Ie="boolean"==typeof Ae?void 0:Ae;return l.createElement(l.Fragment,null,l.createElement(E,{zIndex:V,prefixCls:n,pos:we,showMask:Pe,style:null==Ie?void 0:Ie.style,fill:null==Ie?void 0:Ie.color,open:te,animated:T,rootClassName:D,disabledInteraction:U}),l.createElement(c.A,(0,o.A)({},W,{builtinPlacements:ke,ref:G,popupStyle:fe,popupPlacement:Ne,popupVisible:te,popupClassName:d()(D,he),prefixCls:n,popup:function(){return l.createElement(R,(0,o.A)({arrow:xe,key:"content",prefixCls:n,total:m.length,renderPanel:z,onPrev:function(){Re(J-1)},onNext:function(){Re(J+1)},onClose:Se,current:J,onFinish:function(){Se(),null==N||N()}},m[J],{closable:ye}))},forceRender:!1,destroyPopupOnHide:!0,zIndex:V,getTriggerDOMNode:function(e){return e||Ce||document.body},arrow:!!xe}),l.createElement(s.A,{open:te,autoLock:!0},l.createElement("div",{className:d()(D,"".concat(n,"-target-placeholder")),style:(0,r.A)((0,r.A)({},we||S),{},{position:"fixed",pointerEvents:"none"})}))))}},45062:(e,t,n)=>{n.d(t,{A:()=>y});var r=n(5544),o=n(96540),i=n(40961),a=n(20998),u=(n(68210),n(8719));const l=o.createContext(null);var s=n(60436),c=n(30981),f=[],d=n(85089),h=n(82987),v="rc-util-locker-".concat(Date.now()),m=0;var g=!1,p=function(e){return!1!==e&&((0,a.A)()&&e?"string"==typeof e?document.querySelector(e):"function"==typeof e?e():e:null)};const y=o.forwardRef((function(e,t){var n=e.open,y=e.autoLock,A=e.getContainer,E=(e.debug,e.autoDestroy),b=void 0===E||E,w=e.children,C=o.useState(n),N=(0,r.A)(C,2),x=N[0],M=N[1],R=x||n;o.useEffect((function(){(b||n)&&M(n)}),[n,b]);var k=o.useState((function(){return p(A)})),S=(0,r.A)(k,2),P=S[0],I=S[1];o.useEffect((function(){var e=p(A);I(null!=e?e:null)}));var F=function(e){var t=o.useState((function(){return(0,a.A)()?document.createElement("div"):null})),n=(0,r.A)(t,1)[0],i=o.useRef(!1),u=o.useContext(l),d=o.useState(f),h=(0,r.A)(d,2),v=h[0],m=h[1],g=u||(i.current?void 0:function(e){m((function(t){return[e].concat((0,s.A)(t))}))});function p(){n.parentElement||document.body.appendChild(n),i.current=!0}function y(){var e;null===(e=n.parentElement)||void 0===e||e.removeChild(n),i.current=!1}return(0,c.A)((function(){return e?u?u(p):p():y(),y}),[e]),(0,c.A)((function(){v.length&&(v.forEach((function(e){return e()})),m(f))}),[v]),[n,g]}(R&&!P),O=(0,r.A)(F,2),D=O[0],L=O[1],z=null!=P?P:D;!function(e){var t=!!e,n=o.useState((function(){return m+=1,"".concat(v,"_").concat(m)})),i=(0,r.A)(n,1)[0];(0,c.A)((function(){if(t){var e=(0,h.V)(document.body).width,n=document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth;(0,d.BD)("\nhtml body {\n  overflow-y: hidden;\n  ".concat(n?"width: calc(100% - ".concat(e,"px);"):"","\n}"),i)}else(0,d.m6)(i);return function(){(0,d.m6)(i)}}),[t,i])}(y&&n&&(0,a.A)()&&(z===D||z===document.body));var _=null;w&&(0,u.f3)(w)&&t&&(_=w.ref);var T=(0,u.xK)(_,t);if(!R||!(0,a.A)()||void 0===P)return null;var B=!1===z||g,H=w;return t&&(H=o.cloneElement(w,{ref:T})),o.createElement(l.Provider,{value:L},B?H:(0,i.createPortal)(H,z))}))},58406:(e,t,n)=>{n.d(t,{q6:()=>s,Ho:()=>h,NT:()=>c});var r=n(5544),o=n(26956),i=n(30981),a=n(43210),u=n(96540),l=n(40961);function s(e){var t=u.createContext(void 0);return{Context:t,Provider:function(e){var n=e.value,o=e.children,a=u.useRef(n);a.current=n;var s=u.useState((function(){return{getValue:function(){return a.current},listeners:new Set}})),c=(0,r.A)(s,1)[0];return(0,i.A)((function(){(0,l.unstable_batchedUpdates)((function(){c.listeners.forEach((function(e){e(n)}))}))}),[n]),u.createElement(t.Provider,{value:c},o)},defaultValue:e}}function c(e,t){var n=(0,o.A)("function"==typeof t?t:function(e){if(void 0===t)return e;if(!Array.isArray(t))return e[t];var n={};return t.forEach((function(t){n[t]=e[t]})),n}),l=u.useContext(null==e?void 0:e.Context),s=l||{},c=s.listeners,f=s.getValue,d=u.useRef();d.current=n(l?f():null==e?void 0:e.defaultValue);var h=u.useState({}),v=(0,r.A)(h,2)[1];return(0,i.A)((function(){if(l)return c.add(e),function(){c.delete(e)};function e(e){var t=n(e);(0,a.A)(d.current,t,!0)||v({})}}),[l]),d.current}var f=n(58168),d=n(8719);function h(){var e=u.createContext(null);function t(){return u.useContext(e)}return{makeImmutable:function(n,r){var o=(0,d.f3)(n),i=function(i,a){var l=o?{ref:a}:{},s=u.useRef(0),c=u.useRef(i);return null!==t()?u.createElement(n,(0,f.A)({},i,l)):(r&&!r(c.current,i)||(s.current+=1),c.current=i,u.createElement(e.Provider,{value:s.current},u.createElement(n,(0,f.A)({},i,l))))};return o?u.forwardRef(i):i},responseImmutable:function(e,n){var r=(0,d.f3)(e),o=function(n,o){var i=r?{ref:o}:{};return t(),u.createElement(e,(0,f.A)({},n,i))};return r?u.memo(u.forwardRef(o),n):u.memo(o,n)},useImmutableMark:t}}var v=h();v.makeImmutable,v.responseImmutable,v.useImmutableMark},62427:(e,t,n)=>{n.d(t,{A:()=>V});var r=n(89379),o=n(5544),i=n(53986),a=n(45062),u=n(46942),l=n.n(u),s=n(26076),c=n(66588),f=n(72633),d=n(26956),h=n(56855),v=n(30981),m=n(68430),g=n(96540),p=n(58168),y=n(57557),A=n(8719);function E(e){var t=e.prefixCls,n=e.align,r=e.arrow,o=e.arrowPos,i=r||{},a=i.className,u=i.content,s=o.x,c=void 0===s?0:s,f=o.y,d=void 0===f?0:f,h=g.useRef();if(!n||!n.points)return null;var v={position:"absolute"};if(!1!==n.autoArrow){var m=n.points[0],p=n.points[1],y=m[0],A=m[1],E=p[0],b=p[1];y!==E&&["t","b"].includes(y)?"t"===y?v.top=0:v.bottom=0:v.top=d,A!==b&&["l","r"].includes(A)?"l"===A?v.left=0:v.right=0:v.left=c}return g.createElement("div",{ref:h,className:l()("".concat(t,"-arrow"),a),style:v},u)}function b(e){var t=e.prefixCls,n=e.open,r=e.zIndex,o=e.mask,i=e.motion;return o?g.createElement(y.Ay,(0,p.A)({},i,{motionAppear:!0,visible:n,removeOnLeave:!0}),(function(e){var n=e.className;return g.createElement("div",{style:{zIndex:r},className:l()("".concat(t,"-mask"),n)})})):null}const w=g.memo((function(e){return e.children}),(function(e,t){return t.cache})),C=g.forwardRef((function(e,t){var n=e.popup,i=e.className,a=e.prefixCls,u=e.style,c=e.target,f=e.onVisibleChanged,d=e.open,h=e.keepDom,m=e.fresh,C=e.onClick,N=e.mask,x=e.arrow,M=e.arrowPos,R=e.align,k=e.motion,S=e.maskMotion,P=e.forceRender,I=e.getPopupContainer,F=e.autoDestroy,O=e.portal,D=e.zIndex,L=e.onMouseEnter,z=e.onMouseLeave,_=e.onPointerEnter,T=e.onPointerDownCapture,B=e.ready,H=e.offsetX,q=e.offsetY,V=e.offsetR,j=e.offsetB,Y=e.onAlign,X=e.onPrepare,U=e.stretch,W=e.targetWidth,G=e.targetHeight,$="function"==typeof n?n():n,K=d||h,J=(null==I?void 0:I.length)>0,Q=g.useState(!I||!J),Z=(0,o.A)(Q,2),ee=Z[0],te=Z[1];if((0,v.A)((function(){!ee&&J&&c&&te(!0)}),[ee,J,c]),!ee)return null;var ne="auto",re={left:"-1000vw",top:"-1000vh",right:ne,bottom:ne};if(B||!d){var oe,ie=R.points,ae=R.dynamicInset||(null===(oe=R._experimental)||void 0===oe?void 0:oe.dynamicInset),ue=ae&&"r"===ie[0][1],le=ae&&"b"===ie[0][0];ue?(re.right=V,re.left=ne):(re.left=H,re.right=ne),le?(re.bottom=j,re.top=ne):(re.top=q,re.bottom=ne)}var se={};return U&&(U.includes("height")&&G?se.height=G:U.includes("minHeight")&&G&&(se.minHeight=G),U.includes("width")&&W?se.width=W:U.includes("minWidth")&&W&&(se.minWidth=W)),d||(se.pointerEvents="none"),g.createElement(O,{open:P||K,getContainer:I&&function(){return I(c)},autoDestroy:F},g.createElement(b,{prefixCls:a,open:d,zIndex:D,mask:N,motion:S}),g.createElement(s.A,{onResize:Y,disabled:!d},(function(e){return g.createElement(y.Ay,(0,p.A)({motionAppear:!0,motionEnter:!0,motionLeave:!0,removeOnLeave:!1,forceRender:P,leavedClassName:"".concat(a,"-hidden")},k,{onAppearPrepare:X,onEnterPrepare:X,visible:d,onVisibleChanged:function(e){var t;null==k||null===(t=k.onVisibleChanged)||void 0===t||t.call(k,e),f(e)}}),(function(n,o){var s=n.className,c=n.style,f=l()(a,s,i);return g.createElement("div",{ref:(0,A.K4)(e,t,o),className:f,style:(0,r.A)((0,r.A)((0,r.A)((0,r.A)({"--arrow-x":"".concat(M.x||0,"px"),"--arrow-y":"".concat(M.y||0,"px")},re),se),c),{},{boxSizing:"border-box",zIndex:D},u),onMouseEnter:L,onMouseLeave:z,onPointerEnter:_,onClick:C,onPointerDownCapture:T},x&&g.createElement(E,{prefixCls:a,arrow:x,arrowPos:M,align:R}),g.createElement(w,{cache:!d&&!m},$))}))})))})),N=g.forwardRef((function(e,t){var n=e.children,r=e.getTriggerDOMNode,o=(0,A.f3)(n),i=g.useCallback((function(e){(0,A.Xf)(t,r?r(e):e)}),[r]),a=(0,A.xK)(i,(0,A.A9)(n));return o?g.cloneElement(n,{ref:a}):n})),x=g.createContext(null);function M(e){return e?Array.isArray(e)?e:[e]:[]}var R=n(42467);function k(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return(arguments.length>2?arguments[2]:void 0)?e[0]===t[0]:e[0]===t[0]&&e[1]===t[1]}function S(e,t,n,r){return t||(n?{motionName:"".concat(e,"-").concat(n)}:r?{motionName:r}:null)}function P(e){return e.ownerDocument.defaultView}function I(e){for(var t=[],n=null==e?void 0:e.parentElement,r=["hidden","scroll","clip","auto"];n;){var o=P(n).getComputedStyle(n);[o.overflowX,o.overflowY,o.overflow].some((function(e){return r.includes(e)}))&&t.push(n),n=n.parentElement}return t}function F(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return Number.isNaN(e)?t:e}function O(e){return F(parseFloat(e),0)}function D(e,t){var n=(0,r.A)({},e);return(t||[]).forEach((function(e){if(!(e instanceof HTMLBodyElement||e instanceof HTMLHtmlElement)){var t=P(e).getComputedStyle(e),r=t.overflow,o=t.overflowClipMargin,i=t.borderTopWidth,a=t.borderBottomWidth,u=t.borderLeftWidth,l=t.borderRightWidth,s=e.getBoundingClientRect(),c=e.offsetHeight,f=e.clientHeight,d=e.offsetWidth,h=e.clientWidth,v=O(i),m=O(a),g=O(u),p=O(l),y=F(Math.round(s.width/d*1e3)/1e3),A=F(Math.round(s.height/c*1e3)/1e3),E=(d-h-g-p)*y,b=(c-f-v-m)*A,w=v*A,C=m*A,N=g*y,x=p*y,M=0,R=0;if("clip"===r){var k=O(o);M=k*y,R=k*A}var S=s.x+N-M,I=s.y+w-R,D=S+s.width+2*M-N-x-E,L=I+s.height+2*R-w-C-b;n.left=Math.max(n.left,S),n.top=Math.max(n.top,I),n.right=Math.min(n.right,D),n.bottom=Math.min(n.bottom,L)}})),n}function L(e){var t="".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),n=t.match(/^(.*)\%$/);return n?e*(parseFloat(n[1])/100):parseFloat(t)}function z(e,t){var n=t||[],r=(0,o.A)(n,2),i=r[0],a=r[1];return[L(e.width,i),L(e.height,a)]}function _(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return[e[0],e[1]]}function T(e,t){var n,r=t[0],o=t[1];return n="t"===r?e.y:"b"===r?e.y+e.height:e.y+e.height/2,{x:"l"===o?e.x:"r"===o?e.x+e.width:e.x+e.width/2,y:n}}function B(e,t){var n={t:"b",b:"t",l:"r",r:"l"};return e.map((function(e,r){return r===t?n[e]||"c":e})).join("")}var H=n(60436);n(68210);var q=["prefixCls","children","action","showAction","hideAction","popupVisible","defaultPopupVisible","onPopupVisibleChange","afterPopupVisibleChange","mouseEnterDelay","mouseLeaveDelay","focusDelay","blurDelay","mask","maskClosable","getPopupContainer","forceRender","autoDestroy","destroyPopupOnHide","popup","popupClassName","popupStyle","popupPlacement","builtinPlacements","popupAlign","zIndex","stretch","getPopupClassNameFromAlign","fresh","alignPoint","onPopupClick","onPopupAlign","arrow","popupMotion","maskMotion","popupTransitionName","popupAnimation","maskTransitionName","maskAnimation","className","getTriggerDOMNode"];const V=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.A;return g.forwardRef((function(t,n){var a=t.prefixCls,u=void 0===a?"rc-trigger-popup":a,p=t.children,y=t.action,A=void 0===y?"hover":y,E=t.showAction,b=t.hideAction,w=t.popupVisible,O=t.defaultPopupVisible,L=t.onPopupVisibleChange,V=t.afterPopupVisibleChange,j=t.mouseEnterDelay,Y=t.mouseLeaveDelay,X=void 0===Y?.1:Y,U=t.focusDelay,W=t.blurDelay,G=t.mask,$=t.maskClosable,K=void 0===$||$,J=t.getPopupContainer,Q=t.forceRender,Z=t.autoDestroy,ee=t.destroyPopupOnHide,te=t.popup,ne=t.popupClassName,re=t.popupStyle,oe=t.popupPlacement,ie=t.builtinPlacements,ae=void 0===ie?{}:ie,ue=t.popupAlign,le=t.zIndex,se=t.stretch,ce=t.getPopupClassNameFromAlign,fe=t.fresh,de=t.alignPoint,he=t.onPopupClick,ve=t.onPopupAlign,me=t.arrow,ge=t.popupMotion,pe=t.maskMotion,ye=t.popupTransitionName,Ae=t.popupAnimation,Ee=t.maskTransitionName,be=t.maskAnimation,we=t.className,Ce=t.getTriggerDOMNode,Ne=(0,i.A)(t,q),xe=Z||ee||!1,Me=g.useState(!1),Re=(0,o.A)(Me,2),ke=Re[0],Se=Re[1];(0,v.A)((function(){Se((0,m.A)())}),[]);var Pe=g.useRef({}),Ie=g.useContext(x),Fe=g.useMemo((function(){return{registerSubPopup:function(e,t){Pe.current[e]=t,null==Ie||Ie.registerSubPopup(e,t)}}}),[Ie]),Oe=(0,h.A)(),De=g.useState(null),Le=(0,o.A)(De,2),ze=Le[0],_e=Le[1],Te=g.useRef(null),Be=(0,d.A)((function(e){Te.current=e,(0,c.fk)(e)&&ze!==e&&_e(e),null==Ie||Ie.registerSubPopup(Oe,e)})),He=g.useState(null),qe=(0,o.A)(He,2),Ve=qe[0],je=qe[1],Ye=g.useRef(null),Xe=(0,d.A)((function(e){(0,c.fk)(e)&&Ve!==e&&(je(e),Ye.current=e)})),Ue=g.Children.only(p),We=(null==Ue?void 0:Ue.props)||{},Ge={},$e=(0,d.A)((function(e){var t,n,r=Ve;return(null==r?void 0:r.contains(e))||(null===(t=(0,f.j)(r))||void 0===t?void 0:t.host)===e||e===r||(null==ze?void 0:ze.contains(e))||(null===(n=(0,f.j)(ze))||void 0===n?void 0:n.host)===e||e===ze||Object.values(Pe.current).some((function(t){return(null==t?void 0:t.contains(e))||e===t}))})),Ke=S(u,ge,Ae,ye),Je=S(u,pe,be,Ee),Qe=g.useState(O||!1),Ze=(0,o.A)(Qe,2),et=Ze[0],tt=Ze[1],nt=null!=w?w:et,rt=(0,d.A)((function(e){void 0===w&&tt(e)}));(0,v.A)((function(){tt(w||!1)}),[w]);var ot=g.useRef(nt);ot.current=nt;var it=g.useRef([]);it.current=[];var at=(0,d.A)((function(e){var t;rt(e),(null!==(t=it.current[it.current.length-1])&&void 0!==t?t:nt)!==e&&(it.current.push(e),null==L||L(e))})),ut=g.useRef(),lt=function(){clearTimeout(ut.current)},st=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;lt(),0===t?at(e):ut.current=setTimeout((function(){at(e)}),1e3*t)};g.useEffect((function(){return lt}),[]);var ct=g.useState(!1),ft=(0,o.A)(ct,2),dt=ft[0],ht=ft[1];(0,v.A)((function(e){e&&!nt||ht(!0)}),[nt]);var vt=g.useState(null),mt=(0,o.A)(vt,2),gt=mt[0],pt=mt[1],yt=g.useState(null),At=(0,o.A)(yt,2),Et=At[0],bt=At[1],wt=function(e){bt([e.clientX,e.clientY])},Ct=function(e,t,n,i,a,u,l){var s=g.useState({ready:!1,offsetX:0,offsetY:0,offsetR:0,offsetB:0,arrowX:0,arrowY:0,scaleX:1,scaleY:1,align:a[i]||{}}),f=(0,o.A)(s,2),h=f[0],m=f[1],p=g.useRef(0),y=g.useMemo((function(){return t?I(t):[]}),[t]),A=g.useRef({});e||(A.current={});var E=(0,d.A)((function(){if(t&&n&&e){var s,f,d,h,v,g=t,p=g.ownerDocument,E=P(g).getComputedStyle(g),b=E.width,w=E.height,C=E.position,N=g.style.left,x=g.style.top,M=g.style.right,k=g.style.bottom,S=g.style.overflow,I=(0,r.A)((0,r.A)({},a[i]),u),O=p.createElement("div");if(null===(s=g.parentElement)||void 0===s||s.appendChild(O),O.style.left="".concat(g.offsetLeft,"px"),O.style.top="".concat(g.offsetTop,"px"),O.style.position=C,O.style.height="".concat(g.offsetHeight,"px"),O.style.width="".concat(g.offsetWidth,"px"),g.style.left="0",g.style.top="0",g.style.right="auto",g.style.bottom="auto",g.style.overflow="hidden",Array.isArray(n))v={x:n[0],y:n[1],width:0,height:0};else{var L,H,q=n.getBoundingClientRect();q.x=null!==(L=q.x)&&void 0!==L?L:q.left,q.y=null!==(H=q.y)&&void 0!==H?H:q.top,v={x:q.x,y:q.y,width:q.width,height:q.height}}var V=g.getBoundingClientRect();V.x=null!==(f=V.x)&&void 0!==f?f:V.left,V.y=null!==(d=V.y)&&void 0!==d?d:V.top;var j=p.documentElement,Y=j.clientWidth,X=j.clientHeight,U=j.scrollWidth,W=j.scrollHeight,G=j.scrollTop,$=j.scrollLeft,K=V.height,J=V.width,Q=v.height,Z=v.width,ee={left:0,top:0,right:Y,bottom:X},te={left:-$,top:-G,right:U-$,bottom:W-G},ne=I.htmlRegion,re="visible",oe="visibleFirst";"scroll"!==ne&&ne!==oe&&(ne=re);var ie=ne===oe,ae=D(te,y),ue=D(ee,y),le=ne===re?ue:ae,se=ie?ue:le;g.style.left="auto",g.style.top="auto",g.style.right="0",g.style.bottom="0";var ce=g.getBoundingClientRect();g.style.left=N,g.style.top=x,g.style.right=M,g.style.bottom=k,g.style.overflow=S,null===(h=g.parentElement)||void 0===h||h.removeChild(O);var fe=F(Math.round(J/parseFloat(b)*1e3)/1e3),de=F(Math.round(K/parseFloat(w)*1e3)/1e3);if(0===fe||0===de||(0,c.fk)(n)&&!(0,R.A)(n))return;var he=I.offset,ve=I.targetOffset,me=z(V,he),ge=(0,o.A)(me,2),pe=ge[0],ye=ge[1],Ae=z(v,ve),Ee=(0,o.A)(Ae,2),be=Ee[0],we=Ee[1];v.x-=be,v.y-=we;var Ce=I.points||[],Ne=(0,o.A)(Ce,2),xe=Ne[0],Me=_(Ne[1]),Re=_(xe),ke=T(v,Me),Se=T(V,Re),Pe=(0,r.A)({},I),Ie=ke.x-Se.x+pe,Fe=ke.y-Se.y+ye;function xt(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:le,r=V.x+e,o=V.y+t,i=r+J,a=o+K,u=Math.max(r,n.left),l=Math.max(o,n.top),s=Math.min(i,n.right),c=Math.min(a,n.bottom);return Math.max(0,(s-u)*(c-l))}var Oe,De,Le,ze,_e=xt(Ie,Fe),Te=xt(Ie,Fe,ue),Be=T(v,["t","l"]),He=T(V,["t","l"]),qe=T(v,["b","r"]),Ve=T(V,["b","r"]),je=I.overflow||{},Ye=je.adjustX,Xe=je.adjustY,Ue=je.shiftX,We=je.shiftY,Ge=function(e){return"boolean"==typeof e?e:e>=0};function Mt(){Oe=V.y+Fe,De=Oe+K,Le=V.x+Ie,ze=Le+J}Mt();var $e=Ge(Xe),Ke=Re[0]===Me[0];if($e&&"t"===Re[0]&&(De>se.bottom||A.current.bt)){var Je=Fe;Ke?Je-=K-Q:Je=Be.y-Ve.y-ye;var Qe=xt(Ie,Je),Ze=xt(Ie,Je,ue);Qe>_e||Qe===_e&&(!ie||Ze>=Te)?(A.current.bt=!0,Fe=Je,ye=-ye,Pe.points=[B(Re,0),B(Me,0)]):A.current.bt=!1}if($e&&"b"===Re[0]&&(Oe<se.top||A.current.tb)){var et=Fe;Ke?et+=K-Q:et=qe.y-He.y-ye;var tt=xt(Ie,et),nt=xt(Ie,et,ue);tt>_e||tt===_e&&(!ie||nt>=Te)?(A.current.tb=!0,Fe=et,ye=-ye,Pe.points=[B(Re,0),B(Me,0)]):A.current.tb=!1}var rt=Ge(Ye),ot=Re[1]===Me[1];if(rt&&"l"===Re[1]&&(ze>se.right||A.current.rl)){var it=Ie;ot?it-=J-Z:it=Be.x-Ve.x-pe;var at=xt(it,Fe),ut=xt(it,Fe,ue);at>_e||at===_e&&(!ie||ut>=Te)?(A.current.rl=!0,Ie=it,pe=-pe,Pe.points=[B(Re,1),B(Me,1)]):A.current.rl=!1}if(rt&&"r"===Re[1]&&(Le<se.left||A.current.lr)){var lt=Ie;ot?lt+=J-Z:lt=qe.x-He.x-pe;var st=xt(lt,Fe),ct=xt(lt,Fe,ue);st>_e||st===_e&&(!ie||ct>=Te)?(A.current.lr=!0,Ie=lt,pe=-pe,Pe.points=[B(Re,1),B(Me,1)]):A.current.lr=!1}Mt();var ft=!0===Ue?0:Ue;"number"==typeof ft&&(Le<ue.left&&(Ie-=Le-ue.left-pe,v.x+Z<ue.left+ft&&(Ie+=v.x-ue.left+Z-ft)),ze>ue.right&&(Ie-=ze-ue.right-pe,v.x>ue.right-ft&&(Ie+=v.x-ue.right+ft)));var dt=!0===We?0:We;"number"==typeof dt&&(Oe<ue.top&&(Fe-=Oe-ue.top-ye,v.y+Q<ue.top+dt&&(Fe+=v.y-ue.top+Q-dt)),De>ue.bottom&&(Fe-=De-ue.bottom-ye,v.y>ue.bottom-dt&&(Fe+=v.y-ue.bottom+dt)));var ht=V.x+Ie,vt=ht+J,mt=V.y+Fe,gt=mt+K,pt=v.x,yt=pt+Z,At=v.y,Et=At+Q,bt=(Math.max(ht,pt)+Math.min(vt,yt))/2-ht,wt=(Math.max(mt,At)+Math.min(gt,Et))/2-mt;null==l||l(t,Pe);var Ct=ce.right-V.x-(Ie+V.width),Nt=ce.bottom-V.y-(Fe+V.height);1===fe&&(Ie=Math.round(Ie),Ct=Math.round(Ct)),1===de&&(Fe=Math.round(Fe),Nt=Math.round(Nt)),m({ready:!0,offsetX:Ie/fe,offsetY:Fe/de,offsetR:Ct/fe,offsetB:Nt/de,arrowX:bt/fe,arrowY:wt/de,scaleX:fe,scaleY:de,align:Pe})}})),b=function(){m((function(e){return(0,r.A)((0,r.A)({},e),{},{ready:!1})}))};return(0,v.A)(b,[i]),(0,v.A)((function(){e||b()}),[e]),[h.ready,h.offsetX,h.offsetY,h.offsetR,h.offsetB,h.arrowX,h.arrowY,h.scaleX,h.scaleY,h.align,function(){p.current+=1;var e=p.current;Promise.resolve().then((function(){p.current===e&&E()}))}]}(nt,ze,de&&null!==Et?Et:Ve,oe,ae,ue,ve),Nt=(0,o.A)(Ct,11),xt=Nt[0],Mt=Nt[1],Rt=Nt[2],kt=Nt[3],St=Nt[4],Pt=Nt[5],It=Nt[6],Ft=Nt[7],Ot=Nt[8],Dt=Nt[9],Lt=Nt[10],zt=function(e,t,n,r){return g.useMemo((function(){var o=M(null!=n?n:t),i=M(null!=r?r:t),a=new Set(o),u=new Set(i);return e&&(a.has("hover")&&(a.delete("hover"),a.add("click")),u.has("hover")&&(u.delete("hover"),u.add("click"))),[a,u]}),[e,t,n,r])}(ke,A,E,b),_t=(0,o.A)(zt,2),Tt=_t[0],Bt=_t[1],Ht=Tt.has("click"),qt=Bt.has("click")||Bt.has("contextMenu"),Vt=(0,d.A)((function(){dt||Lt()}));!function(e,t,n,r){(0,v.A)((function(){if(e&&t&&n){var o=n,i=I(t),a=I(o),u=P(o),l=new Set([u].concat((0,H.A)(i),(0,H.A)(a)));function s(){r(),ot.current&&de&&qt&&st(!1)}return l.forEach((function(e){e.addEventListener("scroll",s,{passive:!0})})),u.addEventListener("resize",s,{passive:!0}),r(),function(){l.forEach((function(e){e.removeEventListener("scroll",s),u.removeEventListener("resize",s)}))}}}),[e,t,n])}(nt,Ve,ze,Vt),(0,v.A)((function(){Vt()}),[Et,oe]),(0,v.A)((function(){!nt||null!=ae&&ae[oe]||Vt()}),[JSON.stringify(ue)]);var jt=g.useMemo((function(){var e=function(e,t,n,r){for(var o=n.points,i=Object.keys(e),a=0;a<i.length;a+=1){var u,l=i[a];if(k(null===(u=e[l])||void 0===u?void 0:u.points,o,r))return"".concat(t,"-placement-").concat(l)}return""}(ae,u,Dt,de);return l()(e,null==ce?void 0:ce(Dt))}),[Dt,ce,ae,u,de]);g.useImperativeHandle(n,(function(){return{nativeElement:Ye.current,popupElement:Te.current,forceAlign:Vt}}));var Yt=g.useState(0),Xt=(0,o.A)(Yt,2),Ut=Xt[0],Wt=Xt[1],Gt=g.useState(0),$t=(0,o.A)(Gt,2),Kt=$t[0],Jt=$t[1],Qt=function(){if(se&&Ve){var e=Ve.getBoundingClientRect();Wt(e.width),Jt(e.height)}};function Zt(e,t,n,r){Ge[e]=function(o){var i;null==r||r(o),st(t,n);for(var a=arguments.length,u=new Array(a>1?a-1:0),l=1;l<a;l++)u[l-1]=arguments[l];null===(i=We[e])||void 0===i||i.call.apply(i,[We,o].concat(u))}}(0,v.A)((function(){gt&&(Lt(),gt(),pt(null))}),[gt]),(Ht||qt)&&(Ge.onClick=function(e){var t;ot.current&&qt?st(!1):!ot.current&&Ht&&(wt(e),st(!0));for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];null===(t=We.onClick)||void 0===t||t.call.apply(t,[We,e].concat(r))});var en,tn,nn=function(e,t,n,r,o,i,a,u){var l=g.useRef(e);l.current=e;var s=g.useRef(!1);return g.useEffect((function(){if(t&&r&&(!o||i)){var e=function(){s.current=!1},c=function(e){var t;!l.current||a((null===(t=e.composedPath)||void 0===t||null===(t=t.call(e))||void 0===t?void 0:t[0])||e.target)||s.current||u(!1)},d=P(r);d.addEventListener("pointerdown",e,!0),d.addEventListener("mousedown",c,!0),d.addEventListener("contextmenu",c,!0);var h=(0,f.j)(n);return h&&(h.addEventListener("mousedown",c,!0),h.addEventListener("contextmenu",c,!0)),function(){d.removeEventListener("pointerdown",e,!0),d.removeEventListener("mousedown",c,!0),d.removeEventListener("contextmenu",c,!0),h&&(h.removeEventListener("mousedown",c,!0),h.removeEventListener("contextmenu",c,!0))}}}),[t,n,r,o,i]),function(){s.current=!0}}(nt,qt,Ve,ze,G,K,$e,st),rn=Tt.has("hover"),on=Bt.has("hover");rn&&(Zt("onMouseEnter",!0,j,(function(e){wt(e)})),Zt("onPointerEnter",!0,j,(function(e){wt(e)})),en=function(e){(nt||dt)&&null!=ze&&ze.contains(e.target)&&st(!0,j)},de&&(Ge.onMouseMove=function(e){var t;null===(t=We.onMouseMove)||void 0===t||t.call(We,e)})),on&&(Zt("onMouseLeave",!1,X),Zt("onPointerLeave",!1,X),tn=function(){st(!1,X)}),Tt.has("focus")&&Zt("onFocus",!0,U),Bt.has("focus")&&Zt("onBlur",!1,W),Tt.has("contextMenu")&&(Ge.onContextMenu=function(e){var t;ot.current&&Bt.has("contextMenu")?st(!1):(wt(e),st(!0)),e.preventDefault();for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];null===(t=We.onContextMenu)||void 0===t||t.call.apply(t,[We,e].concat(r))}),we&&(Ge.className=l()(We.className,we));var an=(0,r.A)((0,r.A)({},We),Ge),un={};["onContextMenu","onClick","onMouseDown","onTouchStart","onMouseEnter","onMouseLeave","onFocus","onBlur"].forEach((function(e){Ne[e]&&(un[e]=function(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];null===(t=an[e])||void 0===t||t.call.apply(t,[an].concat(r)),Ne[e].apply(Ne,r)})}));var ln=g.cloneElement(Ue,(0,r.A)((0,r.A)({},an),un)),sn={x:Pt,y:It},cn=me?(0,r.A)({},!0!==me?me:{}):null;return g.createElement(g.Fragment,null,g.createElement(s.A,{disabled:!nt,ref:Xe,onResize:function(){Qt(),Vt()}},g.createElement(N,{getTriggerDOMNode:Ce},ln)),g.createElement(x.Provider,{value:Fe},g.createElement(C,{portal:e,ref:Be,prefixCls:u,popup:te,className:l()(ne,jt),style:re,target:Ve,onMouseEnter:en,onMouseLeave:tn,onPointerEnter:en,zIndex:le,open:nt,keepDom:dt,fresh:fe,onClick:he,onPointerDownCapture:nn,mask:G,motion:Ke,maskMotion:Je,onVisibleChanged:function(e){ht(!1),Lt(),null==V||V(e)},onPrepare:function(){return new Promise((function(e){Qt(),pt((function(){return e}))}))},forceRender:Q,autoDestroy:xe,getPopupContainer:J,align:Dt,arrow:cn,arrowPos:sn,ready:xt,offsetX:Mt,offsetY:Rt,offsetR:kt,offsetB:St,onAlign:Vt,stretch:se,targetWidth:Ut/Ft,targetHeight:Kt/Ot})))}))}(a.A)},71021:(e,t,n)=>{n.d(t,{Q1:()=>y,ZC:()=>x,Ay:()=>L});var r=n(58168),o=n(64467),i=n(5544),a=n(96540),u=n(89379),l=n(23029),s=n(92901),c=n(85501),f=n(29426),d=n(53986),h=n(82284),v=n(77020),m=["b"],g=["v"],p=function(e){return Math.round(Number(e||0))},y=function(e){(0,c.A)(n,e);var t=(0,f.A)(n);function n(e){return(0,l.A)(this,n),t.call(this,function(e){if(e instanceof v.Y)return e;if(e&&"object"===(0,h.A)(e)&&"h"in e&&"b"in e){var t=e,n=t.b,r=(0,d.A)(t,m);return(0,u.A)((0,u.A)({},r),{},{v:n})}return"string"==typeof e&&/hsb/.test(e)?e.replace(/hsb/,"hsv"):e}(e))}return(0,s.A)(n,[{key:"toHsbString",value:function(){var e=this.toHsb(),t=p(100*e.s),n=p(100*e.b),r=p(e.h),o=e.a,i="hsb(".concat(r,", ").concat(t,"%, ").concat(n,"%)"),a="hsba(".concat(r,", ").concat(t,"%, ").concat(n,"%, ").concat(o.toFixed(0===o?0:2),")");return 1===o?i:a}},{key:"toHsb",value:function(){var e=this.toHsv(),t=e.v,n=(0,d.A)(e,g);return(0,u.A)((0,u.A)({},n),{},{b:t,a:this.a})}}]),n}(v.Y),A=function(e){return e instanceof y?e:new y(e)},E=A("#1677ff"),b=function(e){var t=e.offset,n=e.targetRef,r=e.containerRef,o=e.color,i=e.type,a=r.current.getBoundingClientRect(),l=a.width,s=a.height,c=n.current.getBoundingClientRect(),f=c.width/2,d=c.height/2,h=(t.x+f)/l,v=1-(t.y+d)/s,m=o.toHsb(),g=h,p=(t.x+f)/l*360;if(i)switch(i){case"hue":return A((0,u.A)((0,u.A)({},m),{},{h:p<=0?0:p}));case"alpha":return A((0,u.A)((0,u.A)({},m),{},{a:g<=0?0:g}))}return A({h:m.h,s:h<=0?0:h,b:v>=1?1:v,a:m.a})},w=function(e,t){var n=e.toHsb();switch(t){case"hue":return{x:n.h/360*100,y:50};case"alpha":return{x:100*e.a,y:50};default:return{x:100*n.s,y:100*(1-n.b)}}},C=n(46942),N=n.n(C);const x=function(e){var t=e.color,n=e.prefixCls,r=e.className,o=e.style,i=e.onClick,u="".concat(n,"-color-block");return a.createElement("div",{className:N()(u,r),style:o,onClick:i},a.createElement("div",{className:"".concat(u,"-inner"),style:{background:t}}))},M=function(e){var t=e.targetRef,n=e.containerRef,r=e.direction,o=e.onDragChange,u=e.onDragChangeComplete,l=e.calculate,s=e.color,c=e.disabledDrag,f=(0,a.useState)({x:0,y:0}),d=(0,i.A)(f,2),h=d[0],v=d[1],m=(0,a.useRef)(null),g=(0,a.useRef)(null);(0,a.useEffect)((function(){v(l())}),[s]),(0,a.useEffect)((function(){return function(){document.removeEventListener("mousemove",m.current),document.removeEventListener("mouseup",g.current),document.removeEventListener("touchmove",m.current),document.removeEventListener("touchend",g.current),m.current=null,g.current=null}}),[]);var p=function(e){var i=function(e){var t="touches"in e?e.touches[0]:e,n=document.documentElement.scrollLeft||document.body.scrollLeft||window.pageXOffset,r=document.documentElement.scrollTop||document.body.scrollTop||window.pageYOffset;return{pageX:t.pageX-n,pageY:t.pageY-r}}(e),a=i.pageX,u=i.pageY,l=n.current.getBoundingClientRect(),s=l.x,c=l.y,f=l.width,d=l.height,v=t.current.getBoundingClientRect(),m=v.width,g=v.height,p=m/2,y=g/2,A=Math.max(0,Math.min(a-s,f))-p,E=Math.max(0,Math.min(u-c,d))-y,b={x:A,y:"x"===r?h.y:E};if(0===m&&0===g||m!==g)return!1;null==o||o(b)},y=function(e){e.preventDefault(),p(e)},A=function(e){e.preventDefault(),document.removeEventListener("mousemove",m.current),document.removeEventListener("mouseup",g.current),document.removeEventListener("touchmove",m.current),document.removeEventListener("touchend",g.current),m.current=null,g.current=null,null==u||u()};return[h,function(e){document.removeEventListener("mousemove",m.current),document.removeEventListener("mouseup",g.current),c||(p(e),document.addEventListener("mousemove",y),document.addEventListener("mouseup",A),document.addEventListener("touchmove",y),document.addEventListener("touchend",A),m.current=y,g.current=A)}]};var R=n(81470);const k=function(e){var t=e.size,n=void 0===t?"default":t,r=e.color,i=e.prefixCls;return a.createElement("div",{className:N()("".concat(i,"-handler"),(0,o.A)({},"".concat(i,"-handler-sm"),"small"===n)),style:{backgroundColor:r}})},S=function(e){var t=e.children,n=e.style,r=e.prefixCls;return a.createElement("div",{className:"".concat(r,"-palette"),style:(0,u.A)({position:"relative"},n)},t)},P=(0,a.forwardRef)((function(e,t){var n=e.children,r=e.x,o=e.y;return a.createElement("div",{ref:t,style:{position:"absolute",left:"".concat(r,"%"),top:"".concat(o,"%"),zIndex:1,transform:"translate(-50%, -50%)"}},n)})),I=function(e){var t=e.color,n=e.onChange,r=e.prefixCls,o=e.onChangeComplete,u=e.disabled,l=(0,a.useRef)(),s=(0,a.useRef)(),c=(0,a.useRef)(t),f=(0,R._q)((function(e){var r=b({offset:e,targetRef:s,containerRef:l,color:t});c.current=r,n(r)})),d=M({color:t,containerRef:l,targetRef:s,calculate:function(){return w(t)},onDragChange:f,onDragChangeComplete:function(){return null==o?void 0:o(c.current)},disabledDrag:u}),h=(0,i.A)(d,2),v=h[0],m=h[1];return a.createElement("div",{ref:l,className:"".concat(r,"-select"),onMouseDown:m,onTouchStart:m},a.createElement(S,{prefixCls:r},a.createElement(P,{x:v.x,y:v.y,ref:s},a.createElement(k,{color:t.toRgbString(),prefixCls:r})),a.createElement("div",{className:"".concat(r,"-saturation"),style:{backgroundColor:"hsl(".concat(t.toHsb().h,",100%, 50%)"),backgroundImage:"linear-gradient(0deg, #000, transparent),linear-gradient(90deg, #fff, hsla(0, 0%, 100%, 0))"}})))},F=function(e){var t=e.colors,n=e.children,r=e.direction,o=void 0===r?"to right":r,i=e.type,u=e.prefixCls,l=(0,a.useMemo)((function(){return t.map((function(e,n){var r=A(e);return"alpha"===i&&n===t.length-1&&(r=new y(r.setA(1))),r.toRgbString()})).join(",")}),[t,i]);return a.createElement("div",{className:"".concat(u,"-gradient"),style:{position:"absolute",inset:0,background:"linear-gradient(".concat(o,", ").concat(l,")")}},n)},O=function(e){var t=e.prefixCls,n=e.colors,r=e.disabled,o=e.onChange,u=e.onChangeComplete,l=e.color,s=e.type,c=(0,a.useRef)(),f=(0,a.useRef)(),d=(0,a.useRef)(l),h=function(e){return"hue"===s?e.getHue():100*e.a},v=(0,R._q)((function(e){var t=b({offset:e,targetRef:f,containerRef:c,color:l,type:s});d.current=t,o(h(t))})),m=M({color:l,targetRef:f,containerRef:c,calculate:function(){return w(l,s)},onDragChange:v,onDragChangeComplete:function(){u(h(d.current))},direction:"x",disabledDrag:r}),g=(0,i.A)(m,2),p=g[0],A=g[1],E=a.useMemo((function(){if("hue"===s){var e=l.toHsb();return e.s=1,e.b=1,e.a=1,new y(e)}return l}),[l,s]),C=a.useMemo((function(){return n.map((function(e){return"".concat(e.color," ").concat(e.percent,"%")}))}),[n]);return a.createElement("div",{ref:c,className:N()("".concat(t,"-slider"),"".concat(t,"-slider-").concat(s)),onMouseDown:A,onTouchStart:A},a.createElement(S,{prefixCls:t},a.createElement(P,{x:p.x,y:p.y,ref:f},a.createElement(k,{size:"small",color:E.toHexString(),prefixCls:t})),a.createElement(F,{colors:C,type:s,prefixCls:t})))};var D=[{color:"rgb(255, 0, 0)",percent:0},{color:"rgb(255, 255, 0)",percent:17},{color:"rgb(0, 255, 0)",percent:33},{color:"rgb(0, 255, 255)",percent:50},{color:"rgb(0, 0, 255)",percent:67},{color:"rgb(255, 0, 255)",percent:83},{color:"rgb(255, 0, 0)",percent:100}];const L=(0,a.forwardRef)((function(e,t){var n,u=e.value,l=e.defaultValue,s=e.prefixCls,c=void 0===s?"rc-color-picker":s,f=e.onChange,d=e.onChangeComplete,h=e.className,v=e.style,m=e.panelRender,g=e.disabledAlpha,p=void 0!==g&&g,b=e.disabled,w=void 0!==b&&b,C=(n=e.components,a.useMemo((function(){return[(n||{}).slider||O]}),[n])),M=(0,i.A)(C,1)[0],k=function(e,t){var n=(0,R.vz)(e,{value:t}),r=(0,i.A)(n,2),o=r[0],u=r[1];return[(0,a.useMemo)((function(){return A(o)}),[o]),u]}(l||E,u),S=(0,i.A)(k,2),P=S[0],F=S[1],L=(0,a.useMemo)((function(){return P.setA(1).toRgbString()}),[P]),z=function(e,t){u||F(e),null==f||f(e,t)},_=function(e){return new y(P.setHue(e))},T=function(e){return new y(P.setA(e/100))},B=N()("".concat(c,"-panel"),h,(0,o.A)({},"".concat(c,"-panel-disabled"),w)),H={prefixCls:c,disabled:w,color:P},q=a.createElement(a.Fragment,null,a.createElement(I,(0,r.A)({onChange:z},H,{onChangeComplete:d})),a.createElement("div",{className:"".concat(c,"-slider-container")},a.createElement("div",{className:N()("".concat(c,"-slider-group"),(0,o.A)({},"".concat(c,"-slider-group-disabled-alpha"),p))},a.createElement(M,(0,r.A)({},H,{type:"hue",colors:D,min:0,max:359,value:P.getHue(),onChange:function(e){z(_(e),{type:"hue",value:e})},onChangeComplete:function(e){d&&d(_(e))}})),!p&&a.createElement(M,(0,r.A)({},H,{type:"alpha",colors:[{percent:0,color:"rgba(255, 0, 4, 0)"},{percent:100,color:L}],min:0,max:100,value:100*P.a,onChange:function(e){z(T(e),{type:"alpha",value:e})},onChangeComplete:function(e){d&&d(T(e))}}))),a.createElement(x,{color:P.toRgbString(),prefixCls:c})));return a.createElement("div",{className:B,style:v,ref:t},"function"==typeof m?m(q):q)}))},72568:(e,t,n)=>{n.d(t,{A:()=>T});var r=n(89379),o=n(60436),i=n(82284),a=n(23029),u=n(92901),l=n(64467);function s(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}var c=s(),f=n(9417),d=n(85501),h=n(29426),v=n(73437),m=(n(65606),/%[sdj%]/g);function g(e){if(!e||!e.length)return null;var t={};return e.forEach((function(e){var n=e.field;t[n]=t[n]||[],t[n].push(e)})),t}function p(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=0,i=n.length;return"function"==typeof e?e.apply(null,n):"string"==typeof e?e.replace(m,(function(e){if("%%"===e)return"%";if(o>=i)return e;switch(e){case"%s":return String(n[o++]);case"%d":return Number(n[o++]);case"%j":try{return JSON.stringify(n[o++])}catch(e){return"[Circular]"}break;default:return e}})):e}function y(e,t){return null==e||!("array"!==t||!Array.isArray(e)||e.length)||!(!function(e){return"string"===e||"url"===e||"hex"===e||"email"===e||"date"===e||"pattern"===e}(t)||"string"!=typeof e||e)}function A(e,t,n){var r=0,o=e.length;!function i(a){if(a&&a.length)n(a);else{var u=r;r+=1,u<o?t(e[u],i):n([])}}([])}var E=function(e){(0,d.A)(n,e);var t=(0,h.A)(n);function n(e,r){var o;return(0,a.A)(this,n),o=t.call(this,"Async Validation Error"),(0,l.A)((0,f.A)(o),"errors",void 0),(0,l.A)((0,f.A)(o),"fields",void 0),o.errors=e,o.fields=r,o}return(0,u.A)(n)}((0,v.A)(Error));function b(e,t){return function(n){var r,o;return r=e.fullFields?function(e,t){for(var n=e,r=0;r<t.length;r++){if(null==n)return n;n=n[t[r]]}return n}(t,e.fullFields):t[n.field||e.fullField],(o=n)&&void 0!==o.message?(n.field=n.field||e.fullField,n.fieldValue=r,n):{message:"function"==typeof n?n():n,fieldValue:r,field:n.field||e.fullField}}}function w(e,t){if(t)for(var n in t)if(t.hasOwnProperty(n)){var o=t[n];"object"===(0,i.A)(o)&&"object"===(0,i.A)(e[n])?e[n]=(0,r.A)((0,r.A)({},e[n]),o):e[n]=o}return e}var C="enum";const N=function(e,t,n,r,o,i){!e.required||n.hasOwnProperty(e.field)&&!y(t,i||e.type)||r.push(p(o.messages.required,e.fullField))};var x,M=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,R=/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i,k={integer:function(e){return k.number(e)&&parseInt(e,10)===e},float:function(e){return k.number(e)&&!k.integer(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch(e){return!1}},date:function(e){return"function"==typeof e.getTime&&"function"==typeof e.getMonth&&"function"==typeof e.getYear&&!isNaN(e.getTime())},number:function(e){return!isNaN(e)&&"number"==typeof e},object:function(e){return"object"===(0,i.A)(e)&&!k.array(e)},method:function(e){return"function"==typeof e},email:function(e){return"string"==typeof e&&e.length<=320&&!!e.match(M)},url:function(e){return"string"==typeof e&&e.length<=2048&&!!e.match(function(){if(x)return x;var e="[a-fA-F\\d:]",t=function(t){return t&&t.includeBoundaries?"(?:(?<=\\s|^)(?=".concat(e,")|(?<=").concat(e,")(?=\\s|$))"):""},n="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",r="[a-fA-F\\d]{1,4}",o=["(?:".concat(r,":){7}(?:").concat(r,"|:)"),"(?:".concat(r,":){6}(?:").concat(n,"|:").concat(r,"|:)"),"(?:".concat(r,":){5}(?::").concat(n,"|(?::").concat(r,"){1,2}|:)"),"(?:".concat(r,":){4}(?:(?::").concat(r,"){0,1}:").concat(n,"|(?::").concat(r,"){1,3}|:)"),"(?:".concat(r,":){3}(?:(?::").concat(r,"){0,2}:").concat(n,"|(?::").concat(r,"){1,4}|:)"),"(?:".concat(r,":){2}(?:(?::").concat(r,"){0,3}:").concat(n,"|(?::").concat(r,"){1,5}|:)"),"(?:".concat(r,":){1}(?:(?::").concat(r,"){0,4}:").concat(n,"|(?::").concat(r,"){1,6}|:)"),"(?::(?:(?::".concat(r,"){0,5}:").concat(n,"|(?::").concat(r,"){1,7}|:))")],i="(?:".concat(o.join("|"),")").concat("(?:%[0-9a-zA-Z]{1,})?"),a=new RegExp("(?:^".concat(n,"$)|(?:^").concat(i,"$)")),u=new RegExp("^".concat(n,"$")),l=new RegExp("^".concat(i,"$")),s=function(e){return e&&e.exact?a:new RegExp("(?:".concat(t(e)).concat(n).concat(t(e),")|(?:").concat(t(e)).concat(i).concat(t(e),")"),"g")};s.v4=function(e){return e&&e.exact?u:new RegExp("".concat(t(e)).concat(n).concat(t(e)),"g")},s.v6=function(e){return e&&e.exact?l:new RegExp("".concat(t(e)).concat(i).concat(t(e)),"g")};var c=s.v4().source,f=s.v6().source,d="(?:".concat("(?:(?:[a-z]+:)?//)","|www\\.)").concat("(?:\\S+(?::\\S*)?@)?","(?:localhost|").concat(c,"|").concat(f,"|").concat("(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)").concat("(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*").concat("(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))",")").concat("(?::\\d{2,5})?").concat('(?:[/?#][^\\s"]*)?');return x=new RegExp("(?:^".concat(d,"$)"),"i")}())},hex:function(e){return"string"==typeof e&&!!e.match(R)}};const S=N,P=function(e,t,n,r,o){(/^\s+$/.test(t)||""===t)&&r.push(p(o.messages.whitespace,e.fullField))},I=function(e,t,n,r,o){if(e.required&&void 0===t)N(e,t,n,r,o);else{var a=e.type;["integer","float","array","regexp","object","method","email","number","date","url","hex"].indexOf(a)>-1?k[a](t)||r.push(p(o.messages.types[a],e.fullField,e.type)):a&&(0,i.A)(t)!==e.type&&r.push(p(o.messages.types[a],e.fullField,e.type))}},F=function(e,t,n,r,o){var i="number"==typeof e.len,a="number"==typeof e.min,u="number"==typeof e.max,l=t,s=null,c="number"==typeof t,f="string"==typeof t,d=Array.isArray(t);if(c?s="number":f?s="string":d&&(s="array"),!s)return!1;d&&(l=t.length),f&&(l=t.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"_").length),i?l!==e.len&&r.push(p(o.messages[s].len,e.fullField,e.len)):a&&!u&&l<e.min?r.push(p(o.messages[s].min,e.fullField,e.min)):u&&!a&&l>e.max?r.push(p(o.messages[s].max,e.fullField,e.max)):a&&u&&(l<e.min||l>e.max)&&r.push(p(o.messages[s].range,e.fullField,e.min,e.max))},O=function(e,t,n,r,o){e[C]=Array.isArray(e[C])?e[C]:[],-1===e[C].indexOf(t)&&r.push(p(o.messages[C],e.fullField,e[C].join(", ")))},D=function(e,t,n,r,o){e.pattern&&(e.pattern instanceof RegExp?(e.pattern.lastIndex=0,e.pattern.test(t)||r.push(p(o.messages.pattern.mismatch,e.fullField,t,e.pattern))):"string"==typeof e.pattern&&(new RegExp(e.pattern).test(t)||r.push(p(o.messages.pattern.mismatch,e.fullField,t,e.pattern))))},L=function(e,t,n,r,o){var i=e.type,a=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(y(t,i)&&!e.required)return n();S(e,t,r,a,o,i),y(t,i)||I(e,t,r,a,o)}n(a)},z={string:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(y(t,"string")&&!e.required)return n();S(e,t,r,i,o,"string"),y(t,"string")||(I(e,t,r,i,o),F(e,t,r,i,o),D(e,t,r,i,o),!0===e.whitespace&&P(e,t,r,i,o))}n(i)},method:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(y(t)&&!e.required)return n();S(e,t,r,i,o),void 0!==t&&I(e,t,r,i,o)}n(i)},number:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(""===t&&(t=void 0),y(t)&&!e.required)return n();S(e,t,r,i,o),void 0!==t&&(I(e,t,r,i,o),F(e,t,r,i,o))}n(i)},boolean:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(y(t)&&!e.required)return n();S(e,t,r,i,o),void 0!==t&&I(e,t,r,i,o)}n(i)},regexp:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(y(t)&&!e.required)return n();S(e,t,r,i,o),y(t)||I(e,t,r,i,o)}n(i)},integer:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(y(t)&&!e.required)return n();S(e,t,r,i,o),void 0!==t&&(I(e,t,r,i,o),F(e,t,r,i,o))}n(i)},float:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(y(t)&&!e.required)return n();S(e,t,r,i,o),void 0!==t&&(I(e,t,r,i,o),F(e,t,r,i,o))}n(i)},array:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(null==t&&!e.required)return n();S(e,t,r,i,o,"array"),null!=t&&(I(e,t,r,i,o),F(e,t,r,i,o))}n(i)},object:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(y(t)&&!e.required)return n();S(e,t,r,i,o),void 0!==t&&I(e,t,r,i,o)}n(i)},enum:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(y(t)&&!e.required)return n();S(e,t,r,i,o),void 0!==t&&O(e,t,r,i,o)}n(i)},pattern:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(y(t,"string")&&!e.required)return n();S(e,t,r,i,o),y(t,"string")||D(e,t,r,i,o)}n(i)},date:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(y(t,"date")&&!e.required)return n();var a;S(e,t,r,i,o),y(t,"date")||(a=t instanceof Date?t:new Date(t),I(e,a,r,i,o),a&&F(e,a.getTime(),r,i,o))}n(i)},url:L,hex:L,email:L,required:function(e,t,n,r,o){var a=[],u=Array.isArray(t)?"array":(0,i.A)(t);S(e,t,r,a,o,u),n(a)},any:function(e,t,n,r,o){var i=[];if(e.required||!e.required&&r.hasOwnProperty(e.field)){if(y(t)&&!e.required)return n();S(e,t,r,i,o)}n(i)}};var _=function(){function e(t){(0,a.A)(this,e),(0,l.A)(this,"rules",null),(0,l.A)(this,"_messages",c),this.define(t)}return(0,u.A)(e,[{key:"define",value:function(e){var t=this;if(!e)throw new Error("Cannot configure a schema with no rules");if("object"!==(0,i.A)(e)||Array.isArray(e))throw new Error("Rules must be an object");this.rules={},Object.keys(e).forEach((function(n){var r=e[n];t.rules[n]=Array.isArray(r)?r:[r]}))}},{key:"messages",value:function(e){return e&&(this._messages=w(s(),e)),this._messages}},{key:"validate",value:function(t){var n=this,a=t,u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};if("function"==typeof u&&(l=u,u={}),!this.rules||0===Object.keys(this.rules).length)return l&&l(null,a),Promise.resolve(a);if(u.messages){var f=this.messages();f===c&&(f=s()),w(f,u.messages),u.messages=f}else u.messages=this.messages();var d={};(u.keys||Object.keys(this.rules)).forEach((function(e){var o=n.rules[e],u=a[e];o.forEach((function(o){var l=o;"function"==typeof l.transform&&(a===t&&(a=(0,r.A)({},a)),null!=(u=a[e]=l.transform(u))&&(l.type=l.type||(Array.isArray(u)?"array":(0,i.A)(u)))),(l="function"==typeof l?{validator:l}:(0,r.A)({},l)).validator=n.getValidationMethod(l),l.validator&&(l.field=e,l.fullField=l.fullField||e,l.type=n.getType(l),d[e]=d[e]||[],d[e].push({rule:l,value:u,source:a,field:e}))}))}));var h={};return function(e,t,n,r,i){if(t.first){var a=new Promise((function(t,a){var u=function(e){var t=[];return Object.keys(e).forEach((function(n){t.push.apply(t,(0,o.A)(e[n]||[]))})),t}(e);A(u,n,(function(e){return r(e),e.length?a(new E(e,g(e))):t(i)}))}));return a.catch((function(e){return e})),a}var u=!0===t.firstFields?Object.keys(e):t.firstFields||[],l=Object.keys(e),s=l.length,c=0,f=[],d=new Promise((function(t,a){var d=function(e){if(f.push.apply(f,e),++c===s)return r(f),f.length?a(new E(f,g(f))):t(i)};l.length||(r(f),t(i)),l.forEach((function(t){var r=e[t];-1!==u.indexOf(t)?A(r,n,d):function(e,t,n){var r=[],i=0,a=e.length;function u(e){r.push.apply(r,(0,o.A)(e||[])),++i===a&&n(r)}e.forEach((function(e){t(e,u)}))}(r,n,d)}))}));return d.catch((function(e){return e})),d}(d,u,(function(t,n){var l,s=t.rule,c=!("object"!==s.type&&"array"!==s.type||"object"!==(0,i.A)(s.fields)&&"object"!==(0,i.A)(s.defaultField));function f(e,t){return(0,r.A)((0,r.A)({},t),{},{fullField:"".concat(s.fullField,".").concat(e),fullFields:s.fullFields?[].concat((0,o.A)(s.fullFields),[e]):[e]})}function d(){var i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],l=Array.isArray(i)?i:[i];!u.suppressWarning&&l.length&&e.warning("async-validator:",l),l.length&&void 0!==s.message&&(l=[].concat(s.message));var d=l.map(b(s,a));if(u.first&&d.length)return h[s.field]=1,n(d);if(c){if(s.required&&!t.value)return void 0!==s.message?d=[].concat(s.message).map(b(s,a)):u.error&&(d=[u.error(s,p(u.messages.required,s.field))]),n(d);var v={};s.defaultField&&Object.keys(t.value).map((function(e){v[e]=s.defaultField})),v=(0,r.A)((0,r.A)({},v),t.rule.fields);var m={};Object.keys(v).forEach((function(e){var t=v[e],n=Array.isArray(t)?t:[t];m[e]=n.map(f.bind(null,e))}));var g=new e(m);g.messages(u.messages),t.rule.options&&(t.rule.options.messages=u.messages,t.rule.options.error=u.error),g.validate(t.value,t.rule.options||u,(function(e){var t=[];d&&d.length&&t.push.apply(t,(0,o.A)(d)),e&&e.length&&t.push.apply(t,(0,o.A)(e)),n(t.length?t:null)}))}else n(d)}if(c=c&&(s.required||!s.required&&t.value),s.field=t.field,s.asyncValidator)l=s.asyncValidator(s,t.value,d,t.source,u);else if(s.validator){try{l=s.validator(s,t.value,d,t.source,u)}catch(e){var v,m;null===(v=(m=console).error)||void 0===v||v.call(m,e),u.suppressValidatorError||setTimeout((function(){throw e}),0),d(e.message)}!0===l?d():!1===l?d("function"==typeof s.message?s.message(s.fullField||s.field):s.message||"".concat(s.fullField||s.field," fails")):l instanceof Array?d(l):l instanceof Error&&d(l.message)}l&&l.then&&l.then((function(){return d()}),(function(e){return d(e)}))}),(function(e){!function(e){for(var t,n,r=[],i={},u=0;u<e.length;u++)t=e[u],n=void 0,Array.isArray(t)?r=(n=r).concat.apply(n,(0,o.A)(t)):r.push(t);r.length?(i=g(r),l(r,i)):l(null,a)}(e)}),a)}},{key:"getType",value:function(e){if(void 0===e.type&&e.pattern instanceof RegExp&&(e.type="pattern"),"function"!=typeof e.validator&&e.type&&!z.hasOwnProperty(e.type))throw new Error(p("Unknown rule type %s",e.type));return e.type||"string"}},{key:"getValidationMethod",value:function(e){if("function"==typeof e.validator)return e.validator;var t=Object.keys(e),n=t.indexOf("message");return-1!==n&&t.splice(n,1),1===t.length&&"required"===t[0]?z.required:z[this.getType(e)]||void 0}}]),e}();(0,l.A)(_,"register",(function(e,t){if("function"!=typeof t)throw new Error("Cannot register a validator by type, validator is not a function");z[e]=t})),(0,l.A)(_,"warning",(function(){})),(0,l.A)(_,"messages",c),(0,l.A)(_,"validators",z);const T=_},84032:(e,t,n)=>{n.d(t,{Ay:()=>p,i5:()=>c,yH:()=>f,Mg:()=>g,OM:()=>l,yb:()=>d});var r=n(23029),o=n(92901),i=n(64467);function a(){return"function"==typeof BigInt}function u(e){return!e&&0!==e&&!Number.isNaN(e)||!String(e).trim()}function l(e){var t=e.trim(),n=t.startsWith("-");n&&(t=t.slice(1)),(t=t.replace(/(\.\d*[^0])0*$/,"$1").replace(/\.0*$/,"").replace(/^0+/,"")).startsWith(".")&&(t="0".concat(t));var r=t||"0",o=r.split("."),i=o[0]||"0",a=o[1]||"0";"0"===i&&"0"===a&&(n=!1);var u=n?"-":"";return{negative:n,negativeStr:u,trimStr:r,integerStr:i,decimalStr:a,fullStr:"".concat(u).concat(r)}}function s(e){var t=String(e);return!Number.isNaN(Number(t))&&t.includes("e")}function c(e){var t=String(e);if(s(e)){var n=Number(t.slice(t.indexOf("e-")+2)),r=t.match(/\.(\d+)/);return null!=r&&r[1]&&(n+=r[1].length),n}return t.includes(".")&&d(t)?t.length-t.indexOf(".")-1:0}function f(e){var t=String(e);if(s(e)){if(e>Number.MAX_SAFE_INTEGER)return String(a()?BigInt(e).toString():Number.MAX_SAFE_INTEGER);if(e<Number.MIN_SAFE_INTEGER)return String(a()?BigInt(e).toString():Number.MIN_SAFE_INTEGER);t=e.toFixed(c(t))}return l(t).fullStr}function d(e){return"number"==typeof e?!Number.isNaN(e):!!e&&(/^\s*-?\d+(\.\d+)?\s*$/.test(e)||/^\s*-?\d+\.\s*$/.test(e)||/^\s*-?\.\d+\s*$/.test(e))}var h=function(){function e(t){if((0,r.A)(this,e),(0,i.A)(this,"origin",""),(0,i.A)(this,"negative",void 0),(0,i.A)(this,"integer",void 0),(0,i.A)(this,"decimal",void 0),(0,i.A)(this,"decimalLen",void 0),(0,i.A)(this,"empty",void 0),(0,i.A)(this,"nan",void 0),u(t))this.empty=!0;else if(this.origin=String(t),"-"===t||Number.isNaN(t))this.nan=!0;else{var n=t;if(s(n)&&(n=Number(n)),d(n="string"==typeof n?n:f(n))){var o=l(n);this.negative=o.negative;var a=o.trimStr.split(".");this.integer=BigInt(a[0]);var c=a[1]||"0";this.decimal=BigInt(c),this.decimalLen=c.length}else this.nan=!0}}return(0,o.A)(e,[{key:"getMark",value:function(){return this.negative?"-":""}},{key:"getIntegerStr",value:function(){return this.integer.toString()}},{key:"getDecimalStr",value:function(){return this.decimal.toString().padStart(this.decimalLen,"0")}},{key:"alignDecimal",value:function(e){var t="".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(e,"0"));return BigInt(t)}},{key:"negate",value:function(){var t=new e(this.toString());return t.negative=!t.negative,t}},{key:"cal",value:function(t,n,r){var o=Math.max(this.getDecimalStr().length,t.getDecimalStr().length),i=n(this.alignDecimal(o),t.alignDecimal(o)).toString(),a=r(o),u=l(i),s=u.negativeStr,c=u.trimStr,f="".concat(s).concat(c.padStart(a+1,"0"));return new e("".concat(f.slice(0,-a),".").concat(f.slice(-a)))}},{key:"add",value:function(t){if(this.isInvalidate())return new e(t);var n=new e(t);return n.isInvalidate()?this:this.cal(n,(function(e,t){return e+t}),(function(e){return e}))}},{key:"multi",value:function(t){var n=new e(t);return this.isInvalidate()||n.isInvalidate()?new e(NaN):this.cal(n,(function(e,t){return e*t}),(function(e){return 2*e}))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return this.nan}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(e){return this.toString()===(null==e?void 0:e.toString())}},{key:"lessEquals",value:function(e){return this.add(e.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.isNaN()?NaN:Number(this.toString())}},{key:"toString",value:function(){return arguments.length>0&&void 0!==arguments[0]&&!arguments[0]?this.origin:this.isInvalidate()?"":l("".concat(this.getMark()).concat(this.getIntegerStr(),".").concat(this.getDecimalStr())).fullStr}}]),e}(),v=function(){function e(t){(0,r.A)(this,e),(0,i.A)(this,"origin",""),(0,i.A)(this,"number",void 0),(0,i.A)(this,"empty",void 0),u(t)?this.empty=!0:(this.origin=String(t),this.number=Number(t))}return(0,o.A)(e,[{key:"negate",value:function(){return new e(-this.toNumber())}},{key:"add",value:function(t){if(this.isInvalidate())return new e(t);var n=Number(t);if(Number.isNaN(n))return this;var r=this.number+n;if(r>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(r<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var o=Math.max(c(this.number),c(n));return new e(r.toFixed(o))}},{key:"multi",value:function(t){var n=Number(t);if(this.isInvalidate()||Number.isNaN(n))return new e(NaN);var r=this.number*n;if(r>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(r<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var o=Math.max(c(this.number),c(n));return new e(r.toFixed(o))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return Number.isNaN(this.number)}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(e){return this.toNumber()===(null==e?void 0:e.toNumber())}},{key:"lessEquals",value:function(e){return this.add(e.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.number}},{key:"toString",value:function(){return arguments.length>0&&void 0!==arguments[0]&&!arguments[0]?this.origin:this.isInvalidate()?"":f(this.number)}}]),e}();function m(e){return a()?new h(e):new v(e)}function g(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(""===e)return"";var o=l(e),i=o.negativeStr,a=o.integerStr,u=o.decimalStr,s="".concat(t).concat(u),c="".concat(i).concat(a);if(n>=0){var f=Number(u[n]);return f>=5&&!r?g(m(e).add("".concat(i,"0.").concat("0".repeat(n)).concat(10-f)).toString(),t,n,r):0===n?c:"".concat(c).concat(t).concat(u.padEnd(n,"0").slice(0,n))}return".0"===s?c:"".concat(c).concat(s)}const p=m},84648:(e,t,n)=>{n(5544);var r=n(96540);n(30981),n(8719),n(66588),n(26956),n(23029),n(92901),n(85501),n(29426);r.Component,n(20998)},85364:(e,t,n)=>{var r,o,i=n(24765),a=n(23029),u=n(92901),l=n(64467);function s(e,t,n){if(t<0||t>31||e>>>t!=0)throw new RangeError("Value out of range");for(var r=t-1;r>=0;r--)n.push(e>>>r&1)}function c(e,t){return!!(e>>>t&1)}function f(e){if(!e)throw new Error("Assertion error")}var d=function(){function e(t,n){(0,a.A)(this,e),(0,l.A)(this,"modeBits",void 0),(0,l.A)(this,"numBitsCharCount",void 0),this.modeBits=t,this.numBitsCharCount=n}return(0,u.A)(e,[{key:"numCharCountBits",value:function(e){return this.numBitsCharCount[Math.floor((e+7)/17)]}}]),e}();r=d,(0,l.A)(d,"NUMERIC",new r(1,[10,12,14])),(0,l.A)(d,"ALPHANUMERIC",new r(2,[9,11,13])),(0,l.A)(d,"BYTE",new r(4,[8,16,16])),(0,l.A)(d,"KANJI",new r(8,[8,10,12])),(0,l.A)(d,"ECI",new r(7,[0,0,0]));var h=(0,u.A)((function e(t,n){(0,a.A)(this,e),(0,l.A)(this,"ordinal",void 0),(0,l.A)(this,"formatBits",void 0),this.ordinal=t,this.formatBits=n}));o=h,(0,l.A)(h,"LOW",new o(0,1)),(0,l.A)(h,"MEDIUM",new o(1,0)),(0,l.A)(h,"QUARTILE",new o(2,3)),(0,l.A)(h,"HIGH",new o(3,2));var v=function(){function e(t,n,r){if((0,a.A)(this,e),(0,l.A)(this,"mode",void 0),(0,l.A)(this,"numChars",void 0),(0,l.A)(this,"bitData",void 0),this.mode=t,this.numChars=n,this.bitData=r,n<0)throw new RangeError("Invalid argument");this.bitData=r.slice()}return(0,u.A)(e,[{key:"getData",value:function(){return this.bitData.slice()}}],[{key:"makeBytes",value:function(t){var n,r=[],o=(0,i.A)(t);try{for(o.s();!(n=o.n()).done;)s(n.value,8,r)}catch(e){o.e(e)}finally{o.f()}return new e(d.BYTE,t.length,r)}},{key:"makeNumeric",value:function(t){if(!e.isNumeric(t))throw new RangeError("String contains non-numeric characters");for(var n=[],r=0;r<t.length;){var o=Math.min(t.length-r,3);s(parseInt(t.substring(r,r+o),10),3*o+1,n),r+=o}return new e(d.NUMERIC,t.length,n)}},{key:"makeAlphanumeric",value:function(t){if(!e.isAlphanumeric(t))throw new RangeError("String contains unencodable characters in alphanumeric mode");var n,r=[];for(n=0;n+2<=t.length;n+=2){var o=45*e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n));s(o+=e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n+1)),11,r)}return n<t.length&&s(e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n)),6,r),new e(d.ALPHANUMERIC,t.length,r)}},{key:"makeSegments",value:function(t){return""==t?[]:e.isNumeric(t)?[e.makeNumeric(t)]:e.isAlphanumeric(t)?[e.makeAlphanumeric(t)]:[e.makeBytes(e.toUtf8ByteArray(t))]}},{key:"makeEci",value:function(t){var n=[];if(t<0)throw new RangeError("ECI assignment value out of range");if(t<128)s(t,8,n);else if(t<16384)s(2,2,n),s(t,14,n);else{if(!(t<1e6))throw new RangeError("ECI assignment value out of range");s(6,3,n),s(t,21,n)}return new e(d.ECI,0,n)}},{key:"isNumeric",value:function(t){return e.NUMERIC_REGEX.test(t)}},{key:"isAlphanumeric",value:function(t){return e.ALPHANUMERIC_REGEX.test(t)}},{key:"getTotalBits",value:function(e,t){var n,r=0,o=(0,i.A)(e);try{for(o.s();!(n=o.n()).done;){var a=n.value,u=a.mode.numCharCountBits(t);if(a.numChars>=1<<u)return 1/0;r+=4+u+a.bitData.length}}catch(e){o.e(e)}finally{o.f()}return r}},{key:"toUtf8ByteArray",value:function(e){for(var t=encodeURI(e),n=[],r=0;r<t.length;r++)"%"!=t.charAt(r)?n.push(t.charCodeAt(r)):(n.push(parseInt(t.substring(r+1,r+3),16)),r+=2);return n}}]),e}();(0,l.A)(v,"NUMERIC_REGEX",/^[0-9]*$/),(0,l.A)(v,"ALPHANUMERIC_REGEX",/^[A-Z0-9 $%*+.\/:-]*$/),(0,l.A)(v,"ALPHANUMERIC_CHARSET","0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:");var m=function(){function e(t,n,r,o){(0,a.A)(this,e),(0,l.A)(this,"size",void 0),(0,l.A)(this,"mask",void 0),(0,l.A)(this,"modules",[]),(0,l.A)(this,"isFunction",[]),(0,l.A)(this,"version",void 0),(0,l.A)(this,"errorCorrectionLevel",void 0);var i=o;if(this.version=t,this.errorCorrectionLevel=n,t<e.MIN_VERSION||t>e.MAX_VERSION)throw new RangeError("Version value out of range");if(i<-1||i>7)throw new RangeError("Mask value out of range");this.size=4*t+17;for(var u=[],s=0;s<this.size;s++)u.push(!1);for(var c=0;c<this.size;c++)this.modules.push(u.slice()),this.isFunction.push(u.slice());this.drawFunctionPatterns();var d=this.addEccAndInterleave(r);if(this.drawCodewords(d),-1==i)for(var h=1e9,v=0;v<8;v++){this.applyMask(v),this.drawFormatBits(v);var m=this.getPenaltyScore();m<h&&(i=v,h=m),this.applyMask(v)}f(0<=i&&i<=7),this.mask=i,this.applyMask(i),this.drawFormatBits(i),this.isFunction=[]}return(0,u.A)(e,[{key:"getModule",value:function(e,t){return 0<=e&&e<this.size&&0<=t&&t<this.size&&this.modules[t][e]}},{key:"getModules",value:function(){return this.modules}},{key:"drawFunctionPatterns",value:function(){for(var e=0;e<this.size;e++)this.setFunctionModule(6,e,e%2==0),this.setFunctionModule(e,6,e%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);for(var t=this.getAlignmentPatternPositions(),n=t.length,r=0;r<n;r++)for(var o=0;o<n;o++)0==r&&0==o||0==r&&o==n-1||r==n-1&&0==o||this.drawAlignmentPattern(t[r],t[o]);this.drawFormatBits(0),this.drawVersion()}},{key:"drawFormatBits",value:function(e){for(var t=this.errorCorrectionLevel.formatBits<<3|e,n=t,r=0;r<10;r++)n=n<<1^1335*(n>>>9);var o=21522^(t<<10|n);f(o>>>15==0);for(var i=0;i<=5;i++)this.setFunctionModule(8,i,c(o,i));this.setFunctionModule(8,7,c(o,6)),this.setFunctionModule(8,8,c(o,7)),this.setFunctionModule(7,8,c(o,8));for(var a=9;a<15;a++)this.setFunctionModule(14-a,8,c(o,a));for(var u=0;u<8;u++)this.setFunctionModule(this.size-1-u,8,c(o,u));for(var l=8;l<15;l++)this.setFunctionModule(8,this.size-15+l,c(o,l));this.setFunctionModule(8,this.size-8,!0)}},{key:"drawVersion",value:function(){if(!(this.version<7)){for(var e=this.version,t=0;t<12;t++)e=e<<1^7973*(e>>>11);var n=this.version<<12|e;f(n>>>18==0);for(var r=0;r<18;r++){var o=c(n,r),i=this.size-11+r%3,a=Math.floor(r/3);this.setFunctionModule(i,a,o),this.setFunctionModule(a,i,o)}}}},{key:"drawFinderPattern",value:function(e,t){for(var n=-4;n<=4;n++)for(var r=-4;r<=4;r++){var o=Math.max(Math.abs(r),Math.abs(n)),i=e+r,a=t+n;0<=i&&i<this.size&&0<=a&&a<this.size&&this.setFunctionModule(i,a,2!=o&&4!=o)}}},{key:"drawAlignmentPattern",value:function(e,t){for(var n=-2;n<=2;n++)for(var r=-2;r<=2;r++)this.setFunctionModule(e+r,t+n,1!=Math.max(Math.abs(r),Math.abs(n)))}},{key:"setFunctionModule",value:function(e,t,n){this.modules[t][e]=n,this.isFunction[t][e]=!0}},{key:"addEccAndInterleave",value:function(t){var n=this.version,r=this.errorCorrectionLevel;if(t.length!=e.getNumDataCodewords(n,r))throw new RangeError("Invalid argument");for(var o=e.NUM_ERROR_CORRECTION_BLOCKS[r.ordinal][n],i=e.ECC_CODEWORDS_PER_BLOCK[r.ordinal][n],a=Math.floor(e.getNumRawDataModules(n)/8),u=o-a%o,l=Math.floor(a/o),s=[],c=e.reedSolomonComputeDivisor(i),d=0,h=0;d<o;d++){var v=t.slice(h,h+l-i+(d<u?0:1));h+=v.length;var m=e.reedSolomonComputeRemainder(v,c);d<u&&v.push(0),s.push(v.concat(m))}for(var g=[],p=function(e){s.forEach((function(t,n){(e!=l-i||n>=u)&&g.push(t[e])}))},y=0;y<s[0].length;y++)p(y);return f(g.length==a),g}},{key:"drawCodewords",value:function(t){if(t.length!=Math.floor(e.getNumRawDataModules(this.version)/8))throw new RangeError("Invalid argument");for(var n=0,r=this.size-1;r>=1;r-=2){6==r&&(r=5);for(var o=0;o<this.size;o++)for(var i=0;i<2;i++){var a=r-i,u=r+1&2?o:this.size-1-o;!this.isFunction[u][a]&&n<8*t.length&&(this.modules[u][a]=c(t[n>>>3],7-(7&n)),n++)}}f(n==8*t.length)}},{key:"applyMask",value:function(e){if(e<0||e>7)throw new RangeError("Mask value out of range");for(var t=0;t<this.size;t++)for(var n=0;n<this.size;n++){var r=void 0;switch(e){case 0:r=(n+t)%2==0;break;case 1:r=t%2==0;break;case 2:r=n%3==0;break;case 3:r=(n+t)%3==0;break;case 4:r=(Math.floor(n/3)+Math.floor(t/2))%2==0;break;case 5:r=n*t%2+n*t%3==0;break;case 6:r=(n*t%2+n*t%3)%2==0;break;case 7:r=((n+t)%2+n*t%3)%2==0;break;default:throw new Error("Unreachable")}!this.isFunction[t][n]&&r&&(this.modules[t][n]=!this.modules[t][n])}}},{key:"getPenaltyScore",value:function(){for(var t=0,n=0;n<this.size;n++){for(var r=!1,o=0,a=[0,0,0,0,0,0,0],u=0;u<this.size;u++)this.modules[n][u]==r?5==++o?t+=e.PENALTY_N1:o>5&&t++:(this.finderPenaltyAddHistory(o,a),r||(t+=this.finderPenaltyCountPatterns(a)*e.PENALTY_N3),r=this.modules[n][u],o=1);t+=this.finderPenaltyTerminateAndCount(r,o,a)*e.PENALTY_N3}for(var l=0;l<this.size;l++){for(var s=!1,c=0,d=[0,0,0,0,0,0,0],h=0;h<this.size;h++)this.modules[h][l]==s?5==++c?t+=e.PENALTY_N1:c>5&&t++:(this.finderPenaltyAddHistory(c,d),s||(t+=this.finderPenaltyCountPatterns(d)*e.PENALTY_N3),s=this.modules[h][l],c=1);t+=this.finderPenaltyTerminateAndCount(s,c,d)*e.PENALTY_N3}for(var v=0;v<this.size-1;v++)for(var m=0;m<this.size-1;m++){var g=this.modules[v][m];g==this.modules[v][m+1]&&g==this.modules[v+1][m]&&g==this.modules[v+1][m+1]&&(t+=e.PENALTY_N2)}var p,y=0,A=(0,i.A)(this.modules);try{for(A.s();!(p=A.n()).done;)y=p.value.reduce((function(e,t){return e+(t?1:0)}),y)}catch(e){A.e(e)}finally{A.f()}var E=this.size*this.size,b=Math.ceil(Math.abs(20*y-10*E)/E)-1;return f(0<=b&&b<=9),f(0<=(t+=b*e.PENALTY_N4)&&t<=2568888),t}},{key:"getAlignmentPatternPositions",value:function(){if(1==this.version)return[];for(var e=Math.floor(this.version/7)+2,t=32==this.version?26:2*Math.ceil((4*this.version+4)/(2*e-2)),n=[6],r=this.size-7;n.length<e;r-=t)n.splice(1,0,r);return n}},{key:"finderPenaltyCountPatterns",value:function(e){var t=e[1];f(t<=3*this.size);var n=t>0&&e[2]==t&&e[3]==3*t&&e[4]==t&&e[5]==t;return(n&&e[0]>=4*t&&e[6]>=t?1:0)+(n&&e[6]>=4*t&&e[0]>=t?1:0)}},{key:"finderPenaltyTerminateAndCount",value:function(e,t,n){var r=t;return e&&(this.finderPenaltyAddHistory(r,n),r=0),r+=this.size,this.finderPenaltyAddHistory(r,n),this.finderPenaltyCountPatterns(n)}},{key:"finderPenaltyAddHistory",value:function(e,t){var n=e;0==t[0]&&(n+=this.size),t.pop(),t.unshift(n)}}],[{key:"encodeText",value:function(t,n){var r=v.makeSegments(t);return e.encodeSegments(r,n)}},{key:"encodeBinary",value:function(t,n){var r=v.makeBytes(t);return e.encodeSegments([r],n)}},{key:"encodeSegments",value:function(t,n){var r,o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:40,l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1,c=!(arguments.length>5&&void 0!==arguments[5])||arguments[5];if(!(e.MIN_VERSION<=a&&a<=u&&u<=e.MAX_VERSION)||l<-1||l>7)throw new RangeError("Invalid value");for(r=a;;r++){var d=8*e.getNumDataCodewords(r,n),m=v.getTotalBits(t,r);if(m<=d){o=m;break}if(r>=u)throw new RangeError("Data too long")}for(var g=n,p=0,y=[h.MEDIUM,h.QUARTILE,h.HIGH];p<y.length;p++){var A=y[p];c&&o<=8*e.getNumDataCodewords(r,A)&&(g=A)}var E,b=[],w=(0,i.A)(t);try{for(w.s();!(E=w.n()).done;){var C=E.value;s(C.mode.modeBits,4,b),s(C.numChars,C.mode.numCharCountBits(r),b);var N,x=(0,i.A)(C.getData());try{for(x.s();!(N=x.n()).done;){var M=N.value;b.push(M)}}catch(e){x.e(e)}finally{x.f()}}}catch(e){w.e(e)}finally{w.f()}f(b.length==o);var R=8*e.getNumDataCodewords(r,g);f(b.length<=R),s(0,Math.min(4,R-b.length),b),s(0,(8-b.length%8)%8,b),f(b.length%8==0);for(var k=236;b.length<R;k^=253)s(k,8,b);for(var S=[];8*S.length<b.length;)S.push(0);return b.forEach((function(e,t){return S[t>>>3]|=e<<7-(7&t)})),new e(r,g,S,l)}},{key:"getNumRawDataModules",value:function(t){if(t<e.MIN_VERSION||t>e.MAX_VERSION)throw new RangeError("Version number out of range");var n=(16*t+128)*t+64;if(t>=2){var r=Math.floor(t/7)+2;n-=(25*r-10)*r-55,t>=7&&(n-=36)}return f(208<=n&&n<=29648),n}},{key:"getNumDataCodewords",value:function(t,n){return Math.floor(e.getNumRawDataModules(t)/8)-e.ECC_CODEWORDS_PER_BLOCK[n.ordinal][t]*e.NUM_ERROR_CORRECTION_BLOCKS[n.ordinal][t]}},{key:"reedSolomonComputeDivisor",value:function(t){if(t<1||t>255)throw new RangeError("Degree out of range");for(var n=[],r=0;r<t-1;r++)n.push(0);n.push(1);for(var o=1,i=0;i<t;i++){for(var a=0;a<n.length;a++)n[a]=e.reedSolomonMultiply(n[a],o),a+1<n.length&&(n[a]^=n[a+1]);o=e.reedSolomonMultiply(o,2)}return n}},{key:"reedSolomonComputeRemainder",value:function(t,n){var r,o=n.map((function(){return 0})),a=(0,i.A)(t);try{var u=function(){var t=r.value^o.shift();o.push(0),n.forEach((function(n,r){return o[r]^=e.reedSolomonMultiply(n,t)}))};for(a.s();!(r=a.n()).done;)u()}catch(e){a.e(e)}finally{a.f()}return o}},{key:"reedSolomonMultiply",value:function(e,t){if(e>>>8!=0||t>>>8!=0)throw new RangeError("Byte out of range");for(var n=0,r=7;r>=0;r--)n=n<<1^285*(n>>>7),n^=(t>>>r&1)*e;return f(n>>>8==0),n}}]),e}();(0,l.A)(m,"MIN_VERSION",1),(0,l.A)(m,"MAX_VERSION",40),(0,l.A)(m,"PENALTY_N1",3),(0,l.A)(m,"PENALTY_N2",3),(0,l.A)(m,"PENALTY_N3",40),(0,l.A)(m,"PENALTY_N4",10),(0,l.A)(m,"ECC_CODEWORDS_PER_BLOCK",[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]]),(0,l.A)(m,"NUM_ERROR_CORRECTION_BLOCKS",[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]]);var g={L:h.LOW,M:h.MEDIUM,Q:h.QUARTILE,H:h.HIGH},p="#FFFFFF",y="#000000",A=!1;function E(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=[];return e.forEach((function(e,r){var o=null;e.forEach((function(i,a){if(!i&&null!==o)return n.push("M".concat(o+t," ").concat(r+t,"h").concat(a-o,"v1H").concat(o+t,"z")),void(o=null);if(a!==e.length-1)i&&null===o&&(o=a);else{if(!i)return;null===o?n.push("M".concat(a+t,",").concat(r+t," h1v1H").concat(a+t,"z")):n.push("M".concat(o+t,",").concat(r+t," h").concat(a+1-o,"v1H").concat(o+t,"z"))}}))})),n.join("")}function b(e,t){return e.slice().map((function(e,n){return n<t.y||n>=t.y+t.h?e:e.map((function(e,n){return(n<t.x||n>=t.x+t.w)&&e}))}))}var w=function(){try{(new Path2D).addPath(new Path2D)}catch(e){return!1}return!0}(),C=n(58168),N=n(89379),x=n(5544),M=n(53986),R=n(96540);function k(e){var t=e.value,n=e.level,r=e.minVersion,o=e.includeMargin,i=e.marginSize,a=e.imageSettings,u=e.size,l=(0,R.useMemo)((function(){var e=v.makeSegments(t);return m.encodeSegments(e,g[n],r)}),[t,n,r]),s=(0,R.useMemo)((function(){var e=l.getModules(),t=function(e,t){return null!=t?Math.floor(t):e?4:0}(o,i),n=e.length+2*t,r=function(e,t,n,r){if(null==r)return null;var o=e.length+2*n,i=Math.floor(.1*t),a=o/t,u=(r.width||i)*a,l=(r.height||i)*a,s=null==r.x?e.length/2-u/2:r.x*a,c=null==r.y?e.length/2-l/2:r.y*a,f=null==r.opacity?1:r.opacity,d=null;if(r.excavate){var h=Math.floor(s),v=Math.floor(c);d={x:h,y:v,w:Math.ceil(u+s-h),h:Math.ceil(l+c-v)}}return{x:s,y:c,h:l,w:u,excavation:d,opacity:f,crossOrigin:r.crossOrigin}}(e,u,t,a);return{cells:e,margin:t,numCells:n,calculatedImageSettings:r}}),[l,u,a,o,i]),c=s.cells,f=s.margin,d=s.numCells,h=s.calculatedImageSettings;return{qrcode:l,margin:f,cells:c,numCells:d,calculatedImageSettings:h}}var S=["value","size","level","bgColor","fgColor","includeMargin","minVersion","marginSize","style","imageSettings"];R.forwardRef((function(e,t){var n=e.value,r=e.size,o=void 0===r?128:r,i=e.level,a=void 0===i?"L":i,u=e.bgColor,l=void 0===u?p:u,s=e.fgColor,c=void 0===s?y:s,f=e.includeMargin,d=void 0===f?A:f,h=e.minVersion,v=void 0===h?1:h,m=e.marginSize,g=e.style,P=e.imageSettings,I=(0,M.A)(e,S),F=null==P?void 0:P.src,O=(0,R.useRef)(null),D=(0,R.useRef)(null),L=(0,R.useCallback)((function(e){O.current=e,"function"==typeof t?t(e):t&&(t.current=e)}),[t]),z=(0,R.useState)(!1),_=(0,x.A)(z,2)[1],T=k({value:n,level:a,minVersion:v,includeMargin:d,marginSize:m,imageSettings:P,size:o}),B=T.margin,H=T.cells,q=T.numCells,V=T.calculatedImageSettings;(0,R.useEffect)((function(){if(null!=O.current){var e=O.current,t=e.getContext("2d");if(!t)return;var n=H,r=D.current,i=null!=V&&null!==r&&r.complete&&0!==r.naturalHeight&&0!==r.naturalWidth;i&&null!=V.excavation&&(n=b(H,V.excavation));var a=window.devicePixelRatio||1;e.height=e.width=o*a;var u=o/q*a;t.scale(u,u),t.fillStyle=l,t.fillRect(0,0,q,q),t.fillStyle=c,w?t.fill(new Path2D(E(n,B))):H.forEach((function(e,n){e.forEach((function(e,r){e&&t.fillRect(r+B,n+B,1,1)}))})),V&&(t.globalAlpha=V.opacity),i&&t.drawImage(r,V.x+B,V.y+B,V.w,V.h)}})),(0,R.useEffect)((function(){_(!1)}),[F]);var j=(0,N.A)({height:o,width:o},g),Y=null;return null!=F&&(Y=R.createElement("img",{src:F,key:F,style:{display:"none"},onLoad:function(){_(!0)},ref:D,crossOrigin:null==V?void 0:V.crossOrigin})),R.createElement(R.Fragment,null,R.createElement("canvas",(0,C.A)({style:j,height:o,width:o,ref:L,role:"img"},I)),Y)})).displayName="QRCodeCanvas";var P=["value","size","level","bgColor","fgColor","includeMargin","minVersion","title","marginSize","imageSettings"];R.forwardRef((function(e,t){var n=e.value,r=e.size,o=void 0===r?128:r,i=e.level,a=void 0===i?"L":i,u=e.bgColor,l=void 0===u?p:u,s=e.fgColor,c=void 0===s?y:s,f=e.includeMargin,d=void 0===f?A:f,h=e.minVersion,v=void 0===h?1:h,m=e.title,g=e.marginSize,w=e.imageSettings,N=(0,M.A)(e,P),x=k({value:n,level:a,minVersion:v,includeMargin:d,marginSize:g,imageSettings:w,size:o}),S=x.margin,I=x.cells,F=x.numCells,O=x.calculatedImageSettings,D=I,L=null;null!=w&&null!=O&&(null!=O.excavation&&(D=b(I,O.excavation)),L=R.createElement("image",{href:w.src,height:O.h,width:O.w,x:O.x+S,y:O.y+S,preserveAspectRatio:"none",opacity:O.opacity,crossOrigin:O.crossOrigin}));var z=E(D,S);return R.createElement("svg",(0,C.A)({height:o,width:o,viewBox:"0 0 ".concat(F," ").concat(F),ref:t,role:"img"},N),!!m&&R.createElement("title",null,m),R.createElement("path",{fill:l,d:"M0,0 h".concat(F,"v").concat(F,"H0z"),shapeRendering:"crispEdges"}),R.createElement("path",{fill:c,d:z,shapeRendering:"crispEdges"}),L)})).displayName="QRCodeSVG"}}]);