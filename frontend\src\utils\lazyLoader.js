/**
 * Lazy Loader Utility
 *
 * This utility provides functions to lazy load components with fallbacks.
 */
import React, { lazy, Suspense } from 'react';
import { Spin } from 'antd/es/spin';
import { LoadingOutlined } from '@ant-design/icons';
import ErrorBoundary from '../components/ErrorBoundary';
// Import only the CSS for components we use
import 'antd/es/spin/style/css';

/**
 * Default loading component
 */
export const DefaultLoadingComponent = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    padding: '50px'
  }}>
    <Spin
      indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />}
    >
      <div style={{ padding: '50px' }}>
        Loading...
      </div>
    </Spin>
  </div>
);

/**
 * Enhanced loading component with customizable message
 */
export const EnhancedLoadingComponent = ({
  message = 'Loading...',
  size = 'default',
  fullScreen = false
}) => {
  const spinProps = {
    indicator: <LoadingOutlined style={{ fontSize: size === 'large' ? 40 : 24 }} spin />,
    size: size,
    tip: message
  };

  if (fullScreen) {
    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'rgba(255, 255, 255, 0.95)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000
      }}>
        <Spin {...spinProps} />
      </div>
    );
  }

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '200px',
      padding: '20px'
    }}>
      <Spin {...spinProps}>
        <div style={{ padding: '50px' }} />
      </Spin>
    </div>
  );
};

/**
 * Default error component
 */
export const DefaultErrorComponent = ({ error, retry }) => (
  <div style={{
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    padding: '50px',
    color: '#ff4d4f',
    textAlign: 'center'
  }}>
    <h3>Error Loading Component</h3>
    <p>{error?.message || 'An error occurred while loading the component.'}</p>
    {retry && (
      <button
        onClick={retry}
        style={{
          marginTop: '16px',
          padding: '8px 16px',
          background: '#1890ff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Retry
      </button>
    )}
  </div>
);

/**
 * Enhanced lazy load a component with robust error handling and retry logic
 */
export const lazyWithFallback = (
  importFunc,
  {
    fallbackText = 'Loading...',
    fallbackDescription = '',
    size = 'default',
    fullPage = false,
    timeout = 10000,
    retry = 2,
    retryDelay = 1000
  } = {}
) => {
  const LazyComponent = lazy(() => {
    let retryCount = 0;

    const attemptImport = () => {
      return Promise.race([
        importFunc().catch(error => {
          if (retryCount < retry) {
            retryCount++;
            console.warn(`Component import failed, retrying (${retryCount}/${retry})...`, error);
            return new Promise(resolve =>
              setTimeout(() => resolve(attemptImport()), retryDelay)
            );
          }
          throw error;
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Loading timeout')), timeout)
        )
      ]);
    };

    return attemptImport();
  });

  return (props) => (
    <ErrorBoundary>
      <Suspense
        fallback={
          <EnhancedLoadingComponent
            message={fallbackText}
            size={size}
            fullScreen={fullPage}
          />
        }
      >
        <LazyComponent {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

/**
 * Preload a component
 * @param {Function} importFunc - The import function
 * @returns {Promise} - The import promise
 */
export const preloadComponent = (importFunc) => {
  const component = lazy(importFunc);
  component.preload = importFunc;
  return component;
};

/**
 * Preload multiple components
 * @param {Object} components - Object with component names as keys and import functions as values
 * @returns {Object} - Object with component names as keys and preloaded components as values
 */
export const preloadComponents = (components) => {
  const preloadedComponents = {};

  Object.entries(components).forEach(([name, importFunc]) => {
    preloadedComponents[name] = preloadComponent(importFunc);
  });

  return preloadedComponents;
};

/**
 * Enhanced route-level code splitting with preloading
 * @param {Function} importFunc - The import function for the route component
 * @param {Object} options - Configuration options
 * @returns {React.Component} - Lazy-loaded route component
 */
export const createRouteComponent = (importFunc, options = {}) => {
  const {
    preloadOnHover = true,
    preloadDelay = 100,
    ...lazyOptions
  } = options;

  const LazyRouteComponent = lazyWithFallback(importFunc, {
    fallbackText: 'Loading page...',
    size: 'large',
    fullPage: true,
    ...lazyOptions
  });

  // Add preloading capability
  if (preloadOnHover) {
    LazyRouteComponent.preload = () => {
      setTimeout(() => {
        importFunc().catch(() => {
          // Silently fail preload attempts
        });
      }, preloadDelay);
    };
  }

  return LazyRouteComponent;
};

/**
 * Lazy load with intersection observer for viewport-based loading
 * @param {Function} importFunc - The import function
 * @param {Object} options - Configuration options
 * @returns {React.Component} - Component that loads when in viewport
 */
export const lazyWithIntersection = (importFunc, options = {}) => {
  const {
    rootMargin = '50px',
    threshold = 0.1,
    ...lazyOptions
  } = options;

  return (props) => {
    const [shouldLoad, setShouldLoad] = React.useState(false);
    const ref = React.useRef();

    React.useEffect(() => {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setShouldLoad(true);
            observer.disconnect();
          }
        },
        { rootMargin, threshold }
      );

      if (ref.current) {
        observer.observe(ref.current);
      }

      return () => observer.disconnect();
    }, []);

    if (!shouldLoad) {
      return (
        <div ref={ref} style={{ minHeight: '100px' }}>
          <EnhancedLoadingComponent message="Preparing component..." />
        </div>
      );
    }

    const LazyComponent = lazyWithFallback(importFunc, lazyOptions);
    return <LazyComponent {...props} />;
  };
};

export default {
  lazyWithFallback,
  preloadComponent,
  preloadComponents,
  createRouteComponent,
  lazyWithIntersection,
  DefaultLoadingComponent,
  DefaultErrorComponent,
  EnhancedLoadingComponent
};

