"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[5202],{75202:(e,t,n)=>{n.r(t),n.d(t,{default:()=>ue});var a,r,o,l,c,i,p,s=n(60436),u=n(82284),m=n(64467),d=n(10467),f=n(5544),y=n(54756),g=n.n(y),E=n(96540),v=n(1807),A=n(35346),h=n(84447),b=n(57528),x=n(11080),w=n(11606),_=n(70572),k=(v.o5.Title,v.o5.Text),S=(0,_.Ay)(v.$x)(a||(a=(0,b.A)(["\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s;\n\n  &:hover {\n    background-color: rgba(0, 0, 0, 0.05);\n  }\n"])));(0,_.Ay)(v.Zp)(r||(r=(0,b.A)(["\n  max-width: 500px;\n  margin: 0 auto;\n"]))),_.Ay.div(o||(o=(0,b.A)(["\n  display: flex;\n  align-items: center;\n  gap: 16px;\n"]))),_.Ay.div(l||(l=(0,b.A)(["\n  flex: 1;\n"]))),_.Ay.div(c||(c=(0,b.A)(["\n  display: flex;\n  justify-content: space-around;\n  text-align: center;\n"]))),_.Ay.div(i||(i=(0,b.A)(["\n  padding: 0 16px;\n"]))),_.Ay.div(p||(p=(0,b.A)(["\n  display: flex;\n  justify-content: space-between;\n  gap: 8px;\n"])));const O=function(e){var t=e.onLogout,n=e.showAvatar,a=void 0===n||n,r=e.showName,o=void 0===r||r,l=e.showMenu,c=void 0===l||l,i=(0,E.useState)(w.A.getUser()),p=(0,f.A)(i,2),s=p[0],u=p[1];if((0,E.useEffect)((function(){return w.A.addListener((function(e){"login"===e||"register"===e?u(w.A.getUser()):"logout"===e&&u(null)}))}),[]),!s)return null;var m=[{key:"profile",label:"Profile",icon:E.createElement(A.qmv,null),onClick:function(){return window.location.href="/profile"}},{key:"settings",label:"Settings",icon:E.createElement(A.JO7,null),onClick:function(){return window.location.href="/settings"}},{key:"divider",type:"divider"},{key:"logout",label:"Logout",icon:E.createElement(A.$DG,null),onClick:function(){w.A.logout(),t&&t()}}];return!a||o||c?a&&o&&!c?E.createElement(v.$x,null,E.createElement(v.eu,{size:"default",src:s.avatar,icon:!s.avatar&&E.createElement(A.qmv,null),alt:s.name}),E.createElement(k,{strong:!0},s.name)):c?E.createElement(v.ms,{overlay:E.createElement(v.W1,{items:m}),trigger:["click"],placement:"bottomRight"},E.createElement(S,null,E.createElement(v.eu,{size:"default",src:s.avatar,icon:!s.avatar&&E.createElement(A.qmv,null),alt:s.name}),o&&E.createElement(k,{strong:!0},s.name),E.createElement(A.lHd,null))):E.createElement(k,{strong:!0},s.name):E.createElement(v.eu,{size:"large",src:s.avatar,icon:!s.avatar&&E.createElement(A.qmv,null),alt:s.name})};var C,N=(0,_.Ay)(v.$n)(C||(C=(0,b.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s;\n\n  &:hover {\n    transform: rotate(30deg);\n  }\n"])));const q=function(e){var t=e.isDarkMode,n=e.toggleTheme;return E.createElement(v.m_,{title:t?"Switch to Light Mode":"Switch to Dark Mode"},E.createElement(N,{type:"text",icon:t?E.createElement(A.o3f,null):E.createElement(A._r6,null),onClick:n,"aria-label":t?"Switch to Light Mode":"Switch to Dark Mode"}))};var L,P,T=n(17053),j=_.Ay.div(L||(L=(0,b.A)(["\n  display: inline-flex;\n  align-items: center;\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s;\n\n  &:hover {\n    background-color: rgba(0, 0, 0, 0.05);\n  }\n"]))),D=_.Ay.span(P||(P=(0,b.A)(["\n  margin-left: 8px;\n  font-size: 12px;\n  @media (max-width: 768px) {\n    display: none;\n  }\n"])));const U=function(){var e=(0,E.useState)("disconnected"),t=(0,f.A)(e,2),n=t[0],a=t[1],r=(0,E.useState)(null),o=(0,f.A)(r,2),l=o[0],c=o[1],i=(0,E.useState)(0),p=(0,f.A)(i,2),s=p[0],u=p[1],m=(0,E.useState)(!1),d=(0,f.A)(m,2),y=d[0],g=d[1],h=(0,E.useState)(navigator.onLine),b=(0,f.A)(h,2),x=b[0],w=b[1],_=(0,E.useState)(null),k=(0,f.A)(_,2),S=k[0],O=k[1];(0,E.useEffect)((function(){a(T.A.isConnected?"connected":"disconnected"),w(navigator.onLine),u(0);try{if("function"==typeof T.A.getOfflineQueueStatus){var e=T.A.getOfflineQueueStatus();O(e)}}catch(e){console.error("Error getting offline queue status:",e)}var t=function(){a("connected"),u(0)},n=function(e){a("disconnected"),e&&e.code&&c({message:"Disconnected (code: ".concat(e.code,")"),timestamp:new Date})},r=function(){a("connecting")},o=function(e){a("error"),c(e)},l=function(e){a("connecting"),u(e.attempt)},i=function(e){w(e.online)},p=function(){try{if("function"==typeof T.A.getOfflineQueueStatus){var e=T.A.getOfflineQueueStatus();O(e)}}catch(e){console.error("Error getting updated offline queue status:",e)}};T.A.on("connect",t),T.A.on("disconnect",n),T.A.on("connecting",r),T.A.on("error",o),T.A.on("reconnect_scheduled",l),T.A.on("network_status",i),"function"==typeof T.A.on&&"function"==typeof T.A.getOfflineQueueStatus&&(T.A.on("message_queued_offline",p),T.A.on("offline_queue_loaded",p),T.A.on("offline_queue_cleared",p));var s=function(){return w(!0)},m=function(){return w(!1)};return window.addEventListener("online",s),window.addEventListener("offline",m),function(){T.A.off("connect",t),T.A.off("disconnect",n),T.A.off("connecting",r),T.A.off("error",o),T.A.off("reconnect_scheduled",l),T.A.off("network_status",i),"function"==typeof T.A.off&&"function"==typeof T.A.getOfflineQueueStatus&&(T.A.off("message_queued_offline",p),T.A.off("offline_queue_loaded",p),T.A.off("offline_queue_cleared",p)),window.removeEventListener("online",s),window.removeEventListener("offline",m)}}),[]);var C=function(){if(!x){var e=(null==S?void 0:S.count)||0;return{icon:E.createElement(A.gDm,{style:{color:"#faad14"}}),text:e>0?"Offline (".concat(e,")"):"Offline",status:"warning",tooltip:"Browser is offline. "+(e>0?"".concat(e," messages queued for delivery when online."):"No queued messages.")}}if(x&&(null==S?void 0:S.count)>0&&"connected"===n)return{icon:E.createElement(A.Gkj,{style:{color:"#1890ff"}}),text:"Syncing (".concat(S.count,")"),status:"processing",tooltip:"Connected. Syncing ".concat(S.count," queued messages.")};switch(n){case"connected":return{icon:E.createElement(A.t7c,{style:{color:"#52c41a"}}),text:"Connected",status:"success",tooltip:"WebSocket connection established"};case"disconnected":return{icon:E.createElement(A.Bu6,{style:{color:"#bfbfbf"}}),text:"Disconnected",status:"default",tooltip:l?"Disconnected: ".concat(l.message):"WebSocket disconnected"};case"connecting":return{icon:E.createElement(A.NKq,{style:{color:"#1890ff"}}),text:s>0?"Reconnecting (".concat(s,")"):"Connecting",status:"processing",tooltip:"Attempting to establish WebSocket connection"};case"error":return{icon:E.createElement(A.v7y,{style:{color:"#ff4d4f"}}),text:"Connection Error",status:"error",tooltip:l?"Error: ".concat(l.message):"WebSocket connection error"};default:return{icon:E.createElement(A.Bu6,{style:{color:"#bfbfbf"}}),text:"Unknown",status:"default",tooltip:"WebSocket status unknown"}}}();return E.createElement(v.m_,{title:E.createElement("div",null,E.createElement("div",null,C.tooltip),l&&E.createElement("div",{style:{marginTop:4}},"Last error: ",l.message,l.timestamp&&E.createElement("div",{style:{fontSize:"0.8em",opacity:.8}},new Date(l.timestamp).toLocaleTimeString())),(null==S?void 0:S.count)>0&&E.createElement("div",{style:{marginTop:4,borderTop:"1px solid rgba(255,255,255,0.2)",paddingTop:4}},E.createElement("div",null,"Offline queue: ",S.count," message",1!==S.count?"s":""),E.createElement("div",{style:{marginTop:4}},E.createElement("button",{onClick:function(e){if(e.stopPropagation(),(null==S?void 0:S.count)>0&&"function"==typeof T.A.clearOfflineQueue){var t=T.A.clearOfflineQueue();alert("Cleared ".concat(t," messages from the offline queue."))}},style:{background:"rgba(255,255,255,0.2)",border:"none",padding:"2px 8px",borderRadius:"4px",cursor:"pointer",fontSize:"0.8em"}},"Clear Queue"))),!x&&E.createElement("div",{style:{marginTop:8,fontSize:"0.9em"}},"You are offline. Check your internet connection."),x&&"connected"!==n&&E.createElement("div",{style:{marginTop:8,fontSize:"0.9em"}},"Click to reconnect"),x&&"connected"===n&&(null==S?void 0:S.count)>0&&E.createElement("div",{style:{marginTop:8,fontSize:"0.9em"}},"Click to sync offline messages")),open:y,onOpenChange:g},E.createElement(j,{onClick:function(){x?"connected"!==n?T.A.reconnect():(null==S?void 0:S.count)>0&&"function"==typeof T.A.processOfflineQueue&&T.A.processOfflineQueue():alert("You are currently offline. Please check your internet connection.")},"data-testid":"websocket-status"},E.createElement(v.Ex,{status:C.status}),E.createElement(v.$x,{size:4},C.icon,E.createElement(D,null,C.text))))};var I,J,F,Q,B=v.PE.Header,z=(0,_.Ay)(B)(I||(I=(0,b.A)(["\n  display: flex;\n  align-items: center;\n  background-color: #fff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  padding: 0 24px;\n  height: 64px;\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n"]))),M=_.Ay.div(J||(J=(0,b.A)(["\n  font-size: 20px;\n  font-weight: bold;\n  margin-right: 48px;\n\n  a {\n    color: #1890ff;\n    text-decoration: none;\n  }\n"]))),R=(0,_.Ay)(v.W1)(F||(F=(0,b.A)(["\n  flex: 1;\n  border-bottom: none;\n  background: transparent;\n"]))),V=_.Ay.div(Q||(Q=(0,b.A)(["\n  display: flex;\n  align-items: center;\n"])));const $=function(){var e=(0,x.Zp)(),t=(0,x.zy)(),n=w.A.isAuthenticated(),a=t.pathname;return E.createElement(z,null,E.createElement(M,null,E.createElement(x.N_,{to:"/"},"App Builder")),E.createElement(R,{theme:"light",mode:"horizontal",selectedKeys:[a]},E.createElement(v.W1.Item,{key:"/",icon:E.createElement(A.aod,null)},E.createElement(x.N_,{to:"/"},"Home")),E.createElement(v.W1.Item,{key:"/apps",icon:E.createElement(A.rS9,null)},E.createElement(x.N_,{to:"/apps"},"My Apps")),E.createElement(v.W1.Item,{key:"/templates",icon:E.createElement(A.rS9,null)},E.createElement(x.N_,{to:"/templates"},"Templates"))),E.createElement(V,null,E.createElement(U,null),E.createElement(v.cG,{type:"vertical",style:{margin:"0 8px"}}),E.createElement(q,null),n?E.createElement(O,{showAvatar:!0,showName:!0,showMenu:!0,onLogout:function(){w.A.logout(),e("/")}}):E.createElement(v.$x,{split:E.createElement(v.cG,{type:"vertical"})},E.createElement(v.$n,{type:"link",icon:E.createElement(A.W8X,null),onClick:function(){e("/auth?tab=login")}},"Login"),E.createElement(v.$n,{type:"primary",icon:E.createElement(A.Nnt,null),onClick:function(){e("/auth?tab=register")}},"Register"))))};n(53986);var G,W,Y,H=function(e){var t=e.targetId,n=void 0===t?"main-content":t,a=e.children,r=void 0===a?"Skip to main content":a;return E.createElement("a",{href:"#".concat(n),className:"skip-link","aria-label":r},r,E.createElement("style",null,"\n        .skip-link {\n          position: absolute;\n          top: -40px;\n          left: 0;\n          background: #2563EB;\n          color: white;\n          padding: 8px;\n          z-index: 100;\n          transition: top 0.3s;\n        }\n\n        .skip-link:focus {\n          top: 0;\n        }\n      "))},K=v.PE.Content,X=v.PE.Footer,Z=(0,_.Ay)(v.PE)(G||(G=(0,b.A)(["\n  min-height: 100vh;\n"]))),ee=(0,_.Ay)(K)(W||(W=(0,b.A)(["\n  padding: 24px;\n  background-color: #f5f5f5;\n  min-height: calc(100vh - 64px - 70px); /* Viewport height - header - footer */\n"]))),te=(0,_.Ay)(X)(Y||(Y=(0,b.A)(["\n  text-align: center;\n  padding: 24px;\n  background-color: #fff;\n"])));const ne=function(e){var t=e.children;return E.createElement(Z,null,E.createElement(H,{targetId:"main-content"}),E.createElement($,null),E.createElement(ee,{id:"main-content"},t),E.createElement(te,null,"App Builder ©",(new Date).getFullYear()," - Build with ease"),E.createElement(v.XT,null))};function ae(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function re(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ae(Object(n),!0).forEach((function(t){(0,m.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ae(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var oe=v.o5.Title,le=v.o5.Text,ce=v.o5.Paragraph,ie=v.l6.Option,pe=v.tU.TabPane,se=v.pd.TextArea;const ue=function(){var e=(0,E.useState)([]),t=(0,f.A)(e,2),n=t[0],a=t[1],r=(0,E.useState)([]),o=(0,f.A)(r,2),l=o[0],c=o[1],i=(0,E.useState)([]),p=(0,f.A)(i,2),m=p[0],y=p[1],b=(0,E.useState)(!0),x=(0,f.A)(b,2),w=x[0],_=x[1],k=(0,E.useState)(""),S=(0,f.A)(k,2),O=S[0],C=S[1],N=(0,E.useState)(""),q=(0,f.A)(N,2),L=q[0],P=q[1],T=(0,E.useState)(""),j=(0,f.A)(T,2),D=j[0],U=j[1],I=(0,E.useState)("all"),J=(0,f.A)(I,2),F=J[0],Q=J[1],B=(0,E.useState)(!1),z=(0,f.A)(B,2),M=z[0],R=z[1],V=(0,E.useState)(!1),$=(0,f.A)(V,2),G=$[0],W=$[1],Y=(0,E.useState)(!1),H=(0,f.A)(Y,2),K=H[0],X=H[1],Z=(0,E.useState)(null),ee=(0,f.A)(Z,2),te=ee[0],ae=ee[1],ue=v.lV.useForm(),me=(0,f.A)(ue,1)[0],de=(0,E.useState)(!1),fe=(0,f.A)(de,2),ye=fe[0],ge=fe[1],Ee=(0,E.useState)("my"),ve=(0,f.A)(Ee,2),Ae=ve[0],he=ve[1],be=(0,E.useState)("components"),xe=(0,f.A)(be,2),we=xe[0],_e=xe[1],ke=["button","input","select","checkbox","radio","switch","slider","date-picker","time-picker","upload","form","table","list","card","tabs","modal","drawer","menu","layout","custom"],Se=["grid","flex","sidebar","header-footer","dashboard","landing","blog","portfolio","ecommerce","admin","custom"],Oe=[{value:"business",label:"Business Apps"},{value:"ecommerce",label:"E-commerce"},{value:"portfolio",label:"Portfolio"},{value:"dashboard",label:"Dashboard"},{value:"landing",label:"Landing Page"},{value:"blog",label:"Blog"},{value:"social",label:"Social Media"},{value:"education",label:"Education"},{value:"healthcare",label:"Healthcare"},{value:"finance",label:"Finance"},{value:"other",label:"Other"}];(0,E.useEffect)((function(){Ce()}),[Ae,we]);var Ce=function(){var e=(0,d.A)(g().mark((function e(){return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return _(!0),e.prev=1,e.next=4,Promise.all([Ne(),qe(),Le()]);case 4:e.next=10;break;case 6:e.prev=6,e.t0=e.catch(1),console.error("Error fetching templates:",e.t0),v.iU.error("Failed to load templates");case 10:return e.prev=10,_(!1),e.finish(10);case 13:case"end":return e.stop()}}),e,null,[[1,6,10,13]])})));return function(){return e.apply(this,arguments)}}(),Ne=function(){var e=(0,d.A)(g().mark((function e(){var t,n,r;return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t="/api/component-templates/",n=new URLSearchParams,"my"===Ae?n.append("user","current"):"public"===Ae&&n.append("is_public","true"),n.toString()&&(t+="?".concat(n.toString())),e.next=7,h.Ay.get(t);case 7:r=e.sent,a(r.data.results||r.data),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("Error fetching component templates:",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(){return e.apply(this,arguments)}}(),qe=function(){var e=(0,d.A)(g().mark((function e(){var t,n,a;return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t="/api/layout-templates/",n=new URLSearchParams,"my"===Ae?n.append("user","current"):"public"===Ae&&n.append("is_public","true"),n.toString()&&(t+="?".concat(n.toString())),e.next=7,h.Ay.get(t);case 7:a=e.sent,c(a.data.results||a.data),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("Error fetching layout templates:",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(){return e.apply(this,arguments)}}(),Le=function(){var e=(0,d.A)(g().mark((function e(){var t,n,a;return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t="/api/app-templates/",n=new URLSearchParams,"my"===Ae?n.append("user","current"):"public"===Ae&&n.append("is_public","true"),n.toString()&&(t+="?".concat(n.toString())),e.next=7,h.Ay.get(t);case 7:a=e.sent,y(a.data.results||a.data),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("Error fetching app templates:",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(){return e.apply(this,arguments)}}(),Pe=function(){var e=(0,d.A)(g().mark((function e(t){var n,a;return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ge(!0),e.prev=1,"components"===we?(n="/api/component-templates/",a=re(re({},t),{},{default_props:JSON.stringify(t.default_props||{})})):"layouts"===we?(n="/api/layout-templates/",a=re(re({},t),{},{components:JSON.stringify(t.components||{}),default_props:JSON.stringify(t.default_props||{})})):"apps"===we&&(n="/api/app-templates/",a=re(re({},t),{},{components:JSON.stringify(t.components||{}),default_props:JSON.stringify(t.default_props||{}),required_components:JSON.stringify(t.required_components||[])})),e.next=5,h.Ay.post(n,a);case 5:v.iU.success("Template created successfully"),R(!1),me.resetFields(),Ce(),e.next=15;break;case 11:e.prev=11,e.t0=e.catch(1),console.error("Error creating template:",e.t0),v.iU.error("Failed to create template");case 15:return e.prev=15,ge(!1),e.finish(15);case 18:case"end":return e.stop()}}),e,null,[[1,11,15,18]])})));return function(t){return e.apply(this,arguments)}}(),Te=function(){var e=(0,d.A)(g().mark((function e(t){var n,a;return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ge(!0),e.prev=1,"component"===te.template_type?(n="/api/component-templates/".concat(te.id,"/"),a=re(re({},t),{},{default_props:"object"===(0,u.A)(t.default_props)?JSON.stringify(t.default_props):t.default_props})):"layout"===te.template_type?(n="/api/layout-templates/".concat(te.id,"/"),a=re(re({},t),{},{components:"object"===(0,u.A)(t.components)?JSON.stringify(t.components):t.components,default_props:"object"===(0,u.A)(t.default_props)?JSON.stringify(t.default_props):t.default_props})):"app"===te.template_type&&(n="/api/app-templates/".concat(te.id,"/"),a=re(re({},t),{},{components:"object"===(0,u.A)(t.components)?JSON.stringify(t.components):t.components,default_props:"object"===(0,u.A)(t.default_props)?JSON.stringify(t.default_props):t.default_props,required_components:"object"===(0,u.A)(t.required_components)?JSON.stringify(t.required_components):t.required_components})),e.next=5,h.Ay.patch(n,a);case 5:v.iU.success("Template updated successfully"),W(!1),Ce(),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(1),console.error("Error updating template:",e.t0),v.iU.error("Failed to update template");case 14:return e.prev=14,ge(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[1,10,14,17]])})));return function(t){return e.apply(this,arguments)}}(),je=function(){var e=(0,d.A)(g().mark((function e(t){var n;return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,"component"===t.template_type?n="/api/component-templates/".concat(t.id,"/"):"layout"===t.template_type?n="/api/layout-templates/".concat(t.id,"/"):"app"===t.template_type&&(n="/api/app-templates/".concat(t.id,"/")),e.next=4,h.Ay.delete(n);case 4:v.iU.success("Template deleted successfully"),Ce(),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("Error deleting template:",e.t0),v.iU.error("Failed to delete template");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),De=function(){switch(we){case"layouts":return l.map((function(e){return re(re({},e),{},{template_type:"layout"})}));case"apps":return m.map((function(e){return re(re({},e),{},{template_type:"app"})}));default:return n.map((function(e){return re(re({},e),{},{template_type:"component"})}))}},Ue=function(){switch(we){case"layouts":return"Layout Template";case"apps":return"App Template";default:return"Component Template"}},Ie=De().filter((function(e){var t=""===O||e.name.toLowerCase().includes(O.toLowerCase())||e.description.toLowerCase().includes(O.toLowerCase()),n=!0;"components"===we&&L?n=e.component_type===L:"layouts"===we&&L?n=e.layout_type===L:"apps"===we&&D&&(n=e.app_category===D);var a="all"===F||"public"===F&&e.is_public||"private"===F&&!e.is_public;return t&&n&&a})),Je=function(){var e=De();switch(we){case"layouts":return(0,s.A)(new Set(e.map((function(e){return e.layout_type}))));case"apps":return(0,s.A)(new Set(e.map((function(e){return e.app_category}))));default:return(0,s.A)(new Set(e.map((function(e){return e.component_type}))))}},Fe=function(e){return E.createElement(v.B8.Item,null,E.createElement(v.Zp,{title:E.createElement(v.$x,null,function(){switch(e.template_type){case"layout":return E.createElement(A.hy2,null);case"app":return E.createElement(A.jHj,null);default:return E.createElement(A.rS9,null)}}(),E.createElement(le,{strong:!0},e.name),e.is_public?E.createElement(v.vw,{icon:E.createElement(A.Om2,null),color:"green"},"Public"):E.createElement(v.vw,{icon:E.createElement(A.sXv,null),color:"default"},"Private"),"app"===e.template_type&&e.preview_image&&E.createElement(v.vw,{icon:E.createElement(A.L0Y,null),color:"gold"},"Featured")),extra:E.createElement(v.$x,null,E.createElement(v.m_,{title:"Use Template"},E.createElement(v.$n,{icon:E.createElement(A.wq3,null)})),E.createElement(v.m_,{title:"Export Template"},E.createElement(v.$n,{icon:E.createElement(A.jsW,null),onClick:function(){return Qe(e)}})),E.createElement(v.m_,{title:"Edit Template"},E.createElement(v.$n,{icon:E.createElement(A.xjh,null),onClick:function(){return function(e){ae(e);var t="string"==typeof e.default_props?JSON.parse(e.default_props):e.default_props,n=e.components&&"string"==typeof e.components?JSON.parse(e.components):e.components,a=e.required_components&&"string"==typeof e.required_components?JSON.parse(e.required_components):e.required_components,r={name:e.name,description:e.description,default_props:t,is_public:e.is_public};"component"===e.template_type?r.component_type=e.component_type:"layout"===e.template_type?(r.layout_type=e.layout_type,r.components=n):"app"===e.template_type&&(r.app_category=e.app_category,r.components=n,r.required_components=a,r.preview_image=e.preview_image),me.setFieldsValue(r),W(!0)}(e)}})),E.createElement(v.m_,{title:"Delete Template"},E.createElement(v.$n,{danger:!0,icon:E.createElement(A.SUY,null),onClick:function(){return v.aF.confirm({title:"Delete Template",content:'Are you sure you want to delete "'.concat(e.name,'"?'),okText:"Delete",okType:"danger",onOk:function(){return je(e)}})}}))),style:{width:"100%"},cover:"app"===e.template_type&&e.preview_image?E.createElement(v._V,{alt:e.name,src:e.preview_image,height:120,style:{objectFit:"cover"},fallback:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"}):null},E.createElement(ce,{ellipsis:{rows:2}},e.description||"No description provided"),E.createElement(v.$x,{wrap:!0},function(){switch(e.template_type){case"layout":return E.createElement(v.vw,{color:"purple"},e.layout_type);case"app":return E.createElement(v.vw,{color:"orange"},e.app_category);default:return E.createElement(v.vw,{color:"blue"},e.component_type)}}(),"app"===e.template_type&&e.required_components&&e.required_components.length>0&&E.createElement(v.vw,{color:"cyan"},E.createElement(A.o3f,null)," ",e.required_components.length," dependencies"),E.createElement(le,{type:"secondary"},"Created by: ",e.user?e.user.username:"Anonymous"),E.createElement(le,{type:"secondary"},"Created: ",new Date(e.created_at).toLocaleDateString()))))},Qe=function(){var e=(0,d.A)(g().mark((function e(t){var n,a,r,o,l;return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,"component"===t.template_type?n="/api/component-templates/".concat(t.id,"/export_template/"):"layout"===t.template_type?n="/api/layout-templates/".concat(t.id,"/export_template/"):"app"===t.template_type&&(n="/api/app-templates/".concat(t.id,"/export_template/")),e.next=4,h.Ay.get(n);case 4:a=e.sent,r=JSON.stringify(a.data,null,2),o=new Blob([r],{type:"application/json"}),(l=document.createElement("a")).href=URL.createObjectURL(o),l.download="".concat(t.name.replace(/\s+/g,"_"),"_template.json"),l.click(),v.iU.success("Template exported successfully"),e.next=18;break;case 14:e.prev=14,e.t0=e.catch(0),console.error("Error exporting template:",e.t0),v.iU.error("Failed to export template");case 18:case"end":return e.stop()}}),e,null,[[0,14]])})));return function(t){return e.apply(this,arguments)}}(),Be=function(){return E.createElement(v.lV,{form:me,layout:"vertical",onFinish:te?Te:Pe},E.createElement(v.lV.Item,{name:"name",label:"Template Name",rules:[{required:!0,message:"Please enter template name"}]},E.createElement(v.pd,{placeholder:"Enter template name"})),E.createElement(v.lV.Item,{name:"description",label:"Description"},E.createElement(se,{placeholder:"Enter template description",rows:4,maxLength:500,showCount:!0})),"components"===we&&E.createElement(v.lV.Item,{name:"component_type",label:"Component Type",rules:[{required:!0,message:"Please select component type"}]},E.createElement(v.l6,{placeholder:"Select component type"},ke.map((function(e){return E.createElement(ie,{key:e,value:e},e)})))),"layouts"===we&&E.createElement(v.lV.Item,{name:"layout_type",label:"Layout Type",rules:[{required:!0,message:"Please select layout type"}]},E.createElement(v.l6,{placeholder:"Select layout type"},Se.map((function(e){return E.createElement(ie,{key:e,value:e},e)})))),"apps"===we&&E.createElement(v.lV.Item,{name:"app_category",label:"App Category",rules:[{required:!0,message:"Please select app category"}]},E.createElement(v.l6,{placeholder:"Select app category"},Oe.map((function(e){return E.createElement(ie,{key:e.value,value:e.value},e.label)})))),("layouts"===we||"apps"===we)&&E.createElement(v.lV.Item,{name:"components",label:"Components Configuration",rules:[{required:!0,message:"Please enter components configuration"},{validator:function(e,t){try{return"string"==typeof t&&JSON.parse(t),Promise.resolve()}catch(e){return Promise.reject("Please enter valid JSON")}}}]},E.createElement(se,{placeholder:"Enter components configuration in JSON format",rows:8,style:{fontFamily:"monospace"}})),"apps"===we&&E.createElement(v.lV.Item,{name:"required_components",label:"Required Components",rules:[{validator:function(e,t){try{if(t&&"string"==typeof t){var n=JSON.parse(t);if(!Array.isArray(n))return Promise.reject("Required components must be an array")}return Promise.resolve()}catch(e){return Promise.reject("Please enter valid JSON array")}}}]},E.createElement(se,{placeholder:'Enter required components as JSON array, e.g., ["button", "input"]',rows:3,style:{fontFamily:"monospace"}})),"apps"===we&&E.createElement(v.lV.Item,{name:"preview_image",label:"Preview Image URL"},E.createElement(v.pd,{placeholder:"Enter preview image URL (optional)"})),E.createElement(v.lV.Item,{name:"default_props",label:"Default Properties",rules:[{validator:function(e,t){try{return t&&"string"==typeof t&&JSON.parse(t),Promise.resolve()}catch(e){return Promise.reject("Please enter valid JSON")}}}]},E.createElement(se,{placeholder:"Enter default properties in JSON format (optional)",rows:4,style:{fontFamily:"monospace"}})),E.createElement(v.lV.Item,{name:"is_public",label:"Visibility",valuePropName:"checked"},E.createElement(v.dO,{checkedChildren:E.createElement(A.Om2,null),unCheckedChildren:E.createElement(A.sXv,null)}),E.createElement(le,{type:"secondary",style:{marginLeft:8}},"Make this template public")))},ze=function(){var e=(0,d.A)(g().mark((function e(t){var n;return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,"components"===we?n="/api/component-templates/import_template/":"layouts"===we?n="/api/layout-templates/import_template/":"apps"===we&&(n="/api/app-templates/import_template/"),e.next=4,h.Ay.post(n,{template_data:t});case 4:v.iU.success("Template imported successfully"),X(!1),Ce(),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(0),console.error("Error importing template:",e.t0),v.iU.error("Failed to import template");case 13:case"end":return e.stop()}}),e,null,[[0,9]])})));return function(t){return e.apply(this,arguments)}}();return E.createElement(ne,null,E.createElement("div",{className:"templates-page"},E.createElement("div",{className:"templates-header"},E.createElement(oe,{level:2},E.createElement(A.rS9,null)," Template Library"),E.createElement(v.$x,null,E.createElement(v.$n,{icon:E.createElement(A.qvO,null),onClick:function(){return X(!0)}},"Import Template"),E.createElement(v.$n,{type:"primary",icon:E.createElement(A.bW0,null),onClick:function(){me.resetFields(),R(!0)}},"Create ",Ue()))),E.createElement("div",{className:"template-type-tabs"},E.createElement(v.tU,{activeKey:we,onChange:_e,type:"card",size:"large"},E.createElement(pe,{tab:E.createElement("span",null,E.createElement(A.rS9,null),"Components",E.createElement(v.Ex,{count:n.length,style:{marginLeft:8}})),key:"components"}),E.createElement(pe,{tab:E.createElement("span",null,E.createElement(A.hy2,null),"Layouts",E.createElement(v.Ex,{count:l.length,style:{marginLeft:8}})),key:"layouts"}),E.createElement(pe,{tab:E.createElement("span",null,E.createElement(A.jHj,null),"App Starters",E.createElement(v.Ex,{count:m.length,style:{marginLeft:8}})),key:"apps"}))),E.createElement(v.tU,{activeKey:Ae,onChange:he,className:"templates-tabs"},E.createElement(pe,{tab:E.createElement("span",null,E.createElement(A.qmv,null)," My Templates"),key:"my"},E.createElement("div",{className:"templates-filters"},E.createElement(v.pd,{placeholder:"Search templates",prefix:E.createElement(A.VrN,null),value:O,onChange:function(e){return C(e.target.value)},style:{width:250}}),E.createElement(v.l6,{placeholder:"Filter by ".concat("components"===we?"component type":"layouts"===we?"layout type":"category"),value:"apps"===we?D:L,onChange:"apps"===we?U:P,style:{width:200},allowClear:!0,suffixIcon:E.createElement(A.Lxx,null)},"apps"===we?Oe.map((function(e){return E.createElement(ie,{key:e.value,value:e.value},e.label)})):Je().map((function(e){return E.createElement(ie,{key:e,value:e},e)}))),E.createElement(v.l6,{placeholder:"Filter by visibility",value:F,onChange:Q,style:{width:150},suffixIcon:E.createElement(A.Lxx,null)},E.createElement(ie,{value:"all"},"All"),E.createElement(ie,{value:"public"},"Public"),E.createElement(ie,{value:"private"},"Private"))),E.createElement("div",{className:"templates-list"},E.createElement(v.tK,{spinning:w},Ie.length>0?E.createElement(v.B8,{grid:{gutter:16,xs:1,sm:1,md:2,lg:2,xl:3,xxl:3},dataSource:Ie,renderItem:Fe,pagination:{pageSize:9,hideOnSinglePage:!0}}):E.createElement(v.Sv,{description:E.createElement("span",null,w?"Loading templates...":"No ".concat(we," templates found"))})))),E.createElement(pe,{tab:E.createElement("span",null,E.createElement(A.clv,null)," Public Templates"),key:"public"},E.createElement("div",{className:"templates-filters"},E.createElement(v.pd,{placeholder:"Search templates",prefix:E.createElement(A.VrN,null),value:O,onChange:function(e){return C(e.target.value)},style:{width:250}}),E.createElement(v.l6,{placeholder:"Filter by ".concat("components"===we?"component type":"layouts"===we?"layout type":"category"),value:"apps"===we?D:L,onChange:"apps"===we?U:P,style:{width:200},allowClear:!0,suffixIcon:E.createElement(A.Lxx,null)},"apps"===we?Oe.map((function(e){return E.createElement(ie,{key:e.value,value:e.value},e.label)})):Je().map((function(e){return E.createElement(ie,{key:e,value:e},e)})))),E.createElement("div",{className:"templates-list"},E.createElement(v.tK,{spinning:w},Ie.length>0?E.createElement(v.B8,{grid:{gutter:16,xs:1,sm:1,md:2,lg:2,xl:3,xxl:3},dataSource:Ie,renderItem:Fe,pagination:{pageSize:9,hideOnSinglePage:!0}}):E.createElement(v.Sv,{description:E.createElement("span",null,w?"Loading templates...":"No public ".concat(we," templates found"))}))))),E.createElement(v.aF,{title:"Create ".concat(Ue()),open:M,onCancel:function(){return R(!1)},onOk:function(){return me.submit()},okText:"Create",confirmLoading:ye,width:800},Be()),E.createElement(v.aF,{title:"Edit ".concat("component"===(null==te?void 0:te.template_type)?"Component":"layout"===(null==te?void 0:te.template_type)?"Layout":"App"," Template"),open:G,onCancel:function(){return W(!1)},onOk:function(){return me.submit()},okText:"Save Changes",confirmLoading:ye,width:800},Be()),E.createElement(v.aF,{title:"Import Template",open:K,onCancel:function(){return X(!1)},footer:null,width:600},E.createElement(v._O.Dragger,{name:"file",multiple:!1,accept:".json",beforeUpload:function(e){var t=new FileReader;return t.onload=function(){var e=(0,d.A)(g().mark((function e(t){var n;return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,n=JSON.parse(t.target.result),e.next=4,ze(n);case 4:e.next=9;break;case 6:e.prev=6,e.t0=e.catch(0),v.iU.error("Invalid JSON file");case 9:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(t){return e.apply(this,arguments)}}(),t.readAsText(e),!1}},E.createElement("p",{className:"ant-upload-drag-icon"},E.createElement(A.qvO,null)),E.createElement("p",{className:"ant-upload-text"},"Click or drag file to this area to upload"),E.createElement("p",{className:"ant-upload-hint"},"Support for JSON template files only. The template will be imported to the current template type (",we,").")))),E.createElement("style",{jsx:"true"},"\n        .templates-page {\n          max-width: 1400px;\n          margin: 0 auto;\n          padding: 0 16px;\n        }\n\n        .templates-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 24px;\n        }\n\n        .template-type-tabs {\n          margin-bottom: 24px;\n        }\n\n        .template-type-tabs .ant-tabs-card > .ant-tabs-content {\n          margin-top: 0;\n        }\n\n        .template-type-tabs .ant-tabs-card > .ant-tabs-content > .ant-tabs-tabpane {\n          background: transparent;\n          border: none;\n        }\n\n        .templates-tabs {\n          background-color: #fff;\n          padding: 16px;\n          border-radius: 8px;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n        }\n\n        .templates-filters {\n          display: flex;\n          gap: 16px;\n          margin-bottom: 24px;\n          flex-wrap: wrap;\n        }\n\n        .templates-list {\n          min-height: 400px;\n        }\n\n        .ant-card-cover img {\n          border-radius: 8px 8px 0 0;\n        }\n\n        .ant-upload-drag {\n          border: 2px dashed #d9d9d9;\n          border-radius: 8px;\n          background: #fafafa;\n          text-align: center;\n          padding: 40px 20px;\n        }\n\n        .ant-upload-drag:hover {\n          border-color: #1890ff;\n        }\n\n        .ant-upload-drag-icon {\n          font-size: 48px;\n          color: #d9d9d9;\n          margin-bottom: 16px;\n        }\n\n        .ant-upload-text {\n          font-size: 16px;\n          color: #666;\n          margin-bottom: 8px;\n        }\n\n        .ant-upload-hint {\n          font-size: 14px;\n          color: #999;\n        }\n      "))}}}]);