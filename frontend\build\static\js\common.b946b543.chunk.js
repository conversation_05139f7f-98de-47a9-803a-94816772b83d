"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[76],{2569:(e,n,t)=>{t.d(n,{ZV:()=>u,fx:()=>d});var r=t(5544),o=t(6540),a=t(5131),i=t(867),l=(0,o.createContext)({isDarkMode:!1,themeMode:"light",toggleDarkMode:function(){},setThemeMode:function(){},colors:{},systemPrefersDark:!1}),c={primary:"#1890ff",primaryHover:"#40a9ff",secondary:"#52c41a",background:"#ffffff",backgroundSecondary:"#f5f5f5",backgroundTertiary:"#fafafa",surface:"#ffffff",text:"#000000d9",textSecondary:"#00000073",textTertiary:"#00000040",border:"#d9d9d9",borderLight:"#f0f0f0",shadow:"rgba(0, 0, 0, 0.1)",shadowLight:"rgba(0, 0, 0, 0.05)",success:"#52c41a",warning:"#faad14",error:"#ff4d4f",info:"#1890ff"},s={primary:"#1890ff",primaryHover:"#40a9ff",secondary:"#52c41a",background:"#141414",backgroundSecondary:"#1f1f1f",backgroundTertiary:"#262626",surface:"#1f1f1f",text:"#ffffffd9",textSecondary:"#ffffff73",textTertiary:"#ffffff40",border:"#434343",borderLight:"#303030",shadow:"rgba(0, 0, 0, 0.3)",shadowLight:"rgba(0, 0, 0, 0.2)",success:"#52c41a",warning:"#faad14",error:"#ff4d4f",info:"#1890ff"},d=function(e){var n=e.children,t=(0,o.useState)((function(){var e=localStorage.getItem("app-theme-mode");return e&&["light","dark","system"].includes(e)?e:"system"})),d=(0,r.A)(t,2),u=d[0],p=d[1],m=(0,o.useState)((function(){return window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches})),h=(0,r.A)(m,2),v=h[0],f=h[1],g="dark"===u||"system"===u&&v,b=g?s:c;(0,o.useEffect)((function(){var e=window.matchMedia("(prefers-color-scheme: dark)"),n=function(e){f(e.matches)};return e.addEventListener("change",n),function(){return e.removeEventListener("change",n)}}),[]),(0,o.useEffect)((function(){var e=document.documentElement;Object.entries(b).forEach((function(n){var t=(0,r.A)(n,2),o=t[0],a=t[1];e.style.setProperty("--color-".concat(o),a)})),e.setAttribute("data-theme",g?"dark":"light"),g?document.body.classList.add("dark-theme"):document.body.classList.remove("dark-theme");var n=document.querySelector('meta[name="theme-color"]');n&&n.setAttribute("content",b.primary)}),[b,g]),(0,o.useEffect)((function(){localStorage.setItem("app-theme-mode",u)}),[u]);var y=(0,o.useCallback)((function(){p((function(e){return"system"===e?v?"light":"dark":"light"===e?"dark":"light"}))}),[v]),w=(0,o.useCallback)((function(e){["light","dark","system"].includes(e)&&p(e)}),[]),x={algorithm:g?a.A.darkAlgorithm:a.A.defaultAlgorithm,token:{colorPrimary:b.primary,colorSuccess:b.success,colorWarning:b.warning,colorError:b.error,colorInfo:b.info,colorBgBase:b.background,colorBgContainer:b.surface,colorText:b.text,colorTextSecondary:b.textSecondary,colorBorder:b.border,borderRadius:6,wireframe:!1},components:{Layout:{bodyBg:b.background,headerBg:b.surface,footerBg:b.surface},Card:{colorBgContainer:b.surface},Menu:{colorBgContainer:b.surface}}},A={isDarkMode:g,themeMode:u,toggleDarkMode:y,setThemeMode:w,colors:b,systemPrefersDark:v};return o.createElement(l.Provider,{value:A},o.createElement(i.Ay,{theme:x},n))},u=function(){var e=(0,o.useContext)(l);if(!e)throw new Error("useEnhancedTheme must be used within an EnhancedThemeProvider");return e}},4816:(e,n,t)=>{t.r(n),t.d(n,{addComponent:()=>o,addLayout:()=>l,addTheme:()=>d,default:()=>w,removeComponent:()=>i,removeLayout:()=>s,removeTheme:()=>p,setActiveTheme:()=>m,setCurrentView:()=>b,togglePreviewMode:()=>y,toggleSidebar:()=>g,updateComponent:()=>a,updateLayout:()=>c,updateTheme:()=>u,websocketConnected:()=>h,websocketDisconnected:()=>v,websocketMessageReceived:()=>f});var r=t(4318),o=function(e){return{type:r.oz||"ADD_COMPONENT",payload:e}},a=function(e,n){return{type:r.ei||"UPDATE_COMPONENT",payload:{id:e,props:n}}},i=function(e){return{type:r.xS||"REMOVE_COMPONENT",payload:{id:e}}},l=function(e){return{type:r.vs||"ADD_LAYOUT",payload:e}},c=function(e,n){return{type:r.Pe||"UPDATE_LAYOUT",payload:{id:e,props:n}}},s=function(e){return{type:r.gV||"REMOVE_LAYOUT",payload:{id:e}}},d=function(e){return{type:r.U_||"ADD_THEME",payload:e}},u=function(e){return{type:r.gk||"UPDATE_THEME",payload:e}},p=function(e){return{type:r.D||"REMOVE_THEME",payload:{id:e}}},m=function(e){return{type:r.wH||"SET_ACTIVE_THEME",payload:e}},h=function(){return{type:r.Kg||"WEBSOCKET_CONNECTED"}},v=function(){return{type:r.co||"WEBSOCKET_DISCONNECTED"}},f=function(e){return{type:r.ZH||"WEBSOCKET_MESSAGE_RECEIVED",payload:e}},g=function(){return{type:"TOGGLE_SIDEBAR"}},b=function(e){return{type:"SET_CURRENT_VIEW",payload:e}},y=function(){return{type:"TOGGLE_PREVIEW_MODE"}};const w={getState:function(){return{}},dispatch:function(){},subscribe:function(){return function(){}}}},5331:(e,n,t)=>{t.d(n,{tI:()=>r});var r=function(e){return{type:"SET_CURRENT_VIEW",payload:e}}},6220:(e,n,t)=>{t.d(n,{A:()=>at});var r,o,a,i,l,c,s=t(467),d=t(4467),u=t(5544),p=t(7528),m=t(4756),h=t.n(m),v=t(6540),f=t(3016),g=t(2395),b=t(677),y=t(9740),w=t(9029),x=t(9249),A=t(7977),E=t(5039),k=t(2120),S=t(7152),C=t(6370),P=t(5245),_=t(8295),O=t(1468),T=t(5331),D=t(1250),M=t(2507),I=t(1372),j=t(321),z=t(778),L=t(6008),R=t(8990),F=t(3740),B=t(9248),N=t(1348),W=t(234),H=t(1005),U=t(462),Q=t(2569),V=t(5448),G=t(3308),K=(t(9208),V.A.Content),q=V.A.Footer;(0,D.Ay)(V.A)(r||(r=(0,p.A)(["\n  min-height: 100vh;\n  background-color: var(--color-background);\n  transition: background-color 0.3s ease;\n"]))),(0,D.Ay)(K)(o||(o=(0,p.A)(["\n  padding: 24px;\n  background-color: var(--color-background);\n  min-height: calc(100vh - 64px - 70px);\n  transition: all 0.3s ease;\n\n  @media (max-width: 768px) {\n    padding: 16px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 12px;\n  }\n"]))),(0,D.Ay)(q)(a||(a=(0,p.A)(["\n  text-align: center;\n  padding: 24px;\n  background-color: var(--color-surface);\n  border-top: 1px solid var(--color-border-light);\n  color: var(--color-text-secondary);\n  font-size: 14px;\n  transition: all 0.3s ease;\n\n  .footer-content {\n    max-width: 1200px;\n    margin: 0 auto;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    flex-wrap: wrap;\n    gap: 16px;\n\n    @media (max-width: 768px) {\n      flex-direction: column;\n      text-align: center;\n    }\n  }\n\n  .footer-links {\n    display: flex;\n    gap: 24px;\n    align-items: center;\n\n    @media (max-width: 768px) {\n      gap: 16px;\n    }\n\n    a {\n      color: var(--color-text-secondary);\n      text-decoration: none;\n      transition: color 0.3s ease;\n\n      &:hover {\n        color: var(--color-primary);\n      }\n    }\n  }\n\n  .footer-info {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: var(--color-text-tertiary);\n    font-size: 12px;\n  }\n"]))),(0,D.Ay)(G.A)(i||(i=(0,p.A)(["\n  .ant-back-top-content {\n    background-color: var(--color-primary);\n    color: white;\n    border-radius: 50%;\n    width: 48px;\n    height: 48px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-shadow: var(--shadow-lg);\n    transition: all 0.3s ease;\n\n    &:hover {\n      background-color: var(--color-primary-hover);\n      transform: scale(1.1);\n    }\n\n    .anticon {\n      font-size: 16px;\n    }\n  }\n"]))),D.Ay.div(l||(l=(0,p.A)(["\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n"]))),D.Ay.div(c||(c=(0,p.A)(["\n  background-color: var(--color-background);\n  border-radius: var(--border-radius-lg);\n  overflow: hidden;\n  transition: all 0.3s ease;\n\n  @media (max-width: 768px) {\n    border-radius: var(--border-radius-md);\n  }\n"])));var J,Y,Z,X,$,ee=t(9459),ne=t(4358),te=t(9091),re=t(448),oe=t(2648);ne.A.Option;var ae={mobile:{name:"Mobile",icon:v.createElement(te.A,null),variants:{"iphone-se":{name:"iPhone SE",width:375,height:667,scale:.8},"iphone-12":{name:"iPhone 12",width:390,height:844,scale:.7},"pixel-5":{name:"Pixel 5",width:393,height:851,scale:.7},"samsung-s21":{name:"Samsung S21",width:384,height:854,scale:.7}},defaultVariant:"iphone-12",frame:!0,category:"mobile"},tablet:{name:"Tablet",icon:v.createElement(re.A,null),variants:{ipad:{name:"iPad",width:768,height:1024,scale:.6},"ipad-pro":{name:"iPad Pro",width:1024,height:1366,scale:.5},surface:{name:"Surface Pro",width:912,height:1368,scale:.5},"galaxy-tab":{name:"Galaxy Tab",width:800,height:1280,scale:.6}},defaultVariant:"ipad",frame:!0,category:"tablet"},desktop:{name:"Desktop",icon:v.createElement(oe.A,null),variants:{laptop:{name:"Laptop",width:1366,height:768,scale:.7},desktop:{name:"Desktop",width:1920,height:1080,scale:.5},ultrawide:{name:"Ultrawide",width:2560,height:1080,scale:.4},custom:{name:"Custom",width:1200,height:800,scale:.8}},defaultVariant:"laptop",frame:!1,category:"desktop"}};function ie(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function le(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?ie(Object(t),!0).forEach((function(n){(0,d.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ie(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}D.Ay.div(J||(J=(0,p.A)(["\n  position: relative;\n  margin: 20px auto;\n  transition: all 0.3s ease;\n  transform: ",";\n"])),(function(e){return e.orientation,"rotate(0deg)"})),D.Ay.div(Y||(Y=(0,p.A)(["\n  position: relative;\n  background: ",";\n  border-radius: ",";\n  padding: ",";\n  box-shadow: ",";\n  transition: all 0.3s ease;\n  \n  ","\n  \n  ","\n"])),(function(e){return"mobile"===e.category?"#333":"tablet"===e.category?"#444":"transparent"}),(function(e){return"mobile"===e.category?"25px":"tablet"===e.category?"15px":"8px"}),(function(e){return"mobile"===e.category?"20px 10px":"tablet"===e.category?"15px":"0"}),(function(e){return e.frame?"0 8px 32px rgba(0, 0, 0, 0.3)":"none"}),(function(e){return"mobile"===e.category&&"\n    &::before {\n      content: '';\n      position: absolute;\n      top: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 60px;\n      height: 4px;\n      background: #666;\n      border-radius: 2px;\n    }\n    \n    &::after {\n      content: '';\n      position: absolute;\n      bottom: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 40px;\n      height: 40px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  "}),(function(e){return"tablet"===e.category&&"\n    &::before {\n      content: '';\n      position: absolute;\n      bottom: 6px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 30px;\n      height: 30px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  "})),D.Ay.div(Z||(Z=(0,p.A)(["\n  width: ","px;\n  height: ","px;\n  max-width: 100%;\n  max-height: 100%;\n  background: white;\n  border-radius: ",";\n  overflow: auto;\n  position: relative;\n  transform: scale(",");\n  transform-origin: top center;\n  transition: all 0.3s ease;\n  \n  @media (max-width: 1200px) {\n    transform: scale(",");\n  }\n  \n  @media (max-width: 768px) {\n    transform: scale(",");\n  }\n"])),(function(e){return"landscape"===e.orientation?e.height:e.width}),(function(e){return"landscape"===e.orientation?e.width:e.height}),(function(e){return"mobile"===e.category?"8px":"tablet"===e.category?"6px":"4px"}),(function(e){return e.scale}),(function(e){return Math.min(e.scale,.8)}),(function(e){return Math.min(e.scale,.6)})),D.Ay.div(X||(X=(0,p.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 16px;\n  flex-wrap: wrap;\n"]))),D.Ay.div($||($=(0,p.A)(["\n  position: absolute;\n  top: -30px;\n  left: 0;\n  font-size: 12px;\n  color: #666;\n  background: rgba(255, 255, 255, 0.9);\n  padding: 4px 8px;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n"])));var ce=t(3029),se=t(2901);function de(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function ue(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?de(Object(t),!0).forEach((function(n){(0,d.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):de(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var pe=function(){return(0,se.A)((function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,ce.A)(this,e),this.socket=null,this.connected=!1,this.connecting=!1,this.eventListeners=new Map,this.options=ue({autoConnect:!1,autoReconnect:!0,reconnectInterval:2e3,maxReconnectAttempts:10,debug:!1},n),this.previewState={components:new Map,deviceSettings:{},viewportState:{},collaborators:new Map,cursors:new Map},this.previewHandlers=new Map,this.debounceSettings={componentUpdate:300,deviceChange:100,cursorMove:50,viewportChange:200},this.initializePreviewHandlers()}),[{key:"connect",value:function(){var e=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"ws://localhost:8000/ws/collaboration/";return new Promise((function(t,r){if(e.connected)t(e);else if(e.connecting)r(new Error("Connection already in progress"));else{e.connecting=!0;try{e.socket=new WebSocket(n),e.socket.onopen=function(n){e.connected=!0,e.connecting=!1,e.triggerEvent("connect",n),t(e)},e.socket.onclose=function(n){e.connected=!1,e.connecting=!1,e.triggerEvent("disconnect",n)},e.socket.onerror=function(n){e.connecting=!1,e.triggerEvent("error",n),r(new Error("WebSocket connection failed"))},e.socket.onmessage=function(n){try{var t=JSON.parse(n.data);e.triggerEvent("message",t),t.type&&e.triggerEvent(t.type,t)}catch(e){console.error("Error parsing WebSocket message:",e)}}}catch(n){e.connecting=!1,r(n)}}}))}},{key:"disconnect",value:function(){this.socket&&(this.socket.close(),this.socket=null,this.connected=!1)}},{key:"send",value:function(e){var n=this;return new Promise((function(t,r){if(n.connected)try{var o="string"==typeof e?e:JSON.stringify(e);n.socket.send(o),t()}catch(e){r(e)}else r(new Error("WebSocket not connected"))}))}},{key:"on",value:function(e,n){this.eventListeners.has(e)||this.eventListeners.set(e,[]),this.eventListeners.get(e).push(n)}},{key:"triggerEvent",value:function(e,n){(this.eventListeners.get(e)||[]).forEach((function(t){try{t(n)}catch(n){console.error("Error in event handler for ".concat(e,":"),n)}}))}},{key:"initializePreviewHandlers",value:function(){this.on("component_updated",this.handleComponentUpdate.bind(this)),this.on("component_added",this.handleComponentAdd.bind(this)),this.on("component_deleted",this.handleComponentDelete.bind(this)),this.on("component_moved",this.handleComponentMove.bind(this)),this.on("device_changed",this.handleDeviceChange.bind(this)),this.on("viewport_changed",this.handleViewportChange.bind(this)),this.on("collaborator_joined",this.handleCollaboratorJoin.bind(this)),this.on("collaborator_left",this.handleCollaboratorLeave.bind(this)),this.on("cursor_moved",this.handleCursorMove.bind(this)),this.on("selection_changed",this.handleSelectionChange.bind(this)),this.on("preview_state_sync",this.handlePreviewStateSync.bind(this)),this.on("preview_state_request",this.handlePreviewStateRequest.bind(this))}},{key:"sendComponentUpdate",value:(l=(0,s.A)(h().mark((function e(n,t){var r,o,a=arguments;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=a.length>2&&void 0!==a[2]?a[2]:{},o={type:"component_update",component_id:n,component_data:t,timestamp:(new Date).toISOString(),user_id:r.userId||"anonymous",session_id:r.sessionId,immediate:r.immediate||!1},r.immediate&&(this.previewState.components.set(n,t),this.triggerPreviewEvent("component_updated_local",o)),e.abrupt("return",this.send(o,{priority:r.immediate?"high":"normal",compress:!0}));case 4:case"end":return e.stop()}}),e,this)}))),function(e,n){return l.apply(this,arguments)})},{key:"sendDeviceChange",value:(i=(0,s.A)(h().mark((function e(n,t){var r,o,a=arguments;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=a.length>2&&void 0!==a[2]?a[2]:{},o={type:"device_change",device_type:n,device_config:t,timestamp:(new Date).toISOString(),user_id:r.userId||"anonymous",session_id:r.sessionId},this.previewState.deviceSettings={type:n,config:t,timestamp:new Date},e.abrupt("return",this.send(o,{compress:!1}));case 4:case"end":return e.stop()}}),e,this)}))),function(e,n){return i.apply(this,arguments)})},{key:"sendCursorPosition",value:(a=(0,s.A)(h().mark((function e(n){var t,r,o=arguments;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=o.length>1&&void 0!==o[1]?o[1]:{},r={type:"cursor_move",position:n,timestamp:(new Date).toISOString(),user_id:t.userId||"anonymous",session_id:t.sessionId},e.abrupt("return",this.send(r,{priority:"low",compress:!1,throttle:this.debounceSettings.cursorMove}));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return a.apply(this,arguments)})},{key:"sendViewportChange",value:(o=(0,s.A)(h().mark((function e(n){var t,r,o=arguments;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=o.length>1&&void 0!==o[1]?o[1]:{},r={type:"viewport_change",viewport:n,timestamp:(new Date).toISOString(),user_id:t.userId||"anonymous",session_id:t.sessionId},this.previewState.viewportState=ue(ue({},n),{},{timestamp:new Date}),e.abrupt("return",this.send(r,{compress:!0}));case 4:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})},{key:"requestPreviewState",value:(r=(0,s.A)(h().mark((function e(n){return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.send({type:"preview_state_request",session_id:n,timestamp:(new Date).toISOString()}));case 1:case"end":return e.stop()}}),e,this)}))),function(e){return r.apply(this,arguments)})},{key:"syncPreviewState",value:(t=(0,s.A)(h().mark((function e(n){var t;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t={components:Array.from(this.previewState.components.entries()),deviceSettings:this.previewState.deviceSettings,viewportState:this.previewState.viewportState,timestamp:(new Date).toISOString()},e.abrupt("return",this.send({type:"preview_state_sync",session_id:n,state:t},{compress:!0}));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"handleComponentUpdate",value:function(e){var n=e.component_id,t=e.component_data,r=e.user_id,o=e.timestamp;this.previewState.components.set(n,ue(ue({},t),{},{lastUpdatedBy:r,lastUpdated:o})),this.triggerPreviewEvent("component_updated",e)}},{key:"handleComponentAdd",value:function(e){var n=e.component;n&&n.id&&(this.previewState.components.set(n.id,n),this.triggerPreviewEvent("component_added",e))}},{key:"handleComponentDelete",value:function(e){var n=e.component_id;n&&(this.previewState.components.delete(n),this.triggerPreviewEvent("component_deleted",e))}},{key:"handleComponentMove",value:function(e){var n=e.component_id,t=e.new_position,r=this.previewState.components.get(n);r&&(this.previewState.components.set(n,ue(ue({},r),{},{position:t})),this.triggerPreviewEvent("component_moved",e))}},{key:"handleDeviceChange",value:function(e){var n=e.device_type,t=e.device_config,r=e.user_id;this.previewState.deviceSettings={type:n,config:t,changedBy:r,timestamp:new Date},this.triggerPreviewEvent("device_changed",e)}},{key:"handleViewportChange",value:function(e){var n=e.viewport,t=e.user_id;this.previewState.viewportState=ue(ue({},n),{},{changedBy:t,timestamp:new Date}),this.triggerPreviewEvent("viewport_changed",e)}},{key:"handleCollaboratorJoin",value:function(e){var n=e.user_id,t=e.username,r=e.avatar;this.previewState.collaborators.set(n,{username:t,avatar:r,joinedAt:new Date,isActive:!0}),this.triggerPreviewEvent("collaborator_joined",e)}},{key:"handleCollaboratorLeave",value:function(e){var n=e.user_id;this.previewState.collaborators.delete(n),this.previewState.cursors.delete(n),this.triggerPreviewEvent("collaborator_left",e)}},{key:"handleCursorMove",value:function(e){var n=e.user_id,t=e.position;this.previewState.cursors.set(n,{position:t,timestamp:new Date}),this.triggerPreviewEvent("cursor_moved",e)}},{key:"handleSelectionChange",value:function(e){var n=e.user_id,t=e.selection,r=this.previewState.collaborators.get(n);r&&this.previewState.collaborators.set(n,ue(ue({},r),{},{selection:t,lastActivity:new Date})),this.triggerPreviewEvent("selection_changed",e)}},{key:"handlePreviewStateSync",value:function(e){var n=this,t=e.state;t&&(t.components&&(this.previewState.components.clear(),t.components.forEach((function(e){var t=(0,u.A)(e,2),r=t[0],o=t[1];n.previewState.components.set(r,o)}))),t.deviceSettings&&(this.previewState.deviceSettings=t.deviceSettings),t.viewportState&&(this.previewState.viewportState=t.viewportState)),this.triggerPreviewEvent("preview_state_synced",e)}},{key:"handlePreviewStateRequest",value:function(e){this.syncPreviewState(e.session_id)}},{key:"triggerPreviewEvent",value:function(e,n){(this.previewHandlers.get(e)||[]).forEach((function(t){try{t(n)}catch(n){console.error("Error in preview event handler for ".concat(e,":"),n)}}))}},{key:"onPreviewEvent",value:function(e,n){var t=this;return this.previewHandlers.has(e)||this.previewHandlers.set(e,[]),this.previewHandlers.get(e).push(n),function(){var r=t.previewHandlers.get(e)||[],o=r.indexOf(n);o>-1&&r.splice(o,1)}}},{key:"getPreviewState",value:function(){return{components:Array.from(this.previewState.components.entries()),deviceSettings:this.previewState.deviceSettings,viewportState:this.previewState.viewportState,collaborators:Array.from(this.previewState.collaborators.entries()),cursors:Array.from(this.previewState.cursors.entries())}}},{key:"clearPreviewState",value:function(){this.previewState.components.clear(),this.previewState.collaborators.clear(),this.previewState.cursors.clear(),this.previewState.deviceSettings={},this.previewState.viewportState={}}},{key:"joinSession",value:(n=(0,s.A)(h().mark((function e(n){var t,r=arguments;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.length>1&&void 0!==r[1]?r[1]:{},e.abrupt("return",this.send({type:"join_session",session_id:n,user_info:{username:t.username||"Anonymous",avatar:t.avatar||null,timestamp:(new Date).toISOString()}}));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"leaveSession",value:(e=(0,s.A)(h().mark((function e(n){return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.send({type:"leave_session",session_id:n,timestamp:(new Date).toISOString()}));case 1:case"end":return e.stop()}}),e,this)}))),function(n){return e.apply(this,arguments)})}]);var e,n,t,r,o,a,i,l}();const me=pe;function he(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function ve(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?he(Object(t),!0).forEach((function(n){(0,d.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):he(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var fe,ge,be,ye,we;D.Ay.div(fe||(fe=(0,p.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: ",";\n  width: 100%;\n"])),(function(e){return e.minHeight||"200px"})),D.Ay.div(ge||(ge=(0,p.A)(["\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: ",";\n  width: 100%;\n  color: ",";\n  text-align: center;\n  padding: 16px;\n"])),(function(e){return e.minHeight||"200px"}),(function(e){return e.theme.colorPalette.error})),D.Ay.button(be||(be=(0,p.A)(["\n  margin-top: 16px;\n  padding: 8px 16px;\n  background-color: ",";\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  \n  &:hover {\n    background-color: ",";\n  }\n"])),(function(e){return e.theme.colorPalette.primary}),(function(e){return e.theme.colorPalette.primaryDark})),D.Ay.div(ye||(ye=(0,p.A)(["\n  position: relative;\n  overflow-y: auto;\n  width: 100%;\n  height: ",";\n"])),(function(e){return e.height||"400px"})),D.Ay.div(we||(we=(0,p.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n"])));var xe,Ae,Ee,ke,Se,Ce,Pe,_e,Oe,Te,De,Me,Ie,je,ze,Le,Re,Fe,Be,Ne,We,He,Ue,Qe,Ve,Ge,Ke,qe,Je,Ye,Ze,Xe,$e=t(436),en=t(7122),nn=t(6754),tn=t(6552),rn=t(581),on=t(845),an=t(7852),ln=t(2734),cn=t(378),sn=t(1295),dn=t(7749),un=(f.A.Title,f.A.Text,f.A.Paragraph,["flex","flexMd","flexLg","status","messageType"]),pn=function(e){return!un.includes(e)};function mn(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function hn(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?mn(Object(t),!0).forEach((function(n){(0,d.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):mn(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}(0,D.Ay)(dn.sT).withConfig({shouldForwardProp:pn})(xe||(xe=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn._x).withConfig({shouldForwardProp:pn})(Ae||(Ae=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.tK).withConfig({shouldForwardProp:pn})(Ee||(Ee=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.mc).withConfig({shouldForwardProp:pn})(ke||(ke=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.wn).withConfig({shouldForwardProp:pn})(Se||(Se=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.fI).withConfig({shouldForwardProp:pn})(Ce||(Ce=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.VP).withConfig({shouldForwardProp:pn})(Pe||(Pe=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.xA).withConfig({shouldForwardProp:pn})(_e||(_e=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.ee).withConfig({shouldForwardProp:pn})(Oe||(Oe=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.dd).withConfig({shouldForwardProp:pn})(Te||(Te=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.pK).withConfig({shouldForwardProp:pn})(De||(De=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.jn).withConfig({shouldForwardProp:pn})(Me||(Me=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.tA).withConfig({shouldForwardProp:pn})(Ie||(Ie=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.K0).withConfig({shouldForwardProp:pn})(je||(je=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.gE).withConfig({shouldForwardProp:pn})(ze||(ze=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.sQ).withConfig({shouldForwardProp:pn})(Le||(Le=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.aQ).withConfig({shouldForwardProp:pn})(Re||(Re=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.cN).withConfig({shouldForwardProp:pn})(Fe||(Fe=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.YM).withConfig({shouldForwardProp:pn})(Be||(Be=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.IO).withConfig({shouldForwardProp:pn})(Ne||(Ne=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.ih).withConfig({shouldForwardProp:pn})(We||(We=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.hC).withConfig({shouldForwardProp:pn})(He||(He=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.cG).withConfig({shouldForwardProp:pn})(Ue||(Ue=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.Ex).withConfig({shouldForwardProp:pn})(Qe||(Qe=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),(0,D.Ay)(dn.eu).withConfig({shouldForwardProp:pn})(Ve||(Ve=(0,p.A)(["\n  /* Additional styles can be added here */\n"]))),f.A.Title;var vn=f.A.Text,fn=f.A.Paragraph,gn=(0,D.Ay)(b.A)(Ge||(Ge=(0,p.A)(["\n  position: fixed;\n  bottom: ",";\n  top: ",";\n  right: 20px;\n  width: ",";\n  z-index: 1000;\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);\n  transition: all 0.3s ease;\n"])),(function(e){return e.expanded?"20px":"auto"}),(function(e){return e.expanded?"auto":"20px"}),(function(e){return e.expanded?"600px":"300px"})),bn=D.Ay.div(Ke||(Ke=(0,p.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n"]))),yn=D.Ay.div(qe||(qe=(0,p.A)(["\n  display: flex;\n  align-items: center;\n"]))),wn=D.Ay.div(Je||(Je=(0,p.A)(["\n  max-height: 150px;\n  overflow-y: auto;\n"]))),xn=D.Ay.div(Ye||(Ye=(0,p.A)(["\n  margin-bottom: 8px;\n"]))),An=(0,D.Ay)(vn)(Ze||(Ze=(0,p.A)(["\n  font-size: 12px;\n"]))),En=(0,D.Ay)(fn)(Xe||(Xe=(0,p.A)(["\n  font-size: 12px;\n  text-align: center;\n"])));const kn=function(e){var n=e.visible,t=void 0!==n&&n,r=(0,v.useState)({fps:0,memory:{used:0,total:0,limit:0},timing:{domComplete:0,domInteractive:0,loadEvent:0,firstContentfulPaint:0,largestContentfulPaint:0},resources:{count:0,totalSize:0},longTasks:[]}),o=(0,u.A)(r,2),a=o[0],i=o[1],l=(0,v.useState)(!1),c=(0,u.A)(l,2),s=c[0],d=c[1],p=(0,v.useState)(t),m=(0,u.A)(p,2),h=m[0],f=m[1];(0,v.useEffect)((function(){if(h)return function(){var e=0,n=performance.now(),t=function(){var r=performance.now(),o=r-n;if(o>=1e3){var a=Math.round(1e3*e/o);i((function(e){return hn(hn({},e),{},{fps:a})})),e=0,n=r}e++,requestAnimationFrame(t)},r=requestAnimationFrame(t);if(window.performance&&window.performance.memory&&i((function(e){return hn(hn({},e),{},{memory:{used:Math.round(window.performance.memory.usedJSHeapSize/1048576),total:Math.round(window.performance.memory.totalJSHeapSize/1048576),limit:Math.round(window.performance.memory.jsHeapSizeLimit/1048576)}})})),performance.timing){var o=performance.timing;i((function(e){return hn(hn({},e),{},{timing:{domComplete:o.domComplete-o.navigationStart,domInteractive:o.domInteractive-o.navigationStart,loadEvent:o.loadEventEnd-o.navigationStart,firstContentfulPaint:0,largestContentfulPaint:0}})}))}var a=performance.getEntriesByType("resource");i((function(e){return hn(hn({},e),{},{resources:{count:a.length,totalSize:a.reduce((function(e,n){return e+(n.transferSize||0)}),0)/1048576}})})),performance.getEntriesByType("paint").forEach((function(e){"first-contentful-paint"===e.name&&i((function(n){return hn(hn({},n),{},{timing:hn(hn({},n.timing),{},{firstContentfulPaint:e.startTime})})}))}));var l=new PerformanceObserver((function(e){var n=e.getEntries(),t=n[n.length-1];i((function(e){return hn(hn({},e),{},{timing:hn(hn({},e.timing),{},{largestContentfulPaint:t.startTime})})}))}));l.observe({type:"largest-contentful-paint",buffered:!0});var c=new PerformanceObserver((function(e){var n=e.getEntries();i((function(e){return hn(hn({},e),{},{longTasks:[].concat((0,$e.A)(e.longTasks),(0,$e.A)(n)).slice(-10)})}))}));return c.observe({type:"longtask",buffered:!0}),function(){cancelAnimationFrame(r),l.disconnect(),c.disconnect()}}()}),[h]);var g=function(){f(!h)};if(!h)return v.createElement(A.A,{title:"Show Performance Monitor"},v.createElement(x.Ay,{type:"primary",shape:"circle",icon:v.createElement(R.A,null),onClick:g,style:{position:"fixed",bottom:"20px",right:"20px",zIndex:1e3}}));var b=function(e){return e>=55?"#52c41a":e>=30?"#faad14":"#f5222d"},y=function(){return a.memory.total?Math.round(a.memory.used/a.memory.total*100):0},w=function(){var e=y();return e<70?"#52c41a":e<90?"#faad14":"#f5222d"};return v.createElement("div",{className:"performance-monitor"},v.createElement(gn,{expanded:s,title:v.createElement(bn,null,v.createElement("span",null,v.createElement(R.A,null)," Performance Monitor"),v.createElement(yn,null,v.createElement(x.Ay,{type:"text",icon:v.createElement(rn.A,null),onClick:function(){i({fps:0,memory:{used:0,total:0,limit:0},timing:{domComplete:0,domInteractive:0,loadEvent:0,firstContentfulPaint:0,largestContentfulPaint:0},resources:{count:0,totalSize:0},longTasks:[]}),performance.clearMarks(),performance.clearMeasures(),performance.clearResourceTimings()},style:{marginRight:"8px"},"aria-label":"Reset metrics"}),v.createElement(x.Ay,{type:"text",icon:v.createElement(on.A,null),onClick:function(){d(!s)},style:{marginRight:"8px"},"aria-label":s?"Collapse view":"Expand view"}),v.createElement(x.Ay,{type:"text",icon:v.createElement(an.A,null),onClick:g,"aria-label":"Close performance monitor"}))),bodyStyle:{padding:s?"16px":"8px"}},v.createElement(S.A,{gutter:[16,16]},v.createElement(C.A,{span:s?12:24},v.createElement(en.A,{title:"FPS",value:a.fps,suffix:"fps",valueStyle:{color:b(a.fps)},prefix:v.createElement(ln.A,null)}),s&&v.createElement(nn.A,{percent:a.fps/60*100,strokeColor:b(a.fps),showInfo:!1,size:"small",style:{marginTop:"8px"}})),a.memory.used>0&&v.createElement(C.A,{span:s?12:24},v.createElement(en.A,{title:"Memory Usage",value:a.memory.used,suffix:"MB",valueStyle:{color:w()},prefix:v.createElement(on.A,null)}),s&&v.createElement(A.A,{title:"".concat(a.memory.used,"MB / ").concat(a.memory.total,"MB")},v.createElement(nn.A,{percent:y(),strokeColor:w(),showInfo:!1,size:"small",style:{marginTop:"8px"}}))),s&&v.createElement(v.Fragment,null,v.createElement(C.A,{span:24},v.createElement(tn.A,{orientation:"left"},"Page Load Timing")),v.createElement(C.A,{span:12},v.createElement(en.A,{title:"DOM Interactive",value:a.timing.domInteractive,suffix:"ms",valueStyle:{fontSize:"16px"}})),v.createElement(C.A,{span:12},v.createElement(en.A,{title:"DOM Complete",value:a.timing.domComplete,suffix:"ms",valueStyle:{fontSize:"16px"}})),v.createElement(C.A,{span:12},v.createElement(en.A,{title:"First Contentful Paint",value:Math.round(a.timing.firstContentfulPaint),suffix:"ms",valueStyle:{fontSize:"16px"}})),v.createElement(C.A,{span:12},v.createElement(en.A,{title:"Largest Contentful Paint",value:Math.round(a.timing.largestContentfulPaint),suffix:"ms",valueStyle:{fontSize:"16px"}})),v.createElement(C.A,{span:24},v.createElement(tn.A,{orientation:"left"},"Resources")),v.createElement(C.A,{span:12},v.createElement(en.A,{title:"Resource Count",value:a.resources.count,valueStyle:{fontSize:"16px"}})),v.createElement(C.A,{span:12},v.createElement(en.A,{title:"Total Size",value:a.resources.totalSize.toFixed(2),suffix:"MB",valueStyle:{fontSize:"16px"}})),a.longTasks.length>0&&v.createElement(v.Fragment,null,v.createElement(C.A,{span:24},v.createElement(tn.A,{orientation:"left"},"Long Tasks"),v.createElement(wn,null,a.longTasks.map((function(e,n){return v.createElement(xn,{key:n},v.createElement(vn,{type:"danger"},v.createElement(cn.A,null)," Task blocked for ",e.duration.toFixed(2),"ms"),v.createElement("div",null,v.createElement(An,{type:"secondary"},new Date(e.startTime).toLocaleTimeString())))}))))),v.createElement(C.A,{span:24},v.createElement(tn.A,null),v.createElement(En,{type:"secondary"},v.createElement(sn.A,null)," Performance monitoring active"))))))};var Sn=t(6822),Cn=t(3954),Pn=t(5501),_n=t(7197);function On(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(On=function(){return!!e})()}const Tn=function(e){function n(e){var t,r,o,a;return(0,ce.A)(this,n),r=this,o=n,a=[e],o=(0,Cn.A)(o),(t=(0,Sn.A)(r,On()?Reflect.construct(o,a||[],(0,Cn.A)(r).constructor):o.apply(r,a))).state={hasError:!1,error:null},t}return(0,Pn.A)(n,e),(0,se.A)(n,[{key:"componentDidCatch",value:function(e,n){console.error("Component error caught by SafeComponentWrapper:",e,n)}},{key:"render",value:function(){var e,n=this;return this.state.hasError?v.createElement("div",{style:{padding:"20px"}},v.createElement(_n.A,{message:"Component Loading Error",description:v.createElement("div",null,v.createElement("p",null,"This component failed to load properly. This might be due to missing dependencies or configuration issues."),v.createElement("p",null,v.createElement("strong",null,"Error:")," ",(null===(e=this.state.error)||void 0===e?void 0:e.message)||"Unknown error"),this.props.fallback&&v.createElement("div",{style:{marginTop:"16px"}},v.createElement("p",null,"Using fallback component:"),this.props.fallback)),type:"warning",showIcon:!0,action:v.createElement(x.Ay,{size:"small",onClick:function(){return n.setState({hasError:!1,error:null})}},"Retry")})):this.props.children}}],[{key:"getDerivedStateFromError",value:function(e){return{hasError:!0,error:e}}}])}(v.Component);var Dn,Mn,In,jn,zn,Ln,Rn;function Fn(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function Bn(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?Fn(Object(t),!0).forEach((function(n){(0,d.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Fn(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var Nn=(0,v.lazy)((function(){return Promise.all([t.e(874),t.e(96),t.e(76),t.e(309)]).then(t.bind(t,4309)).catch((function(e){return console.warn("Failed to load ComponentBuilder:",e),{default:function(){return v.createElement("div",null,"Component Builder not available")}}}))})),Wn=(0,v.lazy)((function(){return Promise.all([t.e(96),t.e(76),t.e(505)]).then(t.bind(t,5505)).catch((function(e){return console.warn("Failed to load LayoutDesigner:",e),{default:function(){return v.createElement("div",null,"Layout Designer not available")}}}))})),Hn=(0,v.lazy)((function(){return Promise.all([t.e(96),t.e(667)]).then(t.bind(t,1667)).catch((function(e){return console.warn("Failed to load ThemeManager:",e),{default:function(){return v.createElement("div",null,"Theme Manager not available")}}}))})),Un=(0,v.lazy)((function(){return Promise.all([t.e(874),t.e(96),t.e(76),t.e(366)]).then(t.bind(t,5366)).catch((function(e){return console.warn("Failed to load FixedWebSocketManager:",e),{default:function(){return v.createElement("div",null,"WebSocket Manager not available")}}}))})),Qn=(0,v.lazy)((function(){return Promise.all([t.e(874),t.e(96),t.e(680)]).then(t.bind(t,1680)).catch((function(e){return console.warn("Failed to load ProjectManager:",e),{default:function(){return v.createElement("div",null,"Project Manager not available")}}}))})),Vn=(0,v.lazy)((function(){return Promise.all([t.e(874),t.e(96),t.e(205)]).then(t.bind(t,9205)).catch((function(e){return console.warn("Failed to load EnhancedCodeExporter:",e),{default:function(){return v.createElement("div",null,"Enhanced Code Exporter not available")}}}))})),Gn=(0,v.lazy)((function(){return Promise.all([t.e(874),t.e(96),t.e(672)]).then(t.bind(t,672)).catch((function(e){return console.warn("Failed to load PerformanceMonitor:",e),{default:function(){return v.createElement("div",null,"Performance Monitor not available")}}}))})),Kn=(0,v.lazy)((function(){return Promise.all([t.e(96),t.e(76),t.e(481)]).then(t.bind(t,7481)).catch((function(e){return console.warn("Failed to load DataManagementDemo:",e),{default:function(){return v.createElement("div",null,"Data Management not available")}}}))})),qn=(0,v.lazy)((function(){return Promise.all([t.e(96),t.e(76),t.e(605)]).then(t.bind(t,4605)).catch((function(e){return console.warn("Failed to load TestingTools:",e),{default:function(){return v.createElement("div",null,"Testing Tools not available")}}}))})),Jn=(0,v.lazy)((function(){return t.e(726).then(t.bind(t,3726)).catch((function(e){return console.warn("Failed to load TutorialAIPlugin:",e),{default:function(){return v.createElement("div",null,"Tutorial Assistant not available")}}}))})),Yn=f.A.Title,Zn=f.A.Paragraph,Xn=D.Ay.div(Dn||(Dn=(0,p.A)(["\n  padding: var(--spacing-lg);\n  max-width: 1200px;\n  margin: 0 auto;\n  background-color: var(--color-background);\n  min-height: 100vh;\n  transition: all 0.3s ease;\n\n  @media (max-width: 768px) {\n    padding: var(--spacing-md);\n  }\n\n  @media (max-width: 480px) {\n    padding: var(--spacing-sm);\n  }\n"]))),$n=D.Ay.div(Mn||(Mn=(0,p.A)(["\n  margin-bottom: var(--spacing-xl);\n  padding: var(--spacing-lg);\n  background-color: var(--color-surface);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  border: 1px solid var(--color-border-light);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-md);\n  }\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-md);\n\n    h2 {\n      margin-bottom: var(--spacing-sm);\n    }\n  }\n"]))),et=(0,D.Ay)(g.A)(In||(In=(0,p.A)(["\n  .ant-tabs-nav {\n    margin-bottom: var(--spacing-xl);\n    background-color: var(--color-surface);\n    border-radius: var(--border-radius-lg);\n    padding: var(--spacing-sm);\n    box-shadow: var(--shadow-sm);\n    border: 1px solid var(--color-border-light);\n  }\n\n  .ant-tabs-tab {\n    padding: var(--spacing-md) var(--spacing-lg);\n    transition: all 0.3s ease;\n    border-radius: var(--border-radius-md);\n    margin: 0 var(--spacing-xs);\n    color: var(--color-text-secondary);\n    font-weight: 500;\n\n    &:hover {\n      color: var(--color-primary);\n      background-color: var(--color-background-secondary);\n      transform: translateY(-2px);\n    }\n\n    .anticon {\n      margin-right: var(--spacing-sm);\n      font-size: 16px;\n    }\n  }\n\n  .ant-tabs-tab-active {\n    background-color: var(--color-primary);\n    color: white;\n    box-shadow: var(--shadow-md);\n\n    &:hover {\n      background-color: var(--color-primary-hover);\n      color: white;\n    }\n  }\n\n  .ant-tabs-content-holder {\n    background-color: var(--color-surface);\n    border-radius: var(--border-radius-lg);\n    box-shadow: var(--shadow-sm);\n    border: 1px solid var(--color-border-light);\n    overflow: hidden;\n  }\n\n  .ant-tabs-tabpane {\n    padding: var(--spacing-lg);\n  }\n"]))),nt=(0,D.Ay)(b.A)(jn||(jn=(0,p.A)(["\n  height: 100%;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  overflow: hidden;\n\n  &:hover {\n    transform: translateY(-8px);\n    box-shadow: var(--shadow-lg);\n    border-color: var(--color-primary);\n  }\n\n  &:active {\n    transform: translateY(-4px);\n  }\n\n  .ant-card-head {\n    background-color: ",";\n    color: ",";\n    border-bottom: 1px solid var(--color-border-light);\n    transition: all 0.3s ease;\n  }\n\n  .ant-card-body {\n    padding: var(--spacing-lg);\n    text-align: center;\n    background-color: var(--color-surface);\n  }\n"])),(function(e){return e.active?"var(--color-primary)":"var(--color-background-secondary)"}),(function(e){return e.active?"white":"var(--color-text)"})),tt=D.Ay.div(zn||(zn=(0,p.A)(["\n  font-size: 32px;\n  margin-bottom: var(--spacing-md);\n  color: ",";\n  transition: all 0.3s ease;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 64px;\n  height: 64px;\n  border-radius: 50%;\n  background-color: ",";\n  margin: 0 auto var(--spacing-md);\n"])),(function(e){return e.active?"var(--color-primary)":"var(--color-text-secondary)"}),(function(e){return e.active?"rgba(24, 144, 255, 0.1)":"var(--color-background-secondary)"})),rt=(0,D.Ay)(b.A)(Ln||(Ln=(0,p.A)(["\n  margin-bottom: var(--spacing-xl);\n  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);\n  border: none;\n  border-radius: var(--border-radius-xl);\n  overflow: hidden;\n  box-shadow: var(--shadow-lg);\n  position: relative;\n\n  /* Add overlay for better text contrast */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: rgba(0, 0, 0, 0.1);\n    z-index: 1;\n  }\n\n  .ant-card-body {\n    padding: var(--spacing-xxl);\n    color: white;\n    position: relative;\n    z-index: 2;\n  }\n\n  h4, p {\n    color: white !important;\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n    position: relative;\n    z-index: 2;\n  }\n\n  .ant-btn-primary {\n    background-color: white;\n    border-color: white;\n    color: var(--color-primary);\n    font-weight: 600;\n    position: relative;\n    z-index: 2;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n\n    &:hover {\n      background-color: rgba(255, 255, 255, 0.95);\n      border-color: rgba(255, 255, 255, 0.95);\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);\n    }\n  }\n"]))),ot=D.Ay.div(Rn||(Rn=(0,p.A)(["\n  @keyframes spin {\n    0% {\n      transform: rotate(0deg);\n    }\n    100% {\n      transform: rotate(360deg);\n    }\n  }\n"])));const at=function(){var e=(0,O.wA)(),n=(0,Q.ZV)(),t=(n.isDarkMode,n.colors,(0,v.useState)(!0)),r=(0,u.A)(t,2),o=r[0],a=r[1],i=(0,v.useState)(null),l=(0,u.A)(i,2),c=l[0],p=l[1],m=(0,v.useState)({api:"checking",websocket:"checking"}),f=(0,u.A)(m,2),g=f[0],D=f[1],V=(0,O.d4)((function(e){var n;return(null===(n=e.ui)||void 0===n?void 0:n.currentView)||"components"})),G=(0,v.useState)(V),K=(0,u.A)(G,2),q=K[0],J=K[1],Y=(0,v.useState)(!1),Z=(0,u.A)(Y,2),X=Z[0],$=Z[1],ne=(0,v.useState)({realTimeEnabled:!0,collaborationEnabled:!1,performanceMonitoring:!0,deviceSync:!0}),te=(0,u.A)(ne,2),re=te[0],oe=te[1],ie=(0,v.useState)((function(){return"session_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,11))})),ce=(0,u.A)(ie,1)[0],se=(0,v.useState)((function(){return"user_".concat(Math.random().toString(36).substring(2,11))})),de=(0,u.A)(se,1)[0],ue=(0,O.d4)((function(e){var n;return(null===(n=e.websocket)||void 0===n?void 0:n.connected)||!1})),pe=(0,O.d4)((function(e){var n;return(null===(n=e.app)||void 0===n?void 0:n.components)||[]})),he=(function(e){var n,t=e.initialDevice,r=void 0===t?"desktop":t,o=e.initialVariant,a=void 0===o?null:o,i=e.enableBreakpointDetection,l=void 0===i||i,c=e.customBreakpoints,s=void 0===c?null:c,d=(0,v.useState)(r),p=(0,u.A)(d,2),m=p[0],h=p[1],f=(0,v.useState)(a||(null===(n=ae[r])||void 0===n?void 0:n.defaultVariant)),g=(0,u.A)(f,2),b=g[0],y=g[1],w=(0,v.useState)("portrait"),x=(0,u.A)(w,2),A=x[0],E=x[1],k=(0,v.useState)(!1),S=(0,u.A)(k,2),C=S[0],P=S[1],_=(0,v.useState)({width:0,height:0}),O=(0,u.A)(_,2),T=O[0],D=O[1],M=s||{mobile:{min:0,max:767},tablet:{min:768,max:1023},desktop:{min:1024,max:1/0}},I=(0,v.useMemo)((function(){var e=ae[m];if(!e)return null;var n=e.variants[b];return n?le(le(le({},e),n),{},{category:e.category,orientation:A}):null}),[m,b,A]),j=(0,v.useMemo)((function(){return I?le(le({},{fontSize:"mobile"===I.category?"14px":"tablet"===I.category?"15px":"16px",padding:"mobile"===I.category?"8px":"tablet"===I.category?"12px":"16px",margin:"mobile"===I.category?"4px":"8px",borderRadius:"mobile"===I.category?"4px":"6px"}),{mobile:{maxWidth:"100%",touchAction:"manipulation",WebkitTapHighlightColor:"transparent"},tablet:{maxWidth:"100%",touchAction:"manipulation"},desktop:{cursor:"pointer",userSelect:"none"}}[I.category]):{}}),[I]),z=(0,v.useCallback)((function(e){var n;return I&&(null===(n={mobile:{button:"small",input:"small",card:"small",table:"small",form:"small"},tablet:{button:"middle",input:"middle",card:"default",table:"middle",form:"middle"},desktop:{button:"middle",input:"middle",card:"default",table:"middle",form:"middle"}}[I.category])||void 0===n?void 0:n[e])||"middle"}),[I]);(0,v.useEffect)((function(){if(l){var e=function(){D({width:window.innerWidth,height:window.innerHeight})};return e(),window.addEventListener("resize",e),function(){window.removeEventListener("resize",e)}}}),[l]),(0,v.useEffect)((function(){var e;if(l&&0!==T.width){var n,t=null===(e=Object.entries(M).find((function(e){var n=(0,u.A)(e,2),t=(n[0],n[1]);return T.width>=t.min&&T.width<=t.max})))||void 0===e?void 0:e[0];t&&t!==m&&(h(t),y(null===(n=ae[t])||void 0===n?void 0:n.defaultVariant))}}),[T,M,l,m]);var L=(0,v.useCallback)((function(e){var n,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;h(e);var r=t||(null===(n=ae[e])||void 0===n?void 0:n.defaultVariant);y(r),"desktop"===e&&E("portrait")}),[]),R=(0,v.useCallback)((function(e){y(e)}),[]),F=(0,v.useCallback)((function(){"desktop"!==(null==I?void 0:I.category)&&E((function(e){return"portrait"===e?"landscape":"portrait"}))}),[I]),B=(0,v.useCallback)((function(){P((function(e){return!e}))}),[]),N=(0,v.useCallback)((function(){return{mobile:"@media (max-width: ".concat(M.mobile.max,"px)"),tablet:"@media (min-width: ".concat(M.tablet.min,"px) and (max-width: ").concat(M.tablet.max,"px)"),desktop:"@media (min-width: ".concat(M.desktop.min,"px)"),isMobile:"(max-width: ".concat(M.mobile.max,"px)"),isTablet:"(min-width: ".concat(M.tablet.min,"px) and (max-width: ").concat(M.tablet.max,"px)"),isDesktop:"(min-width: ".concat(M.desktop.min,"px)")}}),[M]),W=(0,v.useCallback)((function(e){return(null==I?void 0:I.category)===e}),[I]),H=(0,v.useCallback)((function(e){var n={size:z(e)};return"mobile"===(null==I?void 0:I.category)&&("table"===e&&(n.scroll={x:!0},n.pagination={simple:!0,size:"small"}),"form"===e&&(n.layout="vertical")),n}),[I,z]),U=(0,v.useCallback)((function(){if(!I)return{width:0,height:0};var e=I.width,n=I.height;return"landscape"===A&&"desktop"!==I.category?{width:n,height:e}:{width:e,height:n}}),[I,A]),Q={shouldHideComponent:function(e){var n;return((null==e||null===(n=e.responsive)||void 0===n?void 0:n.hideOn)||[]).includes(null==I?void 0:I.category)},getComponentStyles:function(e){var n=((null==e?void 0:e.responsive)||{})[null==I?void 0:I.category]||{};return le(le({},j),n)},isFeatureSupported:function(e){return!1!=={hover:"desktop"===(null==I?void 0:I.category),touch:"desktop"!==(null==I?void 0:I.category),keyboard:"desktop"===(null==I?void 0:I.category),contextMenu:"desktop"===(null==I?void 0:I.category)}[e]}};le(le({currentDevice:m,currentVariant:b,orientation:A,isFullscreen:C,deviceConfig:I,viewportSize:T,responsiveStyles:j,mediaQueries:N(),dimensions:U(),handleDeviceChange:L,handleVariantChange:R,handleOrientationChange:F,handleFullscreenToggle:B,isDevice:W,getComponentSize:z,getResponsiveProps:H},Q),{},{availableDevices:Object.keys(ae),availableVariants:I?Object.keys(I.variants||{}):[],breakpoints:M})}({initialDevice:"desktop",enableBreakpointDetection:!0}),function(e){var n=e.sessionId,t=e.userId,r=e.username,o=void 0===r?"Anonymous":r,a=e.avatar,i=void 0===a?null:a,l=e.enableCollaboration,c=void 0===l||l,d=e.enableCursorTracking,p=void 0===d||d,m=e.enableDeviceSync,f=void 0===m||m,g=(0,v.useState)(new Map),b=(0,u.A)(g,2),y=b[0],w=b[1],x=(0,v.useState)(new Map),A=(0,u.A)(x,2),E=A[0],k=A[1],S=(0,v.useState)(!1),C=(0,u.A)(S,2),P=C[0],_=C[1],T=(0,v.useState)("disconnected"),D=(0,u.A)(T,2),M=D[0],I=D[1],j=(0,v.useState)(new Map),z=(0,u.A)(j,2),L=z[0],R=z[1],F=(0,v.useState)({}),B=(0,u.A)(F,2),N=B[0],W=B[1],H=(0,v.useState)(new Map),U=(0,u.A)(H,2),Q=U[0],V=U[1],G=(0,v.useRef)(null),K=(0,v.useRef)(new Map),q=(0,v.useRef)(new Date),J=(0,O.d4)((function(e){var n;return(null===(n=e.websocket)||void 0===n?void 0:n.config)||{}}));(0,v.useEffect)((function(){if(c&&n){var e=new me({url:J.url||"ws://localhost:8000/ws/collaboration/",autoConnect:!0,reconnectOptions:{maxAttempts:10,initialDelay:1e3,maxDelay:3e4}});return G.current=e,e.on("connect",(function(){_(!0),I("connected"),e.joinSession(n,{username:o,avatar:i})})),e.on("disconnect",(function(){_(!1),I("disconnected")})),e.on("error",(function(e){I("error"),console.error("Collaborative WebSocket error:",e)})),Y(e),function(){e&&(e.leaveSession(n),e.disconnect()),K.current.forEach((function(e){return clearTimeout(e)})),K.current.clear()}}}),[n,c,o,i,J.url]);var Y=(0,v.useCallback)((function(e){e.onPreviewEvent("collaborator_joined",(function(e){var n=e.user_id,t=e.username,r=e.avatar;w((function(e){return new Map(e.set(n,{id:n,username:t,avatar:r,joinedAt:new Date,isActive:!0}))}))})),e.onPreviewEvent("collaborator_left",(function(e){var n=e.user_id;w((function(e){var t=new Map(e);return t.delete(n),t})),k((function(e){var t=new Map(e);return t.delete(n),t}))})),p&&e.onPreviewEvent("cursor_moved",(function(e){var n=e.user_id,r=e.position;if(n!==t){k((function(e){return new Map(e.set(n,{position:r,timestamp:new Date,userId:n}))}));var o=K.current.get(n);o&&clearTimeout(o);var a=setTimeout((function(){k((function(e){var t=new Map(e);return t.delete(n),t})),K.current.delete(n)}),5e3);K.current.set(n,a)}})),e.onPreviewEvent("component_updated",(function(e){var n=e.component_id,r=e.component_data,o=e.user_id;o!==t&&R((function(e){return new Map(e.set(n,ve(ve({},r),{},{lastUpdatedBy:o,lastUpdated:new Date,synced:!0})))}))})),e.onPreviewEvent("component_added",(function(e){var n=e.component,r=e.user_id;r!==t&&null!=n&&n.id&&R((function(e){return new Map(e.set(n.id,ve(ve({},n),{},{addedBy:r,synced:!0})))}))})),e.onPreviewEvent("component_deleted",(function(e){var n=e.component_id;e.user_id!==t&&R((function(e){var t=new Map(e);return t.delete(n),t}))})),f&&e.onPreviewEvent("device_changed",(function(e){var n=e.device_type,r=e.device_config,o=e.user_id;o!==t&&W({type:n,config:r,changedBy:o,timestamp:new Date})})),e.onPreviewEvent("preview_state_synced",(function(e){var n=e.state;if(n.components){var t=new Map(n.components);R(t)}n.deviceSettings&&f&&W(n.deviceSettings),q.current=new Date}))}),[t,p,f]),Z=(0,v.useCallback)(function(){var e=(0,s.A)(h().mark((function e(r,o){var a,i=arguments;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=i.length>2&&void 0!==i[2]&&i[2],G.current&&P){e.next=3;break}return e.abrupt("return",!1);case 3:return e.prev=3,e.next=6,G.current.sendComponentUpdate(r,o,{userId:t,sessionId:n,immediate:a});case 6:return e.abrupt("return",!0);case 9:return e.prev=9,e.t0=e.catch(3),console.error("Failed to send component update:",e.t0),e.abrupt("return",!1);case 13:case"end":return e.stop()}}),e,null,[[3,9]])})));return function(n,t){return e.apply(this,arguments)}}(),[P,t,n]),X=(0,v.useCallback)(function(){var e=(0,s.A)(h().mark((function e(r){return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(G.current&&P&&p){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,G.current.sendCursorPosition(r,{userId:t,sessionId:n});case 5:e.next=10;break;case 7:e.prev=7,e.t0=e.catch(2),console.error("Failed to send cursor position:",e.t0);case 10:case"end":return e.stop()}}),e,null,[[2,7]])})));return function(n){return e.apply(this,arguments)}}(),[P,t,n,p]),$=(0,v.useCallback)(function(){var e=(0,s.A)(h().mark((function e(r,o){return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(G.current&&P&&f){e.next=2;break}return e.abrupt("return",!1);case 2:return e.prev=2,e.next=5,G.current.sendDeviceChange(r,o,{userId:t,sessionId:n});case 5:return e.abrupt("return",!0);case 8:return e.prev=8,e.t0=e.catch(2),console.error("Failed to send device change:",e.t0),e.abrupt("return",!1);case 12:case"end":return e.stop()}}),e,null,[[2,8]])})));return function(n,t){return e.apply(this,arguments)}}(),[P,t,n,f]),ee=(0,v.useCallback)((0,s.A)(h().mark((function e(){return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(G.current&&P){e.next=2;break}return e.abrupt("return",!1);case 2:return e.prev=2,e.next=5,G.current.requestPreviewState(n);case 5:return e.abrupt("return",!0);case 8:return e.prev=8,e.t0=e.catch(2),console.error("Failed to request preview state:",e.t0),e.abrupt("return",!1);case 12:case"end":return e.stop()}}),e,null,[[2,8]])}))),[P,n]),ne=(0,v.useCallback)((function(e,n){V((function(r){return new Map(r.set(e,{resolution:n,timestamp:new Date,resolvedBy:t}))}))}),[t]),te=(0,v.useCallback)((function(e,n){var t=L.get(e),r=Q.get(e);if(!t)return n;if(!r){var o=new Date((null==n?void 0:n.lastUpdated)||0),a=new Date((null==t?void 0:t.lastUpdated)||0);if(Math.abs(o-a)<1e3)return a>o?t:n}switch(null==r?void 0:r.resolution){case"use_local":return n;case"use_remote":default:return t;case"merge":return ve(ve({},n),t)}}),[L,Q]),re=(0,v.useCallback)((function(){return{isConnected:P,connectionStatus:M,collaboratorCount:y.size,hasActiveCollaborators:y.size>0,lastSyncTime:q.current,syncedComponentCount:L.size}}),[P,M,y.size,L.size]);return{isConnected:P,connectionStatus:M,collaborators:Array.from(y.values()),cursors:Array.from(E.values()),syncedComponents:Array.from(L.entries()),deviceState:N,sendComponentUpdate:Z,sendCursorPosition:X,sendDeviceChange:$,requestSync:ee,resolveConflict:ne,getResolvedComponent:te,getCollaborationStatus:re,wsService:G.current}}({sessionId:ce,userId:de,username:"App Builder User",enableCollaboration:re.collaborationEnabled&&ue,enableCursorTracking:!0,enableDeviceSync:re.deviceSync})),fe=(0,ee.A)({components:pe,websocketService:he.wsService,enableWebSocket:re.realTimeEnabled&&ue,updateDelay:300}),ge=(0,v.useCallback)((function(e,n){oe((function(t){return Bn(Bn({},t),{},(0,d.A)({},e,n))}))}),[]);(0,v.useEffect)((function(){var n=function(){var n=(0,s.A)(h().mark((function n(){var t,r,o;return h().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,e((0,T.tI)(q)),console.log("App Builder initialized successfully"),(t=!1)&&console.log("Development mode: App will continue to load even if connections fail"),n.prev=5,console.log("Checking API connection..."),n.next=9,Promise.race([fetch("/api/status/"),new Promise((function(e,n){return setTimeout((function(){return n(new Error("API timeout"))}),1e3)}))]);case 9:n.sent.ok?(D((function(e){return Bn(Bn({},e),{},{api:"connected"})})),console.log("API connection successful")):(D((function(e){return Bn(Bn({},e),{},{api:t?"warning":"error"})})),console.warn("API connection failed")),n.next=18;break;case 13:n.prev=13,n.t0=n.catch(5),D((function(e){return Bn(Bn({},e),{},{api:t?"warning":"error"})})),console.warn("API connection error:",n.t0.message),t&&console.log("Development mode: Continuing with mock API");case 18:try{console.log("Checking WebSocket connection..."),t?(D((function(e){return Bn(Bn({},e),{},{websocket:"warning"})})),console.log("Development mode: Skipping WebSocket check")):(r=new WebSocket("ws://localhost:8000/ws/app_builder/"),o=setTimeout((function(){r.close(),D((function(e){return Bn(Bn({},e),{},{websocket:"error"})}))}),1e3),r.onopen=function(){clearTimeout(o),D((function(e){return Bn(Bn({},e),{},{websocket:"connected"})})),console.log("WebSocket connection successful"),r.close()},r.onerror=function(){clearTimeout(o),D((function(e){return Bn(Bn({},e),{},{websocket:"error"})})),console.warn("WebSocket connection failed"),r.close()})}catch(e){D((function(e){return Bn(Bn({},e),{},{websocket:t?"warning":"error"})})),console.warn("WebSocket connection error:",e.message)}n.next=26;break;case 21:n.prev=21,n.t1=n.catch(0),console.error("Failed to initialize App Builder:",n.t1),y.Ay.error("Failed to initialize App Builder. Please try refreshing the page."),p("Failed to initialize App Builder. Please try refreshing the page.");case 26:return n.prev=26,setTimeout((function(){a(!1)}),500),n.finish(26);case 29:case"end":return n.stop()}}),n,null,[[0,21,26,29],[5,13]])})));return function(){return n.apply(this,arguments)}}(),t=setTimeout((function(){a(!1),console.log("Loading timeout triggered - forcing app to load")}),3e3);return n(),function(){return clearTimeout(t)}}),[e,q]);var be=(0,v.useCallback)((function(n){J(n),e((0,T.tI)(n))}),[e]),ye=(0,v.useMemo)((function(){return[{key:"projects",label:v.createElement("span",null,v.createElement(M.A,null)," Projects"),children:v.createElement(v.Suspense,{fallback:v.createElement("div",{style:{padding:"20px",textAlign:"center"}},v.createElement(w.A,{size:"large"}),v.createElement("div",{style:{marginTop:"10px"}},"Loading Projects..."))},v.createElement(Qn,null))},{key:"components",label:v.createElement("span",null,v.createElement(I.A,null)," Component Builder"),children:v.createElement(Tn,{fallback:v.createElement("div",null,"Using basic component builder...")},v.createElement(v.Suspense,{fallback:v.createElement("div",{style:{padding:"20px",textAlign:"center"}},v.createElement(w.A,{size:"large"}),v.createElement("div",{style:{marginTop:"10px"}},"Loading Component Builder..."))},v.createElement(Nn,null)))},{key:"layouts",label:v.createElement("span",null,v.createElement(j.A,null)," Layout Designer"),children:v.createElement(Tn,{fallback:v.createElement("div",null,"Using basic layout designer...")},v.createElement(v.Suspense,{fallback:v.createElement("div",{style:{padding:"20px",textAlign:"center"}},v.createElement(w.A,{size:"large"}),v.createElement("div",{style:{marginTop:"10px"}},"Loading Layout Designer..."))},v.createElement(Wn,null)))},{key:"themes",label:v.createElement("span",null,v.createElement(z.A,null)," Theme Manager"),children:v.createElement(Tn,{fallback:v.createElement("div",null,"Using basic theme manager...")},v.createElement(v.Suspense,{fallback:v.createElement("div",{style:{padding:"20px",textAlign:"center"}},v.createElement(w.A,{size:"large"}),v.createElement("div",{style:{marginTop:"10px"}},"Loading Theme Manager..."))},v.createElement(Hn,null)))},{key:"export",label:v.createElement("span",null,v.createElement(L.A,null)," Enhanced Export"),children:v.createElement(v.Suspense,{fallback:v.createElement("div",{style:{padding:"20px",textAlign:"center"}},v.createElement(w.A,{size:"large"}),v.createElement("div",{style:{marginTop:"10px"}},"Loading Enhanced Code Exporter..."))},v.createElement(Vn,null))},{key:"performance",label:v.createElement("span",null,v.createElement(R.A,null)," Performance"),children:v.createElement(v.Suspense,{fallback:v.createElement("div",{style:{padding:"20px",textAlign:"center"}},v.createElement(w.A,{size:"large"}),v.createElement("div",{style:{marginTop:"10px"}},"Loading Performance Monitor..."))},v.createElement(Gn,null))},{key:"websocket",label:v.createElement("span",null,v.createElement(F.A,null)," WebSocket Manager"),children:v.createElement(v.Suspense,{fallback:v.createElement("div",{style:{padding:"20px",textAlign:"center"}},v.createElement(w.A,{size:"large"}),v.createElement("div",{style:{marginTop:"10px"}},"Loading WebSocket Manager..."))},v.createElement(Un,null))},{key:"data",label:v.createElement("span",null,v.createElement(B.A,null)," Data Management"),children:v.createElement(v.Suspense,{fallback:v.createElement("div",{style:{padding:"20px",textAlign:"center"}},v.createElement(w.A,{size:"large"}),v.createElement("div",{style:{marginTop:"10px"}},"Loading Data Management..."))},v.createElement(Kn,null))},{key:"testing",label:v.createElement("span",null,v.createElement(B.A,null)," Testing Tools"),children:v.createElement(v.Suspense,{fallback:v.createElement("div",{style:{padding:"20px",textAlign:"center"}},v.createElement(w.A,{size:"large"}),v.createElement("div",{style:{marginTop:"10px"}},"Loading Testing Tools..."))},v.createElement(qn,null))},{key:"tutorial",label:v.createElement("span",null,v.createElement(N.A,null)," Tutorial Assistant"),children:v.createElement(v.Suspense,{fallback:v.createElement("div",{style:{padding:"20px",textAlign:"center"}},v.createElement(w.A,{size:"large"}),v.createElement("div",{style:{marginTop:"10px"}},"Loading Tutorial Assistant..."))},v.createElement("div",{style:{padding:"20px"}},v.createElement(Yn,{level:3},"Tutorial Assistant"),v.createElement(Zn,null,"Get help and learn how to use App Builder with our AI-powered tutorial assistant."),v.createElement(Jn,null)))}]}),[]);return o?v.createElement(ot,null,v.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"70vh",flexDirection:"column"}},v.createElement("div",{style:{width:"50px",height:"50px",border:"5px solid #f3f3f3",borderTop:"5px solid #3498db",borderRadius:"50%",animation:"spin 1s linear infinite"}}),v.createElement(Yn,{level:3,style:{marginTop:"20px"}},"Loading App Builder..."),v.createElement("div",{style:{marginTop:"20px",textAlign:"center"}},v.createElement("div",{style:{color:"var(--color-text)",backgroundColor:"var(--color-background-secondary)",padding:"8px 16px",borderRadius:"8px",marginBottom:"8px",border:"1px solid var(--color-border-light)"}},"API Connection: "," ",v.createElement("span",{style:{color:"connected"===g.api?"#52c41a":"error"===g.api?"#ff4d4f":"warning"===g.api?"#faad14":"#1890ff",fontWeight:"600"}},"connected"===g.api?"Connected":"error"===g.api?"Failed":"warning"===g.api?"Limited (Mock)":"Checking...")),v.createElement("div",{style:{color:"var(--color-text)",backgroundColor:"var(--color-background-secondary)",padding:"8px 16px",borderRadius:"8px",border:"1px solid var(--color-border-light)"}},"WebSocket Connection: "," ",v.createElement("span",{style:{color:"connected"===g.websocket?"#52c41a":"error"===g.websocket?"#ff4d4f":"warning"===g.websocket?"#faad14":"#1890ff",fontWeight:"600"}},"connected"===g.websocket?"Connected":"error"===g.websocket?"Failed":"warning"===g.websocket?"Limited (Mock)":"Checking...")),("error"===g.api||"error"===g.websocket)&&v.createElement("div",{style:{marginTop:"20px",color:"#ff4d4f",backgroundColor:"var(--color-background-secondary)",padding:"12px 16px",borderRadius:"8px",border:"1px solid #ff4d4f",fontWeight:"500"}},v.createElement("p",{style:{margin:"0 0 8px 0"}},"Some connections failed. The app will continue to load with limited functionality."),v.createElement("p",{style:{margin:0}},"Please ensure the backend server is running at http://localhost:8000")),("warning"===g.api||"warning"===g.websocket)&&v.createElement("div",{style:{marginTop:"20px",color:"#faad14",backgroundColor:"var(--color-background-secondary)",padding:"12px 16px",borderRadius:"8px",border:"1px solid #faad14",fontWeight:"500"}},v.createElement("p",{style:{margin:"0 0 8px 0"}},"Some connections are in limited mode. The app will use mock data."),v.createElement("p",{style:{margin:0}},"This is normal in development mode when the backend is not running."))))):c?v.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"70vh",flexDirection:"column"}},v.createElement("div",{style:{color:"red",fontSize:"48px",marginBottom:"20px"}},v.createElement(B.A,null)),v.createElement(Yn,{level:3,style:{color:"red"}},"Error"),v.createElement(Zn,{style:{textAlign:"center",maxWidth:"600px",marginTop:"20px"}},c),v.createElement(x.Ay,{type:"primary",style:{marginTop:"20px"},onClick:function(){return window.location.reload()}},"Refresh Page")):v.createElement(Xn,{className:"app-builder-enhanced"},v.createElement($n,null,v.createElement("div",null,v.createElement(Yn,{level:2},"App Builder Enhanced"),v.createElement(Zn,null,"Create and manage your application components with ease"),v.createElement("div",{style:{display:"flex",gap:"10px",marginTop:"5px"}},v.createElement(A.A,{title:"connected"===g.api?"API Connected":"warning"===g.api?"API in Limited Mode (Mock)":"API Connection Failed"},v.createElement("div",{style:{display:"inline-flex",alignItems:"center",fontSize:"12px",color:"connected"===g.api?"#52c41a":"warning"===g.api?"#faad14":"#ff4d4f",backgroundColor:"var(--color-background-secondary)",padding:"4px 8px",borderRadius:"12px",border:"1px solid var(--color-border-light)",fontWeight:"500"}},v.createElement("div",{style:{width:"8px",height:"8px",borderRadius:"50%",backgroundColor:"connected"===g.api?"#52c41a":"warning"===g.api?"#faad14":"#ff4d4f",marginRight:"6px",boxShadow:"0 0 4px ".concat("connected"===g.api?"#52c41a":"warning"===g.api?"#faad14":"#ff4d4f")}}),"API")),v.createElement(A.A,{title:"connected"===g.websocket?"WebSocket Connected":"warning"===g.websocket?"WebSocket in Limited Mode (Mock)":"WebSocket Connection Failed"},v.createElement("div",{style:{display:"inline-flex",alignItems:"center",fontSize:"12px",color:"connected"===g.websocket?"#52c41a":"warning"===g.websocket?"#faad14":"#ff4d4f",backgroundColor:"var(--color-background-secondary)",padding:"4px 8px",borderRadius:"12px",border:"1px solid var(--color-border-light)",fontWeight:"500"}},v.createElement("div",{style:{width:"8px",height:"8px",borderRadius:"50%",backgroundColor:"connected"===g.websocket?"#52c41a":"warning"===g.websocket?"#faad14":"#ff4d4f",marginRight:"6px",boxShadow:"0 0 4px ".concat("connected"===g.websocket?"#52c41a":"warning"===g.websocket?"#faad14":"#ff4d4f")}}),"WebSocket")))),v.createElement("div",{style:{display:"flex",alignItems:"center",gap:"12px"}},v.createElement("div",{style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 12px",background:"rgba(255, 255, 255, 0.1)",borderRadius:"8px",border:"1px solid rgba(255, 255, 255, 0.2)"}},v.createElement(A.A,{title:"Real-time Preview"},v.createElement(E.A,{size:"small",checked:re.realTimeEnabled,onChange:function(e){return ge("realTimeEnabled",e)},checkedChildren:v.createElement(W.A,null),unCheckedChildren:v.createElement(W.A,null)})),v.createElement(A.A,{title:"Collaboration"},v.createElement(E.A,{size:"small",checked:re.collaborationEnabled&&ue,onChange:function(e){return ge("collaborationEnabled",e)},disabled:!ue,checkedChildren:v.createElement(H.A,null),unCheckedChildren:v.createElement(H.A,null)})),v.createElement(A.A,{title:"Performance Monitoring"},v.createElement(E.A,{size:"small",checked:re.performanceMonitoring,onChange:function(e){return ge("performanceMonitoring",e)},checkedChildren:v.createElement(U.A,null),unCheckedChildren:v.createElement(U.A,null)})),re.collaborationEnabled&&he.isConnected&&v.createElement(k.A,{count:he.collaborators.length,showZero:!1,style:{backgroundColor:"#52c41a"}},v.createElement(A.A,{title:"Active collaborators"},v.createElement("div",{style:{width:"20px",height:"20px",borderRadius:"50%",background:"#52c41a",display:"flex",alignItems:"center",justifyContent:"center"}},v.createElement(H.A,{style:{fontSize:"10px",color:"white"}}))))),v.createElement(A.A,{title:"Open Tutorial Assistant"},v.createElement(x.Ay,{type:"default",icon:v.createElement(N.A,null),style:{marginRight:"10px"},onClick:function(){return $(!0)}},"Help")),v.createElement(A.A,{title:"Refresh connections"},v.createElement(x.Ay,{type:"default",onClick:function(){return window.location.reload()}},"Refresh")))),v.createElement(rt,null,v.createElement(S.A,{gutter:[24,24]},v.createElement(C.A,{xs:24,md:16},v.createElement(Yn,{level:4},"Welcome to App Builder Enhanced"),v.createElement(Zn,null,"This tool helps you create and manage your application components with ease. Use the tabs below to navigate between different features."),v.createElement(x.Ay,{type:"primary",size:"large",onClick:function(){return be("components")}},"Start Building")),v.createElement(C.A,{xs:24,md:8},v.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},v.createElement("img",{src:"/static/images/app-builder-logo.svg",alt:"App Builder Logo",style:{maxWidth:"100%",height:"auto"},onError:function(e){e.target.onerror=null,e.target.style.display="none"}}))))),v.createElement(et,{activeKey:q,onChange:be,type:"card",size:"large",items:ye}),v.createElement(b.A,{title:"Getting Started",style:{marginTop:"24px"}},v.createElement(S.A,{gutter:[24,24]},v.createElement(C.A,{xs:24,md:6},v.createElement(nt,{title:"Step 1",active:"components"===q,onClick:function(){return be("components")}},v.createElement(tt,{active:"components"===q},v.createElement(I.A,null)),v.createElement(Zn,null,"Use the Component Builder to create UI components"))),v.createElement(C.A,{xs:24,md:6},v.createElement(nt,{title:"Step 2",active:"layouts"===q,onClick:function(){return be("layouts")}},v.createElement(tt,{active:"layouts"===q},v.createElement(j.A,null)),v.createElement(Zn,null,"Design your layout with the Layout Designer"))),v.createElement(C.A,{xs:24,md:6},v.createElement(nt,{title:"Step 3",active:"themes"===q,onClick:function(){return be("themes")}},v.createElement(tt,{active:"themes"===q},v.createElement(z.A,null)),v.createElement(Zn,null,"Customize your theme with the Theme Manager"))),v.createElement(C.A,{xs:24,md:6},v.createElement(nt,{title:"Step 4",active:"websocket"===q,onClick:function(){return be("websocket")}},v.createElement(tt,{active:"websocket"===q},v.createElement(F.A,null)),v.createElement(Zn,null,"Set up real-time communication with WebSocket Manager"))))),re.performanceMonitoring&&v.createElement(kn,{renderTime:fe.isUpdating?16:8,frameRate:60,memoryUsage:performance.memory?Math.round(performance.memory.usedJSHeapSize/1024/1024):0,componentCount:pe.length,visibleComponents:pe.length,cacheSize:0,updateFrequency:fe.hasPendingUpdates?30:0,floating:!0,showAlerts:!0,optimizationsEnabled:re.realTimeEnabled,onToggleOptimizations:function(e){return ge("realTimeEnabled",e)}}),v.createElement(P.A,{icon:v.createElement(N.A,null),type:"primary",style:{right:re.performanceMonitoring?340:24,bottom:24},onClick:function(){return $(!0)},tooltip:"Tutorial Assistant"}),v.createElement(_.A,{title:"Tutorial Assistant",open:X,onCancel:function(){return $(!1)},footer:null,width:800,style:{top:20}},v.createElement("div",{style:{padding:"10px 0"}},v.createElement(Zn,null,"Get help and learn how to use App Builder with our AI-powered tutorial assistant."),v.createElement(v.Suspense,{fallback:v.createElement("div",{style:{padding:"20px",textAlign:"center"}},v.createElement(w.A,{size:"large"}),v.createElement("div",{style:{marginTop:"10px"}},"Loading Tutorial Assistant..."))},v.createElement(Jn,null)))))}},6390:(e,n,t)=>{t.d(n,{$0:()=>a,e9:()=>i});var r="http://localhost:8000",o="ws://localhost:8000/ws";console.log("Environment Variables:"),console.log("API_URL:","http://localhost:8000"),console.log("WS_URL:","ws://localhost:8000/ws"),console.log("WS_HOST:","localhost:8000"),console.log("WS_ENDPOINT:","app_builder"),console.log("BACKEND_HOST:","localhost"),console.log("ENV:","development"),console.log("DEBUG:",!0);var a=function(e){var n,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.replace(/^\/+|\/+$/g,"");t.forceSecure||"undefined"!=typeof window&&window.location.protocol;var a=o.endsWith("/")?o:"".concat(o,"/");n="".concat(a).concat(r,"/"),console.log("Generated WebSocket URL:",n);var i=n.replace(/([^:]\/)\/+/g,"$1");return console.log("WebSocket URL for endpoint '".concat(r,"': ").concat(i)),i},i=function(e){var n=e.replace(/^\/+|\/+$/g,""),t=r.endsWith("/")?r.slice(0,-1):r;return"".concat(t,"/api/").concat(n,"/")}},6413:(e,n,t)=>{t.d(n,{A:()=>l});var r,o=t(7528),a=t(6540),i=t(1250).Ay.a(r||(r=(0,o.A)(["\n  position: absolute;\n  top: -40px;\n  left: 0;\n  background: ",";\n  color: white;\n  padding: 8px;\n  z-index: ",";\n  transition: top 0.3s;\n\n  &:focus {\n    top: 0;\n  }\n"])),(function(e){var n,t,r;return null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colorPalette)&&void 0!==n&&n.primary?e.theme.colorPalette.primary:null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colors)&&void 0!==t&&null!==(t=t.primary)&&void 0!==t&&t.main?e.theme.colors.primary.main:null!==(r=e.theme)&&void 0!==r&&r.primaryColor?e.theme.primaryColor:"#2563EB"}),(function(e){var n,t;return(null===(n=e.theme)||void 0===n||null===(n=n.zIndex)||void 0===n?void 0:n.tooltip)||(null===(t=e.theme)||void 0===t||null===(t=t.zIndex)||void 0===t?void 0:t.modal)||1e3}));const l=function(e){var n=e.targetId,t=void 0===n?"main-content":n;return a.createElement(i,{href:"#".concat(t)},"Skip to main content")}},6894:(e,n,t)=>{t.d(n,{JT:()=>M,LN:()=>C,Lv:()=>P,T6:()=>D,jn:()=>O,n5:()=>I,p1:()=>T,pK:()=>_});var r,o,a,i,l,c,s,d,u,p,m,h,v=t(7528),f=t(1250),g=t(3016),b=t(5448),y=t(677),w=t(9249),x=t(7355),A=t(6955),E=g.A.Title,k=(g.A.Text,g.A.Paragraph),S=b.A.Content,C=(0,f.Ay)(S)(r||(r=(0,v.A)(["\n  padding: 24px;\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  \n  @media (max-width: 768px) {\n    padding: 16px;\n  }\n"]))),P=(f.Ay.div(o||(o=(0,v.A)(["\n  margin-bottom: 32px;\n  padding: ",";\n  background-color: ",";\n  border-radius: 8px;\n"])),(function(e){return e.padded?"24px":"0"}),(function(e){return e.background?e.background:"transparent"})),(0,f.Ay)(y.A)(a||(a=(0,v.A)(["\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);\n  }\n  \n  .ant-card-head {\n    border-bottom: 1px solid #f0f0f0;\n  }\n  \n  .ant-card-head-title {\n    font-weight: 600;\n  }\n"])))),_=(0,f.Ay)(P)(i||(i=(0,v.A)(["\n  height: 100%;\n  \n  .ant-card-body {\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n    height: calc(100% - 57px); // Adjust for card header\n  }\n"]))),O=(0,f.Ay)(w.Ay)(l||(l=(0,v.A)(["\n  height: 40px;\n  font-weight: 500;\n  \n  &.ant-btn-primary {\n    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);\n    \n    &:hover {\n      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);\n    }\n  }\n"]))),T=((0,f.Ay)(w.Ay)(c||(c=(0,v.A)(["\n  height: 40px;\n  font-weight: 500;\n"]))),(0,f.Ay)(E)(s||(s=(0,v.A)(["\n  margin-bottom: "," !important;\n  color: ",";\n"])),(function(e){return e.noMargin?"0":"24px"}),(function(e){return e.color||"inherit"}))),D=(0,f.Ay)(k)(d||(d=(0,v.A)(["\n  margin-bottom: "," !important;\n  font-size: 16px;\n  line-height: 1.6;\n  color: rgba(0, 0, 0, 0.65);\n"])),(function(e){return e.noMargin?"0":"16px"})),M=((0,f.Ay)(x.A)(u||(u=(0,v.A)(["\n  height: 40px;\n"]))),(0,f.Ay)(A.A)(p||(p=(0,v.A)(["\n  .ant-table {\n    border-radius: 8px;\n    overflow: hidden;\n  }\n  \n  .ant-table-thead > tr > th {\n    background-color: #fafafa;\n    font-weight: 600;\n  }\n"]))),f.Ay.div(m||(m=(0,v.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 24px;\n  margin-bottom: 32px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n"])))),I=f.Ay.div(h||(h=(0,v.A)(["\n  display: flex;\n  flex-direction: ",";\n  justify-content: ",";\n  align-items: ",";\n  flex-wrap: ",";\n  gap: ","px;\n"])),(function(e){return e.direction||"row"}),(function(e){return e.justify||"flex-start"}),(function(e){return e.align||"flex-start"}),(function(e){return e.wrap||"nowrap"}),(function(e){return e.gap||"0"}))},7177:(e,n,t)=>{t.r(n),t.d(n,{ConnectionState:()=>c,default:()=>s});var r=t(436),o=t(4467),a=t(3029),i=t(2901);function l(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}var c={CONNECTING:0,OPEN:1,CLOSING:2,CLOSED:3,RECONNECTING:4};const s=function(){return(0,i.A)((function e(n){(0,a.A)(this,e),this.options=function(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?l(Object(t),!0).forEach((function(n){(0,o.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}({url:"",autoConnect:!0,autoReconnect:!0,reconnectInterval:1e3,maxReconnectInterval:3e4,reconnectDecay:1.5,maxReconnectAttempts:1/0,connectionTimeout:5e3,heartbeatInterval:3e4,debug:!1,batchInterval:50,maxBatchSize:100,enableCompression:!0,compressionThreshold:1024,prioritizeUrgentMessages:!0,maxOfflineQueueSize:1e3,persistOfflineMessages:!1,persistenceKey:"enhanced_ws_offline_queue",protocols:null},n),this.ws=null,this.connectionState=c.CLOSED,this.reconnectAttempts=0,this.currentReconnectInterval=this.options.reconnectInterval,this.reconnectTimer=null,this.connectionTimeoutTimer=null,this.heartbeatTimer=null,this.missedHeartbeats=0,this.maxMissedHeartbeats=3,this.messageQueue=[],this.batchTimer=null,this.offlineQueue=[],this.isOnline=navigator.onLine,this.eventListeners={open:[],close:[],error:[],message:[],reconnect:[],reconnect_attempt:[],reconnect_failed:[]},this._handleOpen=this._handleOpen.bind(this),this._handleClose=this._handleClose.bind(this),this._handleError=this._handleError.bind(this),this._handleMessage=this._handleMessage.bind(this),this._handleOnline=this._handleOnline.bind(this),this._handleOffline=this._handleOffline.bind(this),window.addEventListener("online",this._handleOnline),window.addEventListener("offline",this._handleOffline),this.options.persistOfflineMessages&&this._loadPersistedMessages(),this.options.autoConnect&&this.open()}),[{key:"open",value:function(){var e=this;return new Promise((function(n,t){if(e.connectionState===c.CONNECTING||e.connectionState===c.OPEN)return e._debug("WebSocket already connecting or open"),void(e.connectionState===c.OPEN&&n());e.close(),e.connectionState=c.CONNECTING;try{e._debug("Opening WebSocket connection to ".concat(e.options.url)),e.options.protocols?e.ws=new WebSocket(e.options.url,e.options.protocols):e.ws=new WebSocket(e.options.url),e.ws.addEventListener("open",(function(t){e._debug("Connection successful"),e._handleOpen(t),n()}),{once:!0}),e.ws.addEventListener("error",(function(n){var r=new Error("WebSocket connection error");r.originalEvent=n,e._handleError(n),t(r)}),{once:!0}),e.ws.addEventListener("close",(function(n){if(e.connectionState===c.CONNECTING){var r=new Error("WebSocket connection closed during connection attempt: ".concat(n.code," ").concat(n.reason));r.closeEvent=n,t(r)}}),{once:!0}),e.ws.onopen=e._handleOpen,e.ws.onclose=e._handleClose,e.ws.onerror=e._handleError,e.ws.onmessage=e._handleMessage,e.connectionTimeoutTimer=setTimeout((function(){if(e.connectionState===c.CONNECTING){e._debug("Connection timeout");var n=new Error("WebSocket connection timeout after ".concat(e.options.connectionTimeout,"ms"));e.ws.close(),t(n)}}),e.options.connectionTimeout)}catch(n){e._debug("Error creating WebSocket:",n),e._handleError(n),t(n)}}))}},{key:"close",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(this._clearTimers(),this.ws){this.connectionState=c.CLOSING;try{this.ws.close(e,n)}catch(e){this._debug("Error closing WebSocket:",e)}}this.connectionState=c.CLOSED}},{key:"send",value:function(e){var n=this,t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=r.compress,a=void 0===o||o,i=r.urgent;return void 0!==i&&i&&this.connectionState===c.OPEN?this._sendImmediate(e,a):this.connectionState!==c.OPEN?(this._debug("WebSocket not open, queueing message"),this.offlineQueue.push({data:e,options:r}),!1):t&&this.options.batchInterval>0?(this.messageQueue.push({data:e,options:r}),this.batchTimer||(this.batchTimer=setTimeout((function(){return n._processBatch()}),this.options.batchInterval)),this.messageQueue.length>=this.options.maxBatchSize&&this._processBatch(),!0):this._sendImmediate(e,a)}},{key:"_sendImmediate",value:function(e,n){try{var t;if("string"==typeof e)t=e;else try{t=JSON.stringify(e)}catch(e){return this._debug("Error stringifying message:",e),this._handleError(e),!1}return n&&t.length>1024&&(this._debug("Compressing large message (".concat(t.length," bytes)")),t=JSON.stringify({type:"compressed",data:t,originalSize:t.length})),this.ws.send(t),!0}catch(e){return this._debug("Error sending message:",e),this._handleError(e),!1}}},{key:"addEventListener",value:function(e,n){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.eventListeners[e]||(this.eventListeners[e]=[]),this.eventListeners[e].push({callback:n,options:t}),this._debug("Added event listener for ".concat(e))}},{key:"removeEventListener",value:function(e,n){this.eventListeners[e]&&(this.eventListeners[e]=this.eventListeners[e].filter((function(e){return e.callback!==n})),this._debug("Removed event listener for ".concat(e)))}},{key:"_dispatchEvent",value:function(e,n){var t=this;this.eventListeners[e]&&this.eventListeners[e].forEach((function(r){try{r.callback(n),r.options.once&&t.removeEventListener(e,r.callback)}catch(n){t._debug("Error in ".concat(e," listener:"),n)}}))}},{key:"_handleOpen",value:function(e){this._debug("WebSocket connection opened"),clearTimeout(this.connectionTimeoutTimer),this.connectionState=c.OPEN,this.reconnectAttempts=0,this.currentReconnectInterval=this.options.reconnectInterval,this._startHeartbeat(),this._processOfflineQueue(),this._dispatchEvent("open",e)}},{key:"_handleClose",value:function(e){var n=this;this._debug("WebSocket connection closed: ".concat(e.code," ").concat(e.reason)),this._clearTimers();var t={code:e.code,reason:e.reason,wasClean:e.wasClean,timestamp:(new Date).toISOString()};this.connectionState=c.CLOSED;var r=this._handleCloseCode(e.code,e.reason);this.options.autoReconnect&&r&&setTimeout((function(){n._reconnect().catch((function(e){n._debug("Reconnection chain failed:",e)}))}),100),this._dispatchEvent("close",{originalEvent:e,closeInfo:t,willReconnect:this.options.autoReconnect&&r})}},{key:"_handleCloseCode",value:function(e,n){return 1e3===e?(this._debug("Normal closure, not reconnecting"),!1):1001===e?(this._debug("Server going away, will attempt reconnect"),!0):1002===e?(this._debug("Protocol error, will attempt reconnect with delay"),this.currentReconnectInterval=Math.max(2*this.currentReconnectInterval,this.options.reconnectInterval),!0):1003===e?(this._debug("Unsupported data format, will attempt reconnect"),!0):1005===e?(this._debug("No status code provided, will attempt reconnect"),!0):1006===e?(this._debug("Abnormal closure, will attempt reconnect"),!0):1007===e?(this._debug("Invalid frame payload data, will attempt reconnect"),!0):1008===e?(this._debug("Policy violation, will attempt reconnect with increased delay"),this.currentReconnectInterval=Math.max(3*this.currentReconnectInterval,this.options.reconnectInterval),!0):1009===e?(this._debug("Message too big, will attempt reconnect"),!0):1010===e?(this._debug("Missing extension, will attempt reconnect"),!0):1011===e?(this._debug("Server internal error, will attempt reconnect with delay"),this.currentReconnectInterval=Math.max(2*this.currentReconnectInterval,this.options.reconnectInterval),!0):1012===e?(this._debug("Service restart, will attempt reconnect"),!0):1013===e?(this._debug("Service unavailable, will attempt reconnect with increased delay"),this.currentReconnectInterval=Math.max(4*this.currentReconnectInterval,this.options.reconnectInterval),!0):4e3===e?(this._debug("Heartbeat timeout, will attempt reconnect"),!0):(this._debug("Unknown close code ".concat(e,", will attempt reconnect")),!0)}},{key:"_handleError",value:function(e){this._debug("WebSocket error:",e);var n={originalEvent:e,message:"WebSocket connection error",timestamp:(new Date).toISOString(),connectionState:this.connectionState,reconnectAttempts:this.reconnectAttempts,url:this.options.url};this._dispatchEvent("error",n)}},{key:"_handleMessage",value:function(e){var n=this,t=e.data;try{if("string"==typeof t&&(t.startsWith("{")||t.startsWith("["))&&(t=JSON.parse(t)),"pong"===t||t&&"pong"===t.type)return void this._handleHeartbeatResponse();if(t&&"compressed"===t.type&&(this._debug("Received compressed message (".concat(t.originalSize," bytes)")),"string"==typeof(t=t.data)&&(t.startsWith("{")||t.startsWith("["))))try{t=JSON.parse(t)}catch(e){this._debug("Error parsing decompressed data:",e)}if(t&&"batch"===t.type){this._debug("Received batch message with ".concat(t.count," messages"));var r=function(t){Array.isArray(t)&&t.forEach((function(t){try{var r=t;"string"==typeof t&&(t.startsWith("{")||t.startsWith("["))&&(r=JSON.parse(t)),n._dispatchEvent("message",{data:r,originalEvent:e,isBatchItem:!0})}catch(e){n._debug("Error processing batch item:",e)}}))};return void(t.messages?(t.messages.uncompressed&&r(t.messages.uncompressed),t.messages.compressed&&r(t.messages.compressed)):Array.isArray(t.messages)&&r(t.messages))}this._dispatchEvent("message",{data:t,originalEvent:e})}catch(n){this._debug("Error parsing message:",n),this._dispatchEvent("message",{data:e.data,originalEvent:e})}}},{key:"_handleOnline",value:function(){this._debug("Browser went online"),this.isOnline=!0,this.connectionState!==c.OPEN&&this.options.autoReconnect&&this._reconnect()}},{key:"_handleOffline",value:function(){this._debug("Browser went offline"),this.isOnline=!1,this.options.persistOfflineMessages&&this.offlineQueue.length>0&&this._persistOfflineMessages()}},{key:"_reconnect",value:function(){var e=this;return new Promise((function(n,t){if(e.connectionState!==c.RECONNECTING){if(e.reconnectAttempts>=e.options.maxReconnectAttempts){e._debug("Max reconnect attempts reached");var r=new Error("Maximum reconnect attempts (".concat(e.options.maxReconnectAttempts,") reached"));return e._dispatchEvent("reconnect_failed",{attempts:e.reconnectAttempts,error:r}),void t(r)}if(navigator.onLine){e.connectionState=c.RECONNECTING,e.reconnectAttempts++;var o=.3*Math.random()+.85,a=Math.min(e.currentReconnectInterval*e.options.reconnectDecay*o,e.options.maxReconnectInterval);e.currentReconnectInterval=a,e._debug("Reconnecting in ".concat(Math.round(a),"ms (attempt ").concat(e.reconnectAttempts,"/").concat(e.options.maxReconnectAttempts,")")),e._dispatchEvent("reconnect_attempt",{attempt:e.reconnectAttempts,interval:a,maxAttempts:e.options.maxReconnectAttempts}),e.reconnectTimer=setTimeout((function(){e._debug("Attempting reconnect (".concat(e.reconnectAttempts,"/").concat(e.options.maxReconnectAttempts,")")),e.open().then((function(){e._debug("Reconnection successful"),e._dispatchEvent("reconnect",{attempt:e.reconnectAttempts,success:!0}),n()})).catch((function(r){e._debug("Reconnection failed:",r),e._dispatchEvent("reconnect",{attempt:e.reconnectAttempts,success:!1,error:r}),e.reconnectAttempts<e.options.maxReconnectAttempts?e._reconnect().then(n).catch(t):t(r)}))}),a)}else{e._debug("Browser is offline, waiting for online event");var i=new Error("Cannot reconnect while offline");t(i)}}else e._debug("Already attempting to reconnect")}))}},{key:"_startHeartbeat",value:function(){var e=this;this._clearHeartbeat(),this.options.heartbeatInterval<=0||(this._debug("Starting heartbeat (interval: ".concat(this.options.heartbeatInterval,"ms)")),this.missedHeartbeats=0,this.heartbeatTimer=setInterval((function(){e._sendHeartbeat()}),this.options.heartbeatInterval))}},{key:"_sendHeartbeat",value:function(){if(this.connectionState===c.OPEN){if(this._debug("Sending heartbeat"),this.missedHeartbeats++,this.missedHeartbeats>=this.maxMissedHeartbeats)return this._debug("Too many missed heartbeats (".concat(this.missedHeartbeats,"), closing connection")),void this.close(4e3,"Heartbeat timeout");try{this.ws.send(JSON.stringify({type:"ping",timestamp:Date.now()}))}catch(e){this._debug("Error sending heartbeat:",e)}}}},{key:"_handleHeartbeatResponse",value:function(){this._debug("Received heartbeat response"),this.missedHeartbeats=0}},{key:"_clearHeartbeat",value:function(){this.heartbeatTimer&&(clearInterval(this.heartbeatTimer),this.heartbeatTimer=null)}},{key:"_processBatch",value:function(){if(clearTimeout(this.batchTimer),this.batchTimer=null,0!==this.messageQueue.length){var e;if(this.connectionState!==c.OPEN)return(e=this.offlineQueue).push.apply(e,(0,r.A)(this.messageQueue)),void(this.messageQueue=[]);this._debug("Processing batch of ".concat(this.messageQueue.length," messages"));var n=[],t=[];this.messageQueue.forEach((function(e){var r=e.data,o=e.options,a=(void 0===o?{}:o).compress,i=void 0===a||a,l="string"==typeof r?r:JSON.stringify(r);i?n.push(l):t.push(l)}));var o={type:"batch",compressed:n.length>0,messages:{compressed:n,uncompressed:t},count:n.length+t.length,timestamp:Date.now()},a=(0,r.A)(this.messageQueue);this.messageQueue=[];try{this.ws.send(JSON.stringify(o))}catch(e){var i;this._debug("Error sending batch:",e),this._handleError(e),(i=this.offlineQueue).push.apply(i,(0,r.A)(a))}}}},{key:"_processOfflineQueue",value:function(){var e=this;if(0!==this.offlineQueue.length){this._debug("Processing offline queue of ".concat(this.offlineQueue.length," messages"));var n,t=(0,r.A)(this.offlineQueue).sort((function(e,n){var t,r,o=(null===(t=e.options)||void 0===t?void 0:t.urgent)||!1;return((null===(r=n.options)||void 0===r?void 0:r.urgent)||!1)-o})),o=t.filter((function(e){var n;return null===(n=e.options)||void 0===n?void 0:n.urgent})),a=t.filter((function(e){var n;return!(null!==(n=e.options)&&void 0!==n&&n.urgent)}));this.offlineQueue=[],o.forEach((function(n){var t=n.data,r=n.options,o=(void 0===r?{}:r).compress,a=void 0===o||o;e._sendImmediate(t,a)})),a.length>0&&((n=this.messageQueue).push.apply(n,(0,r.A)(a)),this._processBatch())}}},{key:"_clearTimers",value:function(){this.connectionTimeoutTimer&&(clearTimeout(this.connectionTimeoutTimer),this.connectionTimeoutTimer=null),this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this._clearHeartbeat(),this.batchTimer&&(clearTimeout(this.batchTimer),this.batchTimer=null)}},{key:"_debug",value:function(){if(this.options.debug){for(var e,n=new Date,t=n.getFullYear(),r=String(n.getMonth()+1).padStart(2,"0"),o=String(n.getDate()).padStart(2,"0"),a=String(n.getHours()).padStart(2,"0"),i=String(n.getMinutes()).padStart(2,"0"),l=String(n.getSeconds()).padStart(2,"0"),c=String(n.getMilliseconds()).padStart(3,"0"),s="".concat(t,"-").concat(r,"-").concat(o," ").concat(a,":").concat(i,":").concat(l,".").concat(c),d=arguments.length,u=new Array(d),p=0;p<d;p++)u[p]=arguments[p];(e=console).log.apply(e,["[".concat(s,"][EnhancedWebSocketClient]")].concat(u))}}},{key:"_persistOfflineMessages",value:function(){if(this.options.persistOfflineMessages&&0!==this.offlineQueue.length)try{var e=JSON.stringify(this.offlineQueue);localStorage.setItem(this.options.persistenceKey,e),this._debug("Persisted ".concat(this.offlineQueue.length," offline messages to localStorage"))}catch(e){this._debug("Error persisting offline messages:",e)}}},{key:"_loadPersistedMessages",value:function(){if(this.options.persistOfflineMessages)try{var e=localStorage.getItem(this.options.persistenceKey);if(!e)return;var n,t=JSON.parse(e);Array.isArray(t)&&t.length>0&&((n=this.offlineQueue).push.apply(n,(0,r.A)(t)),this.offlineQueue.length>this.options.maxOfflineQueueSize&&(this.offlineQueue=this.offlineQueue.slice(-this.options.maxOfflineQueueSize)),this._debug("Loaded ".concat(t.length," persisted offline messages")),localStorage.removeItem(this.options.persistenceKey))}catch(e){this._debug("Error loading persisted offline messages:",e)}}},{key:"destroy",value:function(){this._debug("Destroying WebSocket client"),this.options.persistOfflineMessages&&this.offlineQueue.length>0&&this._persistOfflineMessages(),this.close(),this._clearTimers(),window.removeEventListener("online",this._handleOnline),window.removeEventListener("offline",this._handleOffline),this.eventListeners={},this.messageQueue=[],this.offlineQueue=[]}}])}()},7683:(e,n,t)=>{t.d(n,{A:()=>D});var r,o,a,i,l,c,s,d,u=t(5544),p=t(7528),m=t(6540),h=t(9249),v=t(7977),f=t(761),g=t(2648),b=t(1972),y=t(8589),w=t(6067),x=t(1250),A=t(2569),E=(0,x.i7)(r||(r=(0,p.A)(["\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n"]))),k=(0,x.i7)(o||(o=(0,p.A)(["\n  from {\n    opacity: 0;\n    transform: scale(0.8);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n"]))),S=x.Ay.div(a||(a=(0,p.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"]))),C=(0,x.Ay)(h.Ay)(i||(i=(0,p.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  border: 1px solid var(--color-border);\n  background-color: var(--color-surface);\n  color: var(--color-text);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n\n  /* Ensure WCAG AA contrast compliance */\n  &:hover, &:focus {\n    border-color: var(--color-primary);\n    color: var(--color-primary);\n    background-color: var(--color-background-secondary);\n    transform: scale(1.05);\n    box-shadow: var(--shadow-md);\n  }\n\n  &:focus-visible {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 2px;\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n\n  .anticon {\n    font-size: 18px;\n    animation: "," 0.3s ease;\n\n    /* Ensure icon has sufficient contrast */\n    filter: contrast(1.1);\n  }\n\n  &.rotating .anticon {\n    animation: "," 0.5s ease;\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border-width: 2px;\n    font-weight: 600;\n\n    &:hover, &:focus {\n      border-width: 3px;\n    }\n\n    .anticon {\n      filter: contrast(1.3);\n    }\n  }\n\n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n\n    &:hover {\n      transform: none;\n    }\n\n    &:active {\n      transform: none;\n    }\n\n    .anticon {\n      animation: none;\n    }\n\n    &.rotating .anticon {\n      animation: none;\n    }\n  }\n"])),k,E),P=x.Ay.div(l||(l=(0,p.A)(["\n  min-width: 180px;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: 8px;\n  box-shadow: var(--shadow-lg);\n  padding: 4px;\n"]))),_=x.Ay.div(c||(c=(0,p.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 12px 16px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border-radius: 6px;\n  margin: 2px 0;\n  color: var(--color-text);\n\n  &:hover {\n    background-color: var(--color-background-secondary);\n    transform: translateX(2px);\n  }\n\n  &:focus {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 1px;\n  }\n\n  &.active {\n    background-color: var(--color-primary);\n    color: white;\n    box-shadow: var(--shadow-sm);\n  }\n\n  .option-content {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n  }\n\n  .anticon {\n    font-size: 16px;\n\n    /* Ensure icon contrast in active state */\n    filter: ",";\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border: 1px solid var(--color-border);\n\n    &:hover {\n      border-color: var(--color-primary);\n    }\n\n    &.active {\n      border: 2px solid white;\n    }\n  }\n\n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: background-color 0.2s ease;\n\n    &:hover {\n      transform: none;\n    }\n  }\n"])),(function(e){var n;return null!==(n=e.className)&&void 0!==n&&n.includes("active")?"none":"contrast(1.1)"})),O=x.Ay.span(s||(s=(0,p.A)(["\n  font-size: 14px;\n  font-weight: 500;\n  line-height: 1.2;\n"]))),T=x.Ay.div(d||(d=(0,p.A)(["\n  font-size: 12px;\n  color: ",";\n  margin-top: 2px;\n  line-height: 1.2;\n\n  /* Ensure description text meets contrast requirements */\n  opacity: ",";\n"])),(function(e){return e.active?"rgba(255, 255, 255, 0.8)":"var(--color-text-secondary)"}),(function(e){return e.active?.9:.8}));const D=function(e){var n=e.showDropdown,t=void 0===n||n,r=e.size,o=void 0===r?"default":r,a=(0,A.ZV)(),i=a.isDarkMode,l=a.themeMode,c=a.toggleDarkMode,s=a.setThemeMode,d=a.systemPrefersDark,p=(0,m.useState)(!1),h=(0,u.A)(p,2),x=h[0],E=h[1],k=function(e){E(!0),s(e),setTimeout((function(){return E(!1)}),500)},D=function(){return"system"===l?m.createElement(g.A,null):i?m.createElement(b.A,null):m.createElement(y.A,null)},M=function(){switch(l){case"light":return"Light mode";case"dark":return"Dark mode";case"system":return"System mode (".concat(d?"dark":"light",")");default:return"Toggle theme"}},I={items:[{key:"light",icon:m.createElement(y.A,null),label:"Light",description:"Light theme"},{key:"dark",icon:m.createElement(b.A,null),label:"Dark",description:"Dark theme"},{key:"system",icon:m.createElement(g.A,null),label:"System",description:"Follow system preference"}].map((function(e){return{key:e.key,label:m.createElement(_,{className:l===e.key?"active":"",onClick:function(){return k(e.key)},role:"menuitem",tabIndex:0,"aria-selected":l===e.key,onKeyDown:function(n){"Enter"!==n.key&&" "!==n.key||(n.preventDefault(),k(e.key))}},m.createElement("div",{className:"option-content"},e.icon,m.createElement("div",null,m.createElement(O,null,e.label),m.createElement(T,{active:l===e.key},e.description))),l===e.key&&m.createElement(w.A,{"aria-label":"Selected"}))}}))};return t?m.createElement(S,null,m.createElement(f.A,{menu:I,trigger:["click"],placement:"bottomRight",arrow:!0,dropdownRender:function(e){return m.createElement(P,{role:"menu","aria-label":"Theme selection menu"},e)},onOpenChange:function(e){e&&setTimeout((function(){var e=document.querySelector('[role="menuitem"]');e&&e.focus()}),100)}},m.createElement(v.A,{title:M(),placement:"bottom"},m.createElement(C,{type:"text",size:o,className:x?"rotating":"","aria-label":"Theme options menu. Current theme: ".concat(M()),"aria-haspopup":"menu","aria-expanded":"false",role:"button"},D())))):m.createElement(S,null,m.createElement(v.A,{title:M(),placement:"bottom"},m.createElement(C,{type:"text",size:o,className:x?"rotating":"",onClick:function(){E(!0),c(),setTimeout((function(){return E(!1)}),500)},"aria-label":"Switch to ".concat(i?"light":"dark"," mode. Current theme: ").concat(M()),"aria-pressed":i,role:"switch"},D())))}},7749:(e,n,t)=>{t.d(n,{Ex:()=>Ee,IO:()=>ye,K0:()=>me,VP:()=>ie,YM:()=>be,_x:()=>ne,aQ:()=>fe,cG:()=>Ae,cN:()=>ge,dd:()=>se,ee:()=>ce,eu:()=>ke,fI:()=>ae,gE:()=>he,hC:()=>xe,ih:()=>we,jn:()=>ue,mc:()=>re,pK:()=>de,sQ:()=>ve,sT:()=>ee,tA:()=>pe,tK:()=>te,wn:()=>oe,xA:()=>le});var r,o,a,i,l,c,s,d,u,p,m,h,v,f,g,b,y,w,x,A,E,k,S,C,P,_,O,T,D,M,I,j,z,L,R,F,B,N,W=t(7528),H=t(1250),U=t(3016),Q=t(677),V=t(9249),G=t(7355),K=t(7197),q=t(9029),J=t(6914),Y=U.A.Title,Z=U.A.Text,X=(U.A.Paragraph,(0,H.AH)(r||(r=(0,W.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n"])))),$=((0,H.AH)(o||(o=(0,W.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n"]))),(0,H.AH)(a||(a=(0,W.A)(["\n  display: flex;\n  flex-direction: column;\n"]))),(0,H.AH)(i||(i=(0,W.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: ","px;\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.md)||16}))),ee=(0,H.Ay)(Y)(l||(l=(0,W.A)(["\n  margin-bottom: ","px;\n  color: ",";\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.lg)||24}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.textPrimary)||"#111827"})),ne=(0,H.Ay)(Y)(c||(c=(0,W.A)(["\n  margin-bottom: ","px;\n  color: ",";\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.md)||16}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.textPrimary)||"#111827"})),te=(0,H.Ay)(Y)(s||(s=(0,W.A)(["\n  margin-bottom: ","px;\n  color: ",";\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.sm)||12}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.textSecondary)||"#4B5563"})),re=((0,H.Ay)(Z)(d||(d=(0,W.A)(["\n  background-color: ",";\n  padding: ","px ","px;\n  border-radius: ",";\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primaryLight)||"#DBEAFE"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.xs)||8}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.sm)||12}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.borderRadius)||void 0===n?void 0:n.sm)||"2px"})),(0,H.Ay)(Z)(u||(u=(0,W.A)(["\n  font-family: 'Courier New', Courier, monospace;\n  background-color: ",";\n  padding: ","px ","px;\n  border-radius: ",";\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.gray100)||"#F3F4F6"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.xs)||8}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.sm)||12}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.borderRadius)||void 0===n?void 0:n.sm)||"2px"})),H.Ay.div(p||(p=(0,W.A)(["\n  max-width: ",";\n  margin: 0 auto;\n  padding: ","px;\n"])),(function(e){return e.maxWidth||"1200px"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.md)||16}))),oe=H.Ay.section(m||(m=(0,W.A)(["\n  margin-bottom: ","px;\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.xl)||32})),ae=H.Ay.div(h||(h=(0,W.A)(["\n  display: flex;\n  flex-wrap: wrap;\n  margin: -","px;\n\n  & > * {\n    padding: ","px;\n  }\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.sm)||12}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.sm)||12})),ie=H.Ay.div(v||(v=(0,W.A)(["\n  flex: ",";\n\n  "," {\n    flex: ",";\n  }\n\n  "," {\n    flex: ",";\n  }\n"])),(function(e){return e.flex||"1"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.media)||void 0===n?void 0:n.md)||"@media (min-width: 768px)"}),(function(e){return e.flexMd||e.flex||"1"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.media)||void 0===n?void 0:n.lg)||"@media (min-width: 992px)"}),(function(e){return e.flexLg||e.flexMd||e.flex||"1"})),le=H.Ay.div(f||(f=(0,W.A)(["\n  ","\n"])),$),ce=(0,H.Ay)(Q.A)(g||(g=(0,W.A)(["\n  border-radius: ",";\n  box-shadow: ",";\n  margin-bottom: ","px;\n  background-color: ",";\n\n  .ant-card-head {\n    border-bottom: 1px solid ",";\n  }\n\n  .ant-card-head-title {\n    color: ",";\n  }\n\n  .ant-card-body {\n    color: ",";\n  }\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.borderRadius)||void 0===n?void 0:n.md)||"4px"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.shadows)||void 0===n?void 0:n.sm)||"0 1px 2px rgba(0, 0, 0, 0.05)"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.md)||16}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.backgroundSecondary)||"#FFFFFF"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.border)||"#D1D5DB"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.textPrimary)||"#111827"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.textPrimary)||"#111827"})),se=(0,H.Ay)(ce)(b||(b=(0,W.A)(["\n  height: 100%;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: ",";\n  }\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.shadows)||void 0===n?void 0:n.md)||"0 4px 6px rgba(0, 0, 0, 0.1)"})),de=(0,H.Ay)(ce)(y||(y=(0,W.A)(["\n  .ant-card-head {\n    background-color: ",";\n  }\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primaryLight)||"#DBEAFE"})),ue=(0,H.Ay)(V.Ay)(w||(w=(0,W.A)(["\n  &.ant-btn-primary {\n    background-color: ",";\n    border-color: ",";\n\n    &:hover, &:focus {\n      background-color: ",";\n      border-color: ",";\n    }\n  }\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primary)||"#2563EB"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primary)||"#2563EB"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primaryDark)||"#1E40AF"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primaryDark)||"#1E40AF"})),pe=(0,H.Ay)(V.Ay)(x||(x=(0,W.A)(["\n  &.ant-btn {\n    border-color: ",";\n    color: ",";\n\n    &:hover, &:focus {\n      border-color: ",";\n      color: ",";\n    }\n  }\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primary)||"#2563EB"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primary)||"#2563EB"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primaryDark)||"#1E40AF"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primaryDark)||"#1E40AF"})),me=(0,H.Ay)(V.Ay)(A||(A=(0,W.A)(["\n  &.ant-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    \n    .anticon {\n      font-size: ",";\n    }\n  }\n"])),(function(e){return"large"===e.size?"18px":"small"===e.size?"12px":"14px"})),he=H.Ay.div(E||(E=(0,W.A)(["\n  margin-bottom: ","px;\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.md)||16})),ve=(0,H.Ay)(G.A)(k||(k=(0,W.A)(["\n  &.ant-input {\n    border-radius: ",";\n    border-color: ",";\n\n    &:hover {\n      border-color: ",";\n    }\n\n    &:focus {\n      border-color: ",";\n      box-shadow: 0 0 0 2px ",";\n    }\n  }\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.borderRadius)||void 0===n?void 0:n.md)||"4px"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.border)||"#D1D5DB"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primary)||"#2563EB"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primary)||"#2563EB"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primaryLight)||"rgba(37, 99, 235, 0.2)"})),fe=(0,H.Ay)(G.A.TextArea)(S||(S=(0,W.A)(["\n  &.ant-input {\n    border-radius: ",";\n    border-color: ",";\n\n    &:hover {\n      border-color: ",";\n    }\n\n    &:focus {\n      border-color: ",";\n      box-shadow: 0 0 0 2px ",";\n    }\n  }\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.borderRadius)||void 0===n?void 0:n.md)||"4px"}),(function(e){var n,t;return null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colorPalette)&&void 0!==n&&n.border?e.theme.colorPalette.border:null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colors)&&void 0!==t&&null!==(t=t.neutral)&&void 0!==t&&t[300]?e.theme.colors.neutral[300]:"#D1D5DB"}),(function(e){var n,t,r;return null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colorPalette)&&void 0!==n&&n.primary?e.theme.colorPalette.primary:null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colors)&&void 0!==t&&null!==(t=t.primary)&&void 0!==t&&t.main?e.theme.colors.primary.main:null!==(r=e.theme)&&void 0!==r&&r.primaryColor?e.theme.primaryColor:"#2563EB"}),(function(e){var n,t,r;return null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colorPalette)&&void 0!==n&&n.primary?e.theme.colorPalette.primary:null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colors)&&void 0!==t&&null!==(t=t.primary)&&void 0!==t&&t.main?e.theme.colors.primary.main:null!==(r=e.theme)&&void 0!==r&&r.primaryColor?e.theme.primaryColor:"#2563EB"}),(function(e){var n,t;return null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colorPalette)&&void 0!==n&&n.primaryLight?e.theme.colorPalette.primaryLight:null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colors)&&void 0!==t&&null!==(t=t.primary)&&void 0!==t&&t.light?e.theme.colors.primary.light:"rgba(37, 99, 235, 0.2)"})),ge=(0,H.Ay)(K.A)(C||(C=(0,W.A)(["\n  &.ant-alert {\n    border-radius: ",";\n    margin-bottom: ","px;\n  }\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.borderRadius)||void 0===n?void 0:n.md)||"4px"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.md)||16})),be=H.Ay.div(P||(P=(0,W.A)(["\n  ","\n  min-height: ",";\n  width: 100%;\n"])),X,(function(e){return e.fullPage?"100vh":"200px"})),ye=(0,H.Ay)(q.A)(_||(_=(0,W.A)(["\n  .ant-spin-dot-item {\n    background-color: ",";\n  }\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primary)||"#2563EB"})),we=(0,H.Ay)(J.A)(O||(O=(0,W.A)(["\n  &.ant-tag {\n    border-radius: ",";\n    margin-right: ","px;\n    margin-bottom: ","px;\n  }\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.borderRadius)||void 0===n?void 0:n.sm)||"2px"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.xs)||8}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.xs)||8})),xe=(0,H.Ay)(we)(T||(T=(0,W.A)(["\n  &.ant-tag {\n    background-color: ",";\n\n    color: ",";\n\n    border-color: ",";\n  }\n"])),(function(e){var n,t,r,o;switch(e.status){case"success":return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.successLight)||"#D1FAE5";case"warning":return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.warningLight)||"#FEF3C7";case"error":return(null===(r=e.theme)||void 0===r||null===(r=r.colorPalette)||void 0===r?void 0:r.errorLight)||"#FEE2E2";default:return(null===(o=e.theme)||void 0===o||null===(o=o.colorPalette)||void 0===o?void 0:o.infoLight)||"#DBEAFE"}}),(function(e){var n,t,r,o;switch(e.status){case"success":return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.success)||"#10B981";case"warning":return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.warning)||"#FBBF24";case"error":return(null===(r=e.theme)||void 0===r||null===(r=r.colorPalette)||void 0===r?void 0:r.error)||"#DC2626";default:return(null===(o=e.theme)||void 0===o||null===(o=o.colorPalette)||void 0===o?void 0:o.info)||"#2563EB"}}),(function(e){var n,t,r,o;switch(e.status){case"success":return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.success)||"#10B981";case"warning":return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.warning)||"#FBBF24";case"error":return(null===(r=e.theme)||void 0===r||null===(r=r.colorPalette)||void 0===r?void 0:r.error)||"#DC2626";default:return(null===(o=e.theme)||void 0===o||null===(o=o.colorPalette)||void 0===o?void 0:o.info)||"#2563EB"}})),Ae=H.Ay.hr(D||(D=(0,W.A)(["\n  border: none;\n  border-top: 1px solid ",";\n  margin: ","px 0;\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.border)||"#D1D5DB"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.md)||16})),Ee=H.Ay.span(M||(M=(0,W.A)(["\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 20px;\n  height: 20px;\n  padding: 0 6px;\n  font-size: 12px;\n  line-height: 1;\n  border-radius: 10px;\n  background-color: ",";\n  color: white;\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primary)||"#2563EB"})),ke=H.Ay.div(I||(I=(0,W.A)(["\n  width: ",";\n  height: ",";\n  border-radius: 50%;\n  background-color: ",";\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: ","px;\n  overflow: hidden;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n  }\n"])),(function(e){return e.size||"40px"}),(function(e){return e.size||"40px"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primary)||"#2563EB"}),(function(e){return parseInt(e.size||"40",10)/2.5}));H.Ay.div(j||(j=(0,W.A)(["\n  padding: ","px ","px;\n  max-width: 300px;\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.xs)||8}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.sm)||12})),H.Ay.div(z||(z=(0,W.A)(["\n  display: flex;\n  justify-content: flex-end;\n  gap: ","px;\n  margin-top: ","px;\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.sm)||12}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.md)||16})),H.Ay.div(L||(L=(0,W.A)(["\n  overflow-x: auto;\n\n  .ant-table {\n    background-color: ",";\n  }\n\n  .ant-table-thead > tr > th {\n    background-color: ",";\n    color: ",";\n  }\n\n  .ant-table-tbody > tr > td {\n    border-bottom: 1px solid ",";\n  }\n\n  .ant-table-tbody > tr:hover > td {\n    background-color: ",";\n  }\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.backgroundSecondary)||"#FFFFFF"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.backgroundTertiary)||"#F9FAFB"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.textPrimary)||"#111827"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.border)||"#D1D5DB"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primaryLight)||"#DBEAFE"})),H.Ay.div(R||(R=(0,W.A)(["\n  display: flex;\n  align-items: center;\n  margin-bottom: ","px;\n\n  a {\n    color: ",";\n\n    &:hover {\n      color: ",";\n    }\n  }\n\n  span {\n    margin: 0 ","px;\n    color: ",";\n  }\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.md)||16}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.textSecondary)||"#4B5563"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.primary)||"#2563EB"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.spacing)||void 0===n?void 0:n.xs)||8}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.textSecondary)||"#4B5563"})),(0,H.AH)(F||(F=(0,W.A)(["\n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n    }\n    to {\n      opacity: 1;\n    }\n  }\n\n  animation: fadeIn "," ease-in-out;\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.animation)||void 0===n?void 0:n.normal)||"0.3s"})),(0,H.AH)(B||(B=(0,W.A)(["\n  @keyframes slideIn {\n    from {\n      transform: translateY(20px);\n      opacity: 0;\n    }\n    to {\n      transform: translateY(0);\n      opacity: 1;\n    }\n  }\n\n  animation: slideIn "," ease-in-out;\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.animation)||void 0===n?void 0:n.normal)||"0.3s"})),(0,H.AH)(N||(N=(0,W.A)(["\n  @keyframes pulse {\n    0% {\n      transform: scale(1);\n    }\n    50% {\n      transform: scale(1.05);\n    }\n    100% {\n      transform: scale(1);\n    }\n  }\n\n  animation: pulse "," ease-in-out infinite;\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.animation)||void 0===n?void 0:n.slow)||"0.5s"}))},8812:(e,n,t)=>{t.d(n,{O2:()=>s});var r,o=t(7528),a=t(6540),i=t(677),l=t(7072),c=(t(2702),(0,t(1250).Ay)(i.A)(r||(r=(0,o.A)(["\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n  margin-bottom: 16px;\n"])))),s=function(e){var n=e.cards,t=void 0===n?4:n;return a.createElement("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(300px, 1fr))",gap:24}},Array.from({length:t}).map((function(e,n){return a.createElement(c,{key:"dashboard-skeleton-card-".concat(n)},a.createElement(l.A,{active:!0,paragraph:{rows:2}}))})))}},9208:(e,n,t)=>{t.d(n,{A:()=>z});var r,o,a,i,l,c,s,d,u,p=t(5544),m=t(7528),h=t(6540),v=t(5448),f=t(3016),g=t(9249),b=t(1849),y=t(1372),w=t(9197),x=t(1250),A=t(2569),E=t(7683),k=v.A.Header,S=f.A.Title,C=(0,x.Ay)(k)(r||(r=(0,m.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 24px;\n  background-color: var(--color-surface);\n  border-bottom: 1px solid var(--color-border-light);\n  box-shadow: var(--shadow-md);\n  position: sticky;\n  top: 0;\n  z-index: var(--z-sticky);\n  transition: all 0.3s ease;\n  min-height: 64px;\n\n  /* Ensure proper contrast for header background */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: var(--color-surface);\n    opacity: 0.98;\n    z-index: -1;\n  }\n\n  /* Focus management for accessibility */\n  &:focus-within {\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: 0 16px;\n    min-height: 56px;\n  }\n\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n  }\n"]))),P=x.Ay.div(o||(o=(0,m.A)(["\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  z-index: 1;\n\n  /* Ensure proper focus styles for accessibility */\n  &:focus {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 2px;\n    border-radius: 4px;\n  }\n\n  &:hover {\n    transform: scale(1.02);\n  }\n\n  &:active {\n    transform: scale(0.98);\n  }\n\n  .logo-icon {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 40px;\n    height: 40px;\n    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));\n    border-radius: 8px;\n    color: white;\n    font-size: 20px;\n    box-shadow: var(--shadow-sm);\n    transition: all 0.3s ease;\n\n    /* Ensure icon contrast meets WCAG AA standards */\n    filter: contrast(1.1);\n\n    &:hover {\n      box-shadow: var(--shadow-md);\n      transform: translateY(-1px);\n    }\n  }\n\n  .logo-text {\n    color: var(--color-text);\n    margin: 0;\n    font-weight: 600;\n    font-size: 20px;\n    line-height: 1.2;\n\n    /* Ensure text contrast meets WCAG AA standards (4.5:1) */\n    text-shadow: 0 0 1px var(--color-background);\n\n    @media (max-width: 480px) {\n      display: none;\n    }\n\n    /* High contrast mode support */\n    @media (prefers-contrast: high) {\n      font-weight: 700;\n      text-shadow: 1px 1px 2px var(--color-background);\n    }\n  }\n\n  /* Keyboard navigation support */\n  &[tabindex] {\n    border-radius: 4px;\n  }\n\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n\n    &:hover {\n      transform: none;\n    }\n  }\n"]))),_=x.Ay.div(a||(a=(0,m.A)(["\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  position: relative;\n  z-index: 1;\n\n  @media (max-width: 768px) {\n    gap: 8px;\n  }\n"]))),O=(0,x.Ay)(g.Ay)(i||(i=(0,m.A)(["\n  display: none;\n\n  @media (max-width: 768px) {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 40px;\n    height: 40px;\n    border-radius: 8px;\n    border: 1px solid var(--color-border);\n    background-color: var(--color-surface);\n    color: var(--color-text);\n    transition: all 0.3s ease;\n\n    /* Ensure button meets WCAG contrast requirements */\n    &:hover, &:focus {\n      border-color: var(--color-primary);\n      color: var(--color-primary);\n      background-color: var(--color-background-secondary);\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &:active {\n      transform: scale(0.95);\n    }\n\n    /* High contrast mode support */\n    @media (prefers-contrast: high) {\n      border-width: 2px;\n      font-weight: 600;\n    }\n\n    /* Keyboard focus indicator */\n    &:focus-visible {\n      outline: 2px solid var(--color-primary);\n      outline-offset: 2px;\n    }\n  }\n"]))),T=x.Ay.div(l||(l=(0,m.A)(["\n  display: flex;\n  align-items: center;\n  gap: 16px;\n\n  @media (max-width: 768px) {\n    display: none;\n  }\n"]))),D=x.Ay.div(c||(c=(0,m.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  border-radius: 20px;\n  background-color: var(--color-background-secondary);\n  border: 1px solid var(--color-border-light);\n  font-size: 12px;\n  color: var(--color-text);\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  /* Ensure status text meets WCAG AA contrast ratio (4.5:1) */\n  text-shadow: 0 0 1px var(--color-background-secondary);\n\n  /* Enhanced visual hierarchy */\n  box-shadow: var(--shadow-sm);\n\n  &:hover {\n    background-color: var(--color-background-tertiary);\n    box-shadow: var(--shadow-md);\n  }\n\n  .status-dot {\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background-color: var(--color-success);\n    box-shadow: 0 0 4px var(--color-success);\n    animation: pulse 2s infinite;\n    position: relative;\n\n    /* Add a subtle glow for better visibility */\n    &::after {\n      content: '';\n      position: absolute;\n      top: -2px;\n      left: -2px;\n      right: -2px;\n      bottom: -2px;\n      border-radius: 50%;\n      background-color: var(--color-success);\n      opacity: 0.3;\n      animation: pulse 2s infinite;\n    }\n  }\n\n  /* High contrast mode adjustments */\n  @media (prefers-contrast: high) {\n    border-width: 2px;\n    font-weight: 600;\n    background-color: var(--color-surface);\n\n    .status-dot {\n      border: 2px solid var(--color-text);\n    }\n  }\n\n  @keyframes pulse {\n    0% {\n      opacity: 1;\n      transform: scale(1);\n    }\n    50% {\n      opacity: 0.7;\n      transform: scale(1.1);\n    }\n    100% {\n      opacity: 1;\n      transform: scale(1);\n    }\n  }\n\n  @media (prefers-reduced-motion: reduce) {\n    .status-dot {\n      animation: none;\n\n      &::after {\n        animation: none;\n      }\n    }\n  }\n"]))),M=(0,x.Ay)(b.A)(s||(s=(0,m.A)(["\n  .ant-drawer-content {\n    background-color: var(--color-surface);\n    border-left: 1px solid var(--color-border-light);\n  }\n\n  .ant-drawer-header {\n    background-color: var(--color-surface);\n    border-bottom: 1px solid var(--color-border-light);\n    padding: 16px 24px;\n\n    .ant-drawer-title {\n      color: var(--color-text);\n      font-weight: 600;\n      font-size: 18px;\n    }\n\n    .ant-drawer-close {\n      color: var(--color-text-secondary);\n      transition: all 0.3s ease;\n\n      &:hover {\n        color: var(--color-primary);\n        background-color: var(--color-background-secondary);\n      }\n\n      &:focus {\n        outline: 2px solid var(--color-primary);\n        outline-offset: 2px;\n      }\n    }\n  }\n\n  .ant-drawer-body {\n    padding: 24px;\n    background-color: var(--color-surface);\n  }\n\n  /* Ensure proper contrast for drawer overlay */\n  .ant-drawer-mask {\n    background-color: rgba(0, 0, 0, 0.45);\n    backdrop-filter: blur(4px);\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    .ant-drawer-content {\n      border-left-width: 3px;\n    }\n\n    .ant-drawer-header {\n      border-bottom-width: 2px;\n    }\n  }\n"]))),I=x.Ay.div(d||(d=(0,m.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n"]))),j=x.Ay.div(u||(u=(0,m.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  padding: 16px;\n  border-radius: 8px;\n  background-color: var(--color-background-secondary);\n  border: 1px solid var(--color-border-light);\n  transition: all 0.3s ease;\n\n  &:hover {\n    background-color: var(--color-background-tertiary);\n    box-shadow: var(--shadow-sm);\n  }\n\n  .section-title {\n    font-size: 16px;\n    font-weight: 600;\n    color: var(--color-text);\n    margin-bottom: 8px;\n\n    /* Ensure title text meets WCAG AA contrast */\n    text-shadow: 0 0 1px var(--color-background-secondary);\n  }\n\n  /* High contrast mode adjustments */\n  @media (prefers-contrast: high) {\n    border-width: 2px;\n\n    .section-title {\n      font-weight: 700;\n      text-shadow: 1px 1px 2px var(--color-background);\n    }\n  }\n"])));const z=function(e){var n=e.title,t=void 0===n?"App Builder 201":n,r=e.showStatus,o=void 0===r||r,a=e.onLogoClick,i=e.children,l=(0,A.ZV)(),c=l.isDarkMode,s=l.themeMode,d=(0,h.useState)(!1),u=(0,p.A)(d,2),m=u[0],v=u[1],f=function(e){"keydown"===e.type&&"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),a?a():window.scrollTo({top:0,behavior:"smooth"}))},g=function(){v(!1)},b=function(e){"Escape"===e.key&&m&&g()};return h.useEffect((function(){return document.addEventListener("keydown",b),function(){document.removeEventListener("keydown",b)}}),[m]),h.createElement(h.Fragment,null,h.createElement(C,{role:"banner","aria-label":"Main navigation"},h.createElement(P,{onClick:f,onKeyDown:f,tabIndex:0,role:"button","aria-label":"".concat(t," - Go to homepage")},h.createElement("div",{className:"logo-icon","aria-hidden":"true"},h.createElement(y.A,null)),h.createElement(S,{level:4,className:"logo-text","aria-hidden":"true"},t)),h.createElement(_,{role:"toolbar","aria-label":"Header actions"},h.createElement(T,null,o&&h.createElement(D,{role:"status","aria-label":"Application status: Online",title:"Application is online and ready"},h.createElement("div",{className:"status-dot","aria-hidden":"true"}),h.createElement("span",null,"Online")),h.createElement("div",{role:"group","aria-label":"Theme controls"},h.createElement(E.A,null)),i&&h.createElement("div",{role:"group","aria-label":"Additional actions"},i)),h.createElement(O,{type:"text",icon:h.createElement(w.A,null),onClick:function(){v(!m)},"aria-label":"".concat(m?"Close":"Open"," mobile menu"),"aria-expanded":m,"aria-controls":"mobile-navigation-drawer"}))),h.createElement(M,{title:"Navigation Menu",placement:"right",onClose:g,open:m,width:280,id:"mobile-navigation-drawer","aria-label":"Mobile navigation menu",closeIcon:h.createElement("span",{"aria-label":"Close menu"},"×"),maskClosable:!0,keyboard:!0,destroyOnClose:!1},h.createElement(I,{role:"navigation","aria-label":"Mobile menu content"},h.createElement(j,null,h.createElement("div",{className:"section-title",id:"theme-section"},"Theme Settings"),h.createElement("div",{role:"group","aria-labelledby":"theme-section"},h.createElement(E.A,{showDropdown:!1}),h.createElement("div",{style:{marginTop:"8px",fontSize:"12px",color:"var(--color-text-secondary)"}},"Current: ","system"===s?"System (".concat(c?"Dark":"Light",")"):"dark"===s?"Dark":"Light"))),o&&h.createElement(j,null,h.createElement("div",{className:"section-title",id:"status-section"},"Application Status"),h.createElement("div",{role:"group","aria-labelledby":"status-section"},h.createElement(D,{role:"status","aria-label":"Application status: Online"},h.createElement("div",{className:"status-dot","aria-hidden":"true"}),h.createElement("span",null,"Online")))),i&&h.createElement(j,null,h.createElement("div",{className:"section-title",id:"actions-section"},"Additional Actions"),h.createElement("div",{role:"group","aria-labelledby":"actions-section"},i)))))}},9459:(e,n,t)=>{t.d(n,{A:()=>u});var r=t(436),o=t(4467),a=t(5544),i=t(6540),l=t(1468),c=t(2543);function s(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function d(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?s(Object(t),!0).forEach((function(n){(0,o.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):s(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}const u=function(e){var n=e.components,t=void 0===n?[]:n,o=e.onUpdateComponent,s=e.onAddComponent,u=e.onDeleteComponent,p=e.websocketService,m=e.updateDelay,h=void 0===m?300:m,v=e.throttleDelay,f=void 0===v?100:v,g=e.enableWebSocket,b=void 0===g||g,y=(0,i.useState)(!1),w=(0,a.A)(y,2),x=w[0],A=w[1],E=(0,i.useState)(null),k=(0,a.A)(E,2),S=k[0],C=k[1],P=(0,i.useState)(new Map),_=(0,a.A)(P,2),O=_[0],T=_[1],D=(0,i.useState)([]),M=(0,a.A)(D,2),I=M[0],j=M[1],z=(0,i.useRef)(null),L=(0,i.useRef)(p),R=(0,i.useRef)(new Map),F=((0,l.wA)(),(0,l.d4)((function(e){var n;return(null===(n=e.websocket)||void 0===n?void 0:n.connected)||!1})));(0,i.useEffect)((function(){L.current=p}),[p]);var B=(0,i.useCallback)((0,c.debounce)((function(e){0!==e.length&&(A(!0),e.forEach((function(e){var n=e.type,t=e.componentId,r=e.data;switch(n){case"update":o&&o(t,r);break;case"add":s&&s(r);break;case"delete":u&&u(t);break;default:console.warn("Unknown update type:",n)}})),b&&F&&L.current&&e.forEach((function(e){var n=e.type,t=e.componentId,r=e.data;L.current.send({type:"component_".concat(n),component_id:t,component_data:r,timestamp:(new Date).toISOString()})})),C(new Date),j([]),T(new Map),setTimeout((function(){return A(!1)}),500))}),h),[o,s,u,b,F,h]),N=(0,i.useCallback)((0,c.throttle)((function(e,n){var t=R.current.get(e)||{};R.current.set(e,d(d({},t),n)),C(new Date)}),f),[f]),W=(0,i.useCallback)((function(e,n){var t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(e){var o=d(d({},O.get(e)||{}),n);T((function(n){return new Map(n.set(e,o))})),j((function(n){return[].concat((0,r.A)(n.filter((function(n){return!("update"===n.type&&n.componentId===e)}))),[{type:"update",componentId:e,data:o}])})),t&&N(e,n),B(I)}}),[O,I,B,N]),H=(0,i.useCallback)((function(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=e.id||Date.now().toString(),o=d(d({},e),{},{id:t});return j((function(e){return[].concat((0,r.A)(e),[{type:"add",componentId:t,data:o}])})),n&&(R.current.set(t,o),C(new Date)),B(I),t}),[I,B]),U=(0,i.useCallback)((function(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e&&(j((function(n){return[].concat((0,r.A)(n),[{type:"delete",componentId:e}])})),n&&(R.current.delete(e),C(new Date)),B(I))}),[I,B]),Q=(0,i.useCallback)((function(e){var n=t.find((function(n){return n.id===e})),r=R.current.get(e),o=O.get(e);return d(d(d({},n),r),o)}),[t,O]),V=(0,i.useCallback)((function(){return t.map((function(e){return Q(e.id)}))}),[t,Q]),G=(0,i.useCallback)((function(){B.flush(),R.current.clear(),T(new Map),j([])}),[B]);return(0,i.useEffect)((function(){if(b&&L.current){var e=function(e){var n;if(null!==(n=e.type)&&void 0!==n&&n.startsWith("component_")){var t=e.component_id,r=e.component_data,o=e.timestamp;r&&t&&(R.current.set(t,r),C(new Date(o)))}};return L.current.addEventListener("message",e),function(){L.current&&L.current.removeEventListener("message",e)}}}),[b]),(0,i.useEffect)((function(){return function(){z.current&&clearTimeout(z.current),B.cancel(),N.cancel()}}),[B,N]),{isUpdating:x,lastUpdateTime:S,websocketConnected:F,hasPendingUpdates:O.size>0,updateComponent:W,addComponent:H,deleteComponent:U,getComponent:Q,getAllComponents:V,forceUpdate:G,clearCache:function(){return R.current.clear()},getPendingUpdates:function(){return Array.from(O.entries())},getUpdateQueueSize:function(){return I.length}}}}}]);