"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[6288],{16918:(e,t,n)=>{n.d(t,{$n:()=>r.Ay,$x:()=>c.A,B8:()=>h.A,Fc:()=>s.A,Zp:()=>o.A,cG:()=>l.A,fI:()=>a.fI,fv:()=>a.fv,jL:()=>v.A,m_:()=>u.A,ms:()=>m.A,o5:()=>i.A,pd:()=>f.A,tK:()=>d.A,vw:()=>p.A});var r=n(49103),o=n(677),i=n(75475),a=(n(45448),n(87206),n(36768)),c=n(28392),l=n(36552),s=n(27197),d=n(29029),u=(n(49222),n(1849),n(37977)),p=(n(28073),n(52120),n(67034)),m=(n(81427),n(88603)),h=(n(12075),n(39356),n(42652)),v=(n(42729),n(44485),n(94431),n(53515),n(6754),n(37122)),f=(n(17308),n(14378),n(97072),n(53308),n(18719),n(8787),n(28792),n(17355));n(36492),n(91196),n(50770),n(15039),n(6531),n(1285),n(77829),n(95082),n(15622),n(80296),n(93438),n(21102),n(1062),n(87937),n(40248),n(62070),n(88845),n(55957),n(33835),n(87959),n(76511),n(16044),n(75245),n(38674),n(58545),Layout,Layout.Header,Layout.Content,Layout.Footer,Layout.Sider,Typography,Typography.Title,Typography.Text,Typography.Paragraph,Typography.Link,Row,Col,Form,Form.Item,Form.List,Input,Input.TextArea,Input.Password,Input.Search,Select,Select.Option,Select.OptGroup},17177:(e,t,n)=>{n.r(t),n.d(t,{ConnectionState:()=>l,default:()=>s});var r=n(60436),o=n(64467),i=n(23029),a=n(92901);function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var l={CONNECTING:0,OPEN:1,CLOSING:2,CLOSED:3,RECONNECTING:4};const s=function(){return(0,a.A)((function e(t){(0,i.A)(this,e),this.options=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({url:"",autoConnect:!0,autoReconnect:!0,reconnectInterval:1e3,maxReconnectInterval:3e4,reconnectDecay:1.5,maxReconnectAttempts:1/0,connectionTimeout:5e3,heartbeatInterval:3e4,debug:!1,batchInterval:50,maxBatchSize:100,enableCompression:!0,compressionThreshold:1024,prioritizeUrgentMessages:!0,maxOfflineQueueSize:1e3,persistOfflineMessages:!1,persistenceKey:"enhanced_ws_offline_queue",protocols:null},t),this.ws=null,this.connectionState=l.CLOSED,this.reconnectAttempts=0,this.currentReconnectInterval=this.options.reconnectInterval,this.reconnectTimer=null,this.connectionTimeoutTimer=null,this.heartbeatTimer=null,this.missedHeartbeats=0,this.maxMissedHeartbeats=3,this.messageQueue=[],this.batchTimer=null,this.offlineQueue=[],this.isOnline=navigator.onLine,this.eventListeners={open:[],close:[],error:[],message:[],reconnect:[],reconnect_attempt:[],reconnect_failed:[]},this._handleOpen=this._handleOpen.bind(this),this._handleClose=this._handleClose.bind(this),this._handleError=this._handleError.bind(this),this._handleMessage=this._handleMessage.bind(this),this._handleOnline=this._handleOnline.bind(this),this._handleOffline=this._handleOffline.bind(this),window.addEventListener("online",this._handleOnline),window.addEventListener("offline",this._handleOffline),this.options.persistOfflineMessages&&this._loadPersistedMessages(),this.options.autoConnect&&this.open()}),[{key:"open",value:function(){var e=this;return new Promise((function(t,n){if(e.connectionState===l.CONNECTING||e.connectionState===l.OPEN)return e._debug("WebSocket already connecting or open"),void(e.connectionState===l.OPEN&&t());e.close(),e.connectionState=l.CONNECTING;try{e._debug("Opening WebSocket connection to ".concat(e.options.url)),e.options.protocols?e.ws=new WebSocket(e.options.url,e.options.protocols):e.ws=new WebSocket(e.options.url),e.ws.addEventListener("open",(function(n){e._debug("Connection successful"),e._handleOpen(n),t()}),{once:!0}),e.ws.addEventListener("error",(function(t){var r=new Error("WebSocket connection error");r.originalEvent=t,e._handleError(t),n(r)}),{once:!0}),e.ws.addEventListener("close",(function(t){if(e.connectionState===l.CONNECTING){var r=new Error("WebSocket connection closed during connection attempt: ".concat(t.code," ").concat(t.reason));r.closeEvent=t,n(r)}}),{once:!0}),e.ws.onopen=e._handleOpen,e.ws.onclose=e._handleClose,e.ws.onerror=e._handleError,e.ws.onmessage=e._handleMessage,e.connectionTimeoutTimer=setTimeout((function(){if(e.connectionState===l.CONNECTING){e._debug("Connection timeout");var t=new Error("WebSocket connection timeout after ".concat(e.options.connectionTimeout,"ms"));e.ws.close(),n(t)}}),e.options.connectionTimeout)}catch(t){e._debug("Error creating WebSocket:",t),e._handleError(t),n(t)}}))}},{key:"close",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(this._clearTimers(),this.ws){this.connectionState=l.CLOSING;try{this.ws.close(e,t)}catch(e){this._debug("Error closing WebSocket:",e)}}this.connectionState=l.CLOSED}},{key:"send",value:function(e){var t=this,n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=r.compress,i=void 0===o||o,a=r.urgent;return void 0!==a&&a&&this.connectionState===l.OPEN?this._sendImmediate(e,i):this.connectionState!==l.OPEN?(this._debug("WebSocket not open, queueing message"),this.offlineQueue.push({data:e,options:r}),!1):n&&this.options.batchInterval>0?(this.messageQueue.push({data:e,options:r}),this.batchTimer||(this.batchTimer=setTimeout((function(){return t._processBatch()}),this.options.batchInterval)),this.messageQueue.length>=this.options.maxBatchSize&&this._processBatch(),!0):this._sendImmediate(e,i)}},{key:"_sendImmediate",value:function(e,t){try{var n;if("string"==typeof e)n=e;else try{n=JSON.stringify(e)}catch(e){return this._debug("Error stringifying message:",e),this._handleError(e),!1}return t&&n.length>1024&&(this._debug("Compressing large message (".concat(n.length," bytes)")),n=JSON.stringify({type:"compressed",data:n,originalSize:n.length})),this.ws.send(n),!0}catch(e){return this._debug("Error sending message:",e),this._handleError(e),!1}}},{key:"addEventListener",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.eventListeners[e]||(this.eventListeners[e]=[]),this.eventListeners[e].push({callback:t,options:n}),this._debug("Added event listener for ".concat(e))}},{key:"removeEventListener",value:function(e,t){this.eventListeners[e]&&(this.eventListeners[e]=this.eventListeners[e].filter((function(e){return e.callback!==t})),this._debug("Removed event listener for ".concat(e)))}},{key:"_dispatchEvent",value:function(e,t){var n=this;this.eventListeners[e]&&this.eventListeners[e].forEach((function(r){try{r.callback(t),r.options.once&&n.removeEventListener(e,r.callback)}catch(t){n._debug("Error in ".concat(e," listener:"),t)}}))}},{key:"_handleOpen",value:function(e){this._debug("WebSocket connection opened"),clearTimeout(this.connectionTimeoutTimer),this.connectionState=l.OPEN,this.reconnectAttempts=0,this.currentReconnectInterval=this.options.reconnectInterval,this._startHeartbeat(),this._processOfflineQueue(),this._dispatchEvent("open",e)}},{key:"_handleClose",value:function(e){var t=this;this._debug("WebSocket connection closed: ".concat(e.code," ").concat(e.reason)),this._clearTimers();var n={code:e.code,reason:e.reason,wasClean:e.wasClean,timestamp:(new Date).toISOString()};this.connectionState=l.CLOSED;var r=this._handleCloseCode(e.code,e.reason);this.options.autoReconnect&&r&&setTimeout((function(){t._reconnect().catch((function(e){t._debug("Reconnection chain failed:",e)}))}),100),this._dispatchEvent("close",{originalEvent:e,closeInfo:n,willReconnect:this.options.autoReconnect&&r})}},{key:"_handleCloseCode",value:function(e,t){return 1e3===e?(this._debug("Normal closure, not reconnecting"),!1):1001===e?(this._debug("Server going away, will attempt reconnect"),!0):1002===e?(this._debug("Protocol error, will attempt reconnect with delay"),this.currentReconnectInterval=Math.max(2*this.currentReconnectInterval,this.options.reconnectInterval),!0):1003===e?(this._debug("Unsupported data format, will attempt reconnect"),!0):1005===e?(this._debug("No status code provided, will attempt reconnect"),!0):1006===e?(this._debug("Abnormal closure, will attempt reconnect"),!0):1007===e?(this._debug("Invalid frame payload data, will attempt reconnect"),!0):1008===e?(this._debug("Policy violation, will attempt reconnect with increased delay"),this.currentReconnectInterval=Math.max(3*this.currentReconnectInterval,this.options.reconnectInterval),!0):1009===e?(this._debug("Message too big, will attempt reconnect"),!0):1010===e?(this._debug("Missing extension, will attempt reconnect"),!0):1011===e?(this._debug("Server internal error, will attempt reconnect with delay"),this.currentReconnectInterval=Math.max(2*this.currentReconnectInterval,this.options.reconnectInterval),!0):1012===e?(this._debug("Service restart, will attempt reconnect"),!0):1013===e?(this._debug("Service unavailable, will attempt reconnect with increased delay"),this.currentReconnectInterval=Math.max(4*this.currentReconnectInterval,this.options.reconnectInterval),!0):4e3===e?(this._debug("Heartbeat timeout, will attempt reconnect"),!0):(this._debug("Unknown close code ".concat(e,", will attempt reconnect")),!0)}},{key:"_handleError",value:function(e){this._debug("WebSocket error:",e);var t={originalEvent:e,message:"WebSocket connection error",timestamp:(new Date).toISOString(),connectionState:this.connectionState,reconnectAttempts:this.reconnectAttempts,url:this.options.url};this._dispatchEvent("error",t)}},{key:"_handleMessage",value:function(e){var t=this,n=e.data;try{if("string"==typeof n&&(n.startsWith("{")||n.startsWith("["))&&(n=JSON.parse(n)),"pong"===n||n&&"pong"===n.type)return void this._handleHeartbeatResponse();if(n&&"compressed"===n.type&&(this._debug("Received compressed message (".concat(n.originalSize," bytes)")),"string"==typeof(n=n.data)&&(n.startsWith("{")||n.startsWith("["))))try{n=JSON.parse(n)}catch(e){this._debug("Error parsing decompressed data:",e)}if(n&&"batch"===n.type){this._debug("Received batch message with ".concat(n.count," messages"));var r=function(n){Array.isArray(n)&&n.forEach((function(n){try{var r=n;"string"==typeof n&&(n.startsWith("{")||n.startsWith("["))&&(r=JSON.parse(n)),t._dispatchEvent("message",{data:r,originalEvent:e,isBatchItem:!0})}catch(e){t._debug("Error processing batch item:",e)}}))};return void(n.messages?(n.messages.uncompressed&&r(n.messages.uncompressed),n.messages.compressed&&r(n.messages.compressed)):Array.isArray(n.messages)&&r(n.messages))}this._dispatchEvent("message",{data:n,originalEvent:e})}catch(t){this._debug("Error parsing message:",t),this._dispatchEvent("message",{data:e.data,originalEvent:e})}}},{key:"_handleOnline",value:function(){this._debug("Browser went online"),this.isOnline=!0,this.connectionState!==l.OPEN&&this.options.autoReconnect&&this._reconnect()}},{key:"_handleOffline",value:function(){this._debug("Browser went offline"),this.isOnline=!1,this.options.persistOfflineMessages&&this.offlineQueue.length>0&&this._persistOfflineMessages()}},{key:"_reconnect",value:function(){var e=this;return new Promise((function(t,n){if(e.connectionState!==l.RECONNECTING){if(e.reconnectAttempts>=e.options.maxReconnectAttempts){e._debug("Max reconnect attempts reached");var r=new Error("Maximum reconnect attempts (".concat(e.options.maxReconnectAttempts,") reached"));return e._dispatchEvent("reconnect_failed",{attempts:e.reconnectAttempts,error:r}),void n(r)}if(navigator.onLine){e.connectionState=l.RECONNECTING,e.reconnectAttempts++;var o=.3*Math.random()+.85,i=Math.min(e.currentReconnectInterval*e.options.reconnectDecay*o,e.options.maxReconnectInterval);e.currentReconnectInterval=i,e._debug("Reconnecting in ".concat(Math.round(i),"ms (attempt ").concat(e.reconnectAttempts,"/").concat(e.options.maxReconnectAttempts,")")),e._dispatchEvent("reconnect_attempt",{attempt:e.reconnectAttempts,interval:i,maxAttempts:e.options.maxReconnectAttempts}),e.reconnectTimer=setTimeout((function(){e._debug("Attempting reconnect (".concat(e.reconnectAttempts,"/").concat(e.options.maxReconnectAttempts,")")),e.open().then((function(){e._debug("Reconnection successful"),e._dispatchEvent("reconnect",{attempt:e.reconnectAttempts,success:!0}),t()})).catch((function(r){e._debug("Reconnection failed:",r),e._dispatchEvent("reconnect",{attempt:e.reconnectAttempts,success:!1,error:r}),e.reconnectAttempts<e.options.maxReconnectAttempts?e._reconnect().then(t).catch(n):n(r)}))}),i)}else{e._debug("Browser is offline, waiting for online event");var a=new Error("Cannot reconnect while offline");n(a)}}else e._debug("Already attempting to reconnect")}))}},{key:"_startHeartbeat",value:function(){var e=this;this._clearHeartbeat(),this.options.heartbeatInterval<=0||(this._debug("Starting heartbeat (interval: ".concat(this.options.heartbeatInterval,"ms)")),this.missedHeartbeats=0,this.heartbeatTimer=setInterval((function(){e._sendHeartbeat()}),this.options.heartbeatInterval))}},{key:"_sendHeartbeat",value:function(){if(this.connectionState===l.OPEN){if(this._debug("Sending heartbeat"),this.missedHeartbeats++,this.missedHeartbeats>=this.maxMissedHeartbeats)return this._debug("Too many missed heartbeats (".concat(this.missedHeartbeats,"), closing connection")),void this.close(4e3,"Heartbeat timeout");try{this.ws.send(JSON.stringify({type:"ping",timestamp:Date.now()}))}catch(e){this._debug("Error sending heartbeat:",e)}}}},{key:"_handleHeartbeatResponse",value:function(){this._debug("Received heartbeat response"),this.missedHeartbeats=0}},{key:"_clearHeartbeat",value:function(){this.heartbeatTimer&&(clearInterval(this.heartbeatTimer),this.heartbeatTimer=null)}},{key:"_processBatch",value:function(){if(clearTimeout(this.batchTimer),this.batchTimer=null,0!==this.messageQueue.length){var e;if(this.connectionState!==l.OPEN)return(e=this.offlineQueue).push.apply(e,(0,r.A)(this.messageQueue)),void(this.messageQueue=[]);this._debug("Processing batch of ".concat(this.messageQueue.length," messages"));var t=[],n=[];this.messageQueue.forEach((function(e){var r=e.data,o=e.options,i=(void 0===o?{}:o).compress,a=void 0===i||i,c="string"==typeof r?r:JSON.stringify(r);a?t.push(c):n.push(c)}));var o={type:"batch",compressed:t.length>0,messages:{compressed:t,uncompressed:n},count:t.length+n.length,timestamp:Date.now()},i=(0,r.A)(this.messageQueue);this.messageQueue=[];try{this.ws.send(JSON.stringify(o))}catch(e){var a;this._debug("Error sending batch:",e),this._handleError(e),(a=this.offlineQueue).push.apply(a,(0,r.A)(i))}}}},{key:"_processOfflineQueue",value:function(){var e=this;if(0!==this.offlineQueue.length){this._debug("Processing offline queue of ".concat(this.offlineQueue.length," messages"));var t,n=(0,r.A)(this.offlineQueue).sort((function(e,t){var n,r,o=(null===(n=e.options)||void 0===n?void 0:n.urgent)||!1;return((null===(r=t.options)||void 0===r?void 0:r.urgent)||!1)-o})),o=n.filter((function(e){var t;return null===(t=e.options)||void 0===t?void 0:t.urgent})),i=n.filter((function(e){var t;return!(null!==(t=e.options)&&void 0!==t&&t.urgent)}));this.offlineQueue=[],o.forEach((function(t){var n=t.data,r=t.options,o=(void 0===r?{}:r).compress,i=void 0===o||o;e._sendImmediate(n,i)})),i.length>0&&((t=this.messageQueue).push.apply(t,(0,r.A)(i)),this._processBatch())}}},{key:"_clearTimers",value:function(){this.connectionTimeoutTimer&&(clearTimeout(this.connectionTimeoutTimer),this.connectionTimeoutTimer=null),this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this._clearHeartbeat(),this.batchTimer&&(clearTimeout(this.batchTimer),this.batchTimer=null)}},{key:"_debug",value:function(){if(this.options.debug){for(var e,t=new Date,n=t.getFullYear(),r=String(t.getMonth()+1).padStart(2,"0"),o=String(t.getDate()).padStart(2,"0"),i=String(t.getHours()).padStart(2,"0"),a=String(t.getMinutes()).padStart(2,"0"),c=String(t.getSeconds()).padStart(2,"0"),l=String(t.getMilliseconds()).padStart(3,"0"),s="".concat(n,"-").concat(r,"-").concat(o," ").concat(i,":").concat(a,":").concat(c,".").concat(l),d=arguments.length,u=new Array(d),p=0;p<d;p++)u[p]=arguments[p];(e=console).log.apply(e,["[".concat(s,"][EnhancedWebSocketClient]")].concat(u))}}},{key:"_persistOfflineMessages",value:function(){if(this.options.persistOfflineMessages&&0!==this.offlineQueue.length)try{var e=JSON.stringify(this.offlineQueue);localStorage.setItem(this.options.persistenceKey,e),this._debug("Persisted ".concat(this.offlineQueue.length," offline messages to localStorage"))}catch(e){this._debug("Error persisting offline messages:",e)}}},{key:"_loadPersistedMessages",value:function(){if(this.options.persistOfflineMessages)try{var e=localStorage.getItem(this.options.persistenceKey);if(!e)return;var t,n=JSON.parse(e);Array.isArray(n)&&n.length>0&&((t=this.offlineQueue).push.apply(t,(0,r.A)(n)),this.offlineQueue.length>this.options.maxOfflineQueueSize&&(this.offlineQueue=this.offlineQueue.slice(-this.options.maxOfflineQueueSize)),this._debug("Loaded ".concat(n.length," persisted offline messages")),localStorage.removeItem(this.options.persistenceKey))}catch(e){this._debug("Error loading persisted offline messages:",e)}}},{key:"destroy",value:function(){this._debug("Destroying WebSocket client"),this.options.persistOfflineMessages&&this.offlineQueue.length>0&&this._persistOfflineMessages(),this.close(),this._clearTimers(),window.removeEventListener("online",this._handleOnline),window.removeEventListener("offline",this._handleOffline),this.eventListeners={},this.messageQueue=[],this.offlineQueue=[]}}])}()},34816:(e,t,n)=>{n.r(t),n.d(t,{addComponent:()=>o,addLayout:()=>c,addTheme:()=>d,default:()=>w,removeComponent:()=>a,removeLayout:()=>s,removeTheme:()=>p,setActiveTheme:()=>m,setCurrentView:()=>b,togglePreviewMode:()=>y,toggleSidebar:()=>g,updateComponent:()=>i,updateLayout:()=>l,updateTheme:()=>u,websocketConnected:()=>h,websocketDisconnected:()=>v,websocketMessageReceived:()=>f});var r=n(4318),o=function(e){return{type:r.oz||"ADD_COMPONENT",payload:e}},i=function(e,t){return{type:r.ei||"UPDATE_COMPONENT",payload:{id:e,props:t}}},a=function(e){return{type:r.xS||"REMOVE_COMPONENT",payload:{id:e}}},c=function(e){return{type:r.vs||"ADD_LAYOUT",payload:e}},l=function(e,t){return{type:r.Pe||"UPDATE_LAYOUT",payload:{id:e,props:t}}},s=function(e){return{type:r.gV||"REMOVE_LAYOUT",payload:{id:e}}},d=function(e){return{type:r.U_||"ADD_THEME",payload:e}},u=function(e){return{type:r.gk||"UPDATE_THEME",payload:e}},p=function(e){return{type:r.D||"REMOVE_THEME",payload:{id:e}}},m=function(e){return{type:r.wH||"SET_ACTIVE_THEME",payload:e}},h=function(){return{type:r.Kg||"WEBSOCKET_CONNECTED"}},v=function(){return{type:r.co||"WEBSOCKET_DISCONNECTED"}},f=function(e){return{type:r.ZH||"WEBSOCKET_MESSAGE_RECEIVED",payload:e}},g=function(){return{type:"TOGGLE_SIDEBAR"}},b=function(e){return{type:"SET_CURRENT_VIEW",payload:e}},y=function(){return{type:"TOGGLE_PREVIEW_MODE"}};const w={getState:function(){return{}},dispatch:function(){},subscribe:function(){return function(){}}}},55876:(e,t,n)=>{var r,o,i,a,c,l,s,d,u,p,m,h,v,f,g,b,y,w,E,x,A,k,S,_,C,P=n(57528),O=(n(96540),n(70572)),T=n(16918),D=n(57749),I=(T.o5.Title,T.o5.Text,T.o5.Paragraph,["flex","flexMd","flexLg","status","messageType"]),M=function(e){return!I.includes(e)};(0,O.Ay)(D.sT).withConfig({shouldForwardProp:M})(r||(r=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D._x).withConfig({shouldForwardProp:M})(o||(o=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.tK).withConfig({shouldForwardProp:M})(i||(i=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.mc).withConfig({shouldForwardProp:M})(a||(a=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.wn).withConfig({shouldForwardProp:M})(c||(c=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.fI).withConfig({shouldForwardProp:M})(l||(l=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.VP).withConfig({shouldForwardProp:M})(s||(s=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.xA).withConfig({shouldForwardProp:M})(d||(d=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.ee).withConfig({shouldForwardProp:M})(u||(u=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.dd).withConfig({shouldForwardProp:M})(p||(p=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.pK).withConfig({shouldForwardProp:M})(m||(m=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.jn).withConfig({shouldForwardProp:M})(h||(h=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.tA).withConfig({shouldForwardProp:M})(v||(v=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.K0).withConfig({shouldForwardProp:M})(f||(f=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.gE).withConfig({shouldForwardProp:M})(g||(g=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.sQ).withConfig({shouldForwardProp:M})(b||(b=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.aQ).withConfig({shouldForwardProp:M})(y||(y=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.cN).withConfig({shouldForwardProp:M})(w||(w=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.YM).withConfig({shouldForwardProp:M})(E||(E=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.IO).withConfig({shouldForwardProp:M})(x||(x=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.ih).withConfig({shouldForwardProp:M})(A||(A=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.hC).withConfig({shouldForwardProp:M})(k||(k=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.cG).withConfig({shouldForwardProp:M})(S||(S=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.Ex).withConfig({shouldForwardProp:M})(_||(_=(0,P.A)(["\n  /* Additional styles can be added here */\n"]))),(0,O.Ay)(D.eu).withConfig({shouldForwardProp:M})(C||(C=(0,P.A)(["\n  /* Additional styles can be added here */\n"])))},57749:(e,t,n)=>{n.d(t,{Ex:()=>ge,IO:()=>me,K0:()=>ce,VP:()=>ee,YM:()=>pe,_x:()=>J,aQ:()=>de,cG:()=>fe,cN:()=>ue,dd:()=>re,ee:()=>ne,eu:()=>be,fI:()=>X,gE:()=>le,hC:()=>ve,ih:()=>he,jn:()=>ie,mc:()=>$,pK:()=>oe,sQ:()=>se,sT:()=>Y,tA:()=>ae,tK:()=>q,wn:()=>Z,xA:()=>te});var r,o,i,a,c,l,s,d,u,p,m,h,v,f,g,b,y,w,E,x,A,k,S,_,C,P,O,T,D,I,M,L,j,F,R,B,z,N,W=n(57528),H=n(70572),U=n(16918),Q=U.o5.Title,K=U.o5.Text,G=(U.o5.Paragraph,(0,H.AH)(r||(r=(0,W.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n"])))),V=((0,H.AH)(o||(o=(0,W.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n"]))),(0,H.AH)(i||(i=(0,W.A)(["\n  display: flex;\n  flex-direction: column;\n"]))),(0,H.AH)(a||(a=(0,W.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: ","px;\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.md)||16}))),Y=(0,H.Ay)(Q)(c||(c=(0,W.A)(["\n  margin-bottom: ","px;\n  color: ",";\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.lg)||24}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.textPrimary)||"#111827"})),J=(0,H.Ay)(Q)(l||(l=(0,W.A)(["\n  margin-bottom: ","px;\n  color: ",";\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.md)||16}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.textPrimary)||"#111827"})),q=(0,H.Ay)(Q)(s||(s=(0,W.A)(["\n  margin-bottom: ","px;\n  color: ",";\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.sm)||12}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.textSecondary)||"#4B5563"})),$=((0,H.Ay)(K)(d||(d=(0,W.A)(["\n  background-color: ",";\n  padding: ","px ","px;\n  border-radius: ",";\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primaryLight)||"#DBEAFE"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.xs)||8}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.sm)||12}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.borderRadius)||void 0===t?void 0:t.sm)||"2px"})),(0,H.Ay)(K)(u||(u=(0,W.A)(["\n  font-family: 'Courier New', Courier, monospace;\n  background-color: ",";\n  padding: ","px ","px;\n  border-radius: ",";\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.gray100)||"#F3F4F6"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.xs)||8}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.sm)||12}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.borderRadius)||void 0===t?void 0:t.sm)||"2px"})),H.Ay.div(p||(p=(0,W.A)(["\n  max-width: ",";\n  margin: 0 auto;\n  padding: ","px;\n"])),(function(e){return e.maxWidth||"1200px"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.md)||16}))),Z=H.Ay.section(m||(m=(0,W.A)(["\n  margin-bottom: ","px;\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.xl)||32})),X=H.Ay.div(h||(h=(0,W.A)(["\n  display: flex;\n  flex-wrap: wrap;\n  margin: -","px;\n\n  & > * {\n    padding: ","px;\n  }\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.sm)||12}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.sm)||12})),ee=H.Ay.div(v||(v=(0,W.A)(["\n  flex: ",";\n\n  "," {\n    flex: ",";\n  }\n\n  "," {\n    flex: ",";\n  }\n"])),(function(e){return e.flex||"1"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.media)||void 0===t?void 0:t.md)||"@media (min-width: 768px)"}),(function(e){return e.flexMd||e.flex||"1"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.media)||void 0===t?void 0:t.lg)||"@media (min-width: 992px)"}),(function(e){return e.flexLg||e.flexMd||e.flex||"1"})),te=H.Ay.div(f||(f=(0,W.A)(["\n  ","\n"])),V),ne=(0,H.Ay)(U.Zp)(g||(g=(0,W.A)(["\n  border-radius: ",";\n  box-shadow: ",";\n  margin-bottom: ","px;\n  background-color: ",";\n\n  .ant-card-head {\n    border-bottom: 1px solid ",";\n  }\n\n  .ant-card-head-title {\n    color: ",";\n  }\n\n  .ant-card-body {\n    color: ",";\n  }\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.borderRadius)||void 0===t?void 0:t.md)||"4px"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.shadows)||void 0===t?void 0:t.sm)||"0 1px 2px rgba(0, 0, 0, 0.05)"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.md)||16}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.backgroundSecondary)||"#FFFFFF"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.border)||"#D1D5DB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.textPrimary)||"#111827"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.textPrimary)||"#111827"})),re=(0,H.Ay)(ne)(b||(b=(0,W.A)(["\n  height: 100%;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n\n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: ",";\n  }\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.shadows)||void 0===t?void 0:t.md)||"0 4px 6px rgba(0, 0, 0, 0.1)"})),oe=(0,H.Ay)(ne)(y||(y=(0,W.A)(["\n  .ant-card-head {\n    background-color: ",";\n  }\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primaryLight)||"#DBEAFE"})),ie=(0,H.Ay)(U.$n)(w||(w=(0,W.A)(["\n  &.ant-btn-primary {\n    background-color: ",";\n    border-color: ",";\n\n    &:hover, &:focus {\n      background-color: ",";\n      border-color: ",";\n    }\n  }\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primary)||"#2563EB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primary)||"#2563EB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primaryDark)||"#1E40AF"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primaryDark)||"#1E40AF"})),ae=(0,H.Ay)(U.$n)(E||(E=(0,W.A)(["\n  &.ant-btn {\n    border-color: ",";\n    color: ",";\n\n    &:hover, &:focus {\n      border-color: ",";\n      color: ",";\n    }\n  }\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primary)||"#2563EB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primary)||"#2563EB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primaryDark)||"#1E40AF"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primaryDark)||"#1E40AF"})),ce=(0,H.Ay)(U.$n)(x||(x=(0,W.A)(["\n  &.ant-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    \n    .anticon {\n      font-size: ",";\n    }\n  }\n"])),(function(e){return"large"===e.size?"18px":"small"===e.size?"12px":"14px"})),le=H.Ay.div(A||(A=(0,W.A)(["\n  margin-bottom: ","px;\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.md)||16})),se=(0,H.Ay)(U.pd)(k||(k=(0,W.A)(["\n  &.ant-input {\n    border-radius: ",";\n    border-color: ",";\n\n    &:hover {\n      border-color: ",";\n    }\n\n    &:focus {\n      border-color: ",";\n      box-shadow: 0 0 0 2px ",";\n    }\n  }\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.borderRadius)||void 0===t?void 0:t.md)||"4px"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.border)||"#D1D5DB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primary)||"#2563EB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primary)||"#2563EB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primaryLight)||"rgba(37, 99, 235, 0.2)"})),de=(0,H.Ay)(U.pd.TextArea)(S||(S=(0,W.A)(["\n  &.ant-input {\n    border-radius: ",";\n    border-color: ",";\n\n    &:hover {\n      border-color: ",";\n    }\n\n    &:focus {\n      border-color: ",";\n      box-shadow: 0 0 0 2px ",";\n    }\n  }\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.borderRadius)||void 0===t?void 0:t.md)||"4px"}),(function(e){var t,n;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.border?e.theme.colorPalette.border:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.neutral)&&void 0!==n&&n[300]?e.theme.colors.neutral[300]:"#D1D5DB"}),(function(e){var t,n,r;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.primary?e.theme.colorPalette.primary:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.primary)&&void 0!==n&&n.main?e.theme.colors.primary.main:null!==(r=e.theme)&&void 0!==r&&r.primaryColor?e.theme.primaryColor:"#2563EB"}),(function(e){var t,n,r;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.primary?e.theme.colorPalette.primary:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.primary)&&void 0!==n&&n.main?e.theme.colors.primary.main:null!==(r=e.theme)&&void 0!==r&&r.primaryColor?e.theme.primaryColor:"#2563EB"}),(function(e){var t,n;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.primaryLight?e.theme.colorPalette.primaryLight:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.primary)&&void 0!==n&&n.light?e.theme.colors.primary.light:"rgba(37, 99, 235, 0.2)"})),ue=(0,H.Ay)(U.Fc)(_||(_=(0,W.A)(["\n  &.ant-alert {\n    border-radius: ",";\n    margin-bottom: ","px;\n  }\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.borderRadius)||void 0===t?void 0:t.md)||"4px"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.md)||16})),pe=H.Ay.div(C||(C=(0,W.A)(["\n  ","\n  min-height: ",";\n  width: 100%;\n"])),G,(function(e){return e.fullPage?"100vh":"200px"})),me=(0,H.Ay)(U.tK)(P||(P=(0,W.A)(["\n  .ant-spin-dot-item {\n    background-color: ",";\n  }\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primary)||"#2563EB"})),he=(0,H.Ay)(U.vw)(O||(O=(0,W.A)(["\n  &.ant-tag {\n    border-radius: ",";\n    margin-right: ","px;\n    margin-bottom: ","px;\n  }\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.borderRadius)||void 0===t?void 0:t.sm)||"2px"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.xs)||8}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.xs)||8})),ve=(0,H.Ay)(he)(T||(T=(0,W.A)(["\n  &.ant-tag {\n    background-color: ",";\n\n    color: ",";\n\n    border-color: ",";\n  }\n"])),(function(e){var t,n,r,o;switch(e.status){case"success":return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.successLight)||"#D1FAE5";case"warning":return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.warningLight)||"#FEF3C7";case"error":return(null===(r=e.theme)||void 0===r||null===(r=r.colorPalette)||void 0===r?void 0:r.errorLight)||"#FEE2E2";default:return(null===(o=e.theme)||void 0===o||null===(o=o.colorPalette)||void 0===o?void 0:o.infoLight)||"#DBEAFE"}}),(function(e){var t,n,r,o;switch(e.status){case"success":return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.success)||"#10B981";case"warning":return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.warning)||"#FBBF24";case"error":return(null===(r=e.theme)||void 0===r||null===(r=r.colorPalette)||void 0===r?void 0:r.error)||"#DC2626";default:return(null===(o=e.theme)||void 0===o||null===(o=o.colorPalette)||void 0===o?void 0:o.info)||"#2563EB"}}),(function(e){var t,n,r,o;switch(e.status){case"success":return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.success)||"#10B981";case"warning":return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.warning)||"#FBBF24";case"error":return(null===(r=e.theme)||void 0===r||null===(r=r.colorPalette)||void 0===r?void 0:r.error)||"#DC2626";default:return(null===(o=e.theme)||void 0===o||null===(o=o.colorPalette)||void 0===o?void 0:o.info)||"#2563EB"}})),fe=H.Ay.hr(D||(D=(0,W.A)(["\n  border: none;\n  border-top: 1px solid ",";\n  margin: ","px 0;\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.border)||"#D1D5DB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.md)||16})),ge=H.Ay.span(I||(I=(0,W.A)(["\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 20px;\n  height: 20px;\n  padding: 0 6px;\n  font-size: 12px;\n  line-height: 1;\n  border-radius: 10px;\n  background-color: ",";\n  color: white;\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primary)||"#2563EB"})),be=H.Ay.div(M||(M=(0,W.A)(["\n  width: ",";\n  height: ",";\n  border-radius: 50%;\n  background-color: ",";\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: ","px;\n  overflow: hidden;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n  }\n"])),(function(e){return e.size||"40px"}),(function(e){return e.size||"40px"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primary)||"#2563EB"}),(function(e){return parseInt(e.size||"40",10)/2.5}));H.Ay.div(L||(L=(0,W.A)(["\n  padding: ","px ","px;\n  max-width: 300px;\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.xs)||8}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.sm)||12})),H.Ay.div(j||(j=(0,W.A)(["\n  display: flex;\n  justify-content: flex-end;\n  gap: ","px;\n  margin-top: ","px;\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.sm)||12}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.md)||16})),H.Ay.div(F||(F=(0,W.A)(["\n  overflow-x: auto;\n\n  .ant-table {\n    background-color: ",";\n  }\n\n  .ant-table-thead > tr > th {\n    background-color: ",";\n    color: ",";\n  }\n\n  .ant-table-tbody > tr > td {\n    border-bottom: 1px solid ",";\n  }\n\n  .ant-table-tbody > tr:hover > td {\n    background-color: ",";\n  }\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.backgroundSecondary)||"#FFFFFF"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.backgroundTertiary)||"#F9FAFB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.textPrimary)||"#111827"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.border)||"#D1D5DB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primaryLight)||"#DBEAFE"})),H.Ay.div(R||(R=(0,W.A)(["\n  display: flex;\n  align-items: center;\n  margin-bottom: ","px;\n\n  a {\n    color: ",";\n\n    &:hover {\n      color: ",";\n    }\n  }\n\n  span {\n    margin: 0 ","px;\n    color: ",";\n  }\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.md)||16}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.textSecondary)||"#4B5563"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.primary)||"#2563EB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.spacing)||void 0===t?void 0:t.xs)||8}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.textSecondary)||"#4B5563"})),(0,H.AH)(B||(B=(0,W.A)(["\n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n    }\n    to {\n      opacity: 1;\n    }\n  }\n\n  animation: fadeIn "," ease-in-out;\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.animation)||void 0===t?void 0:t.normal)||"0.3s"})),(0,H.AH)(z||(z=(0,W.A)(["\n  @keyframes slideIn {\n    from {\n      transform: translateY(20px);\n      opacity: 0;\n    }\n    to {\n      transform: translateY(0);\n      opacity: 1;\n    }\n  }\n\n  animation: slideIn "," ease-in-out;\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.animation)||void 0===t?void 0:t.normal)||"0.3s"})),(0,H.AH)(N||(N=(0,W.A)(["\n  @keyframes pulse {\n    0% {\n      transform: scale(1);\n    }\n    50% {\n      transform: scale(1.05);\n    }\n    100% {\n      transform: scale(1);\n    }\n  }\n\n  animation: pulse "," ease-in-out infinite;\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.animation)||void 0===t?void 0:t.slow)||"0.5s"}))},78058:(e,t,n)=>{n.d(t,{A:()=>re});var r=n(10467),o=n(64467),i=n(5544),a=n(57528),c=n(54756),l=n.n(c),s=n(96540),d=n(1807),u=n(71468),p=n(85331),m=n(70572),h=n(35346),v=n(82569),f=(n(99160),n(79459)),g=n(70405);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var w=n(23029),E=n(92901);function x(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function A(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?x(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):x(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const k=function(){return(0,E.A)((function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,w.A)(this,e),this.socket=null,this.connected=!1,this.connecting=!1,this.eventListeners=new Map,this.options=A({autoConnect:!1,autoReconnect:!0,reconnectInterval:2e3,maxReconnectAttempts:10,debug:!1},t),this.previewState={components:new Map,deviceSettings:{},viewportState:{},collaborators:new Map,cursors:new Map},this.previewHandlers=new Map,this.debounceSettings={componentUpdate:300,deviceChange:100,cursorMove:50,viewportChange:200},this.initializePreviewHandlers()}),[{key:"connect",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"ws://localhost:8000/ws/collaboration/";return new Promise((function(n,r){if(e.connected)n(e);else if(e.connecting)r(new Error("Connection already in progress"));else{e.connecting=!0;try{e.socket=new WebSocket(t),e.socket.onopen=function(t){e.connected=!0,e.connecting=!1,e.triggerEvent("connect",t),n(e)},e.socket.onclose=function(t){e.connected=!1,e.connecting=!1,e.triggerEvent("disconnect",t)},e.socket.onerror=function(t){e.connecting=!1,e.triggerEvent("error",t),r(new Error("WebSocket connection failed"))},e.socket.onmessage=function(t){try{var n=JSON.parse(t.data);e.triggerEvent("message",n),n.type&&e.triggerEvent(n.type,n)}catch(e){console.error("Error parsing WebSocket message:",e)}}}catch(t){e.connecting=!1,r(t)}}}))}},{key:"disconnect",value:function(){this.socket&&(this.socket.close(),this.socket=null,this.connected=!1)}},{key:"send",value:function(e){var t=this;return new Promise((function(n,r){if(t.connected)try{var o="string"==typeof e?e:JSON.stringify(e);t.socket.send(o),n()}catch(e){r(e)}else r(new Error("WebSocket not connected"))}))}},{key:"on",value:function(e,t){this.eventListeners.has(e)||this.eventListeners.set(e,[]),this.eventListeners.get(e).push(t)}},{key:"triggerEvent",value:function(e,t){(this.eventListeners.get(e)||[]).forEach((function(n){try{n(t)}catch(t){console.error("Error in event handler for ".concat(e,":"),t)}}))}},{key:"initializePreviewHandlers",value:function(){this.on("component_updated",this.handleComponentUpdate.bind(this)),this.on("component_added",this.handleComponentAdd.bind(this)),this.on("component_deleted",this.handleComponentDelete.bind(this)),this.on("component_moved",this.handleComponentMove.bind(this)),this.on("device_changed",this.handleDeviceChange.bind(this)),this.on("viewport_changed",this.handleViewportChange.bind(this)),this.on("collaborator_joined",this.handleCollaboratorJoin.bind(this)),this.on("collaborator_left",this.handleCollaboratorLeave.bind(this)),this.on("cursor_moved",this.handleCursorMove.bind(this)),this.on("selection_changed",this.handleSelectionChange.bind(this)),this.on("preview_state_sync",this.handlePreviewStateSync.bind(this)),this.on("preview_state_request",this.handlePreviewStateRequest.bind(this))}},{key:"sendComponentUpdate",value:(d=(0,r.A)(l().mark((function e(t,n){var r,o,i=arguments;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=i.length>2&&void 0!==i[2]?i[2]:{},o={type:"component_update",component_id:t,component_data:n,timestamp:(new Date).toISOString(),user_id:r.userId||"anonymous",session_id:r.sessionId,immediate:r.immediate||!1},r.immediate&&(this.previewState.components.set(t,n),this.triggerPreviewEvent("component_updated_local",o)),e.abrupt("return",this.send(o,{priority:r.immediate?"high":"normal",compress:!0}));case 4:case"end":return e.stop()}}),e,this)}))),function(e,t){return d.apply(this,arguments)})},{key:"sendDeviceChange",value:(s=(0,r.A)(l().mark((function e(t,n){var r,o,i=arguments;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=i.length>2&&void 0!==i[2]?i[2]:{},o={type:"device_change",device_type:t,device_config:n,timestamp:(new Date).toISOString(),user_id:r.userId||"anonymous",session_id:r.sessionId},this.previewState.deviceSettings={type:t,config:n,timestamp:new Date},e.abrupt("return",this.send(o,{compress:!1}));case 4:case"end":return e.stop()}}),e,this)}))),function(e,t){return s.apply(this,arguments)})},{key:"sendCursorPosition",value:(c=(0,r.A)(l().mark((function e(t){var n,r,o=arguments;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r={type:"cursor_move",position:t,timestamp:(new Date).toISOString(),user_id:n.userId||"anonymous",session_id:n.sessionId},e.abrupt("return",this.send(r,{priority:"low",compress:!1,throttle:this.debounceSettings.cursorMove}));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return c.apply(this,arguments)})},{key:"sendViewportChange",value:(a=(0,r.A)(l().mark((function e(t){var n,r,o=arguments;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r={type:"viewport_change",viewport:t,timestamp:(new Date).toISOString(),user_id:n.userId||"anonymous",session_id:n.sessionId},this.previewState.viewportState=A(A({},t),{},{timestamp:new Date}),e.abrupt("return",this.send(r,{compress:!0}));case 4:case"end":return e.stop()}}),e,this)}))),function(e){return a.apply(this,arguments)})},{key:"requestPreviewState",value:(o=(0,r.A)(l().mark((function e(t){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.send({type:"preview_state_request",session_id:t,timestamp:(new Date).toISOString()}));case 1:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})},{key:"syncPreviewState",value:(n=(0,r.A)(l().mark((function e(t){var n;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n={components:Array.from(this.previewState.components.entries()),deviceSettings:this.previewState.deviceSettings,viewportState:this.previewState.viewportState,timestamp:(new Date).toISOString()},e.abrupt("return",this.send({type:"preview_state_sync",session_id:t,state:n},{compress:!0}));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"handleComponentUpdate",value:function(e){var t=e.component_id,n=e.component_data,r=e.user_id,o=e.timestamp;this.previewState.components.set(t,A(A({},n),{},{lastUpdatedBy:r,lastUpdated:o})),this.triggerPreviewEvent("component_updated",e)}},{key:"handleComponentAdd",value:function(e){var t=e.component;t&&t.id&&(this.previewState.components.set(t.id,t),this.triggerPreviewEvent("component_added",e))}},{key:"handleComponentDelete",value:function(e){var t=e.component_id;t&&(this.previewState.components.delete(t),this.triggerPreviewEvent("component_deleted",e))}},{key:"handleComponentMove",value:function(e){var t=e.component_id,n=e.new_position,r=this.previewState.components.get(t);r&&(this.previewState.components.set(t,A(A({},r),{},{position:n})),this.triggerPreviewEvent("component_moved",e))}},{key:"handleDeviceChange",value:function(e){var t=e.device_type,n=e.device_config,r=e.user_id;this.previewState.deviceSettings={type:t,config:n,changedBy:r,timestamp:new Date},this.triggerPreviewEvent("device_changed",e)}},{key:"handleViewportChange",value:function(e){var t=e.viewport,n=e.user_id;this.previewState.viewportState=A(A({},t),{},{changedBy:n,timestamp:new Date}),this.triggerPreviewEvent("viewport_changed",e)}},{key:"handleCollaboratorJoin",value:function(e){var t=e.user_id,n=e.username,r=e.avatar;this.previewState.collaborators.set(t,{username:n,avatar:r,joinedAt:new Date,isActive:!0}),this.triggerPreviewEvent("collaborator_joined",e)}},{key:"handleCollaboratorLeave",value:function(e){var t=e.user_id;this.previewState.collaborators.delete(t),this.previewState.cursors.delete(t),this.triggerPreviewEvent("collaborator_left",e)}},{key:"handleCursorMove",value:function(e){var t=e.user_id,n=e.position;this.previewState.cursors.set(t,{position:n,timestamp:new Date}),this.triggerPreviewEvent("cursor_moved",e)}},{key:"handleSelectionChange",value:function(e){var t=e.user_id,n=e.selection,r=this.previewState.collaborators.get(t);r&&this.previewState.collaborators.set(t,A(A({},r),{},{selection:n,lastActivity:new Date})),this.triggerPreviewEvent("selection_changed",e)}},{key:"handlePreviewStateSync",value:function(e){var t=this,n=e.state;n&&(n.components&&(this.previewState.components.clear(),n.components.forEach((function(e){var n=(0,i.A)(e,2),r=n[0],o=n[1];t.previewState.components.set(r,o)}))),n.deviceSettings&&(this.previewState.deviceSettings=n.deviceSettings),n.viewportState&&(this.previewState.viewportState=n.viewportState)),this.triggerPreviewEvent("preview_state_synced",e)}},{key:"handlePreviewStateRequest",value:function(e){this.syncPreviewState(e.session_id)}},{key:"triggerPreviewEvent",value:function(e,t){(this.previewHandlers.get(e)||[]).forEach((function(n){try{n(t)}catch(t){console.error("Error in preview event handler for ".concat(e,":"),t)}}))}},{key:"onPreviewEvent",value:function(e,t){var n=this;return this.previewHandlers.has(e)||this.previewHandlers.set(e,[]),this.previewHandlers.get(e).push(t),function(){var r=n.previewHandlers.get(e)||[],o=r.indexOf(t);o>-1&&r.splice(o,1)}}},{key:"getPreviewState",value:function(){return{components:Array.from(this.previewState.components.entries()),deviceSettings:this.previewState.deviceSettings,viewportState:this.previewState.viewportState,collaborators:Array.from(this.previewState.collaborators.entries()),cursors:Array.from(this.previewState.cursors.entries())}}},{key:"clearPreviewState",value:function(){this.previewState.components.clear(),this.previewState.collaborators.clear(),this.previewState.cursors.clear(),this.previewState.deviceSettings={},this.previewState.viewportState={}}},{key:"joinSession",value:(t=(0,r.A)(l().mark((function e(t){var n,r=arguments;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:{},e.abrupt("return",this.send({type:"join_session",session_id:t,user_info:{username:n.username||"Anonymous",avatar:n.avatar||null,timestamp:(new Date).toISOString()}}));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"leaveSession",value:(e=(0,r.A)(l().mark((function e(t){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.send({type:"leave_session",session_id:t,timestamp:(new Date).toISOString()}));case 1:case"end":return e.stop()}}),e,this)}))),function(t){return e.apply(this,arguments)})}]);var e,t,n,o,a,c,s,d}();function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var C,P,O,T,D,I,M,L=n(9341),j=n(88167);function F(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function R(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?F(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):F(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var B=(0,s.lazy)((function(){return Promise.all([n.e(6618),n.e(6288),n.e(4043),n.e(4309),n.e(5897),n.e(6329)]).then(n.bind(n,4309)).catch((function(e){return console.warn("Failed to load ComponentBuilder:",e),{default:function(){return s.createElement("div",null,"Component Builder not available")}}}))})),z=(0,s.lazy)((function(){return Promise.all([n.e(6618),n.e(6288),n.e(5505)]).then(n.bind(n,95505)).catch((function(e){return console.warn("Failed to load LayoutDesigner:",e),{default:function(){return s.createElement("div",null,"Layout Designer not available")}}}))})),N=(0,s.lazy)((function(){return n.e(1667).then(n.bind(n,71667)).catch((function(e){return console.warn("Failed to load ThemeManager:",e),{default:function(){return s.createElement("div",null,"Theme Manager not available")}}}))})),W=(0,s.lazy)((function(){return Promise.all([n.e(6618),n.e(6288),n.e(5366)]).then(n.bind(n,65366)).catch((function(e){return console.warn("Failed to load FixedWebSocketManager:",e),{default:function(){return s.createElement("div",null,"WebSocket Manager not available")}}}))})),H=(0,s.lazy)((function(){return n.e(1680).then(n.bind(n,51680)).catch((function(e){return console.warn("Failed to load ProjectManager:",e),{default:function(){return s.createElement("div",null,"Project Manager not available")}}}))})),U=(0,s.lazy)((function(){return n.e(9205).then(n.bind(n,19205)).catch((function(e){return console.warn("Failed to load EnhancedCodeExporter:",e),{default:function(){return s.createElement("div",null,"Enhanced Code Exporter not available")}}}))})),Q=(0,s.lazy)((function(){return n.e(672).then(n.bind(n,40672)).catch((function(e){return console.warn("Failed to load PerformanceMonitor:",e),{default:function(){return s.createElement("div",null,"Performance Monitor not available")}}}))})),K=(0,s.lazy)((function(){return Promise.all([n.e(6618),n.e(6288),n.e(7481)]).then(n.bind(n,97481)).catch((function(e){return console.warn("Failed to load DataManagementDemo:",e),{default:function(){return s.createElement("div",null,"Data Management not available")}}}))})),G=(0,s.lazy)((function(){return Promise.all([n.e(6618),n.e(6288),n.e(4605)]).then(n.bind(n,54605)).catch((function(e){return console.warn("Failed to load TestingTools:",e),{default:function(){return s.createElement("div",null,"Testing Tools not available")}}}))})),V=(0,s.lazy)((function(){return n.e(3726).then(n.bind(n,23726)).catch((function(e){return console.warn("Failed to load TutorialAIPlugin:",e),{default:function(){return s.createElement("div",null,"Tutorial Assistant not available")}}}))})),Y=d.o5.Title,J=d.o5.Paragraph,q=m.Ay.div(C||(C=(0,a.A)(["\n  padding: var(--spacing-lg);\n  max-width: 1200px;\n  margin: 0 auto;\n  background-color: var(--color-background);\n  min-height: 100vh;\n  transition: all 0.3s ease;\n\n  @media (max-width: 768px) {\n    padding: var(--spacing-md);\n  }\n\n  @media (max-width: 480px) {\n    padding: var(--spacing-sm);\n  }\n"]))),$=m.Ay.div(P||(P=(0,a.A)(["\n  margin-bottom: var(--spacing-xl);\n  padding: var(--spacing-lg);\n  background-color: var(--color-surface);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  border: 1px solid var(--color-border-light);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-md);\n  }\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-md);\n\n    h2 {\n      margin-bottom: var(--spacing-sm);\n    }\n  }\n"]))),Z=(0,m.Ay)(d.tU)(O||(O=(0,a.A)(["\n  .ant-tabs-nav {\n    margin-bottom: var(--spacing-xl);\n    background-color: var(--color-surface);\n    border-radius: var(--border-radius-lg);\n    padding: var(--spacing-sm);\n    box-shadow: var(--shadow-sm);\n    border: 1px solid var(--color-border-light);\n  }\n\n  .ant-tabs-tab {\n    padding: var(--spacing-md) var(--spacing-lg);\n    transition: all 0.3s ease;\n    border-radius: var(--border-radius-md);\n    margin: 0 var(--spacing-xs);\n    color: var(--color-text-secondary);\n    font-weight: 500;\n\n    &:hover {\n      color: var(--color-primary);\n      background-color: var(--color-background-secondary);\n      transform: translateY(-2px);\n    }\n\n    .anticon {\n      margin-right: var(--spacing-sm);\n      font-size: 16px;\n    }\n  }\n\n  .ant-tabs-tab-active {\n    background-color: var(--color-primary);\n    color: white;\n    box-shadow: var(--shadow-md);\n\n    &:hover {\n      background-color: var(--color-primary-hover);\n      color: white;\n    }\n  }\n\n  .ant-tabs-content-holder {\n    background-color: var(--color-surface);\n    border-radius: var(--border-radius-lg);\n    box-shadow: var(--shadow-sm);\n    border: 1px solid var(--color-border-light);\n    overflow: hidden;\n  }\n\n  .ant-tabs-tabpane {\n    padding: var(--spacing-lg);\n  }\n"]))),X=(0,m.Ay)(d.Zp)(T||(T=(0,a.A)(["\n  height: 100%;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  overflow: hidden;\n\n  &:hover {\n    transform: translateY(-8px);\n    box-shadow: var(--shadow-lg);\n    border-color: var(--color-primary);\n  }\n\n  &:active {\n    transform: translateY(-4px);\n  }\n\n  .ant-card-head {\n    background-color: ",";\n    color: ",";\n    border-bottom: 1px solid var(--color-border-light);\n    transition: all 0.3s ease;\n  }\n\n  .ant-card-body {\n    padding: var(--spacing-lg);\n    text-align: center;\n    background-color: var(--color-surface);\n  }\n"])),(function(e){return e.active?"var(--color-primary)":"var(--color-background-secondary)"}),(function(e){return e.active?"white":"var(--color-text)"})),ee=m.Ay.div(D||(D=(0,a.A)(["\n  font-size: 32px;\n  margin-bottom: var(--spacing-md);\n  color: ",";\n  transition: all 0.3s ease;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 64px;\n  height: 64px;\n  border-radius: 50%;\n  background-color: ",";\n  margin: 0 auto var(--spacing-md);\n"])),(function(e){return e.active?"var(--color-primary)":"var(--color-text-secondary)"}),(function(e){return e.active?"rgba(24, 144, 255, 0.1)":"var(--color-background-secondary)"})),te=(0,m.Ay)(d.Zp)(I||(I=(0,a.A)(["\n  margin-bottom: var(--spacing-xl);\n  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);\n  border: none;\n  border-radius: var(--border-radius-xl);\n  overflow: hidden;\n  box-shadow: var(--shadow-lg);\n  position: relative;\n\n  /* Add overlay for better text contrast */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: rgba(0, 0, 0, 0.1);\n    z-index: 1;\n  }\n\n  .ant-card-body {\n    padding: var(--spacing-xxl);\n    color: white;\n    position: relative;\n    z-index: 2;\n  }\n\n  h4, p {\n    color: white !important;\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n    position: relative;\n    z-index: 2;\n  }\n\n  .ant-btn-primary {\n    background-color: white;\n    border-color: white;\n    color: var(--color-primary);\n    font-weight: 600;\n    position: relative;\n    z-index: 2;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n\n    &:hover {\n      background-color: rgba(255, 255, 255, 0.95);\n      border-color: rgba(255, 255, 255, 0.95);\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);\n    }\n  }\n"]))),ne=m.Ay.div(M||(M=(0,a.A)(["\n  @keyframes spin {\n    0% {\n      transform: rotate(0deg);\n    }\n    100% {\n      transform: rotate(360deg);\n    }\n  }\n"])));const re=function(){var e=(0,u.wA)(),t=(0,v.ZV)(),n=(t.isDarkMode,t.colors,(0,s.useState)(!0)),a=(0,i.A)(n,2),c=a[0],m=a[1],b=(0,s.useState)(null),w=(0,i.A)(b,2),E=w[0],x=w[1],A=(0,s.useState)({api:"checking",websocket:"checking"}),S=(0,i.A)(A,2),C=S[0],P=S[1],O=(0,u.d4)((function(e){var t;return(null===(t=e.ui)||void 0===t?void 0:t.currentView)||"components"})),T=(0,s.useState)(O),D=(0,i.A)(T,2),I=D[0],M=D[1],F=(0,s.useState)(!1),re=(0,i.A)(F,2),oe=re[0],ie=re[1],ae=(0,s.useState)({realTimeEnabled:!0,collaborationEnabled:!1,performanceMonitoring:!0,deviceSync:!0}),ce=(0,i.A)(ae,2),le=ce[0],se=ce[1],de=(0,s.useState)((function(){return"session_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,11))})),ue=(0,i.A)(de,1)[0],pe=(0,s.useState)((function(){return"user_".concat(Math.random().toString(36).substring(2,11))})),me=(0,i.A)(pe,1)[0],he=(0,u.d4)((function(e){var t;return(null===(t=e.websocket)||void 0===t?void 0:t.connected)||!1})),ve=(0,u.d4)((function(e){var t;return(null===(t=e.app)||void 0===t?void 0:t.components)||[]})),fe=(function(e){var t,n=e.initialDevice,r=void 0===n?"desktop":n,o=e.initialVariant,a=void 0===o?null:o,c=e.enableBreakpointDetection,l=void 0===c||c,d=e.customBreakpoints,u=void 0===d?null:d,p=(0,s.useState)(r),m=(0,i.A)(p,2),h=m[0],v=m[1],f=(0,s.useState)(a||(null===(t=g.c[r])||void 0===t?void 0:t.defaultVariant)),b=(0,i.A)(f,2),w=b[0],E=b[1],x=(0,s.useState)("portrait"),A=(0,i.A)(x,2),k=A[0],S=A[1],_=(0,s.useState)(!1),C=(0,i.A)(_,2),P=C[0],O=C[1],T=(0,s.useState)({width:0,height:0}),D=(0,i.A)(T,2),I=D[0],M=D[1],L=u||{mobile:{min:0,max:767},tablet:{min:768,max:1023},desktop:{min:1024,max:1/0}},j=(0,s.useMemo)((function(){var e=g.c[h];if(!e)return null;var t=e.variants[w];return t?y(y(y({},e),t),{},{category:e.category,orientation:k}):null}),[h,w,k]),F=(0,s.useMemo)((function(){return j?y(y({},{fontSize:"mobile"===j.category?"14px":"tablet"===j.category?"15px":"16px",padding:"mobile"===j.category?"8px":"tablet"===j.category?"12px":"16px",margin:"mobile"===j.category?"4px":"8px",borderRadius:"mobile"===j.category?"4px":"6px"}),{mobile:{maxWidth:"100%",touchAction:"manipulation",WebkitTapHighlightColor:"transparent"},tablet:{maxWidth:"100%",touchAction:"manipulation"},desktop:{cursor:"pointer",userSelect:"none"}}[j.category]):{}}),[j]),R=(0,s.useCallback)((function(e){var t;return j&&(null===(t={mobile:{button:"small",input:"small",card:"small",table:"small",form:"small"},tablet:{button:"middle",input:"middle",card:"default",table:"middle",form:"middle"},desktop:{button:"middle",input:"middle",card:"default",table:"middle",form:"middle"}}[j.category])||void 0===t?void 0:t[e])||"middle"}),[j]);(0,s.useEffect)((function(){if(l){var e=function(){M({width:window.innerWidth,height:window.innerHeight})};return e(),window.addEventListener("resize",e),function(){window.removeEventListener("resize",e)}}}),[l]),(0,s.useEffect)((function(){var e;if(l&&0!==I.width){var t,n=null===(e=Object.entries(L).find((function(e){var t=(0,i.A)(e,2),n=(t[0],t[1]);return I.width>=n.min&&I.width<=n.max})))||void 0===e?void 0:e[0];n&&n!==h&&(v(n),E(null===(t=g.c[n])||void 0===t?void 0:t.defaultVariant))}}),[I,L,l,h]);var B=(0,s.useCallback)((function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;v(e);var r=n||(null===(t=g.c[e])||void 0===t?void 0:t.defaultVariant);E(r),"desktop"===e&&S("portrait")}),[]),z=(0,s.useCallback)((function(e){E(e)}),[]),N=(0,s.useCallback)((function(){"desktop"!==(null==j?void 0:j.category)&&S((function(e){return"portrait"===e?"landscape":"portrait"}))}),[j]),W=(0,s.useCallback)((function(){O((function(e){return!e}))}),[]),H=(0,s.useCallback)((function(){return{mobile:"@media (max-width: ".concat(L.mobile.max,"px)"),tablet:"@media (min-width: ".concat(L.tablet.min,"px) and (max-width: ").concat(L.tablet.max,"px)"),desktop:"@media (min-width: ".concat(L.desktop.min,"px)"),isMobile:"(max-width: ".concat(L.mobile.max,"px)"),isTablet:"(min-width: ".concat(L.tablet.min,"px) and (max-width: ").concat(L.tablet.max,"px)"),isDesktop:"(min-width: ".concat(L.desktop.min,"px)")}}),[L]),U=(0,s.useCallback)((function(e){return(null==j?void 0:j.category)===e}),[j]),Q=(0,s.useCallback)((function(e){var t={size:R(e)};return"mobile"===(null==j?void 0:j.category)&&("table"===e&&(t.scroll={x:!0},t.pagination={simple:!0,size:"small"}),"form"===e&&(t.layout="vertical")),t}),[j,R]),K=(0,s.useCallback)((function(){if(!j)return{width:0,height:0};var e=j.width,t=j.height;return"landscape"===k&&"desktop"!==j.category?{width:t,height:e}:{width:e,height:t}}),[j,k]),G={shouldHideComponent:function(e){var t;return((null==e||null===(t=e.responsive)||void 0===t?void 0:t.hideOn)||[]).includes(null==j?void 0:j.category)},getComponentStyles:function(e){var t=((null==e?void 0:e.responsive)||{})[null==j?void 0:j.category]||{};return y(y({},F),t)},isFeatureSupported:function(e){return!1!=={hover:"desktop"===(null==j?void 0:j.category),touch:"desktop"!==(null==j?void 0:j.category),keyboard:"desktop"===(null==j?void 0:j.category),contextMenu:"desktop"===(null==j?void 0:j.category)}[e]}};y(y({currentDevice:h,currentVariant:w,orientation:k,isFullscreen:P,deviceConfig:j,viewportSize:I,responsiveStyles:F,mediaQueries:H(),dimensions:K(),handleDeviceChange:B,handleVariantChange:z,handleOrientationChange:N,handleFullscreenToggle:W,isDevice:U,getComponentSize:R,getResponsiveProps:Q},G),{},{availableDevices:Object.keys(g.c),availableVariants:j?Object.keys(j.variants||{}):[],breakpoints:L})}({initialDevice:"desktop",enableBreakpointDetection:!0}),function(e){var t=e.sessionId,n=e.userId,o=e.username,a=void 0===o?"Anonymous":o,c=e.avatar,d=void 0===c?null:c,p=e.enableCollaboration,m=void 0===p||p,h=e.enableCursorTracking,v=void 0===h||h,f=e.enableDeviceSync,g=void 0===f||f,b=(0,s.useState)(new Map),y=(0,i.A)(b,2),w=y[0],E=y[1],x=(0,s.useState)(new Map),A=(0,i.A)(x,2),S=A[0],C=A[1],P=(0,s.useState)(!1),O=(0,i.A)(P,2),T=O[0],D=O[1],I=(0,s.useState)("disconnected"),M=(0,i.A)(I,2),L=M[0],j=M[1],F=(0,s.useState)(new Map),R=(0,i.A)(F,2),B=R[0],z=R[1],N=(0,s.useState)({}),W=(0,i.A)(N,2),H=W[0],U=W[1],Q=(0,s.useState)(new Map),K=(0,i.A)(Q,2),G=K[0],V=K[1],Y=(0,s.useRef)(null),J=(0,s.useRef)(new Map),q=(0,s.useRef)(new Date),$=(0,u.d4)((function(e){var t;return(null===(t=e.websocket)||void 0===t?void 0:t.config)||{}}));(0,s.useEffect)((function(){if(m&&t){var e=new k({url:$.url||"ws://localhost:8000/ws/collaboration/",autoConnect:!0,reconnectOptions:{maxAttempts:10,initialDelay:1e3,maxDelay:3e4}});return Y.current=e,e.on("connect",(function(){D(!0),j("connected"),e.joinSession(t,{username:a,avatar:d})})),e.on("disconnect",(function(){D(!1),j("disconnected")})),e.on("error",(function(e){j("error"),console.error("Collaborative WebSocket error:",e)})),Z(e),function(){e&&(e.leaveSession(t),e.disconnect()),J.current.forEach((function(e){return clearTimeout(e)})),J.current.clear()}}}),[t,m,a,d,$.url]);var Z=(0,s.useCallback)((function(e){e.onPreviewEvent("collaborator_joined",(function(e){var t=e.user_id,n=e.username,r=e.avatar;E((function(e){return new Map(e.set(t,{id:t,username:n,avatar:r,joinedAt:new Date,isActive:!0}))}))})),e.onPreviewEvent("collaborator_left",(function(e){var t=e.user_id;E((function(e){var n=new Map(e);return n.delete(t),n})),C((function(e){var n=new Map(e);return n.delete(t),n}))})),v&&e.onPreviewEvent("cursor_moved",(function(e){var t=e.user_id,r=e.position;if(t!==n){C((function(e){return new Map(e.set(t,{position:r,timestamp:new Date,userId:t}))}));var o=J.current.get(t);o&&clearTimeout(o);var i=setTimeout((function(){C((function(e){var n=new Map(e);return n.delete(t),n})),J.current.delete(t)}),5e3);J.current.set(t,i)}})),e.onPreviewEvent("component_updated",(function(e){var t=e.component_id,r=e.component_data,o=e.user_id;o!==n&&z((function(e){return new Map(e.set(t,_(_({},r),{},{lastUpdatedBy:o,lastUpdated:new Date,synced:!0})))}))})),e.onPreviewEvent("component_added",(function(e){var t=e.component,r=e.user_id;r!==n&&null!=t&&t.id&&z((function(e){return new Map(e.set(t.id,_(_({},t),{},{addedBy:r,synced:!0})))}))})),e.onPreviewEvent("component_deleted",(function(e){var t=e.component_id;e.user_id!==n&&z((function(e){var n=new Map(e);return n.delete(t),n}))})),g&&e.onPreviewEvent("device_changed",(function(e){var t=e.device_type,r=e.device_config,o=e.user_id;o!==n&&U({type:t,config:r,changedBy:o,timestamp:new Date})})),e.onPreviewEvent("preview_state_synced",(function(e){var t=e.state;if(t.components){var n=new Map(t.components);z(n)}t.deviceSettings&&g&&U(t.deviceSettings),q.current=new Date}))}),[n,v,g]),X=(0,s.useCallback)(function(){var e=(0,r.A)(l().mark((function e(r,o){var i,a=arguments;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=a.length>2&&void 0!==a[2]&&a[2],Y.current&&T){e.next=3;break}return e.abrupt("return",!1);case 3:return e.prev=3,e.next=6,Y.current.sendComponentUpdate(r,o,{userId:n,sessionId:t,immediate:i});case 6:return e.abrupt("return",!0);case 9:return e.prev=9,e.t0=e.catch(3),console.error("Failed to send component update:",e.t0),e.abrupt("return",!1);case 13:case"end":return e.stop()}}),e,null,[[3,9]])})));return function(t,n){return e.apply(this,arguments)}}(),[T,n,t]),ee=(0,s.useCallback)(function(){var e=(0,r.A)(l().mark((function e(r){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Y.current&&T&&v){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,Y.current.sendCursorPosition(r,{userId:n,sessionId:t});case 5:e.next=10;break;case 7:e.prev=7,e.t0=e.catch(2),console.error("Failed to send cursor position:",e.t0);case 10:case"end":return e.stop()}}),e,null,[[2,7]])})));return function(t){return e.apply(this,arguments)}}(),[T,n,t,v]),te=(0,s.useCallback)(function(){var e=(0,r.A)(l().mark((function e(r,o){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Y.current&&T&&g){e.next=2;break}return e.abrupt("return",!1);case 2:return e.prev=2,e.next=5,Y.current.sendDeviceChange(r,o,{userId:n,sessionId:t});case 5:return e.abrupt("return",!0);case 8:return e.prev=8,e.t0=e.catch(2),console.error("Failed to send device change:",e.t0),e.abrupt("return",!1);case 12:case"end":return e.stop()}}),e,null,[[2,8]])})));return function(t,n){return e.apply(this,arguments)}}(),[T,n,t,g]),ne=(0,s.useCallback)((0,r.A)(l().mark((function e(){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(Y.current&&T){e.next=2;break}return e.abrupt("return",!1);case 2:return e.prev=2,e.next=5,Y.current.requestPreviewState(t);case 5:return e.abrupt("return",!0);case 8:return e.prev=8,e.t0=e.catch(2),console.error("Failed to request preview state:",e.t0),e.abrupt("return",!1);case 12:case"end":return e.stop()}}),e,null,[[2,8]])}))),[T,t]),re=(0,s.useCallback)((function(e,t){V((function(r){return new Map(r.set(e,{resolution:t,timestamp:new Date,resolvedBy:n}))}))}),[n]),oe=(0,s.useCallback)((function(e,t){var n=B.get(e),r=G.get(e);if(!n)return t;if(!r){var o=new Date((null==t?void 0:t.lastUpdated)||0),i=new Date((null==n?void 0:n.lastUpdated)||0);if(Math.abs(o-i)<1e3)return i>o?n:t}switch(null==r?void 0:r.resolution){case"use_local":return t;case"use_remote":default:return n;case"merge":return _(_({},t),n)}}),[B,G]),ie=(0,s.useCallback)((function(){return{isConnected:T,connectionStatus:L,collaboratorCount:w.size,hasActiveCollaborators:w.size>0,lastSyncTime:q.current,syncedComponentCount:B.size}}),[T,L,w.size,B.size]);return{isConnected:T,connectionStatus:L,collaborators:Array.from(w.values()),cursors:Array.from(S.values()),syncedComponents:Array.from(B.entries()),deviceState:H,sendComponentUpdate:X,sendCursorPosition:ee,sendDeviceChange:te,requestSync:ne,resolveConflict:re,getResolvedComponent:oe,getCollaborationStatus:ie,wsService:Y.current}}({sessionId:ue,userId:me,username:"App Builder User",enableCollaboration:le.collaborationEnabled&&he,enableCursorTracking:!0,enableDeviceSync:le.deviceSync})),ge=(0,f.A)({components:ve,websocketService:fe.wsService,enableWebSocket:le.realTimeEnabled&&he,updateDelay:300}),be=(0,s.useCallback)((function(e,t){se((function(n){return R(R({},n),{},(0,o.A)({},e,t))}))}),[]);(0,s.useEffect)((function(){var t=function(){var t=(0,r.A)(l().mark((function t(){var n,r,o;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e((0,p.tI)(I)),console.log("App Builder initialized successfully"),(n=!1)&&console.log("Development mode: App will continue to load even if connections fail"),t.prev=5,console.log("Checking API connection..."),t.next=9,Promise.race([fetch("/api/status/"),new Promise((function(e,t){return setTimeout((function(){return t(new Error("API timeout"))}),1e3)}))]);case 9:t.sent.ok?(P((function(e){return R(R({},e),{},{api:"connected"})})),console.log("API connection successful")):(P((function(e){return R(R({},e),{},{api:n?"warning":"error"})})),console.warn("API connection failed")),t.next=18;break;case 13:t.prev=13,t.t0=t.catch(5),P((function(e){return R(R({},e),{},{api:n?"warning":"error"})})),console.warn("API connection error:",t.t0.message),n&&console.log("Development mode: Continuing with mock API");case 18:try{console.log("Checking WebSocket connection..."),n?(P((function(e){return R(R({},e),{},{websocket:"warning"})})),console.log("Development mode: Skipping WebSocket check")):(r=new WebSocket("ws://localhost:8000/ws/app_builder/"),o=setTimeout((function(){r.close(),P((function(e){return R(R({},e),{},{websocket:"error"})}))}),1e3),r.onopen=function(){clearTimeout(o),P((function(e){return R(R({},e),{},{websocket:"connected"})})),console.log("WebSocket connection successful"),r.close()},r.onerror=function(){clearTimeout(o),P((function(e){return R(R({},e),{},{websocket:"error"})})),console.warn("WebSocket connection failed"),r.close()})}catch(e){P((function(e){return R(R({},e),{},{websocket:n?"warning":"error"})})),console.warn("WebSocket connection error:",e.message)}t.next=26;break;case 21:t.prev=21,t.t1=t.catch(0),console.error("Failed to initialize App Builder:",t.t1),d.iU.error("Failed to initialize App Builder. Please try refreshing the page."),x("Failed to initialize App Builder. Please try refreshing the page.");case 26:return t.prev=26,setTimeout((function(){m(!1)}),500),t.finish(26);case 29:case"end":return t.stop()}}),t,null,[[0,21,26,29],[5,13]])})));return function(){return t.apply(this,arguments)}}(),n=setTimeout((function(){m(!1),console.log("Loading timeout triggered - forcing app to load")}),3e3);return t(),function(){return clearTimeout(n)}}),[e,I]);var ye=(0,s.useCallback)((function(t){M(t),e((0,p.tI)(t))}),[e]),we=(0,s.useMemo)((function(){return[{key:"projects",label:s.createElement("span",null,s.createElement(h.KGW,null)," Projects"),children:s.createElement(s.Suspense,{fallback:s.createElement("div",{style:{padding:"20px",textAlign:"center"}},s.createElement(d.tK,{size:"large"}),s.createElement("div",{style:{marginTop:"10px"}},"Loading Projects..."))},s.createElement(H,null))},{key:"components",label:s.createElement("span",null,s.createElement(h.rS9,null)," Component Builder"),children:s.createElement(j.A,{fallback:s.createElement("div",null,"Using basic component builder...")},s.createElement(s.Suspense,{fallback:s.createElement("div",{style:{padding:"20px",textAlign:"center"}},s.createElement(d.tK,{size:"large"}),s.createElement("div",{style:{marginTop:"10px"}},"Loading Component Builder..."))},s.createElement(B,null)))},{key:"layouts",label:s.createElement("span",null,s.createElement(h.hy2,null)," Layout Designer"),children:s.createElement(j.A,{fallback:s.createElement("div",null,"Using basic layout designer...")},s.createElement(s.Suspense,{fallback:s.createElement("div",{style:{padding:"20px",textAlign:"center"}},s.createElement(d.tK,{size:"large"}),s.createElement("div",{style:{marginTop:"10px"}},"Loading Layout Designer..."))},s.createElement(z,null)))},{key:"themes",label:s.createElement("span",null,s.createElement(h.Ebl,null)," Theme Manager"),children:s.createElement(j.A,{fallback:s.createElement("div",null,"Using basic theme manager...")},s.createElement(s.Suspense,{fallback:s.createElement("div",{style:{padding:"20px",textAlign:"center"}},s.createElement(d.tK,{size:"large"}),s.createElement("div",{style:{marginTop:"10px"}},"Loading Theme Manager..."))},s.createElement(N,null)))},{key:"export",label:s.createElement("span",null,s.createElement(h.C$o,null)," Enhanced Export"),children:s.createElement(s.Suspense,{fallback:s.createElement("div",{style:{padding:"20px",textAlign:"center"}},s.createElement(d.tK,{size:"large"}),s.createElement("div",{style:{marginTop:"10px"}},"Loading Enhanced Code Exporter..."))},s.createElement(U,null))},{key:"performance",label:s.createElement("span",null,s.createElement(h.zpd,null)," Performance"),children:s.createElement(s.Suspense,{fallback:s.createElement("div",{style:{padding:"20px",textAlign:"center"}},s.createElement(d.tK,{size:"large"}),s.createElement("div",{style:{marginTop:"10px"}},"Loading Performance Monitor..."))},s.createElement(Q,null))},{key:"websocket",label:s.createElement("span",null,s.createElement(h.bfv,null)," WebSocket Manager"),children:s.createElement(s.Suspense,{fallback:s.createElement("div",{style:{padding:"20px",textAlign:"center"}},s.createElement(d.tK,{size:"large"}),s.createElement("div",{style:{marginTop:"10px"}},"Loading WebSocket Manager..."))},s.createElement(W,null))},{key:"data",label:s.createElement("span",null,s.createElement(h.rUN,null)," Data Management"),children:s.createElement(s.Suspense,{fallback:s.createElement("div",{style:{padding:"20px",textAlign:"center"}},s.createElement(d.tK,{size:"large"}),s.createElement("div",{style:{marginTop:"10px"}},"Loading Data Management..."))},s.createElement(K,null))},{key:"testing",label:s.createElement("span",null,s.createElement(h.rUN,null)," Testing Tools"),children:s.createElement(s.Suspense,{fallback:s.createElement("div",{style:{padding:"20px",textAlign:"center"}},s.createElement(d.tK,{size:"large"}),s.createElement("div",{style:{marginTop:"10px"}},"Loading Testing Tools..."))},s.createElement(G,null))},{key:"tutorial",label:s.createElement("span",null,s.createElement(h.faO,null)," Tutorial Assistant"),children:s.createElement(s.Suspense,{fallback:s.createElement("div",{style:{padding:"20px",textAlign:"center"}},s.createElement(d.tK,{size:"large"}),s.createElement("div",{style:{marginTop:"10px"}},"Loading Tutorial Assistant..."))},s.createElement("div",{style:{padding:"20px"}},s.createElement(Y,{level:3},"Tutorial Assistant"),s.createElement(J,null,"Get help and learn how to use App Builder with our AI-powered tutorial assistant."),s.createElement(V,null)))}]}),[]);return c?s.createElement(ne,null,s.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"70vh",flexDirection:"column"}},s.createElement("div",{style:{width:"50px",height:"50px",border:"5px solid #f3f3f3",borderTop:"5px solid #3498db",borderRadius:"50%",animation:"spin 1s linear infinite"}}),s.createElement(Y,{level:3,style:{marginTop:"20px"}},"Loading App Builder..."),s.createElement("div",{style:{marginTop:"20px",textAlign:"center"}},s.createElement("div",{style:{color:"var(--color-text)",backgroundColor:"var(--color-background-secondary)",padding:"8px 16px",borderRadius:"8px",marginBottom:"8px",border:"1px solid var(--color-border-light)"}},"API Connection: "," ",s.createElement("span",{style:{color:"connected"===C.api?"#52c41a":"error"===C.api?"#ff4d4f":"warning"===C.api?"#faad14":"#1890ff",fontWeight:"600"}},"connected"===C.api?"Connected":"error"===C.api?"Failed":"warning"===C.api?"Limited (Mock)":"Checking...")),s.createElement("div",{style:{color:"var(--color-text)",backgroundColor:"var(--color-background-secondary)",padding:"8px 16px",borderRadius:"8px",border:"1px solid var(--color-border-light)"}},"WebSocket Connection: "," ",s.createElement("span",{style:{color:"connected"===C.websocket?"#52c41a":"error"===C.websocket?"#ff4d4f":"warning"===C.websocket?"#faad14":"#1890ff",fontWeight:"600"}},"connected"===C.websocket?"Connected":"error"===C.websocket?"Failed":"warning"===C.websocket?"Limited (Mock)":"Checking...")),("error"===C.api||"error"===C.websocket)&&s.createElement("div",{style:{marginTop:"20px",color:"#ff4d4f",backgroundColor:"var(--color-background-secondary)",padding:"12px 16px",borderRadius:"8px",border:"1px solid #ff4d4f",fontWeight:"500"}},s.createElement("p",{style:{margin:"0 0 8px 0"}},"Some connections failed. The app will continue to load with limited functionality."),s.createElement("p",{style:{margin:0}},"Please ensure the backend server is running at http://localhost:8000")),("warning"===C.api||"warning"===C.websocket)&&s.createElement("div",{style:{marginTop:"20px",color:"#faad14",backgroundColor:"var(--color-background-secondary)",padding:"12px 16px",borderRadius:"8px",border:"1px solid #faad14",fontWeight:"500"}},s.createElement("p",{style:{margin:"0 0 8px 0"}},"Some connections are in limited mode. The app will use mock data."),s.createElement("p",{style:{margin:0}},"This is normal in development mode when the backend is not running."))))):E?s.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"70vh",flexDirection:"column"}},s.createElement("div",{style:{color:"red",fontSize:"48px",marginBottom:"20px"}},s.createElement(h.rUN,null)),s.createElement(Y,{level:3,style:{color:"red"}},"Error"),s.createElement(J,{style:{textAlign:"center",maxWidth:"600px",marginTop:"20px"}},E),s.createElement(d.$n,{type:"primary",style:{marginTop:"20px"},onClick:function(){return window.location.reload()}},"Refresh Page")):s.createElement(q,{className:"app-builder-enhanced"},s.createElement($,null,s.createElement("div",null,s.createElement(Y,{level:2},"App Builder Enhanced"),s.createElement(J,null,"Create and manage your application components with ease"),s.createElement("div",{style:{display:"flex",gap:"10px",marginTop:"5px"}},s.createElement(d.m_,{title:"connected"===C.api?"API Connected":"warning"===C.api?"API in Limited Mode (Mock)":"API Connection Failed"},s.createElement("div",{style:{display:"inline-flex",alignItems:"center",fontSize:"12px",color:"connected"===C.api?"#52c41a":"warning"===C.api?"#faad14":"#ff4d4f",backgroundColor:"var(--color-background-secondary)",padding:"4px 8px",borderRadius:"12px",border:"1px solid var(--color-border-light)",fontWeight:"500"}},s.createElement("div",{style:{width:"8px",height:"8px",borderRadius:"50%",backgroundColor:"connected"===C.api?"#52c41a":"warning"===C.api?"#faad14":"#ff4d4f",marginRight:"6px",boxShadow:"0 0 4px ".concat("connected"===C.api?"#52c41a":"warning"===C.api?"#faad14":"#ff4d4f")}}),"API")),s.createElement(d.m_,{title:"connected"===C.websocket?"WebSocket Connected":"warning"===C.websocket?"WebSocket in Limited Mode (Mock)":"WebSocket Connection Failed"},s.createElement("div",{style:{display:"inline-flex",alignItems:"center",fontSize:"12px",color:"connected"===C.websocket?"#52c41a":"warning"===C.websocket?"#faad14":"#ff4d4f",backgroundColor:"var(--color-background-secondary)",padding:"4px 8px",borderRadius:"12px",border:"1px solid var(--color-border-light)",fontWeight:"500"}},s.createElement("div",{style:{width:"8px",height:"8px",borderRadius:"50%",backgroundColor:"connected"===C.websocket?"#52c41a":"warning"===C.websocket?"#faad14":"#ff4d4f",marginRight:"6px",boxShadow:"0 0 4px ".concat("connected"===C.websocket?"#52c41a":"warning"===C.websocket?"#faad14":"#ff4d4f")}}),"WebSocket")))),s.createElement("div",{style:{display:"flex",alignItems:"center",gap:"12px"}},s.createElement("div",{style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 12px",background:"rgba(255, 255, 255, 0.1)",borderRadius:"8px",border:"1px solid rgba(255, 255, 255, 0.2)"}},s.createElement(d.m_,{title:"Real-time Preview"},s.createElement(d.dO,{size:"small",checked:le.realTimeEnabled,onChange:function(e){return be("realTimeEnabled",e)},checkedChildren:s.createElement(h.Om2,null),unCheckedChildren:s.createElement(h.Om2,null)})),s.createElement(d.m_,{title:"Collaboration"},s.createElement(d.dO,{size:"small",checked:le.collaborationEnabled&&he,onChange:function(e){return be("collaborationEnabled",e)},disabled:!he,checkedChildren:s.createElement(h.OmY,null),unCheckedChildren:s.createElement(h.OmY,null)})),s.createElement(d.m_,{title:"Performance Monitoring"},s.createElement(d.dO,{size:"small",checked:le.performanceMonitoring,onChange:function(e){return be("performanceMonitoring",e)},checkedChildren:s.createElement(h.CwG,null),unCheckedChildren:s.createElement(h.CwG,null)})),le.collaborationEnabled&&fe.isConnected&&s.createElement(d.Ex,{count:fe.collaborators.length,showZero:!1,style:{backgroundColor:"#52c41a"}},s.createElement(d.m_,{title:"Active collaborators"},s.createElement("div",{style:{width:"20px",height:"20px",borderRadius:"50%",background:"#52c41a",display:"flex",alignItems:"center",justifyContent:"center"}},s.createElement(h.OmY,{style:{fontSize:"10px",color:"white"}}))))),s.createElement(d.m_,{title:"Open Tutorial Assistant"},s.createElement(d.$n,{type:"default",icon:s.createElement(h.faO,null),style:{marginRight:"10px"},onClick:function(){return ie(!0)}},"Help")),s.createElement(d.m_,{title:"Refresh connections"},s.createElement(d.$n,{type:"default",onClick:function(){return window.location.reload()}},"Refresh")))),s.createElement(te,null,s.createElement(d.fI,{gutter:[24,24]},s.createElement(d.fv,{xs:24,md:16},s.createElement(Y,{level:4},"Welcome to App Builder Enhanced"),s.createElement(J,null,"This tool helps you create and manage your application components with ease. Use the tabs below to navigate between different features."),s.createElement(d.$n,{type:"primary",size:"large",onClick:function(){return ye("components")}},"Start Building")),s.createElement(d.fv,{xs:24,md:8},s.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"}},s.createElement("img",{src:"/static/images/app-builder-logo.svg",alt:"App Builder Logo",style:{maxWidth:"100%",height:"auto"},onError:function(e){e.target.onerror=null,e.target.style.display="none"}}))))),s.createElement(Z,{activeKey:I,onChange:ye,type:"card",size:"large",items:we}),s.createElement(d.Zp,{title:"Getting Started",style:{marginTop:"24px"}},s.createElement(d.fI,{gutter:[24,24]},s.createElement(d.fv,{xs:24,md:6},s.createElement(X,{title:"Step 1",active:"components"===I,onClick:function(){return ye("components")}},s.createElement(ee,{active:"components"===I},s.createElement(h.rS9,null)),s.createElement(J,null,"Use the Component Builder to create UI components"))),s.createElement(d.fv,{xs:24,md:6},s.createElement(X,{title:"Step 2",active:"layouts"===I,onClick:function(){return ye("layouts")}},s.createElement(ee,{active:"layouts"===I},s.createElement(h.hy2,null)),s.createElement(J,null,"Design your layout with the Layout Designer"))),s.createElement(d.fv,{xs:24,md:6},s.createElement(X,{title:"Step 3",active:"themes"===I,onClick:function(){return ye("themes")}},s.createElement(ee,{active:"themes"===I},s.createElement(h.Ebl,null)),s.createElement(J,null,"Customize your theme with the Theme Manager"))),s.createElement(d.fv,{xs:24,md:6},s.createElement(X,{title:"Step 4",active:"websocket"===I,onClick:function(){return ye("websocket")}},s.createElement(ee,{active:"websocket"===I},s.createElement(h.bfv,null)),s.createElement(J,null,"Set up real-time communication with WebSocket Manager"))))),le.performanceMonitoring&&s.createElement(L.r3,{renderTime:ge.isUpdating?16:8,frameRate:60,memoryUsage:performance.memory?Math.round(performance.memory.usedJSHeapSize/1024/1024):0,componentCount:ve.length,visibleComponents:ve.length,cacheSize:0,updateFrequency:ge.hasPendingUpdates?30:0,floating:!0,showAlerts:!0,optimizationsEnabled:le.realTimeEnabled,onToggleOptimizations:function(e){return be("realTimeEnabled",e)}}),s.createElement(d.ff,{icon:s.createElement(h.faO,null),type:"primary",style:{right:le.performanceMonitoring?340:24,bottom:24},onClick:function(){return ie(!0)},tooltip:"Tutorial Assistant"}),s.createElement(d.aF,{title:"Tutorial Assistant",open:oe,onCancel:function(){return ie(!1)},footer:null,width:800,style:{top:20}},s.createElement("div",{style:{padding:"10px 0"}},s.createElement(J,null,"Get help and learn how to use App Builder with our AI-powered tutorial assistant."),s.createElement(s.Suspense,{fallback:s.createElement("div",{style:{padding:"20px",textAlign:"center"}},s.createElement(d.tK,{size:"large"}),s.createElement("div",{style:{marginTop:"10px"}},"Loading Tutorial Assistant..."))},s.createElement(V,null)))))}},79459:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(60436),o=n(64467),i=n(5544),a=n(96540),c=n(71468),l=n(2543);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const u=function(e){var t=e.components,n=void 0===t?[]:t,o=e.onUpdateComponent,s=e.onAddComponent,u=e.onDeleteComponent,p=e.websocketService,m=e.updateDelay,h=void 0===m?300:m,v=e.throttleDelay,f=void 0===v?100:v,g=e.enableWebSocket,b=void 0===g||g,y=(0,a.useState)(!1),w=(0,i.A)(y,2),E=w[0],x=w[1],A=(0,a.useState)(null),k=(0,i.A)(A,2),S=k[0],_=k[1],C=(0,a.useState)(new Map),P=(0,i.A)(C,2),O=P[0],T=P[1],D=(0,a.useState)([]),I=(0,i.A)(D,2),M=I[0],L=I[1],j=(0,a.useRef)(null),F=(0,a.useRef)(p),R=(0,a.useRef)(new Map),B=((0,c.wA)(),(0,c.d4)((function(e){var t;return(null===(t=e.websocket)||void 0===t?void 0:t.connected)||!1})));(0,a.useEffect)((function(){F.current=p}),[p]);var z=(0,a.useCallback)((0,l.debounce)((function(e){0!==e.length&&(x(!0),e.forEach((function(e){var t=e.type,n=e.componentId,r=e.data;switch(t){case"update":o&&o(n,r);break;case"add":s&&s(r);break;case"delete":u&&u(n);break;default:console.warn("Unknown update type:",t)}})),b&&B&&F.current&&e.forEach((function(e){var t=e.type,n=e.componentId,r=e.data;F.current.send({type:"component_".concat(t),component_id:n,component_data:r,timestamp:(new Date).toISOString()})})),_(new Date),L([]),T(new Map),setTimeout((function(){return x(!1)}),500))}),h),[o,s,u,b,B,h]),N=(0,a.useCallback)((0,l.throttle)((function(e,t){var n=R.current.get(e)||{};R.current.set(e,d(d({},n),t)),_(new Date)}),f),[f]),W=(0,a.useCallback)((function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(e){var o=d(d({},O.get(e)||{}),t);T((function(t){return new Map(t.set(e,o))})),L((function(t){return[].concat((0,r.A)(t.filter((function(t){return!("update"===t.type&&t.componentId===e)}))),[{type:"update",componentId:e,data:o}])})),n&&N(e,t),z(M)}}),[O,M,z,N]),H=(0,a.useCallback)((function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.id||Date.now().toString(),o=d(d({},e),{},{id:n});return L((function(e){return[].concat((0,r.A)(e),[{type:"add",componentId:n,data:o}])})),t&&(R.current.set(n,o),_(new Date)),z(M),n}),[M,z]),U=(0,a.useCallback)((function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];e&&(L((function(t){return[].concat((0,r.A)(t),[{type:"delete",componentId:e}])})),t&&(R.current.delete(e),_(new Date)),z(M))}),[M,z]),Q=(0,a.useCallback)((function(e){var t=n.find((function(t){return t.id===e})),r=R.current.get(e),o=O.get(e);return d(d(d({},t),r),o)}),[n,O]),K=(0,a.useCallback)((function(){return n.map((function(e){return Q(e.id)}))}),[n,Q]),G=(0,a.useCallback)((function(){z.flush(),R.current.clear(),T(new Map),L([])}),[z]);return(0,a.useEffect)((function(){if(b&&F.current){var e=function(e){var t;if(null!==(t=e.type)&&void 0!==t&&t.startsWith("component_")){var n=e.component_id,r=e.component_data,o=e.timestamp;r&&n&&(R.current.set(n,r),_(new Date(o)))}};return F.current.addEventListener("message",e),function(){F.current&&F.current.removeEventListener("message",e)}}}),[b]),(0,a.useEffect)((function(){return function(){j.current&&clearTimeout(j.current),z.cancel(),N.cancel()}}),[z,N]),{isUpdating:E,lastUpdateTime:S,websocketConnected:B,hasPendingUpdates:O.size>0,updateComponent:W,addComponent:H,deleteComponent:U,getComponent:Q,getAllComponents:K,forceUpdate:G,clearCache:function(){return R.current.clear()},getPendingUpdates:function(){return Array.from(O.entries())},getUpdateQueueSize:function(){return M.length}}}},81415:(e,t,n)=>{n(60436),n(5544),n(96540)},82569:(e,t,n)=>{n.d(t,{ZV:()=>d,fx:()=>s});var r=n(5544),o=n(96540),i=n(1807),a=(0,o.createContext)({isDarkMode:!1,themeMode:"light",toggleDarkMode:function(){},setThemeMode:function(){},colors:{},systemPrefersDark:!1}),c={primary:"#1890ff",primaryHover:"#40a9ff",secondary:"#52c41a",background:"#ffffff",backgroundSecondary:"#f5f5f5",backgroundTertiary:"#fafafa",surface:"#ffffff",text:"#000000d9",textSecondary:"#00000073",textTertiary:"#00000040",border:"#d9d9d9",borderLight:"#f0f0f0",shadow:"rgba(0, 0, 0, 0.1)",shadowLight:"rgba(0, 0, 0, 0.05)",success:"#52c41a",warning:"#faad14",error:"#ff4d4f",info:"#1890ff"},l={primary:"#1890ff",primaryHover:"#40a9ff",secondary:"#52c41a",background:"#141414",backgroundSecondary:"#1f1f1f",backgroundTertiary:"#262626",surface:"#1f1f1f",text:"#ffffffd9",textSecondary:"#ffffff73",textTertiary:"#ffffff40",border:"#434343",borderLight:"#303030",shadow:"rgba(0, 0, 0, 0.3)",shadowLight:"rgba(0, 0, 0, 0.2)",success:"#52c41a",warning:"#faad14",error:"#ff4d4f",info:"#1890ff"},s=function(e){var t=e.children,n=(0,o.useState)((function(){var e=localStorage.getItem("app-theme-mode");return e&&["light","dark","system"].includes(e)?e:"system"})),s=(0,r.A)(n,2),d=s[0],u=s[1],p=(0,o.useState)((function(){return window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches})),m=(0,r.A)(p,2),h=m[0],v=m[1],f="dark"===d||"system"===d&&h,g=f?l:c;(0,o.useEffect)((function(){var e=window.matchMedia("(prefers-color-scheme: dark)"),t=function(e){v(e.matches)};return e.addEventListener("change",t),function(){return e.removeEventListener("change",t)}}),[]),(0,o.useEffect)((function(){var e=document.documentElement;Object.entries(g).forEach((function(t){var n=(0,r.A)(t,2),o=n[0],i=n[1];e.style.setProperty("--color-".concat(o),i)})),e.setAttribute("data-theme",f?"dark":"light"),f?document.body.classList.add("dark-theme"):document.body.classList.remove("dark-theme");var t=document.querySelector('meta[name="theme-color"]');t&&t.setAttribute("content",g.primary)}),[g,f]),(0,o.useEffect)((function(){localStorage.setItem("app-theme-mode",d)}),[d]);var b=(0,o.useCallback)((function(){u((function(e){return"system"===e?h?"light":"dark":"light"===e?"dark":"light"}))}),[h]),y=(0,o.useCallback)((function(e){["light","dark","system"].includes(e)&&u(e)}),[]),w={algorithm:f?i.w4.darkAlgorithm:i.w4.defaultAlgorithm,token:{colorPrimary:g.primary,colorSuccess:g.success,colorWarning:g.warning,colorError:g.error,colorInfo:g.info,colorBgBase:g.background,colorBgContainer:g.surface,colorText:g.text,colorTextSecondary:g.textSecondary,colorBorder:g.border,borderRadius:6,wireframe:!1},components:{Layout:{bodyBg:g.background,headerBg:g.surface,footerBg:g.surface},Card:{colorBgContainer:g.surface},Menu:{colorBgContainer:g.surface}}},E={isDarkMode:f,themeMode:d,toggleDarkMode:b,setThemeMode:y,colors:g,systemPrefersDark:h};return o.createElement(a.Provider,{value:E},o.createElement(i.sG,{theme:w},t))},d=function(){var e=(0,o.useContext)(a);if(!e)throw new Error("useEnhancedTheme must be used within an EnhancedThemeProvider");return e}},85331:(e,t,n)=>{n.d(t,{tI:()=>r}),n(64467);var r=function(e){return{type:"SET_CURRENT_VIEW",payload:e}}}}]);