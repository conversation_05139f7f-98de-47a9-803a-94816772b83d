"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[5831],{65831:(e,r,t)=>{t.r(r),t.d(r,{default:()=>I});var a,s,l,n,i,m=t(10467),u=t(5544),o=t(57528),c=t(54756),d=t.n(c),g=t(96540),h=t(1807),p=t(35346),f=t(11080),E=t(49391),w=t(63710),v=t(70572),x=(t(57749),h.o5.Title),P=h.o5.Text,A=(h.o5.Paragraph,v.Ay.div(a||(a=(0,o.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: calc(100vh - 200px);\n  padding: 24px;\n"])))),b=(0,v.Ay)(h.Zp)(s||(s=(0,o.A)(["\n  width: 100%;\n  max-width: 500px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n"]))),y=(0,v.Ay)(h.lV)(l||(l=(0,o.A)(["\n  .ant-form-item-label {\n    text-align: left;\n  }\n"]))),q=(0,v.Ay)(h.$n)(n||(n=(0,o.A)(["\n  width: 100%;\n"]))),N=(0,v.Ay)(P)(i||(i=(0,o.A)(["\n  display: block;\n  text-align: center;\n  margin-top: 16px;\n"])));const I=function(){var e=(0,E.As)().register,r=(0,f.Zp)(),t=(0,w.Bd)().t,a=(0,g.useState)(!1),s=(0,u.A)(a,2),l=s[0],n=s[1],i=(0,g.useState)(null),o=(0,u.A)(i,2),c=o[0],v=o[1],P=function(){var t=(0,m.A)(d().mark((function t(a){return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n(!0),v(null),t.prev=2,t.next=5,e({username:a.username,email:a.email,password:a.password,firstName:a.firstName,lastName:a.lastName,phone:a.phone});case 5:r("/",{replace:!0}),t.next=11;break;case 8:t.prev=8,t.t0=t.catch(2),v(t.t0.message||"Registration failed. Please try again.");case 11:return t.prev=11,n(!1),t.finish(11);case 14:case"end":return t.stop()}}),t,null,[[2,8,11,14]])})));return function(e){return t.apply(this,arguments)}}();return g.createElement(A,null,g.createElement(b,null,g.createElement(x,{level:2,style:{textAlign:"center",marginBottom:"24px"}},t("auth.register.title")),c&&g.createElement(h.Fc,{message:t("auth.register.error"),description:c,type:"error",showIcon:!0,style:{marginBottom:"24px"}}),g.createElement(y,{name:"register",onFinish:P,layout:"vertical",scrollToFirstError:!0},g.createElement(h.lV.Item,{label:t("auth.register.username"),name:"username",rules:[{required:!0,message:t("auth.register.usernameRequired")},{min:3,message:t("auth.register.usernameTooShort")},{max:20,message:t("auth.register.usernameTooLong")},{pattern:/^[a-zA-Z0-9_]+$/,message:t("auth.register.usernameInvalid")}]},g.createElement(h.pd,{prefix:g.createElement(p.qmv,null),placeholder:t("auth.register.usernamePlaceholder"),size:"large"})),g.createElement(h.lV.Item,{label:t("auth.register.email"),name:"email",rules:[{required:!0,message:t("auth.register.emailRequired")},{type:"email",message:t("auth.register.emailInvalid")}]},g.createElement(h.pd,{prefix:g.createElement(p._Wu,null),placeholder:t("auth.register.emailPlaceholder"),size:"large"})),g.createElement(h.lV.Item,{label:t("auth.register.password"),name:"password",rules:[{required:!0,message:t("auth.register.passwordRequired")},{min:8,message:t("auth.register.passwordTooShort")},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/,message:t("auth.register.passwordInvalid")}],hasFeedback:!0},g.createElement(h.pd.Password,{prefix:g.createElement(p.sXv,null),placeholder:t("auth.register.passwordPlaceholder"),size:"large"})),g.createElement(h.lV.Item,{label:t("auth.register.confirmPassword"),name:"confirmPassword",dependencies:["password"],hasFeedback:!0,rules:[{required:!0,message:t("auth.register.confirmPasswordRequired")},function(e){var r=e.getFieldValue;return{validator:function(e,a){return a&&r("password")!==a?Promise.reject(new Error(t("auth.register.passwordsMismatch"))):Promise.resolve()}}}]},g.createElement(h.pd.Password,{prefix:g.createElement(p.sXv,null),placeholder:t("auth.register.confirmPasswordPlaceholder"),size:"large"})),g.createElement(h.lV.Item,{label:t("auth.register.firstName"),name:"firstName",rules:[{required:!0,message:t("auth.register.firstNameRequired")}]},g.createElement(h.pd,{placeholder:t("auth.register.firstNamePlaceholder"),size:"large"})),g.createElement(h.lV.Item,{label:t("auth.register.lastName"),name:"lastName",rules:[{required:!0,message:t("auth.register.lastNameRequired")}]},g.createElement(h.pd,{placeholder:t("auth.register.lastNamePlaceholder"),size:"large"})),g.createElement(h.lV.Item,{label:t("auth.register.phone"),name:"phone",rules:[{required:!0,message:t("auth.register.phoneRequired")},{pattern:/^\+?[0-9]{10,15}$/,message:t("auth.register.phoneInvalid")}]},g.createElement(h.pd,{prefix:g.createElement(p.NW5,null),placeholder:t("auth.register.phonePlaceholder"),size:"large"})),g.createElement(h.lV.Item,{name:"agreement",valuePropName:"checked",rules:[{validator:function(e,r){return r?Promise.resolve():Promise.reject(new Error(t("auth.register.agreementRequired")))}}]},g.createElement(h.Sc,null,t("auth.register.agreement")," ",g.createElement(f.N_,{to:"/terms"},t("auth.register.terms")))),g.createElement(h.lV.Item,null,g.createElement(q,{type:"primary",htmlType:"submit",size:"large",loading:l},t("auth.register.submit")))),g.createElement(N,null,t("auth.register.haveAccount")," ",g.createElement(f.N_,{to:"/login"},t("auth.register.login")))))}}}]);