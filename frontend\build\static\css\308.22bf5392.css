.app-builder-page {
  padding: 20px;
  position: relative;
}

.connection-status {
  position: absolute;
  top: 10px;
  right: 20px;
  font-size: 14px;
}

.status-connected {
  color: #52c41a;
}

.status-disconnected {
  color: #ff4d4f;
}

.component-list,
.layout-list,
.style-list,
.data-list {
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.component-item,
.layout-item,
.style-item,
.data-item {
  padding: 8px;
  margin-bottom: 4px;
  border-radius: 4px;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: background-color 0.3s;
}

.component-item:hover,
.layout-item:hover,
.style-item:hover,
.data-item:hover {
  background-color: #f0f0f0;
}

.component-item.selected {
  background-color: #e6f7ff;
  border: 1px solid #1890ff;
}

.component-props,
.layout-components,
.layout-styles {
  font-size: 12px;
  color: #666;
}

.component-buttons,
.layout-buttons,
.style-buttons {
  margin-bottom: 24px;
}

.component-buttons button,
.layout-buttons button,
.style-buttons button {
  margin-right: 8px;
  margin-bottom: 8px;
}

.data-form {
  margin-top: 16px;
}

.action-buttons {
  margin-top: 24px;
  display: flex;
  gap: 16px;
}

.generated-image {
  margin-top: 16px;
  border: 1px solid #f0f0f0;
  padding: 8px;
  border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-builder-page {
    padding: 10px;
  }
  
  .connection-status {
    position: static;
    margin-bottom: 16px;
  }
}

