"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[6488],{28792:(e,t,n)=>{n.d(t,{A:()=>Ae});var r=n(18877),o=n(94241),a=n(60436),l=n(96540),i=n(46942),s=n.n(i),c=n(57557),d=n(23723),u=n(20934);function m(e){const[t,n]=l.useState(e);return l.useEffect((()=>{const t=setTimeout((()=>{n(e)}),e.length?0:10);return()=>{clearTimeout(t)}}),[e]),t}var p=n(36891),f=n(25905),g=n(38328),b=n(51113);const h=e=>{const{componentCls:t}=e,n=`${t}-show-help`,r=`${t}-show-help-item`;return{[n]:{transition:`opacity ${e.motionDurationFast} ${e.motionEaseInOut}`,"&-appear, &-enter":{opacity:0,"&-active":{opacity:1}},"&-leave":{opacity:1,"&-active":{opacity:0}},[r]:{overflow:"hidden",transition:`height ${e.motionDurationFast} ${e.motionEaseInOut},\n                     opacity ${e.motionDurationFast} ${e.motionEaseInOut},\n                     transform ${e.motionDurationFast} ${e.motionEaseInOut} !important`,[`&${r}-appear, &${r}-enter`]:{transform:"translateY(-5px)",opacity:0,"&-active":{transform:"translateY(0)",opacity:1}},[`&${r}-leave-active`]:{transform:"translateY(-5px)"}}}}},y=e=>({legend:{display:"block",width:"100%",marginBottom:e.marginLG,padding:0,color:e.colorTextDescription,fontSize:e.fontSizeLG,lineHeight:"inherit",border:0,borderBottom:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},'input[type="search"]':{boxSizing:"border-box"},'input[type="radio"], input[type="checkbox"]':{lineHeight:"normal"},'input[type="file"]':{display:"block"},'input[type="range"]':{display:"block",width:"100%"},"select[multiple], select[size]":{height:"auto"},"input[type='file']:focus,\n  input[type='radio']:focus,\n  input[type='checkbox']:focus":{outline:0,boxShadow:`0 0 0 ${(0,p.zA)(e.controlOutlineWidth)} ${e.controlOutline}`},output:{display:"block",paddingTop:15,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight}}),$=(e,t)=>{const{formItemCls:n}=e;return{[n]:{[`${n}-label > label`]:{height:t},[`${n}-control-input`]:{minHeight:t}}}},v=e=>{const{componentCls:t}=e;return{[e.componentCls]:Object.assign(Object.assign(Object.assign({},(0,f.dF)(e)),y(e)),{[`${t}-text`]:{display:"inline-block",paddingInlineEnd:e.paddingSM},"&-small":Object.assign({},$(e,e.controlHeightSM)),"&-large":Object.assign({},$(e,e.controlHeightLG))})}},x=e=>{const{formItemCls:t,iconCls:n,rootPrefixCls:r,antCls:o,labelRequiredMarkColor:a,labelColor:l,labelFontSize:i,labelHeight:s,labelColonMarginInlineStart:c,labelColonMarginInlineEnd:d,itemMarginBottom:u}=e;return{[t]:Object.assign(Object.assign({},(0,f.dF)(e)),{marginBottom:u,verticalAlign:"top","&-with-help":{transition:"none"},[`&-hidden,\n        &-hidden${o}-row`]:{display:"none"},"&-has-warning":{[`${t}-split`]:{color:e.colorError}},"&-has-error":{[`${t}-split`]:{color:e.colorWarning}},[`${t}-label`]:{flexGrow:0,overflow:"hidden",whiteSpace:"nowrap",textAlign:"end",verticalAlign:"middle","&-left":{textAlign:"start"},"&-wrap":{overflow:"unset",lineHeight:e.lineHeight,whiteSpace:"unset","> label":{verticalAlign:"middle",textWrap:"balance"}},"> label":{position:"relative",display:"inline-flex",alignItems:"center",maxWidth:"100%",height:s,color:l,fontSize:i,[`> ${n}`]:{fontSize:e.fontSize,verticalAlign:"top"},[`&${t}-required`]:{"&::before":{display:"inline-block",marginInlineEnd:e.marginXXS,color:a,fontSize:e.fontSize,fontFamily:"SimSun, sans-serif",lineHeight:1,content:'"*"'},[`&${t}-required-mark-hidden, &${t}-required-mark-optional`]:{"&::before":{display:"none"}}},[`${t}-optional`]:{display:"inline-block",marginInlineStart:e.marginXXS,color:e.colorTextDescription,[`&${t}-required-mark-hidden`]:{display:"none"}},[`${t}-tooltip`]:{color:e.colorTextDescription,cursor:"help",writingMode:"horizontal-tb",marginInlineStart:e.marginXXS},"&::after":{content:'":"',position:"relative",marginBlock:0,marginInlineStart:c,marginInlineEnd:d},[`&${t}-no-colon::after`]:{content:'"\\a0"'}}},[`${t}-control`]:{"--ant-display":"flex",flexDirection:"column",flexGrow:1,[`&:first-child:not([class^="'${r}-col-'"]):not([class*="' ${r}-col-'"])`]:{width:"100%"},"&-input":{position:"relative",display:"flex",alignItems:"center",minHeight:e.controlHeight,"&-content":{flex:"auto",maxWidth:"100%"}}},[t]:{"&-additional":{display:"flex",flexDirection:"column"},"&-explain, &-extra":{clear:"both",color:e.colorTextDescription,fontSize:e.fontSize,lineHeight:e.lineHeight},"&-explain-connected":{width:"100%"},"&-extra":{minHeight:e.controlHeightSM,transition:`color ${e.motionDurationMid} ${e.motionEaseOut}`},"&-explain":{"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning}}},[`&-with-help ${t}-explain`]:{height:"auto",opacity:1},[`${t}-feedback-icon`]:{fontSize:e.fontSize,textAlign:"center",visibility:"visible",animationName:g.nF,animationDuration:e.motionDurationMid,animationTimingFunction:e.motionEaseOutBack,pointerEvents:"none","&-success":{color:e.colorSuccess},"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning},"&-validating":{color:e.colorPrimary}}})}},O=(e,t)=>{const{formItemCls:n}=e;return{[`${t}-horizontal`]:{[`${n}-label`]:{flexGrow:0},[`${n}-control`]:{flex:"1 1 0",minWidth:0},[`${n}-label[class$='-24'], ${n}-label[class*='-24 ']`]:{[`& + ${n}-control`]:{minWidth:"unset"}}}}},w=e=>{const{componentCls:t,formItemCls:n,inlineItemMarginBottom:r}=e;return{[`${t}-inline`]:{display:"flex",flexWrap:"wrap",[n]:{flex:"none",marginInlineEnd:e.margin,marginBottom:r,"&-row":{flexWrap:"nowrap"},[`> ${n}-label,\n        > ${n}-control`]:{display:"inline-block",verticalAlign:"top"},[`> ${n}-label`]:{flex:"none"},[`${t}-text`]:{display:"inline-block"},[`${n}-has-feedback`]:{display:"inline-block"}}}}},C=e=>({padding:e.verticalLabelPadding,margin:e.verticalLabelMargin,whiteSpace:"initial",textAlign:"start","> label":{margin:0,"&::after":{visibility:"hidden"}}}),E=e=>{const{componentCls:t,formItemCls:n,rootPrefixCls:r}=e;return{[`${n} ${n}-label`]:C(e),[`${t}:not(${t}-inline)`]:{[n]:{flexWrap:"wrap",[`${n}-label, ${n}-control`]:{[`&:not([class*=" ${r}-col-xs"])`]:{flex:"0 0 100%",maxWidth:"100%"}}}}}},S=e=>{const{componentCls:t,formItemCls:n,antCls:r}=e;return{[`${t}-vertical`]:{[`${n}:not(${n}-horizontal)`]:{[`${n}-row`]:{flexDirection:"column"},[`${n}-label > label`]:{height:"auto"},[`${n}-control`]:{width:"100%"},[`${n}-label,\n        ${r}-col-24${n}-label,\n        ${r}-col-xl-24${n}-label`]:C(e)}},[`@media (max-width: ${(0,p.zA)(e.screenXSMax)})`]:[E(e),{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-xs-24${n}-label`]:C(e)}}}],[`@media (max-width: ${(0,p.zA)(e.screenSMMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-sm-24${n}-label`]:C(e)}}},[`@media (max-width: ${(0,p.zA)(e.screenMDMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-md-24${n}-label`]:C(e)}}},[`@media (max-width: ${(0,p.zA)(e.screenLGMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-lg-24${n}-label`]:C(e)}}}}},I=e=>{const{formItemCls:t,antCls:n}=e;return{[`${t}-vertical`]:{[`${t}-row`]:{flexDirection:"column"},[`${t}-label > label`]:{height:"auto"},[`${t}-control`]:{width:"100%"}},[`${t}-vertical ${t}-label,\n      ${n}-col-24${t}-label,\n      ${n}-col-xl-24${t}-label`]:C(e),[`@media (max-width: ${(0,p.zA)(e.screenXSMax)})`]:[E(e),{[t]:{[`${n}-col-xs-24${t}-label`]:C(e)}}],[`@media (max-width: ${(0,p.zA)(e.screenSMMax)})`]:{[t]:{[`${n}-col-sm-24${t}-label`]:C(e)}},[`@media (max-width: ${(0,p.zA)(e.screenMDMax)})`]:{[t]:{[`${n}-col-md-24${t}-label`]:C(e)}},[`@media (max-width: ${(0,p.zA)(e.screenLGMax)})`]:{[t]:{[`${n}-col-lg-24${t}-label`]:C(e)}}}},j=(e,t)=>(0,b.oX)(e,{formItemCls:`${e.componentCls}-item`,rootPrefixCls:t}),k=(0,b.OF)("Form",((e,{rootPrefixCls:t})=>{const n=j(e,t);return[v(n),x(n),h(n),O(n,n.componentCls),O(n,n.formItemCls),w(n),S(n),I(n),(0,g.eG)(n),g.nF]}),(e=>({labelRequiredMarkColor:e.colorError,labelColor:e.colorTextHeading,labelFontSize:e.fontSize,labelHeight:e.controlHeight,labelColonMarginInlineStart:e.marginXXS/2,labelColonMarginInlineEnd:e.marginXS,itemMarginBottom:e.marginLG,verticalLabelPadding:`0 0 ${e.paddingXS}px`,verticalLabelMargin:0,inlineItemMarginBottom:0})),{order:-1e3}),A=[];function F(e,t,n,r=0){return{key:"string"==typeof e?e:`${t}-${r}`,error:e,errorStatus:n}}const M=({help:e,helpStatus:t,errors:n=A,warnings:r=A,className:i,fieldId:p,onVisibleChanged:f})=>{const{prefixCls:g}=l.useContext(o.hb),b=`${g}-item-explain`,h=(0,u.A)(g),[y,$,v]=k(g,h),x=l.useMemo((()=>(0,d.A)(g)),[g]),O=m(n),w=m(r),C=l.useMemo((()=>null!=e?[F(e,"help",t)]:[].concat((0,a.A)(O.map(((e,t)=>F(e,"error","error",t)))),(0,a.A)(w.map(((e,t)=>F(e,"warning","warning",t)))))),[e,t,O,w]),E=l.useMemo((()=>{const e={};return C.forEach((({key:t})=>{e[t]=(e[t]||0)+1})),C.map(((t,n)=>Object.assign(Object.assign({},t),{key:e[t.key]>1?`${t.key}-fallback-${n}`:t.key})))}),[C]),S={};return p&&(S.id=`${p}_help`),y(l.createElement(c.Ay,{motionDeadline:x.motionDeadline,motionName:`${g}-show-help`,visible:!!E.length,onVisibleChanged:f},(e=>{const{className:t,style:n}=e;return l.createElement("div",Object.assign({},S,{className:s()(b,t,v,h,i,$),style:n}),l.createElement(c.aF,Object.assign({keys:E},(0,d.A)(g),{motionName:`${g}-show-help-item`,component:!1}),(e=>{const{key:t,error:n,errorStatus:r,className:o,style:a}=e;return l.createElement("div",{key:t,className:s()(o,{[`${b}-${r}`]:r}),style:a},n)})))})))};var z=n(11523),B=n(62279),P=n(98119),N=n(829),q=n(48224),R=n(66588),_=n(68499);const T=["parentNode"];function L(e){return void 0===e||!1===e?[]:Array.isArray(e)?e:[e]}function H(e,t){if(!e.length)return;const n=e.join("_");return t?`${t}_${n}`:T.includes(n)?`form_item_${n}`:n}function D(e,t,n,r,o,a){let l=r;return void 0!==a?l=a:n.validating?l="validating":e.length?l="error":t.length?l="warning":(n.touched||o&&n.validated)&&(l="success"),l}function W(e){return L(e).join("_")}function G(e,t){const n=t.getFieldInstance(e),r=(0,R.rb)(n);if(r)return r;const o=H(L(e),t.__INTERNAL__.name);return o?document.getElementById(o):void 0}function X(e){const[t]=(0,z.mN)(),n=l.useRef({}),r=l.useMemo((()=>null!=e?e:Object.assign(Object.assign({},t),{__INTERNAL__:{itemRef:e=>t=>{const r=W(e);t?n.current[r]=t:delete n.current[r]}},scrollToField:(e,t={})=>{const{focus:n}=t,o=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(t,["focus"]),a=G(e,r);a&&((0,_.A)(a,Object.assign({scrollMode:"if-needed",block:"nearest"},o)),n&&r.focusField(e))},focusField:e=>{var t,n;const o=r.getFieldInstance(e);"function"==typeof(null==o?void 0:o.focus)?o.focus():null===(n=null===(t=G(e,r))||void 0===t?void 0:t.focus)||void 0===n||n.call(t)},getFieldInstance:e=>{const t=W(e);return n.current[t]}})),[e,t]);return[r]}var K=n(69407);const V=(e,t)=>{const n=l.useContext(P.A),{getPrefixCls:r,direction:a,requiredMark:i,colon:c,scrollToFirstError:d,className:m,style:p}=(0,B.TP)("form"),{prefixCls:f,className:g,rootClassName:b,size:h,disabled:y=n,form:$,colon:v,labelAlign:x,labelWrap:O,labelCol:w,wrapperCol:C,hideRequiredMark:E,layout:S="horizontal",scrollToFirstError:I,requiredMark:j,onFinishFailed:A,name:F,style:M,feedbackIcons:R,variant:_}=e,T=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["prefixCls","className","rootClassName","size","disabled","form","colon","labelAlign","labelWrap","labelCol","wrapperCol","hideRequiredMark","layout","scrollToFirstError","requiredMark","onFinishFailed","name","style","feedbackIcons","variant"]),L=(0,N.A)(h),H=l.useContext(K.A),D=l.useMemo((()=>void 0!==j?j:!E&&(void 0===i||i)),[E,j,i]),W=null!=v?v:c,G=r("form",f),V=(0,u.A)(G),[Y,Q,J]=k(G,V),U=s()(G,`${G}-${S}`,{[`${G}-hide-required-mark`]:!1===D,[`${G}-rtl`]:"rtl"===a,[`${G}-${L}`]:L},J,V,Q,m,g,b),[Z]=X($),{__INTERNAL__:ee}=Z;ee.name=F;const te=l.useMemo((()=>({name:F,labelAlign:x,labelCol:w,labelWrap:O,wrapperCol:C,vertical:"vertical"===S,colon:W,requiredMark:D,itemRef:ee.itemRef,form:Z,feedbackIcons:R})),[F,x,w,C,S,W,D,Z,R]),ne=l.useRef(null);l.useImperativeHandle(t,(()=>{var e;return Object.assign(Object.assign({},Z),{nativeElement:null===(e=ne.current)||void 0===e?void 0:e.nativeElement})}));const re=(e,t)=>{if(e){let n={block:"nearest"};"object"==typeof e&&(n=Object.assign(Object.assign({},n),e)),Z.scrollToField(t,n)}};return Y(l.createElement(o.Pp.Provider,{value:_},l.createElement(P.X,{disabled:y},l.createElement(q.A.Provider,{value:L},l.createElement(o.Op,{validateMessages:H},l.createElement(o.cK.Provider,{value:te},l.createElement(z.Ay,Object.assign({id:F},T,{name:F,onFinishFailed:e=>{if(null==A||A(e),e.errorFields.length){const t=e.errorFields[0].name;if(void 0!==I)return void re(I,t);void 0!==d&&re(d,t)}},form:Z,ref:ne,style:Object.assign(Object.assign({},p),M),className:U}))))))))},Y=l.forwardRef(V);var Q=n(1233),J=n(8719),U=n(40682),Z=n(38674),ee=n(82546);const te=()=>{const{status:e,errors:t=[],warnings:n=[]}=l.useContext(o.$W);return{status:e,errors:t,warnings:n}};te.Context=o.$W;const ne=te;var re=n(25371),oe=n(42467),ae=n(30981),le=n(19853),ie=n(36768),se=n(81470),ce=n(26606);const de=e=>{const{formItemCls:t}=e;return{"@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none)":{[`${t}-control`]:{display:"flex"}}}},ue=(0,b.bf)(["Form","item-item"],((e,{rootPrefixCls:t})=>{const n=j(e,t);return[de(n)]}));const me=e=>{const{prefixCls:t,status:n,labelCol:r,wrapperCol:a,children:i,errors:c,warnings:d,_internalItemRender:u,extra:m,help:p,fieldId:f,marginBottom:g,onErrorVisibleChanged:b,label:h}=e,y=`${t}-item`,$=l.useContext(o.cK),v=l.useMemo((()=>{let e=Object.assign({},a||$.wrapperCol||{});return null!==h||r||a||!$.labelCol||[void 0,"xs","sm","md","lg","xl","xxl"].forEach((t=>{const n=t?[t]:[],r=(0,se.Jt)($.labelCol,n),o="object"==typeof r?r:{},a=(0,se.Jt)(e,n);"span"in o&&!("offset"in("object"==typeof a?a:{}))&&o.span<24&&(e=(0,se.hZ)(e,[].concat(n,["offset"]),o.span))})),e}),[a,$]),x=s()(`${y}-control`,v.className),O=l.useMemo((()=>{const{labelCol:e,wrapperCol:t}=$;return function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}($,["labelCol","wrapperCol"])}),[$]),w=l.useRef(null),[C,E]=l.useState(0);(0,ae.A)((()=>{m&&w.current?E(w.current.clientHeight):E(0)}),[m]);const S=l.createElement("div",{className:`${y}-control-input`},l.createElement("div",{className:`${y}-control-input-content`},i)),I=l.useMemo((()=>({prefixCls:t,status:n})),[t,n]),j=null!==g||c.length||d.length?l.createElement(o.hb.Provider,{value:I},l.createElement(M,{fieldId:f,errors:c,warnings:d,help:p,helpStatus:n,className:`${y}-explain-connected`,onVisibleChanged:b})):null,k={};f&&(k.id=`${f}_extra`);const A=m?l.createElement("div",Object.assign({},k,{className:`${y}-extra`,ref:w}),m):null,F=j||A?l.createElement("div",{className:`${y}-additional`,style:g?{minHeight:g+C}:{}},j,A):null,z=u&&"pro_table_render"===u.mark&&u.render?u.render(e,{input:S,errorList:j,extra:A}):l.createElement(l.Fragment,null,S,F);return l.createElement(o.cK.Provider,{value:O},l.createElement(ce.A,Object.assign({},v,{className:x}),z),l.createElement(ue,{prefixCls:t}))};var pe=n(86944),fe=n(64080),ge=n(21282),be=n(8182),he=n(37977);const ye=({prefixCls:e,label:t,htmlFor:n,labelCol:r,labelAlign:a,colon:i,required:c,requiredMark:d,tooltip:u,vertical:m})=>{var p;const[f]=(0,ge.Ym)("Form"),{labelAlign:g,labelCol:b,labelWrap:h,colon:y}=l.useContext(o.cK);if(!t)return null;const $=r||b||{},v=a||g,x=`${e}-item-label`,O=s()(x,"left"===v&&`${x}-left`,$.className,{[`${x}-wrap`]:!!h});let w=t;const C=!0===i||!1!==y&&!1!==i;C&&!m&&"string"==typeof t&&t.trim()&&(w=t.replace(/[:|：]\s*$/,""));const E=(0,fe.A)(u);if(E){const{icon:t=l.createElement(pe.A,null)}=E,n=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(E,["icon"]),r=l.createElement(he.A,Object.assign({},n),l.cloneElement(t,{className:`${e}-item-tooltip`,title:"",onClick:e=>{e.preventDefault()},tabIndex:null}));w=l.createElement(l.Fragment,null,w,r)}const S="optional"===d,I="function"==typeof d,j=!1===d;let k;I?w=d(w,{required:!!c}):S&&!c&&(w=l.createElement(l.Fragment,null,w,l.createElement("span",{className:`${e}-item-optional`,title:""},(null==f?void 0:f.optional)||(null===(p=be.A.Form)||void 0===p?void 0:p.optional)))),j?k="hidden":(S||I)&&(k="optional");const A=s()({[`${e}-item-required`]:c,[`${e}-item-required-mark-${k}`]:k,[`${e}-item-no-colon`]:!C});return l.createElement(ce.A,Object.assign({},$,{className:O}),l.createElement("label",{htmlFor:n,className:A,title:"string"==typeof t?t:""},w))};var $e=n(24768),ve=n(4732),xe=n(29729),Oe=n(36962);const we={success:$e.A,warning:xe.A,error:ve.A,validating:Oe.A};function Ce({children:e,errors:t,warnings:n,hasFeedback:r,validateStatus:a,prefixCls:i,meta:c,noStyle:d}){const u=`${i}-item`,{feedbackIcons:m}=l.useContext(o.cK),p=D(t,n,c,null,!!r,a),{isFormItemInput:f,status:g,hasFeedback:b,feedbackIcon:h}=l.useContext(o.$W),y=l.useMemo((()=>{var e;let o;if(r){const a=!0!==r&&r.icons||m,i=p&&(null===(e=null==a?void 0:a({status:p,errors:t,warnings:n}))||void 0===e?void 0:e[p]),c=p&&we[p];o=!1!==i&&c?l.createElement("span",{className:s()(`${u}-feedback-icon`,`${u}-feedback-icon-${p}`)},i||l.createElement(c,null)):null}const a={status:p||"",errors:t,warnings:n,hasFeedback:!!r,feedbackIcon:o,isFormItemInput:!0};return d&&(a.status=(null!=p?p:g)||"",a.isFormItemInput=f,a.hasFeedback=!!(null!=r?r:b),a.feedbackIcon=void 0!==r?a.feedbackIcon:h),a}),[p,r,d,f,g]);return l.createElement(o.$W.Provider,{value:y},e)}function Ee(e){const{prefixCls:t,className:n,rootClassName:r,style:a,help:i,errors:c,warnings:d,validateStatus:u,meta:p,hasFeedback:f,hidden:g,children:b,fieldId:h,required:y,isRequired:$,onSubItemMetaChange:v,layout:x}=e,O=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["prefixCls","className","rootClassName","style","help","errors","warnings","validateStatus","meta","hasFeedback","hidden","children","fieldId","required","isRequired","onSubItemMetaChange","layout"]),w=`${t}-item`,{requiredMark:C,vertical:E}=l.useContext(o.cK),S=E||"vertical"===x,I=l.useRef(null),j=m(c),k=m(d),A=null!=i,F=!!(A||c.length||d.length),M=!!I.current&&(0,oe.A)(I.current),[z,B]=l.useState(null);(0,ae.A)((()=>{if(F&&I.current){const e=getComputedStyle(I.current);B(parseInt(e.marginBottom,10))}}),[F,M]);const P=((e=!1)=>D(e?j:p.errors,e?k:p.warnings,p,"",!!f,u))(),N=s()(w,n,r,{[`${w}-with-help`]:A||j.length||k.length,[`${w}-has-feedback`]:P&&f,[`${w}-has-success`]:"success"===P,[`${w}-has-warning`]:"warning"===P,[`${w}-has-error`]:"error"===P,[`${w}-is-validating`]:"validating"===P,[`${w}-hidden`]:g,[`${w}-${x}`]:x});return l.createElement("div",{className:N,style:a,ref:I},l.createElement(ie.fI,Object.assign({className:`${w}-row`},(0,le.A)(O,["_internalItemRender","colon","dependencies","extra","fieldKey","getValueFromEvent","getValueProps","htmlFor","id","initialValue","isListField","label","labelAlign","labelCol","labelWrap","messageVariables","name","normalize","noStyle","preserve","requiredMark","rules","shouldUpdate","trigger","tooltip","validateFirst","validateTrigger","valuePropName","wrapperCol","validateDebounce"])),l.createElement(ye,Object.assign({htmlFor:h},e,{requiredMark:C,required:null!=y?y:$,prefixCls:t,vertical:S})),l.createElement(me,Object.assign({},e,p,{errors:j,warnings:k,prefixCls:t,status:P,help:i,marginBottom:z,onErrorVisibleChanged:e=>{e||B(null)}}),l.createElement(o.jC.Provider,{value:v},l.createElement(Ce,{prefixCls:t,meta:p,errors:p.errors,warnings:p.warnings,hasFeedback:f,validateStatus:P},b)))),!!z&&l.createElement("div",{className:`${w}-margin-offset`,style:{marginBottom:-z}}))}const Se=l.memo((({children:e})=>e),((e,t)=>function(e,t){const n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&n.every((n=>{const r=e[n],o=t[n];return r===o||"function"==typeof r||"function"==typeof o}))}(e.control,t.control)&&e.update===t.update&&e.childProps.length===t.childProps.length&&e.childProps.every(((e,n)=>e===t.childProps[n])))),Ie=function(e){const{name:t,noStyle:n,className:i,dependencies:c,prefixCls:d,shouldUpdate:m,rules:p,children:f,required:g,label:b,messageVariables:h,trigger:y="onChange",validateTrigger:$,hidden:v,help:x,layout:O}=e,{getPrefixCls:w}=l.useContext(Z.QO),{name:C}=l.useContext(o.cK),E=function(e){if("function"==typeof e)return e;const t=(0,ee.A)(e);return t.length<=1?t[0]:t}(f),S="function"==typeof E,I=l.useContext(o.jC),{validateTrigger:j}=l.useContext(z._z),A=void 0!==$?$:j,F=!(null==t),M=w("form",d),B=(0,u.A)(M),[P,N,q]=k(M,B);(0,r.rJ)("Form.Item");const R=l.useContext(z.EF),_=l.useRef(null),[T,D]=function(){const[e,t]=l.useState({}),n=l.useRef(null),r=l.useRef([]),o=l.useRef(!1);return l.useEffect((()=>(o.current=!1,()=>{o.current=!0,re.A.cancel(n.current),n.current=null})),[]),[e,function(e){o.current||(null===n.current&&(r.current=[],n.current=(0,re.A)((()=>{n.current=null,t((e=>{let t=e;return r.current.forEach((e=>{t=e(t)})),t}))}))),r.current.push(e))}]}(),[W,G]=(0,Q.A)((()=>({errors:[],warnings:[],touched:!1,validating:!1,name:[],validated:!1}))),X=(e,t)=>{D((n=>{const r=Object.assign({},n),o=[].concat((0,a.A)(e.name.slice(0,-1)),(0,a.A)(t)).join("__SPLIT__");return e.destroy?delete r[o]:r[o]=e,r}))},[K,V]=l.useMemo((()=>{const e=(0,a.A)(W.errors),t=(0,a.A)(W.warnings);return Object.values(T).forEach((n=>{e.push.apply(e,(0,a.A)(n.errors||[])),t.push.apply(t,(0,a.A)(n.warnings||[]))})),[e,t]}),[T,W.errors,W.warnings]),Y=function(){const{itemRef:e}=l.useContext(o.cK),t=l.useRef({});return function(n,r){const o=r&&"object"==typeof r&&(0,J.A9)(r),a=n.join("_");return t.current.name===a&&t.current.originRef===o||(t.current.name=a,t.current.originRef=o,t.current.ref=(0,J.K4)(e(n),o)),t.current.ref}}();function te(t,r,o){return n&&!v?l.createElement(Ce,{prefixCls:M,hasFeedback:e.hasFeedback,validateStatus:e.validateStatus,meta:W,errors:K,warnings:V,noStyle:!0},t):l.createElement(Ee,Object.assign({key:"row"},e,{className:s()(i,q,B,N),prefixCls:M,fieldId:r,isRequired:o,errors:K,warnings:V,meta:W,onSubItemMetaChange:X,layout:O}),t)}if(!F&&!S&&!c)return P(te(E));let ne={};return"string"==typeof b?ne.label=b:t&&(ne.label=String(t)),h&&(ne=Object.assign(Object.assign({},ne),h)),P(l.createElement(z.D0,Object.assign({},e,{messageVariables:ne,trigger:y,validateTrigger:A,onMetaChange:e=>{const t=null==R?void 0:R.getKey(e.name);if(G(e.destroy?{errors:[],warnings:[],touched:!1,validating:!1,name:[],validated:!1}:e,!0),n&&!1!==x&&I){let n=e.name;if(e.destroy)n=_.current||n;else if(void 0!==t){const[e,r]=t;n=[e].concat((0,a.A)(r)),_.current=n}I(e,n)}}}),((n,r,o)=>{const i=L(t).length&&r?r.name:[],s=H(i,C),d=void 0!==g?g:!!(null==p?void 0:p.some((e=>{if(e&&"object"==typeof e&&e.required&&!e.warningOnly)return!0;if("function"==typeof e){const t=e(o);return(null==t?void 0:t.required)&&!(null==t?void 0:t.warningOnly)}return!1}))),u=Object.assign({},n);let f=null;if(Array.isArray(E)&&F)f=E;else if(S&&(!m&&!c||F));else if(!c||S||F)if(l.isValidElement(E)){const t=Object.assign(Object.assign({},E.props),u);if(t.id||(t.id=s),x||K.length>0||V.length>0||e.extra){const n=[];(x||K.length>0)&&n.push(`${s}_help`),e.extra&&n.push(`${s}_extra`),t["aria-describedby"]=n.join(" ")}K.length>0&&(t["aria-invalid"]="true"),d&&(t["aria-required"]="true"),(0,J.f3)(E)&&(t.ref=Y(i,E)),new Set([].concat((0,a.A)(L(y)),(0,a.A)(L(A)))).forEach((e=>{t[e]=(...t)=>{var n,r,o,a,l;null===(o=u[e])||void 0===o||(n=o).call.apply(n,[u].concat(t)),null===(l=(a=E.props)[e])||void 0===l||(r=l).call.apply(r,[a].concat(t))}}));const n=[t["aria-required"],t["aria-invalid"],t["aria-describedby"]];f=l.createElement(Se,{control:u,update:E,childProps:n},(0,U.Ob)(E,t))}else f=S&&(m||c)&&!F?E(o):E;return te(f,s,d)})))};Ie.useStatus=ne;const je=Ie;const ke=Y;ke.Item=je,ke.List=e=>{var{prefixCls:t,children:n}=e,r=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["prefixCls","children"]);const{getPrefixCls:a}=l.useContext(Z.QO),i=a("form",t),s=l.useMemo((()=>({prefixCls:i,status:"error"})),[i]);return l.createElement(z.B8,Object.assign({},r),((e,t,r)=>l.createElement(o.hb.Provider,{value:s},n(e.map((e=>Object.assign(Object.assign({},e),{fieldKey:e.key}))),t,{errors:r.errors,warnings:r.warnings}))))},ke.ErrorList=M,ke.useForm=X,ke.useFormInstance=function(){const{form:e}=l.useContext(o.cK);return e},ke.useWatch=z.FH,ke.Provider=o.Op,ke.create=()=>{};const Ae=ke},69407:(e,t,n)=>{n.d(t,{A:()=>r});const r=(0,n(96540).createContext)(void 0)},75245:(e,t,n)=>{n.d(t,{A:()=>X});var r=n(96540),o=n(77574),a=n(46942),l=n.n(a),i=n(57557),s=n(8719),c=n(60752),d=n(69423),u=n(32487),m=n(38674);const p=r.createContext(void 0),{Provider:f}=p,g=p;var b=n(19853),h=n(64080),y=n(60275),$=(n(18877),n(52120)),v=n(20934),x=n(37977),O=n(47953);const w=e=>{const{icon:t,description:n,prefixCls:o,className:a}=e,i=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["icon","description","prefixCls","className"]),s=r.createElement("div",{className:`${o}-icon`},r.createElement(O.A,null));return r.createElement("div",Object.assign({},i,{className:l()(a,`${o}-content`)}),t||n?r.createElement(r.Fragment,null,t&&r.createElement("div",{className:`${o}-icon`},t),n&&r.createElement("div",{className:`${o}-description`},n)):s)},C=(0,r.memo)(w);var E=n(36891),S=n(25905),I=n(28680),j=n(51113);const k=e=>0===e?0:e-Math.sqrt(Math.pow(e,2)/2);var A=n(14980);const F=e=>{const{componentCls:t,floatButtonSize:n,motionDurationSlow:r,motionEaseInOutCirc:o,calc:a}=e,l=new E.Mo("antFloatButtonMoveTopIn",{"0%":{transform:`translate3d(0, ${(0,E.zA)(n)}, 0)`,transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),i=new E.Mo("antFloatButtonMoveTopOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:`translate3d(0, ${(0,E.zA)(n)}, 0)`,transformOrigin:"0 0",opacity:0}}),s=new E.Mo("antFloatButtonMoveRightIn",{"0%":{transform:`translate3d(${(0,E.zA)(a(n).mul(-1).equal())}, 0, 0)`,transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),c=new E.Mo("antFloatButtonMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:`translate3d(${(0,E.zA)(a(n).mul(-1).equal())}, 0, 0)`,transformOrigin:"0 0",opacity:0}}),d=new E.Mo("antFloatButtonMoveBottomIn",{"0%":{transform:`translate3d(0, ${(0,E.zA)(a(n).mul(-1).equal())}, 0)`,transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),u=new E.Mo("antFloatButtonMoveBottomOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:`translate3d(0, ${(0,E.zA)(a(n).mul(-1).equal())}, 0)`,transformOrigin:"0 0",opacity:0}}),m=new E.Mo("antFloatButtonMoveLeftIn",{"0%":{transform:`translate3d(${(0,E.zA)(n)}, 0, 0)`,transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),p=new E.Mo("antFloatButtonMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:`translate3d(${(0,E.zA)(n)}, 0, 0)`,transformOrigin:"0 0",opacity:0}}),f=`${t}-group`;return[{[f]:{[`&${f}-top ${f}-wrap`]:(0,A.b)(`${f}-wrap`,l,i,r,!0),[`&${f}-bottom ${f}-wrap`]:(0,A.b)(`${f}-wrap`,d,u,r,!0),[`&${f}-left ${f}-wrap`]:(0,A.b)(`${f}-wrap`,m,p,r,!0),[`&${f}-right ${f}-wrap`]:(0,A.b)(`${f}-wrap`,s,c,r,!0)}},{[`${f}-wrap`]:{[`&${f}-wrap-enter, &${f}-wrap-appear`]:{opacity:0,animationTimingFunction:o},[`&${f}-wrap-leave`]:{opacity:1,animationTimingFunction:o}}}]},M=e=>{const{antCls:t,componentCls:n,floatButtonSize:r,margin:o,borderRadiusLG:a,borderRadiusSM:l,badgeOffset:i,floatButtonBodyPadding:s,zIndexPopupBase:c,calc:d}=e,u=`${n}-group`;return{[u]:Object.assign(Object.assign({},(0,S.dF)(e)),{zIndex:c,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",border:"none",position:"fixed",height:"auto",boxShadow:"none",minWidth:r,minHeight:r,insetInlineEnd:e.floatButtonInsetInlineEnd,bottom:e.floatButtonInsetBlockEnd,borderRadius:a,[`${u}-wrap`]:{zIndex:-1,display:"flex",justifyContent:"center",alignItems:"center",position:"absolute"},[`&${u}-rtl`]:{direction:"rtl"},[n]:{position:"static"}}),[`${u}-top > ${u}-wrap`]:{flexDirection:"column",top:"auto",bottom:d(r).add(o).equal(),"&::after":{content:'""',position:"absolute",width:"100%",height:o,bottom:d(o).mul(-1).equal()}},[`${u}-bottom > ${u}-wrap`]:{flexDirection:"column",top:d(r).add(o).equal(),bottom:"auto","&::after":{content:'""',position:"absolute",width:"100%",height:o,top:d(o).mul(-1).equal()}},[`${u}-right > ${u}-wrap`]:{flexDirection:"row",left:{_skip_check_:!0,value:d(r).add(o).equal()},right:{_skip_check_:!0,value:"auto"},"&::after":{content:'""',position:"absolute",width:o,height:"100%",left:{_skip_check_:!0,value:d(o).mul(-1).equal()}}},[`${u}-left > ${u}-wrap`]:{flexDirection:"row",left:{_skip_check_:!0,value:"auto"},right:{_skip_check_:!0,value:d(r).add(o).equal()},"&::after":{content:'""',position:"absolute",width:o,height:"100%",right:{_skip_check_:!0,value:d(o).mul(-1).equal()}}},[`${u}-circle`]:{gap:o,[`${u}-wrap`]:{gap:o}},[`${u}-square`]:{[`${n}-square`]:{padding:0,borderRadius:0,[`&${u}-trigger`]:{borderRadius:a},"&:first-child":{borderStartStartRadius:a,borderStartEndRadius:a},"&:last-child":{borderEndStartRadius:a,borderEndEndRadius:a},"&:not(:last-child)":{borderBottom:`${(0,E.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-badge`]:{[`${t}-badge-count`]:{top:d(d(s).add(i)).mul(-1).equal(),insetInlineEnd:d(d(s).add(i)).mul(-1).equal()}}},[`${u}-wrap`]:{borderRadius:a,boxShadow:e.boxShadowSecondary,[`${n}-square`]:{boxShadow:"none",borderRadius:0,padding:s,[`${n}-body`]:{width:e.floatButtonBodySize,height:e.floatButtonBodySize,borderRadius:l}}}},[`${u}-top > ${u}-wrap, ${u}-bottom > ${u}-wrap`]:{[`> ${n}-square`]:{"&:first-child":{borderStartStartRadius:a,borderStartEndRadius:a},"&:last-child":{borderEndStartRadius:a,borderEndEndRadius:a},"&:not(:last-child)":{borderBottom:`${(0,E.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}}},[`${u}-left > ${u}-wrap, ${u}-right > ${u}-wrap`]:{[`> ${n}-square`]:{"&:first-child":{borderStartStartRadius:a,borderEndStartRadius:a},"&:last-child":{borderStartEndRadius:a,borderEndEndRadius:a},"&:not(:last-child)":{borderInlineEnd:`${(0,E.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}}},[`${u}-circle-shadow`]:{boxShadow:"none"},[`${u}-square-shadow`]:{boxShadow:e.boxShadowSecondary,[`${n}-square`]:{boxShadow:"none",padding:s,[`${n}-body`]:{width:e.floatButtonBodySize,height:e.floatButtonBodySize,borderRadius:l}}}}},z=e=>{const{antCls:t,componentCls:n,floatButtonBodyPadding:r,floatButtonIconSize:o,floatButtonSize:a,borderRadiusLG:l,badgeOffset:i,dotOffsetInSquare:s,dotOffsetInCircle:c,zIndexPopupBase:d,calc:u}=e;return{[n]:Object.assign(Object.assign({},(0,S.dF)(e)),{border:"none",position:"fixed",cursor:"pointer",zIndex:d,display:"block",width:a,height:a,insetInlineEnd:e.floatButtonInsetInlineEnd,bottom:e.floatButtonInsetBlockEnd,boxShadow:e.boxShadowSecondary,"&-pure":{position:"relative",inset:"auto"},"&:empty":{display:"none"},[`${t}-badge`]:{width:"100%",height:"100%",[`${t}-badge-count`]:{transform:"translate(0, 0)",transformOrigin:"center",top:u(i).mul(-1).equal(),insetInlineEnd:u(i).mul(-1).equal()}},[`${n}-body`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",transition:`all ${e.motionDurationMid}`,[`${n}-content`]:{overflow:"hidden",textAlign:"center",minHeight:a,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:`${(0,E.zA)(u(r).div(2).equal())} ${(0,E.zA)(r)}`,[`${n}-icon`]:{textAlign:"center",margin:"auto",width:o,fontSize:o,lineHeight:1}}}}),[`${n}-rtl`]:{direction:"rtl"},[`${n}-circle`]:{height:a,borderRadius:"50%",[`${t}-badge`]:{[`${t}-badge-dot`]:{top:c,insetInlineEnd:c}},[`${n}-body`]:{borderRadius:"50%"}},[`${n}-square`]:{height:"auto",minHeight:a,borderRadius:l,[`${t}-badge`]:{[`${t}-badge-dot`]:{top:s,insetInlineEnd:s}},[`${n}-body`]:{height:"auto",borderRadius:l}},[`${n}-default`]:{backgroundColor:e.floatButtonBackgroundColor,transition:`background-color ${e.motionDurationMid}`,[`${n}-body`]:{backgroundColor:e.floatButtonBackgroundColor,transition:`background-color ${e.motionDurationMid}`,"&:hover":{backgroundColor:e.colorFillContent},[`${n}-content`]:{[`${n}-icon`]:{color:e.colorText},[`${n}-description`]:{display:"flex",alignItems:"center",lineHeight:(0,E.zA)(e.fontSizeLG),color:e.colorText,fontSize:e.fontSizeSM}}}},[`${n}-primary`]:{backgroundColor:e.colorPrimary,[`${n}-body`]:{backgroundColor:e.colorPrimary,transition:`background-color ${e.motionDurationMid}`,"&:hover":{backgroundColor:e.colorPrimaryHover},[`${n}-content`]:{[`${n}-icon`]:{color:e.colorTextLightSolid},[`${n}-description`]:{display:"flex",alignItems:"center",lineHeight:(0,E.zA)(e.fontSizeLG),color:e.colorTextLightSolid,fontSize:e.fontSizeSM}}}}}},B=(0,j.OF)("FloatButton",(e=>{const{colorTextLightSolid:t,colorBgElevated:n,controlHeightLG:r,marginXXL:o,marginLG:a,fontSize:l,fontSizeIcon:i,controlItemBgHover:s,paddingXXS:c,calc:d}=e,u=(0,j.oX)(e,{floatButtonBackgroundColor:n,floatButtonColor:t,floatButtonHoverBackgroundColor:s,floatButtonFontSize:l,floatButtonIconSize:d(i).mul(1.5).equal(),floatButtonSize:r,floatButtonInsetBlockEnd:o,floatButtonInsetInlineEnd:a,floatButtonBodySize:d(r).sub(d(c).mul(2)).equal(),floatButtonBodyPadding:c,badgeOffset:d(c).mul(1.5).equal()});return[M(u),z(u),(0,I.p9)(e),F(u)]}),(e=>({dotOffsetInCircle:k(e.controlHeightLG/2),dotOffsetInSquare:k(e.borderRadiusLG)})));const P="float-btn",N=r.forwardRef(((e,t)=>{const{prefixCls:n,className:o,rootClassName:a,style:i,type:s="default",shape:c="circle",icon:d,description:u,tooltip:p,htmlType:f="button",badge:O={}}=e,w=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["prefixCls","className","rootClassName","style","type","shape","icon","description","tooltip","htmlType","badge"]),{getPrefixCls:E,direction:S}=(0,r.useContext)(m.QO),I=(0,r.useContext)(g),j=E(P,n),k=(0,v.A)(j),[A,F,M]=B(j,k),z=I||c,N=l()(F,M,k,j,o,a,`${j}-${s}`,`${j}-${z}`,{[`${j}-rtl`]:"rtl"===S}),[q]=(0,y.YK)("FloatButton",null==i?void 0:i.zIndex),R=Object.assign(Object.assign({},i),{zIndex:q}),_=(0,b.A)(O,["title","children","status","text"]);let T=r.createElement("div",{className:`${j}-body`},r.createElement(C,{prefixCls:j,description:u,icon:d}));"badge"in e&&(T=r.createElement($.A,Object.assign({},_),T));const L=(0,h.A)(p);return L&&(T=r.createElement(x.A,Object.assign({},L),T)),A(e.href?r.createElement("a",Object.assign({ref:t},w,{className:N,style:R}),T):r.createElement("button",Object.assign({ref:t},w,{className:N,style:R,type:f}),T))})),q=N;const R=r.forwardRef(((e,t)=>{const{prefixCls:n,className:a,type:p="default",shape:f="circle",visibilityHeight:b=400,icon:h=r.createElement(o.A,null),target:y,onClick:$,duration:v=450}=e,x=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["prefixCls","className","type","shape","visibilityHeight","icon","target","onClick","duration"]),[O,w]=(0,r.useState)(0===b),C=r.useRef(null);r.useImperativeHandle(t,(()=>({nativeElement:C.current})));const E=()=>{var e;return(null===(e=C.current)||void 0===e?void 0:e.ownerDocument)||window},S=(0,u.A)((e=>{const t=(0,c.A)(e.target);w(t>=b)}));(0,r.useEffect)((()=>{const e=(y||E)();return S({target:e}),null==e||e.addEventListener("scroll",S),()=>{S.cancel(),null==e||e.removeEventListener("scroll",S)}}),[y]);const I=e=>{(0,d.A)(0,{getContainer:y||E,duration:v}),null==$||$(e)},{getPrefixCls:j}=(0,r.useContext)(m.QO),k=j(P,n),A=j(),F=(0,r.useContext)(g)||f,M=Object.assign({prefixCls:k,icon:h,type:p,shape:F},x);return r.createElement(i.Ay,{visible:O,motionName:`${A}-fade`},(({className:e},t)=>r.createElement(q,Object.assign({ref:(0,s.K4)(C,t)},M,{onClick:I,className:l()(a,e)}))))}));var _=n(55886),T=n(26956),L=n(12533),H=n(62279);const D=e=>{var t;const{prefixCls:n,className:o,style:a,shape:s="circle",type:c="default",placement:d="top",icon:u=r.createElement(O.A,null),closeIcon:m,description:p,trigger:g,children:b,onOpenChange:h,open:$,onClick:x}=e,w=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["prefixCls","className","style","shape","type","placement","icon","closeIcon","description","trigger","children","onOpenChange","open","onClick"]),{direction:C,getPrefixCls:E,closeIcon:S}=(0,H.TP)("floatButtonGroup"),I=null!==(t=null!=m?m:S)&&void 0!==t?t:r.createElement(_.A,null),j=E(P,n),k=(0,v.A)(j),[A,F,M]=B(j,k),z=`${j}-group`,N=g&&["click","hover"].includes(g),R=d&&["top","left","right","bottom"].includes(d),D=l()(z,F,M,k,o,{[`${z}-rtl`]:"rtl"===C,[`${z}-${s}`]:s,[`${z}-${s}-shadow`]:!N,[`${z}-${d}`]:N&&R}),[W]=(0,y.YK)("FloatButton",null==a?void 0:a.zIndex),G=Object.assign(Object.assign({},a),{zIndex:W}),X=l()(F,`${z}-wrap`),[K,V]=(0,L.A)(!1,{value:$}),Y=r.useRef(null),Q="hover"===g,J="click"===g,U=(0,T.A)((e=>{K!==e&&(V(e),null==h||h(e))}));return r.useEffect((()=>{if(J){const e=e=>{var t;(null===(t=Y.current)||void 0===t?void 0:t.contains(e.target))||U(!1)};return document.addEventListener("click",e,{capture:!0}),()=>document.removeEventListener("click",e,{capture:!0})}}),[J]),A(r.createElement(f,{value:s},r.createElement("div",{ref:Y,className:D,style:G,onMouseEnter:()=>{Q&&U(!0)},onMouseLeave:()=>{Q&&U(!1)}},N?r.createElement(r.Fragment,null,r.createElement(i.Ay,{visible:K,motionName:`${z}-wrap`},(({className:e})=>r.createElement("div",{className:l()(e,X)},b))),r.createElement(q,Object.assign({type:c,icon:K?I:u,description:p,"aria-label":e["aria-label"],className:`${z}-trigger`,onClick:e=>{J&&U(!K),null==x||x(e)}},w))):b)))};var W=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};const G=e=>{var{backTop:t}=e,n=W(e,["backTop"]);return t?r.createElement(R,Object.assign({},n,{visibilityHeight:0})):r.createElement(q,Object.assign({},n))};q.BackTop=R,q.Group=D,q._InternalPanelDoNotUseOrYouWillBeFired=e=>{var{className:t,items:n}=e,o=W(e,["className","items"]);const{prefixCls:a}=o,{getPrefixCls:i}=r.useContext(m.QO),s=`${i(P,a)}-pure`;return n?r.createElement(D,Object.assign({className:l()(t,s)},o),n.map(((e,t)=>r.createElement(G,Object.assign({key:t},e))))):r.createElement(G,Object.assign({className:l()(t,s)},o))};const X=q},86336:(e,t,n)=>{n(96540),n(46942),n(19853),n(42704),n(38674);var r=n(51113);const o=["wrap","nowrap","wrap-reverse"],a=["flex-start","flex-end","start","end","center","space-between","space-around","space-evenly","stretch","normal","left","right"],l=["center","start","end","flex-start","flex-end","self-start","self-end","baseline","normal","stretch"],i=e=>{const{componentCls:t}=e;return{[t]:{display:"flex",margin:0,padding:0,"&-vertical":{flexDirection:"column"},"&-rtl":{direction:"rtl"},"&:empty":{display:"none"}}}},s=e=>{const{componentCls:t}=e;return{[t]:{"&-gap-small":{gap:e.flexGapSM},"&-gap-middle":{gap:e.flexGap},"&-gap-large":{gap:e.flexGapLG}}}},c=e=>{const{componentCls:t}=e,n={};return o.forEach((e=>{n[`${t}-wrap-${e}`]={flexWrap:e}})),n},d=e=>{const{componentCls:t}=e,n={};return l.forEach((e=>{n[`${t}-align-${e}`]={alignItems:e}})),n},u=e=>{const{componentCls:t}=e,n={};return a.forEach((e=>{n[`${t}-justify-${e}`]={justifyContent:e}})),n};(0,r.OF)("Flex",(e=>{const{paddingXS:t,padding:n,paddingLG:o}=e,a=(0,r.oX)(e,{flexGapSM:t,flexGap:n,flexGapLG:o});return[i(a),s(a),c(a),d(a),u(a)]}),(()=>({})),{resetStyle:!1})},90124:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(96540),o=n(94241),a=n(38674);const l=(e,t,n=void 0)=>{var l,i;const{variant:s,[e]:c}=r.useContext(a.QO),d=r.useContext(o.Pp),u=null==c?void 0:c.variant;let m;return m=void 0!==t?t:!1===n?"borderless":null!==(i=null!==(l=null!=d?d:u)&&void 0!==l?l:s)&&void 0!==i?i:"outlined",[m,a.lJ.includes(m)]}},94241:(e,t,n)=>{n.d(t,{$W:()=>d,Op:()=>s,Pp:()=>m,XB:()=>u,cK:()=>l,hb:()=>c,jC:()=>i});var r=n(96540),o=n(11523),a=n(19853);const l=r.createContext({labelAlign:"right",vertical:!1,itemRef:()=>{}}),i=r.createContext(null),s=e=>{const t=(0,a.A)(e,["prefixCls"]);return r.createElement(o.Op,Object.assign({},t))},c=r.createContext({prefixCls:""}),d=r.createContext({}),u=({children:e,status:t,override:n})=>{const o=r.useContext(d),a=r.useMemo((()=>{const e=Object.assign({},o);return n&&delete e.isFormItemInput,t&&(delete e.status,delete e.hasFeedback,delete e.feedbackIcon),e}),[t,n,o]);return r.createElement(d.Provider,{value:a},e)},m=r.createContext(void 0)}}]);