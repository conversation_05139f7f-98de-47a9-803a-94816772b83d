"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[177],{1177:(e,t,r)=>{r.r(t),r.d(t,{default:()=>w});var n,l,a,o,c=r(7528),d=r(6540),m=r(3016),s=r(677),i=r(2702),u=r(6552),E=r(9249),g=r(2569),y=r(7683),p=r(1250),v=m.A.Title,h=m.A.Paragraph,b=m.A.Text,A=p.Ay.div(n||(n=(0,c.A)(["\n  padding: var(--spacing-lg);\n  background-color: var(--color-background);\n  min-height: 100vh;\n  transition: all 0.3s ease;\n"]))),k=(0,p.Ay)(s.A)(l||(l=(0,c.A)(["\n  margin-bottom: var(--spacing-lg);\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-md);\n    transform: translateY(-2px);\n  }\n"]))),x=p.Ay.div(a||(a=(0,c.A)(["\n  display: inline-block;\n  width: 40px;\n  height: 40px;\n  border-radius: var(--border-radius-md);\n  margin-right: var(--spacing-sm);\n  border: 1px solid var(--color-border);\n  vertical-align: middle;\n"]))),f=p.Ay.div(o||(o=(0,c.A)(["\n  background-color: var(--color-background-secondary);\n  padding: var(--spacing-md);\n  border-radius: var(--border-radius-md);\n  border: 1px solid var(--color-border-light);\n  margin: var(--spacing-md) 0;\n"])));const w=function(){var e=(0,g.ZV)(),t=e.isDarkMode,r=e.themeMode,n=e.colors,l=e.systemPrefersDark;return d.createElement(A,null,d.createElement(k,null,d.createElement(v,{level:2},"Dark Mode Test"),d.createElement(h,null,"This component tests the dark mode functionality and theme switching."),d.createElement(i.A,{size:"large",wrap:!0},d.createElement(y.A,{showDropdown:!0}),d.createElement(y.A,{showDropdown:!1})),d.createElement(u.A,null),d.createElement(f,null,d.createElement(v,{level:4},"Current Theme Information"),d.createElement(i.A,{direction:"vertical",size:"small"},d.createElement(b,null,d.createElement("strong",null,"Theme Mode:")," ",r),d.createElement(b,null,d.createElement("strong",null,"Is Dark Mode:")," ",t?"Yes":"No"),d.createElement(b,null,d.createElement("strong",null,"System Prefers Dark:")," ",l?"Yes":"No"))),d.createElement(u.A,null),d.createElement(v,{level:4},"Color Palette"),d.createElement(i.A,{direction:"vertical",size:"small"},d.createElement("div",null,d.createElement(x,{style:{backgroundColor:n.primary}}),d.createElement(b,null,"Primary: ",n.primary)),d.createElement("div",null,d.createElement(x,{style:{backgroundColor:n.secondary}}),d.createElement(b,null,"Secondary: ",n.secondary)),d.createElement("div",null,d.createElement(x,{style:{backgroundColor:n.background}}),d.createElement(b,null,"Background: ",n.background)),d.createElement("div",null,d.createElement(x,{style:{backgroundColor:n.surface}}),d.createElement(b,null,"Surface: ",n.surface)),d.createElement("div",null,d.createElement(x,{style:{backgroundColor:n.text}}),d.createElement(b,null,"Text: ",n.text))),d.createElement(u.A,null),d.createElement(v,{level:4},"Interactive Elements"),d.createElement(i.A,{wrap:!0},d.createElement(E.Ay,{type:"primary"},"Primary Button"),d.createElement(E.Ay,{type:"default"},"Default Button"),d.createElement(E.Ay,{type:"dashed"},"Dashed Button"),d.createElement(E.Ay,{type:"text"},"Text Button"),d.createElement(E.Ay,{type:"link"},"Link Button")),d.createElement(u.A,null),d.createElement(v,{level:4},"Text Variations"),d.createElement(i.A,{direction:"vertical"},d.createElement(b,null,"Default text color"),d.createElement(b,{type:"secondary"},"Secondary text color"),d.createElement(b,{type:"success"},"Success text color"),d.createElement(b,{type:"warning"},"Warning text color"),d.createElement(b,{type:"danger"},"Danger text color"),d.createElement(b,{disabled:!0},"Disabled text color"))),d.createElement(k,null,d.createElement(v,{level:3},"Nested Card Example"),d.createElement(h,null,"This card demonstrates how nested components look in the current theme."),d.createElement(s.A,{type:"inner",title:"Inner Card"},d.createElement(h,null,"This is an inner card that should adapt to the theme as well."),d.createElement(E.Ay,{type:"primary",size:"small"},"Inner Button"))))}}}]);