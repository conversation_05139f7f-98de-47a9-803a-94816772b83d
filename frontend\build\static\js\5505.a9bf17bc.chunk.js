"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[5505],{95505:(e,t,n)=>{n.r(t),n.d(t,{default:()=>ue});var r=n(60436),a=n(64467),l=n(5544),i=n(53986),o=n(57528),c=n(96540),u=n(71468),d=n(35346),s=n(1807),m=s.o5.Title,p=s.o5.Paragraph,g=s.l6.Option;const y=function(){var e=(0,c.useState)([{id:"1",name:"Header Layout",type:"flex",description:"Simple header with navigation",items:["Header","Navigation","Content"]},{id:"2",name:"Sidebar Layout",type:"grid",description:"Layout with sidebar and main content",items:["Sidebar","Main Content","Footer"]},{id:"3",name:"Card Grid",type:"grid",description:"Responsive card grid layout",items:["Card 1","Card 2","Card 3","Card 4"]}]),t=(0,l.A)(e,2),n=t[0],a=t[1],i=(0,c.useState)(""),o=(0,l.A)(i,2),u=o[0],y=o[1],f=(0,c.useState)("grid"),v=(0,l.A)(f,2),E=v[0],h=v[1],x=(0,c.useState)(null),b=(0,l.A)(x,2),S=b[0],A=b[1],C=function(e){A(e)};return c.createElement("div",{style:{padding:"20px"}},c.createElement(m,{level:3},"Layout Designer"),c.createElement(p,null,"Create and manage layout templates for your application. This is a basic version of the layout designer."),c.createElement(s.Fc,{message:"Basic Layout Designer",description:"This is a simplified version of the layout designer. It provides basic layout management functionality.",type:"info",showIcon:!0,style:{marginBottom:"24px"}}),c.createElement(s.fI,{gutter:[24,24]},c.createElement(s.fv,{xs:24,lg:12},c.createElement(s.Zp,{title:"Create New Layout",style:{marginBottom:"24px"}},c.createElement(s.$x,{direction:"vertical",style:{width:"100%"}},c.createElement(s.pd,{placeholder:"Layout name",value:u,onChange:function(e){return y(e.target.value)},prefix:c.createElement(d.hy2,null)}),c.createElement(s.l6,{value:E,onChange:h,style:{width:"100%"}},c.createElement(g,{value:"grid"},"Grid Layout"),c.createElement(g,{value:"flex"},"Flex Layout"),c.createElement(g,{value:"custom"},"Custom Layout")),c.createElement(s.$n,{type:"primary",icon:c.createElement(d.bW0,null),onClick:function(){if(u.trim()){var e={id:Date.now().toString(),name:u.trim(),type:E,description:"Custom layout",items:["Item 1","Item 2"]};a([].concat((0,r.A)(n),[e])),y(""),h("grid")}},disabled:!u.trim(),block:!0},"Add Layout"))),c.createElement(s.Zp,{title:"Layout Library"},c.createElement(s.$x,{direction:"vertical",style:{width:"100%"}},n.map((function(e){return c.createElement(s.Zp,{key:e.id,size:"small",style:{cursor:"pointer",border:(null==S?void 0:S.id)===e.id?"2px solid #1890ff":"1px solid #d9d9d9"},onClick:function(){return C(e)},actions:[c.createElement(d.xjh,{key:"edit",onClick:function(t){t.stopPropagation(),C(e)}}),c.createElement(d.SUY,{key:"delete",onClick:function(t){var r;t.stopPropagation(),r=e.id,a(n.filter((function(e){return e.id!==r}))),S&&S.id===r&&A(null)}})]},c.createElement(s.Zp.Meta,{title:e.name,description:c.createElement("div",null,c.createElement("div",null,"Type: ",e.type),c.createElement("div",null,e.description),c.createElement("div",null,"Items: ",e.items.join(", ")))}))}))))),c.createElement(s.fv,{xs:24,lg:12},c.createElement(s.Zp,{title:"Layout Preview"},S?c.createElement("div",null,c.createElement(m,{level:4},S.name),c.createElement(p,null,S.description),c.createElement("div",{style:{border:"2px dashed #d9d9d9",borderRadius:"8px",padding:"20px",minHeight:"300px",background:"#fafafa"}},c.createElement("div",{style:{display:"flex"===S.type?"flex":"grid",gridTemplateColumns:"grid"===S.type?"repeat(auto-fit, minmax(150px, 1fr))":"none",gap:"16px",height:"100%"}},S.items.map((function(e,t){return c.createElement("div",{key:t,style:{background:"white",border:"1px solid #d9d9d9",borderRadius:"4px",padding:"16px",textAlign:"center",minHeight:"80px",display:"flex",alignItems:"center",justifyContent:"center"}},e)})))),c.createElement("div",{style:{marginTop:"16px"}},c.createElement(s.$n,{type:"primary",icon:c.createElement(d.ylI,null)},"Save Layout"))):c.createElement("div",{style:{textAlign:"center",padding:"60px 20px",color:"#999"}},c.createElement(d.hy2,{style:{fontSize:"48px",marginBottom:"16px"}}),c.createElement("div",null,"Select a layout to preview"))))))};var f,v,E,h,x,b,S,A,C,w,L,k,D,O,I,j,z,R,T,P=["children"],M=["children"],W=["children"],Y=["children"],H=["children"],U=["children"],F=["children"],B=["children"],N=["children"],Z=["children"];function $(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$(Object(n),!0).forEach((function(t){(0,a.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}try{var G=n(34816);L=G.addLayout,k=G.updateLayout,D=G.removeLayout}catch(e){console.warn("Redux actions not available, using fallback"),L=function(){return{type:"ADD_LAYOUT"}},k=function(){return{type:"UPDATE_LAYOUT"}},D=function(){return{type:"REMOVE_LAYOUT"}}}var X,_,q,V,K,Q,ee,te,ne,re,ae=!0;try{var le=n(79146);O=le.styled,I=le.Button,j=le.Card,z=le.Input,R=le.Select,T=n(86020).Ay}catch(e){console.warn("Design system not available, using fallback"),ae=!1,T={spacing:["0","4px","8px","12px","16px","20px","24px","32px","48px"],colors:{primary:{main:"#1976d2",light:"#e3f2fd"},neutral:{100:"#f5f5f5",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e"},danger:{main:"#d32f2f"}},borderRadius:{md:"4px"},typography:{fontWeight:{medium:500,semibold:600},fontSize:{sm:"14px"}}}}ae&&O?(X=O.div(f||(f=(0,o.A)(["\n    display: flex;\n    flex-direction: column;\n    gap: ",";\n  "])),T.spacing[4]),_=O.div(v||(v=(0,o.A)(["\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n    gap: ",";\n  "])),T.spacing[4]),q=O.div(E||(E=(0,o.A)(["\n    display: grid;\n    grid-template-columns: repeat(12, 1fr);\n    grid-template-rows: repeat(12, 40px);\n    gap: ",";\n    background-color: ",";\n    border: 1px dashed ",";\n    border-radius: ",";\n    padding: ",";\n    min-height: 500px;\n  "])),T.spacing[2],T.colors.neutral[100],T.colors.neutral[300],T.borderRadius.md,T.spacing[4]),V=O.div(h||(h=(0,o.A)(["\n    grid-column: span ",";\n    grid-row: span ",";\n    background-color: ",";\n    border: 1px solid ",";\n    border-radius: ",";\n    padding: ",";\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n    cursor: ",";\n    opacity: ",";\n    box-shadow: ",";\n  "])),(function(e){return e.width||3}),(function(e){return e.height||2}),(function(e){return e.isPlaceholder?T.colors.primary.light:"white"}),(function(e){return e.isSelected?T.colors.primary.main:T.colors.neutral[300]}),T.borderRadius.md,T.spacing[2],(function(e){return e.isDragging?"grabbing":"grab"}),(function(e){return e.isDragging?.5:1}),(function(e){return e.isSelected?"0 0 0 2px ".concat(T.colors.primary.main):"none"})),K=O.div(x||(x=(0,o.A)(["\n    display: flex;\n    flex-wrap: wrap;\n    gap: ",";\n    margin-bottom: ",";\n  "])),T.spacing[2],T.spacing[4]),Q=O.div(b||(b=(0,o.A)(["\n    padding: ",";\n    background-color: white;\n    border: 1px solid ",";\n    border-radius: ",";\n    cursor: grab;\n    display: flex;\n    align-items: center;\n    gap: ",";\n\n    &:hover {\n      background-color: ",";\n    }\n  "])),T.spacing[2],T.colors.neutral[300],T.borderRadius.md,T.spacing[2],T.colors.neutral[100]),ee=O.div(S||(S=(0,o.A)(["\n    display: flex;\n    flex-direction: column;\n    gap: ",";\n  "])),T.spacing[3]),te=O.div(A||(A=(0,o.A)(["\n    display: flex;\n    flex-direction: column;\n    gap: ",";\n  "])),T.spacing[2]),ne=O.div(C||(C=(0,o.A)(["\n    display: flex;\n    gap: ",";\n    margin-bottom: ",";\n  "])),T.spacing[2],T.spacing[4]),re=O.div(w||(w=(0,o.A)(["\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding: ",";\n    background-color: ",";\n    border-radius: ",";\n    text-align: center;\n  "])),T.spacing[8],T.colors.neutral[100],T.borderRadius.md)):(X=function(e){var t=e.children,n=(0,i.A)(e,P);return c.createElement("div",n,t)},_=function(e){var t=e.children,n=(0,i.A)(e,M);return c.createElement("div",n,t)},q=function(e){var t=e.children,n=(0,i.A)(e,W);return c.createElement("div",n,t)},V=function(e){var t=e.children,n=(0,i.A)(e,Y);return c.createElement("div",n,t)},K=function(e){var t=e.children,n=(0,i.A)(e,H);return c.createElement("div",n,t)},Q=function(e){var t=e.children,n=(0,i.A)(e,U);return c.createElement("div",n,t)},ee=function(e){var t=e.children,n=(0,i.A)(e,F);return c.createElement("div",n,t)},te=function(e){var t=e.children,n=(0,i.A)(e,B);return c.createElement("div",n,t)},ne=function(e){var t=e.children,n=(0,i.A)(e,N);return c.createElement("div",n,t)},re=function(e){var t=e.children,n=(0,i.A)(e,Z);return c.createElement("div",n,t)});var ie=function(e){var t=e.component,n=(e.index,e.onSelect),r=e.isSelected,a=e.onRemove,i=e.onDragStart,o=e.onDragEnd,u=(0,c.useState)(!1),s=(0,l.A)(u,2),m=s[0],p=s[1],g=(0,c.useState)({x:t.x||0,y:t.y||0}),y=(0,l.A)(g,2),f=y[0],v=y[1];return c.createElement(V,{isDragging:m,isSelected:r,width:t.width,height:t.height,style:{gridColumn:"".concat(f.x+1," / span ").concat(t.width),gridRow:"".concat(f.y+1," / span ").concat(t.height),cursor:m?"grabbing":"grab",position:"relative",zIndex:m?10:1},onClick:function(e){e.stopPropagation(),n(t)},onMouseDown:function(e){p(!0),i&&i(t);var n=e.clientX,r=e.clientY,a=f.x,l=f.y,c=function(e){var t=e.clientX-n,i=e.clientY-r,o=Math.max(0,a+Math.round(t/50)),c=Math.max(0,l+Math.round(i/40));v({x:o,y:c})},u=function(){p(!1),o&&o(t,f),document.removeEventListener("mousemove",c),document.removeEventListener("mouseup",u)};document.addEventListener("mousemove",c),document.addEventListener("mouseup",u)}},c.createElement("div",null,c.createElement("div",{style:{fontWeight:T.typography.fontWeight.semibold}},t.name),c.createElement("div",{style:{fontSize:T.typography.fontSize.sm,color:T.colors.neutral[500]}},t.type)),c.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},c.createElement("div",{style:{fontSize:T.typography.fontSize.sm,color:T.colors.neutral[500]}},t.width,"x",t.height),c.createElement("div",{style:{display:"flex",gap:"4px"}},c.createElement(d.duJ,{style:{cursor:"grab"}}),c.createElement(d.SUY,{style:{cursor:"pointer",color:T.colors.danger.main},onClick:function(e){e.stopPropagation(),a&&a(t.id)}}))))},oe=function(e){var t=e.onDrop,n=e.children,r=(0,c.useState)(!1),a=(0,l.A)(r,2),i=a[0],o=a[1],u=c.useRef(null);return c.createElement("div",{ref:u,style:{position:"relative",height:"100%"},onDragOver:function(e){e.preventDefault(),o(!0)},onDragLeave:function(){o(!1)},onDrop:function(e){e.preventDefault(),o(!1);var n=u.current.getBoundingClientRect(),r=e.clientX-n.left,a=e.clientY-n.top,l=Math.floor(r/50),i=Math.floor(a/40);if(t)try{var c=JSON.parse(e.dataTransfer.getData("application/json"));t(c,{x:l,y:i})}catch(e){console.error("Error parsing drag data:",e)}},onClick:function(){}},n,i&&c.createElement("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(37, 99, 235, 0.1)",border:"2px dashed ".concat(T.colors.primary.main),borderRadius:T.borderRadius.md,zIndex:10}}))},ce=function(){var e=(0,u.wA)(),t=(0,u.d4)((function(e){try{var t,n;return(null===(t=e.app)||void 0===t?void 0:t.components)||(null===(n=e.appData)||void 0===n?void 0:n.components)||[]}catch(e){return console.warn("Error accessing components from state:",e),[]}})),n=(0,u.d4)((function(e){try{var t,n;return(null===(t=e.app)||void 0===t?void 0:t.layouts)||(null===(n=e.appData)||void 0===n?void 0:n.layouts)||[]}catch(e){return console.warn("Error accessing layouts from state:",e),[]}})),a=(0,c.useState)(""),i=(0,l.A)(a,2),o=i[0],s=i[1],m=(0,c.useState)("grid"),p=(0,l.A)(m,2),g=p[0],y=p[1],f=(0,c.useState)([]),v=(0,l.A)(f,2),E=v[0],h=v[1],x=(0,c.useState)(null),b=(0,l.A)(x,2),S=b[0],A=b[1],C=(0,c.useState)(null),w=(0,l.A)(C,2),O=w[0],P=w[1],M=(0,c.useState)(!1),W=(0,l.A)(M,2),Y=W[0],H=W[1],U=(0,c.useState)({}),F=(0,l.A)(U,2),B=F[0],N=F[1],Z=(0,c.useState)(3),$=(0,l.A)(Z,2),G=$[0],V=$[1],ae=(0,c.useState)(2),le=(0,l.A)(ae,2),ce=le[0],ue=le[1],de=function(){var e={};return o.trim()||(e.name="Layout name is required"),N(e),0===Object.keys(e).length},se=function(e){A(e),s(e.name),y(e.type),h(e.items||[]),H(!0),N({})},me=function(e){h(E.filter((function(t){return t.id!==e}))),O&&O.id===e&&P(null)};return c.createElement(X,null,c.createElement(j,null,c.createElement(j.Header,null,c.createElement(j.Title,null,Y?"Edit Layout":"Create Layout"),Y&&c.createElement(I,{variant:"text",size:"small",onClick:function(){s(""),y("grid"),h([]),A(null),H(!1),N({})},startIcon:c.createElement(d.r$3,null)},"Cancel")),c.createElement(j.Content,null,c.createElement(ee,null,c.createElement(te,null,c.createElement(z,{label:"Layout Name",value:o,onChange:function(e){return s(e.target.value)},placeholder:"Enter layout name",fullWidth:!0,error:!!B.name,helperText:B.name})),c.createElement(te,null,c.createElement(R,{label:"Layout Type",value:g,onChange:function(e){return y(e.target.value)},options:[{value:"grid",label:"Grid Layout"},{value:"flex",label:"Flex Layout"},{value:"custom",label:"Custom Layout"}],fullWidth:!0})))),c.createElement(j.Footer,null,Y?c.createElement(I,{variant:"primary",onClick:function(){if(S&&de())try{var t=J(J({},S),{},{name:o.trim(),type:g,items:E,updatedAt:(new Date).toISOString()});e(k(t.id,t)),s(""),y("grid"),h([]),A(null),H(!1),N({}),console.log("Layout updated successfully:",t)}catch(e){console.error("Error updating layout:",e),N({submit:"Failed to update layout"})}},startIcon:c.createElement(d.ylI,null)},"Update Layout"):c.createElement(I,{variant:"primary",onClick:function(){if(de())try{var t={id:Date.now().toString(),name:o.trim(),type:g,items:E,createdAt:(new Date).toISOString()};e(L(t)),s(""),y("grid"),h([]),N({}),console.log("Layout added successfully:",t)}catch(e){console.error("Error adding layout:",e),N({submit:"Failed to add layout"})}},startIcon:c.createElement(d.bW0,null)},"Add Layout"))),c.createElement(j,null,c.createElement(j.Header,null,c.createElement(j.Title,null,"Layout Designer")),c.createElement(j.Content,null,c.createElement(K,null,c.createElement("div",{style:{marginRight:T.spacing[4],fontWeight:T.typography.fontWeight.medium}},"Component Palette:"),t.map((function(e){return c.createElement(Q,{key:e.id,draggable:!0,onDragStart:function(t){t.dataTransfer.setData("application/json",JSON.stringify({id:e.id,name:e.name,type:e.type})),t.dataTransfer.effectAllowed="copy"}},c.createElement(d.duJ,null),c.createElement("span",null,e.name))})),0===t.length&&c.createElement("div",{style:{color:T.colors.neutral[500]}},"No components available. Create components first.")),c.createElement(ne,null,c.createElement("div",{style:{display:"flex",alignItems:"center",gap:T.spacing[2]}},c.createElement(d.x18,null),c.createElement(z,{label:"Width",type:"number",min:1,max:12,value:G,onChange:function(e){return V(parseInt(e.target.value,10))},style:{width:"80px"}})),c.createElement("div",{style:{display:"flex",alignItems:"center",gap:T.spacing[2]}},c.createElement(d.oco,null),c.createElement(z,{label:"Height",type:"number",min:1,max:12,value:ce,onChange:function(e){return ue(parseInt(e.target.value,10))},style:{width:"80px"}})),O&&c.createElement(I,{variant:"primary",size:"small",onClick:function(){if(O){var e=E.map((function(e){return e.id===O.id?J(J({},e),{},{width:G,height:ce}):e}));h(e),P(null)}}},"Apply")),c.createElement(oe,{onDrop:function(e,n){var a=t.find((function(t){return t.id===e.id}));if(a){var l=Math.floor(10*Math.random())+1,i=Math.floor(10*Math.random())+1,o={id:Date.now().toString(),componentId:a.id,name:a.name,type:a.type,x:l,y:i,width:G,height:ce};h([].concat((0,r.A)(E),[o]))}}},c.createElement(q,null,E.map((function(e,t){return c.createElement(ie,{key:e.id,component:e,index:t,onSelect:function(){return function(e){P(e),V(e.width),ue(e.height)}(e)},isSelected:O&&O.id===e.id,onRemove:me,onDragEnd:function(e,t){var n=E.map((function(n){return n.id===e.id?J(J({},n),{},{x:t.x,y:t.y}):n}));h(n)}})})))))),c.createElement(j,null,c.createElement(j.Header,null,c.createElement(j.Title,null,"Saved Layouts")),c.createElement(j.Content,null,0===n.length?c.createElement(re,null,c.createElement("div",{style:{fontSize:"48px",color:T.colors.neutral[400],marginBottom:T.spacing[4]}},c.createElement(d.bnM,null)),c.createElement("h3",null,"No Layouts Yet"),c.createElement("p",null,"Create your first layout to get started")):c.createElement(_,null,n.map((function(t){var n;return c.createElement(j,{key:t.id,elevation:"sm"},c.createElement(j.Header,null,c.createElement("div",null,c.createElement("div",{style:{fontWeight:T.typography.fontWeight.semibold}},t.name),c.createElement("div",{style:{fontSize:T.typography.fontSize.sm,color:T.colors.neutral[500]}},t.type)),c.createElement("div",{style:{display:"flex",gap:T.spacing[1]}},c.createElement(I,{variant:"text",size:"small",onClick:function(){return function(t){var n=J(J({},t),{},{id:Date.now().toString(),name:"".concat(t.name," (Copy)"),createdAt:(new Date).toISOString()});e(L(n))}(t)}},c.createElement(d.wq3,null)),c.createElement(I,{variant:"text",size:"small",onClick:function(){return se(t)}},c.createElement(d.xjh,null)),c.createElement(I,{variant:"text",size:"small",onClick:function(){return function(t){try{e(D(t)),S&&S.id===t&&(s(""),y("grid"),h([]),A(null),H(!1)),console.log("Layout removed successfully:",t)}catch(e){console.error("Error removing layout:",e)}}(t.id)}},c.createElement(d.SUY,null)))),c.createElement(j.Content,{onClick:function(){return se(t)}},c.createElement("div",{style:{height:"150px",backgroundColor:T.colors.neutral[100],borderRadius:T.borderRadius.md,display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer"}},c.createElement(d.Om2,{style:{fontSize:"24px",color:T.colors.neutral[400]}}))),c.createElement(j.Footer,null,c.createElement("div",{style:{fontSize:T.typography.fontSize.sm,color:T.colors.neutral[500]}},(null===(n=t.items)||void 0===n?void 0:n.length)||0," components")))}))))))};const ue=function(){return ae&&O&&I&&j&&z&&R&&T?c.createElement(ce,null):(console.log("Using basic layout designer due to missing dependencies"),c.createElement(y,null))}}}]);