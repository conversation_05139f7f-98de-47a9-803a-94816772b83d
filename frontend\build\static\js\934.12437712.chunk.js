"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[934],{1934:(e,t,n)=>{n.r(t),n.d(t,{default:()=>T});var a=n(436),r=n(4467),l=n(5544),c=n(6540),s=n(2395),o=n(2120),i=n(677),m=n(7122),u=n(6754),d=n(9249),p=n(2652),E=n(6914),g=n(7197),y=n(8990),f=n(1656),A=n(5163),v=n(378),h=n(1295),b=n(2734),S=n(4890),x=n(3740),C=n(8602),M=n(1468);const k={colors:{primary:{main:"#2563EB",light:"#DBEAFE",dark:"#1E40AF",contrastText:"#FFFFFF"},secondary:{main:"#10B981",light:"#D1FAE5",dark:"#047857",contrastText:"#FFFFFF"},background:{default:"#F9FAFB",paper:"#FFFFFF",secondary:"#f0f2f5"},text:{primary:"#111827",secondary:"#4B5563",disabled:"#9CA3AF"},error:{main:"#DC2626",light:"#FEE2E2",dark:"#B91C1C"},warning:{main:"#FBBF24",light:"#FEF3C7",dark:"#D97706"},success:{main:"#10B981",light:"#D1FAE5",dark:"#047857"}},spacing:{xs:"0.5rem",sm:"1rem",md:"1.5rem",lg:"2rem",xl:"2.5rem"},typography:{fontFamily:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",fontWeights:{light:300,regular:400,medium:500,semibold:600,bold:700},sizes:{xs:"0.75rem",sm:"0.875rem",base:"1rem",lg:"1.125rem",xl:"1.25rem"}},borderRadius:{sm:"0.25rem",md:"0.375rem",lg:"0.5rem"},shadows:{sm:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1)"},breakpoints:{xs:"0px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px"}},F=(0,c.createContext)(k);var O=n(5331),w=n(7053),D=n(6894),I=n(8812);function j(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function z(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?j(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var P=s.A.TabPane;const T=function(){var e,t=(0,c.useContext)(F)||k,n=(0,M.wA)(),r=(0,c.useState)(!0),j=(0,l.A)(r,2),T=j[0],B=j[1],L=(0,c.useState)(w.A.getConnectionState?w.A.getConnectionState():{status:"disconnected"}),R=(0,l.A)(L,2),W=R[0],K=R[1],U=(0,c.useState)({sent:0,received:0,errors:0,latency:[]}),Q=(0,l.A)(U,2),J=Q[0],N=Q[1],H=(0,c.useState)({cpu:30*Math.random()+10,memory:40*Math.random()+20,disk:20*Math.random()+5,uptime:Math.floor(1e3*Math.random())+100}),V=(0,l.A)(H,2),q=V[0],G=V[1],X=(0,c.useState)("1"),Y=(0,l.A)(X,2),Z=Y[0],$=Y[1],_={backgroundColor:t.colors.background.default,padding:t.spacing.md},ee={backgroundColor:t.colors.background.paper,borderRadius:t.borderRadius.md,boxShadow:t.shadows.sm};(0,c.useEffect)((function(){n((0,O.tI)("dashboard"));var e=setTimeout((function(){B(!1)}),1500);return function(){return clearTimeout(e)}}),[n]);var te=function(){K(w.A.getConnectionState?w.A.getConnectionState():{status:"disconnected"})},ne=function(){K(w.A.getConnectionState?w.A.getConnectionState():{status:"disconnected"})},ae=function(){K(w.A.getConnectionState?w.A.getConnectionState():{status:"disconnected"}),N((function(e){return z(z({},e),{},{errors:e.errors+1})}))},re=function(e){N((function(t){return z(z({},t),{},{received:t.received+1,latency:[].concat((0,a.A)(t.latency.slice(-50)),[{time:(new Date).toISOString(),value:e.latency||0}])})}))},le=function(){N((function(e){return z(z({},e),{},{sent:e.sent+1})}))};(0,c.useEffect)((function(){w.A.addListener("connect",te),w.A.addListener("disconnect",ne),w.A.addListener("error",ae),w.A.addListener("message",re),w.A.addListener("sent",le),K(w.A.getConnectionState?w.A.getConnectionState():{status:"disconnected"});var e=setInterval((function(){G((function(e){return{cpu:Math.min(Math.max(e.cpu+10*(Math.random()-.5),0),100),memory:Math.min(Math.max(e.memory+10*(Math.random()-.5),0),100),disk:Math.min(Math.max(e.disk+5*(Math.random()-.5),0),100),uptime:e.uptime+1}}))}),5e3);return function(){w.A.off("connect",te),w.A.off("disconnect",ne),w.A.off("error",ae),w.A.off("message",re),w.A.off("sent",le),clearInterval(e)}}),[]);var ce,se,oe,ie,me=function(e){return e<50?"success":e<80?"warning":"exception"};return c.createElement(D.LN,{style:_},T?c.createElement(I.O2,null):c.createElement("div",null,c.createElement(D.p1,{level:2,style:{color:t.colors.text.primary,fontSize:t.typography.sizes.xl,fontWeight:t.typography.fontWeights.semibold}},"Dashboard"),c.createElement(i.A,{style:ee},c.createElement(s.A,{activeKey:Z,onChange:$},c.createElement(P,{tab:c.createElement("span",null,c.createElement(y.A,null),"Overview"),key:"1"},c.createElement(D.JT,{columns:3,columnsMd:2,columnsSm:1},c.createElement(D.pK,{title:"WebSocket Status"},c.createElement(D.n5,{direction:"column",gap:16},c.createElement("div",{style:{fontSize:16}},W.connected?c.createElement(o.A,{status:"success",text:"Connected"}):0===W.readyState?c.createElement(o.A,{status:"processing",text:"Connecting"}):c.createElement(o.A,{status:"error",text:"Disconnected"})),c.createElement("div",null,c.createElement("div",null,"URL: ",W.url||"Not connected"),c.createElement("div",null,"Reconnect Attempts: ",W.reconnectAttempts||0)),c.createElement("div",null,c.createElement(D.jn,{onClick:function(){return w.A.reconnect()},disabled:W.connected},W.connected?"Connected":"Reconnect")))),c.createElement(D.pK,{title:"Message Statistics"},c.createElement(D.n5,{direction:"column",gap:16},c.createElement(D.n5,{justify:"space-between"},c.createElement(m.A,{title:"Sent",value:J.sent,valueStyle:{color:"#3f8600"},prefix:c.createElement(f.A,null)}),c.createElement(m.A,{title:"Received",value:J.received,valueStyle:{color:"#1890ff"},prefix:c.createElement(A.A,null)})),c.createElement(m.A,{title:"Errors",value:J.errors,valueStyle:{color:J.errors>0?"#cf1322":"#3f8600"},prefix:J.errors>0?c.createElement(v.A,null):c.createElement(h.A,null)}))),c.createElement(D.pK,{title:"System Health"},c.createElement(D.n5,{direction:"column",gap:8},c.createElement("div",null,c.createElement("div",{style:{marginBottom:4}},"CPU Usage"),c.createElement(u.A,{percent:Math.round(q.cpu),status:me(q.cpu),size:"small"})),c.createElement("div",null,c.createElement("div",{style:{marginBottom:4}},"Memory Usage"),c.createElement(u.A,{percent:Math.round(q.memory),status:me(q.memory),size:"small"})),c.createElement("div",null,c.createElement("div",{style:{marginBottom:4}},"Disk Usage"),c.createElement(u.A,{percent:Math.round(q.disk),status:me(q.disk),size:"small"})),c.createElement("div",{style:{marginTop:8}},c.createElement(m.A,{title:"Uptime",value:(ce=q.uptime,se=Math.floor(ce/1440),oe=Math.floor(ce%1440/60),ie=ce%60,se>0?"".concat(se,"d ").concat(oe,"h ").concat(ie,"m"):oe>0?"".concat(oe,"h ").concat(ie,"m"):"".concat(ie,"m")),prefix:c.createElement(b.A,null)})))),c.createElement(D.pK,{title:"Latency",extra:c.createElement(d.Ay,{type:"text",icon:c.createElement(S.A,null),onClick:function(){w.A.send({type:"ping",timestamp:Date.now()})},disabled:!W.connected},"Ping"),style:{gridColumn:"span 2"}},c.createElement("div",{style:{height:200,display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",gap:"10px"}},J.latency.length>0?c.createElement(c.Fragment,null,c.createElement("div",null,"Latest latency: ",(null===(e=J.latency[J.latency.length-1])||void 0===e?void 0:e.value)||0," ms"),c.createElement("div",null,"Average latency: ",J.latency.length>0?Math.round(J.latency.reduce((function(e,t){return e+t.value}),0)/J.latency.length):0," ms"),c.createElement("div",null,"Samples: ",J.latency.length)):c.createElement("span",null,'No latency data available. Click "Ping" to measure latency.'))),c.createElement(D.pK,{title:"Recent Activity"},c.createElement(p.A,{size:"small",dataSource:[{title:"WebSocket Connected",time:"2 minutes ago",type:"success"},{title:"Message Received",time:"5 minutes ago",type:"info"},{title:"Configuration Updated",time:"10 minutes ago",type:"warning"},{title:"Application Started",time:"1 hour ago",type:"success"}],renderItem:function(e){return c.createElement(p.A.Item,null,c.createElement(p.A.Item.Meta,{title:c.createElement(D.n5,{justify:"space-between"},c.createElement("span",null,e.title),c.createElement(E.A,{color:"success"===e.type?"success":"warning"===e.type?"warning":"error"===e.type?"error":"blue"},e.time))}))}}))),W.lastError&&c.createElement(g.A,{type:"error",message:"WebSocket Error",description:c.createElement("div",null,c.createElement("div",null,W.lastError.message),W.lastError.timestamp&&c.createElement("div",{style:{color:"rgba(0, 0, 0, 0.45)",marginTop:8}},new Date(W.lastError.timestamp).toLocaleString())),showIcon:!0,style:{marginTop:24}})),c.createElement(P,{tab:c.createElement("span",null,c.createElement(x.A,null),"API Status"),key:"2"},c.createElement(D.JT,{columns:1},c.createElement(D.pK,{title:"API Endpoints"},c.createElement(p.A,{size:"large",dataSource:[{name:"WebSocket API",status:W.connected?"online":"offline",latency:J.latency.length>0?Math.round(J.latency.reduce((function(e,t){return e+t.value}),0)/J.latency.length):null,url:W.url||"ws://localhost:8000/ws"},{name:"REST API",status:"online",latency:45,url:"http://localhost:8000/api"},{name:"Authentication Service",status:"online",latency:78,url:"http://localhost:8000/auth"},{name:"Storage Service",status:"online",latency:112,url:"http://localhost:8000/storage"}],renderItem:function(e){return c.createElement(p.A.Item,{actions:[c.createElement(d.Ay,{key:"test",size:"small"},"Test"),c.createElement(d.Ay,{key:"details",size:"small"},"Details")]},c.createElement(p.A.Item.Meta,{title:c.createElement(D.n5,{justify:"space-between"},c.createElement("span",null,e.name),c.createElement(o.A,{status:"online"===e.status?"success":"error",text:e.status})),description:c.createElement(D.n5,{direction:"column",gap:4},c.createElement("div",null,e.url),e.latency&&c.createElement("div",null,"Latency: ",c.createElement("span",{style:{color:e.latency<100?"#3f8600":e.latency<300?"#faad14":"#cf1322"}},e.latency," ms")))}))}})))),c.createElement(P,{tab:c.createElement("span",null,c.createElement(C.A,null),"Configuration"),key:"3"},c.createElement(D.JT,{columns:2,columnsSm:1},c.createElement(D.pK,{title:"WebSocket Configuration"},c.createElement(p.A,{size:"small",dataSource:[{name:"Reconnect Attempts",value:w.A.maxReconnectAttempts},{name:"Message Queue Size",value:w.A.messageQueueMaxSize},{name:"Compression",value:w.A.performanceOptions.compression.enabled?"Enabled":"Disabled"},{name:"Compression Threshold",value:"".concat(w.A.performanceOptions.compression.threshold," bytes")},{name:"Batching",value:w.A.performanceOptions.batchingEnabled?"Enabled":"Disabled"},{name:"Offline Queue",value:w.A.performanceOptions.offlineQueueEnabled?"Enabled":"Disabled"}],renderItem:function(e){return c.createElement(p.A.Item,null,c.createElement(p.A.Item.Meta,{title:c.createElement(D.n5,{justify:"space-between"},c.createElement("span",null,e.name),c.createElement("span",{style:{fontWeight:"normal"}},e.value))}))}}),c.createElement("div",{style:{marginTop:16}},c.createElement(D.jn,null,"Edit Configuration"))),c.createElement(D.pK,{title:"Security Configuration"},c.createElement(p.A,{size:"small",dataSource:[{name:"Message Validation",value:w.A.securityOptions.validateMessages?"Enabled":"Disabled"},{name:"Message Sanitization",value:w.A.securityOptions.sanitizeMessages?"Enabled":"Disabled"},{name:"Rate Limiting",value:w.A.securityOptions.rateLimiting.enabled?"Enabled":"Disabled"},{name:"Max Messages Per Second",value:w.A.securityOptions.rateLimiting.maxMessagesPerSecond},{name:"Burst Size",value:w.A.securityOptions.rateLimiting.burstSize},{name:"Authentication",value:w.A.authToken?"Enabled":"Disabled"}],renderItem:function(e){return c.createElement(p.A.Item,null,c.createElement(p.A.Item.Meta,{title:c.createElement(D.n5,{justify:"space-between"},c.createElement("span",null,e.name),c.createElement("span",{style:{fontWeight:"normal"}},e.value))}))}}),c.createElement("div",{style:{marginTop:16}},c.createElement(D.jn,null,"Edit Security Settings")))))))))}}}]);