"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[3861],{20038:(e,t,n)=>{n.d(t,{U1:()=>d});var o=n(14644),r=(n(1932),n(25508),n(27346)),i=(n(65606),"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?o.Zz:o.Zz.apply(null,arguments)});"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var c=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}},s=()=>function(e){const{thunk:t=!0,immutableCheck:n=!0,serializableCheck:o=!0,actionCreatorCheck:i=!0}=e??{};let s=new c;return t&&("boolean"==typeof t?s.push(r.P):s.push((0,r.Y)(t.extraArgument))),s},a=e=>t=>{setTimeout(t,e)},u=e=>function(t){const{autoBatch:n=!0}=t??{};let o=new c(e);return n&&o.push(((e={type:"raf"})=>t=>(...n)=>{const o=t(...n);let r=!0,i=!1,c=!1;const s=new Set,u="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:a(10):"callback"===e.type?e.queueNotification:a(e.timeout),d=()=>{c=!1,i&&(i=!1,s.forEach((e=>e())))};return Object.assign({},o,{subscribe(e){const t=o.subscribe((()=>r&&e()));return s.add(e),()=>{t(),s.delete(e)}},dispatch(e){try{return r=!e?.meta?.RTK_autoBatch,i=!r,i&&(c||(c=!0,u(d))),o.dispatch(e)}finally{r=!0}}})})("object"==typeof n?n:void 0)),o};function d(e){const t=s(),{reducer:n,middleware:r,devTools:c=!0,duplicateMiddlewareCheck:a=!0,preloadedState:d,enhancers:f}=e||{};let l,p;if("function"==typeof n)l=n;else{if(!(0,o.Qd)(n))throw new Error(`Minified Redux Toolkit error #${w=1}; visit https://redux-toolkit.js.org/Errors?code=${w} for the full message or use the non-minified dev environment for full errors. `);l=(0,o.HY)(n)}var w;p="function"==typeof r?r(t):t();let h=o.Zz;c&&(h=i({trace:!1,..."object"==typeof c&&c}));const y=(0,o.Tw)(...p),_=u(y),E=h(..."function"==typeof f?f(_):_());return(0,o.y$)(l,d,E)}var{assign:f}=Object;Symbol.for("rtk-state-proxy-original")}}]);