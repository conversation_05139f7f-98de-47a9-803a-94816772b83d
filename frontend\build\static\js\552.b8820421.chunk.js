"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[552],{8552:(e,t,r)=>{r.r(t),r.d(t,{default:()=>E});var n=r(467),s=r(5544),a=r(4756),o=r.n(a),l=r(6540),i=r(3016),c=r(9467),d=r(9740),u=r(677),m=r(9249),p=r(7355),w=r(5763),f=r(7767),y=r(4976),v=i.A.Title,h=i.A.Text;const E=function(){var e=(0,l.useState)(!1),t=(0,s.A)(e,2),r=t[0],a=t[1],i=(0,l.useState)(!0),E=(0,s.A)(i,2),g=E[0],A=E[1],x=c.A.useForm(),k=(0,s.A)(x,1)[0],P=(0,f.g)().token,b=(0,f.Zp)();(0,l.useEffect)((function(){var e=function(){var e=(0,n.A)(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,new Promise((function(e){return setTimeout(e,500)}));case 3:(!P||P.length<10)&&(A(!1),d.Ay.error("Invalid or expired password reset link")),e.next=11;break;case 6:e.prev=6,e.t0=e.catch(0),A(!1),d.Ay.error("Failed to validate reset token"),console.error("Error validating token:",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(){return e.apply(this,arguments)}}();e()}),[P]);var T=function(){var e=(0,n.A)(o().mark((function e(t){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,a(!0),e.next=4,new Promise((function(e){return setTimeout(e,1e3)}));case 4:d.Ay.success("Password has been reset successfully"),b("/login"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),d.Ay.error("Failed to reset password"),console.error("Error resetting password:",e.t0);case 12:return e.prev=12,a(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(t){return e.apply(this,arguments)}}();return g?l.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"100vh",padding:"20px"}},l.createElement(u.A,{style:{width:"100%",maxWidth:400}},l.createElement("div",{style:{textAlign:"center",marginBottom:24}},l.createElement(v,{level:3},"Reset Password"),l.createElement(h,{type:"secondary"},"Enter your new password below")),l.createElement(c.A,{form:k,layout:"vertical",onFinish:T},l.createElement(c.A.Item,{name:"password",rules:[{required:!0,message:"Please enter your new password"},{min:8,message:"Password must be at least 8 characters"}]},l.createElement(p.A.Password,{prefix:l.createElement(w.A,null),placeholder:"New Password",size:"large"})),l.createElement(c.A.Item,{name:"confirmPassword",dependencies:["password"],rules:[{required:!0,message:"Please confirm your password"},function(e){var t=e.getFieldValue;return{validator:function(e,r){return r&&t("password")!==r?Promise.reject(new Error("The two passwords do not match")):Promise.resolve()}}}]},l.createElement(p.A.Password,{prefix:l.createElement(w.A,null),placeholder:"Confirm Password",size:"large"})),l.createElement(c.A.Item,null,l.createElement(m.Ay,{type:"primary",htmlType:"submit",loading:r,block:!0,size:"large"},"Reset Password")),l.createElement("div",{style:{textAlign:"center"}},l.createElement(y.N_,{to:"/login"},"Back to Login"))))):l.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"100vh",padding:"20px"}},l.createElement(u.A,{style:{width:"100%",maxWidth:400,textAlign:"center"}},l.createElement(v,{level:3},"Invalid Reset Link"),l.createElement(h,{type:"secondary"},"The password reset link is invalid or has expired."),l.createElement("div",{style:{marginTop:24}},l.createElement(y.N_,{to:"/forgot-password"},l.createElement(m.Ay,{type:"primary"},"Request New Link")))))}}}]);