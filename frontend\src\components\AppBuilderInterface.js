import React, { useState, useEffect } from 'react';
import { Layout, <PERSON>u, <PERSON><PERSON>, Card, Typography, Tabs, Input, Select, Switch, Collapse, message, Spin, Tooltip, Divider, Modal, Form, Dropdown } from 'antd';
import {
  AppstoreOutlined,
  LayoutOutlined,
  FormOutlined,
  DatabaseOutlined,
  ApiOutlined,
  SettingOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  CodeOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  RocketOutlined,
  BulbOutlined,
  BookOutlined,
  DownOutlined,
  UploadOutlined,
  DownloadOutlined,
  StarOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import axios from 'axios';
import ComponentPalette from './builder/ComponentPalette';
import PreviewArea from './builder/PreviewArea';
import { EnhancedComponentProperties } from './enhanced/property-editor';
import DataSourcePanel from './builder/DataSourcePanel';
import CodePanel from './builder/CodePanel';

// Tutorial System Imports
import {
  TutorialProvider,
  TutorialOverlay,
  ContextualHelp,
  TutorialTrigger,
  withComponentPaletteHelp,
  withPreviewAreaHelp,
  withPropertyEditorHelp,
  TUTORIAL_DEFINITIONS,
  useTutorial
} from './tutorial';
import TutorialRegistration from './tutorial/TutorialRegistration';
import TutorialEntryPoint from './tutorial/TutorialEntryPoint';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;

// Enhanced components with tutorial support
const TutorialComponentPalette = withComponentPaletteHelp(ComponentPalette);
const TutorialPreviewArea = withPreviewAreaHelp(PreviewArea);
const TutorialPropertyEditor = withPropertyEditorHelp(EnhancedComponentProperties);

const AppBuilderInterface = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [currentApp, setCurrentApp] = useState(null);
  const [loading, setLoading] = useState(false);
  const [components, setComponents] = useState([]);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [previewMode, setPreviewMode] = useState(false);
  const [appName, setAppName] = useState('My New App');
  const [dataSources, setDataSources] = useState([]);
  const [activeTab, setActiveTab] = useState('components');

  // Tutorial-related state
  const [showTutorialLauncher, setShowTutorialLauncher] = useState(false);

  // Template-related state
  const [saveTemplateModalVisible, setSaveTemplateModalVisible] = useState(false);
  const [loadTemplateModalVisible, setLoadTemplateModalVisible] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [templateForm] = Form.useForm();
  const [templateLoading, setTemplateLoading] = useState(false);

  // Mock data for demonstration
  const mockApps = [
    { id: 1, name: 'Customer Portal', description: 'Customer management portal' },
    { id: 2, name: 'Inventory System', description: 'Inventory tracking system' },
    { id: 3, name: 'Analytics Dashboard', description: 'Business analytics dashboard' }
  ];

  useEffect(() => {
    // Simulate loading an app
    setLoading(true);
    setTimeout(() => {
      setCurrentApp(mockApps[0]);
      setComponents([
        { id: 'header-1', type: 'header', props: { title: 'Customer Portal', subtitle: 'Manage your customers' } },
        { id: 'table-1', type: 'table', props: { columns: ['Name', 'Email', 'Status'], dataSource: 'customers' } },
        { id: 'form-1', type: 'form', props: { fields: ['name', 'email', 'phone'], submitAction: 'createCustomer' } }
      ]);
      setDataSources([
        { id: 'customers', name: 'Customers', type: 'api', endpoint: '/api/customers' },
        { id: 'products', name: 'Products', type: 'api', endpoint: '/api/products' }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const handleComponentSelect = (component) => {
    setSelectedComponent(component);
  };

  const handleAddComponent = (componentType) => {
    const newComponent = {
      id: `${componentType}-${Date.now()}`,
      type: componentType,
      props: getDefaultPropsForType(componentType)
    };

    setComponents([...components, newComponent]);
    setSelectedComponent(newComponent);
    message.success(`Added ${componentType} component`);
  };

  const handleUpdateComponent = (id, updatedProps) => {
    const updatedComponents = components.map(comp =>
      comp.id === id ? { ...comp, props: { ...comp.props, ...updatedProps } } : comp
    );

    setComponents(updatedComponents);

    // Update selected component if it's the one being edited
    if (selectedComponent && selectedComponent.id === id) {
      setSelectedComponent({ ...selectedComponent, props: { ...selectedComponent.props, ...updatedProps } });
    }

    message.success('Component updated');
  };

  const handleDeleteComponent = (id) => {
    const updatedComponents = components.filter(comp => comp.id !== id);
    setComponents(updatedComponents);

    if (selectedComponent && selectedComponent.id === id) {
      setSelectedComponent(null);
    }

    message.success('Component deleted');
  };

  const handleSaveApp = () => {
    setLoading(true);

    // Simulate saving to backend
    setTimeout(() => {
      setLoading(false);
      message.success('Application saved successfully');
    }, 1000);
  };

  const handlePreviewToggle = () => {
    setPreviewMode(!previewMode);
  };

  const handleAddDataSource = () => {
    const newDataSource = {
      id: `datasource-${Date.now()}`,
      name: 'New Data Source',
      type: 'api',
      endpoint: '/api/data'
    };

    setDataSources([...dataSources, newDataSource]);
    message.success('Data source added');
  };

  // Helper function to get default props for a component type
  const getDefaultPropsForType = (type) => {
    switch (type) {
      case 'header':
        return { title: 'New Header', subtitle: 'Subtitle text' };
      case 'table':
        return { columns: ['Column 1', 'Column 2'], dataSource: '' };
      case 'form':
        return { fields: ['field1', 'field2'], submitAction: '' };
      case 'chart':
        return { type: 'bar', dataSource: '', xField: '', yField: '' };
      case 'card':
        return { title: 'Card Title', content: 'Card content goes here' };
      case 'button':
        return { text: 'Button', action: '', type: 'primary' };
      default:
        return {};
    }
  };

  // Template-related functions
  const fetchTemplates = async () => {
    try {
      const [layoutResponse, appResponse] = await Promise.all([
        axios.get('/api/layout-templates/'),
        axios.get('/api/app-templates/')
      ]);

      const allTemplates = [
        ...layoutResponse.data.results.map(t => ({ ...t, template_type: 'layout' })),
        ...appResponse.data.results.map(t => ({ ...t, template_type: 'app' }))
      ];

      setTemplates(allTemplates);
    } catch (error) {
      console.error('Error fetching templates:', error);
      message.error('Failed to load templates');
    }
  };

  const handleSaveAsTemplate = () => {
    if (components.length === 0) {
      message.warning('Add some components before saving as template');
      return;
    }
    setSaveTemplateModalVisible(true);
  };

  const handleSaveTemplate = async (values) => {
    setTemplateLoading(true);
    try {
      const templateData = {
        name: values.name,
        description: values.description,
        components: components,
        default_props: {
          appName: appName,
          dataSources: dataSources
        },
        is_public: values.is_public || false
      };

      let url;
      if (values.template_type === 'layout') {
        url = '/api/layout-templates/';
        templateData.layout_type = values.layout_type || 'custom';
      } else {
        url = '/api/app-templates/';
        templateData.app_category = values.app_category || 'other';
        templateData.required_components = [...new Set(components.map(c => c.type))];
      }

      await axios.post(url, templateData);

      message.success('Template saved successfully');
      setSaveTemplateModalVisible(false);
      templateForm.resetFields();
      fetchTemplates(); // Refresh templates
    } catch (error) {
      console.error('Error saving template:', error);
      message.error('Failed to save template');
    } finally {
      setTemplateLoading(false);
    }
  };

  const handleLoadTemplate = async (template) => {
    setTemplateLoading(true);
    try {
      let templateComponents = [];
      let templateProps = {};

      if (template.components) {
        if (typeof template.components === 'string') {
          templateComponents = JSON.parse(template.components);
        } else if (Array.isArray(template.components)) {
          templateComponents = template.components;
        } else {
          // Handle object-based component structure
          templateComponents = Object.values(template.components).flat();
        }
      }

      if (template.default_props) {
        if (typeof template.default_props === 'string') {
          templateProps = JSON.parse(template.default_props);
        } else {
          templateProps = template.default_props;
        }
      }

      // Apply template
      setComponents(templateComponents);
      if (templateProps.appName) {
        setAppName(templateProps.appName);
      }
      if (templateProps.dataSources) {
        setDataSources(templateProps.dataSources);
      }

      setLoadTemplateModalVisible(false);
      message.success(`Template "${template.name}" loaded successfully`);
    } catch (error) {
      console.error('Error loading template:', error);
      message.error('Failed to load template');
    } finally {
      setTemplateLoading(false);
    }
  };

  // Load templates on component mount
  useEffect(() => {
    fetchTemplates();
  }, []);

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        width={250}
        collapsible
        collapsed={collapsed}
        onCollapse={setCollapsed}
        theme="light"
        style={{ boxShadow: '2px 0 8px rgba(0,0,0,0.1)' }}
      >
        <div style={{ padding: '16px', textAlign: 'center' }}>
          <Title level={4} style={{ margin: '0 0 8px 0' }}>
            {collapsed ? 'AB' : 'App Builder'}
          </Title>
          {!collapsed && (
            <Text type="secondary">Build your app with ease</Text>
          )}
        </div>

        <Menu
          mode="inline"
          defaultSelectedKeys={['components']}
          selectedKeys={[activeTab]}
          onClick={({ key }) => setActiveTab(key)}
        >
          <Menu.Item key="components" icon={<AppstoreOutlined />}>
            Components
          </Menu.Item>
          <Menu.Item key="templates" icon={<BookOutlined />}>
            Templates
          </Menu.Item>
          <Menu.Item key="layout" icon={<LayoutOutlined />}>
            Layout
          </Menu.Item>
          <Menu.Item key="data" icon={<DatabaseOutlined />}>
            Data Sources
          </Menu.Item>
          <Menu.Item key="api" icon={<ApiOutlined />}>
            API Integration
          </Menu.Item>
          <Menu.Item key="code" icon={<CodeOutlined />}>
            Custom Code
          </Menu.Item>
          <Menu.Item key="settings" icon={<SettingOutlined />}>
            Settings
          </Menu.Item>
          <Menu.Divider />
          <Menu.Item
            key="tutorials"
            icon={<QuestionCircleOutlined />}
            onClick={() => setShowTutorialLauncher(true)}
          >
            {collapsed ? '' : 'Tutorials & Help'}
          </Menu.Item>
        </Menu>

        <div style={{ padding: '16px', position: 'absolute', bottom: 0, width: '100%' }}>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSaveApp}
            loading={loading}
            block
          >
            {collapsed ? '' : 'Save App'}
          </Button>

          <Button
            style={{ marginTop: '8px' }}
            icon={<PlayCircleOutlined />}
            onClick={handlePreviewToggle}
            type={previewMode ? 'default' : 'text'}
            block
            data-tutorial-target={previewMode ? 'edit-mode-button' : 'preview-mode-button'}
          >
            {collapsed ? '' : (previewMode ? 'Exit Preview' : 'Preview')}
          </Button>
        </div>
      </Sider>

      <Layout>
        <Header style={{
          background: '#fff',
          padding: '0 16px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Input
              value={appName}
              onChange={(e) => setAppName(e.target.value)}
              bordered={false}
              style={{ fontSize: '18px', fontWeight: 'bold', width: '300px' }}
            />
            <Tooltip title="App Settings">
              <Button type="text" icon={<SettingOutlined />} />
            </Tooltip>
            <Tooltip title="Help & Tutorials">
              <Button
                type="text"
                icon={<QuestionCircleOutlined />}
                onClick={() => setShowTutorialLauncher(true)}
              />
            </Tooltip>
          </div>

          <div>
            <Dropdown
              overlay={
                <Menu>
                  <Menu.Item
                    key="save-template"
                    icon={<SaveOutlined />}
                    onClick={handleSaveAsTemplate}
                  >
                    Save as Template
                  </Menu.Item>
                  <Menu.Item
                    key="load-template"
                    icon={<UploadOutlined />}
                    onClick={() => setLoadTemplateModalVisible(true)}
                  >
                    Load Template
                  </Menu.Item>
                  <Menu.Divider />
                  <Menu.Item
                    key="browse-templates"
                    icon={<BookOutlined />}
                    onClick={() => window.location.href = '/templates'}
                  >
                    Browse Templates
                  </Menu.Item>
                </Menu>
              }
              trigger={['click']}
            >
              <Button style={{ marginRight: '8px' }}>
                Templates <DownOutlined />
              </Button>
            </Dropdown>

            <Button
              type="primary"
              icon={<RocketOutlined />}
              style={{ marginRight: '8px' }}
            >
              Deploy
            </Button>

            <Select
              defaultValue="1"
              style={{ width: 200 }}
              onChange={(value) => {
                const app = mockApps.find(a => a.id.toString() === value);
                setCurrentApp(app);
              }}
            >
              {mockApps.map(app => (
                <Option key={app.id} value={app.id.toString()}>{app.name}</Option>
              ))}
            </Select>
          </div>
        </Header>

        <Content style={{ margin: '16px', display: 'flex', flexDirection: 'column' }}>
          {/* Tutorial Entry Point for New Users */}
          <TutorialEntryPoint />

          {loading ? (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Spin size="large" tip="Loading App Builder..." />
            </div>
          ) : (
            <div style={{ display: 'flex', height: '100%' }}>
              {/* Left panel - Component palette or data sources depending on active tab */}
              {!previewMode && (
                <Card
                  style={{ width: 250, marginRight: '16px', height: '100%', overflow: 'auto' }}
                  data-tutorial-target="component-palette"
                >
                  {activeTab === 'components' && (
                    <TutorialComponentPalette onAddComponent={handleAddComponent} />
                  )}

                  {activeTab === 'templates' && (
                    <div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                        <Title level={5} style={{ margin: 0 }}>Templates</Title>
                        <Button
                          size="small"
                          icon={<PlusOutlined />}
                          onClick={handleSaveAsTemplate}
                        >
                          Save
                        </Button>
                      </div>

                      <div style={{ marginBottom: '16px' }}>
                        <Button
                          block
                          icon={<UploadOutlined />}
                          onClick={() => setLoadTemplateModalVisible(true)}
                        >
                          Load Template
                        </Button>
                      </div>

                      <Divider orientation="left" plain>Recent Templates</Divider>

                      <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                        {templates.slice(0, 5).map((template) => (
                          <Card
                            key={template.id}
                            size="small"
                            style={{ marginBottom: '8px', cursor: 'pointer' }}
                            hoverable
                            onClick={() => handleLoadTemplate(template)}
                          >
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <div style={{ marginRight: '8px' }}>
                                {template.template_type === 'layout' ?
                                  <LayoutOutlined /> :
                                  <AppstoreOutlined />
                                }
                              </div>
                              <div style={{ flex: 1 }}>
                                <Text strong style={{ fontSize: '12px' }}>
                                  {template.name}
                                </Text>
                                <br />
                                <Text type="secondary" style={{ fontSize: '11px' }}>
                                  {template.template_type === 'layout' ?
                                    template.layout_type :
                                    template.app_category
                                  }
                                </Text>
                              </div>
                              {template.is_public && (
                                <StarOutlined style={{ color: '#faad14' }} />
                              )}
                            </div>
                          </Card>
                        ))}

                        {templates.length === 0 && (
                          <div style={{ textAlign: 'center', padding: '20px' }}>
                            <Text type="secondary">No templates available</Text>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {activeTab === 'data' && (
                    <DataSourcePanel
                      dataSources={dataSources}
                      onAddDataSource={handleAddDataSource}
                    />
                  )}

                  {activeTab === 'code' && (
                    <CodePanel />
                  )}

                  {activeTab === 'layout' && (
                    <div>
                      <Title level={5}>Layout Options</Title>
                      <Collapse defaultActiveKey={['1']}>
                        <Panel header="Page Layout" key="1">
                          <div style={{ marginBottom: '8px' }}>
                            <Text>Page Width</Text>
                            <Select defaultValue="responsive" style={{ width: '100%', marginTop: '4px' }}>
                              <Option value="responsive">Responsive</Option>
                              <Option value="fixed">Fixed Width</Option>
                              <Option value="fullWidth">Full Width</Option>
                            </Select>
                          </div>

                          <div style={{ marginBottom: '8px' }}>
                            <Text>Header Position</Text>
                            <Select defaultValue="top" style={{ width: '100%', marginTop: '4px' }}>
                              <Option value="top">Top</Option>
                              <Option value="sticky">Sticky Top</Option>
                              <Option value="none">No Header</Option>
                            </Select>
                          </div>

                          <div>
                            <Text>Show Footer</Text>
                            <div>
                              <Switch defaultChecked />
                            </div>
                          </div>
                        </Panel>

                        <Panel header="Grid Settings" key="2">
                          <div style={{ marginBottom: '8px' }}>
                            <Text>Grid Columns</Text>
                            <Select defaultValue="12" style={{ width: '100%', marginTop: '4px' }}>
                              <Option value="12">12 Columns</Option>
                              <Option value="16">16 Columns</Option>
                              <Option value="24">24 Columns</Option>
                            </Select>
                          </div>

                          <div>
                            <Text>Grid Gutter</Text>
                            <Select defaultValue="16" style={{ width: '100%', marginTop: '4px' }}>
                              <Option value="8">Small (8px)</Option>
                              <Option value="16">Medium (16px)</Option>
                              <Option value="24">Large (24px)</Option>
                            </Select>
                          </div>
                        </Panel>
                      </Collapse>
                    </div>
                  )}
                </Card>
              )}

              {/* Center panel - Preview area */}
              <Card
                style={{
                  flex: 1,
                  height: '100%',
                  overflow: 'auto',
                  padding: previewMode ? '0' : '16px'
                }}
                bodyStyle={{
                  height: '100%',
                  padding: previewMode ? '0' : '16px',
                  background: previewMode ? '#fff' : '#f5f5f5'
                }}
                data-tutorial-target="preview-area"
              >
                <TutorialPreviewArea
                  components={components}
                  onSelectComponent={handleComponentSelect}
                  onDeleteComponent={handleDeleteComponent}
                  previewMode={previewMode}
                  selectedComponentId={selectedComponent?.id}
                />
              </Card>

              {/* Right panel - Properties panel */}
              {!previewMode && selectedComponent && (
                <Card
                  style={{ width: 300, marginLeft: '16px', height: '100%', overflow: 'auto' }}
                  data-tutorial-target="property-editor"
                >
                  <TutorialPropertyEditor
                    component={selectedComponent}
                    onUpdate={handleUpdateComponent}
                  />
                </Card>
              )}
            </div>
          )}
        </Content>
      </Layout>

      {/* Save Template Modal */}
      <Modal
        title="Save as Template"
        open={saveTemplateModalVisible}
        onCancel={() => setSaveTemplateModalVisible(false)}
        onOk={() => templateForm.submit()}
        confirmLoading={templateLoading}
        width={600}
      >
        <Form
          form={templateForm}
          layout="vertical"
          onFinish={handleSaveTemplate}
        >
          <Form.Item
            name="name"
            label="Template Name"
            rules={[{ required: true, message: 'Please enter template name' }]}
          >
            <Input placeholder="Enter template name" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea
              placeholder="Enter template description"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="template_type"
            label="Template Type"
            rules={[{ required: true, message: 'Please select template type' }]}
          >
            <Select placeholder="Select template type">
              <Option value="layout">Layout Template</Option>
              <Option value="app">App Template</Option>
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.template_type !== currentValues.template_type
            }
          >
            {({ getFieldValue }) => {
              const templateType = getFieldValue('template_type');

              if (templateType === 'layout') {
                return (
                  <Form.Item
                    name="layout_type"
                    label="Layout Type"
                    rules={[{ required: true, message: 'Please select layout type' }]}
                  >
                    <Select placeholder="Select layout type">
                      <Option value="grid">Grid</Option>
                      <Option value="flex">Flex</Option>
                      <Option value="sidebar">Sidebar</Option>
                      <Option value="dashboard">Dashboard</Option>
                      <Option value="landing">Landing</Option>
                      <Option value="custom">Custom</Option>
                    </Select>
                  </Form.Item>
                );
              } else if (templateType === 'app') {
                return (
                  <Form.Item
                    name="app_category"
                    label="App Category"
                    rules={[{ required: true, message: 'Please select app category' }]}
                  >
                    <Select placeholder="Select app category">
                      <Option value="business">Business Apps</Option>
                      <Option value="ecommerce">E-commerce</Option>
                      <Option value="portfolio">Portfolio</Option>
                      <Option value="dashboard">Dashboard</Option>
                      <Option value="landing">Landing Page</Option>
                      <Option value="other">Other</Option>
                    </Select>
                  </Form.Item>
                );
              }
              return null;
            }}
          </Form.Item>

          <Form.Item
            name="is_public"
            valuePropName="checked"
          >
            <Switch /> <span style={{ marginLeft: 8 }}>Make template public</span>
          </Form.Item>
        </Form>
      </Modal>

      {/* Load Template Modal */}
      <Modal
        title="Load Template"
        open={loadTemplateModalVisible}
        onCancel={() => setLoadTemplateModalVisible(false)}
        footer={null}
        width={800}
      >
        <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
          {templates.map((template) => (
            <Card
              key={template.id}
              style={{ marginBottom: '16px', cursor: 'pointer' }}
              hoverable
              onClick={() => handleLoadTemplate(template)}
            >
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{ marginRight: '16px', fontSize: '24px' }}>
                  {template.template_type === 'layout' ?
                    <LayoutOutlined /> :
                    <AppstoreOutlined />
                  }
                </div>
                <div style={{ flex: 1 }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                    <Text strong>{template.name}</Text>
                    {template.is_public && (
                      <StarOutlined style={{ color: '#faad14', marginLeft: '8px' }} />
                    )}
                  </div>
                  <Text type="secondary" style={{ display: 'block', marginBottom: '4px' }}>
                    {template.description || 'No description provided'}
                  </Text>
                  <div>
                    <span style={{
                      background: template.template_type === 'layout' ? '#f0f2ff' : '#fff2e8',
                      color: template.template_type === 'layout' ? '#1890ff' : '#fa8c16',
                      padding: '2px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      marginRight: '8px'
                    }}>
                      {template.template_type === 'layout' ?
                        template.layout_type :
                        template.app_category
                      }
                    </span>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      Created: {new Date(template.created_at).toLocaleDateString()}
                    </Text>
                  </div>
                </div>
              </div>
            </Card>
          ))}

          {templates.length === 0 && (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <Text type="secondary">No templates available</Text>
            </div>
          )}
        </div>
      </Modal>

      {/* Tutorial System Components */}
      <TutorialRegistration />
      <TutorialOverlay />
      <ContextualHelp />
      <TutorialTrigger
        showBadge={true}
        showQuickAccess={true}
        position="bottom-right"
      />
    </Layout>
  );
};

// Wrap the component with TutorialProvider
const AppBuilderWithTutorials = () => {
  return (
    <TutorialProvider userId="current-user">
      <AppBuilderInterface />
    </TutorialProvider>
  );
};

export default AppBuilderWithTutorials;
