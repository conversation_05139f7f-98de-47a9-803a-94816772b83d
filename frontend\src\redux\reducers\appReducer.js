import * as types from '../actions/types';

const initialState = {
  components: [],
  layouts: [],
  styles: {},
  data: {},
  loading: false,
  error: null,
  aiSuggestions: [],
  generatedImage: null,
  aiLoading: false,
  aiError: null
};

const appReducer = (state = initialState, action) => {
  switch (action.type) {
    // App data actions
    case types.FETCH_APP_DATA_REQUEST:
    case types.SAVE_APP_DATA_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      };

    case types.FETCH_APP_DATA_SUCCESS:
      return {
        ...state,
        components: action.payload.components || [],
        layouts: action.payload.layouts || [],
        styles: action.payload.styles || {},
        data: action.payload.data || {},
        loading: false
      };

    case types.SAVE_APP_DATA_SUCCESS:
      return {
        ...state,
        loading: false
      };

    case types.FETCH_APP_DATA_FAILURE:
    case types.SAVE_APP_DATA_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload
      };

    // Component actions
    case types.ADD_COMPONENT:
      // Check if the payload is a complete component or just type/props
      const newComponent = action.payload.id
        ? action.payload
        : {
          id: Date.now().toString(),
          type: action.payload.type,
          props: action.payload.props || {},
          createdAt: new Date().toISOString()
        };

      return {
        ...state,
        components: [...(state.components || []), newComponent]
      };

    case types.UPDATE_COMPONENT:
      return {
        ...state,
        components: (state.components || []).map(component =>
          component.id === action.payload.id
            ? { ...component, ...action.payload, updatedAt: new Date().toISOString() }
            : component
        )
      };

    case types.REMOVE_COMPONENT:
      return {
        ...state,
        components: (state.components || []).filter(component => component.id !== action.payload.id)
      };

    // Layout actions
    case types.ADD_LAYOUT:
      return {
        ...state,
        layouts: [
          ...(state.layouts || []),
          {
            id: Date.now().toString(),
            type: action.payload.type,
            components: action.payload.components,
            styles: action.payload.styles
          }
        ]
      };

    case types.UPDATE_LAYOUT:
      return {
        ...state,
        layouts: (state.layouts || []).map(layout =>
          layout.id === action.payload.id
            ? {
              ...layout,
              components: action.payload.components || layout.components,
              styles: action.payload.styles || layout.styles
            }
            : layout
        )
      };

    case types.REMOVE_LAYOUT:
      return {
        ...state,
        layouts: (state.layouts || []).filter(layout => layout.id !== action.payload.id)
      };

    // Style actions
    case types.ADD_STYLE:
    case types.UPDATE_STYLE:
      return {
        ...state,
        styles: {
          ...state.styles,
          [action.payload.selector]: action.payload.style
        }
      };

    case types.REMOVE_STYLE: {
      const { [action.payload.selector]: _, ...remainingStyles } = state.styles;
      return {
        ...state,
        styles: remainingStyles
      };
    }

    // Data actions
    case types.ADD_DATA:
    case types.UPDATE_DATA:
      return {
        ...state,
        data: {
          ...state.data,
          [action.payload.key]: action.payload.value
        }
      };

    case types.REMOVE_DATA: {
      const { [action.payload.key]: _, ...remainingData } = state.data;
      return {
        ...state,
        data: remainingData
      };
    }

    // AI actions
    case types.GENERATE_AI_SUGGESTIONS_REQUEST:
    case types.GENERATE_IMAGE_REQUEST:
      return {
        ...state,
        aiLoading: true,
        aiError: null
      };

    case types.GENERATE_AI_SUGGESTIONS_SUCCESS:
      return {
        ...state,
        aiSuggestions: action.payload,
        aiLoading: false
      };

    case types.GENERATE_IMAGE_SUCCESS:
      return {
        ...state,
        generatedImage: action.payload,
        aiLoading: false
      };

    case types.GENERATE_AI_SUGGESTIONS_FAILURE:
    case types.GENERATE_IMAGE_FAILURE:
      return {
        ...state,
        aiLoading: false,
        aiError: action.payload
      };

    default:
      return state;
  }
};

export default appReducer;
