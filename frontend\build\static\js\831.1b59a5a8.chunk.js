"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[831],{5831:(e,r,t)=>{t.r(r),t.d(r,{default:()=>B});var a,s,n,l,i,m=t(467),u=t(5544),o=t(7528),c=t(4756),g=t.n(c),h=t(6540),d=t(3016),p=t(677),A=t(9467),f=t(9249),E=t(7197),w=t(7355),x=t(1196),v=t(7450),P=t(5619),y=t(5763),b=t(5542),q=t(7767),N=t(4976),I=t(9391),z=t(2389),k=t(1250),R=(t(7749),d.A.Title),T=d.A.Text,F=(d.A.Paragraph,k.Ay.div(a||(a=(0,o.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: calc(100vh - 200px);\n  padding: 24px;\n"])))),$=(0,k.Ay)(p.A)(s||(s=(0,o.A)(["\n  width: 100%;\n  max-width: 500px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n"]))),S=(0,k.Ay)(A.A)(n||(n=(0,o.A)(["\n  .ant-form-item-label {\n    text-align: left;\n  }\n"]))),Z=(0,k.Ay)(f.Ay)(l||(l=(0,o.A)(["\n  width: 100%;\n"]))),j=(0,k.Ay)(T)(i||(i=(0,o.A)(["\n  display: block;\n  text-align: center;\n  margin-top: 16px;\n"])));const B=function(){var e=(0,I.As)().register,r=(0,q.Zp)(),t=(0,z.Bd)().t,a=(0,h.useState)(!1),s=(0,u.A)(a,2),n=s[0],l=s[1],i=(0,h.useState)(null),o=(0,u.A)(i,2),c=o[0],d=o[1],p=function(){var t=(0,m.A)(g().mark((function t(a){return g().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return l(!0),d(null),t.prev=2,t.next=5,e({username:a.username,email:a.email,password:a.password,firstName:a.firstName,lastName:a.lastName,phone:a.phone});case 5:r("/",{replace:!0}),t.next=11;break;case 8:t.prev=8,t.t0=t.catch(2),d(t.t0.message||"Registration failed. Please try again.");case 11:return t.prev=11,l(!1),t.finish(11);case 14:case"end":return t.stop()}}),t,null,[[2,8,11,14]])})));return function(e){return t.apply(this,arguments)}}();return h.createElement(F,null,h.createElement($,null,h.createElement(R,{level:2,style:{textAlign:"center",marginBottom:"24px"}},t("auth.register.title")),c&&h.createElement(E.A,{message:t("auth.register.error"),description:c,type:"error",showIcon:!0,style:{marginBottom:"24px"}}),h.createElement(S,{name:"register",onFinish:p,layout:"vertical",scrollToFirstError:!0},h.createElement(A.A.Item,{label:t("auth.register.username"),name:"username",rules:[{required:!0,message:t("auth.register.usernameRequired")},{min:3,message:t("auth.register.usernameTooShort")},{max:20,message:t("auth.register.usernameTooLong")},{pattern:/^[a-zA-Z0-9_]+$/,message:t("auth.register.usernameInvalid")}]},h.createElement(w.A,{prefix:h.createElement(v.A,null),placeholder:t("auth.register.usernamePlaceholder"),size:"large"})),h.createElement(A.A.Item,{label:t("auth.register.email"),name:"email",rules:[{required:!0,message:t("auth.register.emailRequired")},{type:"email",message:t("auth.register.emailInvalid")}]},h.createElement(w.A,{prefix:h.createElement(P.A,null),placeholder:t("auth.register.emailPlaceholder"),size:"large"})),h.createElement(A.A.Item,{label:t("auth.register.password"),name:"password",rules:[{required:!0,message:t("auth.register.passwordRequired")},{min:8,message:t("auth.register.passwordTooShort")},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/,message:t("auth.register.passwordInvalid")}],hasFeedback:!0},h.createElement(w.A.Password,{prefix:h.createElement(y.A,null),placeholder:t("auth.register.passwordPlaceholder"),size:"large"})),h.createElement(A.A.Item,{label:t("auth.register.confirmPassword"),name:"confirmPassword",dependencies:["password"],hasFeedback:!0,rules:[{required:!0,message:t("auth.register.confirmPasswordRequired")},function(e){var r=e.getFieldValue;return{validator:function(e,a){return a&&r("password")!==a?Promise.reject(new Error(t("auth.register.passwordsMismatch"))):Promise.resolve()}}}]},h.createElement(w.A.Password,{prefix:h.createElement(y.A,null),placeholder:t("auth.register.confirmPasswordPlaceholder"),size:"large"})),h.createElement(A.A.Item,{label:t("auth.register.firstName"),name:"firstName",rules:[{required:!0,message:t("auth.register.firstNameRequired")}]},h.createElement(w.A,{placeholder:t("auth.register.firstNamePlaceholder"),size:"large"})),h.createElement(A.A.Item,{label:t("auth.register.lastName"),name:"lastName",rules:[{required:!0,message:t("auth.register.lastNameRequired")}]},h.createElement(w.A,{placeholder:t("auth.register.lastNamePlaceholder"),size:"large"})),h.createElement(A.A.Item,{label:t("auth.register.phone"),name:"phone",rules:[{required:!0,message:t("auth.register.phoneRequired")},{pattern:/^\+?[0-9]{10,15}$/,message:t("auth.register.phoneInvalid")}]},h.createElement(w.A,{prefix:h.createElement(b.A,null),placeholder:t("auth.register.phonePlaceholder"),size:"large"})),h.createElement(A.A.Item,{name:"agreement",valuePropName:"checked",rules:[{validator:function(e,r){return r?Promise.resolve():Promise.reject(new Error(t("auth.register.agreementRequired")))}}]},h.createElement(x.A,null,t("auth.register.agreement")," ",h.createElement(N.N_,{to:"/terms"},t("auth.register.terms")))),h.createElement(A.A.Item,null,h.createElement(Z,{type:"primary",htmlType:"submit",size:"large",loading:n},t("auth.register.submit")))),h.createElement(j,null,t("auth.register.haveAccount")," ",h.createElement(N.N_,{to:"/login"},t("auth.register.login")))))}}}]);