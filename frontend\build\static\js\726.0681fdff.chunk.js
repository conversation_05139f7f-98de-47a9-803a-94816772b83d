"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[726],{3726:(e,r,n)=>{n.r(r),n.d(r,{default:()=>P});var o,t,a,c,i,s,l=n(467),u=n(5544),d=n(7528),p=n(4756),m=n.n(p),b=n(6540),y=n(5556),h=n.n(y),g=n(677),v=n(6914),f=n(6552),k=n(3016),w=n(2702),A=n(7355),x=n(9249),E=n(1348),C=n(1372),T=n(321),S=n(778),B=n(3740),M=n(1250),q=n(2569),W=(0,M.Ay)(g.A)(o||(o=(0,d.A)(["\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  .ant-card-body {\n    padding: var(--spacing-lg);\n  }\n"]))),z=(0,M.Ay)(g.A)(t||(t=(0,d.A)(["\n  background-color: var(--color-background-secondary);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-md);\n  margin-bottom: var(--spacing-md);\n\n  .ant-card-body {\n    padding: var(--spacing-md);\n  }\n\n  .ant-typography {\n    color: var(--color-text);\n    margin-bottom: 0;\n  }\n"]))),Y=(0,M.Ay)(v.A)(a||(a=(0,d.A)(["\n  cursor: pointer;\n  padding: var(--spacing-sm) var(--spacing-md);\n  border-radius: var(--border-radius-md);\n  background-color: var(--color-primary);\n  border-color: var(--color-primary);\n  color: white;\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  &:hover {\n    background-color: var(--color-primary-hover);\n    border-color: var(--color-primary-hover);\n    color: white;\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-sm);\n  }\n\n  .anticon {\n    color: white;\n  }\n"]))),D=(0,M.Ay)(f.A)(c||(c=(0,d.A)(["\n  border-color: var(--color-border);\n  margin: var(--spacing-md) 0;\n\n  .ant-divider-inner-text {\n    color: var(--color-text-secondary);\n    font-size: 12px;\n    font-weight: 500;\n  }\n"]))),L=(0,M.Ay)(k.A.Text)(i||(i=(0,d.A)(["\n  color: var(--color-text-tertiary);\n  font-size: 12px;\n  text-align: center;\n  display: block;\n"]))),I=(0,M.Ay)(w.A.Compact)(s||(s=(0,d.A)(["\n  width: 100%;\n\n  .ant-input {\n    background-color: var(--color-surface);\n    border-color: var(--color-border);\n    color: var(--color-text);\n\n    &::placeholder {\n      color: var(--color-text-tertiary);\n    }\n\n    &:focus {\n      border-color: var(--color-primary);\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n  }\n\n  .ant-btn {\n    background-color: var(--color-primary);\n    border-color: var(--color-primary);\n    color: white;\n\n    &:hover {\n      background-color: var(--color-primary-hover);\n      border-color: var(--color-primary-hover);\n    }\n\n    &:disabled {\n      background-color: var(--color-text-tertiary);\n      border-color: var(--color-text-tertiary);\n      color: var(--color-background);\n    }\n  }\n"]))),O=function(e){e.onComplete;var r=(0,q.ZV)(),n=(r.isDarkMode,r.colors,(0,b.useState)(!1)),o=(0,u.A)(n,2),t=o[0],a=o[1],c=(0,b.useState)("Welcome to App Builder 201! I'm your tutorial assistant. Ask me anything about how to use this application."),i=(0,u.A)(c,2),s=i[0],d=i[1],p=(0,b.useState)(""),y=(0,u.A)(p,2),h=y[0],g=y[1],v={"how to add component":'To add a component, go to the Component Builder tab, enter a name, select a type, add any properties in JSON format, and click "Add Component".',"how to create layout":"To create a layout, navigate to the Layouts tab, select a layout type, and arrange your components within it.","what is websocket":"WebSockets provide real-time communication between your browser and the server. You can test this functionality in the WebSocket Manager.","how to use":"App Builder 201 lets you create applications by adding components, designing layouts, and applying themes. Start by adding some components in the Component Builder.",help:"I can help you learn how to use App Builder 201. Try asking specific questions about components, layouts, themes, or WebSockets.","getting started":"Welcome to App Builder! Here's how to get started:\n1. 📦 Create components in the Component Builder\n2. 🎨 Design layouts in the Layout Designer\n3. 🎭 Customize themes in the Theme Manager\n4. 🔌 Test real-time features with WebSocket Manager\n5. 📊 Monitor performance and export your code when ready!","component builder":"The Component Builder lets you create reusable UI components. You can add buttons, text, inputs, cards, and more. Each component can have custom properties like colors, sizes, and behaviors.","layout designer":"The Layout Designer helps you arrange your components into responsive layouts. You can create grids, flexbox layouts, and organize your components visually.","theme manager":"The Theme Manager allows you to customize the look and feel of your application. You can change colors, fonts, spacing, and create consistent design systems.","websocket manager":"The WebSocket Manager enables real-time communication features. You can test live updates, chat functionality, and real-time data synchronization."},f=[{label:"Getting Started",icon:b.createElement(E.A,null),query:"getting started"},{label:"Component Builder",icon:b.createElement(C.A,null),query:"component builder"},{label:"Layout Designer",icon:b.createElement(T.A,null),query:"layout designer"},{label:"Theme Manager",icon:b.createElement(S.A,null),query:"theme manager"},{label:"WebSocket Manager",icon:b.createElement(B.A,null),query:"websocket manager"}],M=function(){var e=(0,l.A)(m().mark((function e(r){var n,o,t,c,i,s,l,d;return m().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a(!0),e.prev=1,e.next=4,fetch("/api/tutorial/response/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:r})});case 4:if((n=e.sent).ok){e.next=7;break}throw new Error("Failed to get tutorial response");case 7:return e.next=9,n.json();case 9:return o=e.sent,e.abrupt("return",o.response);case 13:e.prev=13,e.t0=e.catch(1),console.error("Tutorial generation error:",e.t0),t=r.toLowerCase(),c=0,i=Object.entries(v);case 18:if(!(c<i.length)){e.next=25;break}if(s=(0,u.A)(i[c],2),l=s[0],d=s[1],!t.includes(l)){e.next=22;break}return e.abrupt("return",d);case 22:c++,e.next=18;break;case 25:return e.abrupt("return","I encountered an error. Please try again.");case 26:return e.prev=26,a(!1),e.finish(26);case 29:case"end":return e.stop()}}),e,null,[[1,13,26,29]])})));return function(r){return e.apply(this,arguments)}}(),O=function(){var e=(0,l.A)(m().mark((function e(r){var n;return m().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,M(r);case 2:n=e.sent,d(n);case 4:case"end":return e.stop()}}),e)})));return function(r){return e.apply(this,arguments)}}();return b.createElement(W,null,b.createElement("div",{style:{marginBottom:"16px"}},s&&b.createElement(z,{type:"inner"},b.createElement(k.A.Paragraph,{style:{color:"var(--color-text)",margin:0}},s))),b.createElement(I,null,b.createElement(A.A,{value:h,onChange:function(e){return g(e.target.value)},placeholder:"Ask anything about the app...",disabled:t,onPressEnter:(0,l.A)(m().mark((function e(){var r,n;return m().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(h.trim()){e.next=2;break}return e.abrupt("return");case 2:return r=h,g(""),e.next=6,M(r);case 6:n=e.sent,d(n);case 8:case"end":return e.stop()}}),e)})))}),b.createElement(x.Ay,{type:"primary",loading:t,onClick:(0,l.A)(m().mark((function e(){var r,n;return m().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(h.trim()){e.next=2;break}return e.abrupt("return");case 2:return r=h,g(""),e.next=6,M(r);case 6:n=e.sent,d(n);case 8:case"end":return e.stop()}}),e)}))),disabled:!h.trim()},t?"Thinking...":"Ask")),b.createElement(D,{orientation:"left"},"Quick Help Topics"),b.createElement("div",{style:{marginBottom:"16px"}},b.createElement(w.A,{wrap:!0},f.map((function(e,r){return b.createElement(Y,{key:r,icon:e.icon,onClick:function(){return O(e.query)}},e.label)})))),b.createElement(L,null,"Click on a topic above or ask your own question"))};O.propTypes={onComplete:h().func};const P=O}}]);