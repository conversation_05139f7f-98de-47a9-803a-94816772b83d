"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[680],{1680:(e,t,n)=>{n.r(t),n.d(t,{default:()=>K});var r,o,a,l,c,i,s=n(4467),m=n(5544),u=n(7528),d=n(6540),p=n(1468),y=n(3016),A=n(4358),g=n(677),f=n(9467),E=n(9740),b=n(8295),v=n(9249),w=n(7308),h=n(2652),j=n(761),P=n(7206),S=n(6914),D=n(7355),O=n(2702),k=n(9237),I=n(261),x=n(7046),C=n(1952),T=n(3598),F=n(8854),N=n(6191),L=n(6020),R=n(1616);function M(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function U(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?M(Object(n),!0).forEach((function(t){(0,s.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):M(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var q=y.A.Title,z=y.A.Text,B=y.A.Paragraph,_=(A.A.Option,(0,N.styled)(g.A)(r||(r=(0,u.A)(["\n  margin-bottom: ",";\n  border-radius: ",";\n  box-shadow: ",";\n  transition: all 0.3s ease;\n  cursor: pointer;\n  \n  &:hover {\n    box-shadow: ",";\n    transform: translateY(-2px);\n  }\n  \n  &.active {\n    border-left: 4px solid ",";\n  }\n"])),L.Ay.spacing[3],L.Ay.borderRadius.md,L.Ay.shadows.sm,L.Ay.shadows.md,L.Ay.colors.primary.main)),G=N.styled.div(o||(o=(0,u.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ",";\n"])),L.Ay.spacing[2]),V=(0,N.styled)(q)(a||(a=(0,u.A)(["\n  margin: 0 !important;\n"]))),Y=(0,N.styled)(B)(l||(l=(0,u.A)(["\n  margin-bottom: "," !important;\n  color: ",";\n"])),L.Ay.spacing[2],L.Ay.colors.neutral[600]),H=N.styled.div(c||(c=(0,u.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: ",";\n  font-size: ",";\n"])),L.Ay.colors.neutral[500],L.Ay.typography.fontSize.sm),J=N.styled.div(i||(i=(0,u.A)(["\n  margin-top: ",";\n"])),L.Ay.spacing[2]);const K=function(){var e=(0,p.wA)(),t=(0,p.d4)((function(e){return e.projects})),n=t.projects,r=t.activeProject,o=(0,d.useState)(!1),a=(0,m.A)(o,2),l=a[0],c=a[1],i=(0,d.useState)(null),s=(0,m.A)(i,2),u=s[0],y=s[1],g=f.A.useForm(),N=(0,m.A)(g,1)[0];(0,d.useEffect)((function(){if(0===n.length){var t=[{id:"1",name:"E-commerce Dashboard",description:"Admin dashboard for an e-commerce platform",tags:["dashboard","e-commerce"],createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),components:12,layouts:5},{id:"2",name:"Blog Template",description:"Responsive blog template with multiple layouts",tags:["blog","responsive"],createdAt:new Date(Date.now()-864e5).toISOString(),updatedAt:new Date(Date.now()-864e5).toISOString(),components:8,layouts:3},{id:"3",name:"Portfolio Site",description:"Personal portfolio website template",tags:["portfolio","personal"],createdAt:new Date(Date.now()-1728e5).toISOString(),updatedAt:new Date(Date.now()-1728e5).toISOString(),components:6,layouts:2}];e((0,R.RT)(t)),e((0,R.eL)("1"))}}),[e,n.length]);var L=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;y(e),e?N.setFieldsValue({name:e.name,description:e.description,tags:e.tags}):N.resetFields(),c(!0)},M=function(){c(!1),y(null),N.resetFields()};return d.createElement("div",{className:"project-manager"},d.createElement(G,null,d.createElement(q,{level:3},"My Projects"),d.createElement(v.Ay,{type:"primary",icon:d.createElement(k.A,null),onClick:function(){return L()}},"New Project")),0===n.length?d.createElement(w.A,{description:"No projects found",image:w.A.PRESENTED_IMAGE_SIMPLE}):d.createElement(h.A,{dataSource:n,renderItem:function(t){return d.createElement(_,{className:r===t.id?"active":"",onClick:function(){return n=t.id,void e((0,R.eL)(n));var n}},d.createElement(G,null,d.createElement(V,{level:4},t.name),d.createElement(j.A,{overlay:d.createElement(P.A,null,d.createElement(P.A.Item,{key:"edit",icon:d.createElement(I.A,null),onClick:function(e){e.stopPropagation(),L(t)}},"Edit"),d.createElement(P.A.Item,{key:"duplicate",icon:d.createElement(x.A,null),onClick:function(e){e.stopPropagation(),E.Ay.info("Duplicate feature coming soon")}},"Duplicate"),d.createElement(P.A.Item,{key:"export",icon:d.createElement(C.A,null),onClick:function(e){e.stopPropagation(),E.Ay.info("Export feature coming soon")}},"Export"),d.createElement(P.A.Divider,null),d.createElement(P.A.Item,{key:"delete",danger:!0,icon:d.createElement(T.A,null),onClick:function(n){var r;n.stopPropagation(),r=t.id,b.A.confirm({title:"Delete Project",content:"Are you sure you want to delete this project? This action cannot be undone.",okText:"Delete",okType:"danger",cancelText:"Cancel",onOk:function(){e((0,R.xx)(r)),E.Ay.success("Project deleted successfully")}})}},"Delete")),trigger:["click"]},d.createElement(v.Ay,{type:"text",icon:d.createElement(F.A,null),onClick:function(e){return e.stopPropagation()}}))),d.createElement(Y,null,t.description),d.createElement(J,null,t.tags.map((function(e){return d.createElement(S.A,{key:e,color:"blue",style:{marginBottom:"8px"}},e)}))),d.createElement(H,null,d.createElement("div",null,d.createElement(z,{type:"secondary"},t.components," components · ",t.layouts," layouts")),d.createElement("div",null,d.createElement(z,{type:"secondary"},"Updated ",(n=t.updatedAt,new Date(n).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}))))));var n}}),d.createElement(b.A,{title:u?"Edit Project":"Create Project",visible:l,onCancel:M,footer:null},d.createElement(f.A,{form:N,layout:"vertical",onFinish:function(t){if(u){var n=U(U(U({},u),t),{},{updatedAt:(new Date).toISOString()});e((0,R.vr)(n)),E.Ay.success("Project updated successfully")}else{var r=U(U({id:Date.now().toString()},t),{},{createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),components:0,layouts:0});e((0,R.gA)(r)),E.Ay.success("Project created successfully")}c(!1),y(null),N.resetFields()}},d.createElement(f.A.Item,{name:"name",label:"Project Name",rules:[{required:!0,message:"Please enter a project name"}]},d.createElement(D.A,{placeholder:"Enter project name"})),d.createElement(f.A.Item,{name:"description",label:"Description",rules:[{required:!0,message:"Please enter a description"}]},d.createElement(D.A.TextArea,{placeholder:"Enter project description",rows:3})),d.createElement(f.A.Item,{name:"tags",label:"Tags"},d.createElement(A.A,{mode:"tags",placeholder:"Add tags",style:{width:"100%"}})),d.createElement(f.A.Item,null,d.createElement(O.A,{style:{display:"flex",justifyContent:"flex-end"}},d.createElement(v.Ay,{onClick:M},"Cancel"),d.createElement(v.Ay,{type:"primary",htmlType:"submit"},u?"Update":"Create"))))))}}}]);