"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[7435],{37435:(e,r,t)=>{t.r(r),t.d(r,{default:()=>N});var l,a,n,s,o,i=t(10467),m=t(5544),c=t(57528),p=t(54756),u=t.n(p),d=t(96540),f=t(1807),E=t(35346),h=t(49391),g=t(63710),v=t(70572),w=t(57749),x=f.o5.Title,y=f.o5.Text,P=(f.o5.Paragraph,f.tU.TabPane),b=v.Ay.div(l||(l=(0,c.A)(["\n  padding: 24px;\n  max-width: 800px;\n  margin: 0 auto;\n"]))),A=v.Ay.div(a||(a=(0,c.A)(["\n  display: flex;\n  align-items: center;\n  margin-bottom: 24px;\n  \n  @media (max-width: 576px) {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n"]))),q=(0,v.Ay)(f.eu)(n||(n=(0,c.A)(["\n  margin-right: 24px;\n  background-color: ",";\n  \n  @media (max-width: 576px) {\n    margin-right: 0;\n    margin-bottom: 16px;\n  }\n"])),(function(e){return e.theme.colorPalette.primary})),I=v.Ay.div(s||(s=(0,c.A)(["\n  flex: 1;\n"]))),k=(0,v.Ay)(f.lV)(o||(o=(0,c.A)(["\n  .ant-form-item-label {\n    text-align: left;\n  }\n"])));const N=function(){var e=(0,h.As)().user,r=(0,g.Bd)().t,t=(0,d.useState)(!1),l=(0,m.A)(t,2),a=l[0],n=l[1],s=(0,d.useState)(null),o=(0,m.A)(s,2),c=o[0],p=o[1],v=(0,d.useState)(null),N=(0,m.A)(v,2),V=N[0],F=N[1],T=(0,d.useState)("1"),S=(0,m.A)(T,2),R=S[0],$=S[1],C=(0,d.useState)(!1),U=(0,m.A)(C,2),B=U[0],z=U[1],X=function(){var e=(0,i.A)(u().mark((function e(t){return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n(!0),p(null),F(null),e.prev=3,e.next=6,new Promise((function(e){return setTimeout(e,1e3)}));case 6:F(r("profile.updateSuccess")),z(!1),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(3),p(e.t0.message||r("profile.updateError"));case 13:return e.prev=13,n(!1),e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[3,10,13,16]])})));return function(r){return e.apply(this,arguments)}}(),O=function(){var e=(0,i.A)(u().mark((function e(t){return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n(!0),p(null),F(null),e.prev=3,e.next=6,new Promise((function(e){return setTimeout(e,1e3)}));case 6:F(r("profile.passwordChangeSuccess")),L.resetFields(),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(3),p(e.t0.message||r("profile.passwordChangeError"));case 13:return e.prev=13,n(!1),e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[3,10,13,16]])})));return function(r){return e.apply(this,arguments)}}(),Z=f.lV.useForm(),_=(0,m.A)(Z,1)[0],j=f.lV.useForm(),L=(0,m.A)(j,1)[0];return d.useEffect((function(){e&&_.setFieldsValue({username:e.username,email:e.email,firstName:e.firstName,lastName:e.lastName,phone:e.phone})}),[e,_]),e?d.createElement(b,null,d.createElement(w.sT,{level:2},r("profile.title")),d.createElement(A,null,d.createElement(q,{size:100,icon:d.createElement(E.qmv,null),src:e.avatar}),d.createElement(I,null,d.createElement(x,{level:3},e.firstName," ",e.lastName),d.createElement(y,{type:"secondary"},e.email),e.roles&&e.roles.length>0&&d.createElement("div",{style:{marginTop:"8px"}},e.roles.map((function(e){return d.createElement(w.ih,{key:e,color:"blue"},e)}))))),d.createElement(f.tU,{activeKey:R,onChange:$},d.createElement(P,{tab:d.createElement("span",null,d.createElement(E.qmv,null),r("profile.tabs.profile")),key:"1"},d.createElement(w.ee,{title:r("profile.profileInfo"),extra:d.createElement(f.$n,{type:B?"primary":"default",icon:B?d.createElement(E.ylI,null):d.createElement(E.xjh,null),onClick:function(){B?_.submit():z(!0)}},r(B?"profile.save":"profile.edit"))},c&&d.createElement(f.Fc,{message:r("profile.error"),description:c,type:"error",showIcon:!0,style:{marginBottom:"24px"}}),V&&d.createElement(f.Fc,{message:r("profile.success"),description:V,type:"success",showIcon:!0,style:{marginBottom:"24px"}}),d.createElement(k,{form:_,layout:"vertical",onFinish:X,disabled:!B},d.createElement(f.lV.Item,{label:r("profile.username"),name:"username",rules:[{required:!0,message:r("profile.usernameRequired")},{min:3,message:r("profile.usernameTooShort")},{max:20,message:r("profile.usernameTooLong")},{pattern:/^[a-zA-Z0-9_]+$/,message:r("profile.usernameInvalid")}]},d.createElement(f.pd,{prefix:d.createElement(E.qmv,null),placeholder:r("profile.usernamePlaceholder")})),d.createElement(f.lV.Item,{label:r("profile.email"),name:"email",rules:[{required:!0,message:r("profile.emailRequired")},{type:"email",message:r("profile.emailInvalid")}]},d.createElement(f.pd,{prefix:d.createElement(E._Wu,null),placeholder:r("profile.emailPlaceholder")})),d.createElement(f.lV.Item,{label:r("profile.firstName"),name:"firstName",rules:[{required:!0,message:r("profile.firstNameRequired")}]},d.createElement(f.pd,{placeholder:r("profile.firstNamePlaceholder")})),d.createElement(f.lV.Item,{label:r("profile.lastName"),name:"lastName",rules:[{required:!0,message:r("profile.lastNameRequired")}]},d.createElement(f.pd,{placeholder:r("profile.lastNamePlaceholder")})),d.createElement(f.lV.Item,{label:r("profile.phone"),name:"phone",rules:[{required:!0,message:r("profile.phoneRequired")},{pattern:/^\+?[0-9]{10,15}$/,message:r("profile.phoneInvalid")}]},d.createElement(f.pd,{prefix:d.createElement(E.NW5,null),placeholder:r("profile.phonePlaceholder")}))))),d.createElement(P,{tab:d.createElement("span",null,d.createElement(E.sXv,null),r("profile.tabs.security")),key:"2"},d.createElement(w.ee,{title:r("profile.changePassword")},c&&d.createElement(f.Fc,{message:r("profile.error"),description:c,type:"error",showIcon:!0,style:{marginBottom:"24px"}}),V&&d.createElement(f.Fc,{message:r("profile.success"),description:V,type:"success",showIcon:!0,style:{marginBottom:"24px"}}),d.createElement(k,{form:L,layout:"vertical",onFinish:O},d.createElement(f.lV.Item,{label:r("profile.currentPassword"),name:"currentPassword",rules:[{required:!0,message:r("profile.currentPasswordRequired")}]},d.createElement(f.pd.Password,{prefix:d.createElement(E.sXv,null),placeholder:r("profile.currentPasswordPlaceholder")})),d.createElement(f.lV.Item,{label:r("profile.newPassword"),name:"newPassword",rules:[{required:!0,message:r("profile.newPasswordRequired")},{min:8,message:r("profile.passwordTooShort")},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/,message:r("profile.passwordInvalid")}],hasFeedback:!0},d.createElement(f.pd.Password,{prefix:d.createElement(E.sXv,null),placeholder:r("profile.newPasswordPlaceholder")})),d.createElement(f.lV.Item,{label:r("profile.confirmPassword"),name:"confirmPassword",dependencies:["newPassword"],hasFeedback:!0,rules:[{required:!0,message:r("profile.confirmPasswordRequired")},function(e){var t=e.getFieldValue;return{validator:function(e,l){return l&&t("newPassword")!==l?Promise.reject(new Error(r("profile.passwordsMismatch"))):Promise.resolve()}}}]},d.createElement(f.pd.Password,{prefix:d.createElement(E.sXv,null),placeholder:r("profile.confirmPasswordPlaceholder")})),d.createElement(f.lV.Item,null,d.createElement(f.$n,{type:"primary",htmlType:"submit",loading:a},r("profile.changePassword")))))),d.createElement(P,{tab:d.createElement("span",null,d.createElement(E.qvO,null),r("profile.tabs.avatar")),key:"3"},d.createElement(w.ee,{title:r("profile.changeAvatar")},d.createElement("div",{style:{textAlign:"center",marginBottom:"24px"}},d.createElement(q,{size:150,icon:d.createElement(E.qmv,null),src:e.avatar})),d.createElement(f._O,{name:"avatar",listType:"picture",showUploadList:!1,action:"/api/upload",onChange:function(e){"uploading"!==e.file.status&&("done"===e.file.status?f.iU.success(r("profile.avatarUploadSuccess")):"error"===e.file.status&&f.iU.error(r("profile.avatarUploadError")))}},d.createElement(f.$n,{icon:d.createElement(E.qvO,null)},r("profile.uploadAvatar"))))))):null}}}]);