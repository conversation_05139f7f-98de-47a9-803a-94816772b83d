"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[1680],{51680:(e,t,n)=>{n.r(t),n.d(t,{default:()=>I});var r,o,a,l,c,i,s=n(64467),m=n(5544),u=n(57528),d=n(96540),p=n(71468),y=n(1807),g=n(35346),f=n(79146),E=n(86020),b=n(81616);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){(0,s.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var A=y.o5.Title,h=y.o5.Text,j=y.o5.Paragraph,S=(y.l6.Option,(0,f.styled)(y.Zp)(r||(r=(0,u.A)(["\n  margin-bottom: ",";\n  border-radius: ",";\n  box-shadow: ",";\n  transition: all 0.3s ease;\n  cursor: pointer;\n  \n  &:hover {\n    box-shadow: ",";\n    transform: translateY(-2px);\n  }\n  \n  &.active {\n    border-left: 4px solid ",";\n  }\n"])),E.Ay.spacing[3],E.Ay.borderRadius.md,E.Ay.shadows.sm,E.Ay.shadows.md,E.Ay.colors.primary.main)),P=f.styled.div(o||(o=(0,u.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ",";\n"])),E.Ay.spacing[2]),D=(0,f.styled)(A)(a||(a=(0,u.A)(["\n  margin: 0 !important;\n"]))),O=(0,f.styled)(j)(l||(l=(0,u.A)(["\n  margin-bottom: "," !important;\n  color: ",";\n"])),E.Ay.spacing[2],E.Ay.colors.neutral[600]),k=f.styled.div(c||(c=(0,u.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: ",";\n  font-size: ",";\n"])),E.Ay.colors.neutral[500],E.Ay.typography.fontSize.sm),x=f.styled.div(i||(i=(0,u.A)(["\n  margin-top: ",";\n"])),E.Ay.spacing[2]);const I=function(){var e=(0,p.wA)(),t=(0,p.d4)((function(e){return e.projects})),n=t.projects,r=t.activeProject,o=(0,d.useState)(!1),a=(0,m.A)(o,2),l=a[0],c=a[1],i=(0,d.useState)(null),s=(0,m.A)(i,2),u=s[0],f=s[1],E=y.lV.useForm(),v=(0,m.A)(E,1)[0];(0,d.useEffect)((function(){if(0===n.length){var t=[{id:"1",name:"E-commerce Dashboard",description:"Admin dashboard for an e-commerce platform",tags:["dashboard","e-commerce"],createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),components:12,layouts:5},{id:"2",name:"Blog Template",description:"Responsive blog template with multiple layouts",tags:["blog","responsive"],createdAt:new Date(Date.now()-864e5).toISOString(),updatedAt:new Date(Date.now()-864e5).toISOString(),components:8,layouts:3},{id:"3",name:"Portfolio Site",description:"Personal portfolio website template",tags:["portfolio","personal"],createdAt:new Date(Date.now()-1728e5).toISOString(),updatedAt:new Date(Date.now()-1728e5).toISOString(),components:6,layouts:2}];e((0,b.RT)(t)),e((0,b.eL)("1"))}}),[e,n.length]);var j=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;f(e),e?v.setFieldsValue({name:e.name,description:e.description,tags:e.tags}):v.resetFields(),c(!0)},I=function(){c(!1),f(null),v.resetFields()};return d.createElement("div",{className:"project-manager"},d.createElement(P,null,d.createElement(A,{level:3},"My Projects"),d.createElement(y.$n,{type:"primary",icon:d.createElement(g.bW0,null),onClick:function(){return j()}},"New Project")),0===n.length?d.createElement(y.Sv,{description:"No projects found",image:y.Sv.PRESENTED_IMAGE_SIMPLE}):d.createElement(y.B8,{dataSource:n,renderItem:function(t){return d.createElement(S,{className:r===t.id?"active":"",onClick:function(){return n=t.id,void e((0,b.eL)(n));var n}},d.createElement(P,null,d.createElement(D,{level:4},t.name),d.createElement(y.ms,{overlay:d.createElement(y.W1,null,d.createElement(y.W1.Item,{key:"edit",icon:d.createElement(g.xjh,null),onClick:function(e){e.stopPropagation(),j(t)}},"Edit"),d.createElement(y.W1.Item,{key:"duplicate",icon:d.createElement(g.wq3,null),onClick:function(e){e.stopPropagation(),y.iU.info("Duplicate feature coming soon")}},"Duplicate"),d.createElement(y.W1.Item,{key:"export",icon:d.createElement(g.PZg,null),onClick:function(e){e.stopPropagation(),y.iU.info("Export feature coming soon")}},"Export"),d.createElement(y.W1.Divider,null),d.createElement(y.W1.Item,{key:"delete",danger:!0,icon:d.createElement(g.SUY,null),onClick:function(n){var r;n.stopPropagation(),r=t.id,y.aF.confirm({title:"Delete Project",content:"Are you sure you want to delete this project? This action cannot be undone.",okText:"Delete",okType:"danger",cancelText:"Cancel",onOk:function(){e((0,b.xx)(r)),y.iU.success("Project deleted successfully")}})}},"Delete")),trigger:["click"]},d.createElement(y.$n,{type:"text",icon:d.createElement(g.a7l,null),onClick:function(e){return e.stopPropagation()}}))),d.createElement(O,null,t.description),d.createElement(x,null,t.tags.map((function(e){return d.createElement(y.vw,{key:e,color:"blue",style:{marginBottom:"8px"}},e)}))),d.createElement(k,null,d.createElement("div",null,d.createElement(h,{type:"secondary"},t.components," components · ",t.layouts," layouts")),d.createElement("div",null,d.createElement(h,{type:"secondary"},"Updated ",(n=t.updatedAt,new Date(n).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}))))));var n}}),d.createElement(y.aF,{title:u?"Edit Project":"Create Project",visible:l,onCancel:I,footer:null},d.createElement(y.lV,{form:v,layout:"vertical",onFinish:function(t){if(u){var n=w(w(w({},u),t),{},{updatedAt:(new Date).toISOString()});e((0,b.vr)(n)),y.iU.success("Project updated successfully")}else{var r=w(w({id:Date.now().toString()},t),{},{createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),components:0,layouts:0});e((0,b.gA)(r)),y.iU.success("Project created successfully")}c(!1),f(null),v.resetFields()}},d.createElement(y.lV.Item,{name:"name",label:"Project Name",rules:[{required:!0,message:"Please enter a project name"}]},d.createElement(y.pd,{placeholder:"Enter project name"})),d.createElement(y.lV.Item,{name:"description",label:"Description",rules:[{required:!0,message:"Please enter a description"}]},d.createElement(y.pd.TextArea,{placeholder:"Enter project description",rows:3})),d.createElement(y.lV.Item,{name:"tags",label:"Tags"},d.createElement(y.l6,{mode:"tags",placeholder:"Add tags",style:{width:"100%"}})),d.createElement(y.lV.Item,null,d.createElement(y.$x,{style:{display:"flex",justifyContent:"flex-end"}},d.createElement(y.$n,{onClick:I},"Cancel"),d.createElement(y.$n,{type:"primary",htmlType:"submit"},u?"Update":"Create"))))))}}}]);