"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[672],{40672:(e,t,r)=>{r.r(t),r.d(t,{default:()=>A});var n,a,o,l,s=r(5544),i=r(57528),c=r(96540),m=r(1807),u=r(35346),d=r(79146),p=r(86020),g=m.o5.Title,y=m.o5.Text,f=m.o5.Paragraph,h=m.tU.TabPane,E=d.styled.div(n||(n=(0,i.A)(["\n  padding: ",";\n"])),p.Ay.spacing[3]),v=d.styled.div(a||(a=(0,i.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: ",";\n  margin-bottom: ",";\n"])),p.Ay.spacing[4],p.Ay.spacing[4]),w=(0,d.styled)(m.Zp)(o||(o=(0,i.A)(["\n  text-align: center;\n"]))),k=d.styled.div(l||(l=(0,i.A)(["\n  height: 300px;\n  margin-bottom: ",";\n  background-color: ",";\n  border-radius: ",";\n  display: flex;\n  justify-content: center;\n  align-items: center;\n"])),p.Ay.spacing[4],p.Ay.colors.neutral[100],p.Ay.borderRadius.md);const A=function(){var e=(0,c.useState)({memory:{used:0,total:0,limit:0},cpu:{usage:0,cores:0},network:{requests:0,transferred:0,errors:0},rendering:{fps:0,renderTime:0},components:{count:0,renderCount:0}}),t=(0,s.A)(e,2),r=t[0],n=t[1],a=(0,c.useState)(!0),o=(0,s.A)(a,2),l=o[0],i=o[1],d=(0,c.useState)("overview"),A=(0,s.A)(d,2),M=A[0],S=A[1],x=(0,c.useState)(null),C=(0,s.A)(x,2),b=C[0],z=C[1],T=(0,c.useState)(null),B=(0,s.A)(T,2),L=B[0],j=B[1],F=(0,c.useRef)(),I=(0,c.useRef)(0),R=(0,c.useRef)(performance.now()),K=(0,c.useRef)([]),q=function(){try{var e;i(!0);var t=(null===(e=window.performance)||void 0===e?void 0:e.memory)||{usedJSHeapSize:100*Math.random()*1024*1024,totalJSHeapSize:209715200,jsHeapSizeLimit:524288e3},r=100*Math.random(),a=navigator.hardwareConcurrency||4,o=Math.floor(50*Math.random()),l=5*Math.random()*1024*1024,s=Math.floor(3*Math.random()),c=document.querySelectorAll("[data-component]").length||Math.floor(20*Math.random()),m=Math.floor(100*Math.random()),u=performance.now(),d=u-R.current;R.current=u,K.current.push(d),K.current.length>60&&K.current.shift();var p=K.current.reduce((function(e,t){return e+t}),0)/K.current.length,g=Math.round(1e3/p);n({memory:{used:t.usedJSHeapSize,total:t.totalJSHeapSize,limit:t.jsHeapSizeLimit},cpu:{usage:r,cores:a},network:{requests:o,transferred:l,errors:s},rendering:{fps:g,renderTime:p},components:{count:c,renderCount:m}}),j(new Date),i(!1)}catch(e){console.error("Error collecting metrics:",e),z("Failed to collect performance metrics"),i(!1)}};(0,c.useEffect)((function(){q();var e=function(){I.current++,F.current=requestAnimationFrame(e)};F.current=requestAnimationFrame(e);var t=setInterval((function(){q()}),2e3);return function(){clearInterval(t),cancelAnimationFrame(F.current)}}),[]);var P=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;if(0===e)return"0 Bytes";var r=t<0?0:t,n=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,n)).toFixed(r))+" "+["Bytes","KB","MB","GB","TB"][n]},G=function(e,t){return arguments.length>2&&void 0!==arguments[2]&&arguments[2]?e<=t.danger?p.Ay.colors.error.main:e<=t.warning?p.Ay.colors.warning.main:p.Ay.colors.success.main:e>=t.danger?p.Ay.colors.error.main:e>=t.warning?p.Ay.colors.warning.main:p.Ay.colors.success.main},H=[{title:"URL",dataIndex:"url",key:"url"},{title:"Method",dataIndex:"method",key:"method"},{title:"Status",dataIndex:"status",key:"status",render:function(e){return c.createElement(y,{type:e>=400?"danger":e>=300?"warning":"success",strong:!0},e)}},{title:"Time",dataIndex:"time",key:"time"},{title:"Size",dataIndex:"size",key:"size"}];return c.createElement(E,null,c.createElement(g,{level:4},"Performance Monitor"),c.createElement(f,null,"Monitor and analyze the performance of your application."),b&&c.createElement(m.Fc,{message:"Error",description:b,type:"error",showIcon:!0,closable:!0,onClose:function(){return z(null)},style:{marginBottom:p.Ay.spacing[4]}}),c.createElement(m.$x,{style:{marginBottom:p.Ay.spacing[4]}},c.createElement(m.$n,{icon:c.createElement(u.KF4,null),onClick:q,loading:l},"Refresh Metrics"),L&&c.createElement(y,{type:"secondary"},"Last updated: ",L.toLocaleTimeString())),c.createElement(m.tU,{activeKey:M,onChange:S},c.createElement(h,{tab:"Overview",key:"overview"},c.createElement(v,null,c.createElement(w,null,c.createElement(m.jL,{title:"Memory Usage",value:P(r.memory.used),suffix:" / ".concat(P(r.memory.total))}),c.createElement(m.ke,{percent:Math.round(r.memory.used/r.memory.total*100),status:r.memory.used/r.memory.total>.9?"exception":r.memory.used/r.memory.total>.7?"warning":"normal",strokeColor:G(r.memory.used/r.memory.total*100,{warning:70,danger:90})})),c.createElement(w,null,c.createElement(m.jL,{title:"CPU Usage",value:Math.round(r.cpu.usage),suffix:"%"}),c.createElement(m.ke,{percent:Math.round(r.cpu.usage),status:r.cpu.usage>90?"exception":r.cpu.usage>70?"warning":"normal",strokeColor:G(r.cpu.usage,{warning:70,danger:90})})),c.createElement(w,null,c.createElement(m.jL,{title:"FPS",value:r.rendering.fps,suffix:"fps"}),c.createElement(m.ke,{percent:Math.min(100,Math.round(r.rendering.fps/60*100)),status:r.rendering.fps<30?"exception":r.rendering.fps<50?"warning":"normal",strokeColor:G(r.rendering.fps,{warning:50,danger:30},!0)})),c.createElement(w,null,c.createElement(m.jL,{title:"Network Requests",value:r.network.requests}),c.createElement("div",{style:{marginTop:p.Ay.spacing[2]}},c.createElement(y,{type:r.network.errors>0?"danger":"success"},r.network.errors," errors"),c.createElement("br",null),c.createElement(y,{type:"secondary"},P(r.network.transferred)," transferred")))),c.createElement(k,null,c.createElement(y,{type:"secondary"},"Performance charts will be available in a future update"))),c.createElement(h,{tab:"Network",key:"network"},c.createElement(m.XI,{dataSource:[{key:"1",url:"/api/components",method:"GET",status:200,time:"120ms",size:"5.2KB"},{key:"2",url:"/api/layouts",method:"GET",status:200,time:"85ms",size:"3.8KB"},{key:"3",url:"/api/themes",method:"GET",status:200,time:"95ms",size:"2.1KB"},{key:"4",url:"/api/user",method:"GET",status:200,time:"110ms",size:"1.5KB"},{key:"5",url:"/api/settings",method:"GET",status:404,time:"75ms",size:"0.5KB"}],columns:H,pagination:!1,size:"small"})),c.createElement(h,{tab:"Components",key:"components"},c.createElement(v,null,c.createElement(w,null,c.createElement(m.jL,{title:"Component Count",value:r.components.count})),c.createElement(w,null,c.createElement(m.jL,{title:"Render Count",value:r.components.renderCount})),c.createElement(w,null,c.createElement(m.jL,{title:"Average Render Time",value:Math.round(r.rendering.renderTime),suffix:"ms"}))),c.createElement(m.Fc,{message:"Component Performance",description:"Detailed component performance metrics will be available in a future update.",type:"info",showIcon:!0}))))}}}]);