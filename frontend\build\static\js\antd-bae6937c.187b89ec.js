"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[7979],{5131:(e,o,r)=>{r.d(o,{A:()=>$});var n=r(36891),t=r(17595),i=r(50723),a=r(68734);var l=r(51113),c=r(14184),s=r(78690),d=r(51892);var g=r(45748),p=r(27484),u=r(77020);const b=(e,o)=>new u.Y(e).setA(o).toRgbString(),v=(e,o)=>new u.Y(e).lighten(o).toHexString(),m=e=>{const o=(0,g.cM)(e,{theme:"dark"});return{1:o[0],2:o[1],3:o[2],4:o[3],5:o[6],6:o[5],7:o[4],8:o[6],9:o[5],10:o[4]}},h=(e,o)=>{const r=e||"#000",n=o||"#fff";return{colorBgBase:r,colorTextBase:n,colorText:b(n,.85),colorTextSecondary:b(n,.65),colorTextTertiary:b(n,.45),colorTextQuaternary:b(n,.25),colorFill:b(n,.18),colorFillSecondary:b(n,.12),colorFillTertiary:b(n,.08),colorFillQuaternary:b(n,.04),colorBgSolid:b(n,.95),colorBgSolidHover:b(n,1),colorBgSolidActive:b(n,.9),colorBgElevated:v(r,12),colorBgContainer:v(r,8),colorBgLayout:v(r,0),colorBgSpotlight:v(r,26),colorBgBlur:b(n,.04),colorBorder:v(r,26),colorBorderSecondary:v(r,19)}},$={defaultSeed:l.sb.token,useToken:function(){const[e,o,r]=(0,l.rd)();return{theme:e,token:o,hashId:r}},defaultAlgorithm:c.A,darkAlgorithm:(e,o)=>{const r=Object.keys(i.r).map((o=>{const r=(0,g.cM)(e[o],{theme:"dark"});return Array.from({length:10},(()=>1)).reduce(((e,n,t)=>(e[`${o}-${t+1}`]=r[t],e[`${o}${t+1}`]=r[t],e)),{})})).reduce(((e,o)=>Object.assign(Object.assign({},e),o)),{}),n=null!=o?o:(0,c.A)(e);return Object.assign(Object.assign(Object.assign({},n),r),(0,p.A)(e,{generateColorPalettes:m,generateNeutralColorPalettes:h}))},compactAlgorithm:(e,o)=>{const r=null!=o?o:(0,c.A)(e),n=r.fontSizeSM,t=r.controlHeight-4;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},r),function(e){const{sizeUnit:o,sizeStep:r}=e,n=r-2;return{sizeXXL:o*(n+10),sizeXL:o*(n+6),sizeLG:o*(n+2),sizeMD:o*(n+2),sizeMS:o*(n+1),size:o*n,sizeSM:o*n,sizeXS:o*(n-1),sizeXXS:o*(n-1)}}(null!=o?o:e)),(0,d.A)(n)),{controlHeight:t}),(0,s.A)(Object.assign(Object.assign({},r),{controlHeight:t})))},getDesignToken:e=>{const o=(null==e?void 0:e.algorithm)?(0,n.an)(e.algorithm):t.A,r=Object.assign(Object.assign({},i.A),null==e?void 0:e.token);return(0,n.lO)(r,{override:null==e?void 0:e.token},o,a.A)},defaultConfig:l.sb,_internalContext:l.vG}},12075:(e,o,r)=>{r.d(o,{A:()=>M});var n=r(96540),t=r(55886),i=r(85539),a=r(68940),l=r(46942),c=r.n(l),s=r(55465),d=(r(18877),r(38674)),g=r(20934),p=r(829),u=r(23723);const b={motionAppear:!1,motionEnter:!0,motionLeave:!0};var v=r(82546),m=r(36891),h=r(25905),$=r(51113),f=r(38328);const S=e=>{const{componentCls:o,motionDurationSlow:r}=e;return[{[o]:{[`${o}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${r}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${r}`}}}}},[(0,f._j)(e,"slide-up"),(0,f._j)(e,"slide-down")]]},x=e=>{const{componentCls:o,tabsCardPadding:r,cardBg:n,cardGutter:t,colorBorderSecondary:i,itemSelectedColor:a}=e;return{[`${o}-card`]:{[`> ${o}-nav, > div > ${o}-nav`]:{[`${o}-tab`]:{margin:0,padding:r,background:n,border:`${(0,m.zA)(e.lineWidth)} ${e.lineType} ${i}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${o}-tab-active`]:{color:a,background:e.colorBgContainer},[`${o}-tab-focus`]:Object.assign({},(0,h.jk)(e,-3)),[`${o}-ink-bar`]:{visibility:"hidden"},[`& ${o}-tab${o}-tab-focus ${o}-tab-btn`]:{outline:"none"}},[`&${o}-top, &${o}-bottom`]:{[`> ${o}-nav, > div > ${o}-nav`]:{[`${o}-tab + ${o}-tab`]:{marginLeft:{_skip_check_:!0,value:(0,m.zA)(t)}}}},[`&${o}-top`]:{[`> ${o}-nav, > div > ${o}-nav`]:{[`${o}-tab`]:{borderRadius:`${(0,m.zA)(e.borderRadiusLG)} ${(0,m.zA)(e.borderRadiusLG)} 0 0`},[`${o}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${o}-bottom`]:{[`> ${o}-nav, > div > ${o}-nav`]:{[`${o}-tab`]:{borderRadius:`0 0 ${(0,m.zA)(e.borderRadiusLG)} ${(0,m.zA)(e.borderRadiusLG)}`},[`${o}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${o}-left, &${o}-right`]:{[`> ${o}-nav, > div > ${o}-nav`]:{[`${o}-tab + ${o}-tab`]:{marginTop:(0,m.zA)(t)}}},[`&${o}-left`]:{[`> ${o}-nav, > div > ${o}-nav`]:{[`${o}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,m.zA)(e.borderRadiusLG)} 0 0 ${(0,m.zA)(e.borderRadiusLG)}`}},[`${o}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${o}-right`]:{[`> ${o}-nav, > div > ${o}-nav`]:{[`${o}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,m.zA)(e.borderRadiusLG)} ${(0,m.zA)(e.borderRadiusLG)} 0`}},[`${o}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},y=e=>{const{componentCls:o,itemHoverColor:r,dropdownEdgeChildVerticalPadding:n}=e;return{[`${o}-dropdown`]:Object.assign(Object.assign({},(0,h.dF)(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${o}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${(0,m.zA)(n)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},h.L9),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${(0,m.zA)(e.paddingXXS)} ${(0,m.zA)(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:r}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},k=e=>{const{componentCls:o,margin:r,colorBorderSecondary:n,horizontalMargin:t,verticalItemPadding:i,verticalItemMargin:a,calc:l}=e;return{[`${o}-top, ${o}-bottom`]:{flexDirection:"column",[`> ${o}-nav, > div > ${o}-nav`]:{margin:t,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${(0,m.zA)(e.lineWidth)} ${e.lineType} ${n}`,content:"''"},[`${o}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},\n            right ${e.motionDurationSlow}`}},[`${o}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${o}-nav-wrap-ping-left::before`]:{opacity:1},[`&${o}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${o}-top`]:{[`> ${o}-nav,\n        > div > ${o}-nav`]:{"&::before":{bottom:0},[`${o}-ink-bar`]:{bottom:0}}},[`${o}-bottom`]:{[`> ${o}-nav, > div > ${o}-nav`]:{order:1,marginTop:r,marginBottom:0,"&::before":{top:0},[`${o}-ink-bar`]:{top:0}},[`> ${o}-content-holder, > div > ${o}-content-holder`]:{order:0}},[`${o}-left, ${o}-right`]:{[`> ${o}-nav, > div > ${o}-nav`]:{flexDirection:"column",minWidth:l(e.controlHeight).mul(1.25).equal(),[`${o}-tab`]:{padding:i,textAlign:"center"},[`${o}-tab + ${o}-tab`]:{margin:a},[`${o}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${o}-nav-wrap-ping-top::before`]:{opacity:1},[`&${o}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${o}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${o}-nav-list, ${o}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${o}-left`]:{[`> ${o}-nav, > div > ${o}-nav`]:{[`${o}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${o}-content-holder, > div > ${o}-content-holder`]:{marginLeft:{_skip_check_:!0,value:(0,m.zA)(l(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${(0,m.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${o}-content > ${o}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${o}-right`]:{[`> ${o}-nav, > div > ${o}-nav`]:{order:1,[`${o}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${o}-content-holder, > div > ${o}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:l(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${(0,m.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${o}-content > ${o}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},z=e=>{const{componentCls:o,cardPaddingSM:r,cardPaddingLG:n,cardHeightSM:t,cardHeightLG:i,horizontalItemPaddingSM:a,horizontalItemPaddingLG:l}=e;return{[o]:{"&-small":{[`> ${o}-nav`]:{[`${o}-tab`]:{padding:a,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${o}-nav`]:{[`${o}-tab`]:{padding:l,fontSize:e.titleFontSizeLG,lineHeight:e.lineHeightLG}}}},[`${o}-card`]:{[`&${o}-small`]:{[`> ${o}-nav`]:{[`${o}-tab`]:{padding:r},[`${o}-nav-add`]:{minWidth:t,minHeight:t}},[`&${o}-bottom`]:{[`> ${o}-nav ${o}-tab`]:{borderRadius:`0 0 ${(0,m.zA)(e.borderRadius)} ${(0,m.zA)(e.borderRadius)}`}},[`&${o}-top`]:{[`> ${o}-nav ${o}-tab`]:{borderRadius:`${(0,m.zA)(e.borderRadius)} ${(0,m.zA)(e.borderRadius)} 0 0`}},[`&${o}-right`]:{[`> ${o}-nav ${o}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,m.zA)(e.borderRadius)} ${(0,m.zA)(e.borderRadius)} 0`}}},[`&${o}-left`]:{[`> ${o}-nav ${o}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,m.zA)(e.borderRadius)} 0 0 ${(0,m.zA)(e.borderRadius)}`}}}},[`&${o}-large`]:{[`> ${o}-nav`]:{[`${o}-tab`]:{padding:n},[`${o}-nav-add`]:{minWidth:i,minHeight:i}}}}}},C=e=>{const{componentCls:o,itemActiveColor:r,itemHoverColor:n,iconCls:t,tabsHorizontalItemMargin:i,horizontalItemPadding:a,itemSelectedColor:l,itemColor:c}=e,s=`${o}-tab`;return{[s]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:a,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:c,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:r}},"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${s}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},(0,h.K8)(e)),"&:hover":{color:n},[`&${s}-active ${s}-btn`]:{color:l,textShadow:e.tabsActiveTextShadow},[`&${s}-focus ${s}-btn`]:Object.assign({},(0,h.jk)(e)),[`&${s}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${s}-disabled ${s}-btn, &${s}-disabled ${o}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${s}-remove ${t}`]:{margin:0},[`${t}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${s} + ${s}`]:{margin:{_skip_check_:!0,value:i}}}},O=e=>{const{componentCls:o,tabsHorizontalItemMarginRTL:r,iconCls:n,cardGutter:t,calc:i}=e,a=`${o}-rtl`;return{[a]:{direction:"rtl",[`${o}-nav`]:{[`${o}-tab`]:{margin:{_skip_check_:!0,value:r},[`${o}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[n]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,m.zA)(e.marginSM)}},[`${o}-tab-remove`]:{marginRight:{_skip_check_:!0,value:(0,m.zA)(e.marginXS)},marginLeft:{_skip_check_:!0,value:(0,m.zA)(i(e.marginXXS).mul(-1).equal())},[n]:{margin:0}}}},[`&${o}-left`]:{[`> ${o}-nav`]:{order:1},[`> ${o}-content-holder`]:{order:0}},[`&${o}-right`]:{[`> ${o}-nav`]:{order:0},[`> ${o}-content-holder`]:{order:1}},[`&${o}-card${o}-top, &${o}-card${o}-bottom`]:{[`> ${o}-nav, > div > ${o}-nav`]:{[`${o}-tab + ${o}-tab`]:{marginRight:{_skip_check_:!0,value:t},marginLeft:{_skip_check_:!0,value:0}}}}},[`${o}-dropdown-rtl`]:{direction:"rtl"},[`${o}-menu-item`]:{[`${o}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},A=e=>{const{componentCls:o,tabsCardPadding:r,cardHeight:n,cardGutter:t,itemHoverColor:i,itemActiveColor:a,colorBorderSecondary:l}=e;return{[o]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,h.dF)(e)),{display:"flex",[`> ${o}-nav, > div > ${o}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${o}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${o}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${o}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${o}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${o}-nav-more`]:{position:"relative",padding:r,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${o}-nav-add`]:Object.assign({minWidth:n,minHeight:n,marginLeft:{_skip_check_:!0,value:t},background:"transparent",border:`${(0,m.zA)(e.lineWidth)} ${e.lineType} ${l}`,borderRadius:`${(0,m.zA)(e.borderRadiusLG)} ${(0,m.zA)(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:i},"&:active, &:focus:not(:focus-visible)":{color:a}},(0,h.K8)(e,-3))},[`${o}-extra-content`]:{flex:"none"},[`${o}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),C(e)),{[`${o}-content`]:{position:"relative",width:"100%"},[`${o}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${o}-tabpane`]:Object.assign(Object.assign({},(0,h.K8)(e)),{"&-hidden":{display:"none"}})}),[`${o}-centered`]:{[`> ${o}-nav, > div > ${o}-nav`]:{[`${o}-nav-wrap`]:{[`&:not([class*='${o}-nav-wrap-ping']) > ${o}-nav-list`]:{margin:"auto"}}}}}},H=(0,$.OF)("Tabs",(e=>{const o=(0,$.oX)(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${(0,m.zA)(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${(0,m.zA)(e.horizontalItemGutter)}`});return[z(o),O(o),k(o),y(o),x(o),A(o),S(o)]}),(e=>{const{cardHeight:o,cardHeightSM:r,cardHeightLG:n,controlHeight:t,controlHeightLG:i}=e,a=o||i,l=r||t,c=n||i+8;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:a,cardHeightSM:l,cardHeightLG:c,cardPadding:`${(a-e.fontHeight)/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${(l-e.fontHeight)/2-e.lineWidth}px ${e.paddingXS}px`,cardPaddingLG:`${(c-e.fontHeightLG)/2-e.lineWidth}px ${e.padding}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}}));var w=r(83813);const B=e=>{var o,r,l,m,h,$,f,S,x,y,k;const{type:z,className:C,rootClassName:O,size:A,onEdit:w,hideAdd:B,centered:M,addIcon:_,removeIcon:T,moreIcon:L,more:j,popupClassName:I,children:P,items:X,animated:R,style:E,indicatorSize:D,indicator:G,destroyInactiveTabPane:F,destroyOnHidden:W}=e,N=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r}(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator","destroyInactiveTabPane","destroyOnHidden"]),{prefixCls:Q}=N,{direction:Y,tabs:U,getPrefixCls:V,getPopupContainer:q}=n.useContext(d.QO),K=V("tabs",Q),Z=(0,g.A)(K),[J,ee,oe]=H(K,Z);let re;"editable-card"===z&&(re={onEdit:(e,{key:o,event:r})=>{null==w||w("add"===e?r:o,e)},removeIcon:null!==(o=null!=T?T:null==U?void 0:U.removeIcon)&&void 0!==o?o:n.createElement(t.A,null),addIcon:(null!=_?_:null==U?void 0:U.addIcon)||n.createElement(a.A,null),showAdd:!0!==B});const ne=V(),te=(0,p.A)(A),ie=function(e,o){return e?e.map((e=>{var o;const r=null!==(o=e.destroyOnHidden)&&void 0!==o?o:e.destroyInactiveTabPane;return Object.assign(Object.assign({},e),{destroyInactiveTabPane:r})})):function(e){return e.filter((e=>e))}((0,v.A)(o).map((e=>{if(n.isValidElement(e)){const{key:o,props:r}=e,n=r||{},{tab:t}=n,i=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r}(n,["tab"]);return Object.assign(Object.assign({key:String(o)},i),{label:t})}return null})))}(X,P),ae=function(e,o={inkBar:!0,tabPane:!1}){let r;return r=!1===o?{inkBar:!1,tabPane:!1}:!0===o?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"==typeof o?o:{}),r.tabPane&&(r.tabPaneMotion=Object.assign(Object.assign({},b),{motionName:(0,u.b)(e,"switch")})),r}(K,R),le=Object.assign(Object.assign({},null==U?void 0:U.style),E),ce={align:null!==(r=null==G?void 0:G.align)&&void 0!==r?r:null===(l=null==U?void 0:U.indicator)||void 0===l?void 0:l.align,size:null!==(f=null!==(h=null!==(m=null==G?void 0:G.size)&&void 0!==m?m:D)&&void 0!==h?h:null===($=null==U?void 0:U.indicator)||void 0===$?void 0:$.size)&&void 0!==f?f:null==U?void 0:U.indicatorSize};return J(n.createElement(s.A,Object.assign({direction:Y,getPopupContainer:q},N,{items:ie,className:c()({[`${K}-${te}`]:te,[`${K}-card`]:["card","editable-card"].includes(z),[`${K}-editable-card`]:"editable-card"===z,[`${K}-centered`]:M},null==U?void 0:U.className,C,O,ee,oe,Z),popupClassName:c()(I,ee,oe,Z),style:le,editable:re,more:Object.assign({icon:null!==(k=null!==(y=null!==(x=null===(S=null==U?void 0:U.more)||void 0===S?void 0:S.icon)&&void 0!==x?x:null==U?void 0:U.moreIcon)&&void 0!==y?y:L)&&void 0!==k?k:n.createElement(i.A,null),transitionName:`${ne}-slide-up`},j),prefixCls:K,animated:ae,indicator:ce,destroyInactiveTabPane:null!=W?W:F})))};B.TabPane=w.A;const M=B},14184:(e,o,r)=>{r.d(o,{A:()=>b});var n=r(45748),t=r(50723),i=r(27484);const a=e=>{let o=e,r=e,n=e,t=e;return e<6&&e>=5?o=e+1:e<16&&e>=6?o=e+2:e>=16&&(o=16),e<7&&e>=5?r=4:e<8&&e>=7?r=5:e<14&&e>=8?r=6:e<16&&e>=14?r=7:e>=16&&(r=8),e<6&&e>=2?n=1:e>=6&&(n=2),e>4&&e<8?t=4:e>=8&&(t=6),{borderRadius:e,borderRadiusXS:n,borderRadiusSM:r,borderRadiusLG:o,borderRadiusOuter:t}};var l=r(78690),c=r(51892),s=r(77020);const d=(e,o)=>new s.Y(e).setA(o).toRgbString(),g=(e,o)=>new s.Y(e).darken(o).toHexString(),p=e=>{const o=(0,n.cM)(e);return{1:o[0],2:o[1],3:o[2],4:o[3],5:o[4],6:o[5],7:o[6],8:o[4],9:o[5],10:o[6]}},u=(e,o)=>{const r=e||"#fff",n=o||"#000";return{colorBgBase:r,colorTextBase:n,colorText:d(n,.88),colorTextSecondary:d(n,.65),colorTextTertiary:d(n,.45),colorTextQuaternary:d(n,.25),colorFill:d(n,.15),colorFillSecondary:d(n,.06),colorFillTertiary:d(n,.04),colorFillQuaternary:d(n,.02),colorBgSolid:d(n,1),colorBgSolidHover:d(n,.75),colorBgSolidActive:d(n,.95),colorBgLayout:g(r,4),colorBgContainer:g(r,0),colorBgElevated:g(r,0),colorBgSpotlight:d(n,.85),colorBgBlur:"transparent",colorBorder:g(r,15),colorBorderSecondary:g(r,6)}};function b(e){n.uy.pink=n.uy.magenta,n.UA.pink=n.UA.magenta;const o=Object.keys(t.r).map((o=>{const r=e[o]===n.uy[o]?n.UA[o]:(0,n.cM)(e[o]);return Array.from({length:10},(()=>1)).reduce(((e,n,t)=>(e[`${o}-${t+1}`]=r[t],e[`${o}${t+1}`]=r[t],e)),{})})).reduce(((e,o)=>Object.assign(Object.assign({},e),o)),{});return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},e),o),(0,i.A)(e,{generateColorPalettes:p,generateNeutralColorPalettes:u})),(0,c.A)(e.fontSize)),function(e){const{sizeUnit:o,sizeStep:r}=e;return{sizeXXL:o*(r+8),sizeXL:o*(r+4),sizeLG:o*(r+2),sizeMD:o*(r+1),sizeMS:o*r,size:o*r,sizeSM:o*(r-1),sizeXS:o*(r-2),sizeXXS:o*(r-3)}}(e)),(0,l.A)(e)),function(e){const{motionUnit:o,motionBase:r,borderRadius:n,lineWidth:t}=e;return Object.assign({motionDurationFast:`${(r+o).toFixed(1)}s`,motionDurationMid:`${(r+2*o).toFixed(1)}s`,motionDurationSlow:`${(r+3*o).toFixed(1)}s`,lineWidthBold:t+1},a(n))}(e))}},17595:(e,o,r)=>{r.d(o,{A:()=>i});var n=r(36891),t=r(14184);const i=(0,n.an)(t.A)},27484:(e,o,r)=>{r.d(o,{A:()=>t});var n=r(77020);function t(e,{generateColorPalettes:o,generateNeutralColorPalettes:r}){const{colorSuccess:t,colorWarning:i,colorError:a,colorInfo:l,colorPrimary:c,colorBgBase:s,colorTextBase:d}=e,g=o(c),p=o(t),u=o(i),b=o(a),v=o(l),m=r(s,d),h=o(e.colorLink||e.colorInfo),$=new n.Y(b[1]).mix(new n.Y(b[3]),50).toHexString();return Object.assign(Object.assign({},m),{colorPrimaryBg:g[1],colorPrimaryBgHover:g[2],colorPrimaryBorder:g[3],colorPrimaryBorderHover:g[4],colorPrimaryHover:g[5],colorPrimary:g[6],colorPrimaryActive:g[7],colorPrimaryTextHover:g[8],colorPrimaryText:g[9],colorPrimaryTextActive:g[10],colorSuccessBg:p[1],colorSuccessBgHover:p[2],colorSuccessBorder:p[3],colorSuccessBorderHover:p[4],colorSuccessHover:p[4],colorSuccess:p[6],colorSuccessActive:p[7],colorSuccessTextHover:p[8],colorSuccessText:p[9],colorSuccessTextActive:p[10],colorErrorBg:b[1],colorErrorBgHover:b[2],colorErrorBgFilledHover:$,colorErrorBgActive:b[3],colorErrorBorder:b[3],colorErrorBorderHover:b[4],colorErrorHover:b[5],colorError:b[6],colorErrorActive:b[7],colorErrorTextHover:b[8],colorErrorText:b[9],colorErrorTextActive:b[10],colorWarningBg:u[1],colorWarningBgHover:u[2],colorWarningBorder:u[3],colorWarningBorderHover:u[4],colorWarningHover:u[4],colorWarning:u[6],colorWarningActive:u[7],colorWarningTextHover:u[8],colorWarningText:u[9],colorWarningTextActive:u[10],colorInfoBg:v[1],colorInfoBgHover:v[2],colorInfoBorder:v[3],colorInfoBorderHover:v[4],colorInfoHover:v[4],colorInfo:v[6],colorInfoActive:v[7],colorInfoTextHover:v[8],colorInfoText:v[9],colorInfoTextActive:v[10],colorLinkHover:h[4],colorLink:h[6],colorLinkActive:h[7],colorBgMask:new n.Y("#000").setA(.45).toRgbString(),colorWhite:"#fff"})}},49806:(e,o,r)=>{r.d(o,{sb:()=>a,vG:()=>l,zQ:()=>i.A});var n=r(96540),t=r(50723),i=r(17595);const a={token:t.A,override:{override:t.A},hashed:!0},l=n.createContext(a)},50723:(e,o,r)=>{r.d(o,{A:()=>t,r:()=>n});const n={blue:"#1677FF",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#EB2F96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911"},t=Object.assign(Object.assign({},n),{colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff4d4f",colorInfo:"#1677ff",colorLink:"",colorTextBase:"",colorBgBase:"",fontFamily:"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,\n'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',\n'Noto Color Emoji'",fontFamilyCode:"'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInBack:"cubic-bezier(0.71, -0.46, 0.88, 0.6)",motionEaseInQuint:"cubic-bezier(0.755, 0.05, 0.855, 0.06)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:6,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,motion:!0})},51113:(e,o,r)=>{r.d(o,{vG:()=>m.vG,sW:()=>i.s,sb:()=>m.sb,Or:()=>p,nP:()=>b,OF:()=>g,bf:()=>u,ks:()=>a.k,oX:()=>t.oX,Xo:()=>v,rd:()=>l.Ay});var n=r(36891),t=r(14277),i=r(77523),a=r(94925),l=r(93093),c=r(96540),s=r(62279),d=r(25905);const{genStyleHooks:g,genComponentStyleHook:p,genSubStyleComponent:u}=(0,t.L_)({usePrefix:()=>{const{getPrefixCls:e,iconPrefixCls:o}=(0,c.useContext)(s.QO);return{rootPrefixCls:e(),iconPrefixCls:o}},useToken:()=>{const[e,o,r,n,t]=(0,l.Ay)();return{theme:e,realToken:o,hashId:r,token:n,cssVar:t}},useCSP:()=>{const{csp:e}=(0,c.useContext)(s.QO);return null!=e?e:{}},getResetStyles:(e,o)=>{var r;const n=(0,d.av)(e);return[n,{"&":n},(0,d.jz)(null!==(r=null==o?void 0:o.prefix.iconPrefixCls)&&void 0!==r?r:s.pM)]},getCommonStyle:d.vj,getCompUnitless:()=>l.Is});function b(e,o){return i.s.reduce(((r,n)=>{const t=e[`${n}1`],i=e[`${n}3`],a=e[`${n}6`],l=e[`${n}7`];return Object.assign(Object.assign({},r),o(n,{lightColor:t,lightBorderColor:i,darkColor:a,textColor:l}))}),{})}const v=(e,o)=>{const[r,t]=(0,l.Ay)();return(0,n.IV)({theme:r,token:t,hashId:"",path:["ant-design-icons",e],nonce:()=>null==o?void 0:o.nonce,layer:{name:"antd"}},(()=>[(0,d.jz)(e)]))};var m=r(49806)},51892:(e,o,r)=>{r.d(o,{A:()=>t});var n=r(94925);const t=e=>{const o=(0,n.A)(e),r=o.map((e=>e.size)),t=o.map((e=>e.lineHeight)),i=r[1],a=r[0],l=r[2],c=t[1],s=t[0],d=t[2];return{fontSizeSM:a,fontSize:i,fontSizeLG:l,fontSizeXL:r[3],fontSizeHeading1:r[6],fontSizeHeading2:r[5],fontSizeHeading3:r[4],fontSizeHeading4:r[3],fontSizeHeading5:r[2],lineHeight:c,lineHeightLG:d,lineHeightSM:s,fontHeight:Math.round(c*i),fontHeightLG:Math.round(d*l),fontHeightSM:Math.round(s*a),lineHeightHeading1:t[6],lineHeightHeading2:t[5],lineHeightHeading3:t[4],lineHeightHeading4:t[3],lineHeightHeading5:t[2]}}},67034:(e,o,r)=>{r.d(o,{A:()=>A});var n=r(96540),t=r(46942),i=r.n(t),a=r(19853),l=r(54121),c=r(70064),s=r(40682),d=(r(18877),r(57)),g=r(38674),p=r(36891),u=r(77020),b=r(25905),v=r(51113);const m=e=>{const{lineWidth:o,fontSizeIcon:r,calc:n}=e,t=e.fontSizeSM;return(0,v.oX)(e,{tagFontSize:t,tagLineHeight:(0,p.zA)(n(e.lineHeightSM).mul(t).equal()),tagIconSize:n(r).sub(n(o).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},h=e=>({defaultBg:new u.Y(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),$=(0,v.OF)("Tag",(e=>(e=>{const{paddingXXS:o,lineWidth:r,tagPaddingHorizontal:n,componentCls:t,calc:i}=e,a=i(n).sub(r).equal(),l=i(o).sub(r).equal();return{[t]:Object.assign(Object.assign({},(0,b.dF)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:a,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${t}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${t}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${t}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${t}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:a}}),[`${t}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(m(e))),h);const f=n.forwardRef(((e,o)=>{const{prefixCls:r,style:t,className:a,checked:l,onChange:c,onClick:s}=e,d=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r}(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:u}=n.useContext(g.QO),b=p("tag",r),[v,m,h]=$(b),f=i()(b,`${b}-checkable`,{[`${b}-checkable-checked`]:l},null==u?void 0:u.className,a,m,h);return v(n.createElement("span",Object.assign({},d,{ref:o,style:Object.assign(Object.assign({},t),null==u?void 0:u.style),className:f,onClick:e=>{null==c||c(!l),null==s||s(e)}})))})),S=f,x=(0,v.bf)(["Tag","preset"],(e=>(e=>(0,v.nP)(e,((o,{textColor:r,lightBorderColor:n,lightColor:t,darkColor:i})=>({[`${e.componentCls}${e.componentCls}-${o}`]:{color:r,background:t,borderColor:n,"&-inverse":{color:e.colorTextLightSolid,background:i,borderColor:i},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}))))(m(e))),h);var y=r(44023);const k=(e,o,r)=>{const n=(0,y.A)(r);return{[`${e.componentCls}${e.componentCls}-${o}`]:{color:e[`color${r}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},z=(0,v.bf)(["Tag","status"],(e=>{const o=m(e);return[k(o,"success","Success"),k(o,"processing","Info"),k(o,"error","Error"),k(o,"warning","Warning")]}),h);const C=n.forwardRef(((e,o)=>{const{prefixCls:r,className:t,rootClassName:p,style:u,children:b,icon:v,color:m,onClose:h,bordered:f=!0,visible:S}=e,y=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r}(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:k,direction:C,tag:O}=n.useContext(g.QO),[A,H]=n.useState(!0),w=(0,a.A)(y,["closeIcon","closable"]);n.useEffect((()=>{void 0!==S&&H(S)}),[S]);const B=(0,l.nP)(m),M=(0,l.ZZ)(m),_=B||M,T=Object.assign(Object.assign({backgroundColor:m&&!_?m:void 0},null==O?void 0:O.style),u),L=k("tag",r),[j,I,P]=$(L),X=i()(L,null==O?void 0:O.className,{[`${L}-${m}`]:_,[`${L}-has-color`]:m&&!_,[`${L}-hidden`]:!A,[`${L}-rtl`]:"rtl"===C,[`${L}-borderless`]:!f},t,p,I,P),R=e=>{e.stopPropagation(),null==h||h(e),e.defaultPrevented||H(!1)},[,E]=(0,c.A)((0,c.d)(e),(0,c.d)(O),{closable:!1,closeIconRender:e=>{const o=n.createElement("span",{className:`${L}-close-icon`,onClick:R},e);return(0,s.fx)(e,o,(e=>({onClick:o=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,o),R(o)},className:i()(null==e?void 0:e.className,`${L}-close-icon`)})))}}),D="function"==typeof y.onClick||b&&"a"===b.type,G=v||null,F=G?n.createElement(n.Fragment,null,G,b&&n.createElement("span",null,b)):b,W=n.createElement("span",Object.assign({},w,{ref:o,className:X,style:T}),F,E,B&&n.createElement(x,{key:"preset",prefixCls:L}),M&&n.createElement(z,{key:"status",prefixCls:L}));return j(D?n.createElement(d.A,{component:"Tag"},W):W)})),O=C;O.CheckableTag=S;const A=O},68734:(e,o,r)=>{r.d(o,{A:()=>l});var n=r(77020),t=r(50723),i=r(85045),a=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r};function l(e){const{override:o}=e,r=a(e,["override"]),l=Object.assign({},o);Object.keys(t.A).forEach((e=>{delete l[e]}));const c=Object.assign(Object.assign({},r),l);if(!1===c.motion){const e="0s";c.motionDurationFast=e,c.motionDurationMid=e,c.motionDurationSlow=e}return Object.assign(Object.assign(Object.assign({},c),{colorFillContent:c.colorFillSecondary,colorFillContentHover:c.colorFill,colorFillAlter:c.colorFillQuaternary,colorBgContainerDisabled:c.colorFillTertiary,colorBorderBg:c.colorBgContainer,colorSplit:(0,i.A)(c.colorBorderSecondary,c.colorBgContainer),colorTextPlaceholder:c.colorTextQuaternary,colorTextDisabled:c.colorTextQuaternary,colorTextHeading:c.colorText,colorTextLabel:c.colorTextSecondary,colorTextDescription:c.colorTextTertiary,colorTextLightSolid:c.colorWhite,colorHighlight:c.colorError,colorBgTextHover:c.colorFillSecondary,colorBgTextActive:c.colorFill,colorIcon:c.colorTextTertiary,colorIconHover:c.colorText,colorErrorOutline:(0,i.A)(c.colorErrorBg,c.colorBgContainer),colorWarningOutline:(0,i.A)(c.colorWarningBg,c.colorBgContainer),fontSizeIcon:c.fontSizeSM,lineWidthFocus:3*c.lineWidth,lineWidth:c.lineWidth,controlOutlineWidth:2*c.lineWidth,controlInteractiveSize:c.controlHeight/2,controlItemBgHover:c.colorFillTertiary,controlItemBgActive:c.colorPrimaryBg,controlItemBgActiveHover:c.colorPrimaryBgHover,controlItemBgActiveDisabled:c.colorFill,controlTmpOutline:c.colorFillQuaternary,controlOutline:(0,i.A)(c.colorPrimaryBg,c.colorBgContainer),lineType:c.lineType,borderRadius:c.borderRadius,borderRadiusXS:c.borderRadiusXS,borderRadiusSM:c.borderRadiusSM,borderRadiusLG:c.borderRadiusLG,fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:c.sizeXXS,paddingXS:c.sizeXS,paddingSM:c.sizeSM,padding:c.size,paddingMD:c.sizeMD,paddingLG:c.sizeLG,paddingXL:c.sizeXL,paddingContentHorizontalLG:c.sizeLG,paddingContentVerticalLG:c.sizeMS,paddingContentHorizontal:c.sizeMS,paddingContentVertical:c.sizeSM,paddingContentHorizontalSM:c.size,paddingContentVerticalSM:c.sizeXS,marginXXS:c.sizeXXS,marginXS:c.sizeXS,marginSM:c.sizeSM,margin:c.size,marginMD:c.sizeMD,marginLG:c.sizeLG,marginXL:c.sizeXL,marginXXL:c.sizeXXL,boxShadow:"\n      0 6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 9px 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowSecondary:"\n      0 6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 9px 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowTertiary:"\n      0 1px 2px 0 rgba(0, 0, 0, 0.03),\n      0 1px 6px -1px rgba(0, 0, 0, 0.02),\n      0 2px 4px 0 rgba(0, 0, 0, 0.02)\n    ",screenXS:480,screenXSMin:480,screenXSMax:575,screenSM:576,screenSMMin:576,screenSMMax:767,screenMD:768,screenMDMin:768,screenMDMax:991,screenLG:992,screenLGMin:992,screenLGMax:1199,screenXL:1200,screenXLMin:1200,screenXLMax:1599,screenXXL:1600,screenXXLMin:1600,boxShadowPopoverArrow:"2px 2px 5px rgba(0, 0, 0, 0.05)",boxShadowCard:`\n      0 1px 2px -2px ${new n.Y("rgba(0, 0, 0, 0.16)").toRgbString()},\n      0 3px 6px 0 ${new n.Y("rgba(0, 0, 0, 0.12)").toRgbString()},\n      0 5px 12px 4px ${new n.Y("rgba(0, 0, 0, 0.09)").toRgbString()}\n    `,boxShadowDrawerRight:"\n      -6px 0 16px 0 rgba(0, 0, 0, 0.08),\n      -3px 0 6px -4px rgba(0, 0, 0, 0.12),\n      -9px 0 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowDrawerLeft:"\n      6px 0 16px 0 rgba(0, 0, 0, 0.08),\n      3px 0 6px -4px rgba(0, 0, 0, 0.12),\n      9px 0 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowDrawerUp:"\n      0 6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 9px 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowDrawerDown:"\n      0 -6px 16px 0 rgba(0, 0, 0, 0.08),\n      0 -3px 6px -4px rgba(0, 0, 0, 0.12),\n      0 -9px 28px 8px rgba(0, 0, 0, 0.05)\n    ",boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)"}),l)}},77523:(e,o,r)=>{r.d(o,{s:()=>n});const n=["blue","purple","cyan","green","magenta","pink","red","orange","yellow","volcano","geekblue","lime","gold"]},78690:(e,o,r)=>{r.d(o,{A:()=>n});const n=e=>{const{controlHeight:o}=e;return{controlHeightSM:.75*o,controlHeightXS:.5*o,controlHeightLG:1.25*o}}},85045:(e,o,r)=>{r.d(o,{A:()=>i});var n=r(77020);function t(e){return e>=0&&e<=255}const i=function(e,o){const{r,g:i,b:a,a:l}=new n.Y(e).toRgb();if(l<1)return e;const{r:c,g:s,b:d}=new n.Y(o).toRgb();for(let e=.01;e<=1;e+=.01){const o=Math.round((r-c*(1-e))/e),l=Math.round((i-s*(1-e))/e),g=Math.round((a-d*(1-e))/e);if(t(o)&&t(l)&&t(g))return new n.Y({r:o,g:l,b:g,a:Math.round(100*e)/100}).toRgbString()}return new n.Y({r,g:i,b:a,a:1}).toRgbString()}},93093:(e,o,r)=>{r.d(o,{Ay:()=>b,Is:()=>d});var n=r(96540),t=r(36891),i=r(25640),a=r(49806),l=r(50723),c=r(68734),s=function(e,o){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&o.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(n=Object.getOwnPropertySymbols(e);t<n.length;t++)o.indexOf(n[t])<0&&Object.prototype.propertyIsEnumerable.call(e,n[t])&&(r[n[t]]=e[n[t]])}return r};const d={lineHeight:!0,lineHeightSM:!0,lineHeightLG:!0,lineHeightHeading1:!0,lineHeightHeading2:!0,lineHeightHeading3:!0,lineHeightHeading4:!0,lineHeightHeading5:!0,opacityLoading:!0,fontWeightStrong:!0,zIndexPopupBase:!0,zIndexBase:!0,opacityImage:!0},g={size:!0,sizeSM:!0,sizeLG:!0,sizeMD:!0,sizeXS:!0,sizeXXS:!0,sizeMS:!0,sizeXL:!0,sizeXXL:!0,sizeUnit:!0,sizeStep:!0,motionBase:!0,motionUnit:!0},p={screenXS:!0,screenXSMin:!0,screenXSMax:!0,screenSM:!0,screenSMMin:!0,screenSMMax:!0,screenMD:!0,screenMDMin:!0,screenMDMax:!0,screenLG:!0,screenLGMin:!0,screenLGMax:!0,screenXL:!0,screenXLMin:!0,screenXLMax:!0,screenXXL:!0,screenXXLMin:!0},u=(e,o,r)=>{const n=r.getDerivativeToken(e),{override:t}=o,i=s(o,["override"]);let a=Object.assign(Object.assign({},n),{override:t});return a=(0,c.A)(a),i&&Object.entries(i).forEach((([e,o])=>{const{theme:r}=o,n=s(o,["theme"]);let t=n;r&&(t=u(Object.assign(Object.assign({},a),n),{override:n},r)),a[e]=t})),a};function b(){const{token:e,hashed:o,theme:r,override:s,cssVar:b}=n.useContext(a.vG),v=`${i.A}-${o||""}`,m=r||a.zQ,[h,$,f]=(0,t.hV)(m,[l.A,e],{salt:v,override:s,getComputedToken:u,formatToken:c.A,cssVar:b&&{prefix:b.prefix,key:b.key,unitless:d,ignore:g,preserve:p}});return[m,f,o?$:"",h,b]}},94925:(e,o,r)=>{function n(e){return(e+8)/e}function t(e){const o=Array.from({length:10}).map(((o,r)=>{const n=r-1,t=e*Math.pow(Math.E,n/5),i=r>1?Math.floor(t):Math.ceil(t);return 2*Math.floor(i/2)}));return o[1]=e,o.map((e=>({size:e,lineHeight:n(e)})))}r.d(o,{A:()=>t,k:()=>n})}}]);