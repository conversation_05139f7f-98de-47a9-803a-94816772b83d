import { createLazyComponent, LoadingStates } from '../utils/lazyLoading';

/**
 * Centralized Lazy Component Configuration
 * Manages all lazy-loaded components with appropriate loading states and error handling
 */

// Tutorial System Components
export const TutorialAssistant = createLazyComponent(
  () => import('../components/tutorial/TutorialAssistant'),
  {
    componentName: 'TutorialAssistant',
    fallback: LoadingStates.withDescription('Loading tutorial system...'),
    retryAttempts: 3,
    preload: false // Load on demand only
  }
);

export const TutorialOverlay = createLazyComponent(
  () => import('../components/tutorial/TutorialOverlay'),
  {
    componentName: 'TutorialOverlay',
    fallback: LoadingStates.minimal,
    retryAttempts: 2
  }
);

export const TutorialProgress = createLazyComponent(
  () => import('../components/tutorial/TutorialProgress'),
  {
    componentName: 'TutorialProgress',
    fallback: LoadingStates.minimal,
    retryAttempts: 2
  }
);

// AI Suggestions Components
export const AIDesignSuggestions = createLazyComponent(
  () => import('../components/ai/AIDesignSuggestions'),
  {
    componentName: 'AIDesignSuggestions',
    fallback: LoadingStates.withDescription('Loading AI suggestions engine...'),
    retryAttempts: 3,
    preload: false
  }
);

export const AISuggestionsPanel = createLazyComponent(
  () => import('../components/ai/AISuggestionsPanel'),
  {
    componentName: 'AISuggestionsPanel',
    fallback: LoadingStates.standard,
    retryAttempts: 2
  }
);

export const AILayoutSuggestions = createLazyComponent(
  () => import('../components/ai/AILayoutSuggestions'),
  {
    componentName: 'AILayoutSuggestions',
    fallback: LoadingStates.minimal,
    retryAttempts: 2
  }
);

// Template Management Components
export const TemplateManager = createLazyComponent(
  () => import('../components/templates/TemplateManager'),
  {
    componentName: 'TemplateManager',
    fallback: LoadingStates.withDescription('Loading template management...'),
    retryAttempts: 3,
    preload: false
  }
);

export const TemplateGallery = createLazyComponent(
  () => import('../components/templates/TemplateGallery'),
  {
    componentName: 'TemplateGallery',
    fallback: LoadingStates.standard,
    retryAttempts: 2
  }
);

export const TemplateEditor = createLazyComponent(
  () => import('../components/templates/TemplateEditor'),
  {
    componentName: 'TemplateEditor',
    fallback: LoadingStates.standard,
    retryAttempts: 2
  }
);

// Code Export Components
export const CodeExporter = createLazyComponent(
  () => import('../components/export/CodeExporter'),
  {
    componentName: 'CodeExporter',
    fallback: LoadingStates.withDescription('Loading code export functionality...'),
    retryAttempts: 3,
    preload: false
  }
);

export const ExportPreview = createLazyComponent(
  () => import('../components/export/ExportPreview'),
  {
    componentName: 'ExportPreview',
    fallback: LoadingStates.standard,
    retryAttempts: 2
  }
);

export const ExportSettings = createLazyComponent(
  () => import('../components/export/ExportSettings'),
  {
    componentName: 'ExportSettings',
    fallback: LoadingStates.minimal,
    retryAttempts: 2
  }
);

// Collaboration Components
export const CollaborationIndicator = createLazyComponent(
  () => import('../components/collaboration/CollaborationIndicator'),
  {
    componentName: 'CollaborationIndicator',
    fallback: LoadingStates.minimal,
    retryAttempts: 2,
    preload: true // Preload since it's likely to be used
  }
);

export const CollaborationPanel = createLazyComponent(
  () => import('../components/collaboration/CollaborationPanel'),
  {
    componentName: 'CollaborationPanel',
    fallback: LoadingStates.standard,
    retryAttempts: 2
  }
);

export const LiveCursors = createLazyComponent(
  () => import('../components/collaboration/LiveCursors'),
  {
    componentName: 'LiveCursors',
    fallback: LoadingStates.minimal,
    retryAttempts: 2
  }
);

// Advanced Property Editors
export const ColorPicker = createLazyComponent(
  () => import('../components/properties/ColorPicker'),
  {
    componentName: 'ColorPicker',
    fallback: LoadingStates.minimal,
    retryAttempts: 2,
    preload: false
  }
);

export const SpacingEditor = createLazyComponent(
  () => import('../components/properties/SpacingEditor'),
  {
    componentName: 'SpacingEditor',
    fallback: LoadingStates.minimal,
    retryAttempts: 2,
    preload: false
  }
);

export const TypographyEditor = createLazyComponent(
  () => import('../components/properties/TypographyEditor'),
  {
    componentName: 'TypographyEditor',
    fallback: LoadingStates.minimal,
    retryAttempts: 2,
    preload: false
  }
);

export const AnimationEditor = createLazyComponent(
  () => import('../components/properties/AnimationEditor'),
  {
    componentName: 'AnimationEditor',
    fallback: LoadingStates.minimal,
    retryAttempts: 2,
    preload: false
  }
);

// Preview Components
export const MultiDevicePreview = createLazyComponent(
  () => import('../components/preview/MultiDevicePreview'),
  {
    componentName: 'MultiDevicePreview',
    fallback: LoadingStates.withDescription('Loading device preview...'),
    retryAttempts: 2,
    preload: false
  }
);

export const ResponsivePreview = createLazyComponent(
  () => import('../components/preview/ResponsivePreview'),
  {
    componentName: 'ResponsivePreview',
    fallback: LoadingStates.standard,
    retryAttempts: 2,
    preload: false
  }
);

// Performance and Analytics
export const PerformanceMonitor = createLazyComponent(
  () => import('../components/performance/PerformanceMonitor'),
  {
    componentName: 'PerformanceMonitor',
    fallback: LoadingStates.minimal,
    retryAttempts: 2,
    preload: false
  }
);

export const AnalyticsPanel = createLazyComponent(
  () => import('../components/analytics/AnalyticsPanel'),
  {
    componentName: 'AnalyticsPanel',
    fallback: LoadingStates.standard,
    retryAttempts: 2,
    preload: false
  }
);

// Enhanced Components (if they exist and are large)
export const ComponentBuilder = createLazyComponent(
  () => import('../components/enhanced/ComponentBuilder').catch(() => {
    // Fallback if component doesn't exist
    return { default: () => <div>Component Builder not available</div> };
  }),
  {
    componentName: 'ComponentBuilder',
    fallback: LoadingStates.withDescription('Loading component builder...'),
    retryAttempts: 2,
    preload: false
  }
);

// Grouping for progressive loading
export const TutorialComponents = [
  TutorialAssistant,
  TutorialOverlay,
  TutorialProgress
];

export const AIComponents = [
  AIDesignSuggestions,
  AISuggestionsPanel,
  AILayoutSuggestions
];

export const TemplateComponents = [
  TemplateManager,
  TemplateGallery,
  TemplateEditor
];

export const ExportComponents = [
  CodeExporter,
  ExportPreview,
  ExportSettings
];

export const CollaborationComponents = [
  CollaborationIndicator,
  CollaborationPanel,
  LiveCursors
];

export const PropertyEditorComponents = [
  ColorPicker,
  SpacingEditor,
  TypographyEditor,
  AnimationEditor
];

export const PreviewComponents = [
  MultiDevicePreview,
  ResponsivePreview
];

// Feature-based component groups for conditional loading
export const FeatureComponents = {
  tutorial: TutorialComponents,
  aiSuggestions: AIComponents,
  templates: TemplateComponents,
  codeExport: ExportComponents,
  collaboration: CollaborationComponents,
  advancedProperties: PropertyEditorComponents,
  preview: PreviewComponents
};

// Priority loading order (high priority components load first)
export const LoadingPriority = {
  high: [CollaborationIndicator], // Always visible
  medium: [TutorialAssistant, AIDesignSuggestions], // Feature entry points
  low: [TemplateManager, CodeExporter], // On-demand features
  veryLow: PropertyEditorComponents.concat(PreviewComponents) // Specific use cases
};
