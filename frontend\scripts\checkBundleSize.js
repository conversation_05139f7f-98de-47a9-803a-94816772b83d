/**
 * Bundle Size Checker
 * 
 * This script analyzes the bundle stats and checks if the bundle size exceeds the threshold.
 * It can be used in CI/CD pipelines to prevent deploying bundles that are too large.
 */

const fs = require('fs');
const path = require('path');
const chalk = require('chalk');
const { testPreloads } = require('../src/utils/resourceHintsTester');

function validatePreloads() {
  const results = testPreloads();
  const report = results.validationReport;

  // Print validation results
  console.log(chalk.bold('\n🔍 Preload Validation Results\n'));

  let hasErrors = false;

  if (report.errors.length > 0) {
    hasErrors = true;
    console.log(chalk.red('Critical Preload Errors:'));
    report.errors.forEach(error => {
      console.log(chalk.red(`  ✘ ${error}`));

      // Extract resource path from error
      const resourceMatch = error.match(/resource ['"]([^'"]+)['"]/);
      if (resourceMatch) {
        const resource = resourceMatch[1];
        console.log(chalk.yellow(`    → Suggestion: Verify that '${resource}' exists and is correctly referenced`));

        // Check if resource is actually needed
        if (!isResourceRequired(resource)) {
          console.log(chalk.yellow(`    → Consider removing this preload if the resource is not critical`));
        }
      }
    });
  }

  if (report.warnings.length > 0) {
    console.log(chalk.yellow('\nPreload Warnings:'));
    report.warnings.forEach(warning => {
      console.log(chalk.yellow(`  ⚠ ${warning}`));

      // Provide specific suggestions based on warning type
      if (warning.includes('unused')) {
        console.log(chalk.gray(`    → Remove unused preload to improve performance`));
      } else if (warning.includes('late')) {
        console.log(chalk.gray(`    → Consider moving this preload earlier in the document`));
      }
    });
  }

  // Add performance suggestions
  console.log(chalk.cyan('\nPerformance Suggestions:'));
  const suggestions = generatePreloadSuggestions(results);
  suggestions.forEach(suggestion => {
    console.log(`  • ${suggestion}`);
  });

  console.log(chalk.bold('\nSummary:'));
  console.log(`  Errors: ${chalk.red(report.summary.errorCount)}`);
  console.log(`  Warnings: ${chalk.yellow(report.summary.warningCount)}`);
  console.log(`  Status: ${report.summary.status === 'PASS'
    ? chalk.green('PASS')
    : chalk.red('FAIL')}`);

  return !hasErrors;
}

function isResourceRequired(resource) {
  // Add logic to check if resource is referenced in your bundled code
  // This is a simplified example
  const bundleContent = fs.readFileSync('path/to/bundle.js', 'utf8');
  return bundleContent.includes(resource);
}

function generatePreloadSuggestions(results) {
  const suggestions = [];

  // Add specific suggestions based on validation results
  if (results.resourceHints.preload.unused.length > 0) {
    suggestions.push('Remove unused preload hints to reduce bandwidth waste');
  }

  if (results.resourceHints.preload.late.length > 0) {
    suggestions.push('Move critical resource preloads to the document head');
  }

  // Add general best practices
  suggestions.push('Consider using "fetchpriority" attribute for critical resources');
  suggestions.push('Ensure preloaded resources match their final URL exactly');

  return suggestions;
}

// Configuration with more restrictive thresholds
const CONFIG = {
  statsFile: path.join(__dirname, '../bundle-stats.json'),
  thresholds: {
    main: 244 * 1024, // 244 KB (recommended limit)
    vendor: 500 * 1024, // 500 KB (more restrictive)
    antd: 244 * 1024, // 244 KB for Ant Design bundle
    'antd-icons': 100 * 1024, // 100 KB for Ant Design icons
    react: 200 * 1024, // 200 KB for React bundle
    common: 100 * 1024, // 100 KB for common code
    runtime: 50 * 1024, // 50 KB for runtime
    total: 1.5 * 1024 * 1024 // 1.5 MB total (more restrictive)
  },
  // Previous stats file to compare with
  previousStatsFile: path.join(__dirname, '../bundle-stats-previous.json'),
  // Whether to save current stats as previous for future comparison
  savePreviousStats: true
};

// Check if the stats file exists
if (!fs.existsSync(CONFIG.statsFile)) {
  console.error(chalk.red('Error: Stats file not found. Run "npm run build:analyze" first.'));
  process.exit(1);
}

// Load the stats file
const stats = JSON.parse(fs.readFileSync(CONFIG.statsFile, 'utf8'));

// Extract asset sizes
const assets = stats.assets || [];
const assetSizes = {};
let totalSize = 0;

// Calculate sizes with enhanced categorization
assets.forEach(asset => {
  const name = asset.name;
  const size = asset.size;

  // Enhanced categorization for new bundle structure
  let category = 'other';
  if (name.includes('main')) {
    category = 'main';
  } else if (name.includes('antd-icons')) {
    category = 'antd-icons';
  } else if (name.includes('antd')) {
    category = 'antd';
  } else if (name.includes('react')) {
    category = 'react';
  } else if (name.includes('vendor')) {
    category = 'vendor';
  } else if (name.includes('common')) {
    category = 'common';
  } else if (name.includes('runtime')) {
    category = 'runtime';
  }

  // Add to category
  if (!assetSizes[category]) {
    assetSizes[category] = 0;
  }
  assetSizes[category] += size;

  // Add to total
  totalSize += size;
});

// Load previous stats if available
let previousAssetSizes = {};
let previousTotalSize = 0;
if (fs.existsSync(CONFIG.previousStatsFile)) {
  try {
    const previousStats = JSON.parse(fs.readFileSync(CONFIG.previousStatsFile, 'utf8'));
    const previousAssets = previousStats.assets || [];

    // Calculate previous sizes with enhanced categorization
    previousAssets.forEach(asset => {
      const name = asset.name;
      const size = asset.size;

      // Enhanced categorization for new bundle structure
      let category = 'other';
      if (name.includes('main')) {
        category = 'main';
      } else if (name.includes('antd-icons')) {
        category = 'antd-icons';
      } else if (name.includes('antd')) {
        category = 'antd';
      } else if (name.includes('react')) {
        category = 'react';
      } else if (name.includes('vendor')) {
        category = 'vendor';
      } else if (name.includes('common')) {
        category = 'common';
      } else if (name.includes('runtime')) {
        category = 'runtime';
      }

      // Add to category
      if (!previousAssetSizes[category]) {
        previousAssetSizes[category] = 0;
      }
      previousAssetSizes[category] += size;

      // Add to total
      previousTotalSize += size;
    });
  } catch (error) {
    console.warn(chalk.yellow('Warning: Failed to load previous stats file.'));
  }
}

// Format size
function formatSize(size) {
  if (size < 1024) {
    return `${size} B`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)} KB`;
  } else {
    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  }
}

// Calculate size difference
function getSizeDiff(current, previous) {
  if (!previous) return '';

  const diff = current - previous;
  const percentage = ((diff / previous) * 100).toFixed(2);

  if (diff === 0) {
    return chalk.gray(' (no change)');
  } else if (diff > 0) {
    return chalk.red(` (+${formatSize(diff)}, +${percentage}%)`);
  } else {
    return chalk.green(` (${formatSize(diff)}, ${percentage}%)`);
  }
}

// Check if size exceeds threshold
function checkThreshold(category, size) {
  const threshold = CONFIG.thresholds[category];
  if (!threshold) return true;

  return size <= threshold;
}

// Add preload validation to build checks
function validateBuild() {
  const preloadResults = testPreloads();

  if (preloadResults.resourceHints.preload.invalid > 0) {
    console.error('Invalid preload directives found:',
      preloadResults.resourceHints.preload.details
        .filter(r => !r.valid)
        .map(r => r.details)
        .join('\n')
    );
    process.exit(1);
  }
}

// Print results
console.log(chalk.bold('\n📦 Bundle Size Analysis\n'));

// Print category sizes
Object.keys(assetSizes).forEach(category => {
  const size = assetSizes[category];
  const previous = previousAssetSizes[category];
  const diff = getSizeDiff(size, previous);

  const exceedsThreshold = !checkThreshold(category, size);
  const sizeText = formatSize(size);

  if (exceedsThreshold) {
    console.log(`${chalk.red('✘')} ${chalk.bold(category)}: ${chalk.red(sizeText)}${diff} - exceeds threshold of ${formatSize(CONFIG.thresholds[category])}`);
  } else {
    console.log(`${chalk.green('✓')} ${chalk.bold(category)}: ${sizeText}${diff}`);
  }
});

// Print total size
const exceedsTotalThreshold = !checkThreshold('total', totalSize);
const totalSizeText = formatSize(totalSize);
const totalDiff = getSizeDiff(totalSize, previousTotalSize);

console.log(chalk.bold('\nTotal Size:'));
if (exceedsTotalThreshold) {
  console.log(`${chalk.red('✘')} ${chalk.red(totalSizeText)}${totalDiff} - exceeds threshold of ${formatSize(CONFIG.thresholds.total)}`);
} else {
  console.log(`${chalk.green('✓')} ${totalSizeText}${totalDiff}`);
}

// Save current stats as previous for future comparison
if (CONFIG.savePreviousStats) {
  fs.writeFileSync(CONFIG.previousStatsFile, fs.readFileSync(CONFIG.statsFile));
  console.log(chalk.gray('\nSaved current stats for future comparison.'));
}

// Exit with error if any threshold is exceeded
if (exceedsTotalThreshold || Object.keys(assetSizes).some(category => !checkThreshold(category, assetSizes[category]))) {
  console.log(chalk.red('\n❌ Bundle size check failed. Some bundles exceed the size threshold.'));
  process.exit(1);
} else {
  console.log(chalk.green('\n✅ Bundle size check passed!'));
  process.exit(0);
}

// Add preload validation
if (!validatePreloads()) {
  console.error(chalk.red('\n❌ Preload validation failed'));
  process.exit(1);
}

console.log(chalk.green('\n✅ All checks passed!'));
process.exit(0);



