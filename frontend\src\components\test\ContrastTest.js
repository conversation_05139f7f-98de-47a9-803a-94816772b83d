import React from 'react';
import { <PERSON>, Typography, Button, Space, Divider, Tag, Alert, Badge, Progress } from 'antd';
import { useEnhancedTheme } from '../../contexts/EnhancedThemeContext';
import DarkModeToggle from '../ui/DarkModeToggle';
import styled from 'styled-components';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;

const TestContainer = styled.div`
  padding: var(--spacing-lg);
  background-color: var(--color-background);
  min-height: 100vh;
  transition: all 0.3s ease;
`;

const TestCard = styled(Card)`
  margin-bottom: var(--spacing-lg);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;

  .ant-card-head {
    background-color: var(--color-background-secondary);
    border-bottom: 1px solid var(--color-border-light);
  }

  .ant-card-head-title {
    color: var(--color-text);
  }
`;

const ContrastGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin: var(--spacing-md) 0;
`;

const ContrastBox = styled.div`
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--color-border);
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
`;

const StatusIndicator = styled.div`
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  font-weight: 500;
  margin: var(--spacing-xs);
`;

const ContrastTest = () => {
  const { isDarkMode, colors, themeMode } = useEnhancedTheme();

  const statusVariants = [
    { type: 'success', color: '#52c41a', bg: 'rgba(82, 196, 26, 0.1)', text: 'Success Status' },
    { type: 'warning', color: '#faad14', bg: 'rgba(250, 173, 20, 0.1)', text: 'Warning Status' },
    { type: 'error', color: '#ff4d4f', bg: 'rgba(255, 77, 79, 0.1)', text: 'Error Status' },
    { type: 'info', color: '#1890ff', bg: 'rgba(24, 144, 255, 0.1)', text: 'Info Status' },
  ];

  return (
    <TestContainer>
      <TestCard title="Text Contrast & Visibility Test">
        <div style={{ marginBottom: '24px', textAlign: 'center' }}>
          <Title level={3}>Current Theme: {themeMode} mode</Title>
          <DarkModeToggle />
        </div>

        <Divider>Typography Hierarchy</Divider>
        
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={1} style={{ color: 'var(--color-text)' }}>Heading Level 1</Title>
            <Title level={2} style={{ color: 'var(--color-text)' }}>Heading Level 2</Title>
            <Title level={3} style={{ color: 'var(--color-text)' }}>Heading Level 3</Title>
            <Title level={4} style={{ color: 'var(--color-text)' }}>Heading Level 4</Title>
            <Title level={5} style={{ color: 'var(--color-text)' }}>Heading Level 5</Title>
          </div>

          <div>
            <Paragraph style={{ color: 'var(--color-text)' }}>
              This is a regular paragraph with normal text color. It should be easily readable 
              against the current background in both light and dark modes.
            </Paragraph>
            <Text type="secondary">This is secondary text that should have good contrast.</Text>
            <br />
            <Text type="success">Success text should be visible and accessible.</Text>
            <br />
            <Text type="warning">Warning text should stand out appropriately.</Text>
            <br />
            <Text type="danger">Error text should be clearly visible.</Text>
            <br />
            <Text disabled>Disabled text should be distinguishable but subdued.</Text>
          </div>
        </Space>

        <Divider>Status Indicators</Divider>
        
        <div>
          {statusVariants.map((status, index) => (
            <StatusIndicator
              key={index}
              style={{
                color: status.color,
                backgroundColor: status.bg,
                border: `1px solid ${status.color}`,
              }}
            >
              {status.type === 'success' && <CheckCircleOutlined />}
              {status.type === 'warning' && <ExclamationCircleOutlined />}
              {status.type === 'error' && <CloseCircleOutlined />}
              {status.type === 'info' && <InfoCircleOutlined />}
              {status.text}
            </StatusIndicator>
          ))}
        </div>

        <Divider>Interactive Elements</Divider>
        
        <Space wrap>
          <Button type="primary">Primary Button</Button>
          <Button type="default">Default Button</Button>
          <Button type="dashed">Dashed Button</Button>
          <Button type="text">Text Button</Button>
          <Button type="link">Link Button</Button>
          <Button danger>Danger Button</Button>
        </Space>

        <Divider>Tags and Badges</Divider>
        
        <Space wrap>
          <Tag color="blue">Blue Tag</Tag>
          <Tag color="green">Green Tag</Tag>
          <Tag color="orange">Orange Tag</Tag>
          <Tag color="red">Red Tag</Tag>
          <Tag color="purple">Purple Tag</Tag>
          <Badge count={5} style={{ backgroundColor: '#52c41a' }}>
            <div style={{ 
              width: 40, 
              height: 40, 
              backgroundColor: 'var(--color-background-secondary)',
              border: '1px solid var(--color-border)',
              borderRadius: '4px'
            }} />
          </Badge>
        </Space>

        <Divider>Alerts</Divider>
        
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert message="Success Alert" type="success" showIcon />
          <Alert message="Info Alert" type="info" showIcon />
          <Alert message="Warning Alert" type="warning" showIcon />
          <Alert message="Error Alert" type="error" showIcon />
        </Space>

        <Divider>Progress Indicators</Divider>
        
        <Space direction="vertical" style={{ width: '100%' }}>
          <Progress percent={30} status="active" />
          <Progress percent={50} status="normal" />
          <Progress percent={70} status="exception" />
          <Progress percent={100} />
        </Space>

        <Divider>Background Variations</Divider>
        
        <ContrastGrid>
          <ContrastBox style={{ backgroundColor: 'var(--color-surface)' }}>
            <Text style={{ color: 'var(--color-text)' }}>Surface Background</Text>
          </ContrastBox>
          <ContrastBox style={{ backgroundColor: 'var(--color-background-secondary)' }}>
            <Text style={{ color: 'var(--color-text)' }}>Secondary Background</Text>
          </ContrastBox>
          <ContrastBox style={{ backgroundColor: 'var(--color-background-tertiary)' }}>
            <Text style={{ color: 'var(--color-text)' }}>Tertiary Background</Text>
          </ContrastBox>
          <ContrastBox style={{ backgroundColor: 'var(--color-primary)', color: 'white' }}>
            <Text style={{ color: 'white' }}>Primary Background</Text>
          </ContrastBox>
        </ContrastGrid>

        <Divider>Theme Information</Divider>
        
        <div style={{ 
          backgroundColor: 'var(--color-background-secondary)',
          padding: '16px',
          borderRadius: '8px',
          border: '1px solid var(--color-border-light)'
        }}>
          <Text style={{ color: 'var(--color-text)' }}>
            <strong>Current Theme:</strong> {isDarkMode ? 'Dark' : 'Light'} Mode<br />
            <strong>Theme Mode Setting:</strong> {themeMode}<br />
            <strong>Primary Color:</strong> {colors.primary}<br />
            <strong>Background Color:</strong> {colors.background}<br />
            <strong>Text Color:</strong> {colors.text}
          </Text>
        </div>
      </TestCard>
    </TestContainer>
  );
};

export default ContrastTest;
