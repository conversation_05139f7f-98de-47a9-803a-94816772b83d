"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[8832],{18832:(e,t,n)=>{n.r(t),n.d(t,{default:()=>f});var l=n(64467),a=n(5544),r=n(96540),o=n(71468),c=n(1807),i=n(35346),s=n(81616);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){(0,l.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}n(17053);var p=c.o5.Title,d=c.o5.Text,y=c.l6.Option,E=c.tU.TabPane;const f=function(){var e=(0,o.wA)(),t=(0,o.d4)((function(e){var t;return(null===(t=e.app)||void 0===t?void 0:t.components)||[]})),n=(0,o.d4)((function(e){var t;return(null===(t=e.app)||void 0===t?void 0:t.layouts)||[]})),l=(0,o.d4)((function(e){var t;return null===(t=e.websocket)||void 0===t?void 0:t.connected})),u=(0,r.useState)(""),f=(0,a.A)(u,2),v=f[0],g=f[1],b=(0,r.useState)("Button"),h=(0,a.A)(b,2),w=h[0],S=h[1],C=(0,r.useState)({}),A=(0,a.A)(C,2),k=A[0],x=A[1],P=(0,r.useState)("Grid"),O=(0,a.A)(P,2),I=O[0],j=O[1],B=(0,r.useState)("builder"),L=(0,a.A)(B,2),T=L[0],$=L[1],D=(0,r.useState)(!1),U=(0,a.A)(D,2),z=U[0],N=U[1],_=(0,r.useState)(null),J=(0,a.A)(_,2),Z=(J[0],J[1]),F=(0,r.useState)(!1),M=(0,a.A)(F,2),Y=(M[0],M[1]),R=(0,r.useState)(!1),W=(0,a.A)(R,2),G=(W[0],W[1]),V=(0,r.useState)("My App"),H=(0,a.A)(V,2),q=H[0],K=(H[1],(0,r.useState)("Built with App Builder MVP")),X=(0,a.A)(K,2),Q=X[0],ee=(X[1],[{value:"Button",label:"Button",icon:"🔘",defaultProps:{text:"Click me",type:"primary",size:"medium"}},{value:"Text",label:"Text",icon:"📝",defaultProps:{content:"Sample text",size:"medium",color:"#000000"}},{value:"Input",label:"Input Field",icon:"📝",defaultProps:{placeholder:"Enter text...",type:"text",required:!1}},{value:"Image",label:"Image",icon:"🖼️",defaultProps:{src:"https://via.placeholder.com/150",alt:"Image",width:150}},{value:"Card",label:"Card",icon:"🃏",defaultProps:{title:"Card Title",content:"Card content goes here"}},{value:"List",label:"List",icon:"📋",defaultProps:{items:["Item 1","Item 2","Item 3"],type:"unordered"}}]),te=[{value:"Grid",label:"Grid Layout",icon:"⚏",defaultProps:{columns:3,gap:"16px",responsive:!0}},{value:"Flex",label:"Flex Layout",icon:"↔️",defaultProps:{direction:"row",justify:"center",align:"center"}},{value:"Stack",label:"Stack Layout",icon:"📚",defaultProps:{spacing:"16px",align:"center",direction:"vertical"}}];(0,r.useEffect)((function(){var e=ee.find((function(e){return e.value===w}));e&&x(e.defaultProps)}),[w]),(0,r.useEffect)((function(){l&&c.iU.success("Real-time connection established")}),[l]);var ne=function(){if(v.trim()){var t=ee.find((function(e){return e.value===w})),n={id:"".concat(w.toLowerCase(),"-").concat(Date.now()),type:w,name:v,props:m(m(m({},t.defaultProps),k),{},{name:v}),created:(new Date).toISOString()};e(s.X8(n)),g(""),c.iU.success("Added ".concat(w," component: ").concat(v))}else c.iU.error("Please enter a component name")};return r.createElement("div",{className:"app-builder-mvp"},r.createElement(c.Zp,{title:r.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},r.createElement("div",null,r.createElement(p,{level:3,style:{margin:0}},q," - App Builder MVP"),r.createElement(d,{type:"secondary"},Q)),r.createElement("div",{className:"connection-status"},l?r.createElement(c.vw,{color:"green",icon:"●"},"Real-time connected"):r.createElement(c.vw,{color:"orange",icon:"●"},"HTTP fallback"))),bordered:!1,extra:r.createElement(c.$x,null,r.createElement(c.m_,{title:"App Settings"},r.createElement(c.$n,{icon:r.createElement(i.JO7,null),onClick:function(){return G(!0)}})),r.createElement(c.m_,{title:z?"Exit Preview":"Preview App"},r.createElement(c.$n,{icon:r.createElement(i.Om2,null),type:z?"primary":"default",onClick:function(){N(!z),c.iU.info(z?"Exited preview mode":"Entered preview mode")}})),r.createElement(c.m_,{title:"Save App"},r.createElement(c.$n,{icon:r.createElement(i.ylI,null),type:"primary",onClick:function(){var l={name:q,description:Q,components:t,layouts:n,styles:{},data:{},metadata:{version:"1.0.0",created:(new Date).toISOString(),componentCount:t.length,layoutCount:n.length}};e(s.ZL(l)),c.iU.success("App data saved successfully")}})),r.createElement(c.m_,{title:"Export App"},r.createElement(c.$n,{icon:r.createElement(i.jsW,null),onClick:function(){var e={name:q,description:Q,components:t,layouts:n,exportedAt:(new Date).toISOString()},l=JSON.stringify(e,null,2),a="data:application/json;charset=utf-8,"+encodeURIComponent(l),r="".concat(q.replace(/\s+/g,"-").toLowerCase(),"-app.json"),o=document.createElement("a");o.setAttribute("href",a),o.setAttribute("download",r),o.click(),c.iU.success("App exported successfully")}})))},r.createElement(c.tU,{activeKey:T,onChange:$},r.createElement(E,{tab:r.createElement("span",null,r.createElement(i.bW0,null),"Builder"),key:"builder"},r.createElement(c.Zp,{title:"Add Components",size:"small",style:{marginBottom:16}},r.createElement(c.fI,{gutter:[16,16]},r.createElement(c.fv,{span:8},r.createElement(c.pd,{placeholder:"Component Name",value:v,onChange:function(e){return g(e.target.value)},onPressEnter:ne})),r.createElement(c.fv,{span:8},r.createElement(c.l6,{value:w,onChange:S,style:{width:"100%"},placeholder:"Select component type"},ee.map((function(e){return r.createElement(y,{key:e.value,value:e.value},e.icon," ",e.label)})))),r.createElement(c.fv,{span:8},r.createElement(c.$n,{type:"primary",icon:r.createElement(i.bW0,null),onClick:ne,block:!0},"Add Component")))),r.createElement(c.Zp,{title:"Add Layouts",size:"small",style:{marginBottom:16}},r.createElement(c.fI,{gutter:[16,16]},r.createElement(c.fv,{span:16},r.createElement(c.l6,{value:I,onChange:j,style:{width:"100%"},placeholder:"Select layout type"},te.map((function(e){return r.createElement(y,{key:e.value,value:e.value},e.icon," ",e.label)})))),r.createElement(c.fv,{span:8},r.createElement(c.$n,{type:"primary",icon:r.createElement(i.bW0,null),onClick:function(){var t=te.find((function(e){return e.value===I})),n={id:"".concat(I.toLowerCase(),"-").concat(Date.now()),type:I,components:[],props:t.defaultProps,created:(new Date).toISOString()};e(s.S7(n.type,n.components,n.props)),c.iU.success("Added ".concat(I," layout"))},block:!0},"Add Layout"))))),r.createElement(E,{tab:r.createElement("span",null,r.createElement(i.C$o,null),"Components"),key:"components"},r.createElement(c.Zp,{title:"Components (".concat(t.length,")"),size:"small"},0===t.length?r.createElement(c.Fc,{message:"No components added yet",description:"Start building your app by adding components in the Builder tab.",type:"info",showIcon:!0}):r.createElement(c.B8,{dataSource:t,renderItem:function(t,n){var l;return r.createElement(c.B8.Item,{key:t.id||"component-".concat(n),actions:[r.createElement(c.m_,{title:"Edit"},r.createElement(c.$n,{icon:r.createElement(i.xjh,null),size:"small",onClick:function(){return function(e){Z(e),Y(!0)}(t)}})),r.createElement(c.iS,{title:"Delete this component?",onConfirm:function(){return n=t.id,e(s.$5(n)),void c.iU.success("Component deleted");var n},okText:"Yes",cancelText:"No"},r.createElement(c.m_,{title:"Delete"},r.createElement(c.$n,{icon:r.createElement(i.SUY,null),size:"small",danger:!0})))]},r.createElement(c.B8.Item.Meta,{title:r.createElement(c.$x,null,r.createElement(c.vw,{color:"blue"},t.type),r.createElement(d,{strong:!0},t.name||(null===(l=t.props)||void 0===l?void 0:l.name))),description:r.createElement(d,{type:"secondary"},JSON.stringify(t.props,null,2).substring(0,100),JSON.stringify(t.props).length>100?"...":"")}))}}))),r.createElement(E,{tab:r.createElement("span",null,r.createElement(i.JO7,null),"Layouts"),key:"layouts"},r.createElement(c.Zp,{title:"Layouts (".concat(n.length,")"),size:"small"},0===n.length?r.createElement(c.Fc,{message:"No layouts added yet",description:"Add layouts to organize your components in the Builder tab.",type:"info",showIcon:!0}):r.createElement(c.B8,{dataSource:n,renderItem:function(e,t){return r.createElement(c.B8.Item,{key:e.id||"layout-".concat(t),actions:[r.createElement(c.m_,{title:"Edit"},r.createElement(c.$n,{icon:r.createElement(i.xjh,null),size:"small"})),r.createElement(c.iS,{title:"Delete this layout?",onConfirm:function(){c.iU.success("Layout deleted")},okText:"Yes",cancelText:"No"},r.createElement(c.m_,{title:"Delete"},r.createElement(c.$n,{icon:r.createElement(i.SUY,null),size:"small",danger:!0})))]},r.createElement(c.B8.Item.Meta,{title:r.createElement(c.$x,null,r.createElement(c.vw,{color:"green"},e.type),r.createElement(d,{strong:!0},"Layout ",t+1)),description:r.createElement(d,{type:"secondary"},JSON.stringify(e.styles||e.props,null,2).substring(0,100),JSON.stringify(e.styles||e.props).length>100?"...":"")}))}}))),r.createElement(E,{tab:r.createElement("span",null,r.createElement(i.VgC,null),"Preview"),key:"preview"},r.createElement(c.Zp,{title:"App Preview",size:"small"},r.createElement(c.Fc,{message:"Preview Mode",description:"This is a simplified preview of your app. In a full implementation, this would render your actual components and layouts.",type:"info",showIcon:!0,style:{marginBottom:16}}),r.createElement("div",{style:{border:"2px dashed #d9d9d9",borderRadius:"6px",padding:"24px",textAlign:"center",minHeight:"300px",backgroundColor:"#fafafa"}},r.createElement(p,{level:4},"Your App Preview"),r.createElement(d,{type:"secondary"},"Components: ",t.length," | Layouts: ",n.length),t.length>0&&r.createElement("div",{style:{marginTop:16}},r.createElement(d,{strong:!0},"Components in your app:"),r.createElement("div",{style:{marginTop:8}},t.map((function(e,t){var n;return r.createElement(c.vw,{key:t,style:{margin:4}},e.type,": ",e.name||(null===(n=e.props)||void 0===n?void 0:n.name))})))),n.length>0&&r.createElement("div",{style:{marginTop:16}},r.createElement(d,{strong:!0},"Layouts in your app:"),r.createElement("div",{style:{marginTop:8}},n.map((function(e,t){return r.createElement(c.vw,{key:t,color:"green",style:{margin:4}},e.type," Layout")}))))))))))}}}]);