"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[368],{368:(e,t,n)=>{n.r(t),n.d(t,{default:()=>d});var a,u=n(7528),r=n(6540),i=n(4378),l=n(9249),c=n(4976),o=n(2389),h=n(1250).Ay.div(a||(a=(0,u.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: calc(100vh - 200px);\n  padding: 24px;\n"])));const d=function(){var e=(0,o.Bd)().t;return r.createElement(h,null,r.createElement(i.Ay,{status:"403",title:e("auth.unauthorized.title"),subTitle:e("auth.unauthorized.subtitle"),extra:[r.createElement(l.<PERSON>y,{type:"primary",key:"home"},r.createElement(c.N_,{to:"/"},e("auth.unauthorized.backHome"))),r.createElement(l.Ay,{key:"login"},r.createElement(c.N_,{to:"/login"},e("auth.unauthorized.login")))]}))}}}]);