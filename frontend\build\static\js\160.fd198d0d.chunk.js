"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[160],{1160:(e,r,t)=>{t.r(r),t.d(r,{default:()=>M});var a,n,l,o,c,s=t(7528),d=t(6540),i=t(3016),m=t(677),u=t(6552),g=t(2702),E=t(9249),y=t(6914),p=t(2120),b=t(7197),v=t(6754),A=t(2569),h=t(7683),x=t(1250),k=t(1295),f=t(2629),w=t(9552),T=t(9248),C=i.A.Title,B=i.A.Paragraph,I=i.A.Text,S=x.Ay.div(a||(a=(0,s.A)(["\n  padding: var(--spacing-lg);\n  background-color: var(--color-background);\n  min-height: 100vh;\n  transition: all 0.3s ease;\n"]))),L=(0,x.Ay)(m.A)(n||(n=(0,s.A)(["\n  margin-bottom: var(--spacing-lg);\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  .ant-card-head {\n    background-color: var(--color-background-secondary);\n    border-bottom: 1px solid var(--color-border-light);\n  }\n\n  .ant-card-head-title {\n    color: var(--color-text);\n  }\n"]))),D=x.Ay.div(l||(l=(0,s.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: var(--spacing-md);\n  margin: var(--spacing-md) 0;\n"]))),H=x.Ay.div(o||(o=(0,s.A)(["\n  padding: var(--spacing-md);\n  border-radius: var(--border-radius-md);\n  border: 1px solid var(--color-border);\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-md);\n  }\n"]))),P=x.Ay.div(c||(c=(0,s.A)(["\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--border-radius-sm);\n  font-size: 12px;\n  font-weight: 500;\n  margin: var(--spacing-xs);\n"])));const M=function(){var e=(0,A.ZV)(),r=e.isDarkMode,t=e.colors,a=e.themeMode;return d.createElement(S,null,d.createElement(L,{title:"Text Contrast & Visibility Test"},d.createElement("div",{style:{marginBottom:"24px",textAlign:"center"}},d.createElement(C,{level:3},"Current Theme: ",a," mode"),d.createElement(h.A,null)),d.createElement(u.A,null,"Typography Hierarchy"),d.createElement(g.A,{direction:"vertical",size:"large",style:{width:"100%"}},d.createElement("div",null,d.createElement(C,{level:1,style:{color:"var(--color-text)"}},"Heading Level 1"),d.createElement(C,{level:2,style:{color:"var(--color-text)"}},"Heading Level 2"),d.createElement(C,{level:3,style:{color:"var(--color-text)"}},"Heading Level 3"),d.createElement(C,{level:4,style:{color:"var(--color-text)"}},"Heading Level 4"),d.createElement(C,{level:5,style:{color:"var(--color-text)"}},"Heading Level 5")),d.createElement("div",null,d.createElement(B,{style:{color:"var(--color-text)"}},"This is a regular paragraph with normal text color. It should be easily readable against the current background in both light and dark modes."),d.createElement(I,{type:"secondary"},"This is secondary text that should have good contrast."),d.createElement("br",null),d.createElement(I,{type:"success"},"Success text should be visible and accessible."),d.createElement("br",null),d.createElement(I,{type:"warning"},"Warning text should stand out appropriately."),d.createElement("br",null),d.createElement(I,{type:"danger"},"Error text should be clearly visible."),d.createElement("br",null),d.createElement(I,{disabled:!0},"Disabled text should be distinguishable but subdued."))),d.createElement(u.A,null,"Status Indicators"),d.createElement("div",null,[{type:"success",color:"#52c41a",bg:"rgba(82, 196, 26, 0.1)",text:"Success Status"},{type:"warning",color:"#faad14",bg:"rgba(250, 173, 20, 0.1)",text:"Warning Status"},{type:"error",color:"#ff4d4f",bg:"rgba(255, 77, 79, 0.1)",text:"Error Status"},{type:"info",color:"#1890ff",bg:"rgba(24, 144, 255, 0.1)",text:"Info Status"}].map((function(e,r){return d.createElement(P,{key:r,style:{color:e.color,backgroundColor:e.bg,border:"1px solid ".concat(e.color)}},"success"===e.type&&d.createElement(k.A,null),"warning"===e.type&&d.createElement(f.A,null),"error"===e.type&&d.createElement(w.A,null),"info"===e.type&&d.createElement(T.A,null),e.text)}))),d.createElement(u.A,null,"Interactive Elements"),d.createElement(g.A,{wrap:!0},d.createElement(E.Ay,{type:"primary"},"Primary Button"),d.createElement(E.Ay,{type:"default"},"Default Button"),d.createElement(E.Ay,{type:"dashed"},"Dashed Button"),d.createElement(E.Ay,{type:"text"},"Text Button"),d.createElement(E.Ay,{type:"link"},"Link Button"),d.createElement(E.Ay,{danger:!0},"Danger Button")),d.createElement(u.A,null,"Tags and Badges"),d.createElement(g.A,{wrap:!0},d.createElement(y.A,{color:"blue"},"Blue Tag"),d.createElement(y.A,{color:"green"},"Green Tag"),d.createElement(y.A,{color:"orange"},"Orange Tag"),d.createElement(y.A,{color:"red"},"Red Tag"),d.createElement(y.A,{color:"purple"},"Purple Tag"),d.createElement(p.A,{count:5,style:{backgroundColor:"#52c41a"}},d.createElement("div",{style:{width:40,height:40,backgroundColor:"var(--color-background-secondary)",border:"1px solid var(--color-border)",borderRadius:"4px"}}))),d.createElement(u.A,null,"Alerts"),d.createElement(g.A,{direction:"vertical",style:{width:"100%"}},d.createElement(b.A,{message:"Success Alert",type:"success",showIcon:!0}),d.createElement(b.A,{message:"Info Alert",type:"info",showIcon:!0}),d.createElement(b.A,{message:"Warning Alert",type:"warning",showIcon:!0}),d.createElement(b.A,{message:"Error Alert",type:"error",showIcon:!0})),d.createElement(u.A,null,"Progress Indicators"),d.createElement(g.A,{direction:"vertical",style:{width:"100%"}},d.createElement(v.A,{percent:30,status:"active"}),d.createElement(v.A,{percent:50,status:"normal"}),d.createElement(v.A,{percent:70,status:"exception"}),d.createElement(v.A,{percent:100})),d.createElement(u.A,null,"Background Variations"),d.createElement(D,null,d.createElement(H,{style:{backgroundColor:"var(--color-surface)"}},d.createElement(I,{style:{color:"var(--color-text)"}},"Surface Background")),d.createElement(H,{style:{backgroundColor:"var(--color-background-secondary)"}},d.createElement(I,{style:{color:"var(--color-text)"}},"Secondary Background")),d.createElement(H,{style:{backgroundColor:"var(--color-background-tertiary)"}},d.createElement(I,{style:{color:"var(--color-text)"}},"Tertiary Background")),d.createElement(H,{style:{backgroundColor:"var(--color-primary)",color:"white"}},d.createElement(I,{style:{color:"white"}},"Primary Background"))),d.createElement(u.A,null,"Theme Information"),d.createElement("div",{style:{backgroundColor:"var(--color-background-secondary)",padding:"16px",borderRadius:"8px",border:"1px solid var(--color-border-light)"}},d.createElement(I,{style:{color:"var(--color-text)"}},d.createElement("strong",null,"Current Theme:")," ",r?"Dark":"Light"," Mode",d.createElement("br",null),d.createElement("strong",null,"Theme Mode Setting:")," ",a,d.createElement("br",null),d.createElement("strong",null,"Primary Color:")," ",t.primary,d.createElement("br",null),d.createElement("strong",null,"Background Color:")," ",t.background,d.createElement("br",null),d.createElement("strong",null,"Text Color:")," ",t.text))))}}}]);