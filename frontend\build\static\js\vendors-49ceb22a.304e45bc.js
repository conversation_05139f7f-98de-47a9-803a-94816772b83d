/*! For license information please see vendors-49ceb22a.304e45bc.js.LICENSE.txt */
"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[1871],{11080:(e,t,r)=>{r.d(t,{Kd:()=>M,N_:()=>K,C5:()=>U,qh:()=>j,BV:()=>D,zy:()=>g,Zp:()=>y,g:()=>b});var n=r(96540),o=r.t(n,2),a=r(40961),i=r.t(a,2),s=r(45588);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},u.apply(this,arguments)}const l=n.createContext(null),c=n.createContext(null),p=n.createContext(null),d=n.createContext(null),f=n.createContext({outlet:null,matches:[],isDataRoute:!1}),h=n.createContext(null);function v(){return null!=n.useContext(d)}function g(){return v()||(0,s.Oi)(!1),n.useContext(d).location}function m(e){n.useContext(p).static||n.useLayoutEffect(e)}function y(){let{isDataRoute:e}=n.useContext(f);return e?function(){let{router:e}=k(N.UseNavigateStable),t=P(R.UseNavigateStable),r=n.useRef(!1);return m((()=>{r.current=!0})),n.useCallback((function(n,o){void 0===o&&(o={}),r.current&&("number"==typeof n?e.navigate(n):e.navigate(n,u({fromRouteId:t},o)))}),[e,t])}():function(){v()||(0,s.Oi)(!1);let e=n.useContext(l),{basename:t,future:r,navigator:o}=n.useContext(p),{matches:a}=n.useContext(f),{pathname:i}=g(),u=JSON.stringify((0,s.yD)(a,r.v7_relativeSplatPath)),c=n.useRef(!1);return m((()=>{c.current=!0})),n.useCallback((function(r,n){if(void 0===n&&(n={}),!c.current)return;if("number"==typeof r)return void o.go(r);let a=(0,s.Gh)(r,JSON.parse(u),i,"path"===n.relative);null==e&&"/"!==t&&(a.pathname="/"===a.pathname?t:(0,s.HS)([t,a.pathname])),(n.replace?o.replace:o.push)(a,n.state,n)}),[t,o,u,i,e])}()}function b(){let{matches:e}=n.useContext(f),t=e[e.length-1];return t?t.params:{}}function w(e,t){let{relative:r}=void 0===t?{}:t,{future:o}=n.useContext(p),{matches:a}=n.useContext(f),{pathname:i}=g(),u=JSON.stringify((0,s.yD)(a,o.v7_relativeSplatPath));return n.useMemo((()=>(0,s.Gh)(e,JSON.parse(u),i,"path"===r)),[e,u,i,r])}function E(e,t,r,o){v()||(0,s.Oi)(!1);let{navigator:a}=n.useContext(p),{matches:i}=n.useContext(f),l=i[i.length-1],c=l?l.params:{},h=(l&&l.pathname,l?l.pathnameBase:"/");l&&l.route;let m,y=g();if(t){var b;let e="string"==typeof t?(0,s.Rr)(t):t;"/"===h||(null==(b=e.pathname)?void 0:b.startsWith(h))||(0,s.Oi)(!1),m=e}else m=y;let w=m.pathname||"/",E=w;if("/"!==h){let e=h.replace(/^\//,"").split("/");E="/"+w.replace(/^\//,"").split("/").slice(e.length).join("/")}let S=(0,s.ue)(e,{pathname:E}),N=function(e,t,r,o){var a;if(void 0===t&&(t=[]),void 0===r&&(r=null),void 0===o&&(o=null),null==e){var i;if(!r)return null;if(r.errors)e=r.matches;else{if(!(null!=(i=o)&&i.v7_partialHydration&&0===t.length&&!r.initialized&&r.matches.length>0))return null;e=r.matches}}let u=e,l=null==(a=r)?void 0:a.errors;if(null!=l){let e=u.findIndex((e=>e.route.id&&void 0!==(null==l?void 0:l[e.route.id])));e>=0||(0,s.Oi)(!1),u=u.slice(0,Math.min(u.length,e+1))}let c=!1,p=-1;if(r&&o&&o.v7_partialHydration)for(let e=0;e<u.length;e++){let t=u[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(p=e),t.route.id){let{loaderData:e,errors:n}=r,o=t.route.loader&&void 0===e[t.route.id]&&(!n||void 0===n[t.route.id]);if(t.route.lazy||o){c=!0,u=p>=0?u.slice(0,p+1):[u[0]];break}}}return u.reduceRight(((e,o,a)=>{let i,s=!1,d=null,f=null;var h;r&&(i=l&&o.route.id?l[o.route.id]:void 0,d=o.route.errorElement||x,c&&(p<0&&0===a?(T[h="route-fallback"]||(T[h]=!0),s=!0,f=null):p===a&&(s=!0,f=o.route.hydrateFallbackElement||null)));let v=t.concat(u.slice(0,a+1)),g=()=>{let t;return t=i?d:s?f:o.route.Component?n.createElement(o.route.Component,null):o.route.element?o.route.element:e,n.createElement(O,{match:o,routeContext:{outlet:e,matches:v,isDataRoute:null!=r},children:t})};return r&&(o.route.ErrorBoundary||o.route.errorElement||0===a)?n.createElement(C,{location:r.location,revalidation:r.revalidation,component:d,error:i,children:g(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):g()}),null)}(S&&S.map((e=>Object.assign({},e,{params:Object.assign({},c,e.params),pathname:(0,s.HS)([h,a.encodeLocation?a.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?h:(0,s.HS)([h,a.encodeLocation?a.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),i,r,o);return t&&N?n.createElement(d.Provider,{value:{location:u({pathname:"/",search:"",hash:"",state:null,key:"default"},m),navigationType:s.rc.Pop}},N):N}function S(){let e=function(){var e;let t=n.useContext(h),r=function(){let e=n.useContext(c);return e||(0,s.Oi)(!1),e}(R.UseRouteError),o=P(R.UseRouteError);return void 0!==t?t:null==(e=r.errors)?void 0:e[o]}(),t=(0,s.pX)(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return n.createElement(n.Fragment,null,n.createElement("h2",null,"Unexpected Application Error!"),n.createElement("h3",{style:{fontStyle:"italic"}},t),r?n.createElement("pre",{style:o},r):null,null)}const x=n.createElement(S,null);class C extends n.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?n.createElement(f.Provider,{value:this.props.routeContext},n.createElement(h.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function O(e){let{routeContext:t,match:r,children:o}=e,a=n.useContext(l);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),n.createElement(f.Provider,{value:t},o)}var N=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(N||{}),R=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(R||{});function k(e){let t=n.useContext(l);return t||(0,s.Oi)(!1),t}function P(e){let t=function(){let e=n.useContext(f);return e||(0,s.Oi)(!1),e}(),r=t.matches[t.matches.length-1];return r.route.id||(0,s.Oi)(!1),r.route.id}const T={},_=(e,t,r)=>{};function U(e){let{to:t,replace:r,state:o,relative:a}=e;v()||(0,s.Oi)(!1);let{future:i,static:u}=n.useContext(p),{matches:l}=n.useContext(f),{pathname:c}=g(),d=y(),h=(0,s.Gh)(t,(0,s.yD)(l,i.v7_relativeSplatPath),c,"path"===a),m=JSON.stringify(h);return n.useEffect((()=>d(JSON.parse(m),{replace:r,state:o,relative:a})),[d,m,a,r,o]),null}function j(e){(0,s.Oi)(!1)}function I(e){let{basename:t="/",children:r=null,location:o,navigationType:a=s.rc.Pop,navigator:i,static:l=!1,future:c}=e;v()&&(0,s.Oi)(!1);let f=t.replace(/^\/*/,"/"),h=n.useMemo((()=>({basename:f,navigator:i,static:l,future:u({v7_relativeSplatPath:!1},c)})),[f,c,i,l]);"string"==typeof o&&(o=(0,s.Rr)(o));let{pathname:g="/",search:m="",hash:y="",state:b=null,key:w="default"}=o,E=n.useMemo((()=>{let e=(0,s.pb)(g,f);return null==e?null:{location:{pathname:e,search:m,hash:y,state:b,key:w},navigationType:a}}),[f,g,m,y,b,w,a]);return null==E?null:n.createElement(p.Provider,{value:h},n.createElement(d.Provider,{children:r,value:E}))}function D(e){let{children:t,location:r}=e;return E(B(t),r)}function B(e,t){void 0===t&&(t=[]);let r=[];return n.Children.forEach(e,((e,o)=>{if(!n.isValidElement(e))return;let a=[...t,o];if(e.type===n.Fragment)return void r.push.apply(r,B(e.props.children,a));e.type!==j&&(0,s.Oi)(!1),e.props.index&&e.props.children&&(0,s.Oi)(!1);let i={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=B(e.props.children,a)),r.push(i)})),r}function F(){return F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},F.apply(this,arguments)}o.startTransition,new Promise((()=>{})),n.Component,new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const L=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(e){}new Map;const A=o.startTransition;function M(e){let{basename:t,children:r,future:o,window:a}=e,i=n.useRef();null==i.current&&(i.current=(0,s.zR)({window:a,v5Compat:!0}));let u=i.current,[l,c]=n.useState({action:u.action,location:u.location}),{v7_startTransition:p}=o||{},d=n.useCallback((e=>{p&&A?A((()=>c(e))):c(e)}),[c,p]);return n.useLayoutEffect((()=>u.listen(d)),[u,d]),n.useEffect((()=>{return void 0===(null==(e=o)?void 0:e.v7_startTransition)&&_("v7_startTransition","React Router will begin wrapping state updates in `React.startTransition` in v7","https://reactrouter.com/v6/upgrading/future#v7_starttransition"),void 0!==(null==e?void 0:e.v7_relativeSplatPath)||t&&void 0!==t.v7_relativeSplatPath||_("v7_relativeSplatPath","Relative route resolution within Splat routes is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath"),void(t&&(void 0===t.v7_fetcherPersist&&_("v7_fetcherPersist","The persistence behavior of fetchers is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist"),void 0===t.v7_normalizeFormMethod&&_("v7_normalizeFormMethod","Casing of `formMethod` fields is being normalized to uppercase in v7","https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod"),void 0===t.v7_partialHydration&&_("v7_partialHydration","`RouterProvider` hydration behavior is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_partialhydration"),void 0===t.v7_skipActionErrorRevalidation&&_("v7_skipActionErrorRevalidation","The revalidation behavior after 4xx/5xx `action` responses is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation")));var e,t}),[o]),n.createElement(I,{basename:t,children:r,location:l.location,navigationType:l.action,navigator:u,future:o})}i.flushSync,o.useId;const z="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,H=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,K=n.forwardRef((function(e,t){let r,{onClick:o,relative:a,reloadDocument:i,replace:u,state:l,target:c,to:d,preventScrollReset:f,viewTransition:h}=e,m=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,L),{basename:b}=n.useContext(p),E=!1;if("string"==typeof d&&H.test(d)&&(r=d,z))try{let e=new URL(window.location.href),t=d.startsWith("//")?new URL(e.protocol+d):new URL(d),r=(0,s.pb)(t.pathname,b);t.origin===e.origin&&null!=r?d=r+t.search+t.hash:E=!0}catch(e){}let S=function(e,t){let{relative:r}=void 0===t?{}:t;v()||(0,s.Oi)(!1);let{basename:o,navigator:a}=n.useContext(p),{hash:i,pathname:u,search:l}=w(e,{relative:r}),c=u;return"/"!==o&&(c="/"===u?o:(0,s.HS)([o,u])),a.createHref({pathname:c,search:l,hash:i})}(d,{relative:a}),x=function(e,t){let{target:r,replace:o,state:a,preventScrollReset:i,relative:u,viewTransition:l}=void 0===t?{}:t,c=y(),p=g(),d=w(e,{relative:u});return n.useCallback((t=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(t,r)){t.preventDefault();let r=void 0!==o?o:(0,s.AO)(p)===(0,s.AO)(d);c(e,{replace:r,state:a,preventScrollReset:i,relative:u,viewTransition:l})}}),[p,c,d,o,a,r,e,i,u,l])}(d,{replace:u,state:l,target:c,preventScrollReset:f,relative:a,viewTransition:h});return n.createElement("a",F({},m,{href:r||S,onClick:E||i?o:function(e){o&&o(e),e.defaultPrevented||x(e)},ref:t,target:c}))}));var V,$;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(V||(V={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}($||($={}))},14644:(e,t,r)=>{function n(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}r.d(t,{HY:()=>l,Qd:()=>s,Tw:()=>p,Zz:()=>c,ve:()=>d,y$:()=>u});var o=(()=>"function"==typeof Symbol&&Symbol.observable||"@@observable")(),a=()=>Math.random().toString(36).substring(7).split("").join("."),i={INIT:`@@redux/INIT${a()}`,REPLACE:`@@redux/REPLACE${a()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${a()}`};function s(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function u(e,t,r){if("function"!=typeof e)throw new Error(n(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(n(1));return r(u)(e,t)}let a=e,l=t,c=new Map,p=c,d=0,f=!1;function h(){p===c&&(p=new Map,c.forEach(((e,t)=>{p.set(t,e)})))}function v(){if(f)throw new Error(n(3));return l}function g(e){if("function"!=typeof e)throw new Error(n(4));if(f)throw new Error(n(5));let t=!0;h();const r=d++;return p.set(r,e),function(){if(t){if(f)throw new Error(n(6));t=!1,h(),p.delete(r),c=null}}}function m(e){if(!s(e))throw new Error(n(7));if(void 0===e.type)throw new Error(n(8));if("string"!=typeof e.type)throw new Error(n(17));if(f)throw new Error(n(9));try{f=!0,l=a(l,e)}finally{f=!1}return(c=p).forEach((e=>{e()})),e}return m({type:i.INIT}),{dispatch:m,subscribe:g,getState:v,replaceReducer:function(e){if("function"!=typeof e)throw new Error(n(10));a=e,m({type:i.REPLACE})},[o]:function(){const e=g;return{subscribe(t){if("object"!=typeof t||null===t)throw new Error(n(11));function r(){const e=t;e.next&&e.next(v())}return r(),{unsubscribe:e(r)}},[o](){return this}}}}}function l(e){const t=Object.keys(e),r={};for(let n=0;n<t.length;n++){const o=t[n];"function"==typeof e[o]&&(r[o]=e[o])}const o=Object.keys(r);let a;try{!function(e){Object.keys(e).forEach((t=>{const r=e[t];if(void 0===r(void 0,{type:i.INIT}))throw new Error(n(12));if(void 0===r(void 0,{type:i.PROBE_UNKNOWN_ACTION()}))throw new Error(n(13))}))}(r)}catch(e){a=e}return function(e={},t){if(a)throw a;let i=!1;const s={};for(let a=0;a<o.length;a++){const u=o[a],l=r[u],c=e[u],p=l(c,t);if(void 0===p)throw t&&t.type,new Error(n(14));s[u]=p,i=i||p!==c}return i=i||o.length!==Object.keys(e).length,i?s:e}}function c(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce(((e,t)=>(...r)=>e(t(...r))))}function p(...e){return t=>(r,o)=>{const a=t(r,o);let i=()=>{throw new Error(n(15))};const s={getState:a.getState,dispatch:(e,...t)=>i(e,...t)},u=e.map((e=>e(s)));return i=c(...u)(a.dispatch),{...a,dispatch:i}}}function d(e){return s(e)&&"type"in e&&"string"==typeof e.type}},27346:(e,t,r)=>{function n(e){return({dispatch:t,getState:r})=>n=>o=>"function"==typeof o?o(t,r,e):n(o)}r.d(t,{P:()=>o,Y:()=>a});var o=n(),a=n},63710:(e,t,r)=>{r.d(t,{Bd:()=>m});var n=r(96540);r(23804);const o={},a=(e,t,r,n)=>{l(r)&&o[r]||(l(r)&&(o[r]=new Date),((e,t,r,n)=>{const o=[r,{code:t,...n||{}}];if(e?.services?.logger?.forward)return e.services.logger.forward(o,"warn","react-i18next::",!0);l(o[0])&&(o[0]=`react-i18next:: ${o[0]}`),e?.services?.logger?.warn?e.services.logger.warn(...o):console?.warn&&console.warn(...o)})(e,t,r,n))},i=(e,t)=>()=>{if(e.isInitialized)t();else{const r=()=>{setTimeout((()=>{e.off("initialized",r)}),0),t()};e.on("initialized",r)}},s=(e,t,r)=>{e.loadNamespaces(t,i(e,r))},u=(e,t,r,n)=>{if(l(r)&&(r=[r]),e.options.preload&&e.options.preload.indexOf(t)>-1)return s(e,r,n);r.forEach((t=>{e.options.ns.indexOf(t)<0&&e.options.ns.push(t)})),e.loadLanguages(t,i(e,n))},l=e=>"string"==typeof e,c=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,p={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},d=e=>p[e];let f={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(c,d)};const h=(0,n.createContext)();class v{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach((e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)}))}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const g=(e,t,r,n)=>e.getFixedT(t,r,n),m=(e,t={})=>{const{i18n:r}=t,{i18n:o,defaultNS:i}=(0,n.useContext)(h)||{},c=r||o||undefined;if(c&&!c.reportNamespaces&&(c.reportNamespaces=new v),!c){a(c,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const e=(e,t)=>{return l(t)?t:"object"==typeof(r=t)&&null!==r&&l(t.defaultValue)?t.defaultValue:Array.isArray(e)?e[e.length-1]:e;var r},t=[e,{},!1];return t.t=e,t.i18n={},t.ready=!1,t}c.options.react?.wait&&a(c,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const p={...f,...c.options.react,...t},{useSuspense:d,keyPrefix:m}=p;let y=e||i||c.options?.defaultNS;y=l(y)?[y]:y||["translation"],c.reportNamespaces.addUsedNamespaces?.(y);const b=(c.isInitialized||c.initializedStoreOnce)&&y.every((e=>((e,t,r={})=>t.languages&&t.languages.length?t.hasLoadedNamespace(e,{lng:r.lng,precheck:(t,n)=>{if(r.bindI18n?.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!n(t.isLanguageChangingTo,e))return!1}}):(a(t,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:t.languages}),!0))(e,c,p))),w=((e,t,r,o)=>(0,n.useCallback)(g(e,t,r,o),[e,t,r,o]))(c,t.lng||null,"fallback"===p.nsMode?y:y[0],m),E=()=>w,S=()=>g(c,t.lng||null,"fallback"===p.nsMode?y:y[0],m),[x,C]=(0,n.useState)(E);let O=y.join();t.lng&&(O=`${t.lng}${O}`);const N=((e,t)=>{const r=(0,n.useRef)();return(0,n.useEffect)((()=>{r.current=e}),[e,t]),r.current})(O),R=(0,n.useRef)(!0);(0,n.useEffect)((()=>{const{bindI18n:e,bindI18nStore:r}=p;R.current=!0,b||d||(t.lng?u(c,t.lng,y,(()=>{R.current&&C(S)})):s(c,y,(()=>{R.current&&C(S)}))),b&&N&&N!==O&&R.current&&C(S);const n=()=>{R.current&&C(S)};return e&&c?.on(e,n),r&&c?.store.on(r,n),()=>{R.current=!1,c&&e?.split(" ").forEach((e=>c.off(e,n))),r&&c&&r.split(" ").forEach((e=>c.store.off(e,n)))}}),[c,O]),(0,n.useEffect)((()=>{R.current&&b&&C(E)}),[c,m,b]);const k=[x,c,b];if(k.t=x,k.i18n=c,k.ready=b,b)return k;if(!b&&!d)return k;throw new Promise((e=>{t.lng?u(c,t.lng,y,(()=>e())):s(c,y,(()=>e()))}))}},71468:(e,t,r)=>{r.d(t,{Kq:()=>f,d4:()=>S,wA:()=>b});var n=r(96540),o=r(78418);var a={notify(){},get:()=>[]};var i=(()=>!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement))(),s=(()=>"undefined"!=typeof navigator&&"ReactNative"===navigator.product)(),u=(()=>i||s?n.useLayoutEffect:n.useEffect)();Object.defineProperty,Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var l=Symbol.for("react-redux-context"),c="undefined"!=typeof globalThis?globalThis:{};function p(){if(!n.createContext)return{};const e=c[l]??=new Map;let t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}var d=p(),f=function(e){const{children:t,context:r,serverState:o,store:i}=e,s=n.useMemo((()=>{const e=function(e,t){let r,n=a,o=0,i=!1;function s(){c.onStateChange&&c.onStateChange()}function u(){o++,r||(r=t?t.addNestedSub(s):e.subscribe(s),n=function(){let e=null,t=null;return{clear(){e=null,t=null},notify(){(()=>{let t=e;for(;t;)t.callback(),t=t.next})()},get(){const t=[];let r=e;for(;r;)t.push(r),r=r.next;return t},subscribe(r){let n=!0;const o=t={callback:r,next:null,prev:t};return o.prev?o.prev.next=o:e=o,function(){n&&null!==e&&(n=!1,o.next?o.next.prev=o.prev:t=o.prev,o.prev?o.prev.next=o.next:e=o.next)}}}}())}function l(){o--,r&&0===o&&(r(),r=void 0,n.clear(),n=a)}const c={addNestedSub:function(e){u();const t=n.subscribe(e);let r=!1;return()=>{r||(r=!0,t(),l())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:s,isSubscribed:function(){return i},trySubscribe:function(){i||(i=!0,u())},tryUnsubscribe:function(){i&&(i=!1,l())},getListeners:()=>n};return c}(i);return{store:i,subscription:e,getServerState:o?()=>o:void 0}}),[i,o]),l=n.useMemo((()=>i.getState()),[i]);u((()=>{const{subscription:e}=s;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),l!==i.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}}),[s,l]);const c=r||d;return n.createElement(c.Provider,{value:s},t)};function h(e=d){return function(){return n.useContext(e)}}var v=h();function g(e=d){const t=e===d?v:h(e),r=()=>{const{store:e}=t();return e};return Object.assign(r,{withTypes:()=>r}),r}var m=g();function y(e=d){const t=e===d?m:g(e),r=()=>t().dispatch;return Object.assign(r,{withTypes:()=>r}),r}var b=y(),w=(e,t)=>e===t;function E(e=d){const t=e===d?v:h(e),r=(e,r={})=>{const{equalityFn:a=w}="function"==typeof r?{equalityFn:r}:r,i=t(),{store:s,subscription:u,getServerState:l}=i,c=(n.useRef(!0),n.useCallback({[e.name]:t=>e(t)}[e.name],[e])),p=(0,o.useSyncExternalStoreWithSelector)(u.addNestedSub,s.getState,l||s.getState,c,a);return n.useDebugValue(p),p};return Object.assign(r,{withTypes:()=>r}),r}var S=E()}}]);