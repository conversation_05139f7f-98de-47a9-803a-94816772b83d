"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[366],{3774:(e,n,t)=>{t.r(n),t.d(n,{timeSync:()=>i});var o=t(467),r=t(3029),c=t(2901),a=t(4756),s=t.n(a),i=new(function(){return(0,c.A)((function e(){(0,r.A)(this,e),this.offset=0,this.syncAttempts=5,this.syncing=!1}),[{key:"synchronize",value:(e=(0,o.A)(s().mark((function e(){var n,t,o,r,c,a,i,l,u,d,g;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.syncing){e.next=2;break}return e.abrupt("return");case 2:this.syncing=!0,n=[],t=0;case 5:if(!(t<this.syncAttempts)){e.next=30;break}return o=Date.now(),e.prev=7,e.next=10,fetch("/api/time");case 10:return r=e.sent,c=Date.now(),e.next=14,r.json();case 14:return a=e.sent,i=a.serverTime,l=new Date(i).getTime(),u=(l-o+(l-c))/2,n.push(u),e.next=22,new Promise((function(e){return setTimeout(e,100)}));case 22:e.next=27;break;case 24:e.prev=24,e.t0=e.catch(7),console.error("Time sync failed:",e.t0);case 27:t++,e.next=5;break;case 30:d=[].concat(n).sort((function(e,n){return e-n})),g=d.slice(1,-1),this.offset=g.reduce((function(e,n){return e+n}),0)/g.length,this.syncing=!1,console.log("Time synchronized, offset:",this.offset,"ms");case 35:case"end":return e.stop()}}),e,this,[[7,24]])}))),function(){return e.apply(this,arguments)})},{key:"now",value:function(){return new Date(Date.now()+this.offset)}},{key:"getServerTime",value:function(e){return new Date(e.getTime()+this.offset)}},{key:"getClientTime",value:function(e){return new Date(e.getTime()-this.offset)}}]);var e}())},5366:(e,n,t)=>{t.r(n),t.d(n,{default:()=>De});var o=t(5544),r=t(7528),c=t(6540),a=t(2395),s=t(6191),i=t(6020),l=t(436),u=t(2284),d=t(3016),g=t(4358),m=t(7355),p=t(677),f=t(7197),b=t(2652),v=t(5039),h=t(9249),y=t(2120),A=t(1295),k=t(1005),S=t(9552),E=t(9248),w=t(581),C=t(5850),x=t(7345),O=t(1250),L=t(3029),T=t(2901),W=t(4467),R=t(6390),I=t(467),N=t(4756),D=t.n(N),M=t(7362),j=["NS_ERROR_CORRUPTED_CONTENT","NS_ERROR_NET_INADEQUATE_SECURITY","SECURITY_ERR","NetworkError","network error response","fetch event","resulted in network error","promise was rejected"];function P(e){var n=e instanceof Error?e.message:String(e);return j.some((function(e){return n.includes(e)||n.toLowerCase().includes("serviceworker")||n.toLowerCase().includes("service worker")}))}function B(){return(B=(0,I.A)(D().mark((function e(n){return D().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.error("WebSocket error occurred:",n),!P(n)){e.next=6;break}if(console.log("Detected service worker related WebSocket error"),!window.confirm("A service worker is interfering with the WebSocket connection. Would you like to fix this issue? This will refresh the page.")){e.next=6;break}return e.abrupt("return",(0,M.jY)());case 6:return e.abrupt("return",!1);case 7:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function U(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,o=Array(n);t<n;t++)o[t]=e[t];return o}function _(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);n&&(o=o.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,o)}return t}function z(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?_(Object(t),!0).forEach((function(n){(0,W.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):_(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}function Q(){var e=new Error;return Error.captureStackTrace(e,Q),e.stack}const F=function(){function e(){var n=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if((0,L.A)(this,e),(0,W.A)(this,"logger",{_formatMessage:function(e){for(var t=(new Date).toISOString(),o=arguments.length,r=new Array(o>1?o-1:0),c=1;c<o;c++)r[c-1]=arguments[c];return["[".concat(t,"][WebSocketClient:").concat(n.connectionId,"][").concat(e,"]")].concat(r)},debug:function(){if(n.currentLogLevel<=n.logLevels.DEBUG){for(var e,t,o=arguments.length,r=new Array(o),c=0;c<o;c++)r[c]=arguments[c];(e=console).debug.apply(e,(0,l.A)((t=n.logger)._formatMessage.apply(t,["DEBUG"].concat(r))))}},info:function(){if(n.currentLogLevel<=n.logLevels.INFO){for(var e,t,o=arguments.length,r=new Array(o),c=0;c<o;c++)r[c]=arguments[c];(e=console).info.apply(e,(0,l.A)((t=n.logger)._formatMessage.apply(t,["INFO"].concat(r))))}},warn:function(){if(n.currentLogLevel<=n.logLevels.WARN){for(var e,t,o=arguments.length,r=new Array(o),c=0;c<o;c++)r[c]=arguments[c];(e=console).warn.apply(e,(0,l.A)((t=n.logger)._formatMessage.apply(t,["WARN"].concat(r))))}},error:function(){if(n.currentLogLevel<=n.logLevels.ERROR){for(var e,t,o=arguments.length,r=new Array(o),c=0;c<o;c++)r[c]=arguments[c];(e=console).error.apply(e,(0,l.A)((t=n.logger)._formatMessage.apply(t,["ERROR"].concat(r))))}},group:function(e){n.debug&&console.group("[WebSocketClient:".concat(n.connectionId,"] ").concat(e))},groupEnd:function(){n.debug&&console.groupEnd()}}),(0,W.A)(this,"setConnectionState",(function(e){var t=n.connectionState;n.connectionState=e,n.dispatchEvent("state_change",{previousState:t,currentState:e})})),(0,W.A)(this,"open",(function(e){if(console.log("open method called with url:",e),console.log("this in open method before any operations:",n),e&&(n.url=e),!n.url)throw console.error("WebSocketClient: URL not set in open method"),new Error("WebSocket URL not set");console.log("URL after validation:",n.url),!n.socket||n.socket.readyState!==WebSocket.OPEN&&n.socket.readyState!==WebSocket.CONNECTING||(n.log("Closing existing connection before opening a new one"),n.socket.close()),n.log("Opening connection to ".concat(n.url)),console.log("WebSocketClient: Connecting to ".concat(n.url)),console.log("WebSocketClient: Network status - Online: ".concat(navigator.onLine));try{var t=new URL(n.url);console.log("WebSocketClient: URL parsed successfully - Protocol: ".concat(t.protocol,", Host: ").concat(t.host,", Path: ").concat(t.pathname))}catch(e){console.error("WebSocketClient: Invalid URL - ".concat(n.url),e)}n.setConnectionState(0);try{return console.log("Creating new WebSocket object with URL:",n.url),console.log("this before WebSocket creation:",n),n.socket=new WebSocket(n.url),n.socket.binaryType="arraybuffer",console.log("WebSocket object created:",n.socket),console.log("WebSocket readyState:",n.socket.readyState),console.log("Setting up connection timeout of",n.connectionTimeout,"ms"),n.connectionTimeoutId=setTimeout((function(){console.log("Connection timeout callback triggered"),console.log("this in timeout callback:",n),n.socket&&n.socket.readyState!==WebSocket.OPEN&&(console.warn("WebSocketClient: Connection timeout after ".concat(n.connectionTimeout,"ms")),n.socket.close(4e3,"Connection timeout"),n.dispatchEvent("error",new Error("Connection timeout")))}),n.connectionTimeout),console.log("Event handlers before binding:"),console.log("onOpen:",n.onOpen),console.log("onMessage:",n.onMessage),console.log("onClose:",n.onClose),console.log("onError:",n.onError),n.socket.onopen=n.onOpen,n.socket.onmessage=n.onMessage,n.socket.onclose=n.onClose,n.socket.onerror=n.onError,console.log("Event handlers after binding:"),console.log("socket.onopen:",n.socket.onopen),console.log("socket.onmessage:",n.socket.onmessage),console.log("socket.onclose:",n.socket.onclose),console.log("socket.onerror:",n.socket.onerror),n}catch(e){n.log("Error creating WebSocket:",e);var o=Q();return console.error("WebSocketClient: Error creating connection:",e),console.error("WebSocketClient: Connection error stack trace:",o),e&&"object"===(0,u.A)(e)&&(e.stack=e.stack||o),P(e)?(console.warn("WebSocketClient: Detected service worker related error"),function(e){return B.apply(this,arguments)}(e).then((function(t){t||(n.dispatchEvent("error",e),n.reconnect())}))):(n.dispatchEvent("error",e),n.reconnect()),n}})),(0,W.A)(this,"close",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Normal closure";return n.socket&&(n.log("Closing connection with code ".concat(e,": ").concat(t)),n.log("Stopping heartbeat"),n.stopHeartbeat(),n.setConnectionState(2),n.socket.close(e,t)),n})),(0,W.A)(this,"cleanup",(function(){window.removeEventListener("beforeunload",n.handleBeforeUnload),n.connectionTimeoutId&&(clearTimeout(n.connectionTimeoutId),n.connectionTimeoutId=null),n.reconnectTimeoutId&&(clearTimeout(n.reconnectTimeoutId),n.reconnectTimeoutId=null),n.stopHeartbeat()})),(0,W.A)(this,"startHeartbeat",(function(){n.enableHeartbeat&&1===n.connectionState&&(n.heartbeatTimeoutId&&clearTimeout(n.heartbeatTimeoutId),n.heartbeatTimeoutId=setTimeout((function(){if(1===n.connectionState)try{var e="object"===(0,u.A)(n.heartbeatMessage)?z(z({},n.heartbeatMessage),{},{timestamp:(new Date).toISOString()}):n.heartbeatMessage;n.send(e),n.logger.debug("Heartbeat sent",{timestamp:(new Date).toISOString()}),n.startHeartbeat()}catch(e){n.logger.error("Failed to send heartbeat",{error:e}),n.reconnect()}}),n.heartbeatInterval))})),(0,W.A)(this,"stopHeartbeat",(function(){n.heartbeatTimeoutId&&(clearTimeout(n.heartbeatTimeoutId),n.heartbeatTimeoutId=null)})),(0,W.A)(this,"send",(function(e){n.logger.group("Sending Message");try{if(null==e)return n.logger.error("Cannot send undefined or null data"),!1;var t;if(e instanceof ArrayBuffer||e instanceof Blob)t=e,n.logger.debug("Sending binary data",{type:e instanceof ArrayBuffer?"ArrayBuffer":"Blob",size:e instanceof ArrayBuffer?e.byteLength:e.size});else if("object"===(0,u.A)(e))try{t=JSON.stringify(e)}catch(e){return n.logger.error("Failed to stringify object",{error:e}),!1}else t=e;return 1===n.connectionState?n.socket&&n.socket.readyState===WebSocket.OPEN?(n.socket.send(t),"string"==typeof t?n.logger.debug("Message sent successfully",{dataType:(0,u.A)(e),dataLength:t.length,preview:t.length>100?t.substring(0,97)+"...":t}):n.logger.debug("Binary message sent successfully",{dataType:t instanceof ArrayBuffer?"ArrayBuffer":"Blob",dataSize:t instanceof ArrayBuffer?t.byteLength:t.size}),!0):(n.logger.warn("Socket not open despite connection state",{connectionState:n.connectionState,socketReadyState:n.socket?n.socket.readyState:"no socket",messageQueued:!0}),n.messageQueue.push(t),!1):(n.logger.warn("Cannot send - connection not open",{connectionState:n.connectionState,messageQueued:!0}),n.messageQueue.push(t),!1)}catch(t){return n.logger.error("Failed to send message",{error:t,errorMessage:t.message,errorStack:t.stack,data:"object"===(0,u.A)(e)?"object":e,connectionState:n.connectionState}),!1}finally{n.logger.groupEnd()}})),(0,W.A)(this,"reconnect",(function(){if(4!==n.connectionState&&2!==n.connectionState){if(n.reconnectAttempts>=n.maxReconnectAttempts)return n.log("Max reconnect attempts reached"),void n.dispatchEvent("reconnect_failed");n.reconnectAttempts++,n.setConnectionState(4);var e=Math.min(n.reconnectInterval*Math.pow(n.reconnectDecay,n.reconnectAttempts-1),n.maxReconnectInterval);n.log("Reconnecting in ".concat(e,"ms (attempt ").concat(n.reconnectAttempts,"/").concat(n.maxReconnectAttempts,")")),n.dispatchEvent("reconnect_attempt",{attempt:n.reconnectAttempts,delay:e}),setTimeout((function(){n.log("Attempting reconnect ".concat(n.reconnectAttempts,"/").concat(n.maxReconnectAttempts));try{n.open()}catch(e){n.log("Reconnect error:",e),n.dispatchEvent("reconnect_error",e),n.reconnect()}}),e)}})),(0,W.A)(this,"onOpen",(function(e){if(console.log("onOpen event handler called with event:",e),console.log("this in onOpen handler:",n),console.log("Current socket:",n.socket),console.log("Current connection state:",n.connectionState),n.log("Connection opened"),n.connectionTimeoutId&&(console.log("Clearing connection timeout:",n.connectionTimeoutId),clearTimeout(n.connectionTimeoutId),n.connectionTimeoutId=null),console.log("Setting connection state to OPEN"),n.setConnectionState(1),n.reconnectAttempts=0,n.messageQueue.length>0){n.log("Processing ".concat(n.messageQueue.length," queued messages"));var t=(0,l.A)(n.messageQueue);n.messageQueue=[];var o,r=function(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,n){if(e){if("string"==typeof e)return U(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?U(e,n):void 0}}(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var o=0,r=function(){};return{s:r,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var c,a=!0,s=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){s=!0,c=e},f:function(){try{a||null==t.return||t.return()}finally{if(s)throw c}}}}(t);try{for(r.s();!(o=r.n()).done;){var c=o.value;try{n.socket&&n.socket.readyState===WebSocket.OPEN?(n.socket.send(c),n.log("Queued message sent successfully")):(n.log("Socket not open while processing queue, re-queuing message"),n.messageQueue.push(c))}catch(e){n.log("Error sending queued message:",e),"SyntaxError"!==e.name&&"TypeError"!==e.name&&n.messageQueue.push(c)}}}catch(e){r.e(e)}finally{r.f()}n.log("Queue processing complete. ".concat(n.messageQueue.length," messages re-queued"))}n.enableHeartbeat&&(n.log("Starting heartbeat"),n.startHeartbeat()),n.dispatchEvent("open",e)})),(0,W.A)(this,"onMessage",(function(e){try{if(e.data instanceof Blob||e.data instanceof ArrayBuffer)return n.log("Binary message received"),void n.handleBinaryMessage(e);if(n.log("Message received:","string"==typeof e.data?e.data.substring(0,100)+(e.data.length>100?"...":""):e.data),"string"!=typeof e.data)return n.log("Non-string data received:",(0,u.A)(e.data)),void n.dispatchEvent("message",{originalEvent:e,data:e.data});if(!e.data||""===e.data.trim())return n.log("Empty message received"),void n.dispatchEvent("message",{originalEvent:e,data:""});try{var t=JSON.parse(e.data);n.dispatchEvent("message",{originalEvent:e,data:t})}catch(t){n.log("Error parsing JSON message:",t),n.dispatchEvent("message",{originalEvent:e,data:e.data,parseError:t.message})}}catch(t){n.log("Error in onMessage handler:",t),n.dispatchEvent("error",{message:"Error processing WebSocket message",error:t.message,originalEvent:e})}})),(0,W.A)(this,"handleBinaryMessage",(function(e){var t=new FileReader;if(t.onload=function(){try{var o=t.result;n.log("Binary message converted to text:",o.substring(0,100)+(o.length>100?"...":""));try{var r=JSON.parse(o);n.dispatchEvent("message",{originalEvent:e,data:r,binary:!0})}catch(t){n.dispatchEvent("message",{originalEvent:e,data:o,binary:!0,parseError:t.message})}}catch(t){n.log("Error processing binary message:",t),n.dispatchEvent("error",{message:"Error processing binary WebSocket message",error:t.message,originalEvent:e})}},t.onerror=function(t){n.log("Error reading binary message:",t),n.dispatchEvent("error",{message:"Error reading binary WebSocket message",error:t.message,originalEvent:e})},e.data instanceof Blob)t.readAsText(e.data);else if(e.data instanceof ArrayBuffer){var o=(new TextDecoder).decode(e.data);try{var r=JSON.parse(o);n.dispatchEvent("message",{originalEvent:e,data:r,binary:!0})}catch(t){n.dispatchEvent("message",{originalEvent:e,data:o,binary:!0,parseError:t.message})}}})),(0,W.A)(this,"onClose",(function(e){var t={code:e.code,reason:e.reason||"No reason provided",wasClean:e.wasClean,timestamp:(new Date).toISOString(),url:n.url,networkOnline:navigator.onLine,connectionState:n.connectionState};n.connectionTimeoutId&&(clearTimeout(n.connectionTimeoutId),n.connectionTimeoutId=null),n.log("Connection closed: ".concat(t.code," ").concat(t.reason," (clean: ").concat(t.wasClean,")")),n.setConnectionState(3),n.dispatchEvent("close",t),n.cleanup();var o=1e3===e.code||1001===e.code,r=1006===e.code,c=e.code>=1011&&e.code<=1015;if(r){if(console.warn("WebSocketClient: Abnormal closure (code 1006) detected"),console.log("WebSocketClient: Network status:",navigator.onLine?"Online":"Offline"),!navigator.onLine){console.warn("WebSocketClient: Network appears to be offline, waiting for online status");var a=function(){console.log("WebSocketClient: Network is back online, attempting to reconnect"),window.removeEventListener("online",a),setTimeout((function(){return n.reconnect()}),1e3)};return void window.addEventListener("online",a)}return console.log("WebSocketClient: Network is online, attempting immediate reconnect"),n.reconnectInterval=1e3,void setTimeout((function(){return n.reconnect()}),250)}if(c)return console.warn("WebSocketClient: Server error (code ".concat(e.code,") detected, using exponential backoff")),void n.reconnect();o?console.log("WebSocketClient: Not reconnecting after clean close (".concat(e.code,")")):(console.log("WebSocketClient: Non-normal closure (code ".concat(e.code,"), scheduling reconnect")),n.reconnect())})),(0,W.A)(this,"onError",(function(e){try{console.log("WebSocket error occurred:",e),n.connectionTimeoutId&&(console.log("Clearing connection timeout in onError:",n.connectionTimeoutId),clearTimeout(n.connectionTimeoutId),n.connectionTimeoutId=null);var t=!navigator||"boolean"!=typeof navigator.onLine||navigator.onLine,o={originalEvent:e,message:"WebSocket connection error",timestamp:(new Date).toISOString(),connectionState:n.connectionState,url:n.url,reconnectAttempts:n.reconnectAttempts,maxReconnectAttempts:n.maxReconnectAttempts,browser:{online:t}};console.error("WebSocketClient error details:",o),n.dispatchEvent("error",o),4!==n.connectionState&&n.reconnect()}catch(e){console.error("Error in WebSocket error handler:",e)}})),(0,W.A)(this,"handleBeforeUnload",(function(e){n.socket&&1===n.connectionState&&(n.log("Page unloading, closing WebSocket connection"),n.close(1e3,"Page unloaded"))})),console.log("WebSocketClient constructor called with options:",t),console.log("this in constructor:",this),t.url)try{var o=new URL(t.url);"ws:"!==o.protocol&&"wss:"!==o.protocol&&(console.warn("WebSocketClient: Invalid protocol - ".concat(o.protocol,". URL should use ws:// or wss://")),"http:"===o.protocol?(t.url=t.url.replace("http:","ws:"),console.log("WebSocketClient: Corrected URL to ".concat(t.url))):"https:"===o.protocol&&(t.url=t.url.replace("https:","wss:"),console.log("WebSocketClient: Corrected URL to ".concat(t.url))))}catch(e){console.error("WebSocketClient: Invalid URL - ".concat(t.url),e),t.url=null}this.url=t.url||null,this.reconnectInterval=t.reconnectInterval||1e3,this.maxReconnectInterval=t.maxReconnectInterval||3e4,this.reconnectDecay=t.reconnectDecay||1.5,this.maxReconnectAttempts=t.maxReconnectAttempts||10,this.debug=t.debug||!1,this.automaticOpen=!1!==t.automaticOpen,this.connectionTimeout=t.connectionTimeout||5e3,this.enableHeartbeat=!1!==t.enableHeartbeat,this.heartbeatInterval=t.heartbeatInterval||3e4,this.heartbeatMessage=t.heartbeatMessage||{type:"ping",timestamp:(new Date).toISOString()},this.heartbeatTimeoutId=null,this.connectionId=Math.random().toString(36).substring(2,11),this.logLevels={DEBUG:0,INFO:1,WARN:2,ERROR:3},this.currentLogLevel=this.debug?this.logLevels.DEBUG:this.logLevels.INFO,this.socket=null,this.connectionState=3,this.reconnectAttempts=0,this.messageQueue=[],this.eventListeners={open:[],message:[],close:[],error:[],reconnect:[],reconnect_attempt:[],reconnect_error:[],reconnect_failed:[],state_change:[]},window.addEventListener("beforeunload",this.handleBeforeUnload),this.url&&this.automaticOpen&&this.open()}return(0,T.A)(e,[{key:"addEventListener",value:function(e,n){var t=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.eventListeners[e]||(this.eventListeners[e]=[]);var r={callback:n,once:o.once||!1};return this.eventListeners[e].push(r),function(){return t.removeEventListener(e,n)}}},{key:"removeEventListener",value:function(e,n){if(!this.eventListeners[e])return!1;var t=this.eventListeners[e].length;return this.eventListeners[e]=this.eventListeners[e].filter((function(e){return e.callback!==n})),t!==this.eventListeners[e].length}},{key:"dispatchEvent",value:function(e,n){this.eventListeners[e]&&((0,l.A)(this.eventListeners[e]).forEach((function(t){try{t.callback(n)}catch(n){console.error("Error in ".concat(e," listener:"),n)}})),this.eventListeners[e]=this.eventListeners[e].filter((function(e){return!e.once})))}},{key:"getState",value:function(){return this.connectionState}},{key:"isOpen",value:function(){return 1===this.connectionState}},{key:"isConnecting",value:function(){return 0===this.connectionState||4===this.connectionState}},{key:"isClosed",value:function(){return 3===this.connectionState||2===this.connectionState}}],[{key:"forEndpoint",value:function(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=(0,R.$0)(n);return console.log("Creating WebSocketClient for endpoint '".concat(n,"' with URL: ").concat(o)),new e(z(z({},t),{},{url:o,debug:!0}))}}])}();var H,J,q,G,Y,$,V,K=d.A.Title,X=d.A.Text,Z=d.A.Paragraph,ee=(g.A.Option,m.A.TextArea),ne=O.Ay.div(H||(H=(0,r.A)(["\n  padding: 20px;\n"]))),te=(0,O.Ay)(p.A)(J||(J=(0,r.A)(["\n  margin-bottom: 20px;\n"]))),oe=O.Ay.div(q||(q=(0,r.A)(["\n  margin-bottom: 16px;\n\n  .label {\n    display: block;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n"]))),re=O.Ay.div(G||(G=(0,r.A)(["\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n"]))),ce=(0,O.Ay)(f.A)(Y||(Y=(0,r.A)(["\n  margin-bottom: 16px;\n"]))),ae=(0,O.Ay)(b.A)($||($=(0,r.A)(["\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid var(--border-color);\n  border-radius: 4px;\n  margin-bottom: 16px;\n\n  .ant-list-item {\n    padding: 8px 16px;\n    border-bottom: 1px solid var(--border-color);\n  }\n\n  .message-direction {\n    font-weight: bold;\n    margin-right: 8px;\n  }\n\n  .message-time {\n    color: var(--text-secondary);\n    font-size: 12px;\n  }\n\n  .message-content {\n    margin-top: 4px;\n    word-break: break-word;\n  }\n"]))),se=O.Ay.div(V||(V=(0,r.A)(["\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 8px 16px;\n  border-radius: 4px;\n  background-color: ",";\n\n  .status-icon {\n    margin-right: 8px;\n    font-size: 16px;\n    color: ",";\n  }\n"])),(function(e){switch(e.status){case"connected":return"rgba(82, 196, 26, 0.1)";case"connecting":return"rgba(250, 173, 20, 0.1)";case"disconnected":return"rgba(245, 34, 45, 0.1)";default:return"rgba(0, 0, 0, 0.05)"}}),(function(e){switch(e.status){case"connected":return"#52c41a";case"connecting":return"#faad14";case"disconnected":return"#f5222d";default:return"#8c8c8c"}}));const ie=function(){var e=(0,c.useState)((0,R.$0)("app_builder")),n=(0,o.A)(e,2),t=n[0],r=n[1],a=(0,c.useState)(!0),s=(0,o.A)(a,2),i=s[0],d=s[1],g=(0,c.useState)(!0),p=(0,o.A)(g,2),O=p[0],L=p[1],T=(0,c.useState)(""),W=(0,o.A)(T,2),I=W[0],N=W[1],D=(0,c.useState)([]),M=(0,o.A)(D,2),j=M[0],P=M[1],B=(0,c.useRef)(null),U=function(e){var n="string"==typeof e?{url:e}:e,t=(0,c.useState)(3),r=(0,o.A)(t,2),a=r[0],s=r[1],i=(0,c.useState)(null),l=(0,o.A)(i,2),u=l[0],d=l[1],g=(0,c.useState)(null),m=(0,o.A)(g,2),p=m[0],f=m[1],b=(0,c.useRef)(null),v=(0,c.useRef)({onOpen:n.onOpen,onMessage:n.onMessage,onClose:n.onClose,onError:n.onError});(0,c.useEffect)((function(){v.current={onOpen:n.onOpen,onMessage:n.onMessage,onClose:n.onClose,onError:n.onError}}),[n.onOpen,n.onMessage,n.onClose,n.onError]),(0,c.useEffect)((function(){var e,t={url:n.url,reconnectInterval:n.reconnectInterval,maxReconnectAttempts:n.maxReconnectAttempts,debug:n.debug,automaticOpen:!1};e=n.endpoint?F.forEndpoint(n.endpoint,t):new F(t),b.current=e;var o=function(e){var n=e.currentState;s(n)},r=function(e){v.current.onOpen&&v.current.onOpen(e)},c=function(e){var n=e.data;d(n),v.current.onMessage&&v.current.onMessage(n)},a=function(e){v.current.onClose&&v.current.onClose(e)},i=function(e){var t={timestamp:(new Date).toISOString(),type:e.type||"unknown",message:e.message||"WebSocket connection error",code:e.code,reason:e.reason,wasClean:e.wasClean,originalEvent:e};n.debug&&console.error("WebSocket Error:",t),f(t),v.current.onError&&v.current.onError(t)};return e.addEventListener("state_change",o),e.addEventListener("open",r),e.addEventListener("message",c),e.addEventListener("close",a),e.addEventListener("error",i),!1!==n.autoConnect&&e.open(),function(){e.removeEventListener("state_change",o),e.removeEventListener("open",r),e.removeEventListener("message",c),e.removeEventListener("close",a),e.removeEventListener("error",i),e.close()}}),[n.url,n.endpoint]);var h=(0,c.useCallback)((function(){if(b.current)try{f(null),b.current.open(),n.debug&&console.log("WebSocket: Attempting to connect to ".concat(n.url||n.endpoint))}catch(n){var e={timestamp:(new Date).toISOString(),type:"connection_error",message:n.message||"Failed to initiate WebSocket connection",originalError:n};f(e),v.current.onError&&v.current.onError(e)}}),[n.debug,n.url,n.endpoint]),y=(0,c.useCallback)((function(e,n){b.current&&b.current.close(e,n)}),[]),A=(0,c.useCallback)((function(e){return!!b.current&&b.current.send(e)}),[]),k=(0,c.useCallback)((function(e,n,t){return b.current?b.current.addEventListener(e,n,t):function(){}}),[]),S=(0,c.useCallback)((function(e,n){return!!b.current&&b.current.removeEventListener(e,n)}),[]);return{connectionState:a,lastMessage:u,lastError:p,isConnecting:0===a||4===a,isOpen:1===a,isClosed:3===a||2===a,hasError:null!==p,errorMessage:p?p.message:null,errorType:p?p.type:null,errorTimestamp:p?p.timestamp:null,connect:h,disconnect:y,send:A,addEventListener:k,removeEventListener:S,clearError:function(){return f(null)},client:b.current}}({url:t,autoConnect:!1,reconnect:i,debug:O,onMessage:function(e){var n={id:Date.now(),direction:"received",content:"object"===(0,u.A)(e)?JSON.stringify(e):e,timestamp:(new Date).toISOString()};P((function(e){return[].concat((0,l.A)(e),[n])}))}}),_=(U.connectionState,U.lastMessage,U.lastError),z=U.isConnecting,Q=U.isOpen,H=(U.isClosed,U.hasError),J=U.connect,q=U.disconnect,G=U.send,Y=U.clearError;return(0,c.useEffect)((function(){B.current&&B.current.scrollIntoView({behavior:"smooth"})}),[j]),c.createElement(ne,null,c.createElement(K,{level:3},"WebSocket Demo"),c.createElement(Z,null,"This demo shows how to use WebSockets with the useWebSocket hook."),c.createElement(te,{title:"WebSocket Connection"},c.createElement(ce,{type:"info",message:"WebSocket Configuration",description:"Configure and manage your WebSocket connection.",icon:c.createElement(E.A,null)}),c.createElement(se,{status:Q?"connected":z?"connecting":"disconnected"},Q?c.createElement(A.A,{className:"status-icon"}):z?c.createElement(k.A,{spin:!0,className:"status-icon"}):c.createElement(S.A,{className:"status-icon"}),c.createElement(X,{strong:!0},Q?"Connected":z?"Connecting...":H?"Disconnected (".concat((null==_?void 0:_.message)||"Error",")"):"Disconnected")),H&&c.createElement(f.A,{type:"error",message:"Connection Error",description:(null==_?void 0:_.message)||"An error occurred with the WebSocket connection",style:{marginBottom:"16px"},closable:!0,onClose:Y}),c.createElement(oe,null,c.createElement("div",{className:"label"},"WebSocket URL:"),c.createElement(m.A,{value:t,onChange:function(e){return r(e.target.value)},placeholder:"Enter WebSocket URL",disabled:Q||z})),c.createElement(oe,null,c.createElement("div",{className:"label"},"Auto Reconnect:"),c.createElement(v.A,{checked:i,onChange:d,disabled:Q||z})),c.createElement(oe,null,c.createElement("div",{className:"label"},"Debug Mode:"),c.createElement(v.A,{checked:O,onChange:L})),c.createElement(re,null,Q?c.createElement(h.Ay,{danger:!0,icon:c.createElement(S.A,null),onClick:function(){q(1e3,"User initiated disconnect")}},"Disconnect"):c.createElement(h.Ay,{type:"primary",icon:c.createElement(w.A,null),onClick:function(){J()},loading:z,disabled:z},"Connect"))),c.createElement(te,{title:"Messages"},c.createElement(ce,{type:"info",message:"Send and Receive Messages",description:"Send messages to the WebSocket server and view received messages.",icon:c.createElement(E.A,null)}),c.createElement(ae,{dataSource:j,locale:{emptyText:"No messages yet"},renderItem:function(e){return c.createElement(b.A.Item,null,c.createElement("div",{style:{width:"100%"}},c.createElement("div",null,c.createElement(y.A,{color:"sent"===e.direction?"blue":"green",text:c.createElement("span",{className:"message-direction"},"sent"===e.direction?"Sent":"Received")}),c.createElement("span",{className:"message-time"},new Date(e.timestamp).toLocaleTimeString())),c.createElement("div",{className:"message-content"},c.createElement(X,{code:!0},e.content))))}}),c.createElement("div",{ref:B}),c.createElement(oe,null,c.createElement("div",{className:"label"},"Message:"),c.createElement(ee,{value:I,onChange:function(e){return N(e.target.value)},placeholder:"Enter message to send",rows:4,disabled:!Q})),c.createElement(re,null,c.createElement(h.Ay,{type:"primary",icon:c.createElement(C.A,null),onClick:function(){if(I.trim()&&G(I)){var e={id:Date.now(),direction:"sent",content:I,timestamp:(new Date).toISOString()};P((function(n){return[].concat((0,l.A)(n),[e])})),N("")}},disabled:!Q||!I.trim()},"Send"),c.createElement(h.Ay,{icon:c.createElement(x.A,null),onClick:function(){P([])},disabled:0===j.length},"Clear Messages"))))};var le,ue,de,ge=t(1468),me=(t(9740),t(6893),t(3986)),pe=t(4816),fe=["url","autoConnect","autoReconnect","updateRedux","debug"];function be(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);n&&(o=o.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,o)}return t}function ve(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?be(Object(t),!0).forEach((function(n){(0,W.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):be(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}try{var he=t(7177);le=he.default||he.EnhancedWebSocketClient,ue=he.ConnectionState}catch(e){console.warn("EnhancedWebSocketClient not available, using fallback")}try{var ye=t(3774);de=ye.timeSync||ye.default}catch(e){console.warn("TimeSyncService not available, using fallback")}a.A.TabPane,m.A.TextArea,d.A.Title,d.A.Text,d.A.Paragraph;var Ae,ke,Se,Ee,we,Ce,xe,Oe,Le,Te,We=s.styled.div(Ae||(Ae=(0,r.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),i.Ay.spacing[4]);s.styled.div(ke||(ke=(0,r.A)(["\n  display: flex;\n  align-items: center;\n  gap: ",";\n  padding: ",";\n  border-radius: ",";\n  background-color: ",";\n  color: ",";\n"])),i.Ay.spacing[2],i.Ay.spacing[3],i.Ay.borderRadius.md,(function(e){return e.connected?i.Ay.colors.success.light:i.Ay.colors.error.light}),(function(e){return e.connected?i.Ay.colors.success.dark:i.Ay.colors.error.dark})),s.styled.div(Se||(Se=(0,r.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n  max-height: 400px;\n  overflow-y: auto;\n  padding: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  background-color: ",";\n"])),i.Ay.spacing[2],i.Ay.spacing[2],i.Ay.colors.neutral[200],i.Ay.borderRadius.md,i.Ay.colors.neutral[50]),s.styled.div(Ee||(Ee=(0,r.A)(["\n  padding: "," ",";\n  border-radius: ",";\n  background-color: ",";\n  border-left: 4px solid ",";\n  box-shadow: ",";\n\n  .message-header {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: ",";\n    font-size: ",";\n    color: ",";\n  }\n\n  .timestamp {\n    font-family: monospace;\n    font-size: ",";\n    color: ",";\n    background-color: ",";\n    padding: 2px 4px;\n    border-radius: 3px;\n  }\n\n  .message-content {\n    word-break: break-word;\n  }\n\n  pre {\n    background-color: ",";\n    padding: ",";\n    border-radius: ",";\n    overflow-x: auto;\n    font-family: ",";\n    font-size: ",";\n    margin: "," 0 0 0;\n  }\n"])),i.Ay.spacing[2],i.Ay.spacing[3],i.Ay.borderRadius.md,(function(e){return"sent"===e.type?i.Ay.colors.primary.light:"white"}),(function(e){return"sent"===e.type?i.Ay.colors.primary.main:i.Ay.colors.secondary.main}),i.Ay.shadows.sm,i.Ay.spacing[1],i.Ay.typography.fontSize.sm,i.Ay.colors.neutral[500],i.Ay.typography.fontSize.xs,i.Ay.colors.neutral[600],i.Ay.colors.neutral[100],i.Ay.colors.neutral[100],i.Ay.spacing[2],i.Ay.borderRadius.sm,i.Ay.typography.fontFamily.code,i.Ay.typography.fontSize.sm,i.Ay.spacing[2]),s.styled.div(we||(we=(0,r.A)(["\n  display: flex;\n  gap: ",";\n"])),i.Ay.spacing[2]),s.styled.div(Ce||(Ce=(0,r.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n  padding: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  background-color: white;\n"])),i.Ay.spacing[3],i.Ay.spacing[3],i.Ay.colors.neutral[200],i.Ay.borderRadius.md),s.styled.div(xe||(xe=(0,r.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),i.Ay.spacing[2]),s.styled.div(Oe||(Oe=(0,r.A)(["\n  display: flex;\n  flex-wrap: wrap;\n  gap: ",";\n  margin-bottom: ",";\n"])),i.Ay.spacing[2],i.Ay.spacing[3]),s.styled.div(Le||(Le=(0,r.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n  padding: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  background-color: white;\n"])),i.Ay.spacing[3],i.Ay.spacing[3],i.Ay.colors.neutral[200],i.Ay.borderRadius.md),s.styled.div(Te||(Te=(0,r.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n  padding: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  background-color: ",";\n"])),i.Ay.spacing[1],i.Ay.spacing[2],i.Ay.colors.neutral[200],i.Ay.borderRadius.md,i.Ay.colors.neutral[50]);const Re=function(){var e=(0,ge.d4)((function(e){try{return(null==e?void 0:e.websocket)||{}}catch(e){return console.warn("Error accessing websocket state:",e),{}}})),n=null==e?void 0:e.url,t=(0,c.useState)("messages"),r=(0,o.A)(t,2),a=(r[0],r[1],(0,c.useState)(n||"ws://localhost:8000/ws/test/")),s=(0,o.A)(a,2),i=s[0],d=(s[1],(0,c.useState)("")),g=(0,o.A)(d,2),m=(g[0],g[1],(0,c.useState)(!0)),b=(0,o.A)(m,2),v=b[0],y=(b[1],(0,c.useState)(!0)),A=(0,o.A)(y,2),k=(A[0],A[1],(0,c.useState)(!0)),S=(0,o.A)(k,2),E=S[0],w=(S[1],(0,c.useRef)(null)),C=function(e){var n=e.url,t=e.autoConnect,r=void 0===t||t,a=e.autoReconnect,s=void 0===a||a,i=e.updateRedux,u=void 0===i||i,d=e.debug,g=void 0!==d&&d,m=(0,me.A)(e,fe),p=(0,ge.wA)(),f=(0,c.useRef)(null),b=(0,c.useState)(!1),v=(0,o.A)(b,2),h=v[0],y=v[1],A=(0,c.useState)(!1),k=(0,o.A)(A,2),S=k[0],E=k[1],w=(0,c.useState)([]),C=(0,o.A)(w,2),x=C[0],O=C[1],L=(0,c.useState)(null),T=(0,o.A)(L,2),W=T[0],R=T[1],N=(0,c.useState)(0),M=(0,o.A)(N,2),j=M[0],P=M[1],B=(0,c.useState)(null),U=(0,o.A)(B,2),_=U[0],z=U[1];(0,c.useEffect)((function(){if(n){if(!le){console.warn("EnhancedWebSocketClient not available, using native WebSocket");try{var e=new WebSocket(n);return f.current=e,e.destroy=function(){return e.close()},e.open=function(){},function(){f.current&&(f.current.close(),f.current=null)}}catch(e){return console.error("Failed to create WebSocket:",e),void R(e)}}var t=new le(ve({url:n,autoConnect:r,autoReconnect:s,debug:g},m));return f.current=t,function(){f.current&&(f.current.destroy(),f.current=null)}}}),[n,r,s,g]),(0,c.useEffect)((function(){var e=f.current;if(e){var n=function(){var e=(0,I.A)(D().mark((function e(n){return D().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!de||"function"!=typeof de.synchronize){e.next=9;break}return e.prev=1,e.next=4,de.synchronize();case 4:e.next=9;break;case 6:e.prev=6,e.t0=e.catch(1),console.warn("Time synchronization failed:",e.t0);case 9:if(console.log("WebSocket connection opened:",n),y(!0),E(!1),R(null),z(null),u)try{p((0,pe.websocketConnected)())}catch(e){console.warn("Failed to dispatch websocketConnected:",e)}case 15:case"end":return e.stop()}}),e,null,[[1,6]])})));return function(n){return e.apply(this,arguments)}}(),t=function(e){console.log("WebSocket connection closed:",e),y(!1),E(!1),z(e),u&&p((0,pe.websocketDisconnected)())},o=function(e){console.error("WebSocket error:",e),R(e),E(!1)},r=(0,c.useCallback)((function(e){try{var n=e;if(e.data)try{n=JSON.parse(e.data)}catch(t){n={content:e.data}}var t=new Date(n.timestamp||Date.now()),o=t;if(de&&"function"==typeof de.getClientTime)try{o=de.getClientTime(t)}catch(e){console.warn("Time sync failed, using server timestamp:",e)}if(O((function(e){return[].concat((0,l.A)(e),[ve(ve({},n),{},{timestamp:o,serverTimestamp:t})])})),u)try{p((0,pe.websocketMessageReceived)({type:"received",content:n.content||n,timestamp:o,serverTimestamp:t}))}catch(e){console.warn("Failed to dispatch websocketMessageReceived:",e)}}catch(e){console.error("Error handling WebSocket message:",e)}}),[p,u]),a=function(e){console.log("WebSocket reconnect attempt:",e),E(!0),P(e.attempt)};return e.addEventListener("open",n),e.addEventListener("close",t),e.addEventListener("error",o),e.addEventListener("message",r),e.addEventListener("reconnect_attempt",a),function(){e&&(e.removeEventListener("open",n),e.removeEventListener("close",t),e.removeEventListener("error",o),e.removeEventListener("message",r),e.removeEventListener("reconnect_attempt",a))}}}),[p,u]);var Q=(0,c.useCallback)((function(){f.current&&(E(!0),f.current.open())}),[]),F=(0,c.useCallback)((function(){f.current&&f.current.close()}),[]),H=(0,c.useCallback)((function(e){var n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(!f.current)return!1;if(!(ue?f.current.connectionState===ue.OPEN:f.current.readyState===WebSocket.OPEN))return!1;try{var t=!1;if(f.current.send&&"function"==typeof f.current.send)t=f.current.send(e,n);else{var o="string"==typeof e?e:JSON.stringify(e);f.current.send(o),t=!0}if(t){var r=(new Date).toISOString();if(O((function(n){return[].concat((0,l.A)(n),[{type:"sent",content:e,timestamp:r}])})),u)try{p((0,pe.websocketMessageReceived)({type:"sent",content:e,timestamp:r}))}catch(e){console.warn("Failed to dispatch sent message:",e)}}return t}catch(e){return console.error("Error sending WebSocket message:",e),!1}}),[p,u]);return{connected:h,connecting:S,reconnectAttempt:j,messages:x,error:W,closeInfo:_,connect:Q,disconnect:F,sendMessage:H,clearMessages:(0,c.useCallback)((function(){O([])}),[]),resetConnection:(0,c.useCallback)((function(){f.current&&(f.current.close(),setTimeout((function(){R(null),z(null),P(0),f.current.open()}),1e3))}),[]),client:f.current}}({url:i,autoConnect:!1,autoReconnect:v,debug:!0,updateRedux:!0,heartbeatInterval:E?3e4:0,reconnectInterval:2e3,maxReconnectAttempts:5}),x=(C.connected,C.connecting,C.reconnectAttempt,C.messages);return C.error,C.closeInfo,C.connect,C.disconnect,C.sendMessage,C.clearMessages,C.resetConnection,e&&"object"===(0,u.A)(e)?((0,c.useEffect)((function(){w.current&&w.current.scrollIntoView({behavior:"smooth"})}),[x]),c.createElement(We,null,c.createElement(p.A,{title:"Legacy WebSocket Manager"},c.createElement(f.A,{message:"Legacy Component",description:"This is the legacy WebSocket manager. We recommend using the new WebSocket Demo component for better performance and features.",type:"warning",showIcon:!0,style:{marginBottom:"16px"}}),c.createElement(h.Ay,{type:"primary",onClick:function(){return window.location.reload()},style:{marginBottom:"16px"}},"Switch to New WebSocket Demo")))):c.createElement(p.A,{title:"Legacy WebSocket Manager",style:{margin:"20px"}},c.createElement(f.A,{message:"Legacy Component",description:"This is the legacy WebSocket manager. We recommend using the new WebSocket Demo component for better performance and features.",type:"warning",showIcon:!0,style:{marginBottom:"16px"}}),c.createElement(h.Ay,{type:"primary",onClick:function(){return window.location.reload()}},"Switch to New WebSocket Demo"))};var Ie,Ne=s.styled.div(Ie||(Ie=(0,r.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),i.Ay.spacing[4]);const De=function(){var e=(0,c.useState)("new"),n=(0,o.A)(e,2),t=n[0],r=n[1];return c.createElement(Ne,null,c.createElement(a.A,{activeKey:t,onChange:r},c.createElement(a.A.TabPane,{tab:"Enhanced WebSocket",key:"new"},c.createElement(ie,null)),c.createElement(a.A.TabPane,{tab:"Legacy WebSocket",key:"legacy"},c.createElement(Re,null))))}}}]);