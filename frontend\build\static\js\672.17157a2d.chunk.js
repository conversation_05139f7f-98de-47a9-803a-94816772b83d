"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[672],{672:(e,t,r)=>{r.r(t),r.d(t,{default:()=>B});var n,a,o,l,s=r(5544),i=r(7528),c=r(6540),u=r(3016),m=r(2395),d=r(677),p=r(7197),g=r(2702),y=r(9249),f=r(7122),h=r(6754),A=r(6955),E=r(581),v=r(6191),w=r(6020),k=u.A.Title,M=u.A.Text,S=u.A.Paragraph,x=m.A.TabPane,C=v.styled.div(n||(n=(0,i.A)(["\n  padding: ",";\n"])),w.Ay.spacing[3]),b=v.styled.div(a||(a=(0,i.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: ",";\n  margin-bottom: ",";\n"])),w.Ay.spacing[4],w.Ay.spacing[4]),z=(0,v.styled)(d.A)(o||(o=(0,i.A)(["\n  text-align: center;\n"]))),T=v.styled.div(l||(l=(0,i.A)(["\n  height: 300px;\n  margin-bottom: ",";\n  background-color: ",";\n  border-radius: ",";\n  display: flex;\n  justify-content: center;\n  align-items: center;\n"])),w.Ay.spacing[4],w.Ay.colors.neutral[100],w.Ay.borderRadius.md);const B=function(){var e=(0,c.useState)({memory:{used:0,total:0,limit:0},cpu:{usage:0,cores:0},network:{requests:0,transferred:0,errors:0},rendering:{fps:0,renderTime:0},components:{count:0,renderCount:0}}),t=(0,s.A)(e,2),r=t[0],n=t[1],a=(0,c.useState)(!0),o=(0,s.A)(a,2),l=o[0],i=o[1],u=(0,c.useState)("overview"),d=(0,s.A)(u,2),v=d[0],B=d[1],R=(0,c.useState)(null),I=(0,s.A)(R,2),q=I[0],F=I[1],K=(0,c.useState)(null),P=(0,s.A)(K,2),G=P[0],H=P[1],L=(0,c.useRef)(),J=(0,c.useRef)(0),U=(0,c.useRef)(performance.now()),j=(0,c.useRef)([]),D=function(){try{var e;i(!0);var t=(null===(e=window.performance)||void 0===e?void 0:e.memory)||{usedJSHeapSize:100*Math.random()*1024*1024,totalJSHeapSize:209715200,jsHeapSizeLimit:524288e3},r=100*Math.random(),a=navigator.hardwareConcurrency||4,o=Math.floor(50*Math.random()),l=5*Math.random()*1024*1024,s=Math.floor(3*Math.random()),c=document.querySelectorAll("[data-component]").length||Math.floor(20*Math.random()),u=Math.floor(100*Math.random()),m=performance.now(),d=m-U.current;U.current=m,j.current.push(d),j.current.length>60&&j.current.shift();var p=j.current.reduce((function(e,t){return e+t}),0)/j.current.length,g=Math.round(1e3/p);n({memory:{used:t.usedJSHeapSize,total:t.totalJSHeapSize,limit:t.jsHeapSizeLimit},cpu:{usage:r,cores:a},network:{requests:o,transferred:l,errors:s},rendering:{fps:g,renderTime:p},components:{count:c,renderCount:u}}),H(new Date),i(!1)}catch(e){console.error("Error collecting metrics:",e),F("Failed to collect performance metrics"),i(!1)}};(0,c.useEffect)((function(){D();var e=function(){J.current++,L.current=requestAnimationFrame(e)};L.current=requestAnimationFrame(e);var t=setInterval((function(){D()}),2e3);return function(){clearInterval(t),cancelAnimationFrame(L.current)}}),[]);var N=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;if(0===e)return"0 Bytes";var r=t<0?0:t,n=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,n)).toFixed(r))+" "+["Bytes","KB","MB","GB","TB"][n]},O=function(e,t){return e>=t.danger?w.Ay.colors.danger.main:e>=t.warning?w.Ay.colors.warning.main:w.Ay.colors.success.main},Q=[{title:"URL",dataIndex:"url",key:"url"},{title:"Method",dataIndex:"method",key:"method"},{title:"Status",dataIndex:"status",key:"status",render:function(e){return c.createElement(M,{type:e>=400?"danger":e>=300?"warning":"success",strong:!0},e)}},{title:"Time",dataIndex:"time",key:"time"},{title:"Size",dataIndex:"size",key:"size"}];return c.createElement(C,null,c.createElement(k,{level:4},"Performance Monitor"),c.createElement(S,null,"Monitor and analyze the performance of your application."),q&&c.createElement(p.A,{message:"Error",description:q,type:"error",showIcon:!0,closable:!0,onClose:function(){return F(null)},style:{marginBottom:w.Ay.spacing[4]}}),c.createElement(g.A,{style:{marginBottom:w.Ay.spacing[4]}},c.createElement(y.Ay,{icon:c.createElement(E.A,null),onClick:D,loading:l},"Refresh Metrics"),G&&c.createElement(M,{type:"secondary"},"Last updated: ",G.toLocaleTimeString())),c.createElement(m.A,{activeKey:v,onChange:B},c.createElement(x,{tab:"Overview",key:"overview"},c.createElement(b,null,c.createElement(z,null,c.createElement(f.A,{title:"Memory Usage",value:N(r.memory.used),suffix:" / ".concat(N(r.memory.total))}),c.createElement(h.A,{percent:Math.round(r.memory.used/r.memory.total*100),status:r.memory.used/r.memory.total>.9?"exception":r.memory.used/r.memory.total>.7?"warning":"normal",strokeColor:O(r.memory.used/r.memory.total*100,{warning:70,danger:90})})),c.createElement(z,null,c.createElement(f.A,{title:"CPU Usage",value:Math.round(r.cpu.usage),suffix:"%"}),c.createElement(h.A,{percent:Math.round(r.cpu.usage),status:r.cpu.usage>90?"exception":r.cpu.usage>70?"warning":"normal",strokeColor:O(r.cpu.usage,{warning:70,danger:90})})),c.createElement(z,null,c.createElement(f.A,{title:"FPS",value:r.rendering.fps,suffix:"fps"}),c.createElement(h.A,{percent:Math.min(100,Math.round(r.rendering.fps/60*100)),status:r.rendering.fps<30?"exception":r.rendering.fps<50?"warning":"normal",strokeColor:O(r.rendering.fps,{warning:50,danger:30})})),c.createElement(z,null,c.createElement(f.A,{title:"Network Requests",value:r.network.requests}),c.createElement("div",{style:{marginTop:w.Ay.spacing[2]}},c.createElement(M,{type:r.network.errors>0?"danger":"success"},r.network.errors," errors"),c.createElement("br",null),c.createElement(M,{type:"secondary"},N(r.network.transferred)," transferred")))),c.createElement(T,null,c.createElement(M,{type:"secondary"},"Performance charts will be available in a future update"))),c.createElement(x,{tab:"Network",key:"network"},c.createElement(A.A,{dataSource:[{key:"1",url:"/api/components",method:"GET",status:200,time:"120ms",size:"5.2KB"},{key:"2",url:"/api/layouts",method:"GET",status:200,time:"85ms",size:"3.8KB"},{key:"3",url:"/api/themes",method:"GET",status:200,time:"95ms",size:"2.1KB"},{key:"4",url:"/api/user",method:"GET",status:200,time:"110ms",size:"1.5KB"},{key:"5",url:"/api/settings",method:"GET",status:404,time:"75ms",size:"0.5KB"}],columns:Q,pagination:!1,size:"small"})),c.createElement(x,{tab:"Components",key:"components"},c.createElement(b,null,c.createElement(z,null,c.createElement(f.A,{title:"Component Count",value:r.components.count})),c.createElement(z,null,c.createElement(f.A,{title:"Render Count",value:r.components.renderCount})),c.createElement(z,null,c.createElement(f.A,{title:"Average Render Time",value:Math.round(r.rendering.renderTime),suffix:"ms"}))),c.createElement(p.A,{message:"Component Performance",description:"Detailed component performance metrics will be available in a future update.",type:"info",showIcon:!0}))))}}}]);