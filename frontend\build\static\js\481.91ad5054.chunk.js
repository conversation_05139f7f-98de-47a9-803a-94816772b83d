"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[481],{7481:(e,t,a)=>{a.r(t),a.d(t,{default:()=>K});var r=a(467),n=a(5544),l=a(7528),c=a(4756),i=a.n(c),o=a(6540),s=a(3016),u=a(4358),p=a(677),m=a(7197),d=a(9029),h=a(7355),g=a(5039),v=a(9249),f=a(9248),y=a(3903),E=a(581),x=a(7345),w=a(1250),A=a(1468),b=(a(8035),a(9740),{temporary:new Map,persistent:new Map,expiring:new Map,timestamps:new Map});const C=function(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{type:"temporary",expiresIn:36e5},r=a.type,n=a.expiresIn;switch(r){case"persistent":b.persistent.set(e,t);try{localStorage.setItem("cache_".concat(e),JSON.stringify(t))}catch(e){console.warn("Failed to save to localStorage:",e)}break;case"expiring":b.expiring.set(e,t),b.timestamps.set(e,Date.now()+n);break;default:b.temporary.set(e,t)}},k=function(e){switch(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"temporary"){case"persistent":if(b.persistent.has(e))return b.persistent.get(e);try{var t=localStorage.getItem("cache_".concat(e));if(t){var a=JSON.parse(t);return b.persistent.set(e,a),a}}catch(e){console.warn("Failed to retrieve from localStorage:",e)}return null;case"expiring":if(b.expiring.has(e)){var r=b.timestamps.get(e);if(r&&r>Date.now())return b.expiring.get(e);b.expiring.delete(e),b.timestamps.delete(e)}return null;default:return b.temporary.get(e)||null}},S=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"temporary";if(e)switch(t){case"persistent":b.persistent.delete(e);try{localStorage.removeItem("cache_".concat(e))}catch(e){console.warn("Failed to remove from localStorage:",e)}break;case"expiring":b.expiring.delete(e),b.timestamps.delete(e);break;case"all":b.temporary.delete(e),b.persistent.delete(e),b.expiring.delete(e),b.timestamps.delete(e);try{localStorage.removeItem("cache_".concat(e))}catch(e){console.warn("Failed to remove from localStorage:",e)}break;default:b.temporary.delete(e)}else switch(t){case"persistent":b.persistent.clear();try{Object.keys(localStorage).forEach((function(e){e.startsWith("cache_")&&localStorage.removeItem(e)}))}catch(e){console.warn("Failed to clear localStorage:",e)}break;case"expiring":b.expiring.clear(),b.timestamps.clear();break;case"all":b.temporary.clear(),b.persistent.clear(),b.expiring.clear(),b.timestamps.clear();try{Object.keys(localStorage).forEach((function(e){e.startsWith("cache_")&&localStorage.removeItem(e)}))}catch(e){console.warn("Failed to clear localStorage:",e)}break;default:b.temporary.clear()}};var T,D,I,_,M,N=a(4816),V=s.A.Title,F=s.A.Text,O=s.A.Paragraph,R=u.A.Option,P=w.Ay.div(T||(T=(0,l.A)(["\n  padding: 20px;\n"]))),L=(0,w.Ay)(p.A)(D||(D=(0,l.A)(["\n  margin-bottom: 20px;\n"]))),j=w.Ay.div(I||(I=(0,l.A)(["\n  margin-bottom: 16px;\n  \n  .label {\n    display: block;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n"]))),z=w.Ay.div(_||(_=(0,l.A)(["\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n"]))),J=(0,w.Ay)(m.A)(M||(M=(0,l.A)(["\n  margin-bottom: 16px;\n"])));const K=function(){var e=(0,o.useState)(""),t=(0,n.A)(e,2),a=t[0],l=t[1],c=(0,o.useState)("temporary"),s=(0,n.A)(c,2),p=s[0],w=s[1],b=(0,o.useState)(!0),T=(0,n.A)(b,2),D=T[0],I=T[1],_=function(e){var t=e.selector,a=e.action,l=e.cacheKey,c=e.cacheType,s=void 0===c?"temporary":c,u=e.expiresIn,p=void 0===u?36e5:u,m=e.useCache,d=void 0===m||m,h=e.defaultValue,g=void 0===h?null:h,v=(0,A.wA)(),f=(0,o.useState)(!1),y=(0,n.A)(f,2),E=y[0],x=y[1],w=(0,o.useState)(null),b=(0,n.A)(w,2),T=b[0],D=b[1],I=t?(0,A.d4)(t):void 0,_=(0,o.useCallback)((function(){return d&&l?k(l,s):null}),[d,l,s]),M=(0,o.useCallback)((function(e){d&&l&&C(l,e,{type:s,expiresIn:p})}),[d,l,s,p]),N=(0,o.useCallback)((function(){l&&S(l,s)}),[l,s]),V=(0,o.useCallback)(function(){var e=(0,r.A)(i().mark((function e(t){var r;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a){e.next=2;break}return e.abrupt("return");case 2:return x(!0),D(null),e.prev=4,e.next=7,v(a(t));case 7:return r=e.sent,d&&l&&M((null==r?void 0:r.payload)||r),x(!1),e.abrupt("return",r);case 13:throw e.prev=13,e.t0=e.catch(4),D(e.t0),x(!1),e.t0;case 18:case"end":return e.stop()}}),e,null,[[4,13]])})));return function(t){return e.apply(this,arguments)}}(),[a,v,d,l,M]),F=(0,o.useCallback)((function(){if(void 0!==I)return I;var e=_();return null!==e?e:g}),[I,_,g]),O=(0,o.useCallback)((0,r.A)(i().mark((function e(){var t;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a){e.next=2;break}return e.abrupt("return");case 2:return x(!0),D(null),e.prev=4,e.next=7,v(a());case 7:return t=e.sent,d&&l&&M((null==t?void 0:t.payload)||t),x(!1),e.abrupt("return",t);case 13:throw e.prev=13,e.t0=e.catch(4),D(e.t0),x(!1),e.t0;case 18:case"end":return e.stop()}}),e,null,[[4,13]])}))),[a,v,d,l,M]);return(0,o.useEffect)((function(){d&&l&&void 0!==I&&!_()&&M(I)}),[d,l,I,_,M]),{data:F(),loading:E,error:T,updateData:V,refreshData:O,clearCache:N}}({selector:function(e){var t;return null===(t=e.ui)||void 0===t?void 0:t.currentView},action:N.setCurrentView,cacheKey:"current_view",cacheType:p,useCache:D,defaultValue:"components"}),M=_.data,K=_.loading,W=_.error,B=_.updateData,G=_.refreshData,U=_.clearCache,q=function(){var e=(0,r.A)(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a.trim()){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,B(a);case 5:l(""),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(2),console.error("Error saving data:",e.t0);case 11:case"end":return e.stop()}}),e,null,[[2,8]])})));return function(){return e.apply(this,arguments)}}(),H=function(){var e=(0,r.A)(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,G();case 3:e.next=8;break;case 5:e.prev=5,e.t0=e.catch(0),console.error("Error refreshing data:",e.t0);case 8:case"end":return e.stop()}}),e,null,[[0,5]])})));return function(){return e.apply(this,arguments)}}();return o.createElement(P,null,o.createElement(V,{level:3},"Data Management Demo"),o.createElement(O,null,"This demo shows how to use the data management utilities to optimize Redux usage and handle data operations."),o.createElement(L,{title:"Current View Data"},o.createElement(J,{type:"info",message:"Redux + Cache Integration",description:"This example shows how to use the useDataManager hook to manage data with Redux and caching.",icon:o.createElement(f.A,null)}),K?o.createElement("div",{style:{textAlign:"center",padding:"20px"}},o.createElement(d.A,{size:"large"}),o.createElement("div",{style:{marginTop:"10px"}},"Loading data...")):o.createElement(o.Fragment,null,W&&o.createElement(m.A,{type:"error",message:"Error",description:W.message||"An error occurred while managing data",style:{marginBottom:"16px"}}),o.createElement(j,null,o.createElement("div",{className:"label"},"Current View:"),o.createElement(F,{strong:!0},M)),o.createElement(j,null,o.createElement("div",{className:"label"},"New View:"),o.createElement(h.A,{value:a,onChange:function(e){return l(e.target.value)},placeholder:"Enter a new view name"})),o.createElement(j,null,o.createElement("div",{className:"label"},"Cache Type:"),o.createElement(u.A,{value:p,onChange:w,style:{width:"100%"}},o.createElement(R,{value:"temporary"},"Temporary (Memory only)"),o.createElement(R,{value:"persistent"},"Persistent (LocalStorage)"),o.createElement(R,{value:"expiring"},"Expiring (Time-based)"))),o.createElement(j,null,o.createElement("div",{className:"label"},"Use Cache:"),o.createElement(g.A,{checked:D,onChange:I})),o.createElement(z,null,o.createElement(v.Ay,{type:"primary",icon:o.createElement(y.A,null),onClick:q,disabled:!a.trim()},"Save to Redux"),o.createElement(v.Ay,{icon:o.createElement(E.A,null),onClick:H},"Refresh Data"),o.createElement(v.Ay,{danger:!0,icon:o.createElement(x.A,null),onClick:function(){U()}},"Clear Cache")))),o.createElement(L,{title:"Direct Cache Operations"},o.createElement(J,{type:"info",message:"Direct Cache API",description:"This example shows how to use the dataManager utility directly for caching operations.",icon:o.createElement(f.A,null)}),o.createElement(j,null,o.createElement("div",{className:"label"},"Cache Value:"),o.createElement(h.A,{value:a,onChange:function(e){return l(e.target.value)},placeholder:"Enter a value to cache"})),o.createElement(j,null,o.createElement("div",{className:"label"},"Cache Type:"),o.createElement(u.A,{value:p,onChange:w,style:{width:"100%"}},o.createElement(R,{value:"temporary"},"Temporary (Memory only)"),o.createElement(R,{value:"persistent"},"Persistent (LocalStorage)"),o.createElement(R,{value:"expiring"},"Expiring (Time-based)"))),o.createElement(z,null,o.createElement(v.Ay,{type:"primary",onClick:function(){a.trim()&&(C("direct_cache_demo",a,{type:p,expiresIn:36e5}),l(""))},disabled:!a.trim()},"Set Cache"),o.createElement(v.Ay,{onClick:function(){var e=k("direct_cache_demo",p);l(e||"")}},"Get Cache"),o.createElement(v.Ay,{danger:!0,onClick:function(){return S("direct_cache_demo",p)}},"Clear Cache"))))}}}]);