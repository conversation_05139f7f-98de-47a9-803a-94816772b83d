"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[905],{4905:(e,t,n)=>{n.r(t),n.d(t,{default:()=>m});var o,r,l=n(7528),i=n(6540),a=n(1250),u=n(6413),c=a.Ay.div(o||(o=(0,l.A)(["\n  padding: 20px;\n  background-color: ",";\n  color: ",";\n"])),(function(e){var t,n,o;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.background?e.theme.colorPalette.background:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.background)&&void 0!==n&&n.default?e.theme.colors.background.default:null!==(o=e.theme)&&void 0!==o&&o.backgroundColor?e.theme.backgroundColor:"#FFFFFF"}),(function(e){var t,n,o;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.textPrimary?e.theme.colorPalette.textPrimary:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.text)&&void 0!==n&&n.primary?e.theme.colors.text.primary:null!==(o=e.theme)&&void 0!==o&&o.textColor?e.theme.textColor:"#111827"})),d=a.Ay.button(r||(r=(0,l.A)(["\n  background-color: ",";\n  color: white;\n  padding: 10px 20px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  margin: 10px;\n  \n  &:hover {\n    opacity: 0.8;\n  }\n"])),(function(e){var t,n,o;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.primary?e.theme.colorPalette.primary:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.primary)&&void 0!==n&&n.main?e.theme.colors.primary.main:null!==(o=e.theme)&&void 0!==o&&o.primaryColor?e.theme.primaryColor:"#2563EB"}));const m=function(){return i.createElement(c,null,i.createElement("h2",null,"Theme Test Component"),i.createElement("p",null,"This component tests if the theme is working properly."),i.createElement(u.A,{targetId:"test-content"}),i.createElement(d,{onClick:function(){return alert("Theme test button clicked!")}},"Test Button"),i.createElement("div",{id:"test-content"},i.createElement("p",null,"This is the test content that the skip link should navigate to."),i.createElement("p",null,"If you can see this page without errors, the theme fixes are working!")))}}}]);