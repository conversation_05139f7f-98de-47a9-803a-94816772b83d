"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[3876],{3497:(e,t,n)=>{n.d(t,{A:()=>w});var r=n(58168),i=n(64467),a=n(5544),o=n(53986),s=n(62427),u=n(46942),l=n.n(u),c=n(8719),d=n(96540),f=n(16928),v=n(25371),g=f.A.ESC,h=f.A.TAB;const p=(0,d.forwardRef)((function(e,t){var n=e.overlay,r=e.arrow,i=e.prefixCls,a=(0,d.useMemo)((function(){return"function"==typeof n?n():n}),[n]),o=(0,c.K4)(t,(0,c.A9)(a));return d.createElement(d.Fragment,null,r&&d.createElement("div",{className:"".concat(i,"-arrow")}),d.cloneElement(a,{ref:(0,c.f3)(a)?o:void 0}))}));var m={adjustX:1,adjustY:1},A=[0,0];const F={topLeft:{points:["bl","tl"],overflow:m,offset:[0,-4],targetOffset:A},top:{points:["bc","tc"],overflow:m,offset:[0,-4],targetOffset:A},topRight:{points:["br","tr"],overflow:m,offset:[0,-4],targetOffset:A},bottomLeft:{points:["tl","bl"],overflow:m,offset:[0,4],targetOffset:A},bottom:{points:["tc","bc"],overflow:m,offset:[0,4],targetOffset:A},bottomRight:{points:["tr","br"],overflow:m,offset:[0,4],targetOffset:A}};var y=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus","overlay","children","onVisibleChange"];function b(e,t){var n,u=e.arrow,f=void 0!==u&&u,m=e.prefixCls,A=void 0===m?"rc-dropdown":m,b=e.transitionName,w=e.animation,V=e.align,E=e.placement,k=void 0===E?"bottomLeft":E,P=e.placements,C=void 0===P?F:P,x=e.getPopupContainer,N=e.showAction,R=e.hideAction,M=e.overlayClassName,O=e.overlayStyle,I=e.visible,$=e.trigger,L=void 0===$?["hover"]:$,S=e.autoFocus,T=e.overlay,W=e.children,U=e.onVisibleChange,_=(0,o.A)(e,y),j=d.useState(),D=(0,a.A)(j,2),H=D[0],z=D[1],K="visible"in e?I:H,q=d.useRef(null),B=d.useRef(null),J=d.useRef(null);d.useImperativeHandle(t,(function(){return q.current}));var Y=function(e){z(e),null==U||U(e)};!function(e){var t=e.visible,n=e.triggerRef,r=e.onVisibleChange,i=e.autoFocus,a=e.overlayRef,o=d.useRef(!1),s=function(){var e,i;t&&(null===(e=n.current)||void 0===e||null===(i=e.focus)||void 0===i||i.call(e),null==r||r(!1))},u=function(){var e;return!(null===(e=a.current)||void 0===e||!e.focus||(a.current.focus(),o.current=!0,0))},l=function(e){switch(e.keyCode){case g:s();break;case h:var t=!1;o.current||(t=u()),t?e.preventDefault():s()}};d.useEffect((function(){return t?(window.addEventListener("keydown",l),i&&(0,v.A)(u,3),function(){window.removeEventListener("keydown",l),o.current=!1}):function(){o.current=!1}}),[t])}({visible:K,triggerRef:J,onVisibleChange:Y,autoFocus:S,overlayRef:B});var G,X,Q,Z=function(){return d.createElement(p,{ref:B,overlay:T,prefixCls:A,arrow:f})},ee=d.cloneElement(W,{className:l()(null===(n=W.props)||void 0===n?void 0:n.className,K&&(G=e.openClassName,void 0!==G?G:"".concat(A,"-open"))),ref:(0,c.f3)(W)?(0,c.K4)(J,(0,c.A9)(W)):void 0}),te=R;return te||-1===L.indexOf("contextMenu")||(te=["click"]),d.createElement(s.A,(0,r.A)({builtinPlacements:C},_,{prefixCls:A,ref:q,popupClassName:l()(M,(0,i.A)({},"".concat(A,"-show-arrow"),f)),popupStyle:O,action:L,showAction:N,hideAction:te,popupPlacement:k,popupAlign:V,popupTransitionName:b,popupAnimation:w,popupVisible:K,stretch:(X=e.minOverlayWidthMatchTrigger,Q=e.alignPoint,("minOverlayWidthMatchTrigger"in e?X:!Q)?"minWidth":""),popup:"function"==typeof T?Z:Z(),onPopupVisibleChange:Y,onPopupClick:function(t){var n=e.onOverlayClick;z(!1),n&&n(t)},getPopupContainer:x}),ee)}const w=d.forwardRef(b)},11523:(e,t,n)=>{n.d(t,{D0:()=>J,_z:()=>b,Op:()=>ie,B8:()=>Y,EF:()=>w,Ay:()=>de,mN:()=>ne,FH:()=>le});var r=n(96540),i=n(58168),a=n(53986),o=n(90675),s=n(10467),u=n(89379),l=n(60436),c=n(23029),d=n(92901),f=n(9417),v=n(85501),g=n(29426),h=n(64467),p=n(82546),m=n(43210),A=n(68210),F="RC_FORM_INTERNAL_HOOKS",y=function(){(0,A.Ay)(!1,"Can not find FormContext. Please make sure you wrap Field under Form.")};const b=r.createContext({getFieldValue:y,getFieldsValue:y,getFieldError:y,getFieldWarning:y,getFieldsError:y,isFieldsTouched:y,isFieldTouched:y,isFieldValidating:y,isFieldsValidating:y,resetFields:y,setFields:y,setFieldValue:y,setFieldsValue:y,validateFields:y,submit:y,getInternalHooks:function(){return y(),{dispatch:y,initEntityValue:y,registerField:y,useSubscribe:y,setInitialValues:y,destroyForm:y,setCallbacks:y,registerWatch:y,getFields:y,setValidateMessages:y,setPreserve:y,getInitialValue:y}}}),w=r.createContext(null);function V(e){return null==e?[]:Array.isArray(e)?e:[e]}var E=n(72568),k="'${name}' is not a valid ${type}",P={default:"Validation error on field '${name}'",required:"'${name}' is required",enum:"'${name}' must be one of [${enum}]",whitespace:"'${name}' cannot be empty",date:{format:"'${name}' is invalid for format date",parse:"'${name}' could not be parsed as date",invalid:"'${name}' is invalid date"},types:{string:k,method:k,array:k,object:k,number:k,date:k,boolean:k,integer:k,float:k,regexp:k,email:k,url:k,hex:k},string:{len:"'${name}' must be exactly ${len} characters",min:"'${name}' must be at least ${min} characters",max:"'${name}' cannot be longer than ${max} characters",range:"'${name}' must be between ${min} and ${max} characters"},number:{len:"'${name}' must equal ${len}",min:"'${name}' cannot be less than ${min}",max:"'${name}' cannot be greater than ${max}",range:"'${name}' must be between ${min} and ${max}"},array:{len:"'${name}' must be exactly ${len} in length",min:"'${name}' cannot be less than ${min} in length",max:"'${name}' cannot be greater than ${max} in length",range:"'${name}' must be between ${min} and ${max} in length"},pattern:{mismatch:"'${name}' does not match pattern ${pattern}"}},C=n(20488),x=E.A;function N(e,t){return e.replace(/\\?\$\{\w+\}/g,(function(e){if(e.startsWith("\\"))return e.slice(1);var n=e.slice(2,-1);return t[n]}))}var R="CODE_LOGIC_ERROR";function M(e,t,n,r,i){return O.apply(this,arguments)}function O(){return O=(0,s.A)((0,o.A)().mark((function e(t,n,i,a,s){var c,d,f,v,g,p,m,A,F;return(0,o.A)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return delete(c=(0,u.A)({},i)).ruleIndex,x.warning=function(){},c.validator&&(d=c.validator,c.validator=function(){try{return d.apply(void 0,arguments)}catch(e){return console.error(e),Promise.reject(R)}}),f=null,c&&"array"===c.type&&c.defaultField&&(f=c.defaultField,delete c.defaultField),v=new x((0,h.A)({},t,[c])),g=(0,C.h)(P,a.validateMessages),v.messages(g),p=[],e.prev=10,e.next=13,Promise.resolve(v.validate((0,h.A)({},t,n),(0,u.A)({},a)));case 13:e.next=18;break;case 15:e.prev=15,e.t0=e.catch(10),e.t0.errors&&(p=e.t0.errors.map((function(e,t){var n=e.message,i=n===R?g.default:n;return r.isValidElement(i)?r.cloneElement(i,{key:"error_".concat(t)}):i})));case 18:if(p.length||!f){e.next=23;break}return e.next=21,Promise.all(n.map((function(e,n){return M("".concat(t,".").concat(n),e,f,a,s)})));case 21:return m=e.sent,e.abrupt("return",m.reduce((function(e,t){return[].concat((0,l.A)(e),(0,l.A)(t))}),[]));case 23:return A=(0,u.A)((0,u.A)({},i),{},{name:t,enum:(i.enum||[]).join(", ")},s),F=p.map((function(e){return"string"==typeof e?N(e,A):e})),e.abrupt("return",F);case 26:case"end":return e.stop()}}),e,null,[[10,15]])}))),O.apply(this,arguments)}function I(e,t,n,r,i,a){var l,c=e.join("."),d=n.map((function(e,t){var n=e.validator,r=(0,u.A)((0,u.A)({},e),{},{ruleIndex:t});return n&&(r.validator=function(e,t,r){var i=!1,a=n(e,t,(function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];Promise.resolve().then((function(){(0,A.Ay)(!i,"Your validator function has already return a promise. `callback` will be ignored."),i||r.apply(void 0,t)}))}));i=a&&"function"==typeof a.then&&"function"==typeof a.catch,(0,A.Ay)(i,"`callback` is deprecated. Please return a promise instead."),i&&a.then((function(){r()})).catch((function(e){r(e||" ")}))}),r})).sort((function(e,t){var n=e.warningOnly,r=e.ruleIndex,i=t.warningOnly,a=t.ruleIndex;return!!n==!!i?r-a:n?1:-1}));if(!0===i)l=new Promise(function(){var e=(0,s.A)((0,o.A)().mark((function e(n,i){var s,u,l;return(0,o.A)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:s=0;case 1:if(!(s<d.length)){e.next=12;break}return u=d[s],e.next=5,M(c,t,u,r,a);case 5:if(!(l=e.sent).length){e.next=9;break}return i([{errors:l,rule:u}]),e.abrupt("return");case 9:s+=1,e.next=1;break;case 12:n([]);case 13:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}());else{var f=d.map((function(e){return M(c,t,e,r,a).then((function(t){return{errors:t,rule:e}}))}));l=(i?function(e){return L.apply(this,arguments)}(f):function(e){return $.apply(this,arguments)}(f)).then((function(e){return Promise.reject(e)}))}return l.catch((function(e){return e})),l}function $(){return($=(0,s.A)((0,o.A)().mark((function e(t){return(0,o.A)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",Promise.all(t).then((function(e){var t;return(t=[]).concat.apply(t,(0,l.A)(e))})));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function L(){return(L=(0,s.A)((0,o.A)().mark((function e(t){var n;return(0,o.A)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=0,e.abrupt("return",new Promise((function(e){t.forEach((function(r){r.then((function(r){r.errors.length&&e([r]),(n+=1)===t.length&&e([])}))}))})));case 2:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var S=n(82284),T=n(16300);function W(e){return V(e)}function U(e,t){var n={};return t.forEach((function(t){var r=(0,T.A)(e,t);n=(0,C.A)(n,t,r)})),n}function _(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return e&&e.some((function(e){return j(t,e,n)}))}function j(e,t){return!(!e||!t)&&!(!(arguments.length>2&&void 0!==arguments[2]&&arguments[2])&&e.length!==t.length)&&t.every((function(t,n){return e[n]===t}))}function D(e){var t=arguments.length<=1?void 0:arguments[1];return t&&t.target&&"object"===(0,S.A)(t.target)&&e in t.target?t.target[e]:t}function H(e,t,n){var r=e.length;if(t<0||t>=r||n<0||n>=r)return e;var i=e[t],a=t-n;return a>0?[].concat((0,l.A)(e.slice(0,n)),[i],(0,l.A)(e.slice(n,t)),(0,l.A)(e.slice(t+1,r))):a<0?[].concat((0,l.A)(e.slice(0,t)),(0,l.A)(e.slice(t+1,n+1)),[i],(0,l.A)(e.slice(n+1,r))):e}var z=["name"],K=[];function q(e,t,n,r,i,a){return"function"==typeof e?e(t,n,"source"in a?{source:a.source}:{}):r!==i}var B=function(e){(0,v.A)(n,e);var t=(0,g.A)(n);function n(e){var i;return(0,c.A)(this,n),i=t.call(this,e),(0,h.A)((0,f.A)(i),"state",{resetCount:0}),(0,h.A)((0,f.A)(i),"cancelRegisterFunc",null),(0,h.A)((0,f.A)(i),"mounted",!1),(0,h.A)((0,f.A)(i),"touched",!1),(0,h.A)((0,f.A)(i),"dirty",!1),(0,h.A)((0,f.A)(i),"validatePromise",void 0),(0,h.A)((0,f.A)(i),"prevValidating",void 0),(0,h.A)((0,f.A)(i),"errors",K),(0,h.A)((0,f.A)(i),"warnings",K),(0,h.A)((0,f.A)(i),"cancelRegister",(function(){var e=i.props,t=e.preserve,n=e.isListField,r=e.name;i.cancelRegisterFunc&&i.cancelRegisterFunc(n,t,W(r)),i.cancelRegisterFunc=null})),(0,h.A)((0,f.A)(i),"getNamePath",(function(){var e=i.props,t=e.name,n=e.fieldContext.prefixName,r=void 0===n?[]:n;return void 0!==t?[].concat((0,l.A)(r),(0,l.A)(t)):[]})),(0,h.A)((0,f.A)(i),"getRules",(function(){var e=i.props,t=e.rules,n=void 0===t?[]:t,r=e.fieldContext;return n.map((function(e){return"function"==typeof e?e(r):e}))})),(0,h.A)((0,f.A)(i),"refresh",(function(){i.mounted&&i.setState((function(e){return{resetCount:e.resetCount+1}}))})),(0,h.A)((0,f.A)(i),"metaCache",null),(0,h.A)((0,f.A)(i),"triggerMetaEvent",(function(e){var t=i.props.onMetaChange;if(t){var n=(0,u.A)((0,u.A)({},i.getMeta()),{},{destroy:e});(0,m.A)(i.metaCache,n)||t(n),i.metaCache=n}else i.metaCache=null})),(0,h.A)((0,f.A)(i),"onStoreChange",(function(e,t,n){var r=i.props,a=r.shouldUpdate,o=r.dependencies,s=void 0===o?[]:o,u=r.onReset,l=n.store,c=i.getNamePath(),d=i.getValue(e),f=i.getValue(l),v=t&&_(t,c);switch("valueUpdate"!==n.type||"external"!==n.source||(0,m.A)(d,f)||(i.touched=!0,i.dirty=!0,i.validatePromise=null,i.errors=K,i.warnings=K,i.triggerMetaEvent()),n.type){case"reset":if(!t||v)return i.touched=!1,i.dirty=!1,i.validatePromise=void 0,i.errors=K,i.warnings=K,i.triggerMetaEvent(),null==u||u(),void i.refresh();break;case"remove":if(a&&q(a,e,l,d,f,n))return void i.reRender();break;case"setField":var g=n.data;if(v)return"touched"in g&&(i.touched=g.touched),"validating"in g&&!("originRCField"in g)&&(i.validatePromise=g.validating?Promise.resolve([]):null),"errors"in g&&(i.errors=g.errors||K),"warnings"in g&&(i.warnings=g.warnings||K),i.dirty=!0,i.triggerMetaEvent(),void i.reRender();if("value"in g&&_(t,c,!0))return void i.reRender();if(a&&!c.length&&q(a,e,l,d,f,n))return void i.reRender();break;case"dependenciesUpdate":if(s.map(W).some((function(e){return _(n.relatedFields,e)})))return void i.reRender();break;default:if(v||(!s.length||c.length||a)&&q(a,e,l,d,f,n))return void i.reRender()}!0===a&&i.reRender()})),(0,h.A)((0,f.A)(i),"validateRules",(function(e){var t=i.getNamePath(),n=i.getValue(),r=e||{},a=r.triggerName,u=r.validateOnly,c=void 0!==u&&u,d=Promise.resolve().then((0,s.A)((0,o.A)().mark((function r(){var s,u,c,f,v,g,h;return(0,o.A)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(i.mounted){r.next=2;break}return r.abrupt("return",[]);case 2:if(s=i.props,u=s.validateFirst,c=void 0!==u&&u,f=s.messageVariables,v=s.validateDebounce,g=i.getRules(),a&&(g=g.filter((function(e){return e})).filter((function(e){var t=e.validateTrigger;return!t||V(t).includes(a)}))),!v||!a){r.next=10;break}return r.next=8,new Promise((function(e){setTimeout(e,v)}));case 8:if(i.validatePromise===d){r.next=10;break}return r.abrupt("return",[]);case 10:return(h=I(t,n,g,e,c,f)).catch((function(e){return e})).then((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:K;if(i.validatePromise===d){var t;i.validatePromise=null;var n=[],r=[];null===(t=e.forEach)||void 0===t||t.call(e,(function(e){var t=e.rule.warningOnly,i=e.errors,a=void 0===i?K:i;t?r.push.apply(r,(0,l.A)(a)):n.push.apply(n,(0,l.A)(a))})),i.errors=n,i.warnings=r,i.triggerMetaEvent(),i.reRender()}})),r.abrupt("return",h);case 13:case"end":return r.stop()}}),r)}))));return c||(i.validatePromise=d,i.dirty=!0,i.errors=K,i.warnings=K,i.triggerMetaEvent(),i.reRender()),d})),(0,h.A)((0,f.A)(i),"isFieldValidating",(function(){return!!i.validatePromise})),(0,h.A)((0,f.A)(i),"isFieldTouched",(function(){return i.touched})),(0,h.A)((0,f.A)(i),"isFieldDirty",(function(){return!(!i.dirty&&void 0===i.props.initialValue)||void 0!==(0,i.props.fieldContext.getInternalHooks(F).getInitialValue)(i.getNamePath())})),(0,h.A)((0,f.A)(i),"getErrors",(function(){return i.errors})),(0,h.A)((0,f.A)(i),"getWarnings",(function(){return i.warnings})),(0,h.A)((0,f.A)(i),"isListField",(function(){return i.props.isListField})),(0,h.A)((0,f.A)(i),"isList",(function(){return i.props.isList})),(0,h.A)((0,f.A)(i),"isPreserve",(function(){return i.props.preserve})),(0,h.A)((0,f.A)(i),"getMeta",(function(){return i.prevValidating=i.isFieldValidating(),{touched:i.isFieldTouched(),validating:i.prevValidating,errors:i.errors,warnings:i.warnings,name:i.getNamePath(),validated:null===i.validatePromise}})),(0,h.A)((0,f.A)(i),"getOnlyChild",(function(e){if("function"==typeof e){var t=i.getMeta();return(0,u.A)((0,u.A)({},i.getOnlyChild(e(i.getControlled(),t,i.props.fieldContext))),{},{isFunction:!0})}var n=(0,p.A)(e);return 1===n.length&&r.isValidElement(n[0])?{child:n[0],isFunction:!1}:{child:n,isFunction:!1}})),(0,h.A)((0,f.A)(i),"getValue",(function(e){var t=i.props.fieldContext.getFieldsValue,n=i.getNamePath();return(0,T.A)(e||t(!0),n)})),(0,h.A)((0,f.A)(i),"getControlled",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=i.props,n=t.name,r=t.trigger,a=t.validateTrigger,o=t.getValueFromEvent,s=t.normalize,l=t.valuePropName,c=t.getValueProps,d=t.fieldContext,f=void 0!==a?a:d.validateTrigger,v=i.getNamePath(),g=d.getInternalHooks,p=d.getFieldsValue,m=g(F).dispatch,A=i.getValue(),y=c||function(e){return(0,h.A)({},l,e)},b=e[r],w=void 0!==n?y(A):{},E=(0,u.A)((0,u.A)({},e),w);return E[r]=function(){var e;i.touched=!0,i.dirty=!0,i.triggerMetaEvent();for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];e=o?o.apply(void 0,n):D.apply(void 0,[l].concat(n)),s&&(e=s(e,A,p(!0))),e!==A&&m({type:"updateValue",namePath:v,value:e}),b&&b.apply(void 0,n)},V(f||[]).forEach((function(e){var t=E[e];E[e]=function(){t&&t.apply(void 0,arguments);var n=i.props.rules;n&&n.length&&m({type:"validateField",namePath:v,triggerName:e})}})),E})),e.fieldContext&&(0,(0,e.fieldContext.getInternalHooks)(F).initEntityValue)((0,f.A)(i)),i}return(0,d.A)(n,[{key:"componentDidMount",value:function(){var e=this.props,t=e.shouldUpdate,n=e.fieldContext;if(this.mounted=!0,n){var r=(0,n.getInternalHooks)(F).registerField;this.cancelRegisterFunc=r(this)}!0===t&&this.reRender()}},{key:"componentWillUnmount",value:function(){this.cancelRegister(),this.triggerMetaEvent(!0),this.mounted=!1}},{key:"reRender",value:function(){this.mounted&&this.forceUpdate()}},{key:"render",value:function(){var e,t=this.state.resetCount,n=this.props.children,i=this.getOnlyChild(n),a=i.child;return i.isFunction?e=a:r.isValidElement(a)?e=r.cloneElement(a,this.getControlled(a.props)):((0,A.Ay)(!a,"`children` of Field is not validate ReactElement."),e=a),r.createElement(r.Fragment,{key:t},e)}}]),n}(r.Component);(0,h.A)(B,"contextType",b),(0,h.A)(B,"defaultProps",{trigger:"onChange",valuePropName:"value"});const J=function(e){var t,n=e.name,o=(0,a.A)(e,z),s=r.useContext(b),u=r.useContext(w),l=void 0!==n?W(n):void 0,c=null!==(t=o.isListField)&&void 0!==t?t:!!u,d="keep";return c||(d="_".concat((l||[]).join("_"))),r.createElement(B,(0,i.A)({key:d,name:l,isListField:c},o,{fieldContext:s}))},Y=function(e){var t=e.name,n=e.initialValue,i=e.children,a=e.rules,o=e.validateTrigger,s=e.isListField,c=r.useContext(b),d=r.useContext(w),f=r.useRef({keys:[],id:0}).current,v=r.useMemo((function(){var e=W(c.prefixName)||[];return[].concat((0,l.A)(e),(0,l.A)(W(t)))}),[c.prefixName,t]),g=r.useMemo((function(){return(0,u.A)((0,u.A)({},c),{},{prefixName:v})}),[c,v]),h=r.useMemo((function(){return{getKey:function(e){var t=v.length,n=e[t];return[f.keys[n],e.slice(t+1)]}}}),[v]);return"function"!=typeof i?((0,A.Ay)(!1,"Form.List only accepts function as children."),null):r.createElement(w.Provider,{value:h},r.createElement(b.Provider,{value:g},r.createElement(J,{name:[],shouldUpdate:function(e,t,n){return"internal"!==n.source&&e!==t},rules:a,validateTrigger:o,initialValue:n,isList:!0,isListField:null!=s?s:!!d},(function(e,t){var n=e.value,r=void 0===n?[]:n,a=e.onChange,o=c.getFieldValue,s=function(){return o(v||[])||[]},u={add:function(e,t){var n=s();t>=0&&t<=n.length?(f.keys=[].concat((0,l.A)(f.keys.slice(0,t)),[f.id],(0,l.A)(f.keys.slice(t))),a([].concat((0,l.A)(n.slice(0,t)),[e],(0,l.A)(n.slice(t))))):(f.keys=[].concat((0,l.A)(f.keys),[f.id]),a([].concat((0,l.A)(n),[e]))),f.id+=1},remove:function(e){var t=s(),n=new Set(Array.isArray(e)?e:[e]);n.size<=0||(f.keys=f.keys.filter((function(e,t){return!n.has(t)})),a(t.filter((function(e,t){return!n.has(t)}))))},move:function(e,t){if(e!==t){var n=s();e<0||e>=n.length||t<0||t>=n.length||(f.keys=H(f.keys,e,t),a(H(n,e,t)))}}},d=r||[];return Array.isArray(d)||(d=[]),i(d.map((function(e,t){var n=f.keys[t];return void 0===n&&(f.keys[t]=f.id,n=f.keys[t],f.id+=1),{name:t,key:n,isListField:!0}})),u,t)}))))};var G=n(5544),X="__@field_split__";function Q(e){return e.map((function(e){return"".concat((0,S.A)(e),":").concat(e)})).join(X)}const Z=function(){function e(){(0,c.A)(this,e),(0,h.A)(this,"kvs",new Map)}return(0,d.A)(e,[{key:"set",value:function(e,t){this.kvs.set(Q(e),t)}},{key:"get",value:function(e){return this.kvs.get(Q(e))}},{key:"update",value:function(e,t){var n=t(this.get(e));n?this.set(e,n):this.delete(e)}},{key:"delete",value:function(e){this.kvs.delete(Q(e))}},{key:"map",value:function(e){return(0,l.A)(this.kvs.entries()).map((function(t){var n=(0,G.A)(t,2),r=n[0],i=n[1],a=r.split(X);return e({key:a.map((function(e){var t=e.match(/^([^:]*):(.*)$/),n=(0,G.A)(t,3),r=n[1],i=n[2];return"number"===r?Number(i):i})),value:i})}))}},{key:"toJSON",value:function(){var e={};return this.map((function(t){var n=t.key,r=t.value;return e[n.join(".")]=r,null})),e}}]),e}();var ee=["name"],te=(0,d.A)((function e(t){var n=this;(0,c.A)(this,e),(0,h.A)(this,"formHooked",!1),(0,h.A)(this,"forceRootUpdate",void 0),(0,h.A)(this,"subscribable",!0),(0,h.A)(this,"store",{}),(0,h.A)(this,"fieldEntities",[]),(0,h.A)(this,"initialValues",{}),(0,h.A)(this,"callbacks",{}),(0,h.A)(this,"validateMessages",null),(0,h.A)(this,"preserve",null),(0,h.A)(this,"lastValidatePromise",null),(0,h.A)(this,"getForm",(function(){return{getFieldValue:n.getFieldValue,getFieldsValue:n.getFieldsValue,getFieldError:n.getFieldError,getFieldWarning:n.getFieldWarning,getFieldsError:n.getFieldsError,isFieldsTouched:n.isFieldsTouched,isFieldTouched:n.isFieldTouched,isFieldValidating:n.isFieldValidating,isFieldsValidating:n.isFieldsValidating,resetFields:n.resetFields,setFields:n.setFields,setFieldValue:n.setFieldValue,setFieldsValue:n.setFieldsValue,validateFields:n.validateFields,submit:n.submit,_init:!0,getInternalHooks:n.getInternalHooks}})),(0,h.A)(this,"getInternalHooks",(function(e){return e===F?(n.formHooked=!0,{dispatch:n.dispatch,initEntityValue:n.initEntityValue,registerField:n.registerField,useSubscribe:n.useSubscribe,setInitialValues:n.setInitialValues,destroyForm:n.destroyForm,setCallbacks:n.setCallbacks,setValidateMessages:n.setValidateMessages,getFields:n.getFields,setPreserve:n.setPreserve,getInitialValue:n.getInitialValue,registerWatch:n.registerWatch}):((0,A.Ay)(!1,"`getInternalHooks` is internal usage. Should not call directly."),null)})),(0,h.A)(this,"useSubscribe",(function(e){n.subscribable=e})),(0,h.A)(this,"prevWithoutPreserves",null),(0,h.A)(this,"setInitialValues",(function(e,t){if(n.initialValues=e||{},t){var r,i=(0,C.h)(e,n.store);null===(r=n.prevWithoutPreserves)||void 0===r||r.map((function(t){var n=t.key;i=(0,C.A)(i,n,(0,T.A)(e,n))})),n.prevWithoutPreserves=null,n.updateStore(i)}})),(0,h.A)(this,"destroyForm",(function(e){if(e)n.updateStore({});else{var t=new Z;n.getFieldEntities(!0).forEach((function(e){n.isMergedPreserve(e.isPreserve())||t.set(e.getNamePath(),!0)})),n.prevWithoutPreserves=t}})),(0,h.A)(this,"getInitialValue",(function(e){var t=(0,T.A)(n.initialValues,e);return e.length?(0,C.h)(t):t})),(0,h.A)(this,"setCallbacks",(function(e){n.callbacks=e})),(0,h.A)(this,"setValidateMessages",(function(e){n.validateMessages=e})),(0,h.A)(this,"setPreserve",(function(e){n.preserve=e})),(0,h.A)(this,"watchList",[]),(0,h.A)(this,"registerWatch",(function(e){return n.watchList.push(e),function(){n.watchList=n.watchList.filter((function(t){return t!==e}))}})),(0,h.A)(this,"notifyWatch",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];if(n.watchList.length){var t=n.getFieldsValue(),r=n.getFieldsValue(!0);n.watchList.forEach((function(n){n(t,r,e)}))}})),(0,h.A)(this,"timeoutId",null),(0,h.A)(this,"warningUnhooked",(function(){})),(0,h.A)(this,"updateStore",(function(e){n.store=e})),(0,h.A)(this,"getFieldEntities",(function(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?n.fieldEntities.filter((function(e){return e.getNamePath().length})):n.fieldEntities})),(0,h.A)(this,"getFieldsMap",(function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=new Z;return n.getFieldEntities(e).forEach((function(e){var n=e.getNamePath();t.set(n,e)})),t})),(0,h.A)(this,"getFieldEntitiesForNamePathList",(function(e){if(!e)return n.getFieldEntities(!0);var t=n.getFieldsMap(!0);return e.map((function(e){var n=W(e);return t.get(n)||{INVALIDATE_NAME_PATH:W(e)}}))})),(0,h.A)(this,"getFieldsValue",(function(e,t){var r,i,a;if(n.warningUnhooked(),!0===e||Array.isArray(e)?(r=e,i=t):e&&"object"===(0,S.A)(e)&&(a=e.strict,i=e.filter),!0===r&&!i)return n.store;var o=n.getFieldEntitiesForNamePathList(Array.isArray(r)?r:null),s=[];return o.forEach((function(e){var t,n,o,u,l="INVALIDATE_NAME_PATH"in e?e.INVALIDATE_NAME_PATH:e.getNamePath();if(a){if(null!==(o=(u=e).isList)&&void 0!==o&&o.call(u))return}else if(!r&&null!==(t=(n=e).isListField)&&void 0!==t&&t.call(n))return;if(i){var c="getMeta"in e?e.getMeta():null;i(c)&&s.push(l)}else s.push(l)})),U(n.store,s.map(W))})),(0,h.A)(this,"getFieldValue",(function(e){n.warningUnhooked();var t=W(e);return(0,T.A)(n.store,t)})),(0,h.A)(this,"getFieldsError",(function(e){return n.warningUnhooked(),n.getFieldEntitiesForNamePathList(e).map((function(t,n){return t&&!("INVALIDATE_NAME_PATH"in t)?{name:t.getNamePath(),errors:t.getErrors(),warnings:t.getWarnings()}:{name:W(e[n]),errors:[],warnings:[]}}))})),(0,h.A)(this,"getFieldError",(function(e){n.warningUnhooked();var t=W(e);return n.getFieldsError([t])[0].errors})),(0,h.A)(this,"getFieldWarning",(function(e){n.warningUnhooked();var t=W(e);return n.getFieldsError([t])[0].warnings})),(0,h.A)(this,"isFieldsTouched",(function(){n.warningUnhooked();for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var i,a=t[0],o=t[1],s=!1;0===t.length?i=null:1===t.length?Array.isArray(a)?(i=a.map(W),s=!1):(i=null,s=a):(i=a.map(W),s=o);var u=n.getFieldEntities(!0),c=function(e){return e.isFieldTouched()};if(!i)return s?u.every((function(e){return c(e)||e.isList()})):u.some(c);var d=new Z;i.forEach((function(e){d.set(e,[])})),u.forEach((function(e){var t=e.getNamePath();i.forEach((function(n){n.every((function(e,n){return t[n]===e}))&&d.update(n,(function(t){return[].concat((0,l.A)(t),[e])}))}))}));var f=function(e){return e.some(c)},v=d.map((function(e){return e.value}));return s?v.every(f):v.some(f)})),(0,h.A)(this,"isFieldTouched",(function(e){return n.warningUnhooked(),n.isFieldsTouched([e])})),(0,h.A)(this,"isFieldsValidating",(function(e){n.warningUnhooked();var t=n.getFieldEntities();if(!e)return t.some((function(e){return e.isFieldValidating()}));var r=e.map(W);return t.some((function(e){var t=e.getNamePath();return _(r,t)&&e.isFieldValidating()}))})),(0,h.A)(this,"isFieldValidating",(function(e){return n.warningUnhooked(),n.isFieldsValidating([e])})),(0,h.A)(this,"resetWithFieldInitialValue",(function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=new Z,i=n.getFieldEntities(!0);i.forEach((function(e){var t=e.props.initialValue,n=e.getNamePath();if(void 0!==t){var i=r.get(n)||new Set;i.add({entity:e,value:t}),r.set(n,i)}})),t.entities?e=t.entities:t.namePathList?(e=[],t.namePathList.forEach((function(t){var n,i=r.get(t);i&&(n=e).push.apply(n,(0,l.A)((0,l.A)(i).map((function(e){return e.entity}))))}))):e=i,e.forEach((function(e){if(void 0!==e.props.initialValue){var i=e.getNamePath();if(void 0!==n.getInitialValue(i))(0,A.Ay)(!1,"Form already set 'initialValues' with path '".concat(i.join("."),"'. Field can not overwrite it."));else{var a=r.get(i);if(a&&a.size>1)(0,A.Ay)(!1,"Multiple Field with path '".concat(i.join("."),"' set 'initialValue'. Can not decide which one to pick."));else if(a){var o=n.getFieldValue(i);e.isListField()||t.skipExist&&void 0!==o||n.updateStore((0,C.A)(n.store,i,(0,l.A)(a)[0].value))}}}}))})),(0,h.A)(this,"resetFields",(function(e){n.warningUnhooked();var t=n.store;if(!e)return n.updateStore((0,C.h)(n.initialValues)),n.resetWithFieldInitialValue(),n.notifyObservers(t,null,{type:"reset"}),void n.notifyWatch();var r=e.map(W);r.forEach((function(e){var t=n.getInitialValue(e);n.updateStore((0,C.A)(n.store,e,t))})),n.resetWithFieldInitialValue({namePathList:r}),n.notifyObservers(t,r,{type:"reset"}),n.notifyWatch(r)})),(0,h.A)(this,"setFields",(function(e){n.warningUnhooked();var t=n.store,r=[];e.forEach((function(e){var i=e.name,o=(0,a.A)(e,ee),s=W(i);r.push(s),"value"in o&&n.updateStore((0,C.A)(n.store,s,o.value)),n.notifyObservers(t,[s],{type:"setField",data:e})})),n.notifyWatch(r)})),(0,h.A)(this,"getFields",(function(){return n.getFieldEntities(!0).map((function(e){var t=e.getNamePath(),r=e.getMeta(),i=(0,u.A)((0,u.A)({},r),{},{name:t,value:n.getFieldValue(t)});return Object.defineProperty(i,"originRCField",{value:!0}),i}))})),(0,h.A)(this,"initEntityValue",(function(e){var t=e.props.initialValue;if(void 0!==t){var r=e.getNamePath();void 0===(0,T.A)(n.store,r)&&n.updateStore((0,C.A)(n.store,r,t))}})),(0,h.A)(this,"isMergedPreserve",(function(e){var t=void 0!==e?e:n.preserve;return null==t||t})),(0,h.A)(this,"registerField",(function(e){n.fieldEntities.push(e);var t=e.getNamePath();if(n.notifyWatch([t]),void 0!==e.props.initialValue){var r=n.store;n.resetWithFieldInitialValue({entities:[e],skipExist:!0}),n.notifyObservers(r,[e.getNamePath()],{type:"valueUpdate",source:"internal"})}return function(r,i){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];if(n.fieldEntities=n.fieldEntities.filter((function(t){return t!==e})),!n.isMergedPreserve(i)&&(!r||a.length>1)){var o=r?void 0:n.getInitialValue(t);if(t.length&&n.getFieldValue(t)!==o&&n.fieldEntities.every((function(e){return!j(e.getNamePath(),t)}))){var s=n.store;n.updateStore((0,C.A)(s,t,o,!0)),n.notifyObservers(s,[t],{type:"remove"}),n.triggerDependenciesUpdate(s,t)}}n.notifyWatch([t])}})),(0,h.A)(this,"dispatch",(function(e){switch(e.type){case"updateValue":var t=e.namePath,r=e.value;n.updateValue(t,r);break;case"validateField":var i=e.namePath,a=e.triggerName;n.validateFields([i],{triggerName:a})}})),(0,h.A)(this,"notifyObservers",(function(e,t,r){if(n.subscribable){var i=(0,u.A)((0,u.A)({},r),{},{store:n.getFieldsValue(!0)});n.getFieldEntities().forEach((function(n){(0,n.onStoreChange)(e,t,i)}))}else n.forceRootUpdate()})),(0,h.A)(this,"triggerDependenciesUpdate",(function(e,t){var r=n.getDependencyChildrenFields(t);return r.length&&n.validateFields(r),n.notifyObservers(e,r,{type:"dependenciesUpdate",relatedFields:[t].concat((0,l.A)(r))}),r})),(0,h.A)(this,"updateValue",(function(e,t){var r=W(e),i=n.store;n.updateStore((0,C.A)(n.store,r,t)),n.notifyObservers(i,[r],{type:"valueUpdate",source:"internal"}),n.notifyWatch([r]);var a=n.triggerDependenciesUpdate(i,r),o=n.callbacks.onValuesChange;o&&o(U(n.store,[r]),n.getFieldsValue()),n.triggerOnFieldsChange([r].concat((0,l.A)(a)))})),(0,h.A)(this,"setFieldsValue",(function(e){n.warningUnhooked();var t=n.store;if(e){var r=(0,C.h)(n.store,e);n.updateStore(r)}n.notifyObservers(t,null,{type:"valueUpdate",source:"external"}),n.notifyWatch()})),(0,h.A)(this,"setFieldValue",(function(e,t){n.setFields([{name:e,value:t,errors:[],warnings:[]}])})),(0,h.A)(this,"getDependencyChildrenFields",(function(e){var t=new Set,r=[],i=new Z;return n.getFieldEntities().forEach((function(e){(e.props.dependencies||[]).forEach((function(t){var n=W(t);i.update(n,(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Set;return t.add(e),t}))}))})),function e(n){(i.get(n)||new Set).forEach((function(n){if(!t.has(n)){t.add(n);var i=n.getNamePath();n.isFieldDirty()&&i.length&&(r.push(i),e(i))}}))}(e),r})),(0,h.A)(this,"triggerOnFieldsChange",(function(e,t){var r=n.callbacks.onFieldsChange;if(r){var i=n.getFields();if(t){var a=new Z;t.forEach((function(e){var t=e.name,n=e.errors;a.set(t,n)})),i.forEach((function(e){e.errors=a.get(e.name)||e.errors}))}var o=i.filter((function(t){var n=t.name;return _(e,n)}));o.length&&r(o,i)}})),(0,h.A)(this,"validateFields",(function(e,t){var r,i;n.warningUnhooked(),Array.isArray(e)||"string"==typeof e||"string"==typeof t?(r=e,i=t):i=e;var a=!!r,o=a?r.map(W):[],s=[],c=String(Date.now()),d=new Set,f=i||{},v=f.recursive,g=f.dirty;n.getFieldEntities(!0).forEach((function(e){if(a||o.push(e.getNamePath()),e.props.rules&&e.props.rules.length&&(!g||e.isFieldDirty())){var t=e.getNamePath();if(d.add(t.join(c)),!a||_(o,t,v)){var r=e.validateRules((0,u.A)({validateMessages:(0,u.A)((0,u.A)({},P),n.validateMessages)},i));s.push(r.then((function(){return{name:t,errors:[],warnings:[]}})).catch((function(e){var n,r=[],i=[];return null===(n=e.forEach)||void 0===n||n.call(e,(function(e){var t=e.rule.warningOnly,n=e.errors;t?i.push.apply(i,(0,l.A)(n)):r.push.apply(r,(0,l.A)(n))})),r.length?Promise.reject({name:t,errors:r,warnings:i}):{name:t,errors:r,warnings:i}})))}}}));var h=function(e){var t=!1,n=e.length,r=[];return e.length?new Promise((function(i,a){e.forEach((function(e,o){e.catch((function(e){return t=!0,e})).then((function(e){n-=1,r[o]=e,n>0||(t&&a(r),i(r))}))}))})):Promise.resolve([])}(s);n.lastValidatePromise=h,h.catch((function(e){return e})).then((function(e){var t=e.map((function(e){return e.name}));n.notifyObservers(n.store,t,{type:"validateFinish"}),n.triggerOnFieldsChange(t,e)}));var p=h.then((function(){return n.lastValidatePromise===h?Promise.resolve(n.getFieldsValue(o)):Promise.reject([])})).catch((function(e){var t=e.filter((function(e){return e&&e.errors.length}));return Promise.reject({values:n.getFieldsValue(o),errorFields:t,outOfDate:n.lastValidatePromise!==h})}));p.catch((function(e){return e}));var m=o.filter((function(e){return d.has(e.join(c))}));return n.triggerOnFieldsChange(m),p})),(0,h.A)(this,"submit",(function(){n.warningUnhooked(),n.validateFields().then((function(e){var t=n.callbacks.onFinish;if(t)try{t(e)}catch(e){console.error(e)}})).catch((function(e){var t=n.callbacks.onFinishFailed;t&&t(e)}))})),this.forceRootUpdate=t}));const ne=function(e){var t=r.useRef(),n=r.useState({}),i=(0,G.A)(n,2)[1];if(!t.current)if(e)t.current=e;else{var a=new te((function(){i({})}));t.current=a.getForm()}return[t.current]};var re=r.createContext({triggerFormChange:function(){},triggerFormFinish:function(){},registerForm:function(){},unregisterForm:function(){}}),ie=function(e){var t=e.validateMessages,n=e.onFormChange,i=e.onFormFinish,a=e.children,o=r.useContext(re),s=r.useRef({});return r.createElement(re.Provider,{value:(0,u.A)((0,u.A)({},o),{},{validateMessages:(0,u.A)((0,u.A)({},o.validateMessages),t),triggerFormChange:function(e,t){n&&n(e,{changedFields:t,forms:s.current}),o.triggerFormChange(e,t)},triggerFormFinish:function(e,t){i&&i(e,{values:t,forms:s.current}),o.triggerFormFinish(e,t)},registerForm:function(e,t){e&&(s.current=(0,u.A)((0,u.A)({},s.current),{},(0,h.A)({},e,t))),o.registerForm(e,t)},unregisterForm:function(e){var t=(0,u.A)({},s.current);delete t[e],s.current=t,o.unregisterForm(e)}})},a)};const ae=re;var oe=["name","initialValues","fields","form","preserve","children","component","validateMessages","validateTrigger","onValuesChange","onFieldsChange","onFinish","onFinishFailed","clearOnDestroy"];const se=function(e,t){var n=e.name,o=e.initialValues,s=e.fields,c=e.form,d=e.preserve,f=e.children,v=e.component,g=void 0===v?"form":v,h=e.validateMessages,p=e.validateTrigger,m=void 0===p?"onChange":p,A=e.onValuesChange,y=e.onFieldsChange,V=e.onFinish,E=e.onFinishFailed,k=e.clearOnDestroy,P=(0,a.A)(e,oe),C=r.useRef(null),x=r.useContext(ae),N=ne(c),R=(0,G.A)(N,1)[0],M=R.getInternalHooks(F),O=M.useSubscribe,I=M.setInitialValues,$=M.setCallbacks,L=M.setValidateMessages,T=M.setPreserve,W=M.destroyForm;r.useImperativeHandle(t,(function(){return(0,u.A)((0,u.A)({},R),{},{nativeElement:C.current})})),r.useEffect((function(){return x.registerForm(n,R),function(){x.unregisterForm(n)}}),[x,R,n]),L((0,u.A)((0,u.A)({},x.validateMessages),h)),$({onValuesChange:A,onFieldsChange:function(e){if(x.triggerFormChange(n,e),y){for(var t=arguments.length,r=new Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];y.apply(void 0,[e].concat(r))}},onFinish:function(e){x.triggerFormFinish(n,e),V&&V(e)},onFinishFailed:E}),T(d);var U,_=r.useRef(null);I(o,!_.current),_.current||(_.current=!0),r.useEffect((function(){return function(){return W(k)}}),[]);var j="function"==typeof f;U=j?f(R.getFieldsValue(!0),R):f,O(!j);var D=r.useRef();r.useEffect((function(){(function(e,t){if(e===t)return!0;if(!e&&t||e&&!t)return!1;if(!e||!t||"object"!==(0,S.A)(e)||"object"!==(0,S.A)(t))return!1;var n=Object.keys(e),r=Object.keys(t),i=new Set([].concat(n,r));return(0,l.A)(i).every((function(n){var r=e[n],i=t[n];return"function"==typeof r&&"function"==typeof i||r===i}))})(D.current||[],s||[])||R.setFields(s||[]),D.current=s}),[s,R]);var H=r.useMemo((function(){return(0,u.A)((0,u.A)({},R),{},{validateTrigger:m})}),[R,m]),z=r.createElement(w.Provider,{value:null},r.createElement(b.Provider,{value:H},U));return!1===g?z:r.createElement(g,(0,i.A)({},P,{ref:C,onSubmit:function(e){e.preventDefault(),e.stopPropagation(),R.submit()},onReset:function(e){var t;e.preventDefault(),R.resetFields(),null===(t=P.onReset)||void 0===t||t.call(P,e)}}),z)};function ue(e){try{return JSON.stringify(e)}catch(e){return Math.random()}}const le=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=t[0],a=t[1],o=void 0===a?{}:a,s=function(e){return e&&!!e._init}(o)?{form:o}:o,u=s.form,l=(0,r.useState)(),c=(0,G.A)(l,2),d=c[0],f=c[1],v=(0,r.useMemo)((function(){return ue(d)}),[d]),g=(0,r.useRef)(v);g.current=v;var h=(0,r.useContext)(b),p=u||h,m=p&&p._init,A=W(i),y=(0,r.useRef)(A);return y.current=A,(0,r.useEffect)((function(){if(m){var e=p.getFieldsValue,t=(0,p.getInternalHooks)(F).registerWatch,n=function(e,t){var n=s.preserve?t:e;return"function"==typeof i?i(n):(0,T.A)(n,y.current)},r=t((function(e,t){var r=n(e,t),i=ue(r);g.current!==i&&(g.current=i,f(r))})),a=n(e(),e(!0));return d!==a&&f(a),r}}),[m]),d};var ce=r.forwardRef(se);ce.FormProvider=ie,ce.Field=J,ce.List=Y,ce.useForm=ne,ce.useWatch=le;const de=ce}}]);