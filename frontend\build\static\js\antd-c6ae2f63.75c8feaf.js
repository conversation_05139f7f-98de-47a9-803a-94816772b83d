"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[5351],{14980:(t,e,n)=>{n.d(e,{b:()=>r});const i=t=>({animationDuration:t,animationFillMode:"both"}),o=t=>({animationDuration:t,animationFillMode:"both"}),r=(t,e,n,r,a=!1)=>{const l=a?"&":"";return{[`\n      ${l}${t}-enter,\n      ${l}${t}-appear\n    `]:Object.assign(Object.assign({},i(r)),{animationPlayState:"paused"}),[`${l}${t}-leave`]:Object.assign(Object.assign({},o(r)),{animationPlayState:"paused"}),[`\n      ${l}${t}-enter${t}-enter-active,\n      ${l}${t}-appear${t}-appear-active\n    `]:{animationName:e,animationPlayState:"running"},[`${l}${t}-leave${t}-leave-active`]:{animationName:n,animationPlayState:"running",pointerEvents:"none"}}}},20791:(t,e,n)=>{n.d(e,{j:()=>r,n:()=>o});var i=n(36891);function o(t){const{sizePopupArrow:e,borderRadiusXS:n,borderRadiusOuter:i}=t,o=e/2,r=o,a=1*i/Math.sqrt(2),l=o-i*(1-1/Math.sqrt(2)),s=o-n*(1/Math.sqrt(2)),c=i*(Math.sqrt(2)-1)+n*(1/Math.sqrt(2)),m=2*o-s,d=c,p=2*o-a,u=l,g=2*o-0,f=r,$=o*Math.sqrt(2)+i*(Math.sqrt(2)-2),h=i*(Math.sqrt(2)-1);return{arrowShadowWidth:$,arrowPath:`path('M 0 ${r} A ${i} ${i} 0 0 0 ${a} ${l} L ${s} ${c} A ${n} ${n} 0 0 1 ${m} ${d} L ${p} ${u} A ${i} ${i} 0 0 0 ${g} ${f} Z')`,arrowPolygon:`polygon(${h}px 100%, 50% ${h}px, ${2*o-h}px 100%, ${h}px 100%)`}}const r=(t,e,n)=>{const{sizePopupArrow:o,arrowPolygon:r,arrowPath:a,arrowShadowWidth:l,borderRadiusXS:s,calc:c}=t;return{pointerEvents:"none",width:o,height:o,overflow:"hidden","&::before":{position:"absolute",bottom:0,insetInlineStart:0,width:o,height:c(o).div(2).equal(),background:e,clipPath:{_multi_value_:!0,value:[r,a]},content:'""'},"&::after":{content:'""',position:"absolute",width:l,height:l,bottom:0,insetInline:0,margin:"auto",borderRadius:{_skip_check_:!0,value:`0 0 ${(0,i.zA)(s)} 0`},transform:"translateY(50%) rotate(-135deg)",boxShadow:n,zIndex:0,background:"transparent"}}}},25905:(t,e,n)=>{n.d(e,{K8:()=>d,L9:()=>o,Nk:()=>a,Y1:()=>u,av:()=>s,dF:()=>r,jk:()=>m,jz:()=>p,t6:()=>l,vj:()=>c});var i=n(36891);const o={overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"},r=(t,e=!1)=>({boxSizing:"border-box",margin:0,padding:0,color:t.colorText,fontSize:t.fontSize,lineHeight:t.lineHeight,listStyle:"none",fontFamily:e?"inherit":t.fontFamily}),a=()=>({display:"inline-flex",alignItems:"center",color:"inherit",fontStyle:"normal",lineHeight:0,textAlign:"center",textTransform:"none",verticalAlign:"-0.125em",textRendering:"optimizeLegibility","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale","> *":{lineHeight:1},svg:{display:"inline-block"}}),l=()=>({"&::before":{display:"table",content:'""'},"&::after":{display:"table",clear:"both",content:'""'}}),s=t=>({a:{color:t.colorLink,textDecoration:t.linkDecoration,backgroundColor:"transparent",outline:"none",cursor:"pointer",transition:`color ${t.motionDurationSlow}`,"-webkit-text-decoration-skip":"objects","&:hover":{color:t.colorLinkHover},"&:active":{color:t.colorLinkActive},"&:active, &:hover":{textDecoration:t.linkHoverDecoration,outline:0},"&:focus":{textDecoration:t.linkFocusDecoration,outline:0},"&[disabled]":{color:t.colorTextDisabled,cursor:"not-allowed"}}}),c=(t,e,n,i)=>{const o=`[class^="${e}"], [class*=" ${e}"]`,r=n?`.${n}`:o,a={boxSizing:"border-box","&::before, &::after":{boxSizing:"border-box"}};let l={};return!1!==i&&(l={fontFamily:t.fontFamily,fontSize:t.fontSize}),{[r]:Object.assign(Object.assign(Object.assign({},l),a),{[o]:a})}},m=(t,e)=>({outline:`${(0,i.zA)(t.lineWidthFocus)} solid ${t.colorPrimaryBorder}`,outlineOffset:null!=e?e:1,transition:"outline-offset 0s, outline 0s"}),d=(t,e)=>({"&:focus-visible":Object.assign({},m(t,e))}),p=t=>({[`.${t}`]:Object.assign(Object.assign({},a()),{[`.${t} .${t}-icon`]:{display:"block"}})}),u=t=>Object.assign(Object.assign({color:t.colorLink,textDecoration:t.linkDecoration,outline:"none",cursor:"pointer",transition:`all ${t.motionDurationSlow}`,border:0,padding:0,background:"none",userSelect:"none"},d(t)),{"&:focus, &:hover":{color:t.colorLinkHover},"&:active":{color:t.colorLinkActive}})},28680:(t,e,n)=>{n.d(e,{p9:()=>l});var i=n(36891),o=n(14980);const r=new i.Mo("antFadeIn",{"0%":{opacity:0},"100%":{opacity:1}}),a=new i.Mo("antFadeOut",{"0%":{opacity:1},"100%":{opacity:0}}),l=(t,e=!1)=>{const{antCls:n}=t,i=`${n}-fade`,l=e?"&":"";return[(0,o.b)(i,r,a,t.motionDurationMid,e),{[`\n        ${l}${i}-enter,\n        ${l}${i}-appear\n      `]:{opacity:0,animationTimingFunction:"linear"},[`${l}${i}-leave`]:{animationTimingFunction:"linear"}}]}},37122:(t,e,n)=>{n.d(e,{A:()=>w});var i=n(96540),o=(n(18877),n(81470)),r=n(25371),a=n(40682),l=n(46942),s=n.n(l),c=n(72065),m=n(62279),d=n(97072);const p=t=>{const{value:e,formatter:n,precision:o,decimalSeparator:r,groupSeparator:a="",prefixCls:l}=t;let s;if("function"==typeof n)s=n(e);else{const t=String(e),n=t.match(/^(-?)(\d*)(\.(\d+))?$/);if(n&&"-"!==t){const t=n[1];let e=n[2]||"0",c=n[4]||"";e=e.replace(/\B(?=(\d{3})+(?!\d))/g,a),"number"==typeof o&&(c=c.padEnd(o,"0").slice(0,o>0?o:0)),c&&(c=`${r}${c}`),s=[i.createElement("span",{key:"int",className:`${l}-content-value-int`},t,e),c&&i.createElement("span",{key:"decimal",className:`${l}-content-value-decimal`},c)]}else s=t}return i.createElement("span",{className:`${l}-content-value`},s)};var u=n(25905),g=n(51113);const f=t=>{const{componentCls:e,marginXXS:n,padding:i,colorTextDescription:o,titleFontSize:r,colorTextHeading:a,contentFontSize:l,fontFamily:s}=t;return{[e]:Object.assign(Object.assign({},(0,u.dF)(t)),{[`${e}-title`]:{marginBottom:n,color:o,fontSize:r},[`${e}-skeleton`]:{paddingTop:i},[`${e}-content`]:{color:a,fontSize:l,fontFamily:s,[`${e}-content-value`]:{display:"inline-block",direction:"ltr"},[`${e}-content-prefix, ${e}-content-suffix`]:{display:"inline-block"},[`${e}-content-prefix`]:{marginInlineEnd:n},[`${e}-content-suffix`]:{marginInlineStart:n}}})}},$=(0,g.OF)("Statistic",(t=>{const e=(0,g.oX)(t,{});return[f(e)]}),(t=>{const{fontSizeHeading3:e,fontSize:n}=t;return{titleFontSize:n,contentFontSize:e}}));const h=t=>{const{prefixCls:e,className:n,rootClassName:o,style:r,valueStyle:a,value:l=0,title:u,valueRender:g,prefix:f,suffix:h,loading:b=!1,formatter:v,precision:S,decimalSeparator:y=".",groupSeparator:w=",",onMouseEnter:O,onMouseLeave:z}=t,x=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(i=Object.getOwnPropertySymbols(t);o<i.length;o++)e.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(t,i[o])&&(n[i[o]]=t[i[o]])}return n}(t,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:I,direction:C,className:M,style:k}=(0,m.TP)("statistic"),j=I("statistic",e),[E,A,T]=$(j),q=i.createElement(p,{decimalSeparator:y,groupSeparator:w,prefixCls:j,formatter:v,precision:S,value:l}),D=s()(j,{[`${j}-rtl`]:"rtl"===C},M,n,o,A,T),H=(0,c.A)(x,{aria:!0,data:!0});return E(i.createElement("div",Object.assign({},H,{className:D,style:Object.assign(Object.assign({},k),r),onMouseEnter:O,onMouseLeave:z}),u&&i.createElement("div",{className:`${j}-title`},u),i.createElement(d.A,{paragraph:!1,loading:b,className:`${j}-skeleton`},i.createElement("div",{style:a,className:`${j}-content`},f&&i.createElement("span",{className:`${j}-content-prefix`},f),g?g(q):q,h&&i.createElement("span",{className:`${j}-content-suffix`},h)))))},b=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];const v=t=>{const{value:e,format:n="HH:mm:ss",onChange:l,onFinish:s,type:c}=t,m=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(i=Object.getOwnPropertySymbols(t);o<i.length;o++)e.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(t,i[o])&&(n[i[o]]=t[i[o]])}return n}(t,["value","format","onChange","onFinish","type"]),d="countdown"===c,[p,u]=i.useState(null),g=(0,o._q)((()=>{const t=Date.now(),n=function(t){return new Date(t).getTime()}(e);return u({}),null==l||l(d?n-t:t-n),!(d&&n<t&&(null==s||s(),1))}));return i.useEffect((()=>{let t;const e=()=>{t=(0,r.A)((()=>{g()&&e()}))};return e(),()=>r.A.cancel(t)}),[e,d]),i.useEffect((()=>{u({})}),[]),i.createElement(h,Object.assign({},m,{value:e,valueRender:t=>(0,a.Ob)(t,{title:void 0}),formatter:(t,e)=>p?function(t,e,n){const{format:i=""}=e,o=new Date(t).getTime(),r=Date.now();return function(t,e){let n=t;const i=/\[[^\]]*]/g,o=(e.match(i)||[]).map((t=>t.slice(1,-1))),r=e.replace(i,"[]"),a=b.reduce(((t,[e,i])=>{if(t.includes(e)){const o=Math.floor(n/i);return n-=o*i,t.replace(new RegExp(`${e}+`,"g"),(t=>{const e=t.length;return o.toString().padStart(e,"0")}))}return t}),r);let l=0;return a.replace(i,(()=>{const t=o[l];return l+=1,t}))}(n?Math.max(o-r,0):Math.max(r-o,0),i)}(t,Object.assign(Object.assign({},e),{format:n}),d):"-"}))},S=t=>i.createElement(v,Object.assign({},t,{type:"countdown"})),y=i.memo(S);h.Timer=v,h.Countdown=y;const w=h},38328:(t,e,n)=>{n.d(e,{eG:()=>i,p9:()=>o.p9,Mh:()=>g,_j:()=>z,aB:()=>B,nP:()=>h,YU:()=>b,ox:()=>f,vR:()=>$,nF:()=>x});const i=t=>({[t.componentCls]:{[`${t.antCls}-motion-collapse-legacy`]:{overflow:"hidden","&-active":{transition:`height ${t.motionDurationMid} ${t.motionEaseInOut},\n        opacity ${t.motionDurationMid} ${t.motionEaseInOut} !important`}},[`${t.antCls}-motion-collapse`]:{overflow:"hidden",transition:`height ${t.motionDurationMid} ${t.motionEaseInOut},\n        opacity ${t.motionDurationMid} ${t.motionEaseInOut} !important`}}});var o=n(28680),r=n(36891),a=n(14980);const l=new r.Mo("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),s=new r.Mo("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),c=new r.Mo("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),m=new r.Mo("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),d=new r.Mo("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),p=new r.Mo("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),u={"move-up":{inKeyframes:new r.Mo("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),outKeyframes:new r.Mo("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}})},"move-down":{inKeyframes:l,outKeyframes:s},"move-left":{inKeyframes:c,outKeyframes:m},"move-right":{inKeyframes:d,outKeyframes:p}},g=(t,e)=>{const{antCls:n}=t,i=`${n}-${e}`,{inKeyframes:o,outKeyframes:r}=u[e];return[(0,a.b)(i,o,r,t.motionDurationMid),{[`\n        ${i}-enter,\n        ${i}-appear\n      `]:{opacity:0,animationTimingFunction:t.motionEaseOutCirc},[`${i}-leave`]:{animationTimingFunction:t.motionEaseInOutCirc}}]},f=new r.Mo("antSlideUpIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1}}),$=new r.Mo("antSlideUpOut",{"0%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0}}),h=new r.Mo("antSlideDownIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1}}),b=new r.Mo("antSlideDownOut",{"0%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0}}),v=new r.Mo("antSlideLeftIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1}}),S=new r.Mo("antSlideLeftOut",{"0%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0}}),y=new r.Mo("antSlideRightIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1}}),w=new r.Mo("antSlideRightOut",{"0%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0}}),O={"slide-up":{inKeyframes:f,outKeyframes:$},"slide-down":{inKeyframes:h,outKeyframes:b},"slide-left":{inKeyframes:v,outKeyframes:S},"slide-right":{inKeyframes:y,outKeyframes:w}},z=(t,e)=>{const{antCls:n}=t,i=`${n}-${e}`,{inKeyframes:o,outKeyframes:r}=O[e];return[(0,a.b)(i,o,r,t.motionDurationMid),{[`\n      ${i}-enter,\n      ${i}-appear\n    `]:{transform:"scale(0)",transformOrigin:"0% 0%",opacity:0,animationTimingFunction:t.motionEaseOutQuint,"&-prepare":{transform:"scale(1)"}},[`${i}-leave`]:{animationTimingFunction:t.motionEaseInQuint}}]},x=new r.Mo("antZoomIn",{"0%":{transform:"scale(0.2)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),I=new r.Mo("antZoomOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.2)",opacity:0}}),C=new r.Mo("antZoomBigIn",{"0%":{transform:"scale(0.8)",opacity:0},"100%":{transform:"scale(1)",opacity:1}}),M=new r.Mo("antZoomBigOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0.8)",opacity:0}}),k=new r.Mo("antZoomUpIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 0%"}}),j=new r.Mo("antZoomUpOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 0%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 0%",opacity:0}}),E=new r.Mo("antZoomLeftIn",{"0%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"0% 50%"}}),A=new r.Mo("antZoomLeftOut",{"0%":{transform:"scale(1)",transformOrigin:"0% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"0% 50%",opacity:0}}),T=new r.Mo("antZoomRightIn",{"0%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"100% 50%"}}),q=new r.Mo("antZoomRightOut",{"0%":{transform:"scale(1)",transformOrigin:"100% 50%"},"100%":{transform:"scale(0.8)",transformOrigin:"100% 50%",opacity:0}}),D=new r.Mo("antZoomDownIn",{"0%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0},"100%":{transform:"scale(1)",transformOrigin:"50% 100%"}}),H=new r.Mo("antZoomDownOut",{"0%":{transform:"scale(1)",transformOrigin:"50% 100%"},"100%":{transform:"scale(0.8)",transformOrigin:"50% 100%",opacity:0}}),X={zoom:{inKeyframes:x,outKeyframes:I},"zoom-big":{inKeyframes:C,outKeyframes:M},"zoom-big-fast":{inKeyframes:C,outKeyframes:M},"zoom-left":{inKeyframes:E,outKeyframes:A},"zoom-right":{inKeyframes:T,outKeyframes:q},"zoom-up":{inKeyframes:k,outKeyframes:j},"zoom-down":{inKeyframes:D,outKeyframes:H}},B=(t,e)=>{const{antCls:n}=t,i=`${n}-${e}`,{inKeyframes:o,outKeyframes:r}=X[e];return[(0,a.b)(i,o,r,"zoom-big-fast"===e?t.motionDurationFast:t.motionDurationMid),{[`\n        ${i}-enter,\n        ${i}-appear\n      `]:{transform:"scale(0)",opacity:0,animationTimingFunction:t.motionEaseOutCirc,"&-prepare":{transform:"none"}},[`${i}-leave`]:{animationTimingFunction:t.motionEaseInOutCirc}}]}},53515:(t,e,n)=>{var i=n(96540),o=n(77906),r=n(55886),a=n(46942),l=n.n(a),s=n(92387),c=n(62279),m=n(829),d=n(78551),p=n(6754),u=n(37977),g=n(36891),f=n(25905),$=n(51113);const h=t=>{const{componentCls:e,customIconTop:n,customIconSize:i,customIconFontSize:o}=t;return{[`${e}-item-custom`]:{[`> ${e}-item-container > ${e}-item-icon`]:{height:"auto",background:"none",border:0,[`> ${e}-icon`]:{top:n,width:i,height:i,fontSize:o,lineHeight:(0,g.zA)(i)}}},[`&:not(${e}-vertical)`]:{[`${e}-item-custom`]:{[`${e}-item-icon`]:{width:"auto",background:"none"}}}}},b=t=>{const{componentCls:e}=t,n=`${e}-item`;return{[`${e}-horizontal`]:{[`${n}-tail`]:{transform:"translateY(-50%)"}}}},v=t=>{const{componentCls:e,inlineDotSize:n,inlineTitleColor:i,inlineTailColor:o}=t,r=t.calc(t.paddingXS).add(t.lineWidth).equal(),a={[`${e}-item-container ${e}-item-content ${e}-item-title`]:{color:i}};return{[`&${e}-inline`]:{width:"auto",display:"inline-flex",[`${e}-item`]:{flex:"none","&-container":{padding:`${(0,g.zA)(r)} ${(0,g.zA)(t.paddingXXS)} 0`,margin:`0 ${(0,g.zA)(t.calc(t.marginXXS).div(2).equal())}`,borderRadius:t.borderRadiusSM,cursor:"pointer",transition:`background-color ${t.motionDurationMid}`,"&:hover":{background:t.controlItemBgHover},"&[role='button']:hover":{opacity:1}},"&-icon":{width:n,height:n,marginInlineStart:`calc(50% - ${(0,g.zA)(t.calc(n).div(2).equal())})`,[`> ${e}-icon`]:{top:0},[`${e}-icon-dot`]:{borderRadius:t.calc(t.fontSizeSM).div(4).equal(),"&::after":{display:"none"}}},"&-content":{width:"auto",marginTop:t.calc(t.marginXS).sub(t.lineWidth).equal()},"&-title":{color:i,fontSize:t.fontSizeSM,lineHeight:t.lineHeightSM,fontWeight:"normal",marginBottom:t.calc(t.marginXXS).div(2).equal()},"&-description":{display:"none"},"&-tail":{marginInlineStart:0,top:t.calc(n).div(2).add(r).equal(),transform:"translateY(-50%)","&:after":{width:"100%",height:t.lineWidth,borderRadius:0,marginInlineStart:0,background:o}},[`&:first-child ${e}-item-tail`]:{width:"50%",marginInlineStart:"50%"},[`&:last-child ${e}-item-tail`]:{display:"block",width:"50%"},"&-wait":Object.assign({[`${e}-item-icon ${e}-icon ${e}-icon-dot`]:{backgroundColor:t.colorBorderBg,border:`${(0,g.zA)(t.lineWidth)} ${t.lineType} ${o}`}},a),"&-finish":Object.assign({[`${e}-item-tail::after`]:{backgroundColor:o},[`${e}-item-icon ${e}-icon ${e}-icon-dot`]:{backgroundColor:o,border:`${(0,g.zA)(t.lineWidth)} ${t.lineType} ${o}`}},a),"&-error":a,"&-active, &-process":Object.assign({[`${e}-item-icon`]:{width:n,height:n,marginInlineStart:`calc(50% - ${(0,g.zA)(t.calc(n).div(2).equal())})`,top:0}},a),[`&:not(${e}-item-active) > ${e}-item-container[role='button']:hover`]:{[`${e}-item-title`]:{color:i}}}}}},S=t=>{const{componentCls:e,iconSize:n,lineHeight:i,iconSizeSM:o}=t;return{[`&${e}-label-vertical`]:{[`${e}-item`]:{overflow:"visible","&-tail":{marginInlineStart:t.calc(n).div(2).add(t.controlHeightLG).equal(),padding:`0 ${(0,g.zA)(t.paddingLG)}`},"&-content":{display:"block",width:t.calc(n).div(2).add(t.controlHeightLG).mul(2).equal(),marginTop:t.marginSM,textAlign:"center"},"&-icon":{display:"inline-block",marginInlineStart:t.controlHeightLG},"&-title":{paddingInlineEnd:0,paddingInlineStart:0,"&::after":{display:"none"}},"&-subtitle":{display:"block",marginBottom:t.marginXXS,marginInlineStart:0,lineHeight:i}},[`&${e}-small:not(${e}-dot)`]:{[`${e}-item`]:{"&-icon":{marginInlineStart:t.calc(n).sub(o).div(2).add(t.controlHeightLG).equal()}}}}}},y=t=>{const{componentCls:e,navContentMaxWidth:n,navArrowColor:i,stepsNavActiveColor:o,motionDurationSlow:r}=t;return{[`&${e}-navigation`]:{paddingTop:t.paddingSM,[`&${e}-small`]:{[`${e}-item`]:{"&-container":{marginInlineStart:t.calc(t.marginSM).mul(-1).equal()}}},[`${e}-item`]:{overflow:"visible",textAlign:"center","&-container":{display:"inline-block",height:"100%",marginInlineStart:t.calc(t.margin).mul(-1).equal(),paddingBottom:t.paddingSM,textAlign:"start",transition:`opacity ${r}`,[`${e}-item-content`]:{maxWidth:n},[`${e}-item-title`]:Object.assign(Object.assign({maxWidth:"100%",paddingInlineEnd:0},f.L9),{"&::after":{display:"none"}})},[`&:not(${e}-item-active)`]:{[`${e}-item-container[role='button']`]:{cursor:"pointer","&:hover":{opacity:.85}}},"&:last-child":{flex:1,"&::after":{display:"none"}},"&::after":{position:"absolute",top:`calc(50% - ${(0,g.zA)(t.calc(t.paddingSM).div(2).equal())})`,insetInlineStart:"100%",display:"inline-block",width:t.fontSizeIcon,height:t.fontSizeIcon,borderTop:`${(0,g.zA)(t.lineWidth)} ${t.lineType} ${i}`,borderBottom:"none",borderInlineStart:"none",borderInlineEnd:`${(0,g.zA)(t.lineWidth)} ${t.lineType} ${i}`,transform:"translateY(-50%) translateX(-50%) rotate(45deg)",content:'""'},"&::before":{position:"absolute",bottom:0,insetInlineStart:"50%",display:"inline-block",width:0,height:t.lineWidthBold,backgroundColor:o,transition:`width ${r}, inset-inline-start ${r}`,transitionTimingFunction:"ease-out",content:'""'}},[`${e}-item${e}-item-active::before`]:{insetInlineStart:0,width:"100%"}},[`&${e}-navigation${e}-vertical`]:{[`> ${e}-item`]:{marginInlineEnd:0,"&::before":{display:"none"},[`&${e}-item-active::before`]:{top:0,insetInlineEnd:0,insetInlineStart:"unset",display:"block",width:t.calc(t.lineWidth).mul(3).equal(),height:`calc(100% - ${(0,g.zA)(t.marginLG)})`},"&::after":{position:"relative",insetInlineStart:"50%",display:"block",width:t.calc(t.controlHeight).mul(.25).equal(),height:t.calc(t.controlHeight).mul(.25).equal(),marginBottom:t.marginXS,textAlign:"center",transform:"translateY(-50%) translateX(-50%) rotate(135deg)"},"&:last-child":{"&::after":{display:"none"}},[`> ${e}-item-container > ${e}-item-tail`]:{visibility:"hidden"}}},[`&${e}-navigation${e}-horizontal`]:{[`> ${e}-item > ${e}-item-container > ${e}-item-tail`]:{visibility:"hidden"}}}},w=t=>{const{antCls:e,componentCls:n,iconSize:i,iconSizeSM:o,processIconColor:r,marginXXS:a,lineWidthBold:l,lineWidth:s,paddingXXS:c}=t,m=t.calc(i).add(t.calc(l).mul(4).equal()).equal(),d=t.calc(o).add(t.calc(t.lineWidth).mul(4).equal()).equal();return{[`&${n}-with-progress`]:{[`${n}-item`]:{paddingTop:c,[`&-process ${n}-item-container ${n}-item-icon ${n}-icon`]:{color:r}},[`&${n}-vertical > ${n}-item `]:{paddingInlineStart:c,[`> ${n}-item-container > ${n}-item-tail`]:{top:a,insetInlineStart:t.calc(i).div(2).sub(s).add(c).equal()}},[`&, &${n}-small`]:{[`&${n}-horizontal ${n}-item:first-child`]:{paddingBottom:c,paddingInlineStart:c}},[`&${n}-small${n}-vertical > ${n}-item > ${n}-item-container > ${n}-item-tail`]:{insetInlineStart:t.calc(o).div(2).sub(s).add(c).equal()},[`&${n}-label-vertical ${n}-item ${n}-item-tail`]:{top:t.calc(i).div(2).add(c).equal()},[`${n}-item-icon`]:{position:"relative",[`${e}-progress`]:{position:"absolute",insetInlineStart:"50%",top:"50%",transform:"translate(-50%, -50%)","&-inner":{width:`${(0,g.zA)(m)} !important`,height:`${(0,g.zA)(m)} !important`}}},[`&${n}-small`]:{[`&${n}-label-vertical ${n}-item ${n}-item-tail`]:{top:t.calc(o).div(2).add(c).equal()},[`${n}-item-icon ${e}-progress-inner`]:{width:`${(0,g.zA)(d)} !important`,height:`${(0,g.zA)(d)} !important`}}}}},O=t=>{const{componentCls:e,descriptionMaxWidth:n,lineHeight:i,dotCurrentSize:o,dotSize:r,motionDurationSlow:a}=t;return{[`&${e}-dot, &${e}-dot${e}-small`]:{[`${e}-item`]:{"&-title":{lineHeight:i},"&-tail":{top:t.calc(t.dotSize).sub(t.calc(t.lineWidth).mul(3).equal()).div(2).equal(),width:"100%",marginTop:0,marginBottom:0,marginInline:`${(0,g.zA)(t.calc(n).div(2).equal())} 0`,padding:0,"&::after":{width:`calc(100% - ${(0,g.zA)(t.calc(t.marginSM).mul(2).equal())})`,height:t.calc(t.lineWidth).mul(3).equal(),marginInlineStart:t.marginSM}},"&-icon":{width:r,height:r,marginInlineStart:t.calc(t.descriptionMaxWidth).sub(r).div(2).equal(),paddingInlineEnd:0,lineHeight:(0,g.zA)(r),background:"transparent",border:0,[`${e}-icon-dot`]:{position:"relative",float:"left",width:"100%",height:"100%",borderRadius:100,transition:`all ${a}`,"&::after":{position:"absolute",top:t.calc(t.marginSM).mul(-1).equal(),insetInlineStart:t.calc(r).sub(t.calc(t.controlHeightLG).mul(1.5).equal()).div(2).equal(),width:t.calc(t.controlHeightLG).mul(1.5).equal(),height:t.controlHeight,background:"transparent",content:'""'}}},"&-content":{width:n},[`&-process ${e}-item-icon`]:{position:"relative",top:t.calc(r).sub(o).div(2).equal(),width:o,height:o,lineHeight:(0,g.zA)(o),background:"none",marginInlineStart:t.calc(t.descriptionMaxWidth).sub(o).div(2).equal()},[`&-process ${e}-icon`]:{[`&:first-child ${e}-icon-dot`]:{insetInlineStart:0}}}},[`&${e}-vertical${e}-dot`]:{[`${e}-item-icon`]:{marginTop:t.calc(t.controlHeight).sub(r).div(2).equal(),marginInlineStart:0,background:"none"},[`${e}-item-process ${e}-item-icon`]:{marginTop:t.calc(t.controlHeight).sub(o).div(2).equal(),top:0,insetInlineStart:t.calc(r).sub(o).div(2).equal(),marginInlineStart:0},[`${e}-item > ${e}-item-container > ${e}-item-tail`]:{top:t.calc(t.controlHeight).sub(r).div(2).equal(),insetInlineStart:0,margin:0,padding:`${(0,g.zA)(t.calc(r).add(t.paddingXS).equal())} 0 ${(0,g.zA)(t.paddingXS)}`,"&::after":{marginInlineStart:t.calc(r).sub(t.lineWidth).div(2).equal()}},[`&${e}-small`]:{[`${e}-item-icon`]:{marginTop:t.calc(t.controlHeightSM).sub(r).div(2).equal()},[`${e}-item-process ${e}-item-icon`]:{marginTop:t.calc(t.controlHeightSM).sub(o).div(2).equal()},[`${e}-item > ${e}-item-container > ${e}-item-tail`]:{top:t.calc(t.controlHeightSM).sub(r).div(2).equal()}},[`${e}-item:first-child ${e}-icon-dot`]:{insetInlineStart:0},[`${e}-item-content`]:{width:"inherit"}}}},z=t=>{const{componentCls:e}=t;return{[`&${e}-rtl`]:{direction:"rtl",[`${e}-item`]:{"&-subtitle":{float:"left"}},[`&${e}-navigation`]:{[`${e}-item::after`]:{transform:"rotate(-45deg)"}},[`&${e}-vertical`]:{[`> ${e}-item`]:{"&::after":{transform:"rotate(225deg)"},[`${e}-item-icon`]:{float:"right"}}},[`&${e}-dot`]:{[`${e}-item-icon ${e}-icon-dot, &${e}-small ${e}-item-icon ${e}-icon-dot`]:{float:"right"}}}}},x=t=>{const{componentCls:e,iconSizeSM:n,fontSizeSM:i,fontSize:o,colorTextDescription:r}=t;return{[`&${e}-small`]:{[`&${e}-horizontal:not(${e}-label-vertical) ${e}-item`]:{paddingInlineStart:t.paddingSM,"&:first-child":{paddingInlineStart:0}},[`${e}-item-icon`]:{width:n,height:n,marginTop:0,marginBottom:0,marginInline:`0 ${(0,g.zA)(t.marginXS)}`,fontSize:i,lineHeight:(0,g.zA)(n),textAlign:"center",borderRadius:n},[`${e}-item-title`]:{paddingInlineEnd:t.paddingSM,fontSize:o,lineHeight:(0,g.zA)(n),"&::after":{top:t.calc(n).div(2).equal()}},[`${e}-item-description`]:{color:r,fontSize:o},[`${e}-item-tail`]:{top:t.calc(n).div(2).sub(t.paddingXXS).equal()},[`${e}-item-custom ${e}-item-icon`]:{width:"inherit",height:"inherit",lineHeight:"inherit",background:"none",border:0,borderRadius:0,[`> ${e}-icon`]:{fontSize:n,lineHeight:(0,g.zA)(n),transform:"none"}}}}},I=t=>{const{componentCls:e,iconSizeSM:n,iconSize:i}=t;return{[`&${e}-vertical`]:{display:"flex",flexDirection:"column",[`> ${e}-item`]:{display:"block",flex:"1 0 auto",paddingInlineStart:0,overflow:"visible",[`${e}-item-icon`]:{float:"left",marginInlineEnd:t.margin},[`${e}-item-content`]:{display:"block",minHeight:t.calc(t.controlHeight).mul(1.5).equal(),overflow:"hidden"},[`${e}-item-title`]:{lineHeight:(0,g.zA)(i)},[`${e}-item-description`]:{paddingBottom:t.paddingSM}},[`> ${e}-item > ${e}-item-container > ${e}-item-tail`]:{position:"absolute",top:0,insetInlineStart:t.calc(i).div(2).sub(t.lineWidth).equal(),width:t.lineWidth,height:"100%",padding:`${(0,g.zA)(t.calc(t.marginXXS).mul(1.5).add(i).equal())} 0 ${(0,g.zA)(t.calc(t.marginXXS).mul(1.5).equal())}`,"&::after":{width:t.lineWidth,height:"100%"}},[`> ${e}-item:not(:last-child) > ${e}-item-container > ${e}-item-tail`]:{display:"block"},[` > ${e}-item > ${e}-item-container > ${e}-item-content > ${e}-item-title`]:{"&::after":{display:"none"}},[`&${e}-small ${e}-item-container`]:{[`${e}-item-tail`]:{position:"absolute",top:0,insetInlineStart:t.calc(n).div(2).sub(t.lineWidth).equal(),padding:`${(0,g.zA)(t.calc(t.marginXXS).mul(1.5).add(n).equal())} 0 ${(0,g.zA)(t.calc(t.marginXXS).mul(1.5).equal())}`},[`${e}-item-title`]:{lineHeight:(0,g.zA)(n)}}}}},C=(t,e)=>{const n=`${e.componentCls}-item`,i=`${t}IconColor`,o=`${t}TitleColor`,r=`${t}DescriptionColor`,a=`${t}TailColor`,l=`${t}IconBgColor`,s=`${t}IconBorderColor`,c=`${t}DotColor`;return{[`${n}-${t} ${n}-icon`]:{backgroundColor:e[l],borderColor:e[s],[`> ${e.componentCls}-icon`]:{color:e[i],[`${e.componentCls}-icon-dot`]:{background:e[c]}}},[`${n}-${t}${n}-custom ${n}-icon`]:{[`> ${e.componentCls}-icon`]:{color:e[c]}},[`${n}-${t} > ${n}-container > ${n}-content > ${n}-title`]:{color:e[o],"&::after":{backgroundColor:e[a]}},[`${n}-${t} > ${n}-container > ${n}-content > ${n}-description`]:{color:e[r]},[`${n}-${t} > ${n}-container > ${n}-tail::after`]:{backgroundColor:e[a]}}},M=t=>{const{componentCls:e,motionDurationSlow:n}=t,i=`${e}-item`,o=`${i}-icon`;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({[i]:{position:"relative",display:"inline-block",flex:1,overflow:"hidden",verticalAlign:"top","&:last-child":{flex:"none",[`> ${i}-container > ${i}-tail, > ${i}-container >  ${i}-content > ${i}-title::after`]:{display:"none"}}},[`${i}-container`]:{outline:"none","&:focus-visible":{[o]:Object.assign({},(0,f.jk)(t))}},[`${o}, ${i}-content`]:{display:"inline-block",verticalAlign:"top"},[o]:{width:t.iconSize,height:t.iconSize,marginTop:0,marginBottom:0,marginInlineStart:0,marginInlineEnd:t.marginXS,fontSize:t.iconFontSize,fontFamily:t.fontFamily,lineHeight:(0,g.zA)(t.iconSize),textAlign:"center",borderRadius:t.iconSize,border:`${(0,g.zA)(t.lineWidth)} ${t.lineType} transparent`,transition:`background-color ${n}, border-color ${n}`,[`${e}-icon`]:{position:"relative",top:t.iconTop,color:t.colorPrimary,lineHeight:1}},[`${i}-tail`]:{position:"absolute",top:t.calc(t.iconSize).div(2).equal(),insetInlineStart:0,width:"100%","&::after":{display:"inline-block",width:"100%",height:t.lineWidth,background:t.colorSplit,borderRadius:t.lineWidth,transition:`background ${n}`,content:'""'}},[`${i}-title`]:{position:"relative",display:"inline-block",paddingInlineEnd:t.padding,color:t.colorText,fontSize:t.fontSizeLG,lineHeight:(0,g.zA)(t.titleLineHeight),"&::after":{position:"absolute",top:t.calc(t.titleLineHeight).div(2).equal(),insetInlineStart:"100%",display:"block",width:9999,height:t.lineWidth,background:t.processTailColor,content:'""'}},[`${i}-subtitle`]:{display:"inline",marginInlineStart:t.marginXS,color:t.colorTextDescription,fontWeight:"normal",fontSize:t.fontSize},[`${i}-description`]:{color:t.colorTextDescription,fontSize:t.fontSize}},C("wait",t)),C("process",t)),{[`${i}-process > ${i}-container > ${i}-title`]:{fontWeight:t.fontWeightStrong}}),C("finish",t)),C("error",t)),{[`${i}${e}-next-error > ${e}-item-title::after`]:{background:t.colorError},[`${i}-disabled`]:{cursor:"not-allowed"}})},k=t=>{const{componentCls:e,motionDurationSlow:n}=t;return{[`& ${e}-item`]:{[`&:not(${e}-item-active)`]:{[`& > ${e}-item-container[role='button']`]:{cursor:"pointer",[`${e}-item`]:{[`&-title, &-subtitle, &-description, &-icon ${e}-icon`]:{transition:`color ${n}`}},"&:hover":{[`${e}-item`]:{"&-title, &-subtitle, &-description":{color:t.colorPrimary}}}},[`&:not(${e}-item-process)`]:{[`& > ${e}-item-container[role='button']:hover`]:{[`${e}-item`]:{"&-icon":{borderColor:t.colorPrimary,[`${e}-icon`]:{color:t.colorPrimary}}}}}}},[`&${e}-horizontal:not(${e}-label-vertical)`]:{[`${e}-item`]:{paddingInlineStart:t.padding,whiteSpace:"nowrap","&:first-child":{paddingInlineStart:0},[`&:last-child ${e}-item-title`]:{paddingInlineEnd:0},"&-tail":{display:"none"},"&-description":{maxWidth:t.descriptionMaxWidth,whiteSpace:"normal"}}}}},j=t=>{const{componentCls:e}=t;return{[e]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,f.dF)(t)),{display:"flex",width:"100%",fontSize:0,textAlign:"initial"}),M(t)),k(t)),h(t)),x(t)),I(t)),b(t)),S(t)),O(t)),y(t)),z(t)),w(t)),v(t))}},E=(0,$.OF)("Steps",(t=>{const{colorTextDisabled:e,controlHeightLG:n,colorTextLightSolid:i,colorText:o,colorPrimary:r,colorTextDescription:a,colorTextQuaternary:l,colorError:s,colorBorderSecondary:c,colorSplit:m}=t,d=(0,$.oX)(t,{processIconColor:i,processTitleColor:o,processDescriptionColor:o,processIconBgColor:r,processIconBorderColor:r,processDotColor:r,processTailColor:m,waitTitleColor:a,waitDescriptionColor:a,waitTailColor:m,waitDotColor:e,finishIconColor:r,finishTitleColor:o,finishDescriptionColor:a,finishTailColor:r,finishDotColor:r,errorIconColor:i,errorTitleColor:s,errorDescriptionColor:s,errorTailColor:m,errorIconBgColor:s,errorIconBorderColor:s,errorDotColor:s,stepsNavActiveColor:r,stepsProgressSize:n,inlineDotSize:6,inlineTitleColor:l,inlineTailColor:c});return[j(d)]}),(t=>({titleLineHeight:t.controlHeight,customIconSize:t.controlHeight,customIconTop:0,customIconFontSize:t.controlHeightSM,iconSize:t.controlHeight,iconTop:-.5,iconFontSize:t.fontSize,iconSizeSM:t.fontSizeHeading3,dotSize:t.controlHeight/4,dotCurrentSize:t.controlHeightLG/4,navArrowColor:t.colorTextDisabled,navContentMaxWidth:"unset",descriptionMaxWidth:140,waitIconColor:t.wireframe?t.colorTextDisabled:t.colorTextLabel,waitIconBgColor:t.wireframe?t.colorBgContainer:t.colorFillContent,waitIconBorderColor:t.wireframe?t.colorTextDisabled:"transparent",finishIconBgColor:t.wireframe?t.colorBgContainer:t.controlItemBgActive,finishIconBorderColor:t.wireframe?t.colorPrimary:t.controlItemBgActive})));var A=n(82546);n(18877);(t=>{const{percent:e,size:n,className:a,rootClassName:g,direction:f,items:$,responsive:h=!0,current:b=0,children:v,style:S}=t,y=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(i=Object.getOwnPropertySymbols(t);o<i.length;o++)e.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(t,i[o])&&(n[i[o]]=t[i[o]])}return n}(t,["percent","size","className","rootClassName","direction","items","responsive","current","children","style"]),{xs:w}=(0,d.A)(h),{getPrefixCls:O,direction:z,className:x,style:I}=(0,c.TP)("steps"),C=i.useMemo((()=>h&&w?"vertical":f),[w,f]),M=(0,m.A)(n),k=O("steps",t.prefixCls),[j,T,q]=E(k),D="inline"===t.type,H=O("",t.iconPrefix),X=function(t,e){return t||function(t){return t.filter((t=>t))}((0,A.A)(e).map((t=>{if(i.isValidElement(t)){const{props:e}=t;return Object.assign({},e)}return null})))}($,v),B=D?void 0:e,_=Object.assign(Object.assign({},I),S),N=l()(x,{[`${k}-rtl`]:"rtl"===z,[`${k}-with-progress`]:void 0!==B},a,g,T,q),R={finish:i.createElement(o.A,{className:`${k}-finish-icon`}),error:i.createElement(r.A,{className:`${k}-error-icon`})};return j(i.createElement(s.A,Object.assign({icons:R},y,{style:_,current:b,size:M,items:X,itemRender:D?(t,e)=>t.description?i.createElement(u.A,{title:t.description},e):e:void 0,stepIcon:({node:t,status:e})=>{if("process"===e&&void 0!==B){const e="small"===M?32:40;return i.createElement("div",{className:`${k}-progress-icon`},i.createElement(p.A,{type:"circle",percent:B,size:e,strokeWidth:4,format:()=>null}),t)}return t},direction:C,prefixCls:k,iconPrefix:H,className:N})))}).Step=s.A.Step},55974:(t,e,n)=>{function i(t,e,n){const{focusElCls:i,focus:o,borderElCls:r}=n,a=r?"> *":"",l=["hover",o?"focus":null,"active"].filter(Boolean).map((t=>`&:${t} ${a}`)).join(",");return{[`&-item:not(${e}-last-item)`]:{marginInlineEnd:t.calc(t.lineWidth).mul(-1).equal()},"&-item":Object.assign(Object.assign({[l]:{zIndex:2}},i?{[`&${i}`]:{zIndex:2}}:{}),{[`&[disabled] ${a}`]:{zIndex:0}})}}function o(t,e,n){const{borderElCls:i}=n,o=i?`> ${i}`:"";return{[`&-item:not(${e}-first-item):not(${e}-last-item) ${o}`]:{borderRadius:0},[`&-item:not(${e}-last-item)${e}-first-item`]:{[`& ${o}, &${t}-sm ${o}, &${t}-lg ${o}`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&-item:not(${e}-first-item)${e}-last-item`]:{[`& ${o}, &${t}-sm ${o}, &${t}-lg ${o}`]:{borderStartStartRadius:0,borderEndStartRadius:0}}}}function r(t,e={focus:!0}){const{componentCls:n}=t,r=`${n}-compact`;return{[r]:Object.assign(Object.assign({},i(t,r,e)),o(n,r,e))}}n.d(e,{G:()=>r})},65461:(t,e,n)=>{function i(t,e){return{[`&-item:not(${e}-last-item)`]:{marginBottom:t.calc(t.lineWidth).mul(-1).equal()},"&-item":{"&:hover,&:focus,&:active":{zIndex:2},"&[disabled]":{zIndex:0}}}}function o(t){const e=`${t.componentCls}-compact-vertical`;return{[e]:Object.assign(Object.assign({},i(t,e)),(n=t.componentCls,o=e,{[`&-item:not(${o}-first-item):not(${o}-last-item)`]:{borderRadius:0},[`&-item${o}-first-item:not(${o}-last-item)`]:{[`&, &${n}-sm, &${n}-lg`]:{borderEndEndRadius:0,borderEndStartRadius:0}},[`&-item${o}-last-item:not(${o}-first-item)`]:{[`&, &${n}-sm, &${n}-lg`]:{borderStartStartRadius:0,borderStartEndRadius:0}}}))};var n,o}n.d(e,{q:()=>o})},76763:(t,e,n)=>{var i=n(96540),o=n(46942),r=n.n(o);const a=(0,i.forwardRef)(((t,e)=>{const{prefixCls:n,className:o,children:a,size:l,style:s={}}=t,c=r()(`${n}-panel`,{[`${n}-panel-hidden`]:0===l},o),m=void 0!==l;return i.createElement("div",{ref:e,className:c,style:Object.assign(Object.assign({},s),{flexBasis:m?l:"auto",flexGrow:m?0:1})},a)}));var l=n(26076),s=n(26956),c=(n(18877),n(62279)),m=n(20934),d=n(82546);function p(t){if(t&&"object"==typeof t)return t;const e=!!t;return{start:e,end:e}}var u=n(60436);function g(t){return Number(t.slice(0,-1))/100}function f(t){return"string"==typeof t&&t.endsWith("%")}var $=n(73964),h=n(26557),b=n(14588),v=n(3085);function S(t){return"number"!=typeof t||Number.isNaN(t)?0:Math.round(t)}const y=t=>{const{prefixCls:e,vertical:n,index:o,active:a,ariaNow:l,ariaMin:c,ariaMax:m,resizable:d,startCollapsible:p,endCollapsible:u,onOffsetStart:g,onOffsetUpdate:f,onOffsetEnd:y,onCollapse:w,lazy:O,containerSize:z}=t,x=`${e}-bar`,[I,C]=(0,i.useState)(null),[M,k]=(0,i.useState)(0),j=n?0:M,E=n?M:0,A=(0,s.A)(((t,e)=>{const i=(t=>{const e=z*l/100,n=e+t,i=Math.max(0,z*c/100),o=Math.min(z,z*m/100);return Math.max(i,Math.min(o,n))-e})(n?e:t);k(i)})),T=(0,s.A)((()=>{f(o,j,E,!0),k(0),y(!0)}));i.useEffect((()=>{if(I){const t=t=>{const{pageX:e,pageY:n}=t,i=e-I[0],r=n-I[1];O?A(i,r):f(o,i,r)},e=()=>{O?T():y(),C(null)},n=t=>{if(1===t.touches.length){const e=t.touches[0],n=e.pageX-I[0],i=e.pageY-I[1];O?A(n,i):f(o,n,i)}},i=()=>{O?T():y(),C(null)};return window.addEventListener("touchmove",n),window.addEventListener("touchend",i),window.addEventListener("mousemove",t),window.addEventListener("mouseup",e),()=>{window.removeEventListener("mousemove",t),window.removeEventListener("mouseup",e),window.removeEventListener("touchmove",n),window.removeEventListener("touchend",i)}}}),[I,O,n,o,z,l,c,m]);const q={[`--${x}-preview-offset`]:`${M}px`},D=n?v.A:h.A,H=n?$.A:b.A;return i.createElement("div",{className:x,role:"separator","aria-valuenow":S(l),"aria-valuemin":S(c),"aria-valuemax":S(m)},O&&i.createElement("div",{className:r()(`${x}-preview`,{[`${x}-preview-active`]:!!M}),style:q}),i.createElement("div",{className:r()(`${x}-dragger`,{[`${x}-dragger-disabled`]:!d,[`${x}-dragger-active`]:a}),onMouseDown:t=>{d&&t.currentTarget&&(C([t.pageX,t.pageY]),g(o))},onTouchStart:t=>{if(d&&1===t.touches.length){const e=t.touches[0];C([e.pageX,e.pageY]),g(o)}}}),p&&i.createElement("div",{className:r()(`${x}-collapse-bar`,`${x}-collapse-bar-start`),onClick:()=>w(o,"start")},i.createElement(D,{className:r()(`${x}-collapse-icon`,`${x}-collapse-start`)})),u&&i.createElement("div",{className:r()(`${x}-collapse-bar`,`${x}-collapse-bar-end`),onClick:()=>w(o,"end")},i.createElement(H,{className:r()(`${x}-collapse-icon`,`${x}-collapse-end`)})))};var w=n(25905),O=n(51113);const z=t=>{const{componentCls:e}=t;return{[`&-rtl${e}-horizontal`]:{[`> ${e}-bar`]:{[`${e}-bar-collapse-previous`]:{insetInlineEnd:0,insetInlineStart:"unset"},[`${e}-bar-collapse-next`]:{insetInlineEnd:"unset",insetInlineStart:0}}},[`&-rtl${e}-vertical`]:{[`> ${e}-bar`]:{[`${e}-bar-collapse-previous`]:{insetInlineEnd:"50%",insetInlineStart:"unset"},[`${e}-bar-collapse-next`]:{insetInlineEnd:"50%",insetInlineStart:"unset"}}}}},x={position:"absolute",top:"50%",left:{_skip_check_:!0,value:"50%"},transform:"translate(-50%, -50%)"},I=t=>{const{componentCls:e,colorFill:n,splitBarDraggableSize:i,splitBarSize:o,splitTriggerSize:r,controlItemBgHover:a,controlItemBgActive:l,controlItemBgActiveHover:s,prefixCls:c}=t,m=`${e}-bar`,d=`${e}-mask`,p=`${e}-panel`,u=t.calc(r).div(2).equal(),g=`${c}-bar-preview-offset`,f={position:"absolute",background:t.colorPrimary,opacity:.2,pointerEvents:"none",transition:"none",zIndex:1,display:"none"};return{[e]:Object.assign(Object.assign(Object.assign({},(0,w.dF)(t)),{display:"flex",width:"100%",height:"100%",alignItems:"stretch",[`> ${m}`]:{flex:"none",position:"relative",userSelect:"none",[`${m}-dragger`]:Object.assign(Object.assign({},x),{zIndex:1,"&::before":Object.assign({content:'""',background:a},x),"&::after":Object.assign({content:'""',background:n},x),[`&:hover:not(${m}-dragger-active)`]:{"&::before":{background:l}},"&-active":{zIndex:2,"&::before":{background:s}},[`&-disabled${m}-dragger`]:{zIndex:0,"&, &:hover, &-active":{cursor:"default","&::before":{background:a}},"&::after":{display:"none"}}}),[`${m}-collapse-bar`]:Object.assign(Object.assign({},x),{zIndex:t.zIndexPopupBase,background:a,fontSize:t.fontSizeSM,borderRadius:t.borderRadiusXS,color:t.colorText,cursor:"pointer",opacity:0,display:"flex",alignItems:"center",justifyContent:"center","@media(hover:none)":{opacity:1},"&:hover":{background:l},"&:active":{background:s}}),"&:hover, &:active":{[`${m}-collapse-bar`]:{opacity:1}}},[d]:{position:"fixed",zIndex:t.zIndexPopupBase,inset:0,"&-horizontal":{cursor:"col-resize"},"&-vertical":{cursor:"row-resize"}},"&-horizontal":{flexDirection:"row",[`> ${m}`]:{width:0,[`${m}-preview`]:Object.assign(Object.assign({height:"100%",width:o},f),{[`&${m}-preview-active`]:{display:"block",transform:`translateX(var(--${g}))`}}),[`${m}-dragger`]:{cursor:"col-resize",height:"100%",width:r,"&::before":{height:"100%",width:o},"&::after":{height:i,width:o}},[`${m}-collapse-bar`]:{width:t.fontSizeSM,height:t.controlHeightSM,"&-start":{left:{_skip_check_:!0,value:"auto"},right:{_skip_check_:!0,value:u},transform:"translateY(-50%)"},"&-end":{left:{_skip_check_:!0,value:u},right:{_skip_check_:!0,value:"auto"},transform:"translateY(-50%)"}}}},"&-vertical":{flexDirection:"column",[`> ${m}`]:{height:0,[`${m}-preview`]:Object.assign(Object.assign({height:o,width:"100%"},f),{[`&${m}-preview-active`]:{display:"block",transform:`translateY(var(--${g}))`}}),[`${m}-dragger`]:{cursor:"row-resize",width:"100%",height:r,"&::before":{width:"100%",height:o},"&::after":{width:i,height:o}},[`${m}-collapse-bar`]:{height:t.fontSizeSM,width:t.controlHeightSM,"&-start":{top:"auto",bottom:u,transform:"translateX(-50%)"},"&-end":{top:u,bottom:"auto",transform:"translateX(-50%)"}}}},[p]:{overflow:"auto",padding:"0 1px",scrollbarWidth:"thin",boxSizing:"border-box","&-hidden":{padding:0,overflow:"hidden"},[`&:has(${e}:only-child)`]:{overflow:"hidden"}}}),z(t))}},C=(0,O.OF)("Splitter",(t=>[I(t)]),(t=>{var e;const n=t.splitBarSize||2,i=t.splitTriggerSize||6,o=t.resizeSpinnerSize||20;return{splitBarSize:n,splitTriggerSize:i,splitBarDraggableSize:null!==(e=t.splitBarDraggableSize)&&void 0!==e?e:o,resizeSpinnerSize:o}})),M=t=>{const{prefixCls:e,className:n,style:o,layout:$="horizontal",children:h,rootClassName:b,onResizeStart:v,onResize:S,onResizeEnd:w,lazy:O}=t,{getPrefixCls:z,direction:x,className:I,style:M}=(0,c.TP)("splitter"),k=z("splitter",e),j=(0,m.A)(k),[E,A,T]=C(k,j),q="vertical"===$,D="rtl"===x,H=!q&&D,X=function(t){return i.useMemo((()=>(0,d.A)(t).filter(i.isValidElement).map((t=>{const{props:e}=t,{collapsible:n}=e,i=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(i=Object.getOwnPropertySymbols(t);o<i.length;o++)e.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(t,i[o])&&(n[i[o]]=t[i[o]])}return n}(e,["collapsible"]);return Object.assign(Object.assign({},i),{collapsible:p(n)})}))),[t])}(h),[B,_]=(0,i.useState)(),[N,R,L,P,W,F]=function(t,e){const n=t.map((t=>t.size)),o=t.length,r=e||0,a=t=>t*r,[l,s]=i.useState((()=>t.map((t=>t.defaultSize)))),c=i.useMemo((()=>{var t;const e=[];for(let i=0;i<o;i+=1)e[i]=null!==(t=n[i])&&void 0!==t?t:l[i];return e}),[o,l,n]),m=i.useMemo((()=>{let t=[],e=0;for(let n=0;n<o;n+=1){const i=c[n];if(f(i))t[n]=g(i);else if(i||0===i){const e=Number(i);Number.isNaN(e)||(t[n]=e/r)}else e+=1,t[n]=void 0}const n=t.reduce(((t,e)=>t+(e||0)),0);if(n>1||!e){const e=1/n;t=t.map((t=>void 0===t?0:t*e))}else{const i=(1-n)/e;t=t.map((t=>void 0===t?i:t))}return t}),[c,r]),d=i.useMemo((()=>m.map(a)),[m,r]),p=i.useMemo((()=>t.map((t=>f(t.min)?g(t.min):(t.min||0)/r))),[t,r]),u=i.useMemo((()=>t.map((t=>f(t.max)?g(t.max):(t.max||r)/r))),[t,r]);return[i.useMemo((()=>e?d:c),[d,e]),d,m,p,u,s]}(X,B),K=function(t,e,n){return i.useMemo((()=>{const i=[];for(let o=0;o<t.length-1;o+=1){const r=t[o],a=t[o+1],l=e[o],s=e[o+1],{resizable:c=!0,min:m,collapsible:d}=r,{resizable:p=!0,min:u,collapsible:g}=a,f=c&&p&&(0!==l||!m)&&(0!==s||!u),$=d.end&&l>0||g.start&&0===s&&l>0,h=g.start&&s>0||d.end&&0===l&&s>0;i[o]={resizable:f,startCollapsible:!!(n?h:$),endCollapsible:!!(n?$:h)}}return i}),[e,t])}(X,R,D),[Y,G,Z,U,V]=function(t,e,n,o,r,a){const l=t.map((t=>[t.min,t.max])),s=o||0,c=t=>t*s;function m(t,e){return"string"==typeof t?c(g(t)):null!=t?t:e}const[d,p]=i.useState([]),f=i.useRef([]),[$,h]=i.useState(null),b=()=>n.map(c);return[t=>{p(b()),h({index:t,confirmed:!1})},(t,n)=>{var i;let o=null;if(!($&&$.confirmed||0===n))if(n>0)o=t,h({index:t,confirmed:!0});else for(let n=t;n>=0;n-=1)if(d[n]>0&&e[n].resizable){o=n,h({index:n,confirmed:!0});break}const a=null!==(i=null!=o?o:null==$?void 0:$.index)&&void 0!==i?i:t,c=(0,u.A)(d),p=a+1,g=m(l[a][0],0),f=m(l[p][0],0),b=m(l[a][1],s),v=m(l[p][1],s);let S=n;return c[a]+S<g&&(S=g-c[a]),c[p]-S<f&&(S=c[p]-f),c[a]+S>b&&(S=b-c[a]),c[p]-S>v&&(S=c[p]-v),c[a]+=S,c[p]-=S,r(c),c},()=>{h(null)},(t,e)=>{const n=b(),i=a?"start"===e?"end":"start":e,o="start"===i?t:t+1,c="start"===i?t+1:t,d=n[o],p=n[c];if(0!==d&&0!==p)n[o]=0,n[c]+=d,f.current[t]=d;else{const e=d+p,i=m(l[o][0],0),r=m(l[o][1],s),a=m(l[c][0],0),u=m(l[c][1],s),g=Math.max(i,e-u),$=Math.min(r,e-a),h=a||($-g)/2,b=f.current[t],v=e-b;b&&b<=u&&b>=a&&v<=r&&v>=i?(n[c]=b,n[o]=v):(n[o]-=h,n[c]+=h)}return r(n),n},null==$?void 0:$.index]}(X,K,L,B,F,D),Q=(0,s.A)((t=>{Y(t),null==v||v(R)})),J=(0,s.A)(((t,e,n)=>{const i=G(t,e);n?null==w||w(i):null==S||S(i)})),tt=(0,s.A)((t=>{Z(),t||null==w||w(R)})),et=(0,s.A)(((t,e)=>{const n=U(t,e);null==S||S(n),null==w||w(n)})),nt=r()(k,n,`${k}-${$}`,{[`${k}-rtl`]:D},b,I,T,j,A),it=`${k}-mask`,ot=i.useMemo((()=>{const t=[];let e=0;for(let n=0;n<X.length;n+=1)e+=L[n],t.push(e);return t}),[L]),rt=Object.assign(Object.assign({},M),o);return E(i.createElement(l.A,{onResize:t=>{const{offsetWidth:e,offsetHeight:n}=t,i=q?n:e;0!==i&&_(i)}},i.createElement("div",{style:rt,className:nt},X.map(((t,e)=>{const n=i.createElement(a,Object.assign({},t,{prefixCls:k,size:N[e]}));let o=null;const r=K[e];if(r){const t=(ot[e-1]||0)+P[e],n=(ot[e+1]||100)-W[e+1],a=(ot[e-1]||0)+W[e],l=(ot[e+1]||100)-P[e+1];o=i.createElement(y,{lazy:O,index:e,active:V===e,prefixCls:k,vertical:q,resizable:r.resizable,ariaNow:100*ot[e],ariaMin:100*Math.max(t,n),ariaMax:100*Math.min(a,l),startCollapsible:r.startCollapsible,endCollapsible:r.endCollapsible,onOffsetStart:Q,onOffsetUpdate:(t,e,n,i)=>{let o=q?n:e;H&&(o=-o),J(t,o,i)},onOffsetEnd:tt,onCollapse:et,containerSize:B||0})}return i.createElement(i.Fragment,{key:`split-panel-${e}`},n,o)})),"number"==typeof V&&i.createElement("div",{"aria-hidden":!0,className:r()(it,`${it}-${$}`)}))))};M.Panel=()=>null},95201:(t,e,n)=>{n.d(e,{Ay:()=>s,Ke:()=>a,Zs:()=>r});var i=n(36891),o=n(20791);const r=8;function a(t){const{contentRadius:e,limitVerticalRadius:n}=t,i=e>12?e+2:12;return{arrowOffsetHorizontal:i,arrowOffsetVertical:n?r:i}}function l(t,e){return t?e:{}}function s(t,e,n){const{componentCls:r,boxShadowPopoverArrow:a,arrowOffsetVertical:s,arrowOffsetHorizontal:c}=t,{arrowDistance:m=0,arrowPlacement:d={left:!0,right:!0,top:!0,bottom:!0}}=n||{};return{[r]:Object.assign(Object.assign(Object.assign(Object.assign({[`${r}-arrow`]:[Object.assign(Object.assign({position:"absolute",zIndex:1,display:"block"},(0,o.j)(t,e,a)),{"&:before":{background:e}})]},l(!!d.top,{[[`&-placement-top > ${r}-arrow`,`&-placement-topLeft > ${r}-arrow`,`&-placement-topRight > ${r}-arrow`].join(",")]:{bottom:m,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top > ${r}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},"&-placement-topLeft":{"--arrow-offset-horizontal":c,[`> ${r}-arrow`]:{left:{_skip_check_:!0,value:c}}},"&-placement-topRight":{"--arrow-offset-horizontal":`calc(100% - ${(0,i.zA)(c)})`,[`> ${r}-arrow`]:{right:{_skip_check_:!0,value:c}}}})),l(!!d.bottom,{[[`&-placement-bottom > ${r}-arrow`,`&-placement-bottomLeft > ${r}-arrow`,`&-placement-bottomRight > ${r}-arrow`].join(",")]:{top:m,transform:"translateY(-100%)"},[`&-placement-bottom > ${r}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(-100%)"},"&-placement-bottomLeft":{"--arrow-offset-horizontal":c,[`> ${r}-arrow`]:{left:{_skip_check_:!0,value:c}}},"&-placement-bottomRight":{"--arrow-offset-horizontal":`calc(100% - ${(0,i.zA)(c)})`,[`> ${r}-arrow`]:{right:{_skip_check_:!0,value:c}}}})),l(!!d.left,{[[`&-placement-left > ${r}-arrow`,`&-placement-leftTop > ${r}-arrow`,`&-placement-leftBottom > ${r}-arrow`].join(",")]:{right:{_skip_check_:!0,value:m},transform:"translateX(100%) rotate(90deg)"},[`&-placement-left > ${r}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(100%) rotate(90deg)"},[`&-placement-leftTop > ${r}-arrow`]:{top:s},[`&-placement-leftBottom > ${r}-arrow`]:{bottom:s}})),l(!!d.right,{[[`&-placement-right > ${r}-arrow`,`&-placement-rightTop > ${r}-arrow`,`&-placement-rightBottom > ${r}-arrow`].join(",")]:{left:{_skip_check_:!0,value:m},transform:"translateX(-100%) rotate(-90deg)"},[`&-placement-right > ${r}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(-100%) rotate(-90deg)"},[`&-placement-rightTop > ${r}-arrow`]:{top:s},[`&-placement-rightBottom > ${r}-arrow`]:{bottom:s}}))}}}}]);