/**
 * Accessibility Utilities
 * 
 * Comprehensive accessibility utilities for WCAG 2.1 AA compliance
 * including keyboard navigation, screen reader support, focus management,
 * and accessible drag-and-drop interactions.
 */

import { theme } from '../design-system';

/**
 * ARIA utilities for screen reader support
 */
export const ariaUtils = {
  /**
   * Generate unique IDs for ARIA relationships
   */
  generateId: (prefix = 'aria') => `${prefix}-${Math.random().toString(36).substr(2, 9)}`,

  /**
   * Create ARIA label for components
   */
  createLabel: (component, action = 'interact with') => {
    const type = component.type || 'component';
    const label = component.props?.label || component.props?.title || component.props?.text || type;
    return `${action} ${label} ${type}`;
  },

  /**
   * Create ARIA description for components
   */
  createDescription: (component) => {
    const descriptions = [];

    if (component.props?.description) {
      descriptions.push(component.props.description);
    }

    if (component.props?.placeholder) {
      descriptions.push(`Placeholder: ${component.props.placeholder}`);
    }

    if (component.props?.required) {
      descriptions.push('Required field');
    }

    if (component.props?.disabled) {
      descriptions.push('Disabled');
    }

    return descriptions.join('. ');
  },

  /**
   * Create live region announcements
   */
  announce: (message, priority = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  },

  /**
   * Create ARIA attributes for drag and drop
   */
  createDragDropAttributes: (component, isDragging = false, isDropTarget = false) => {
    const attributes = {
      'aria-grabbed': isDragging,
      'aria-describedby': `${component.id}-drag-instructions`,
    };

    if (isDropTarget) {
      attributes['aria-dropeffect'] = 'move';
    }

    return attributes;
  },

  /**
   * Create skip links for keyboard navigation
   */
  createSkipLink: (targetId, text) => ({
    href: `#${targetId}`,
    className: 'skip-link',
    'aria-label': text,
    onKeyDown: (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        document.getElementById(targetId)?.focus();
      }
    }
  }),
};

/**
 * Keyboard navigation utilities
 */
export const keyboardUtils = {
  /**
   * Standard key codes for accessibility
   */
  KEYS: {
    ENTER: 'Enter',
    SPACE: ' ',
    ESCAPE: 'Escape',
    TAB: 'Tab',
    ARROW_UP: 'ArrowUp',
    ARROW_DOWN: 'ArrowDown',
    ARROW_LEFT: 'ArrowLeft',
    ARROW_RIGHT: 'ArrowRight',
    HOME: 'Home',
    END: 'End',
    PAGE_UP: 'PageUp',
    PAGE_DOWN: 'PageDown',
    DELETE: 'Delete',
    BACKSPACE: 'Backspace'
  },

  /**
   * Handle keyboard activation (Enter/Space)
   */
  handleActivation: (callback) => (e) => {
    if (e.key === keyboardUtils.KEYS.ENTER || e.key === keyboardUtils.KEYS.SPACE) {
      e.preventDefault();
      callback(e);
    }
  },

  /**
   * Handle arrow key navigation in lists/grids
   */
  handleArrowNavigation: (elements, currentIndex, orientation = 'vertical') => (e) => {
    let newIndex = currentIndex;

    switch (e.key) {
      case keyboardUtils.KEYS.ARROW_UP:
        if (orientation === 'vertical') {
          newIndex = Math.max(0, currentIndex - 1);
          e.preventDefault();
        }
        break;
      case keyboardUtils.KEYS.ARROW_DOWN:
        if (orientation === 'vertical') {
          newIndex = Math.min(elements.length - 1, currentIndex + 1);
          e.preventDefault();
        }
        break;
      case keyboardUtils.KEYS.ARROW_LEFT:
        if (orientation === 'horizontal') {
          newIndex = Math.max(0, currentIndex - 1);
          e.preventDefault();
        }
        break;
      case keyboardUtils.KEYS.ARROW_RIGHT:
        if (orientation === 'horizontal') {
          newIndex = Math.min(elements.length - 1, currentIndex + 1);
          e.preventDefault();
        }
        break;
      case keyboardUtils.KEYS.HOME:
        newIndex = 0;
        e.preventDefault();
        break;
      case keyboardUtils.KEYS.END:
        newIndex = elements.length - 1;
        e.preventDefault();
        break;
    }

    if (newIndex !== currentIndex && elements[newIndex]) {
      elements[newIndex].focus();
      return newIndex;
    }

    return currentIndex;
  },

  /**
   * Create roving tabindex for component groups
   */
  createRovingTabindex: (elements, activeIndex = 0) => {
    return elements.map((element, index) => ({
      ...element,
      tabIndex: index === activeIndex ? 0 : -1,
      'aria-selected': index === activeIndex,
    }));
  },

  /**
   * Handle escape key to close modals/dropdowns
   */
  handleEscape: (callback) => (e) => {
    if (e.key === keyboardUtils.KEYS.ESCAPE) {
      e.preventDefault();
      callback(e);
    }
  },
};

/**
 * Focus management utilities
 */
export const focusUtils = {
  /**
   * Focus trap for modals and dialogs
   */
  createFocusTrap: (containerRef) => {
    const getFocusableElements = () => {
      if (!containerRef.current) return [];

      const focusableSelectors = [
        'button:not([disabled])',
        'input:not([disabled])',
        'select:not([disabled])',
        'textarea:not([disabled])',
        'a[href]',
        '[tabindex]:not([tabindex="-1"])',
        '[contenteditable="true"]'
      ].join(', ');

      return Array.from(containerRef.current.querySelectorAll(focusableSelectors));
    };

    const handleKeyDown = (e) => {
      if (e.key !== keyboardUtils.KEYS.TAB) return;

      const focusableElements = getFocusableElements();
      if (focusableElements.length === 0) return;

      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };

    const activate = () => {
      const focusableElements = getFocusableElements();
      if (focusableElements.length > 0) {
        focusableElements[0].focus();
      }
      document.addEventListener('keydown', handleKeyDown);
    };

    const deactivate = () => {
      document.removeEventListener('keydown', handleKeyDown);
    };

    return { activate, deactivate };
  },

  /**
   * Restore focus to previous element
   */
  createFocusRestore: () => {
    const previousActiveElement = document.activeElement;

    return () => {
      if (previousActiveElement && typeof previousActiveElement.focus === 'function') {
        previousActiveElement.focus();
      }
    };
  },

  /**
   * Focus first error in form validation
   */
  focusFirstError: (formRef, errorClass = '.ant-form-item-has-error') => {
    if (!formRef.current) return;

    const firstError = formRef.current.querySelector(`${errorClass} input, ${errorClass} textarea, ${errorClass} select`);
    if (firstError) {
      firstError.focus();
      firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  },

  /**
   * Manage focus for dynamic content
   */
  manageDynamicFocus: (newContentRef, announcement) => {
    if (newContentRef.current) {
      // Focus the new content
      newContentRef.current.focus();

      // Announce the change
      if (announcement) {
        ariaUtils.announce(announcement);
      }
    }
  },
};

/**
 * Color contrast utilities for WCAG compliance
 */
export const contrastUtils = {
  /**
   * Calculate relative luminance
   */
  getLuminance: (color) => {
    const rgb = contrastUtils.hexToRgb(color);
    if (!rgb) return 0;

    const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  },

  /**
   * Convert hex to RGB
   */
  hexToRgb: (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  },

  /**
   * Calculate contrast ratio between two colors
   */
  getContrastRatio: (color1, color2) => {
    const lum1 = contrastUtils.getLuminance(color1);
    const lum2 = contrastUtils.getLuminance(color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  },

  /**
   * Check if color combination meets WCAG standards
   */
  meetsWCAG: (foreground, background, level = 'AA', size = 'normal') => {
    const ratio = contrastUtils.getContrastRatio(foreground, background);

    if (level === 'AAA') {
      return size === 'large' ? ratio >= 4.5 : ratio >= 7;
    }

    return size === 'large' ? ratio >= 3 : ratio >= 4.5;
  },

  /**
   * Get accessible text color for background
   */
  getAccessibleTextColor: (backgroundColor) => {
    const whiteContrast = contrastUtils.getContrastRatio('#FFFFFF', backgroundColor);
    const blackContrast = contrastUtils.getContrastRatio('#000000', backgroundColor);

    return whiteContrast > blackContrast ? '#FFFFFF' : '#000000';
  },
};

/**
 * Accessible drag and drop utilities
 */
export const accessibleDragDrop = {
  /**
   * Create keyboard-accessible drag and drop
   */
  createKeyboardDragDrop: (items, onMove) => {
    let draggedIndex = null;
    let dropTargetIndex = null;

    const handleKeyDown = (e, index) => {
      switch (e.key) {
        case keyboardUtils.KEYS.SPACE:
          e.preventDefault();
          if (draggedIndex === null) {
            // Start drag
            draggedIndex = index;
            ariaUtils.announce(`Picked up item ${index + 1}. Use arrow keys to move, space to drop, escape to cancel.`);
          } else if (draggedIndex === index) {
            // Drop at current position
            if (dropTargetIndex !== null && dropTargetIndex !== draggedIndex) {
              onMove(draggedIndex, dropTargetIndex);
              ariaUtils.announce(`Moved item from position ${draggedIndex + 1} to position ${dropTargetIndex + 1}.`);
            }
            draggedIndex = null;
            dropTargetIndex = null;
          }
          break;

        case keyboardUtils.KEYS.ESCAPE:
          if (draggedIndex !== null) {
            e.preventDefault();
            draggedIndex = null;
            dropTargetIndex = null;
            ariaUtils.announce('Drag operation cancelled.');
          }
          break;

        case keyboardUtils.KEYS.ARROW_UP:
        case keyboardUtils.KEYS.ARROW_DOWN:
          if (draggedIndex !== null) {
            e.preventDefault();
            const direction = e.key === keyboardUtils.KEYS.ARROW_UP ? -1 : 1;
            const newIndex = Math.max(0, Math.min(items.length - 1, index + direction));
            if (newIndex !== index) {
              dropTargetIndex = newIndex;
              ariaUtils.announce(`Moving to position ${newIndex + 1}.`);
            }
          }
          break;
      }
    };

    return {
      handleKeyDown,
      getDragAttributes: (index) => ({
        'aria-grabbed': draggedIndex === index,
        'aria-dropeffect': draggedIndex !== null && draggedIndex !== index ? 'move' : 'none',
        'aria-describedby': `drag-instructions-${index}`,
      }),
    };
  },

  /**
   * Create drag instructions for screen readers
   */
  createDragInstructions: (id) => ({
    id: `drag-instructions-${id}`,
    className: 'sr-only',
    children: 'Press space to pick up this item. Use arrow keys to move it to a new position, then press space again to drop it. Press escape to cancel.'
  }),
};

/**
 * Screen reader utilities
 */
export const screenReaderUtils = {
  /**
   * Create screen reader only content
   */
  srOnly: (content) => ({
    className: 'sr-only',
    children: content,
    'aria-hidden': false,
  }),

  /**
   * Hide decorative content from screen readers
   */
  hideFromScreenReader: () => ({
    'aria-hidden': true,
  }),

  /**
   * Create accessible loading states
   */
  createLoadingState: (isLoading, loadingText = 'Loading...') => ({
    'aria-busy': isLoading,
    'aria-live': 'polite',
    'aria-label': isLoading ? loadingText : undefined,
  }),

  /**
   * Create accessible error states
   */
  createErrorState: (hasError, errorMessage) => ({
    'aria-invalid': hasError,
    'aria-describedby': hasError ? 'error-message' : undefined,
    role: hasError ? 'alert' : undefined,
  }),
};

/**
 * High contrast mode utilities
 */
export const highContrastUtils = {
  /**
   * Detect high contrast mode
   */
  isHighContrastMode: () => {
    return window.matchMedia('(prefers-contrast: high)').matches;
  },

  /**
   * Create high contrast styles
   */
  createHighContrastStyles: (baseStyles) => ({
    ...baseStyles,
    '@media (prefers-contrast: high)': {
      border: '2px solid',
      outline: '1px solid',
      backgroundColor: 'Canvas',
      color: 'CanvasText',
    },
  }),

  /**
   * Ensure minimum contrast in high contrast mode
   */
  ensureHighContrast: (element) => {
    if (highContrastUtils.isHighContrastMode()) {
      element.style.border = '2px solid';
      element.style.outline = '1px solid';
    }
  },
};

/**
 * Reduced motion utilities
 */
export const reducedMotionUtils = {
  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion: () => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },

  /**
   * Create motion-safe animations
   */
  createMotionSafeAnimation: (animation) => ({
    animation: reducedMotionUtils.prefersReducedMotion() ? 'none' : animation,
    transition: reducedMotionUtils.prefersReducedMotion() ? 'none' : theme.transitions.default,
  }),

  /**
   * Respect reduced motion preferences
   */
  respectReducedMotion: (styles) => ({
    ...styles,
    '@media (prefers-reduced-motion: reduce)': {
      animation: 'none !important',
      transition: 'none !important',
    },
  }),
};

/**
 * Accessibility testing utilities
 */
export const a11yTestUtils = {
  /**
   * Check if element has proper ARIA attributes
   */
  validateAriaAttributes: (element) => {
    const issues = [];

    // Check for required ARIA attributes
    if (element.getAttribute('role') === 'button' && !element.hasAttribute('aria-label') && !element.textContent.trim()) {
      issues.push('Button missing accessible name');
    }

    if (element.hasAttribute('aria-describedby')) {
      const describedById = element.getAttribute('aria-describedby');
      if (!document.getElementById(describedById)) {
        issues.push(`aria-describedby references non-existent element: ${describedById}`);
      }
    }

    if (element.hasAttribute('aria-labelledby')) {
      const labelledById = element.getAttribute('aria-labelledby');
      if (!document.getElementById(labelledById)) {
        issues.push(`aria-labelledby references non-existent element: ${labelledById}`);
      }
    }

    return issues;
  },

  /**
   * Check keyboard accessibility
   */
  validateKeyboardAccess: (element) => {
    const issues = [];
    const interactiveElements = ['button', 'input', 'select', 'textarea', 'a'];
    const tagName = element.tagName.toLowerCase();
    const role = element.getAttribute('role');

    if (interactiveElements.includes(tagName) || role === 'button') {
      if (element.tabIndex < 0 && !element.hasAttribute('disabled')) {
        issues.push('Interactive element not keyboard accessible');
      }
    }

    return issues;
  },

  /**
   * Check color contrast
   */
  validateColorContrast: (element) => {
    const issues = [];
    const computedStyle = window.getComputedStyle(element);
    const color = computedStyle.color;
    const backgroundColor = computedStyle.backgroundColor;

    if (color && backgroundColor && color !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
      const ratio = contrastUtils.getContrastRatio(color, backgroundColor);
      if (ratio < 4.5) {
        issues.push(`Insufficient color contrast: ${ratio.toFixed(2)}:1 (minimum 4.5:1)`);
      }
    }

    return issues;
  },

  /**
   * Run comprehensive accessibility audit
   */
  auditElement: (element) => {
    const issues = [
      ...a11yTestUtils.validateAriaAttributes(element),
      ...a11yTestUtils.validateKeyboardAccess(element),
      ...a11yTestUtils.validateColorContrast(element),
    ];

    return {
      element,
      issues,
      isAccessible: issues.length === 0,
    };
  },
};

/**
 * Accessibility monitoring utilities
 */
export const a11yMonitorUtils = {
  /**
   * Monitor focus changes for debugging
   */
  monitorFocus: (enabled = true) => {
    if (!enabled) return;

    let previousFocus = null;

    const handleFocusChange = (e) => {
      console.log('Focus changed:', {
        from: previousFocus,
        to: e.target,
        timestamp: new Date().toISOString(),
      });
      previousFocus = e.target;
    };

    document.addEventListener('focusin', handleFocusChange);
    document.addEventListener('focusout', handleFocusChange);

    return () => {
      document.removeEventListener('focusin', handleFocusChange);
      document.removeEventListener('focusout', handleFocusChange);
    };
  },

  /**
   * Monitor ARIA live region announcements
   */
  monitorLiveRegions: (enabled = true) => {
    if (!enabled) return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          const target = mutation.target;
          const ariaLive = target.getAttribute('aria-live');

          if (ariaLive) {
            console.log('Live region updated:', {
              element: target,
              content: target.textContent,
              priority: ariaLive,
              timestamp: new Date().toISOString(),
            });
          }
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => observer.disconnect();
  },

  /**
   * Log accessibility violations
   */
  logViolations: (violations) => {
    if (process.env.NODE_ENV === 'development') {
      console.group('Accessibility Violations');
      violations.forEach((violation, index) => {
        console.warn(`${index + 1}. ${violation.description}`, violation.element);
      });
      console.groupEnd();
    }
  },
};

// Export all utilities
export default {
  aria: ariaUtils,
  keyboard: keyboardUtils,
  focus: focusUtils,
  contrast: contrastUtils,
  dragDrop: accessibleDragDrop,
  screenReader: screenReaderUtils,
  highContrast: highContrastUtils,
  reducedMotion: reducedMotionUtils,
  testing: a11yTestUtils,
  monitoring: a11yMonitorUtils,
};
