"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[4673],{51379:(t,e,r)=>{r(58168),r(64467),r(5544),r(53986),r(46942),r(12533),r(16928),r(72065),r(96540)},71559:(t,e,r)=>{r.d(e,{jl:()=>E});var o=r(58168),n=r(89379),c=r(53986),a=r(96540),s=r(46942),i=r.n(s),l={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},u=r(82284),f=r(5544),p=r(20998),d=0,g=(0,p.A)();var k=function(t){var e=t.bg,r=t.children;return a.createElement("div",{style:{width:"100%",height:"100%",background:e}},r)};function h(t,e){return Object.keys(t).map((function(r){var o=parseFloat(r),n="".concat(Math.floor(o*e),"%");return"".concat(t[r]," ").concat(n)}))}const m=a.forwardRef((function(t,e){var r=t.prefixCls,o=t.color,n=t.gradientId,c=t.radius,s=t.style,i=t.ptg,l=t.strokeLinecap,f=t.strokeWidth,p=t.size,d=t.gapDegree,g=o&&"object"===(0,u.A)(o),m=g?"#FFF":void 0,y=p/2,v=a.createElement("circle",{className:"".concat(r,"-circle-path"),r:c,cx:y,cy:y,stroke:m,strokeLinecap:l,strokeWidth:f,opacity:0===i?0:1,style:s,ref:e});if(!g)return v;var b="".concat(n,"-conic"),E=d?"".concat(180+d/2,"deg"):"0deg",A=h(o,(360-d)/360),D=h(o,1),x="conic-gradient(from ".concat(E,", ").concat(A.join(", "),")"),C="linear-gradient(to ".concat(d?"bottom":"top",", ").concat(D.join(", "),")");return a.createElement(a.Fragment,null,a.createElement("mask",{id:b},v),a.createElement("foreignObject",{x:0,y:0,width:p,height:p,mask:"url(#".concat(b,")")},a.createElement(k,{bg:C},a.createElement(k,{bg:x}))))}));var y=function(t,e,r,o,n,c,a,s,i,l){var u=arguments.length>10&&void 0!==arguments[10]?arguments[10]:0,f=r/100*360*((360-c)/360),p=0===c?0:{bottom:0,top:180,left:90,right:-90}[a],d=(100-o)/100*e;return"round"===i&&100!==o&&(d+=l/2)>=e&&(d=e-.01),{stroke:"string"==typeof s?s:void 0,strokeDasharray:"".concat(e,"px ").concat(t),strokeDashoffset:d+u,transform:"rotate(".concat(n+f+p,"deg)"),transformOrigin:"".concat(50,"px ").concat(50,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},v=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function b(t){var e=null!=t?t:[];return Array.isArray(e)?e:[e]}const E=function(t){var e,r,s,p,k,h=(0,n.A)((0,n.A)({},l),t),E=h.id,A=h.prefixCls,D=h.steps,x=h.strokeWidth,C=h.trailWidth,W=h.gapDegree,j=void 0===W?0:W,w=h.gapPosition,L=h.trailColor,N=h.strokeLinecap,F=h.style,O=h.className,R=h.strokeColor,P=h.percent,S=(0,c.A)(h,v),_=function(t){var e=a.useState(),r=(0,f.A)(e,2),o=r[0],n=r[1];return a.useEffect((function(){var t;n("rc_progress_".concat((g?(t=d,d+=1):t="TEST_OR_SSR",t)))}),[]),t||o}(E),I="".concat(_,"-gradient"),M=50-x/2,z=2*Math.PI*M,T=j>0?90+j/2:-90,B=z*((360-j)/360),q="object"===(0,u.A)(D)?D:{count:D,gap:2},G=q.count,H=q.gap,J=b(P),K=b(R),Q=K.find((function(t){return t&&"object"===(0,u.A)(t)})),U=Q&&"object"===(0,u.A)(Q)?"butt":N,V=y(z,B,0,100,T,j,w,L,U,x),X=(p=(0,a.useRef)([]),k=(0,a.useRef)(null),(0,a.useEffect)((function(){var t=Date.now(),e=!1;p.current.forEach((function(r){if(r){e=!0;var o=r.style;o.transitionDuration=".3s, .3s, .3s, .06s",k.current&&t-k.current<100&&(o.transitionDuration="0s, 0s")}})),e&&(k.current=Date.now())})),p.current);return a.createElement("svg",(0,o.A)({className:i()("".concat(A,"-circle"),O),viewBox:"0 0 ".concat(100," ").concat(100),style:F,id:E,role:"presentation"},S),!G&&a.createElement("circle",{className:"".concat(A,"-circle-trail"),r:M,cx:50,cy:50,stroke:L,strokeLinecap:U,strokeWidth:C||x,style:V}),G?(e=Math.round(G*(J[0]/100)),r=100/G,s=0,new Array(G).fill(null).map((function(t,o){var n=o<=e-1?K[0]:L,c=n&&"object"===(0,u.A)(n)?"url(#".concat(I,")"):void 0,i=y(z,B,s,r,T,j,w,n,"butt",x,H);return s+=100*(B-i.strokeDashoffset+H)/B,a.createElement("circle",{key:o,className:"".concat(A,"-circle-path"),r:M,cx:50,cy:50,stroke:c,strokeWidth:x,opacity:1,style:i,ref:function(t){X[o]=t}})}))):function(){var t=0;return J.map((function(e,r){var o=K[r]||K[K.length-1],n=y(z,B,t,e,T,j,w,o,U,x);return t+=e,a.createElement(m,{key:r,color:o,ptg:e,radius:M,prefixCls:A,gradientId:I,style:n,strokeLinecap:U,strokeWidth:x,gapDegree:j,ref:function(t){X[r]=t},size:100})})).reverse()}())}}}]);