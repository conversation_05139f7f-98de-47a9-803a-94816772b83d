"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[792],{1606:(e,t,n)=>{n.d(t,{A:()=>p});var r=n(467),o=n(3029),a=n(2901),i=n(4756),s=n.n(i),c=n(1083),l="app_builder_auth_token",u="app_builder_user";const p=new(function(){return(0,a.A)((function e(){(0,o.A)(this,e),this.token=localStorage.getItem(l),this.user=JSON.parse(localStorage.getItem(u)||"null"),this.listeners=[],this.initInterceptors()}),[{key:"initInterceptors",value:function(){var e=this;c.A.interceptors.request.use((function(t){return e.token&&(t.headers.Authorization="Bearer ".concat(e.token)),t}),(function(e){return Promise.reject(e)})),c.A.interceptors.response.use((function(e){return e}),(function(t){return t.response&&401===t.response.status&&e.logout(),Promise.reject(t)}))}},{key:"login",value:(t=(0,r.A)(s().mark((function e(t,n){var r,o,a,i;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,c.A.post("/api/auth/login",{email:t,password:n});case 3:return r=e.sent,o=r.data,a=o.token,i=o.user,this.setToken(a),this.setUser(i),this.notifyListeners("login",i),e.abrupt("return",i);case 11:throw e.prev=11,e.t0=e.catch(0),console.error("Login error:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,this,[[0,11]])}))),function(e,n){return t.apply(this,arguments)})},{key:"register",value:(e=(0,r.A)(s().mark((function e(t,n,r){var o,a,i,l;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,c.A.post("/api/auth/register",{name:t,email:n,password:r});case 3:return o=e.sent,a=o.data,i=a.token,l=a.user,this.setToken(i),this.setUser(l),this.notifyListeners("register",l),e.abrupt("return",l);case 11:throw e.prev=11,e.t0=e.catch(0),console.error("Register error:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,this,[[0,11]])}))),function(t,n,r){return e.apply(this,arguments)})},{key:"logout",value:function(){this.setToken(null),this.setUser(null),this.notifyListeners("logout")}},{key:"getUser",value:function(){return this.user}},{key:"isAuthenticated",value:function(){return!!this.token}},{key:"setToken",value:function(e){this.token=e,e?localStorage.setItem(l,e):localStorage.removeItem(l)}},{key:"setUser",value:function(e){this.user=e,e?localStorage.setItem(u,JSON.stringify(e)):localStorage.removeItem(u)}},{key:"addListener",value:function(e){var t=this;return this.listeners.push(e),function(){t.listeners=t.listeners.filter((function(t){return t!==e}))}}},{key:"notifyListeners",value:function(e,t){this.listeners.forEach((function(n){try{n(e,t)}catch(e){console.error("Error in auth listener:",e)}}))}}]);var e,t}())},1616:(e,t,n)=>{n.d(t,{$5:()=>v,Ic:()=>C,Q3:()=>d,Qo:()=>O,RT:()=>A,S7:()=>g,V_:()=>w,X8:()=>m,ZL:()=>f,ZP:()=>y,eL:()=>b,gA:()=>S,vr:()=>E,xx:()=>k,zp:()=>_});var r=n(467),o=n(4756),a=n.n(o),i="ADD_COMPONENT",s="ADD_LAYOUT",c="FETCH_APP_DATA_SUCCESS",l="FETCH_APP_DATA_ERROR",u="UPDATE_COMPONENT",p="DELETE_COMPONENT",d={ADD_COMPONENT:i,ADD_LAYOUT:s,ADD_STYLE:"ADD_STYLE",ADD_DATA:"ADD_DATA",FETCH_APP_DATA_SUCCESS:c,FETCH_APP_DATA_ERROR:l,WS_CONNECT:"WS_CONNECT",WS_CONNECTED:"WS_CONNECTED",WS_DISCONNECTED:"WS_DISCONNECTED",WS_MESSAGE_RECEIVED:"WS_MESSAGE_RECEIVED",WS_ERROR:"WS_ERROR",UPDATE_COMPONENT:u,DELETE_COMPONENT:p,UPDATE_LAYOUT:"UPDATE_LAYOUT",DELETE_LAYOUT:"DELETE_LAYOUT",SAVE_APP_DATA:"SAVE_APP_DATA",LOAD_APP_DATA:"LOAD_APP_DATA",SET_LOADING:"SET_LOADING",SET_ERROR:"SET_ERROR",CLEAR_ERROR:"CLEAR_ERROR"},m=function(e){return{type:i,payload:e}},g=function(e){return{type:s,payload:{type:e,components:arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],styles:arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}}}},h=function(e){return{type:c,payload:e}},f=function(e){return function(){var t=(0,r.A)(a().mark((function t(n){var r,o;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("".concat("http://localhost:8000","/api/app-data/"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});case 3:return r=t.sent,t.next=6,r.json();case 6:o=t.sent,n(h(o)),t.next=14;break;case 10:t.prev=10,t.t0=t.catch(0),console.error("Error saving app data:",t.t0),n((a=t.t0.message,{type:l,payload:a}));case 14:case"end":return t.stop()}var a}),t,null,[[0,10]])})));return function(e){return t.apply(this,arguments)}}()},y=function(e,t){return{type:u,payload:{index:e,updates:t}}},v=function(e){return{type:p,payload:{index:e}}},A=function(){return{type:"LOAD_PROJECTS"}},b=function(e){return{type:"SET_ACTIVE_PROJECT",payload:e}},E=function(e){return{type:"UPDATE_PROJECT",payload:e}},S=function(e){return{type:"CREATE_PROJECT",payload:e}},k=function(e){return{type:"DELETE_PROJECT",payload:e}},_=function(e){return{type:"ADD_THEME",payload:e}},w=function(e){return{type:"UPDATE_THEME",payload:e}},O=function(e){return{type:"REMOVE_THEME",payload:{id:e}}},C=function(e){return{type:"SET_ACTIVE_THEME",payload:e}}},3385:(e,t,n)=>{n.d(t,{HP:()=>A,NP:()=>y,rV:()=>b});var r=n(5544),o=n(4467),a=n(6540),i=n(9249),s=n(9467),c=n(7355),l=n(778);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var d="light",m="dark",g="system",h=(0,o.A)((0,o.A)({},d,{primary:"#1976d2",secondary:"#f50057",background:"#ffffff",surface:"#f5f5f5",text:"#212121",textSecondary:"#757575",border:"#e0e0e0",error:"#d32f2f",warning:"#f57c00",info:"#0288d1",success:"#388e3c"}),m,{primary:"#90caf9",secondary:"#f48fb1",background:"#121212",surface:"#1e1e1e",text:"#ffffff",textSecondary:"#b0b0b0",border:"#333333",error:"#f44336",warning:"#ff9800",info:"#29b6f6",success:"#66bb6a"}),f=(0,a.createContext)({theme:d,colors:h[d],setTheme:function(){},setCustomColors:function(){}}),y=function(e){var t=e.children,n=e.initialTheme,o=void 0===n?g:n,i=(0,a.useState)(o),s=(0,r.A)(i,2),c=s[0],l=s[1],u=(0,a.useState)({}),y=(0,r.A)(u,2),v=y[0],A=y[1],b=(0,a.useState)(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?m:d),E=(0,r.A)(b,2),S=E[0],k=E[1],_=function(){return c===g?h[S]:"custom"===c?p(p({},h[d]),v):h[c]};return(0,a.useEffect)((function(){var e=_();Object.entries(e).forEach((function(e){var t=(0,r.A)(e,2),n=t[0],o=t[1];document.documentElement.style.setProperty("--color-".concat(n),o)}));var t=c===g?S:c;document.documentElement.setAttribute("data-theme",t),t===m?document.body.classList.add("dark-theme"):document.body.classList.remove("dark-theme")}),[c,S,v]),(0,a.useEffect)((function(){var e=window.matchMedia("(prefers-color-scheme: dark)"),t=function(e){k(e.matches?m:d)};e.addEventListener?e.addEventListener("change",t):e.addListener(t);var n=localStorage.getItem("app_theme");return n&&l(n),function(){e.removeEventListener?e.removeEventListener("change",t):e.removeListener(t)}}),[]),a.createElement(f.Provider,{value:{theme:c,colors:_(),setTheme:function(e){l(e),localStorage.setItem("app_theme",e)},setCustomColors:A}},t)},v=function(){var e=(0,a.useContext)(f);if(!e)throw new Error("useTheme must be used within a ThemeProvider");return e},A=function(e){var t=e.position,n=void 0===t?"right":t,r=v(),o=r.theme,s=r.setTheme;return a.createElement("div",{className:"theme-switcher ".concat(n)},a.createElement(i.Ay,{type:"text",icon:a.createElement(l.A,null),onClick:function(){s(o===m?d:m)},"aria-label":"Switch to ".concat(o===m?"light":"dark"," theme")}))},b=function(e){var t=e.onSave,n=v(),o=n.colors,l=n.setCustomColors,u=s.A.useForm(),p=(0,r.A)(u,1)[0];return(0,a.useEffect)((function(){p.setFieldsValue({primaryColor:o.primary,secondaryColor:o.secondary,backgroundColor:o.background,textColor:o.text})}),[o,p]),a.createElement("div",{className:"theme-customizer"},a.createElement(s.A,{form:p,layout:"vertical",onFinish:function(e){l(e),t&&t(e)}},a.createElement(s.A.Item,{name:"primaryColor",label:"Primary Color"},a.createElement(c.A,{type:"color"})),a.createElement(s.A.Item,{name:"secondaryColor",label:"Secondary Color"},a.createElement(c.A,{type:"color"})),a.createElement(s.A.Item,{name:"backgroundColor",label:"Background Color"},a.createElement(c.A,{type:"color"})),a.createElement(s.A.Item,{name:"textColor",label:"Text Color"},a.createElement(c.A,{type:"color"})),a.createElement(s.A.Item,null,a.createElement(i.Ay,{type:"primary",htmlType:"submit"},"Apply Theme"))))}},4318:(e,t,n)=>{n.d(t,{AS:()=>d,D:()=>E,H2:()=>l,Kg:()=>r,Pe:()=>y,RH:()=>c,Te:()=>i,U_:()=>A,WD:()=>p,YG:()=>a,ZH:()=>u,_E:()=>k,co:()=>o,ei:()=>g,gV:()=>v,gk:()=>b,oz:()=>m,uV:()=>s,vs:()=>f,wH:()=>S,xS:()=>h});var r="WEBSOCKET_CONNECTED",o="WEBSOCKET_DISCONNECTED",a="WS_CONNECT",i="WS_CONNECTED",s="WS_DISCONNECT",c="WS_DISCONNECTED",l="WS_MESSAGE",u="WS_MESSAGE_RECEIVED",p="WS_SEND_MESSAGE",d="WS_ERROR",m="ADD_COMPONENT",g="UPDATE_COMPONENT",h="REMOVE_COMPONENT",f="ADD_LAYOUT",y="UPDATE_LAYOUT",v="REMOVE_LAYOUT",A="ADD_THEME",b="UPDATE_THEME",E="REMOVE_THEME",S="SET_ACTIVE_THEME",k="TOGGLE_AUTO_APPLY_THEME"},4465:(e,t,n)=>{var r,o,a,i,s,c,l,u=n(6540),p=n(5338),d=n(1468),m=n(8035),g=n(4976),h=n(7767),f=n(7528),y=n(1250),v=n(3385),A=y.Ay.div(r||(r=(0,f.A)(["\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: var(--background-color);\n  color: var(--text-color);\n  transition: background-color 0.3s ease, color 0.3s ease;\n"]))),b=y.Ay.header(o||(o=(0,f.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 20px;\n  height: 64px;\n  background-color: var(--background-secondary, #f5f5f5);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n"]))),E=y.Ay.div(a||(a=(0,f.A)(["\n  font-size: 1.5rem;\n  font-weight: bold;\n\n  a {\n    color: var(--primary-color);\n    text-decoration: none;\n\n    &:hover {\n      text-decoration: none;\n      opacity: 0.9;\n    }\n  }\n"]))),S=y.Ay.nav(i||(i=(0,f.A)(["\n  ul {\n    display: flex;\n    list-style: none;\n    margin: 0;\n    padding: 0;\n\n    li {\n      margin-left: 20px;\n\n      a {\n        color: var(--text-color);\n        text-decoration: none;\n        padding: 8px 12px;\n        border-radius: 4px;\n        transition: background-color 0.3s ease;\n\n        &:hover {\n          background-color: rgba(0, 0, 0, 0.05);\n        }\n\n        &.active {\n          color: var(--primary-color);\n          font-weight: 500;\n        }\n      }\n    }\n  }\n"]))),k=y.Ay.main(s||(s=(0,f.A)(["\n  flex: 1;\n  padding: 20px;\n"]))),_=y.Ay.footer(c||(c=(0,f.A)(["\n  padding: 20px;\n  text-align: center;\n  background-color: var(--background-secondary, #f5f5f5);\n  border-top: 1px solid var(--border-color, #e8e8e8);\n"]))),w=y.Ay.div(l||(l=(0,f.A)(["\n  display: flex;\n  align-items: center;\n"])));const O=function(e){var t=e.children;return u.createElement(A,{className:"app-layout"},u.createElement(b,{className:"app-header"},u.createElement(E,{className:"logo"},u.createElement(g.N_,{to:"/"},"App Builder")),u.createElement(S,{className:"main-nav"},u.createElement("ul",null,u.createElement("li",null,u.createElement(g.N_,{to:"/"},"Home")),u.createElement("li",null,u.createElement(g.N_,{to:"/app-builder"},"App Builder")),u.createElement("li",null,u.createElement(g.N_,{to:"/templates"},"Templates")),u.createElement("li",null,u.createElement(g.N_,{to:"/websocket"},"WebSocket")))),u.createElement(w,null,u.createElement(v.HP,null))),u.createElement(k,{className:"app-main"},t),u.createElement(_,{className:"app-footer"},u.createElement("p",null,"© ",(new Date).getFullYear()," App Builder")))};var C=n(4467),x=n(9029),T=n(3567);function P(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function D(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?P(Object(n),!0).forEach((function(t){(0,C.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):P(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const R=function(e){var t=e.tip,n=void 0===t?"Loading...":t,r=e.size,o=void 0===r?"large":r,a=e.fullScreen,i=void 0!==a&&a,s=e.backgroundColor,c=void 0===s?"rgba(255, 255, 255, 0.8)":s,l=e.icon,p=void 0===l?null:l,d=e.className,m=void 0===d?"":d,g=e.style,h=void 0===g?{}:g,f=p||u.createElement(T.A,{style:{fontSize:"large"===o?40:24},spin:!0});return i?u.createElement("div",{className:"loading-container ".concat(m),style:D({backgroundColor:c},h),"aria-live":"polite","aria-busy":"true"},u.createElement("div",{className:"loading-content"},u.createElement(x.A,{className:"loading-spinner",indicator:f,size:o}),n&&u.createElement("div",{className:"loading-text",role:"status"},n))):u.createElement("div",{style:D({textAlign:"center",padding:"20px"},h),className:m,"aria-live":"polite","aria-busy":"true"},u.createElement(x.A,{indicator:f,size:o}),n&&u.createElement("div",{style:{marginTop:"12px"},role:"status"},n))};var I,W=n(5544),F=n(1606),N=n(3016),L=n(7197),U=n(2702),M=N.A.Text,z=y.Ay.div(I||(I=(0,f.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  width: 100%;\n  position: fixed;\n  top: 0;\n  left: 0;\n  background: rgba(255, 255, 255, 0.95);\n  z-index: 1000;\n"])));const j=function(e){var t=e.loading,n=void 0===t||t,r=e.message,o=void 0===r?"Loading...":r,a=e.description,i=void 0===a?"":a,s=e.size,c=void 0===s?"default":s,l=e.fullPage,p=void 0!==l&&l,d=e.children,m=void 0===d?null:d,g=e.error,h=void 0===g?null:g,f=e.retry,y=void 0===f?null:f;if(!n&&!h)return u.createElement(u.Fragment,null,m);var v=u.createElement(T.A,{style:{fontSize:"large"===c?40:24},spin:!0}),A=h?u.createElement(L.A,{message:h.title||"Error",description:u.createElement(U.A,{direction:"vertical"},u.createElement(M,null,h.message||"An error occurred"),y&&u.createElement("a",{onClick:y},"Try again")),type:"error",showIcon:!0}):u.createElement(x.A,{indicator:v,size:c},u.createElement("div",{style:{padding:o?"30px":"50px"}},o&&u.createElement("div",{style:{marginTop:8}},o),i&&u.createElement("div",{style:{marginTop:4,color:"rgba(0, 0, 0, 0.45)"}},i)));return p?u.createElement(z,null,A):A},H=function(e){var t=e.children,n=e.redirectTo,r=void 0===n?"/login":n,o=e.roles,a=void 0===o?[]:o,i=e.loadingFallback,s=void 0===i?null:i,c=(0,u.useState)(!0),l=(0,W.A)(c,2),p=l[0],d=l[1],m=(0,u.useState)(!1),g=(0,W.A)(m,2),f=g[0],y=g[1],v=(0,u.useState)(!0),A=(0,W.A)(v,2),b=A[0],E=A[1];if((0,u.useEffect)((function(){var e=F.A.isAuthenticated();if(y(e),e&&a.length>0){var t=F.A.getUser(),n=(null==t?void 0:t.roles)||[],r=a.some((function(e){return n.includes(e)}));E(r)}d(!1)}),[a]),(0,u.useEffect)((function(){return F.A.addListener((function(e){if("login"===e||"register"===e){if(y(!0),a.length>0){var t=F.A.getUser(),n=(null==t?void 0:t.roles)||[],r=a.some((function(e){return n.includes(e)}));E(r)}}else"logout"===e&&(y(!1),E(!1))}))}),[a]),p)return s||u.createElement(j,{loading:!0,message:"Checking authentication...",fullPage:!0});if(!f){var S="".concat(r,"?redirect=").concat(encodeURIComponent(window.location.pathname));return u.createElement(h.C5,{to:S,replace:!0})}return b?t:u.createElement(h.C5,{to:"/unauthorized",replace:!0})};var B=n(9391),G=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(913)]).then(n.bind(n,3913))})),V=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(548)]).then(n.bind(n,3548))})),q=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(430)]).then(n.bind(n,3430))})),Q=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(498)]).then(n.bind(n,2498))})),K=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(602)]).then(n.bind(n,1602))})),Y=(0,u.lazy)((function(){return n.e(253).then(n.bind(n,253))})),J=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(368)]).then(n.bind(n,368))})),X=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(67)]).then(n.bind(n,4067))})),$=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(831)]).then(n.bind(n,5831))})),Z=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(167)]).then(n.bind(n,6548))})),ee=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(552)]).then(n.bind(n,8552))})),te=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(435)]).then(n.bind(n,7435))})),ne=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(730)]).then(n.bind(n,3730))})),re=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(934)]).then(n.bind(n,1934))})),oe=(0,u.lazy)((function(){return n.e(375).then(n.bind(n,1375))})),ae=(0,u.lazy)((function(){return Promise.all([n.e(76),n.e(905)]).then(n.bind(n,4905))})),ie=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(177)]).then(n.bind(n,1177))})),se=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(160)]).then(n.bind(n,1160))})),ce=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(76),n.e(553)]).then(n.bind(n,2553))})),le=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(772)]).then(n.bind(n,7772))})),ue=(0,u.lazy)((function(){return Promise.all([n.e(874),n.e(96),n.e(202)]).then(n.bind(n,5202))})),pe=function(){return u.createElement(R,{tip:"Loading page...",fullScreen:!0,backgroundColor:"rgba(255, 255, 255, 0.9)"})};const de=function(){var e=(0,B.As)().isAuthenticated;return u.createElement(O,null,u.createElement(u.Suspense,{fallback:u.createElement(pe,null)},u.createElement(h.BV,null,u.createElement(h.qh,{path:"/",element:u.createElement(V,null)}),u.createElement(h.qh,{path:"/home",element:u.createElement(G,null)}),u.createElement(h.qh,{path:"/home-mvp",element:u.createElement(V,null)}),u.createElement(h.qh,{path:"/mvp",element:u.createElement(q,null)}),u.createElement(h.qh,{path:"/templates",element:u.createElement(ue,null)}),u.createElement(h.qh,{path:"/login",element:e?u.createElement(h.C5,{to:"/dashboard"}):u.createElement(X,null)}),u.createElement(h.qh,{path:"/register",element:e?u.createElement(h.C5,{to:"/dashboard"}):u.createElement($,null)}),u.createElement(h.qh,{path:"/forgot-password",element:e?u.createElement(h.C5,{to:"/dashboard"}):u.createElement(Z,null)}),u.createElement(h.qh,{path:"/reset-password/:token",element:e?u.createElement(h.C5,{to:"/dashboard"}):u.createElement(ee,null)}),u.createElement(h.qh,{path:"/dashboard",element:u.createElement(H,null,u.createElement(re,null))}),u.createElement(h.qh,{path:"/app-builder",element:u.createElement(H,null,u.createElement(Q,null))}),u.createElement(h.qh,{path:"/websocket",element:u.createElement(H,null,u.createElement(K,null))}),u.createElement(h.qh,{path:"/profile",element:u.createElement(H,null,u.createElement(te,null))}),u.createElement(h.qh,{path:"/projects",element:u.createElement(H,null,u.createElement(ne,null))}),u.createElement(h.qh,{path:"/settings",element:u.createElement(H,null,u.createElement(oe,null))}),u.createElement(h.qh,{path:"/theme-test",element:u.createElement(ae,null)}),u.createElement(h.qh,{path:"/dark-mode-test",element:u.createElement(ie,null)}),u.createElement(h.qh,{path:"/contrast-test",element:u.createElement(se,null)}),u.createElement(h.qh,{path:"/header-contrast-test",element:u.createElement(ce,null)}),u.createElement(h.qh,{path:"/service-worker-test",element:u.createElement(le,null)}),u.createElement(h.qh,{path:"/unauthorized",element:u.createElement(J,null)}),u.createElement(h.qh,{path:"/404",element:u.createElement(Y,null)}),u.createElement(h.qh,{path:"*",element:u.createElement(h.C5,{to:"/404"})}))))};var me=n(3029),ge=n(2901),he=n(6822),fe=n(3954),ye=n(5501),ve=n(5556),Ae=n.n(ve),be=n(6020),Ee=n(6191),Se=n(378),ke=n(6893),_e=n(581),we=n(8572),Oe=n(436),Ce=n(467),xe=n(4756),Te=n.n(xe);function Pe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function De(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Pe(Object(n),!0).forEach((function(t){(0,C.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Re={enabled:!0,samplingRate:1,errorLimit:100,breadcrumbLimit:50,ignoredErrors:[/ResizeObserver loop limit exceeded/,/Loading chunk \d+ failed/,/Network request failed/,/Script error/,/Extension context invalidated/,/Failed to report error/,/Error reporting failed/,/TypeError: Failed to fetch/,/TypeError: NetworkError when attempting to fetch resource/,/AbortError/,/Request aborted/,/Request timed out/,/Load failed/],reportingEndpoint:"/api/errors",logToConsole:!0,captureConsoleErrors:!0,captureNetworkErrors:!0,captureUnhandledRejections:!0,captureBreadcrumbs:!0},Ie={state:"CLOSED",failureCount:0,lastFailureTime:null,failureThreshold:5,timeout:6e4,successThreshold:2},We={errors:[],breadcrumbs:[],sessionId:"".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),startTime:(new Date).toISOString(),reportingQueue:[],lastReportAttempt:null,reportingInProgress:!1};function Fe(e){e.preventDefault(),Le({type:"uncaught_error",message:e.message||"Unknown error",stack:e.error?e.error.stack:null,source:e.filename,line:e.lineno,column:e.colno,timestamp:(new Date).toISOString()}),Re.logToConsole&&console.error("Uncaught error:",e.message)}function Ne(e){e.preventDefault();var t=e.reason,n=t instanceof Error?t.message:String(t);Le({type:"unhandled_rejection",message:n||"Unhandled promise rejection",stack:t instanceof Error?t.stack:null,timestamp:(new Date).toISOString()}),Re.logToConsole&&console.error("Unhandled rejection:",n)}function Le(e){Re.enabled&&(Math.random()>Re.samplingRate||function(e){return Re.ignoredErrors.some((function(t){return t instanceof RegExp?t.test(e.message):e.message.includes(t)}))}(e)||(e.sessionId=We.sessionId,e.userAgent=navigator.userAgent,e.url=window.location.href,e.breadcrumbs=(0,Oe.A)(We.breadcrumbs),We.errors.push(e),We.errors.length>Re.errorLimit&&We.errors.shift(),Me(e)))}function Ue(e){Re.captureBreadcrumbs&&(We.breadcrumbs.push(e),We.breadcrumbs.length>Re.breadcrumbLimit&&We.breadcrumbs.shift())}function Me(e){var t,n,r,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;if(Re.reportingEndpoint)if(r=Date.now(),"OPEN"!==Ie.state||r-Ie.lastFailureTime>=Ie.timeout&&(Ie.state="HALF_OPEN",1)){if(!("error_reporting_failure"===e.type||null!==(t=e.message)&&void 0!==t&&t.includes("Failed to report error")||null!==(n=e.url)&&void 0!==n&&n.includes(Re.reportingEndpoint)))if(We.reportingInProgress)We.reportingQueue.push(e);else{We.reportingInProgress=!0,We.lastReportAttempt=Date.now();var i=Math.min(1e3*Math.pow(2,o),3e4),s=function(){(window._originalFetch||window.fetch)(Re.reportingEndpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e),keepalive:!0}).then((function(e){if(!e.ok)throw new Error("HTTP ".concat(e.status,": ").concat(e.statusText));if("HALF_OPEN"===Ie.state?(Ie.successThreshold--,Ie.successThreshold<=0&&(Ie.state="CLOSED",Ie.failureCount=0,Ie.successThreshold=2)):"CLOSED"===Ie.state&&(Ie.failureCount=0),"CLOSED"===Ie.state&&We.reportingQueue.length>0){var t=We.reportingQueue.shift();setTimeout((function(){return Me(t)}),100)}})).catch((function(t){Ie.failureCount++,Ie.lastFailureTime=Date.now(),Ie.failureCount>=Ie.failureThreshold&&(Ie.state="OPEN"),o<a?setTimeout((function(){Me(e,o+1,a)}),i):(function(e){try{var t="errorTracker_failedReports",n=localStorage.getItem(t),r=n?JSON.parse(n):[];r.push(De(De({},e),{},{storedAt:(new Date).toISOString()})),r.length>50&&r.splice(0,r.length-50),localStorage.setItem(t,JSON.stringify(r))}catch(e){Re.logToConsole}}(e),Re.logToConsole)})).finally((function(){We.reportingInProgress=!1}))};o>0?setTimeout(s,i):s()}}else We.reportingQueue.push(e)}function ze(){return{trackError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Le(e instanceof Error?De(De({type:"manual",message:e.message,stack:e.stack},t),{},{timestamp:(new Date).toISOString()}):De(De({type:"manual",message:String(e)},t),{},{timestamp:(new Date).toISOString()}))},addBreadcrumb:function(e){Ue({type:"manual",category:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"manual",message:e,data:arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},timestamp:(new Date).toISOString()})},getErrors:function(){return(0,Oe.A)(We.errors)},getBreadcrumbs:function(){return(0,Oe.A)(We.breadcrumbs)},clearErrors:function(){We.errors=[]},clearBreadcrumbs:function(){We.breadcrumbs=[]},getConfig:function(){return De({},Re)},updateConfig:function(e){Object.assign(Re,e)},getCircuitBreakerStatus:function(){return{state:Ie.state,failureCount:Ie.failureCount,lastFailureTime:Ie.lastFailureTime,queueLength:We.reportingQueue.length}},retryQueuedErrors:function(){return new Promise((function(e){0!==We.reportingQueue.length?("OPEN"===Ie.state&&(Ie.state="HALF_OPEN"),Me(We.reportingQueue.shift()),setTimeout(e,1e3)):e()}))},getLocallyStoredErrors:function(){try{var e=localStorage.getItem("errorTracker_failedReports");return e?JSON.parse(e):[]}catch(e){return[]}},clearLocallyStoredErrors:function(){try{localStorage.removeItem("errorTracker_failedReports")}catch(e){}}}}const je=function(){var e,t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.assign(Re,n),Re.enabled?(window.addEventListener("error",Fe),window.addEventListener("unhandledrejection",Ne),Re.captureConsoleErrors&&(e=console.error,t=console.warn,console.error=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];e.apply(console,n),Le({type:"console_error",message:n.map((function(e){return"string"==typeof e?e:JSON.stringify(e)})).join(" "),timestamp:(new Date).toISOString()})},console.warn=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t.apply(console,n),Re.captureBreadcrumbs&&Ue({type:"console_warn",message:n.map((function(e){return"string"==typeof e?e:JSON.stringify(e)})).join(" "),timestamp:(new Date).toISOString()})}),Re.captureNetworkErrors&&function(){window._originalFetch||(window._originalFetch=window.fetch);var e=window._originalFetch;window.fetch=(0,Ce.A)(Te().mark((function t(){var n,r,o,a,i,s,c,l=arguments;return Te().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:for(n=l.length,r=new Array(n),o=0;o<n;o++)r[o]=l[o];return t.prev=1,t.next=4,e.apply(window,r);case 4:return a=t.sent,Re.captureBreadcrumbs&&Ue({type:"network",category:"fetch",data:{url:"string"==typeof r[0]?r[0]:r[0].url,method:(null===(i=r[1])||void 0===i?void 0:i.method)||"GET",status:a.status},timestamp:(new Date).toISOString()}),a.ok||Le({type:"network_error",message:"Fetch error: ".concat(a.status," ").concat(a.statusText),data:{url:"string"==typeof r[0]?r[0]:r[0].url,method:(null===(s=r[1])||void 0===s?void 0:s.method)||"GET",status:a.status,statusText:a.statusText},timestamp:(new Date).toISOString()}),t.abrupt("return",a);case 10:throw t.prev=10,t.t0=t.catch(1),Le({type:"network_error",message:"Fetch failed: ".concat(t.t0.message),stack:t.t0.stack,data:{url:"string"==typeof r[0]?r[0]:null===(c=r[0])||void 0===c?void 0:c.url},timestamp:(new Date).toISOString()}),t.t0;case 14:case"end":return t.stop()}}),t,null,[[1,10]])})));var t=XMLHttpRequest.prototype.open,n=XMLHttpRequest.prototype.send;XMLHttpRequest.prototype.open=function(e,n){return this._errorTracking={method:e,url:n},t.apply(this,arguments)},XMLHttpRequest.prototype.send=function(){return this.addEventListener("load",(function(){var e,t,n,r;Re.captureBreadcrumbs&&Ue({type:"network",category:"xhr",data:{url:null===(e=this._errorTracking)||void 0===e?void 0:e.url,method:null===(t=this._errorTracking)||void 0===t?void 0:t.method,status:this.status},timestamp:(new Date).toISOString()}),this.status>=400&&Le({type:"network_error",message:"XHR error: ".concat(this.status," ").concat(this.statusText),data:{url:null===(n=this._errorTracking)||void 0===n?void 0:n.url,method:null===(r=this._errorTracking)||void 0===r?void 0:r.method,status:this.status,statusText:this.statusText},timestamp:(new Date).toISOString()})})),this.addEventListener("error",(function(){var e,t;Le({type:"network_error",message:"XHR failed",data:{url:null===(e=this._errorTracking)||void 0===e?void 0:e.url,method:null===(t=this._errorTracking)||void 0===t?void 0:t.method},timestamp:(new Date).toISOString()})})),n.apply(this,arguments)}}(),Re.captureBreadcrumbs&&(document.addEventListener("click",(function(e){var t,n=e.target,r=n.tagName.toLowerCase(),o=n.id?"#".concat(n.id):"",a=Array.from(n.classList).map((function(e){return".".concat(e)})).join(""),i=null===(t=n.innerText)||void 0===t?void 0:t.substring(0,50);Ue({type:"user",category:"click",data:{element:"".concat(r).concat(o).concat(a),text:i},timestamp:(new Date).toISOString()})})),window.addEventListener("popstate",(function(){Ue({type:"navigation",data:{from:document.referrer,to:window.location.href},timestamp:(new Date).toISOString()})}))),console.log("Error tracking initialized"),ze()):(console.log("Error tracking is disabled"),ze())}();var He,Be,Ge,Ve,qe,Qe;function Ke(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Ke=function(){return!!e})()}var Ye=y.Ay.div(He||(He=(0,f.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ",";\n  background-color: ",";\n  border-radius: ",";\n  box-shadow: ",";\n  max-width: 800px;\n  margin: 0 auto;\n  text-align: center;\n"])),be.Ay.spacing[6],be.Ay.colors.neutral[100],be.Ay.borderRadius.lg,be.Ay.shadows.md),Je=y.Ay.div(Be||(Be=(0,f.A)(["\n  font-size: 48px;\n  color: ",";\n  margin-bottom: ",";\n"])),be.Ay.colors.error.main,be.Ay.spacing[4]),Xe=y.Ay.h2(Ge||(Ge=(0,f.A)(["\n  font-size: ",";\n  color: ",";\n  margin-bottom: ",";\n"])),be.Ay.typography.fontSize.xl,be.Ay.colors.neutral[900],be.Ay.spacing[3]),$e=y.Ay.p(Ve||(Ve=(0,f.A)(["\n  font-size: ",";\n  color: ",";\n  margin-bottom: ",";\n"])),be.Ay.typography.fontSize.md,be.Ay.colors.neutral[700],be.Ay.spacing[4]),Ze=y.Ay.div(qe||(qe=(0,f.A)(["\n  background-color: ",";\n  padding: ",";\n  border-radius: ",";\n  margin-bottom: ",";\n  text-align: left;\n  overflow: auto;\n  max-height: 200px;\n  width: 100%;\n  font-family: ",";\n  font-size: ",";\n"])),be.Ay.colors.neutral[200],be.Ay.spacing[3],be.Ay.borderRadius.md,be.Ay.spacing[4],be.Ay.typography.fontFamily.code,be.Ay.typography.fontSize.sm),et=y.Ay.div(Qe||(Qe=(0,f.A)(["\n  display: flex;\n  gap: ",";\n  margin-top: ",";\n"])),be.Ay.spacing[3],be.Ay.spacing[4]),tt=function(e){function t(e){var n,r,o,a;return(0,me.A)(this,t),r=this,o=t,a=[e],o=(0,fe.A)(o),n=(0,he.A)(r,Ke()?Reflect.construct(o,a||[],(0,fe.A)(r).constructor):o.apply(r,a)),(0,C.A)(n,"handleReload",(function(){window.location.reload()})),(0,C.A)(n,"handleReset",(function(){n.setState({hasError:!1,error:null,errorInfo:null})})),(0,C.A)(n,"handleGoHome",(function(){window.location.href="/"})),n.state={hasError:!1,error:null,errorInfo:null,errorCount:0},n}return(0,ye.A)(t,e),(0,ge.A)(t,[{key:"componentDidCatch",value:function(e,t){console.error("Error caught by ErrorBoundary:",e,t),je.trackError(e,{componentStack:t.componentStack,source:"react_error_boundary",component:this.constructor.name,props:JSON.stringify(this.props)}),je.addBreadcrumb("Error in component: ".concat(e.message),"error_boundary",{componentStack:t.componentStack}),this.setState((function(e){return{errorInfo:t,errorCount:e.errorCount+1}})),this.props.onError&&this.props.onError(e,t)}},{key:"render",value:function(){var e=this.state,t=e.hasError,n=e.error,r=e.errorInfo,o=e.errorCount,a=this.props,i=a.fallback,s=a.children;return t?i?i(n,r,this.handleReset):u.createElement(Ye,null,u.createElement(Je,null,u.createElement(Se.A,null)),u.createElement(Xe,null,"Something went wrong"),u.createElement($e,null,"We're sorry, but an error occurred while rendering this component.",o>1&&u.createElement("div",{style:{marginTop:be.Ay.spacing[2],color:be.Ay.colors.error.main}},"Multiple errors detected (",o,"). You may need to reload the page.")),n&&u.createElement(Ze,null,u.createElement("strong",null,"Error:")," ",n.toString(),r&&u.createElement("div",{style:{marginTop:be.Ay.spacing[2]}},u.createElement("strong",null,"Component Stack:"),u.createElement("pre",null,r.componentStack))),u.createElement(et,null,u.createElement(Ee.Button,{onClick:this.handleReset,icon:u.createElement(ke.A,null),variant:"outline"},"Try Again"),u.createElement(Ee.Button,{onClick:this.handleReload,icon:u.createElement(_e.A,null)},"Reload Page"),u.createElement(Ee.Button,{onClick:this.handleGoHome,icon:u.createElement(we.A,null),variant:"outline"},"Go to Home"))):s}}],[{key:"getDerivedStateFromError",value:function(e){return{hasError:!0,error:e}}}])}(u.Component);tt.propTypes={children:Ae().node.isRequired,fallback:Ae().func,onError:Ae().func};const nt=tt;var rt=n(7362);function ot(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(ot=function(){return!!e})()}var at=function(e){function t(e){var n,r,o,a;return(0,me.A)(this,t),r=this,o=t,o=(0,fe.A)(o),(n=(0,he.A)(r,ot()?Reflect.construct(o,[],(0,fe.A)(r).constructor):o.apply(r,a))).url=e,n.readyState=t.CONNECTING,n.protocol="",n.extensions="",n.bufferedAmount=0,n.binaryType="blob",setTimeout((function(){n.readyState=t.OPEN;var r=new Event("open");n.dispatchEvent(r),"function"==typeof n.onopen&&n.onopen(r),console.log("Mock WebSocket connected to ".concat(e))}),500),n}return(0,ye.A)(t,e),(0,ge.A)(t,[{key:"send",value:function(e){var n,r=this;if(this.readyState!==t.OPEN)throw new Error("WebSocket is not open");console.log("Mock WebSocket sending data:",e);try{n="string"==typeof e?JSON.parse(e):e}catch(t){n=e}setTimeout((function(){"ping"===n.type?r._handlePing(n):"request_app_data"===n.type?r._handleAppDataRequest(n):r._handleGenericMessage(n)}),200)}},{key:"close",value:function(){var e=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";this.readyState!==t.CLOSED&&(this.readyState=t.CLOSING,setTimeout((function(){e.readyState=t.CLOSED;var o=new CloseEvent("close",{code:n,reason:r,wasClean:1e3===n});e.dispatchEvent(o),"function"==typeof e.onclose&&e.onclose(o),console.log("Mock WebSocket closed: ".concat(n," ").concat(r))}),100))}},{key:"_handlePing",value:function(e){var t={type:"pong",timestamp:(new Date).toISOString(),originalTimestamp:e.timestamp,server:"MockWebSocketServer"};this._sendMessage(t)}},{key:"_handleAppDataRequest",value:function(e){var t={type:"app_data",data:{app:{name:"App Builder",version:"1.0.0",components:[{id:1,type:"Button",props:{text:"Click Me",variant:"primary"}},{id:2,type:"Input",props:{placeholder:"Enter text",label:"Name"}},{id:3,type:"Text",props:{content:"Hello World",style:{fontWeight:"bold"}}}],layouts:[{id:1,type:"Grid",components:[1,2],styles:{gap:"10px"}},{id:2,type:"Flex",components:[3],styles:{justifyContent:"center"}}],styles:{".container":{display:"flex",flexDirection:"column",gap:"20px"},".header":{fontSize:"24px",fontWeight:"bold",marginBottom:"16px"}},status:"online"},_meta:{source:"mock-websocket",timestamp:(new Date).toISOString(),requestId:e.id||e.timestamp}},timestamp:(new Date).toISOString()};this._sendMessage(t)}},{key:"_handleGenericMessage",value:function(e){var t={type:"echo",originalMessage:e,timestamp:(new Date).toISOString(),server:"MockWebSocketServer"};this._sendMessage(t)}},{key:"_sendMessage",value:function(e){var t=JSON.stringify(e),n=new MessageEvent("message",{data:t,origin:this.url,lastEventId:"",source:null,ports:[]});this.dispatchEvent(n),"function"==typeof this.onmessage&&this.onmessage(n),console.log("Mock WebSocket received data:",e)}}])}((0,n(3437).A)(EventTarget));(0,C.A)(at,"CONNECTING",0),(0,C.A)(at,"OPEN",1),(0,C.A)(at,"CLOSING",2),(0,C.A)(at,"CLOSED",3);var it,st=document.createElement("style");st.textContent="\n  body {\n    margin: 0;\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n      sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n\n  * {\n    box-sizing: border-box;\n  }\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n",document.head.appendChild(st);var ct=document.getElementById("root");if(!ct)throw new Error("Root element not found");var lt=(0,p.H)(ct),ut=function(){return u.createElement(d.Kq,{store:m.A},u.createElement(B.OJ,null,u.createElement(nt,null,u.createElement(v.NP,{initialTheme:"light"},u.createElement(g.Kd,null,u.createElement(de,null))))))};console.log("🔌 FORCING REAL WEBSOCKET MODE - Disabling all mock WebSocket functionality..."),window.WebSocket!==window._originalWebSocket&&window._originalWebSocket&&(console.log("🔌 Mock WebSocket detected - restoring real WebSocket..."),window._originalWebSocket&&(window.WebSocket=window._originalWebSocket,console.log("Mock WebSocket server disabled"))),window.WebSocket&&"MockWebSocket"===window.WebSocket.name&&(console.log("🔌 MockWebSocket class detected - forcing native WebSocket..."),window._originalWebSocket&&(window.WebSocket=window._originalWebSocket)),window.USE_REAL_API=!0,window.MOCK_SERVERS_ENABLED=!1,window.FORCE_REAL_WEBSOCKET=!0,console.log("🔌 Current WebSocket class:",(null===(it=window.WebSocket)||void 0===it?void 0:it.name)||"WebSocket"),console.log("🔌 Real API mode:",window.USE_REAL_API),console.log("🔌 Mock servers disabled:",!window.MOCK_SERVERS_ENABLED);try{console.log("🚀 Starting App Builder 201..."),console.log("🔌 Using real WebSocket connections to backend"),lt.render(u.createElement(u.StrictMode,null,u.createElement(ut,null))),console.log("✅ App Builder 201 loaded successfully!"),(0,rt.kz)({onSuccess:function(e){console.log("✅ Service Worker registered successfully:",e)},onUpdate:function(e){console.log("🔄 Service Worker updated:",e)},onWaiting:function(){console.log("⏳ Service Worker waiting for activation")}})}catch(e){console.error("❌ Failed to load App Builder 201:",e),lt.render(u.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh",fontFamily:"Arial, sans-serif",background:"#f8fafc",color:"#1f2937",textAlign:"center",padding:"2rem"}},u.createElement("div",{style:{background:"white",padding:"2rem",borderRadius:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",maxWidth:"500px"}},u.createElement("h1",{style:{margin:"0 0 1rem",color:"#dc2626"}},"App Loading Error"),u.createElement("p",{style:{margin:"0 0 1rem"}},"There was an error loading the App Builder application."),u.createElement("button",{onClick:function(){return window.location.reload()},style:{background:"#3b82f6",color:"white",border:"none",padding:"0.75rem 1.5rem",borderRadius:"6px",cursor:"pointer",fontSize:"1rem"}},"Reload Page"))))}},4702:(e,t,n)=>{n.d(t,{ec:()=>N,gf:()=>k,wz:()=>O,VM:()=>W,wR:()=>T,iD:()=>P,ri:()=>R,Be:()=>I,kz:()=>D,eg:()=>F});var r=n(467),o=n(4756),a=n.n(o),i=n(4467),s=n(3029),c=n(2901),l=n(1083),u="http://localhost:8000",p=u;console.log("API Base URL:","http://localhost:8000"),console.log("Development mode:",!1),console.log("API Prefix:",p);var d={CSRF_TOKEN:"".concat(p,"/csrf-token/"),STATUS:["".concat(p,"/status/"),"".concat(p,"/health/"),"".concat(p,"/health-check/")],HEALTH:["".concat(p,"/health/"),"".concat(p,"/health-check/"),"".concat(p,"/status/")],APP_DATA:["".concat(p,"/get_app_data/"),"".concat(p,"/apps/"),"".concat(p,"/v1/apps/"),"".concat(p,"/app-data/")],SAVE_APP_DATA:["".concat(p,"/save_app_data/"),"".concat(p,"/apps/"),"".concat(p,"/v1/apps/"),"".concat(p,"/app-data/")],EXPORT_APP_DATA:["".concat(p,"/api/app-data/export/"),"".concat(p,"/export_app_data/")],IMPORT_APP_DATA:["".concat(p,"/api/app-data/import/"),"".concat(p,"/import_app_data/")],GENERATE_AI_SUGGESTIONS:["".concat(u,"/api/ai/suggestions/"),"".concat(u,"/generate_ai_suggestions/")],GENERATE_IMAGE:["".concat(u,"/api/ai/generate-image/"),"".concat(u,"/generate_image/")],LOGIN:["".concat(u,"/api/auth/login/"),"".concat(u,"/auth/login/")],REGISTER:["".concat(u,"/api/auth/register/"),"".concat(u,"/auth/register/")],USER_PROFILE:["".concat(u,"/api/auth/profile/"),"".concat(u,"/auth/profile/")],UPDATE_PROFILE:["".concat(u,"/api/auth/profile/update/"),"".concat(u,"/auth/profile/update/")],API_KEYS:["".concat(u,"/api/api-keys/"),"".concat(u,"/api/v1/api-keys/")],VALIDATE_API_KEY:["".concat(u,"/api/validate-api-key/"),"".concat(u,"/api/v1/validate-api-key/")],API_V1:"".concat(u,"/api/v1/"),API_V2:"".concat(u,"/api/v2/"),GRAPHQL:"".concat(u,"/graphql/")};function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const h=new(function(){return(0,c.A)((function e(){(0,s.A)(this,e),this.token=null,this.tokenPromise=null}),[{key:"getTokenFromCookie",value:function(){var e="; ".concat(document.cookie).split("; ".concat("csrftoken","="));return 2===e.length?e.pop().split(";").shift():null}},{key:"fetchToken",value:(i=(0,r.A)(a().mark((function e(){var t,n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch(d.CSRF_TOKEN,{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});case 3:if((t=e.sent).ok){e.next=6;break}throw new Error("Failed to fetch CSRF token: ".concat(t.status));case 6:return e.next=8,t.json();case 8:return n=e.sent,this.token=n.csrfToken,e.abrupt("return",this.token);case 13:throw e.prev=13,e.t0=e.catch(0),console.error("Error fetching CSRF token:",e.t0),e.t0;case 17:case"end":return e.stop()}}),e,this,[[0,13]])}))),function(){return i.apply(this,arguments)})},{key:"getToken",value:(o=(0,r.A)(a().mark((function e(){var t,n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.token){e.next=2;break}return e.abrupt("return",this.token);case 2:if(!this.tokenPromise){e.next=4;break}return e.abrupt("return",this.tokenPromise);case 4:if(!(t=this.getTokenFromCookie())){e.next=8;break}return this.token=t,e.abrupt("return",this.token);case 8:return this.tokenPromise=this.fetchToken(),e.prev=9,e.next=12,this.tokenPromise;case 12:return n=e.sent,this.tokenPromise=null,e.abrupt("return",n);case 17:throw e.prev=17,e.t0=e.catch(9),this.tokenPromise=null,e.t0;case 21:case"end":return e.stop()}}),e,this,[[9,17]])}))),function(){return o.apply(this,arguments)})},{key:"clearToken",value:function(){this.token=null,this.tokenPromise=null}},{key:"getHeaders",value:(n=(0,r.A)(a().mark((function e(){var t,n,r=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.length>0&&void 0!==r[0]?r[0]:{},e.prev=1,e.next=4,this.getToken();case 4:return n=e.sent,e.abrupt("return",g({"X-CSRFToken":n,"Content-Type":"application/json"},t));case 8:return e.prev=8,e.t0=e.catch(1),console.warn("Failed to get CSRF token, proceeding without it:",e.t0),e.abrupt("return",g({"Content-Type":"application/json"},t));case 12:case"end":return e.stop()}}),e,this,[[1,8]])}))),function(){return n.apply(this,arguments)})},{key:"request",value:(t=(0,r.A)(a().mark((function e(t){var n,r,o=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},e.next=3,this.getHeaders(n.headers);case 3:return r=e.sent,e.abrupt("return",fetch(t,g(g({},n),{},{headers:r,credentials:"include"})));case 5:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"initialize",value:(e=(0,r.A)(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.getToken();case 3:console.log("CSRF service initialized successfully"),e.next=9;break;case 6:e.prev=6,e.t0=e.catch(0),console.warn("Failed to initialize CSRF service:",e.t0);case 9:case"end":return e.stop()}}),e,this,[[0,6]])}))),function(){return e.apply(this,arguments)})}]);var e,t,n,o,i}());function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v={baseURL:"http://localhost:8000",timeout:1e4,headers:{"Content-Type":"application/json",Accept:"application/json"}};const A=new(function(){return(0,c.A)((function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,s.A)(this,e),this.config=y(y({},v),n),this.client=l.A.create(this.config),this.endpoints=[],this.initialized=!1,this.client.interceptors.request.use(function(){var e=(0,r.A)(a().mark((function e(t){var n,r;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((n=k())&&(t.headers.Authorization="Bearer ".concat(n)),!t.method||"get"===t.method.toLowerCase()){e.next=13;break}return e.prev=3,e.next=6,h.getHeaders();case 6:r=e.sent,t.headers=y(y({},t.headers),r),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(3),console.warn("Failed to get CSRF token:",e.t0);case 13:return t.withCredentials=!0,e.abrupt("return",t);case 15:case"end":return e.stop()}}),e,null,[[3,10]])})));return function(t){return e.apply(this,arguments)}}(),(function(e){return Promise.reject(e)})),this.client.interceptors.response.use((function(e){return e.data}),function(){var e=(0,r.A)(a().mark((function e(n){var r,o;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.config,!n.response||401!==n.response.status||r._retry){e.next=18;break}return r._retry=!0,e.prev=3,e.next=6,I();case 6:if(!(o=e.sent)){e.next=11;break}return t.client.defaults.headers.common.Authorization="Bearer ".concat(o),r.headers.Authorization="Bearer ".concat(o),e.abrupt("return",t.client(r));case 11:e.next=18;break;case 13:return e.prev=13,e.t0=e.catch(3),console.error("Token refresh failed:",e.t0),window.location.href="/login",e.abrupt("return",Promise.reject(e.t0));case 18:return e.abrupt("return",Promise.reject(n));case 19:case"end":return e.stop()}}),e,null,[[3,13]])})));return function(t){return e.apply(this,arguments)}}())}),[{key:"initServices",value:(p=(0,r.A)(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.discoverEndpoints();case 3:return this.initialized=!0,e.abrupt("return",{initialized:!0,endpoints:this.endpoints});case 7:e.prev=7,e.t0=e.catch(0),console.warn("API client initialization failed:",e.t0),e.next=14;break;case 14:throw e.t0;case 15:case"end":return e.stop()}}),e,this,[[0,7]])}))),function(){return p.apply(this,arguments)})},{key:"discoverEndpoints",value:(u=(0,r.A)(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.client.get("/");case 3:if(!(t=e.sent)||!t.endpoints){e.next=7;break}return this.endpoints=t.endpoints,e.abrupt("return",this.endpoints);case 7:return this.endpoints=[{path:"/status",method:"GET",description:"Get API status"},{path:"/app-data",method:"GET",description:"Get app data"},{path:"/components",method:"GET",description:"Get components"},{path:"/templates",method:"GET",description:"Get templates"},{path:"/projects",method:"GET",description:"Get projects"},{path:"/users",method:"GET",description:"Get users"},{path:"/auth",method:"POST",description:"Authenticate user"}],e.abrupt("return",this.endpoints);case 11:return e.prev=11,e.t0=e.catch(0),console.warn("API endpoint discovery failed:",e.t0),this.endpoints=[{path:"/status",method:"GET",description:"Get API status"},{path:"/app-data",method:"GET",description:"Get app data"},{path:"/components",method:"GET",description:"Get components"},{path:"/templates",method:"GET",description:"Get templates"},{path:"/projects",method:"GET",description:"Get projects"},{path:"/users",method:"GET",description:"Get users"},{path:"/auth",method:"POST",description:"Authenticate user"}],e.abrupt("return",this.endpoints);case 16:case"end":return e.stop()}}),e,this,[[0,11]])}))),function(){return u.apply(this,arguments)})},{key:"get",value:(i=(0,r.A)(a().mark((function e(t){var n,r=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:{},e.abrupt("return",this.client.get(t,n));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"post",value:(o=(0,r.A)(a().mark((function e(t){var n,r,o=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r=o.length>2&&void 0!==o[2]?o[2]:{},e.abrupt("return",this.client.post(t,n,r));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})},{key:"put",value:(n=(0,r.A)(a().mark((function e(t){var n,r,o=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r=o.length>2&&void 0!==o[2]?o[2]:{},e.abrupt("return",this.client.put(t,n,r));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"patch",value:(t=(0,r.A)(a().mark((function e(t){var n,r,o=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r=o.length>2&&void 0!==o[2]?o[2]:{},e.abrupt("return",this.client.patch(t,n,r));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"delete",value:(e=(0,r.A)(a().mark((function e(t){var n,r=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:{},e.abrupt("return",this.client.delete(t,n));case 2:case"end":return e.stop()}}),e,this)}))),function(t){return e.apply(this,arguments)})},{key:"hasEndpoint",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET";return this.endpoints.some((function(n){return n.path===e&&n.method===t}))}},{key:"getEndpointDescription",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET",n=this.endpoints.find((function(n){return n.path===e&&n.method===t}));return n?n.description:""}}]);var e,t,n,o,i,u,p}());var b="app_auth_token",E="app_refresh_token",S="app_user",k=function(){return localStorage.getItem(b)},_=function(e){localStorage.setItem(b,e)},w=function(e){localStorage.setItem(E,e)},O=function(){var e=localStorage.getItem(S);if(e)try{return JSON.parse(e)}catch(e){return console.error("Error parsing user data:",e),null}return null},C=function(e){localStorage.setItem(S,JSON.stringify(e))},x=function(){localStorage.removeItem(b),localStorage.removeItem(E),localStorage.removeItem(S)},T=function(){return!!k()},P=function(){var e=(0,r.A)(a().mark((function e(t,n){var r,o,i;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,A.post("/auth/login/",{username:t,password:n});case 3:if(!(r=e.sent).access){e.next=20;break}return _(r.access),r.refresh&&w(r.refresh),e.prev=7,e.next=10,A.get("/auth/profile/");case 10:(o=e.sent)&&C(o),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(7),console.warn("Failed to fetch user profile:",e.t0);case 17:return e.abrupt("return",{success:!0,user:r.user||{username:t}});case 20:if(!r.token){e.next=24;break}return _(r.token),r.user&&C(r.user),e.abrupt("return",{success:!0,user:r.user});case 24:return e.abrupt("return",{success:!1,error:"Invalid response from server"});case 27:return e.prev=27,e.t1=e.catch(0),console.error("Login error:",e.t1),e.abrupt("return",{success:!1,error:(null===(i=e.t1.response)||void 0===i||null===(i=i.data)||void 0===i?void 0:i.detail)||e.t1.message||"Login failed"});case 31:case"end":return e.stop()}}),e,null,[[0,27],[7,14]])})));return function(t,n){return e.apply(this,arguments)}}(),D=function(){var e=(0,r.A)(a().mark((function e(t){var n,r,o,i;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,A.post("/auth/register/",t);case 3:if(!(n=e.sent).access){e.next=20;break}return _(n.access),n.refresh&&w(n.refresh),e.prev=7,e.next=10,A.get("/auth/profile/");case 10:(r=e.sent)&&C(r),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(7),console.warn("Failed to fetch user profile:",e.t0);case 17:return e.abrupt("return",{success:!0,user:n.user||{username:t.username}});case 20:if(!n.token){e.next=26;break}return _(n.token),n.user&&C(n.user),e.abrupt("return",{success:!0,user:n.user});case 26:if(!n.success){e.next=28;break}return e.abrupt("return",{success:!0,user:n.user});case 28:return e.abrupt("return",{success:!1,error:n.error||"Registration failed"});case 31:return e.prev=31,e.t1=e.catch(0),console.error("Registration error:",e.t1),e.abrupt("return",{success:!1,error:(null===(o=e.t1.response)||void 0===o||null===(o=o.data)||void 0===o?void 0:o.detail)||(null===(i=e.t1.response)||void 0===i||null===(i=i.data)||void 0===i?void 0:i.message)||e.t1.message||"Registration failed"});case 35:case"end":return e.stop()}}),e,null,[[0,31],[7,14]])})));return function(t){return e.apply(this,arguments)}}(),R=function(){var e=(0,r.A)(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,A.post("/auth/logout");case 3:e.next=8;break;case 5:e.prev=5,e.t0=e.catch(0),console.warn("Logout notification failed:",e.t0);case 8:return e.prev=8,x(),e.finish(8);case 11:return e.abrupt("return",{success:!0});case 12:case"end":return e.stop()}}),e,null,[[0,5,8,11]])})));return function(){return e.apply(this,arguments)}}(),I=function(){var e=(0,r.A)(a().mark((function e(){var t,n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,t=localStorage.getItem(E)){e.next=4;break}throw new Error("No refresh token available");case 4:return e.next=6,A.post("/auth/token/refresh/",{refresh:t});case 6:if(!(n=e.sent).access){e.next=13;break}return _(n.access),n.refresh&&w(n.refresh),e.abrupt("return",n.access);case 13:if(!n.token){e.next=17;break}return _(n.token),n.refreshToken&&w(n.refreshToken),e.abrupt("return",n.token);case 17:return e.abrupt("return",null);case 20:return e.prev=20,e.t0=e.catch(0),console.error("Token refresh error:",e.t0),x(),e.abrupt("return",null);case 25:case"end":return e.stop()}}),e,null,[[0,20]])})));return function(){return e.apply(this,arguments)}}(),W=function(){var e=(0,r.A)(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,A.get("/auth/profile");case 3:return e.abrupt("return",e.sent);case 6:return e.prev=6,e.t0=e.catch(0),console.error("Get user profile error:",e.t0),e.abrupt("return",null);case 10:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(){return e.apply(this,arguments)}}(),F=function(){var e=(0,r.A)(a().mark((function e(t){var n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,A.put("/auth/profile",t);case 3:if(!(n=e.sent).user){e.next=7;break}return C(n.user),e.abrupt("return",{success:!0,user:n.user});case 7:return e.abrupt("return",{success:!1,error:"Invalid response from server"});case 10:return e.prev=10,e.t0=e.catch(0),console.error("Update profile error:",e.t0),e.abrupt("return",{success:!1,error:e.t0.message||"Update profile failed"});case 14:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(t){return e.apply(this,arguments)}}(),N=function(){var e=(0,r.A)(a().mark((function e(t,n){var r;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,A.post("/auth/change-password",{currentPassword:t,newPassword:n});case 3:return r=e.sent,e.abrupt("return",{success:r.success,error:r.error});case 7:return e.prev=7,e.t0=e.catch(0),console.error("Change password error:",e.t0),e.abrupt("return",{success:!1,error:e.t0.message||"Change password failed"});case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t,n){return e.apply(this,arguments)}}()},6020:(e,t,n)=>{n.d(t,{Ay:()=>h,Eo:()=>i,HP:()=>p,Il:()=>o,Ti:()=>d,Tj:()=>r,Vq:()=>s,WT:()=>m,YK:()=>a,bm:()=>c,dK:()=>g,fE:()=>l,fi:()=>u});var r={primary:{50:"#EFF6FF",100:"#DBEAFE",200:"#BFDBFE",300:"#93C5FD",400:"#60A5FA",500:"#3B82F6",600:"#2563EB",700:"#1D4ED8",800:"#1E40AF",900:"#1E3A8A",main:"#2563EB",light:"#DBEAFE",dark:"#1E40AF",contrastText:"#FFFFFF"},secondary:{50:"#ECFDF5",100:"#D1FAE5",200:"#A7F3D0",300:"#6EE7B7",400:"#34D399",500:"#10B981",600:"#059669",700:"#047857",800:"#065F46",900:"#064E3B",main:"#10B981",light:"#D1FAE5",dark:"#047857",contrastText:"#FFFFFF"},accent:{50:"#FAF5FF",100:"#F3E8FF",200:"#E9D5FF",300:"#D8B4FE",400:"#C084FC",500:"#A855F7",600:"#9333EA",700:"#7C3AED",800:"#6B21A8",900:"#581C87",main:"#8B5CF6",light:"#EDE9FE",dark:"#6D28D9",contrastText:"#FFFFFF"},neutral:{0:"#FFFFFF",50:"#F9FAFB",100:"#F3F4F6",200:"#E5E7EB",300:"#D1D5DB",400:"#9CA3AF",500:"#6B7280",600:"#4B5563",700:"#374151",800:"#1F2937",900:"#111827",950:"#030712"},success:{50:"#F0FDF4",100:"#DCFCE7",200:"#BBF7D0",300:"#86EFAC",400:"#4ADE80",500:"#22C55E",600:"#16A34A",700:"#15803D",800:"#166534",900:"#14532D",main:"#16A34A",light:"#DCFCE7",dark:"#15803D",contrastText:"#FFFFFF"},warning:{50:"#FFFBEB",100:"#FEF3C7",200:"#FDE68A",300:"#FCD34D",400:"#FBBF24",500:"#F59E0B",600:"#D97706",700:"#B45309",800:"#92400E",900:"#78350F",main:"#D97706",light:"#FEF3C7",dark:"#B45309",contrastText:"#FFFFFF"},error:{50:"#FEF2F2",100:"#FEE2E2",200:"#FECACA",300:"#FCA5A5",400:"#F87171",500:"#EF4444",600:"#DC2626",700:"#B91C1C",800:"#991B1B",900:"#7F1D1D",main:"#DC2626",light:"#FEE2E2",dark:"#B91C1C",contrastText:"#FFFFFF"},info:{50:"#EFF6FF",100:"#DBEAFE",200:"#BFDBFE",300:"#93C5FD",400:"#60A5FA",500:"#3B82F6",600:"#2563EB",700:"#1D4ED8",800:"#1E40AF",900:"#1E3A8A",main:"#2563EB",light:"#DBEAFE",dark:"#1D4ED8",contrastText:"#FFFFFF"},background:{default:"#FFFFFF",paper:"#FFFFFF",secondary:"#F9FAFB",tertiary:"#F3F4F6",overlay:"rgba(0, 0, 0, 0.5)",disabled:"#F3F4F6"},text:{primary:"#111827",secondary:"#4B5563",tertiary:"#6B7280",disabled:"#9CA3AF",hint:"#9CA3AF",inverse:"#FFFFFF",link:"#2563EB",linkHover:"#1D4ED8"},border:{default:"#E5E7EB",light:"#F3F4F6",medium:"#D1D5DB",dark:"#9CA3AF",focus:"#2563EB",error:"#DC2626",success:"#16A34A",warning:"#D97706"},interactive:{hover:"rgba(37, 99, 235, 0.04)",pressed:"rgba(37, 99, 235, 0.08)",focus:"rgba(37, 99, 235, 0.12)",selected:"rgba(37, 99, 235, 0.08)",disabled:"rgba(0, 0, 0, 0.04)"}},o={fontFamily:{primary:'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',secondary:'"Inter", system-ui, sans-serif',code:'"Fira Code", "JetBrains Mono", "Roboto Mono", "Courier New", monospace',display:'"Inter", system-ui, sans-serif'},fontWeight:{thin:100,extralight:200,light:300,regular:400,medium:500,semibold:600,bold:700,extrabold:800,black:900},fontSize:{xs:"0.75rem",sm:"0.875rem",base:"1rem",md:"1rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem","6xl":"3.75rem","7xl":"4.5rem","8xl":"6rem","9xl":"8rem"},lineHeight:{none:1,tight:1.25,snug:1.375,normal:1.5,relaxed:1.625,loose:2,3:.75,4:1,5:1.25,6:1.5,7:1.75,8:2,9:2.25,10:2.5},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0",wide:"0.025em",wider:"0.05em",widest:"0.1em"},textStyles:{h1:{fontSize:"2.25rem",fontWeight:700,lineHeight:1.25,letterSpacing:"-0.025em"},h2:{fontSize:"1.875rem",fontWeight:600,lineHeight:1.25,letterSpacing:"-0.025em"},h3:{fontSize:"1.5rem",fontWeight:600,lineHeight:1.375},h4:{fontSize:"1.25rem",fontWeight:600,lineHeight:1.375},h5:{fontSize:"1.125rem",fontWeight:600,lineHeight:1.375},h6:{fontSize:"1rem",fontWeight:600,lineHeight:1.375},body1:{fontSize:"1rem",fontWeight:400,lineHeight:1.5},body2:{fontSize:"0.875rem",fontWeight:400,lineHeight:1.5},caption:{fontSize:"0.75rem",fontWeight:400,lineHeight:1.375},overline:{fontSize:"0.75rem",fontWeight:600,lineHeight:1.375,letterSpacing:"0.1em",textTransform:"uppercase"},button:{fontSize:"0.875rem",fontWeight:500,lineHeight:1.25,letterSpacing:"0.025em"},code:{fontFamily:'"Fira Code", "JetBrains Mono", "Roboto Mono", monospace',fontSize:"0.875rem",fontWeight:400,lineHeight:1.5}}},a={px:"1px",0:"0",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},i={none:"none",xs:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",sm:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",xl:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)","2xl":"0 25px 50px -12px rgba(0, 0, 0, 0.25)","3xl":"0 35px 60px -12px rgba(0, 0, 0, 0.35)",inner:"inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)",primary:"0 4px 14px 0 rgba(37, 99, 235, 0.15)",secondary:"0 4px 14px 0 rgba(16, 185, 129, 0.15)",error:"0 4px 14px 0 rgba(220, 38, 38, 0.15)",warning:"0 4px 14px 0 rgba(217, 119, 6, 0.15)",success:"0 4px 14px 0 rgba(22, 163, 74, 0.15)",focus:"0 0 0 3px rgba(37, 99, 235, 0.1)",focusError:"0 0 0 3px rgba(220, 38, 38, 0.1)",focusSuccess:"0 0 0 3px rgba(22, 163, 74, 0.1)",focusWarning:"0 0 0 3px rgba(217, 119, 6, 0.1)"},s={none:"0",xs:"0.0625rem",sm:"0.125rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem","4xl":"2rem",full:"9999px",button:"0.375rem",card:"0.5rem",input:"0.375rem",modal:"0.75rem",tooltip:"0.25rem"},c={none:"none",all:"all 150ms cubic-bezier(0.4, 0, 0.2, 1)",default:"all 150ms cubic-bezier(0.4, 0, 0.2, 1)",fast:"all 100ms cubic-bezier(0.4, 0, 0.2, 1)",slow:"all 300ms cubic-bezier(0.4, 0, 0.2, 1)",colors:"color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1), border-color 150ms cubic-bezier(0.4, 0, 0.2, 1)",opacity:"opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)",shadow:"box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1)",transform:"transform 150ms cubic-bezier(0.4, 0, 0.2, 1)",easing:{linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)",inOut:"cubic-bezier(0.4, 0, 0.2, 1)"}},l={hide:-1,auto:"auto",base:0,docked:10,dropdown:1e3,sticky:1100,banner:1200,overlay:1300,modal:1400,popover:1500,skipLink:1600,toast:1700,tooltip:1800,appHeader:100,appSidebar:200,componentPalette:300,propertyEditor:300,previewArea:100,dragOverlay:1e3,tutorialOverlay:1500,aiSuggestions:1200},u={xs:"0px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px","3xl":"1920px",mobile:"640px",tablet:"768px",desktop:"1024px",wide:"1280px",ultrawide:"1536px"},p={sm:"@media (min-width: ".concat(u.sm,")"),md:"@media (min-width: ".concat(u.md,")"),lg:"@media (min-width: ".concat(u.lg,")"),xl:"@media (min-width: ".concat(u.xl,")"),"2xl":"@media (min-width: ".concat(u["2xl"],")"),"3xl":"@media (min-width: ".concat(u["3xl"],")"),maxSm:"@media (max-width: ".concat(parseInt(u.sm)-1,"px)"),maxMd:"@media (max-width: ".concat(parseInt(u.md)-1,"px)"),maxLg:"@media (max-width: ".concat(parseInt(u.lg)-1,"px)"),maxXl:"@media (max-width: ".concat(parseInt(u.xl)-1,"px)"),smToMd:"@media (min-width: ".concat(u.sm,") and (max-width: ").concat(parseInt(u.md)-1,"px)"),mdToLg:"@media (min-width: ".concat(u.md,") and (max-width: ").concat(parseInt(u.lg)-1,"px)"),lgToXl:"@media (min-width: ".concat(u.lg,") and (max-width: ").concat(parseInt(u.xl)-1,"px)"),reducedMotion:"@media (prefers-reduced-motion: reduce)",highContrast:"@media (prefers-contrast: high)",darkMode:"@media (prefers-color-scheme: dark)",lightMode:"@media (prefers-color-scheme: light)",touch:"@media (hover: none) and (pointer: coarse)",mouse:"@media (hover: hover) and (pointer: fine)",retina:"@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)"},d={focusRing:{width:"2px",style:"solid",color:r.primary.main,offset:"2px"},minTouchTarget:{width:"44px",height:"44px"},srOnly:{position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",border:"0"},highContrast:{border:"1px solid",outline:"1px solid"}},m={fadeIn:"fadeIn 150ms cubic-bezier(0.4, 0, 0.2, 1)",fadeOut:"fadeOut 150ms cubic-bezier(0.4, 0, 0.2, 1)",slideInUp:"slideInUp 200ms cubic-bezier(0.4, 0, 0.2, 1)",slideInDown:"slideInDown 200ms cubic-bezier(0.4, 0, 0.2, 1)",slideInLeft:"slideInLeft 200ms cubic-bezier(0.4, 0, 0.2, 1)",slideInRight:"slideInRight 200ms cubic-bezier(0.4, 0, 0.2, 1)",scaleIn:"scaleIn 150ms cubic-bezier(0.4, 0, 0.2, 1)",scaleOut:"scaleOut 150ms cubic-bezier(0.4, 0, 0.2, 1)",bounce:"bounce 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55)",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",spin:"spin 1s linear infinite"},g={button:{height:{sm:"32px",md:"40px",lg:"48px"},padding:{sm:"0 12px",md:"0 16px",lg:"0 24px"},fontSize:{sm:o.fontSize.sm,md:o.fontSize.base,lg:o.fontSize.lg}},input:{height:{sm:"32px",md:"40px",lg:"48px"},padding:{sm:"0 8px",md:"0 12px",lg:"0 16px"}},card:{padding:{sm:a[4],md:a[6],lg:a[8]}},modal:{maxWidth:{sm:"400px",md:"600px",lg:"800px",xl:"1200px"}}};const h={colors:r,typography:o,spacing:a,shadows:i,borderRadius:s,transitions:c,zIndex:l,breakpoints:u,mediaQueries:p,accessibility:d,animations:m,components:g}},6191:(e,t,n)=>{n.r(t),n.d(t,{Button:()=>U,Card:()=>te,Input:()=>Re,Select:()=>Be,Text:()=>tt,ThemeProvider:()=>W.NP,VERSION:()=>ot,a11yUtils:()=>S,accessibility:()=>r.Ti,animationUtils:()=>k,animations:()=>r.WT,borderRadius:()=>r.Vq,breakpoints:()=>r.fi,colorUtils:()=>A,colors:()=>r.Tj,componentTokens:()=>r.dK,componentUtils:()=>_,config:()=>at,createGlobalStyle:()=>W.DU,css:()=>W.AH,designUtils:()=>O,globalStyles:()=>rt,hierarchyUtils:()=>m,informationArchitecture:()=>d,interactionPatterns:()=>p,keyframes:()=>W.i7,mediaQueries:()=>r.HP,responsiveUtils:()=>w,shadows:()=>r.Eo,spacing:()=>r.YK,spacingHierarchy:()=>l,spacingUtils:()=>b,styled:()=>nt,theme:()=>r.Ay,transitions:()=>r.bm,typography:()=>r.Il,typographyUtils:()=>E,visualGrouping:()=>u,visualHierarchy:()=>c,zIndex:()=>r.fE});var r=n(6020),o=n(436),a=n(4467);function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){(0,a.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var c={display:{large:{fontSize:r.Ay.typography.fontSize["6xl"],fontWeight:r.Ay.typography.fontWeight.bold,lineHeight:r.Ay.typography.lineHeight.tight,letterSpacing:"-0.025em",marginBottom:r.Ay.spacing[6],color:r.Ay.colors.text.primary},medium:{fontSize:r.Ay.typography.fontSize["5xl"],fontWeight:r.Ay.typography.fontWeight.bold,lineHeight:r.Ay.typography.lineHeight.tight,letterSpacing:"-0.025em",marginBottom:r.Ay.spacing[5],color:r.Ay.colors.text.primary},small:{fontSize:r.Ay.typography.fontSize["4xl"],fontWeight:r.Ay.typography.fontWeight.semibold,lineHeight:r.Ay.typography.lineHeight.tight,letterSpacing:"-0.025em",marginBottom:r.Ay.spacing[4],color:r.Ay.colors.text.primary}},heading:{h1:{fontSize:r.Ay.typography.fontSize["3xl"],fontWeight:r.Ay.typography.fontWeight.bold,lineHeight:r.Ay.typography.lineHeight.tight,marginBottom:r.Ay.spacing[4],color:r.Ay.colors.text.primary},h2:{fontSize:r.Ay.typography.fontSize["2xl"],fontWeight:r.Ay.typography.fontWeight.semibold,lineHeight:r.Ay.typography.lineHeight.snug,marginBottom:r.Ay.spacing[3],color:r.Ay.colors.text.primary},h3:{fontSize:r.Ay.typography.fontSize.xl,fontWeight:r.Ay.typography.fontWeight.semibold,lineHeight:r.Ay.typography.lineHeight.snug,marginBottom:r.Ay.spacing[3],color:r.Ay.colors.text.primary},h4:{fontSize:r.Ay.typography.fontSize.lg,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.normal,marginBottom:r.Ay.spacing[2],color:r.Ay.colors.text.primary},h5:{fontSize:r.Ay.typography.fontSize.base,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.normal,marginBottom:r.Ay.spacing[2],color:r.Ay.colors.text.primary},h6:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.normal,marginBottom:r.Ay.spacing[1],color:r.Ay.colors.text.secondary,textTransform:"uppercase",letterSpacing:"0.05em"}},body:{large:{fontSize:r.Ay.typography.fontSize.lg,fontWeight:r.Ay.typography.fontWeight.regular,lineHeight:r.Ay.typography.lineHeight.relaxed,marginBottom:r.Ay.spacing[4],color:r.Ay.colors.text.primary},medium:{fontSize:r.Ay.typography.fontSize.base,fontWeight:r.Ay.typography.fontWeight.regular,lineHeight:r.Ay.typography.lineHeight.relaxed,marginBottom:r.Ay.spacing[3],color:r.Ay.colors.text.primary},small:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.regular,lineHeight:r.Ay.typography.lineHeight.normal,marginBottom:r.Ay.spacing[2],color:r.Ay.colors.text.secondary}},label:{large:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.normal,color:r.Ay.colors.text.primary},medium:{fontSize:r.Ay.typography.fontSize.xs,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.normal,color:r.Ay.colors.text.secondary},small:{fontSize:r.Ay.typography.fontSize.xs,fontWeight:r.Ay.typography.fontWeight.regular,lineHeight:r.Ay.typography.lineHeight.normal,color:r.Ay.colors.text.tertiary}},interactive:{button:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.tight,letterSpacing:"0.025em"},link:{fontSize:"inherit",fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:"inherit",color:r.Ay.colors.primary.main,textDecoration:"none"},tab:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.normal}}},l={component:{tight:r.Ay.spacing[1],normal:r.Ay.spacing[2],comfortable:r.Ay.spacing[3],loose:r.Ay.spacing[4]},element:{tight:r.Ay.spacing[2],normal:r.Ay.spacing[3],comfortable:r.Ay.spacing[4],loose:r.Ay.spacing[6]},section:{tight:r.Ay.spacing[6],normal:r.Ay.spacing[8],comfortable:r.Ay.spacing[12],loose:r.Ay.spacing[16]},layout:{tight:r.Ay.spacing[8],normal:r.Ay.spacing[12],comfortable:r.Ay.spacing[16],loose:r.Ay.spacing[24]}},u={card:{minimal:{background:r.Ay.colors.background.paper,border:"1px solid ".concat(r.Ay.colors.border.light),borderRadius:r.Ay.borderRadius.md,padding:r.Ay.spacing[4],boxShadow:"none"},elevated:{background:r.Ay.colors.background.paper,border:"1px solid ".concat(r.Ay.colors.border.light),borderRadius:r.Ay.borderRadius.lg,padding:r.Ay.spacing[6],boxShadow:r.Ay.shadows.md},prominent:{background:r.Ay.colors.background.paper,border:"1px solid ".concat(r.Ay.colors.border.light),borderRadius:r.Ay.borderRadius.lg,padding:r.Ay.spacing[8],boxShadow:r.Ay.shadows.lg}},section:{subtle:{background:r.Ay.colors.background.secondary,borderRadius:r.Ay.borderRadius.md,padding:r.Ay.spacing[4]},defined:{background:r.Ay.colors.background.paper,border:"1px solid ".concat(r.Ay.colors.border.light),borderRadius:r.Ay.borderRadius.md,padding:r.Ay.spacing[6]},prominent:{background:r.Ay.colors.background.paper,border:"2px solid ".concat(r.Ay.colors.primary.light),borderRadius:r.Ay.borderRadius.lg,padding:r.Ay.spacing[8]}},list:{simple:{gap:r.Ay.spacing[2],divider:"none"},divided:{gap:r.Ay.spacing[3],divider:"1px solid ".concat(r.Ay.colors.border.light)},spaced:{gap:r.Ay.spacing[4],divider:"none"}}},p={button:{primary:{default:{background:r.Ay.colors.primary.main,color:r.Ay.colors.primary.contrastText,border:"1px solid ".concat(r.Ay.colors.primary.main),boxShadow:"none"},hover:{background:r.Ay.colors.primary.dark,color:r.Ay.colors.primary.contrastText,border:"1px solid ".concat(r.Ay.colors.primary.dark),boxShadow:r.Ay.shadows.sm,transform:"translateY(-1px)"},active:{background:r.Ay.colors.primary.dark,color:r.Ay.colors.primary.contrastText,border:"1px solid ".concat(r.Ay.colors.primary.dark),boxShadow:r.Ay.shadows.inner,transform:"translateY(0)"},focus:{outline:"2px solid ".concat(r.Ay.colors.primary.main),outlineOffset:"2px"},disabled:{background:r.Ay.colors.neutral[300],color:r.Ay.colors.neutral[500],border:"1px solid ".concat(r.Ay.colors.neutral[300]),cursor:"not-allowed",opacity:.6}},secondary:{default:{background:"transparent",color:r.Ay.colors.primary.main,border:"1px solid ".concat(r.Ay.colors.primary.main),boxShadow:"none"},hover:{background:r.Ay.colors.primary.light,color:r.Ay.colors.primary.dark,border:"1px solid ".concat(r.Ay.colors.primary.main),boxShadow:r.Ay.shadows.sm},active:{background:r.Ay.colors.primary.light,color:r.Ay.colors.primary.dark,border:"1px solid ".concat(r.Ay.colors.primary.dark),boxShadow:r.Ay.shadows.inner},focus:{outline:"2px solid ".concat(r.Ay.colors.primary.main),outlineOffset:"2px"}}},interactive:{default:{cursor:"pointer",transition:r.Ay.transitions.default},hover:{background:r.Ay.colors.interactive.hover,transform:"translateY(-1px)",boxShadow:r.Ay.shadows.sm},active:{background:r.Ay.colors.interactive.pressed,transform:"translateY(0)",boxShadow:r.Ay.shadows.inner},focus:{outline:"2px solid ".concat(r.Ay.colors.primary.main),outlineOffset:"2px"},selected:{background:r.Ay.colors.interactive.selected,border:"1px solid ".concat(r.Ay.colors.primary.main)},disabled:{background:r.Ay.colors.interactive.disabled,cursor:"not-allowed",opacity:.6}},dragDrop:{draggable:{cursor:"grab",transition:r.Ay.transitions.default},dragging:{cursor:"grabbing",opacity:.8,transform:"rotate(2deg) scale(1.02)",boxShadow:r.Ay.shadows.lg,zIndex:r.Ay.zIndex.dragOverlay},dropTarget:{background:r.Ay.colors.success.light,border:"2px dashed ".concat(r.Ay.colors.success.main),borderRadius:r.Ay.borderRadius.md},dropActive:{background:r.Ay.colors.success.light,border:"2px solid ".concat(r.Ay.colors.success.main),borderRadius:r.Ay.borderRadius.md,boxShadow:"0 0 0 4px ".concat(r.Ay.colors.success.main,"20")}}},d={navigation:{primary:{fontSize:r.Ay.typography.fontSize.base,fontWeight:r.Ay.typography.fontWeight.medium,spacing:r.Ay.spacing[6],hierarchy:"horizontal"},secondary:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.regular,spacing:r.Ay.spacing[4],hierarchy:"vertical"},breadcrumb:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.regular,spacing:r.Ay.spacing[2],separator:"/"}},content:{hero:{alignment:"center",spacing:l.layout.comfortable,typography:c.display.large},section:{alignment:"left",spacing:l.section.normal,typography:c.heading.h2},subsection:{alignment:"left",spacing:l.element.comfortable,typography:c.heading.h3},paragraph:{alignment:"left",spacing:l.element.normal,typography:c.body.medium}},form:{fieldGroup:{spacing:l.element.comfortable,grouping:u.section.subtle},field:{spacing:l.element.normal,labelSpacing:r.Ay.spacing[1]},actions:{spacing:l.element.normal,alignment:"right",grouping:"horizontal"}},data:{table:{headerSpacing:l.component.comfortable,cellSpacing:l.component.normal,rowSpacing:r.Ay.spacing[2]},list:{itemSpacing:l.element.normal,groupSpacing:l.section.tight},card:{contentSpacing:l.component.comfortable,actionSpacing:l.element.normal}}},m={getTypography:function(e,t){var n;return(null===(n=c[e])||void 0===n?void 0:n[t])||{}},getSpacing:function(e,t){var n;return(null===(n=l[e])||void 0===n?void 0:n[t])||r.Ay.spacing[4]},getGrouping:function(e,t){var n;return(null===(n=u[e])||void 0===n?void 0:n[t])||{}},getInteraction:function(e,t){var n;return(null===(n=p[e])||void 0===n?void 0:n[t])||{}},createResponsiveTypography:function(e,t,n){return s(s({},m.getTypography.apply(m,(0,o.A)(e))),{},(0,a.A)((0,a.A)({},r.Ay.mediaQueries.maxLg,m.getTypography.apply(m,(0,o.A)(t))),r.Ay.mediaQueries.maxMd,m.getTypography.apply(m,(0,o.A)(n))))},createResponsiveSpacing:function(e,t,n){return(0,a.A)((0,a.A)({margin:m.getSpacing.apply(m,(0,o.A)(e))},r.Ay.mediaQueries.maxLg,{margin:m.getSpacing.apply(m,(0,o.A)(t))}),r.Ay.mediaQueries.maxMd,{margin:m.getSpacing.apply(m,(0,o.A)(n))})}};m.getTypography("heading","h4"),m.getTypography("heading","h5"),m.getTypography("label","medium"),m.getTypography("label","small"),l.section.tight,l.element.comfortable,l.element.normal,m.getTypography("heading","h4"),m.getTypography("heading","h6"),m.getTypography("label","large"),m.getTypography("label","small"),l.section.tight,l.element.comfortable,l.element.normal,m.getTypography("label","medium"),m.getTypography("label","small"),m.getTypography("label","small"),l.component.comfortable,l.component.normal,m.getTypography("heading","h3"),m.getTypography("body","medium"),m.getTypography("interactive","button"),l.section.tight,l.element.comfortable,l.element.normal,m.getTypography("heading","h4"),m.getTypography("body","small"),m.getTypography("body","medium"),m.getTypography("label","small"),l.element.normal,l.element.comfortable,l.element.normal,m.getTypography("heading","h3"),m.getTypography("heading","h5"),m.getTypography("label","large"),m.getTypography("label","small"),s(s({},m.getTypography("label","small")),{},{color:r.Ay.colors.error.main}),l.section.normal,l.element.comfortable,l.element.normal,"2px solid ".concat(r.Ay.colors.primary.main),r.Ay.borderRadius.sm,r.Ay.accessibility.srOnly,r.Ay.spacing[2],r.Ay.colors.primary.main,r.Ay.colors.primary.contrastText,"".concat(r.Ay.spacing[2]," ").concat(r.Ay.spacing[4]),r.Ay.borderRadius.md,r.Ay.typography.fontWeight.medium,r.Ay.zIndex.skipLink,r.Ay.transitions.default,r.Ay.spacing[2];var g=n(5544),h=n(2284);function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){(0,a.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var A={getColor:function(e){var t,n=e.split("."),o=r.Ay.colors,a=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return v(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?v(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){s=!0,a=e},f:function(){try{i||null==n.return||n.return()}finally{if(s)throw a}}}}(n);try{for(a.s();!(t=a.n()).done;){var i=t.value;if(!o||"object"!==(0,h.A)(o)||!(i in o))return console.warn('Color path "'.concat(e,'" not found in theme')),r.Ay.colors.neutral[500];o=o[i]}}catch(e){a.e(e)}finally{a.f()}return o},hexToRgba:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=parseInt(e.slice(1,3),16),r=parseInt(e.slice(3,5),16),o=parseInt(e.slice(5,7),16);return"rgba(".concat(n,", ").concat(r,", ").concat(o,", ").concat(t,")")},lighten:function(e,t){var n=parseInt(e.replace("#",""),16),r=Math.round(2.55*t),o=(n>>16)+r,a=(n>>8&255)+r,i=(255&n)+r;return"#"+(16777216+65536*(o<255?o<1?0:o:255)+256*(a<255?a<1?0:a:255)+(i<255?i<1?0:i:255)).toString(16).slice(1)},darken:function(e,t){return A.lighten(e,-t)},checkContrast:function(e,t){return!0}},b={getSpacing:function(e){return"number"==typeof e?"".concat(.25*e,"rem"):r.Ay.spacing[e]||e},responsive:function(e){var t="";return Object.entries(e).forEach((function(e){var n=(0,g.A)(e,2),o=n[0],a=n[1];t+="base"===o?"".concat(b.getSpacing(a),";"):"".concat(r.Ay.mediaQueries[o]," { ").concat(b.getSpacing(a),"; }")})),t}},E={getTextStyle:function(e){return r.Ay.typography.textStyles[e]||{}},responsiveText:function(e){var t={};return Object.entries(e).forEach((function(e){var n=(0,g.A)(e,2),o=n[0],a=n[1];"base"===o?Object.assign(t,E.getTextStyle(a)):t[r.Ay.mediaQueries[o]]=E.getTextStyle(a)})),t}},S={focusRing:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r.Ay.colors.primary.main;return{outline:"".concat(r.Ay.accessibility.focusRing.width," ").concat(r.Ay.accessibility.focusRing.style," ").concat(e),outlineOffset:r.Ay.accessibility.focusRing.offset}},srOnly:function(){return r.Ay.accessibility.srOnly},touchTarget:function(){return{minWidth:r.Ay.accessibility.minTouchTarget.width,minHeight:r.Ay.accessibility.minTouchTarget.height}},highContrast:function(){return{"@media (prefers-contrast: high)":r.Ay.accessibility.highContrast}}},k={getAnimation:function(e){return r.Ay.animations[e]||e},withReducedMotion:function(e){return(0,a.A)({animation:e},r.Ay.mediaQueries.reducedMotion,{animation:"none"})}},_={getComponentTokens:function(e){return r.Ay.components[e]||{}},getVariantStyles:function(e){var t,n,o,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"primary",i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"md",s=_.getComponentTokens(e),c={height:null===(t=s.height)||void 0===t?void 0:t[i],padding:null===(n=s.padding)||void 0===n?void 0:n[i],fontSize:null===(o=s.fontSize)||void 0===o?void 0:o[i],borderRadius:r.Ay.borderRadius[e]||r.Ay.borderRadius.md,transition:r.Ay.transitions.default},l={primary:{backgroundColor:r.Ay.colors.primary.main,color:r.Ay.colors.primary.contrastText,border:"1px solid ".concat(r.Ay.colors.primary.main),"&:hover":{backgroundColor:r.Ay.colors.primary.dark},"&:focus":y({},S.focusRing())},secondary:{backgroundColor:"transparent",color:r.Ay.colors.primary.main,border:"1px solid ".concat(r.Ay.colors.primary.main),"&:hover":{backgroundColor:r.Ay.colors.primary.light},"&:focus":y({},S.focusRing())},ghost:{backgroundColor:"transparent",color:r.Ay.colors.text.primary,border:"1px solid transparent","&:hover":{backgroundColor:r.Ay.colors.interactive.hover},"&:focus":y({},S.focusRing())}};return y(y({},c),l[a])}},w={responsive:function(e){var t={};return Object.entries(e).forEach((function(e){var n=(0,g.A)(e,2),o=n[0],a=n[1];"base"===o?Object.assign(t,a):t[r.Ay.mediaQueries[o]]=a})),t},matchesBreakpoint:function(e){if("undefined"==typeof window)return!1;var t=parseInt(r.Ay.breakpoints[e]);return window.innerWidth>=t}};const O={color:A,spacing:b,typography:E,a11y:S,animation:k,component:_,responsive:w};var C,x=n(8168),T=n(3986),P=n(7528),D=n(6540),R=n(5556),I=n.n(R),W=n(1250),F=["children","variant","size","disabled","fullWidth","startIcon","endIcon","onClick"],N=W.Ay.button(C||(C=(0,P.A)(["\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: "," ",";\n  border-radius: ",";\n  font-family: ",";\n  font-weight: ",";\n  font-size: ",";\n  transition: ",";\n  cursor: ",";\n  opacity: ",";\n  width: ",";\n\n  /* Variant styles */\n  background-color: ",";\n\n  color: ",";\n\n  border: ",";\n\n  &:hover {\n    background-color: ",";\n\n    color: ",";\n  }\n\n  &:focus {\n    outline: none;\n    box-shadow: 0 0 0 3px ",";\n  }\n\n  .button-start-icon {\n    margin-right: ",";\n  }\n\n  .button-end-icon {\n    margin-left: ",";\n  }\n"])),(function(e){return"small"===e.size?r.Ay.spacing[2]:"large"===e.size?r.Ay.spacing[4]:r.Ay.spacing[3]}),(function(e){return"small"===e.size?r.Ay.spacing[3]:"large"===e.size?r.Ay.spacing[6]:r.Ay.spacing[4]}),r.Ay.borderRadius.md,r.Ay.typography.fontFamily.primary,r.Ay.typography.fontWeight.medium,(function(e){return"small"===e.size?r.Ay.typography.fontSize.sm:"large"===e.size?r.Ay.typography.fontSize.lg:r.Ay.typography.fontSize.md}),r.Ay.transitions.default,(function(e){return e.disabled?"not-allowed":"pointer"}),(function(e){return e.disabled?.6:1}),(function(e){return e.fullWidth?"100%":"auto"}),(function(e){return"primary"===e.variant?r.Ay.colors.primary.main:"secondary"===e.variant?r.Ay.colors.secondary.main:"outline"===e.variant||"text"===e.variant?"transparent":r.Ay.colors.primary.main}),(function(e){return"outline"===e.variant||"text"===e.variant?r.Ay.colors.primary.main:r.Ay.colors.primary.contrastText}),(function(e){return"outline"===e.variant?"1px solid ".concat(r.Ay.colors.primary.main):"none"}),(function(e){return"primary"===e.variant?r.Ay.colors.primary.dark:"secondary"===e.variant?r.Ay.colors.secondary.dark:"outline"===e.variant||"text"===e.variant?r.Ay.colors.primary.light:r.Ay.colors.primary.dark}),(function(e){return"outline"===e.variant||"text"===e.variant?r.Ay.colors.primary.dark:r.Ay.colors.primary.contrastText}),r.Ay.colors.primary.light,r.Ay.spacing[2],r.Ay.spacing[2]),L=function(e){var t=e.children,n=e.variant,r=void 0===n?"primary":n,o=e.size,a=void 0===o?"medium":o,i=e.disabled,s=void 0!==i&&i,c=e.fullWidth,l=void 0!==c&&c,u=e.startIcon,p=e.endIcon,d=e.onClick,m=(0,T.A)(e,F);return D.createElement(N,(0,x.A)({variant:r,size:a,disabled:s,fullWidth:l,onClick:s?void 0:d},m),u&&D.createElement("span",{className:"button-start-icon"},u),t,p&&D.createElement("span",{className:"button-end-icon"},p))};L.propTypes={children:I().node.isRequired,variant:I().oneOf(["primary","secondary","outline","text"]),size:I().oneOf(["small","medium","large"]),disabled:I().bool,fullWidth:I().bool,startIcon:I().node,endIcon:I().node,onClick:I().func};const U=L;var M,z,j,H,B,G=["children","elevation","radius","fullWidth","fullHeight"],V=["children","divider"],q=["children"],Q=["children"],K=["children","divider","align"],Y=W.Ay.div(M||(M=(0,P.A)(["\n  background-color: white;\n  border-radius: ",";\n  box-shadow: ",";\n  overflow: hidden;\n  width: ",";\n  height: ",";\n  display: flex;\n  flex-direction: column;\n"])),(function(e){return r.Ay.borderRadius[e.radius]}),(function(e){return r.Ay.shadows[e.elevation]}),(function(e){return e.fullWidth?"100%":"auto"}),(function(e){return e.fullHeight?"100%":"auto"})),J=W.Ay.div(z||(z=(0,P.A)(["\n  padding: ",";\n  border-bottom: ",";\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n"])),r.Ay.spacing[4],(function(e){return e.divider?"1px solid ".concat(r.Ay.colors.neutral[200]):"none"})),X=W.Ay.h3(j||(j=(0,P.A)(["\n  margin: 0;\n  font-size: ",";\n  font-weight: ",";\n  color: ",";\n"])),r.Ay.typography.fontSize.lg,r.Ay.typography.fontWeight.semibold,r.Ay.colors.neutral[900]),$=W.Ay.div(H||(H=(0,P.A)(["\n  padding: ",";\n  flex: 1;\n"])),r.Ay.spacing[4]),Z=W.Ay.div(B||(B=(0,P.A)(["\n  padding: ",";\n  border-top: ",";\n  display: flex;\n  align-items: center;\n  justify-content: ",";\n  gap: ",";\n"])),r.Ay.spacing[4],(function(e){return e.divider?"1px solid ".concat(r.Ay.colors.neutral[200]):"none"}),(function(e){return"right"===e.align?"flex-end":"center"===e.align?"center":"flex-start"}),r.Ay.spacing[2]),ee=function(e){var t=e.children,n=e.elevation,r=void 0===n?"md":n,o=e.radius,a=void 0===o?"md":o,i=e.fullWidth,s=void 0!==i&&i,c=e.fullHeight,l=void 0!==c&&c,u=(0,T.A)(e,G);return D.createElement(Y,(0,x.A)({elevation:r,radius:a,fullWidth:s,fullHeight:l},u),t)};ee.Header=function(e){var t=e.children,n=e.divider,r=void 0!==n&&n,o=(0,T.A)(e,V);return D.createElement(J,(0,x.A)({divider:r},o),t)},ee.Title=function(e){var t=e.children,n=(0,T.A)(e,q);return D.createElement(X,n,t)},ee.Content=function(e){var t=e.children,n=(0,T.A)(e,Q);return D.createElement($,n,t)},ee.Footer=function(e){var t=e.children,n=e.divider,r=void 0===n||n,o=e.align,a=void 0===o?"right":o,i=(0,T.A)(e,K);return D.createElement(Z,(0,x.A)({divider:r,align:a},i),t)},ee.propTypes={children:I().node.isRequired,elevation:I().oneOf(["none","sm","md","lg","xl","2xl"]),radius:I().oneOf(["none","sm","md","lg","xl","2xl","3xl","full"]),fullWidth:I().bool,fullHeight:I().bool},ee.Header.propTypes={children:I().node.isRequired,divider:I().bool},ee.Title.propTypes={children:I().node.isRequired},ee.Content.propTypes={children:I().node.isRequired},ee.Footer.propTypes={children:I().node.isRequired,divider:I().bool,align:I().oneOf(["left","center","right"])};const te=ee;var ne,re,oe,ae,ie,se,ce,le,ue,pe,de,me,ge,he,fe,ye,ve,Ae,be,Ee,Se,ke=["label","helperText","error","fullWidth","prefix","suffix","disabled"],_e=W.Ay.div(ne||(ne=(0,P.A)(["\n  display: flex;\n  flex-direction: column;\n  width: ",";\n"])),(function(e){return e.fullWidth?"100%":"auto"})),we=W.Ay.label(re||(re=(0,P.A)(["\n  font-size: ",";\n  font-weight: ",";\n  color: ",";\n  margin-bottom: ",";\n"])),r.Ay.typography.fontSize.sm,r.Ay.typography.fontWeight.medium,r.Ay.colors.neutral[700],r.Ay.spacing[1]),Oe=W.Ay.input(oe||(oe=(0,P.A)(["\n  font-family: ",";\n  font-size: ",";\n  color: ",";\n  background-color: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  padding: "," ",";\n  transition: ",";\n  width: 100%;\n\n  &:focus {\n    outline: none;\n    border-color: ",";\n    box-shadow: 0 0 0 3px ",";\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n    opacity: 0.7;\n  }\n\n  &::placeholder {\n    color: ",";\n  }\n"])),(null===r.Ay||void 0===r.Ay||null===(ae=r.Ay.typography)||void 0===ae||null===(ae=ae.fontFamily)||void 0===ae?void 0:ae.primary)||"Inter, sans-serif",(null===r.Ay||void 0===r.Ay||null===(ie=r.Ay.typography)||void 0===ie||null===(ie=ie.fontSize)||void 0===ie?void 0:ie.md)||"16px",(null===r.Ay||void 0===r.Ay||null===(se=r.Ay.colors)||void 0===se||null===(se=se.neutral)||void 0===se?void 0:se[900])||"#111827",(function(e){var t;return e.disabled?(null===r.Ay||void 0===r.Ay||null===(t=r.Ay.colors)||void 0===t||null===(t=t.neutral)||void 0===t?void 0:t[100])||"#F3F4F6":"white"}),(function(e){var t,n,o;return e.error?(null===r.Ay||void 0===r.Ay||null===(t=r.Ay.colors)||void 0===t||null===(t=t.error)||void 0===t?void 0:t.main)||"#DC2626":e.focused?(null===r.Ay||void 0===r.Ay||null===(n=r.Ay.colors)||void 0===n||null===(n=n.primary)||void 0===n?void 0:n.main)||"#2563EB":(null===r.Ay||void 0===r.Ay||null===(o=r.Ay.colors)||void 0===o||null===(o=o.neutral)||void 0===o?void 0:o[300])||"#D1D5DB"}),(null===r.Ay||void 0===r.Ay||null===(ce=r.Ay.borderRadius)||void 0===ce?void 0:ce.md)||"4px",(null===r.Ay||void 0===r.Ay||null===(le=r.Ay.spacing)||void 0===le?void 0:le[2])||"8px",(null===r.Ay||void 0===r.Ay||null===(ue=r.Ay.spacing)||void 0===ue?void 0:ue[3])||"12px",(null===r.Ay||void 0===r.Ay||null===(pe=r.Ay.transitions)||void 0===pe?void 0:pe.default)||"all 0.2s ease-in-out",(null===r.Ay||void 0===r.Ay||null===(de=r.Ay.colors)||void 0===de||null===(de=de.primary)||void 0===de?void 0:de.main)||"#2563EB",(null===r.Ay||void 0===r.Ay||null===(me=r.Ay.colors)||void 0===me||null===(me=me.primary)||void 0===me?void 0:me.light)||"rgba(37, 99, 235, 0.2)",(null===r.Ay||void 0===r.Ay||null===(ge=r.Ay.colors)||void 0===ge||null===(ge=ge.neutral)||void 0===ge?void 0:ge[400])||"#9CA3AF"),Ce=W.Ay.div(he||(he=(0,P.A)(["\n  display: flex;\n  align-items: center;\n  margin-right: ",";\n"])),(null===r.Ay||void 0===r.Ay||null===(fe=r.Ay.spacing)||void 0===fe?void 0:fe[2])||"8px"),xe=W.Ay.div(ye||(ye=(0,P.A)(["\n  display: flex;\n  align-items: center;\n  margin-left: ",";\n"])),(null===r.Ay||void 0===r.Ay||null===(ve=r.Ay.spacing)||void 0===ve?void 0:ve[2])||"8px"),Te=W.Ay.div(Ae||(Ae=(0,P.A)(["\n  display: flex;\n  align-items: center;\n  position: relative;\n  width: 100%;\n"]))),Pe=W.Ay.div(be||(be=(0,P.A)(["\n  font-size: ",";\n  margin-top: ",";\n  color: ",";\n"])),(null===r.Ay||void 0===r.Ay||null===(Ee=r.Ay.typography)||void 0===Ee||null===(Ee=Ee.fontSize)||void 0===Ee?void 0:Ee.xs)||"12px",(null===r.Ay||void 0===r.Ay||null===(Se=r.Ay.spacing)||void 0===Se?void 0:Se[1])||"4px",(function(e){var t,n;return e.error?(null===r.Ay||void 0===r.Ay||null===(t=r.Ay.colors)||void 0===t||null===(t=t.error)||void 0===t?void 0:t.main)||"#DC2626":(null===r.Ay||void 0===r.Ay||null===(n=r.Ay.colors)||void 0===n||null===(n=n.neutral)||void 0===n?void 0:n[500])||"#6B7280"})),De=(0,D.forwardRef)((function(e,t){var n=e.label,r=e.helperText,o=e.error,a=e.fullWidth,i=void 0!==a&&a,s=e.prefix,c=e.suffix,l=e.disabled,u=void 0!==l&&l,p=(0,T.A)(e,ke),d=D.useState(!1),m=(0,g.A)(d,2),h=m[0],f=m[1];return D.createElement(_e,{fullWidth:i},n&&D.createElement(we,null,n),D.createElement(Te,null,s&&D.createElement(Ce,null,s),D.createElement(Oe,(0,x.A)({ref:t,disabled:u,error:o,focused:h,onFocus:function(e){f(!0),p.onFocus&&p.onFocus(e)},onBlur:function(e){f(!1),p.onBlur&&p.onBlur(e)}},p)),c&&D.createElement(xe,null,c)),r&&D.createElement(Pe,{error:o},r))}));De.displayName="Input",De.propTypes={label:I().string,helperText:I().string,error:I().bool,fullWidth:I().bool,prefix:I().node,suffix:I().node,disabled:I().bool,onFocus:I().func,onBlur:I().func};const Re=De;var Ie,We,Fe,Ne,Le=["label","helperText","error","fullWidth","options","disabled"],Ue=W.Ay.div(Ie||(Ie=(0,P.A)(["\n  display: flex;\n  flex-direction: column;\n  width: ",";\n"])),(function(e){return e.fullWidth?"100%":"auto"})),Me=W.Ay.label(We||(We=(0,P.A)(["\n  font-size: ",";\n  font-weight: ",";\n  color: ",";\n  margin-bottom: ",";\n"])),r.Ay.typography.fontSize.sm,r.Ay.typography.fontWeight.medium,r.Ay.colors.neutral[700],r.Ay.spacing[1]),ze=W.Ay.select(Fe||(Fe=(0,P.A)(["\n  font-family: ",";\n  font-size: ",";\n  color: ",";\n  background-color: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  padding: "," ",";\n  transition: ",";\n  width: 100%;\n  appearance: none;\n  background-image: url(\"data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E\");\n  background-position: right "," center;\n  background-repeat: no-repeat;\n  background-size: 1.5em 1.5em;\n  padding-right: 2.5em;\n\n  &:focus {\n    outline: none;\n    border-color: ",";\n    box-shadow: 0 0 0 3px ",";\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n    opacity: 0.7;\n  }\n"])),r.Ay.typography.fontFamily.primary,r.Ay.typography.fontSize.md,r.Ay.colors.neutral[900],(function(e){return e.disabled?r.Ay.colors.neutral[100]:"white"}),(function(e){return e.error?r.Ay.colors.error.main:e.focused?r.Ay.colors.primary.main:r.Ay.colors.neutral[300]}),r.Ay.borderRadius.md,r.Ay.spacing[2],r.Ay.spacing[3],r.Ay.transitions.default,r.Ay.spacing[2],r.Ay.colors.primary.main,r.Ay.colors.primary.light),je=W.Ay.div(Ne||(Ne=(0,P.A)(["\n  font-size: ",";\n  margin-top: ",";\n  color: ",";\n"])),r.Ay.typography.fontSize.xs,r.Ay.spacing[1],(function(e){return e.error?r.Ay.colors.error.main:r.Ay.colors.neutral[500]})),He=(0,D.forwardRef)((function(e,t){var n=e.label,r=e.helperText,o=e.error,a=e.fullWidth,i=void 0!==a&&a,s=e.options,c=void 0===s?[]:s,l=e.disabled,u=void 0!==l&&l,p=(0,T.A)(e,Le),d=D.useState(!1),m=(0,g.A)(d,2),h=m[0],f=m[1];return D.createElement(Ue,{fullWidth:i},n&&D.createElement(Me,null,n),D.createElement(ze,(0,x.A)({ref:t,disabled:u,error:o,focused:h,onFocus:function(e){f(!0),p.onFocus&&p.onFocus(e)},onBlur:function(e){f(!1),p.onBlur&&p.onBlur(e)}},p),c.map((function(e){return D.createElement("option",{key:e.value,value:e.value},e.label)}))),r&&D.createElement(je,{error:o},r))}));He.displayName="Select",He.propTypes={label:I().string,helperText:I().string,error:I().bool,fullWidth:I().bool,options:I().arrayOf(I().shape({value:I().oneOfType([I().string,I().number]).isRequired,label:I().string.isRequired})),disabled:I().bool,onFocus:I().func,onBlur:I().func};const Be=He;var Ge,Ve,qe,Qe,Ke,Ye,Je,Xe,$e,Ze=W.Ay.span(Ge||(Ge=(0,P.A)(["\n  font-family: ",";\n  font-size: ",";\n  font-weight: ",";\n  color: ",";\n  line-height: ",";\n  margin: 0;\n  ","\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.typography)||void 0===t?void 0:t.fontFamily)||"Inter, sans-serif"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.typography)||void 0===t||null===(t=t.fontSize)||void 0===t?void 0:t[e.size||"md"])||"16px"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.typography)||void 0===t||null===(t=t.fontWeight)||void 0===t?void 0:t[e.weight||"normal"])||400}),(function(e){var t,n;return e.color?(null===(t=e.theme)||void 0===t||null===(t=t.colors)||void 0===t?void 0:t[e.color])||e.color:(null===(n=e.theme)||void 0===n||null===(n=n.colors)||void 0===n||null===(n=n.text)||void 0===n?void 0:n.primary)||"#111827"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.typography)||void 0===t||null===(t=t.lineHeight)||void 0===t?void 0:t[e.lineHeight||"normal"])||1.5}),(function(e){return e.truncate&&"\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  "})),et=((0,W.Ay)(Ze)(Ve||(Ve=(0,P.A)(["\n  display: block;\n  margin-bottom: ",";\n"])),(function(e){return e.theme.spacing[3]})),(0,W.Ay)(Ze)(qe||(qe=(0,P.A)(["\n  font-weight: ",";\n  line-height: ",";\n  margin-bottom: ",";\n"])),(function(e){return e.theme.typography.fontWeight.bold}),(function(e){return e.theme.typography.lineHeight.tight}),(function(e){return e.theme.spacing[3]})));(0,W.Ay)(et)(Qe||(Qe=(0,P.A)(["\n  font-size: ",";\n"])),(function(e){return e.theme.typography.fontSize.xxxl})),(0,W.Ay)(et)(Ke||(Ke=(0,P.A)(["\n  font-size: ",";\n"])),(function(e){return e.theme.typography.fontSize.xxl})),(0,W.Ay)(et)(Ye||(Ye=(0,P.A)(["\n  font-size: ",";\n"])),(function(e){return e.theme.typography.fontSize.xl})),(0,W.Ay)(et)(Je||(Je=(0,P.A)(["\n  font-size: ",";\n"])),(function(e){return e.theme.typography.fontSize.lg})),(0,W.Ay)(et)(Xe||(Xe=(0,P.A)(["\n  font-size: ",";\n"])),(function(e){return e.theme.typography.fontSize.md})),(0,W.Ay)(et)($e||($e=(0,P.A)(["\n  font-size: ",";\n"])),(function(e){return e.theme.typography.fontSize.sm}));const tt=Ze,nt=W.Ay;var rt="./global.css",ot="2.0.0",at={prefix:"ds-",enableRTL:!1,enableDarkMode:!0,enableHighContrast:!0,enableReducedMotion:!0}},7053:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(436),o=n(2284),a=n(4467),i=n(3029),s=n(2901);function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){(0,a.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const u=new(function(){function e(){(0,i.A)(this,e),this.socket=null,this.connected=!1,this.connecting=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=10,this.reconnectInterval=2e3,this.maxReconnectInterval=3e4,this.reconnectDecay=1.5,this.eventListeners={},this.offlineQueue=[],this.isReconnecting=!1,this.isClosing=!1,this.isSuspended=!1,this.messageQueue=[],this.lastError=null,this.heartbeatInterval=3e4,this.heartbeatTimeoutId=null,this.missedHeartbeats=0,this.maxMissedHeartbeats=3,this.connectionTimeoutId=null,this.debug=!1,this.instance=null}return(0,s.A)(e,[{key:"init",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.options=l({autoConnect:!1,autoReconnect:!0,reconnectInterval:2e3,maxReconnectAttempts:10,debug:!1,queueOfflineMessages:!0},e),this.connected=!1,this.connecting=!1,this.debug=this.options.debug,this.options.autoConnect&&this.connect(),this}},{key:"setReconnectOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.maxReconnectAttempts=e.maxAttempts||this.maxReconnectAttempts,this.reconnectInterval=e.initialDelay||this.reconnectInterval,this.maxReconnectInterval=e.maxDelay||this.maxReconnectInterval,this.reconnectDecay=e.useExponentialBackoff?1.5:1,this}},{key:"configureSecurityOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.securityOptions=l({validateMessages:!0,sanitizeMessages:!0,rateLimiting:{enabled:!0,maxMessagesPerSecond:20,burstSize:50}},e),this}},{key:"configurePerformanceOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.performanceOptions=l({compression:{enabled:!0,threshold:1024,level:6},batchingEnabled:!0,batchInterval:50,maxBatchSize:20,offlineQueueEnabled:!0,offlineStorage:{enabled:!0,persistKey:"websocket_offline_queue"}},e),this}},{key:"isConnected",value:function(){try{return null!==this.socket&&void 0!==this.socket&&this.socket.readyState===WebSocket.OPEN}catch(e){return console.error("Error checking connection status:",e),!1}}},{key:"connect",value:function(e){var t=this;return new Promise((function(n,r){if(t.isConnected())n(t);else if(t.connecting)r(new Error("Connection already in progress"));else{var o;t.connecting=!0,e&&(t.endpoint=e),o=t.url?t.url:"ws://localhost:8765",t.debug&&console.log("Connecting to WebSocket at ".concat(o));try{t.socket=new WebSocket(o),t.socket.onopen=function(e){t.connected=!0,t.connecting=!1,t.reconnectAttempts=0,t.debug&&console.log("WebSocket connection established"),t.startHeartbeat(),t.processOfflineQueue(),t.triggerEvent("open",e),t.triggerEvent("connect",e),n(t)},t.socket.onclose=function(e){t.connected=!1,t.connecting=!1,t.debug&&console.log("WebSocket connection closed: ".concat(e.code," ").concat(e.reason)),t.stopHeartbeat(),t.triggerEvent("close",e),t.triggerEvent("disconnect",e),!t.isClosing&&t.options.autoReconnect&&t.reconnect(),t.isClosing=!1},t.socket.onerror=function(e){console.error("WebSocket error occurred:",e),t.debug&&(console.log("WebSocket readyState:",t.socket.readyState),console.log("Connection URL:",o),console.log("Browser:",navigator.userAgent));var n=t._handleConnectionError(e,t.reconnectAttempts);t.connecting&&(t.connecting=!1,r(n)),t.triggerEvent("error",{originalEvent:e,url:o,timestamp:Date.now(),reconnectAttempts:t.reconnectAttempts,error:n})},t.socket.onmessage=function(e){var n=e.data;try{if("string"==typeof n&&n.startsWith("C")){var r=n.substring(1);n=t._decompressMessage(r)}if("string"==typeof n&&(n=JSON.parse(n)),t.debug&&console.log("WebSocket message received:",n),"pong"===n.type)return t.missedHeartbeats=0,void t.triggerEvent("pong",n);if("batch"===n.type&&Array.isArray(n.messages))return n.messages.forEach((function(e){t.triggerEvent("message",e),e.type&&t.triggerEvent(e.type,e)})),void t.triggerEvent("batch",n);t.triggerEvent("message",n),n.type&&t.triggerEvent(n.type,n)}catch(n){t._handleMessageError(n,e.data)}}}catch(e){t.connecting=!1,t.lastError=e,t.debug&&console.error("Error creating WebSocket:",e),r(e)}}}))}},{key:"disconnect",value:function(){if(this.socket){this.isClosing=!0;try{this.stopHeartbeat(),this.socket.close(1e3,"Normal closure"),this.debug&&console.log("WebSocket disconnected")}catch(e){console.error("Error disconnecting WebSocket:",e)}}}},{key:"close",value:function(){return this.disconnect()}},{key:"reconnect",value:function(){var e=this;if(!this.isReconnecting){if(this.isReconnecting=!0,this.reconnectAttempts>=this.maxReconnectAttempts)return this.debug&&console.log("Maximum reconnect attempts (".concat(this.maxReconnectAttempts,") reached")),this.triggerEvent("max_retries_reached",{attempts:this.reconnectAttempts,max:this.maxReconnectAttempts}),void(this.isReconnecting=!1);this.reconnectAttempts++;var t=this.reconnectInterval*Math.pow(this.reconnectDecay,this.reconnectAttempts-1),n=.8*(t=Math.min(t,this.maxReconnectInterval)),r=1.2*t;t=Math.floor(n+Math.random()*(r-n)),this.debug&&console.log("Reconnecting in ".concat(t,"ms (attempt ").concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,")")),this.triggerEvent("reconnecting",{attempt:this.reconnectAttempts,maxAttempts:this.maxReconnectAttempts,delay:t}),setTimeout((function(){e.isReconnecting=!1,e.connect().catch((function(e){console.error("Reconnection failed:",e)}))}),t)}}},{key:"suspend",value:function(){if(!this.isSuspended)return this.isSuspended=!0,this.debug&&console.log("WebSocket connection suspended"),this.wasPreviouslyConnected=this.isConnected(),this.socket&&(this.isClosing=!0,this.stopHeartbeat(),this.socket.close(1e3,"Connection suspended")),this.triggerEvent("suspended",{timestamp:Date.now(),wasPreviouslyConnected:this.wasPreviouslyConnected}),this}},{key:"resume",value:function(){if(this.isSuspended)return this.isSuspended=!1,this.debug&&console.log("Resuming WebSocket connection"),this.wasPreviouslyConnected&&this.reconnect(),this.triggerEvent("resumed",{timestamp:Date.now(),reconnecting:this.wasPreviouslyConnected}),this}},{key:"handleOnline",value:function(){return this.debug&&console.log("Network is online"),this.wasPreviouslyConnected&&this.reconnect(),this.triggerEvent("network_status_change",{status:"online",timestamp:Date.now()}),this}},{key:"handleOffline",value:function(){return this.debug&&console.log("Network is offline"),this.wasPreviouslyConnected=this.isConnected(),this.triggerEvent("network_status_change",{status:"offline",timestamp:Date.now()}),this}},{key:"send",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(r,a){if(t.isConnected())try{var i,s,c,u,p,d;if(null!==(i=t.securityOptions)&&void 0!==i&&null!==(i=i.rateLimiting)&&void 0!==i&&i.enabled){var m=Date.now();t._rateLimitState||(t._rateLimitState={messageCount:0,windowStart:m,burstCount:0,burstStart:m,queue:[]});var g=t._rateLimitState,h=t.securityOptions.rateLimiting,f=h.maxMessagesPerSecond,y=h.burstSize;if(m-g.windowStart>1e3&&(g.messageCount=0,g.windowStart=m),m-g.burstStart>100&&(g.burstCount=0,g.burstStart=m),g.messageCount>=f||g.burstCount>=y)return n.important?g.queue.unshift({message:e,options:n,timestamp:m}):g.queue.push({message:e,options:n,timestamp:m}),t.triggerEvent("rate_limited",{queueLength:g.queue.length,messageCount:g.messageCount,burstCount:g.burstCount}),g.processingQueue||(g.processingQueue=!0,setTimeout((function(){return t._processRateLimitQueue()}),100)),void r({queued:!0,rateLimited:!0});g.messageCount++,g.burstCount++}var v,A=e;if("object"!==(0,o.A)(A)||A.timestamp||(A=l(l({},A),{},{timestamp:Date.now()})),"object"!==(0,o.A)(A)||A.id||(A=l(l({},A),{},{id:t._generateMessageId()})),null!==(s=t.securityOptions)&&void 0!==s&&s.validateMessages){var b=t._validateMessage(A);if(!b.valid)return void a(new Error("Invalid message: ".concat(b.reason)))}if(null!==(c=t.securityOptions)&&void 0!==c&&c.sanitizeMessages&&(A=t._sanitizeMessage(A)),null!==(u=t.securityOptions)&&void 0!==u&&u.authToken&&(A=l(l({},A),{},{auth:t.securityOptions.authToken})),null!==(p=t.performanceOptions)&&void 0!==p&&p.batchingEnabled&&!n.immediate)return t._addToBatch(A,n),void r({queued:!0,batched:!0});var E=!1;if(null!==(d=t.performanceOptions)&&void 0!==d&&null!==(d=d.compression)&&void 0!==d&&d.enabled&&(n.compress||t.performanceOptions.compression.enabled)){var S="string"==typeof A?A:JSON.stringify(A);if(S.length>t.performanceOptions.compression.threshold)try{v=t._compressMessage(S),E=!0,v="C".concat(v)}catch(e){console.error("Error compressing message:",e),v=S}else v=S}else v="string"==typeof A?A:JSON.stringify(A);t.socket.send(v),t.debug&&console.log("WebSocket message sent".concat(E?" (compressed)":"",":"),A),t.triggerEvent("message_sent",{message:A,compressed:E,size:v.length}),r({sent:!0,compressed:E})}catch(e){console.error("Error sending WebSocket message:",e),a(e)}else t.options.queueOfflineMessages?(t.queueMessage(e,n),r({queued:!0})):a(new Error("WebSocket is not connected"))}))}},{key:"_generateMessageId",value:function(){return"".concat(Date.now(),"-").concat(Math.random().toString(36).substring(2,11))}},{key:"_validateMessage",value:function(e){return e?"object"!==(0,o.A)(e)?{valid:!1,reason:"Message must be an object"}:e.type?{valid:!0}:{valid:!1,reason:"Message must have a type"}:{valid:!1,reason:"Message is empty"}}},{key:"_sanitizeMessage",value:function(e){var t=JSON.parse(JSON.stringify(e)),n=function(e){return"string"==typeof e?e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/\//g,"&#x2F;"):e},r=function(e){if(!e||"object"!==(0,o.A)(e))return e;if(Array.isArray(e))return e.map((function(e){return r(e)}));var t={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){var i=e[a];"object"===(0,o.A)(i)&&null!==i?t[a]=r(i):t[a]=n(i)}return t};return r(t)}},{key:"_compressMessage",value:function(e){try{return btoa(e)}catch(t){return console.error("Error compressing message:",t),e}}},{key:"_decompressMessage",value:function(e){try{return atob(e)}catch(t){return console.error("Error decompressing message:",t),e}}},{key:"_addToBatch",value:function(e,t){var n=this;this._batch||(this._batch={messages:[],timer:null}),this._batch.messages.push({message:e,options:t}),this._batch.timer||(this._batch.timer=setTimeout((function(){n._sendBatch()}),this.performanceOptions.batchInterval)),this._batch.messages.length>=this.performanceOptions.maxBatchSize&&(clearTimeout(this._batch.timer),this._batch.timer=null,this._sendBatch())}},{key:"_sendBatch",value:function(){var e=this;if(this._batch&&0!==this._batch.messages.length){var t={type:"batch",messages:this._batch.messages.map((function(e){return e.message})),timestamp:Date.now(),count:this._batch.messages.length},n=this._batch.messages;this._batch.messages=[],this._batch.timer=null,this.send(t,{immediate:!0}).catch((function(t){console.error("Error sending batch:",t),n.forEach((function(t){e.queueMessage(t.message,t.options)}))}))}}},{key:"_processRateLimitQueue",value:function(){var e=this;if(this._rateLimitState&&0!==this._rateLimitState.queue.length){var t=Date.now(),n=this._rateLimitState,r=this.securityOptions.rateLimiting.maxMessagesPerSecond;if(t-n.windowStart>1e3&&(n.messageCount=0,n.windowStart=t),n.messageCount<r){var o=n.queue.shift();this.send(o.message,l(l({},o.options),{},{immediate:!0})).catch((function(e){console.error("Error sending queued message:",e)})),n.messageCount++,n.queue.length>0?setTimeout((function(){return e._processRateLimitQueue()}),50):n.processingQueue=!1}else setTimeout((function(){return e._processRateLimitQueue()}),100)}else this._rateLimitState&&(this._rateLimitState.processingQueue=!1)}},{key:"sendMessage",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.send(e,t)}},{key:"queueMessage",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.debug&&console.log("Queuing message for later delivery:",e),this.offlineQueue.push({message:e,options:t}),this.triggerEvent("message_queued_offline",{message:e,queueLength:this.offlineQueue.length}),this.offlineQueue.length}},{key:"processOfflineQueue",value:function(){var e=this;if(0!==this.offlineQueue.length){this.debug&&console.log("Processing offline queue (".concat(this.offlineQueue.length," messages)"));var t=(0,r.A)(this.offlineQueue);this.offlineQueue=[],t.forEach((function(t){var n=t.message,r=t.options;e.send(n,r).catch((function(e){console.error("Error sending queued message:",e)}))})),this.triggerEvent("offline_queue_processed",{processedCount:t.length})}}},{key:"startHeartbeat",value:function(){var e=this;this.stopHeartbeat(),this.missedHeartbeats=0,this.heartbeatTimeoutId=setInterval((function(){if(e.isConnected()){if(e.missedHeartbeats++,e.missedHeartbeats>=e.maxMissedHeartbeats)return e.debug&&console.warn("Missed ".concat(e.missedHeartbeats," heartbeats, reconnecting...")),e.triggerEvent("heartbeat_timeout",{missed:e.missedHeartbeats,max:e.maxMissedHeartbeats}),void e.reconnect();e.send({type:"ping",timestamp:Date.now()}).catch((function(e){console.error("Error sending heartbeat:",e)}))}else e.stopHeartbeat()}),this.heartbeatInterval)}},{key:"stopHeartbeat",value:function(){this.heartbeatTimeoutId&&(clearInterval(this.heartbeatTimeoutId),this.heartbeatTimeoutId=null)}},{key:"addEventListener",value:function(e,t){return this.eventListeners[e]||(this.eventListeners[e]=[]),this.eventListeners[e].push(t),this}},{key:"removeEventListener",value:function(e,t){return this.eventListeners[e]?(this.eventListeners[e]=this.eventListeners[e].filter((function(e){return e!==t})),this):this}},{key:"on",value:function(e,t){return this.addEventListener(e,t)}},{key:"off",value:function(e,t){return this.removeEventListener(e,t)}},{key:"triggerEvent",value:function(e,t){this.eventListeners[e]&&this.eventListeners[e].forEach((function(n){try{n(t)}catch(t){console.error("Error in ".concat(e," event listener:"),t)}}))}},{key:"getConnectionState",value:function(){var e="CLOSED";if(this.socket)switch(this.socket.readyState){case WebSocket.CONNECTING:e="CONNECTING";break;case WebSocket.OPEN:e="OPEN";break;case WebSocket.CLOSING:e="CLOSING";break;case WebSocket.CLOSED:e="CLOSED"}return{connected:this.isConnected(),connecting:this.connecting,readyState:this.socket?this.socket.readyState:WebSocket.CLOSED,readyStateText:e,reconnectAttempts:this.reconnectAttempts,maxReconnectAttempts:this.maxReconnectAttempts,url:this.url,lastError:this.lastError}}},{key:"getOfflineQueueStatus",value:function(){return{count:this.offlineQueue.length,enabled:this.options.queueOfflineMessages}}},{key:"clearOfflineQueue",value:function(){var e=this.offlineQueue.length;return this.offlineQueue=[],this.triggerEvent("offline_queue_cleared",{count:e}),e}},{key:"_getDefaultWebSocketUrl",value:function(){var e="https:"===window.location.protocol?"wss:":"ws:",t=window.location.host;return"".concat(e,"//").concat(t,"/ws")}},{key:"_createError",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=new Error(t);return r.type=e,r.details=n,r.timestamp=Date.now(),this.debug&&console.error("WebSocketError [".concat(e,"]: ").concat(t),n),this.triggerEvent("error",r),r}},{key:"_handleConnectionError",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this.lastError=e;var r="CONNECTION_ERROR",o="Failed to connect to WebSocket server";if(e.code)switch(e.code){case 1e3:r="NORMAL_CLOSURE",o="Connection closed normally";break;case 1001:r="GOING_AWAY",o="Server is going away";break;case 1002:r="PROTOCOL_ERROR",o="Protocol error";break;case 1003:r="UNSUPPORTED_DATA",o="Unsupported data";break;case 1005:r="NO_STATUS",o="No status code was provided";break;case 1006:r="ABNORMAL_CLOSURE",o="Connection closed abnormally";break;case 1007:r="INVALID_FRAME_PAYLOAD",o="Invalid frame payload data";break;case 1008:r="POLICY_VIOLATION",o="Policy violation";break;case 1009:r="MESSAGE_TOO_BIG",o="Message too big";break;case 1010:r="MISSING_EXTENSION",o="Required extension is missing";break;case 1011:r="INTERNAL_ERROR",o="Internal server error";break;case 1012:r="SERVICE_RESTART",o="Service is restarting";break;case 1013:r="TRY_AGAIN_LATER",o="Try again later";break;case 1014:r="BAD_GATEWAY",o="Bad gateway";break;case 1015:r="TLS_HANDSHAKE_FAILURE",o="TLS handshake failure";break;default:r="CODE_".concat(e.code),o="WebSocket error code ".concat(e.code)}else e.message&&(e.message.includes("timeout")?(r="CONNECTION_TIMEOUT",o="Connection timed out"):e.message.includes("refused")?(r="CONNECTION_REFUSED",o="Connection refused"):e.message.includes("ENOTFOUND")&&(r="HOST_NOT_FOUND",o="Host not found"));var a=this._createError(r,o,{originalError:e,attempt:n,url:this.url});switch(r){case"NORMAL_CLOSURE":break;case"GOING_AWAY":case"SERVICE_RESTART":this.options.autoReconnect&&setTimeout((function(){return t.reconnect()}),5e3);break;case"TRY_AGAIN_LATER":this.options.autoReconnect&&setTimeout((function(){return t.reconnect()}),1e4);break;case"CONNECTION_REFUSED":case"HOST_NOT_FOUND":if(this.options.fallbackUrls&&this.options.fallbackUrls.length>0){var i=this.options.fallbackUrls[n%this.options.fallbackUrls.length];this.debug&&console.log("Trying fallback URL: ".concat(i)),this.url=i,this.options.autoReconnect&&setTimeout((function(){return t.reconnect()}),1e3)}else this.options.autoReconnect&&this.reconnect();break;case"ABNORMAL_CLOSURE":case"INTERNAL_ERROR":case"BAD_GATEWAY":if(this.options.autoReconnect){var s=Math.min(1e3*Math.pow(2,n),3e4);setTimeout((function(){return t.reconnect()}),s)}break;default:this.options.autoReconnect&&this.reconnect()}return a}},{key:"_handleMessageError",value:function(e,t){var n="MESSAGE_ERROR",r="Failed to process WebSocket message";return e.message&&(e.message.includes("JSON")?(n="INVALID_JSON",r="Invalid JSON in message"):e.message.includes("timeout")?(n="MESSAGE_TIMEOUT",r="Message processing timed out"):e.message.includes("rate limit")&&(n="RATE_LIMITED",r="Rate limit exceeded")),this._createError(n,r,{originalError:e,message:t})}}],[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e,t&&e.instance.init(t)),e.instance}}])}())},7362:(e,t,n)=>{n.d(t,{hl:()=>p,jY:()=>m,kz:()=>u});var r=n(467),o=n(4756),a=n.n(o),i=n(9730);function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return c(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){s=!0,a=e},f:function(){try{i||null==n.return||n.return()}finally{if(s)throw a}}}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var l=Boolean("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function u(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("serviceWorker"in navigator){try{if("true"===localStorage.getItem("disable_sw_temp"))return console.log("Service worker registration skipped: temporarily disabled"),void localStorage.removeItem("disable_sw_temp")}catch(e){}if(new URL({ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.PUBLIC_URL||"",window.location.href).origin!==window.location.origin)return void console.log("Service worker registration skipped: different origin");window.addEventListener("load",(function(){var t="".concat({ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.PUBLIC_URL||"","/service-worker.js");console.log("Service Worker: Using ".concat(t)),Array.from(document.querySelectorAll("script")).some((function(e){return e.src&&(e.src.includes("socket.io")||e.src.includes("websocket")||e.src.includes("ws"))}))&&console.log("Service Worker: Detected active WebSocket scripts, registering with caution"),l?(function(e,t){fetch(e,{headers:{"Service-Worker":"script"},cache:"reload"}).then((function(n){var r=n.headers.get("content-type");404===n.status||null!=r&&-1===r.indexOf("javascript")?(console.warn("Service Worker: Invalid service worker detected. Attempting to unregister."),navigator.serviceWorker.getRegistrations().then((function(e){var t,n=s(e);try{for(n.s();!(t=n.n()).done;){var r=t.value;console.log("Unregistering service worker:",r.scope),r.unregister()}}catch(e){n.e(e)}finally{n.f()}window.location.reload()}))):(console.log("Service Worker: Valid service worker found. Registering..."),function(e,t){navigator.serviceWorker.register(e).then((function(e){e.onupdatefound=function(){var n=e.installing;null!=n&&(n.onstatechange=function(){"installed"===n.state&&(navigator.serviceWorker.controller?(console.log("Service Worker: New content is available and will be used when all tabs for this page are closed"),t&&t.onUpdate&&t.onUpdate(e)):(console.log("Service Worker: Content is cached for offline use"),t&&t.onSuccess&&t.onSuccess(e)))})}})).catch((function(e){console.error("Service Worker: Error during registration:",e)}))}(e,t))})).catch((function(e){console.log("Service Worker: No internet connection or error occurred:",e),console.log("App is running in offline mode.")}))}(t,e),navigator.serviceWorker.ready.then((function(){console.log("Service Worker: Ready and running in development mode")}))):function(e,t){try{var n=new i.JK(e);"function"!=typeof n.messageSkipWaiting&&(n.messageSkipWaiting=function(){console.log("Service Worker: Custom messageSkipWaiting called"),n.controlling?n.controlling.postMessage("skipWaiting"):navigator.serviceWorker.ready.then((function(e){e.waiting&&e.waiting.postMessage("skipWaiting")}))}),n.addEventListener("installed",(function(e){if(e.isUpdate){if(console.log("Service Worker: Updated service worker installed"),t&&t.onUpdate)try{t.onUpdate(n)}catch(e){console.error("Service Worker: Error in onUpdate callback:",e)}}else if(console.log("Service Worker: New service worker installed"),t&&t.onSuccess)try{t.onSuccess(n)}catch(e){console.error("Service Worker: Error in onSuccess callback:",e)}})),n.addEventListener("activated",(function(e){e.isUpdate?console.log("Service Worker: Updated service worker activated"):console.log("Service Worker: New service worker activated")})),n.addEventListener("waiting",(function(e){if(console.log("Service Worker: New version waiting to be activated"),t&&t.onWaiting)try{t.onWaiting(n)}catch(e){console.error("Service Worker: Error in onWaiting callback:",e)}})),n.addEventListener("controlling",(function(e){console.log("Service Worker: Controlling the page")})),n.addEventListener("redundant",(function(e){console.warn("Service Worker: The installing service worker became redundant")})),n.addEventListener("error",(function(e){console.error("Service Worker: Error during operation:",e)})),n.register().catch((function(e){console.error("Service Worker: Registration failed:",e)}))}catch(e){console.error("Service Worker: Error during registration:",e)}}(t,e)}))}else console.log("Service Worker: Registration skipped - not supported")}function p(){return d.apply(this,arguments)}function d(){return(d=(0,r.A)(a().mark((function e(){var t,n,r,o,i,c,l,u;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("serviceWorker"in navigator){e.next=2;break}return e.abrupt("return",!1);case 2:return e.prev=2,e.next=5,navigator.serviceWorker.getRegistrations();case 5:if(0!==(t=e.sent).length){e.next=9;break}return console.log("No service workers to clean"),e.abrupt("return",!1);case 9:console.log("Forcefully cleaning ".concat(t.length," service workers")),n=s(t),e.prev=11,n.s();case 13:if((r=n.n()).done){e.next=26;break}return o=r.value,e.prev=15,e.next=18,o.unregister();case 18:console.log("Unregistered service worker:",o.scope),e.next=24;break;case 21:e.prev=21,e.t0=e.catch(15),console.error("Failed to unregister service worker:",e.t0);case 24:e.next=13;break;case 26:e.next=31;break;case 28:e.prev=28,e.t1=e.catch(11),n.e(e.t1);case 31:return e.prev=31,n.f(),e.finish(31);case 34:if(!("caches"in window)){e.next=62;break}return e.prev=35,e.next=38,caches.keys();case 38:i=e.sent,c=s(i),e.prev=40,c.s();case 42:if((l=c.n()).done){e.next=49;break}return u=l.value,e.next=46,caches.delete(u);case 46:console.log("Deleted cache:",u);case 47:e.next=42;break;case 49:e.next=54;break;case 51:e.prev=51,e.t2=e.catch(40),c.e(e.t2);case 54:return e.prev=54,c.f(),e.finish(54);case 57:e.next=62;break;case 59:e.prev=59,e.t3=e.catch(35),console.error("Failed to clear caches:",e.t3);case 62:try{localStorage.setItem("disable_sw_temp","true")}catch(e){}return e.abrupt("return",!0);case 66:return e.prev=66,e.t4=e.catch(2),console.error("Error cleaning service workers:",e.t4),e.abrupt("return",!1);case 70:case"end":return e.stop()}}),e,null,[[2,66],[11,28,31,34],[15,21],[35,59],[40,51,54,57]])})))).apply(this,arguments)}function m(){return"serviceWorker"in navigator?(console.log("Fixing WebSocket issues by checking service workers..."),new Promise((function(e){navigator.serviceWorker.getRegistrations().then(function(){var t=(0,r.A)(a().mark((function t(n){var r,o,i,c,l,u,p;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(n.length>0)){t.next=63;break}console.log("Found ".concat(n.length," service worker registrations that might affect WebSockets")),r=s(n),t.prev=3,r.s();case 5:if((o=r.n()).done){t.next=19;break}return i=o.value,console.log("Unregistering service worker to fix WebSocket issues:",i.scope),t.prev=8,t.next=11,i.unregister();case 11:console.log("Successfully unregistered service worker"),t.next=17;break;case 14:t.prev=14,t.t0=t.catch(8),console.error("Failed to unregister service worker:",t.t0);case 17:t.next=5;break;case 19:t.next=24;break;case 21:t.prev=21,t.t1=t.catch(3),r.e(t.t1);case 24:return t.prev=24,r.f(),t.finish(24);case 27:if(!("caches"in window)){t.next=57;break}return t.prev=28,t.next=31,caches.keys();case 31:c=t.sent,l=s(c),t.prev=33,l.s();case 35:if((u=l.n()).done){t.next=43;break}if(!((p=u.value).includes("api")||p.includes("ws")||p.includes("socket")||p.includes("workbox"))){t.next=41;break}return console.log("Deleting potentially problematic cache:",p),t.next=41,caches.delete(p);case 41:t.next=35;break;case 43:t.next=48;break;case 45:t.prev=45,t.t2=t.catch(33),l.e(t.t2);case 48:return t.prev=48,l.f(),t.finish(48);case 51:console.log("Cache cleanup completed"),t.next=57;break;case 54:t.prev=54,t.t3=t.catch(28),console.error("Error cleaning caches:",t.t3);case 57:try{localStorage.setItem("disable_sw_temp","true"),console.log("Temporarily disabled service worker for next page load")}catch(e){console.error("Failed to set localStorage flag:",e)}console.log("WebSocket issues should be fixed. Reloading page..."),setTimeout((function(){window.location.reload()}),1e3),e(!0),t.next=65;break;case 63:console.log("No service workers found that might affect WebSockets"),e(!1);case 65:case"end":return t.stop()}}),t,null,[[3,21,24,27],[8,14],[28,54],[33,45,48,51]])})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){console.error("Error while fixing WebSocket issues:",t),e(!1)}))}))):Promise.resolve(!1)}},8035:(e,t,n)=>{n.d(t,{A:()=>y});var r=n(9360),o=n(436),a=n(4467),i=n(1616);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,a.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={app:{components:[],layouts:[],styles:{},data:{}},websocket:{connected:!1,connecting:!1,url:null,error:null,lastMessage:null,reconnectAttempts:0},loading:!1,error:null,themes:[],activeTheme:"default"};const u=function(){var e,t,n,r,a,s,u,p,d,m,g,h,f=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,y=arguments.length>1?arguments[1]:void 0;switch(y.type){case i.Q3.WS_CONNECT:return c(c({},f),{},{websocket:c(c({},f.websocket),{},{connecting:!0,url:(null===(e=y.payload)||void 0===e?void 0:e.url)||f.websocket.url,error:null})});case i.Q3.WS_CONNECTED:return c(c({},f),{},{websocket:c(c({},f.websocket),{},{connected:!0,connecting:!1,error:null,reconnectAttempts:0})});case i.Q3.WS_DISCONNECTED:return c(c({},f),{},{websocket:c(c({},f.websocket),{},{connected:!1,connecting:!1,error:(null===(t=y.payload)||void 0===t?void 0:t.error)||null})});case i.Q3.WS_MESSAGE_RECEIVED:var v=y.payload;return"app_data"===v.type?c(c({},f),{},{websocket:c(c({},f.websocket),{},{lastMessage:v}),app:c(c({},f.app),{},{components:v.data.components||[],layouts:v.data.layouts||[],styles:v.data.styles||{},data:v.data.data||{}})}):c(c({},f),{},{websocket:c(c({},f.websocket),{},{lastMessage:v})});case i.Q3.WS_ERROR:return c(c({},f),{},{websocket:c(c({},f.websocket),{},{error:y.payload,connected:!1,connecting:!1})});case i.Q3.ADD_COMPONENT:return c(c({},f),{},{app:c(c({},f.app),{},{components:[].concat((0,o.A)((null===(n=f.app)||void 0===n?void 0:n.components)||[]),[y.payload])})});case i.Q3.UPDATE_COMPONENT:return c(c({},f),{},{app:c(c({},f.app),{},{components:((null===(r=f.app)||void 0===r?void 0:r.components)||[]).map((function(e,t){return t===y.payload.index?c(c({},e),y.payload.updates):e}))})});case i.Q3.DELETE_COMPONENT:return c(c({},f),{},{app:c(c({},f.app),{},{components:((null===(a=f.app)||void 0===a?void 0:a.components)||[]).filter((function(e,t){return t!==y.payload.index}))})});case i.Q3.ADD_LAYOUT:return c(c({},f),{},{app:c(c({},f.app),{},{layouts:[].concat((0,o.A)((null===(s=f.app)||void 0===s?void 0:s.layouts)||[]),[y.payload])})});case i.Q3.UPDATE_LAYOUT:return c(c({},f),{},{app:c(c({},f.app),{},{layouts:((null===(u=f.app)||void 0===u?void 0:u.layouts)||[]).map((function(e,t){return t===y.payload.index?c(c({},e),y.payload.updates):e}))})});case i.Q3.DELETE_LAYOUT:return c(c({},f),{},{app:c(c({},f.app),{},{layouts:((null===(p=f.app)||void 0===p?void 0:p.layouts)||[]).filter((function(e,t){return t!==y.payload.index}))})});case i.Q3.SAVE_APP_DATA:return c(c({},f),{},{app:c(c({},f.app),{},{components:y.payload.components||(null===(d=f.app)||void 0===d?void 0:d.components)||[],layouts:y.payload.layouts||(null===(m=f.app)||void 0===m?void 0:m.layouts)||[],styles:y.payload.styles||(null===(g=f.app)||void 0===g?void 0:g.styles)||{},data:y.payload.data||(null===(h=f.app)||void 0===h?void 0:h.data)||{}})});case i.Q3.LOAD_APP_DATA:return c(c({},f),{},{app:c(c({},f.app),{},{components:y.payload.components||[],layouts:y.payload.layouts||[],styles:y.payload.styles||{},data:y.payload.data||{}})});case i.Q3.SET_LOADING:return c(c({},f),{},{loading:y.payload});case i.Q3.SET_ERROR:return c(c({},f),{},{error:y.payload});case i.Q3.CLEAR_ERROR:return c(c({},f),{},{error:null});case"ADD_THEME":return c(c({},f),{},{themes:[].concat((0,o.A)(f.themes),[y.payload])});case"UPDATE_THEME":return c(c({},f),{},{themes:f.themes.map((function(e){return e.id===y.payload.id?y.payload:e}))});case"REMOVE_THEME":return c(c({},f),{},{themes:f.themes.filter((function(e){return e.id!==y.payload.id}))});case"SET_ACTIVE_THEME":return c(c({},f),{},{activeTheme:y.payload});default:return f}};var p=n(7053),d=n(4318),m=function(){return{type:d.Te}},g=function(e){return{type:d.AS,payload:{error:e}}};function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var f=function(e){return function(t){return function(n){var r=performance.now(),o=t(n),a=performance.now()-r;return"undefined"!=typeof window&&window.dispatchEvent(new CustomEvent("redux-action",{detail:{type:n.type,duration:a,timestamp:(new Date).toISOString(),state:e.getState()}})),o}}};const y=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,r.U1)({reducer:u,preloadedState:e,middleware:function(e){return e({serializableCheck:{ignoredActions:["WS_MESSAGE_RECEIVED","WS_ERROR","WEBSOCKET_MESSAGE_RECEIVED","WEBSOCKET_ERROR"],ignoredPaths:["websocket.socket","error.originalError","webSocketClient.socket"]},thunk:!0}).concat((t=!1,function(e){return function(n){return function(r){switch(r.type){case d.YG:if(!t){var o=p.A.getInstance();o.connect().then((function(){t=!0,e.dispatch(m())})).catch((function(t){e.dispatch(g(t))})),o.addEventListener("open",(function(){t=!0,e.dispatch(m())})),o.addEventListener("close",(function(){t=!1,e.dispatch({type:d.RH})})),o.addEventListener("error",(function(t){e.dispatch(g(t))}));var i=["app_data","app_data_update","component_added","component_updated","component_deleted","layout_added","layout_updated","layout_deleted","error"];o.addEventListener("message",(function(t){var n;t&&t.type&&i.includes(t.type)&&(e.dispatch((n={type:t.type,data:t},{type:d.H2,payload:n})),e.dispatch({type:"WS_".concat(t.type.toUpperCase()),payload:t}))}))}break;case d.uV:t&&(p.A.getInstance().close(),t=!1);break;case d.WD:if(t){var s=r.payload,c=s.messageType,l=s.data;p.A.getInstance().sendMessage(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){(0,a.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({type:c},l)).catch((function(t){console.error("Error sending message:",t),e.dispatch(g(t))}))}else console.warn("Cannot send message: WebSocket is not connected"),e.dispatch(g(new Error("WebSocket is not connected")))}return n(r)}}}),f);var t},devTools:{name:"App Builder 201",trace:!0,traceLimit:25}})}()},9391:(e,t,n)=>{n.d(t,{OJ:()=>p,As:()=>d});var r=n(467),o=n(5544),a=n(4756),i=n.n(a),s=n(6540),c=n(4702),l=(0,s.createContext)({trackEvent:function(){},trackPageView:function(){},trackError:function(){}}),u=(0,s.createContext)({user:null,isAuthenticated:!1,isLoading:!0,login:function(){},register:function(){},logout:function(){},hasRole:function(){},hasPermission:function(){}}),p=function(e){var t=e.children,n=(0,s.useState)(null),a=(0,o.A)(n,2),p=a[0],d=a[1],m=(0,s.useState)(!0),g=(0,o.A)(m,2),h=g[0],f=g[1],y=(0,s.useContext)(l).trackEvent;(0,s.useEffect)((function(){var e=function(){var e=(0,r.A)(i().mark((function e(){var t;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{(0,c.wR)()&&(t=(0,c.wz)(),(0,c.gf)(),t&&(d(t),y("auth_initialized",{userId:t.id||t.username,username:t.username})))}catch(e){console.error("Auth initialization error:",e)}finally{f(!1)}case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[y]);var v=function(){var e=(0,r.A)(i().mark((function e(t,n){var r;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return f(!0),e.prev=1,e.next=4,(0,c.iD)(t,n);case 4:if(!(r=e.sent).success){e.next=11;break}return d(r.user),y("auth_login",{userId:r.user.id||r.user.username,username:r.user.username}),e.abrupt("return",r);case 11:throw y("auth_login_error",{error:r.error}),new Error(r.error);case 13:e.next=19;break;case 15:throw e.prev=15,e.t0=e.catch(1),y("auth_login_error",{error:e.t0.message}),e.t0;case 19:return e.prev=19,f(!1),e.finish(19);case 22:case"end":return e.stop()}}),e,null,[[1,15,19,22]])})));return function(t,n){return e.apply(this,arguments)}}(),A=function(){var e=(0,r.A)(i().mark((function e(t){var n;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return f(!0),e.prev=1,e.next=4,(0,c.kz)(t);case 4:if(!(n=e.sent).success){e.next=11;break}return d(n.user),y("auth_register",{userId:n.user.id||n.user.username,username:n.user.username}),e.abrupt("return",n);case 11:throw y("auth_register_error",{error:n.error}),new Error(n.error);case 13:e.next=19;break;case 15:throw e.prev=15,e.t0=e.catch(1),y("auth_register_error",{error:e.t0.message}),e.t0;case 19:return e.prev=19,f(!1),e.finish(19);case 22:case"end":return e.stop()}}),e,null,[[1,15,19,22]])})));return function(t){return e.apply(this,arguments)}}(),b=function(){var e=(0,r.A)(i().mark((function e(){var t;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return f(!0),e.prev=1,e.next=4,(0,c.ri)();case 4:return t=e.sent,d(null),y("auth_logout"),e.abrupt("return",!1!==t.success);case 10:return e.prev=10,e.t0=e.catch(1),console.error("Logout error:",e.t0),d(null),e.abrupt("return",!1);case 15:return e.prev=15,f(!1),e.finish(15);case 18:case"end":return e.stop()}}),e,null,[[1,10,15,18]])})));return function(){return e.apply(this,arguments)}}(),E={user:p,isAuthenticated:!!p,isLoading:h,login:v,register:A,logout:b,hasRole:function(e){return p&&p.roles&&p.roles.includes(e)},hasPermission:function(e){return p&&p.permissions&&p.permissions.includes(e)}};return s.createElement(u.Provider,{value:E},t)},d=function(){return(0,s.useContext)(u)}}},e=>{e.O(0,[874,96],(()=>e(e.s=4465))),e.O()}]);