"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[8540],{33205:(e,n,t)=>{t.d(n,{A:()=>W});var o=t(58168),r=t(89379),a=t(64467),i=t(5544),c=t(82284),l=t(53986),s=t(46942),u=t.n(s),f=t(92830),m=t(12533),d=t(96540),v=t(33766),p=t(69916),h=t(16928),g=t(45062),C=t(57557),A=d.createContext(null);const b=function(e){var n=e.visible,t=e.maskTransitionName,o=e.getContainer,i=e.prefixCls,c=e.rootClassName,l=e.icons,s=e.countRender,f=e.showSwitch,m=e.showProgress,v=e.current,p=e.transform,b=e.count,y=e.scale,w=e.minScale,x=e.maxScale,k=e.closeIcon,R=e.onActive,E=e.onClose,S=e.onZoomIn,N=e.onZoomOut,T=e.onRotateRight,I=e.onRotateLeft,Y=e.onFlipX,X=e.onFlipY,P=e.onReset,M=e.toolbarRender,z=e.zIndex,Z=e.image,L=(0,d.useContext)(A),F=l.rotateLeft,O=l.rotateRight,D=l.zoomIn,W=l.zoomOut,H=l.close,V=l.left,j=l.right,B=l.flipX,G=l.flipY,U="".concat(i,"-operations-operation");d.useEffect((function(){var e=function(e){e.keyCode===h.A.ESC&&E()};return n&&window.addEventListener("keydown",e),function(){window.removeEventListener("keydown",e)}}),[n]);var $=function(e,n){e.preventDefault(),e.stopPropagation(),R(n)},q=d.useCallback((function(e){var n=e.type,t=e.disabled,o=e.onClick,r=e.icon;return d.createElement("div",{key:n,className:u()(U,"".concat(i,"-operations-operation-").concat(n),(0,a.A)({},"".concat(i,"-operations-operation-disabled"),!!t)),onClick:o},r)}),[U,i]),J=f?q({icon:V,onClick:function(e){return $(e,-1)},type:"prev",disabled:0===v}):void 0,K=f?q({icon:j,onClick:function(e){return $(e,1)},type:"next",disabled:v===b-1}):void 0,Q=q({icon:G,onClick:X,type:"flipY"}),_=q({icon:B,onClick:Y,type:"flipX"}),ee=q({icon:F,onClick:I,type:"rotateLeft"}),ne=q({icon:O,onClick:T,type:"rotateRight"}),te=q({icon:W,onClick:N,type:"zoomOut",disabled:y<=w}),oe=q({icon:D,onClick:S,type:"zoomIn",disabled:y===x}),re=d.createElement("div",{className:"".concat(i,"-operations")},Q,_,ee,ne,te,oe);return d.createElement(C.Ay,{visible:n,motionName:t},(function(e){var n=e.className,t=e.style;return d.createElement(g.A,{open:!0,getContainer:null!=o?o:document.body},d.createElement("div",{className:u()("".concat(i,"-operations-wrapper"),n,c),style:(0,r.A)((0,r.A)({},t),{},{zIndex:z})},null===k?null:d.createElement("button",{className:"".concat(i,"-close"),onClick:E},k||H),f&&d.createElement(d.Fragment,null,d.createElement("div",{className:u()("".concat(i,"-switch-left"),(0,a.A)({},"".concat(i,"-switch-left-disabled"),0===v)),onClick:function(e){return $(e,-1)}},V),d.createElement("div",{className:u()("".concat(i,"-switch-right"),(0,a.A)({},"".concat(i,"-switch-right-disabled"),v===b-1)),onClick:function(e){return $(e,1)}},j)),d.createElement("div",{className:"".concat(i,"-footer")},m&&d.createElement("div",{className:"".concat(i,"-progress")},s?s(v+1,b):d.createElement("bdi",null,"".concat(v+1," / ").concat(b))),M?M(re,(0,r.A)((0,r.A)({icons:{prevIcon:J,nextIcon:K,flipYIcon:Q,flipXIcon:_,rotateLeftIcon:ee,rotateRightIcon:ne,zoomOutIcon:te,zoomInIcon:oe},actions:{onActive:R,onFlipY:X,onFlipX:Y,onRotateLeft:I,onRotateRight:T,onZoomOut:N,onZoomIn:S,onReset:P,onClose:E},transform:p},L?{current:v,total:b}:{}),{},{image:Z})):re)))}))};var y=t(43210),w=t(25371),x={x:0,y:0,rotate:0,scale:1,flipX:!1,flipY:!1},k=t(68210);function R(e,n,t,o){var r=n+t,i=(t-o)/2;if(t>o){if(n>0)return(0,a.A)({},e,i);if(n<0&&r<o)return(0,a.A)({},e,-i)}else if(n<0||r>o)return(0,a.A)({},e,n<0?i:-i);return{}}function E(e,n,t,o){var a=(0,f.XV)(),i=a.width,c=a.height,l=null;return e<=i&&n<=c?l={x:0,y:0}:(e>i||n>c)&&(l=(0,r.A)((0,r.A)({},R("x",t,e,i)),R("y",o,n,c))),l}function S(e){var n=e.src,t=e.isCustomPlaceholder,o=e.fallback,r=(0,d.useState)(t?"loading":"normal"),a=(0,i.A)(r,2),c=a[0],l=a[1],s=(0,d.useRef)(!1),u="error"===c;(0,d.useEffect)((function(){var e=!0;return function(e){return new Promise((function(n){if(e){var t=document.createElement("img");t.onerror=function(){return n(!1)},t.onload=function(){return n(!0)},t.src=e}else n(!1)}))}(n).then((function(n){!n&&e&&l("error")})),function(){e=!1}}),[n]),(0,d.useEffect)((function(){t&&!s.current?l("loading"):u&&l("normal")}),[n]);var f=function(){l("normal")};return[function(e){s.current=!1,"loading"===c&&null!=e&&e.complete&&(e.naturalWidth||e.naturalHeight)&&(s.current=!0,f())},u&&o?{src:o}:{onLoad:f,src:n},c]}function N(e,n){var t=e.x-n.x,o=e.y-n.y;return Math.hypot(t,o)}var T=["fallback","src","imgRef"],I=["prefixCls","src","alt","imageInfo","fallback","movable","onClose","visible","icons","rootClassName","closeIcon","getContainer","current","count","countRender","scaleStep","minScale","maxScale","transitionName","maskTransitionName","imageRender","imgCommonProps","toolbarRender","onTransform","onChange"],Y=function(e){var n=e.fallback,t=e.src,r=e.imgRef,a=(0,l.A)(e,T),c=S({src:t,fallback:n}),s=(0,i.A)(c,2),u=s[0],f=s[1];return d.createElement("img",(0,o.A)({ref:function(e){r.current=e,u(e)}},a,f))};const X=function(e){var n=e.prefixCls,t=e.src,c=e.alt,s=e.imageInfo,m=e.fallback,g=e.movable,C=void 0===g||g,R=e.onClose,S=e.visible,T=e.icons,X=void 0===T?{}:T,P=e.rootClassName,M=e.closeIcon,z=e.getContainer,Z=e.current,L=void 0===Z?0:Z,F=e.count,O=void 0===F?1:F,D=e.countRender,W=e.scaleStep,H=void 0===W?.5:W,V=e.minScale,j=void 0===V?1:V,B=e.maxScale,G=void 0===B?50:B,U=e.transitionName,$=void 0===U?"zoom":U,q=e.maskTransitionName,J=void 0===q?"fade":q,K=e.imageRender,Q=e.imgCommonProps,_=e.toolbarRender,ee=e.onTransform,ne=e.onChange,te=(0,l.A)(e,I),oe=(0,d.useRef)(),re=(0,d.useContext)(A),ae=re&&O>1,ie=re&&O>=1,ce=(0,d.useState)(!0),le=(0,i.A)(ce,2),se=le[0],ue=le[1],fe=function(e,n,t,o){var a=(0,d.useRef)(null),c=(0,d.useRef)([]),l=(0,d.useState)(x),s=(0,i.A)(l,2),u=s[0],m=s[1],v=function(e,n){null===a.current&&(c.current=[],a.current=(0,w.A)((function(){m((function(e){var t=e;return c.current.forEach((function(e){t=(0,r.A)((0,r.A)({},t),e)})),a.current=null,null==o||o({transform:t,action:n}),t}))}))),c.current.push((0,r.A)((0,r.A)({},u),e))};return{transform:u,resetTransform:function(e){m(x),(0,y.A)(x,u)||null==o||o({transform:x,action:e})},updateTransform:v,dispatchZoomChange:function(o,r,a,i,c){var l=e.current,s=l.width,m=l.height,d=l.offsetWidth,p=l.offsetHeight,h=l.offsetLeft,g=l.offsetTop,C=o,A=u.scale*o;A>t?(A=t,C=t/u.scale):A<n&&(C=(A=c?A:n)/u.scale);var b=null!=a?a:innerWidth/2,y=null!=i?i:innerHeight/2,w=C-1,x=w*s*.5,k=w*m*.5,R=w*(b-u.x-h),E=w*(y-u.y-g),S=u.x-(R-x),N=u.y-(E-k);if(o<1&&1===A){var T=d*A,I=p*A,Y=(0,f.XV)(),X=Y.width,P=Y.height;T<=X&&I<=P&&(S=0,N=0)}v({x:S,y:N,scale:A},r)}}}(oe,j,G,ee),me=fe.transform,de=fe.resetTransform,ve=fe.updateTransform,pe=fe.dispatchZoomChange,he=function(e,n,t,o,a,c,l){var s=a.rotate,u=a.scale,f=a.x,m=a.y,v=(0,d.useState)(!1),h=(0,i.A)(v,2),g=h[0],C=h[1],A=(0,d.useRef)({diffX:0,diffY:0,transformX:0,transformY:0}),b=function(e){t&&g&&c({x:e.pageX-A.current.diffX,y:e.pageY-A.current.diffY},"move")},y=function(){if(t&&g){C(!1);var n=A.current,o=n.transformX,a=n.transformY;if(f===o||m===a)return;var i=e.current.offsetWidth*u,l=e.current.offsetHeight*u,d=e.current.getBoundingClientRect(),v=d.left,p=d.top,h=s%180!=0,b=E(h?l:i,h?i:l,v,p);b&&c((0,r.A)({},b),"dragRebound")}};return(0,d.useEffect)((function(){var e,t,o,r;if(n){o=(0,p.A)(window,"mouseup",y,!1),r=(0,p.A)(window,"mousemove",b,!1);try{window.top!==window.self&&(e=(0,p.A)(window.top,"mouseup",y,!1),t=(0,p.A)(window.top,"mousemove",b,!1))}catch(e){(0,k.$e)(!1,"[rc-image] ".concat(e))}}return function(){var n,a,i,c;null===(n=o)||void 0===n||n.remove(),null===(a=r)||void 0===a||a.remove(),null===(i=e)||void 0===i||i.remove(),null===(c=t)||void 0===c||c.remove()}}),[t,g,f,m,s,n]),{isMoving:g,onMouseDown:function(e){n&&0===e.button&&(e.preventDefault(),e.stopPropagation(),A.current={diffX:e.pageX-f,diffY:e.pageY-m,transformX:f,transformY:m},C(!0))},onMouseMove:b,onMouseUp:y,onWheel:function(e){if(t&&0!=e.deltaY){var n=Math.abs(e.deltaY/100),r=1+Math.min(n,1)*o;e.deltaY>0&&(r=1/r),l(r,"wheel",e.clientX,e.clientY)}}}}(oe,C,S,H,me,ve,pe),ge=he.isMoving,Ce=he.onMouseDown,Ae=he.onWheel,be=function(e,n,t,o,a,c,l){var s=a.rotate,u=a.scale,f=a.x,m=a.y,v=(0,d.useState)(!1),h=(0,i.A)(v,2),g=h[0],C=h[1],A=(0,d.useRef)({point1:{x:0,y:0},point2:{x:0,y:0},eventType:"none"}),b=function(e){A.current=(0,r.A)((0,r.A)({},A.current),e)};return(0,d.useEffect)((function(){var e;return t&&n&&(e=(0,p.A)(window,"touchmove",(function(e){return e.preventDefault()}),{passive:!1})),function(){var n;null===(n=e)||void 0===n||n.remove()}}),[t,n]),{isTouching:g,onTouchStart:function(e){if(n){e.stopPropagation(),C(!0);var t=e.touches,o=void 0===t?[]:t;o.length>1?b({point1:{x:o[0].clientX,y:o[0].clientY},point2:{x:o[1].clientX,y:o[1].clientY},eventType:"touchZoom"}):b({point1:{x:o[0].clientX-f,y:o[0].clientY-m},eventType:"move"})}},onTouchMove:function(e){var n=e.touches,t=void 0===n?[]:n,o=A.current,r=o.point1,a=o.point2,s=o.eventType;if(t.length>1&&"touchZoom"===s){var u={x:t[0].clientX,y:t[0].clientY},f={x:t[1].clientX,y:t[1].clientY},m=function(e,n,t,o){var r=N(e,t),a=N(n,o);if(0===r&&0===a)return[e.x,e.y];var i=r/(r+a);return[e.x+i*(n.x-e.x),e.y+i*(n.y-e.y)]}(r,a,u,f),d=(0,i.A)(m,2),v=d[0],p=d[1],h=N(u,f)/N(r,a);l(h,"touchZoom",v,p,!0),b({point1:u,point2:f,eventType:"touchZoom"})}else"move"===s&&(c({x:t[0].clientX-r.x,y:t[0].clientY-r.y},"move"),b({eventType:"move"}))},onTouchEnd:function(){if(t){if(g&&C(!1),b({eventType:"none"}),o>u)return c({x:0,y:0,scale:o},"touchZoom");var n=e.current.offsetWidth*u,a=e.current.offsetHeight*u,i=e.current.getBoundingClientRect(),l=i.left,f=i.top,m=s%180!=0,d=E(m?a:n,m?n:a,l,f);d&&c((0,r.A)({},d),"dragRebound")}}}}(oe,C,S,j,me,ve,pe),ye=be.isTouching,we=be.onTouchStart,xe=be.onTouchMove,ke=be.onTouchEnd,Re=me.rotate,Ee=me.scale,Se=u()((0,a.A)({},"".concat(n,"-moving"),ge));(0,d.useEffect)((function(){se||ue(!0)}),[se]);var Ne=function(e){var n=L+e;!Number.isInteger(n)||n<0||n>O-1||(ue(!1),de(e<0?"prev":"next"),null==ne||ne(n,L))},Te=function(e){S&&ae&&(e.keyCode===h.A.LEFT?Ne(-1):e.keyCode===h.A.RIGHT&&Ne(1))};(0,d.useEffect)((function(){var e=(0,p.A)(window,"keydown",Te,!1);return function(){e.remove()}}),[S,ae,L]);var Ie=d.createElement(Y,(0,o.A)({},Q,{width:e.width,height:e.height,imgRef:oe,className:"".concat(n,"-img"),alt:c,style:{transform:"translate3d(".concat(me.x,"px, ").concat(me.y,"px, 0) scale3d(").concat(me.flipX?"-":"").concat(Ee,", ").concat(me.flipY?"-":"").concat(Ee,", 1) rotate(").concat(Re,"deg)"),transitionDuration:(!se||ye)&&"0s"},fallback:m,src:t,onWheel:Ae,onMouseDown:Ce,onDoubleClick:function(e){S&&(1!==Ee?ve({x:0,y:0,scale:1},"doubleClick"):pe(1+H,"doubleClick",e.clientX,e.clientY))},onTouchStart:we,onTouchMove:xe,onTouchEnd:ke,onTouchCancel:ke})),Ye=(0,r.A)({url:t,alt:c},s);return d.createElement(d.Fragment,null,d.createElement(v.A,(0,o.A)({transitionName:$,maskTransitionName:J,closable:!1,keyboard:!0,prefixCls:n,onClose:R,visible:S,classNames:{wrapper:Se},rootClassName:P,getContainer:z},te,{afterClose:function(){de("close")}}),d.createElement("div",{className:"".concat(n,"-img-wrapper")},K?K(Ie,(0,r.A)({transform:me,image:Ye},re?{current:L}:{})):Ie)),d.createElement(b,{visible:S,transform:me,maskTransitionName:J,closeIcon:M,getContainer:z,prefixCls:n,rootClassName:P,icons:X,countRender:D,showSwitch:ae,showProgress:ie,current:L,count:O,scale:Ee,minScale:j,maxScale:G,toolbarRender:_,onActive:Ne,onZoomIn:function(){pe(1+H,"zoomIn")},onZoomOut:function(){pe(1/(1+H),"zoomOut")},onRotateRight:function(){ve({rotate:Re+90},"rotateRight")},onRotateLeft:function(){ve({rotate:Re-90},"rotateLeft")},onFlipX:function(){ve({flipX:!me.flipX},"flipX")},onFlipY:function(){ve({flipY:!me.flipY},"flipY")},onClose:R,onReset:function(){de("reset")},zIndex:void 0!==te.zIndex?te.zIndex+1:void 0,image:Ye}))};var P=t(60436),M=["crossOrigin","decoding","draggable","loading","referrerPolicy","sizes","srcSet","useMap","alt"],z=["visible","onVisibleChange","getContainer","current","movable","minScale","maxScale","countRender","closeIcon","onChange","onTransform","toolbarRender","imageRender"],Z=["src"],L=0,F=["src","alt","onPreviewClose","prefixCls","previewPrefixCls","placeholder","fallback","width","height","style","preview","className","onClick","onError","wrapperClassName","wrapperStyle","rootClassName"],O=["src","visible","onVisibleChange","getContainer","mask","maskClassName","movable","icons","scaleStep","minScale","maxScale","imageRender","toolbarRender"],D=function(e){var n=e.src,t=e.alt,s=e.onPreviewClose,v=e.prefixCls,p=void 0===v?"rc-image":v,h=e.previewPrefixCls,g=void 0===h?"".concat(p,"-preview"):h,C=e.placeholder,b=e.fallback,y=e.width,w=e.height,x=e.style,k=e.preview,R=void 0===k||k,E=e.className,N=e.onClick,T=e.onError,I=e.wrapperClassName,Y=e.wrapperStyle,P=e.rootClassName,z=(0,l.A)(e,F),Z=C&&!0!==C,D="object"===(0,c.A)(R)?R:{},W=D.src,H=D.visible,V=void 0===H?void 0:H,j=D.onVisibleChange,B=void 0===j?s:j,G=D.getContainer,U=void 0===G?void 0:G,$=D.mask,q=D.maskClassName,J=D.movable,K=D.icons,Q=D.scaleStep,_=D.minScale,ee=D.maxScale,ne=D.imageRender,te=D.toolbarRender,oe=(0,l.A)(D,O),re=null!=W?W:n,ae=(0,m.A)(!!V,{value:V,onChange:B}),ie=(0,i.A)(ae,2),ce=ie[0],le=ie[1],se=S({src:n,isCustomPlaceholder:Z,fallback:b}),ue=(0,i.A)(se,3),fe=ue[0],me=ue[1],de=ue[2],ve=(0,d.useState)(null),pe=(0,i.A)(ve,2),he=pe[0],ge=pe[1],Ce=(0,d.useContext)(A),Ae=!!R,be=u()(p,I,P,(0,a.A)({},"".concat(p,"-error"),"error"===de)),ye=(0,d.useMemo)((function(){var n={};return M.forEach((function(t){void 0!==e[t]&&(n[t]=e[t])})),n}),M.map((function(n){return e[n]}))),we=function(e,n){var t=d.useState((function(){return String(L+=1)})),o=(0,i.A)(t,1)[0],r=d.useContext(A),a={data:n,canPreview:e};return d.useEffect((function(){if(r)return r.register(o,a)}),[]),d.useEffect((function(){r&&r.register(o,a)}),[e,n]),o}(Ae,(0,d.useMemo)((function(){return(0,r.A)((0,r.A)({},ye),{},{src:re})}),[re,ye]));return d.createElement(d.Fragment,null,d.createElement("div",(0,o.A)({},z,{className:be,onClick:Ae?function(e){var n=(0,f.A3)(e.target),t=n.left,o=n.top;Ce?Ce.onPreview(we,re,t,o):(ge({x:t,y:o}),le(!0)),null==N||N(e)}:N,style:(0,r.A)({width:y,height:w},Y)}),d.createElement("img",(0,o.A)({},ye,{className:u()("".concat(p,"-img"),(0,a.A)({},"".concat(p,"-img-placeholder"),!0===C),E),style:(0,r.A)({height:w},x),ref:fe},me,{width:y,height:w,onError:T})),"loading"===de&&d.createElement("div",{"aria-hidden":"true",className:"".concat(p,"-placeholder")},C),$&&Ae&&d.createElement("div",{className:u()("".concat(p,"-mask"),q),style:{display:"none"===(null==x?void 0:x.display)?"none":void 0}},$)),!Ce&&Ae&&d.createElement(X,(0,o.A)({"aria-hidden":!ce,visible:ce,prefixCls:g,onClose:function(){le(!1),ge(null)},mousePosition:he,src:re,alt:t,imageInfo:{width:y,height:w},fallback:b,getContainer:U,icons:K,movable:J,scaleStep:Q,minScale:_,maxScale:ee,rootClassName:P,imageRender:ne,imgCommonProps:ye,toolbarRender:te},oe)))};D.PreviewGroup=function(e){var n,t=e.previewPrefixCls,s=void 0===t?"rc-image-preview":t,u=e.children,f=e.icons,v=void 0===f?{}:f,p=e.items,h=e.preview,g=e.fallback,C="object"===(0,c.A)(h)?h:{},b=C.visible,y=C.onVisibleChange,w=C.getContainer,x=C.current,k=C.movable,R=C.minScale,E=C.maxScale,S=C.countRender,N=C.closeIcon,T=C.onChange,I=C.onTransform,Y=C.toolbarRender,L=C.imageRender,F=(0,l.A)(C,z),O=function(e){var n=d.useState({}),t=(0,i.A)(n,2),o=t[0],c=t[1],l=d.useCallback((function(e,n){return c((function(t){return(0,r.A)((0,r.A)({},t),{},(0,a.A)({},e,n))})),function(){c((function(n){var t=(0,r.A)({},n);return delete t[e],t}))}}),[]);return[d.useMemo((function(){return e?e.map((function(e){if("string"==typeof e)return{data:{src:e}};var n={};return Object.keys(e).forEach((function(t){["src"].concat((0,P.A)(M)).includes(t)&&(n[t]=e[t])})),{data:n}})):Object.keys(o).reduce((function(e,n){var t=o[n],r=t.canPreview,a=t.data;return r&&e.push({data:a,id:n}),e}),[])}),[e,o]),l,!!e]}(p),D=(0,i.A)(O,3),W=D[0],H=D[1],V=D[2],j=(0,m.A)(0,{value:x}),B=(0,i.A)(j,2),G=B[0],U=B[1],$=(0,d.useState)(!1),q=(0,i.A)($,2),J=q[0],K=q[1],Q=(null===(n=W[G])||void 0===n?void 0:n.data)||{},_=Q.src,ee=(0,l.A)(Q,Z),ne=(0,m.A)(!!b,{value:b,onChange:function(e,n){null==y||y(e,n,G)}}),te=(0,i.A)(ne,2),oe=te[0],re=te[1],ae=(0,d.useState)(null),ie=(0,i.A)(ae,2),ce=ie[0],le=ie[1],se=d.useCallback((function(e,n,t,o){var r=V?W.findIndex((function(e){return e.data.src===n})):W.findIndex((function(n){return n.id===e}));U(r<0?0:r),re(!0),le({x:t,y:o}),K(!0)}),[W,V]);d.useEffect((function(){oe?J||U(0):K(!1)}),[oe]);var ue=d.useMemo((function(){return{register:H,onPreview:se}}),[H,se]);return d.createElement(A.Provider,{value:ue},u,d.createElement(X,(0,o.A)({"aria-hidden":!oe,movable:k,visible:oe,prefixCls:s,closeIcon:N,onClose:function(){re(!1),le(null)},mousePosition:ce,imgCommonProps:ee,src:_,fallback:g,icons:v,minScale:R,maxScale:E,getContainer:w,current:G,count:W.length,countRender:S,onTransform:I,toolbarRender:Y,imageRender:L,onChange:function(e,n){U(e),null==T||T(e,n)}},F)))};const W=D}}]);