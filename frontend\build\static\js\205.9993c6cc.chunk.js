"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[205],{9205:(e,n,t)=>{t.r(n),t.d(n,{default:()=>Be});var r=t(436),o=t(4467),a=t(467),c=t(5544),i=t(7528),s=t(4756),l=t.n(s),p=t(6540),u=t(1468),d=t(3016),m=t(2395),f=t(4358),y=t(9356),v=t(677),g=t(9740),h=t(2702),b=t(770),A=t(1196),E=t(7977),S=t(6552),x=t(9249),w=t(6754),j=t(7197),O=t(9029),k=t(2652),C=t(6914),P=t(6008),I=t(8602),R=t(9248),T=t(1952),N=t(234),D=t(7046),M=t(6325),B=t(3598),z=t(756),U=t(6191),F=t(6020),L=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.components,r=e.layouts,o=e.styles,a=e.data,i=n.typescript,s=n.includeAccessibility;if("full-project"===n.projectStructure)return generateVueProject(e,n);var l='<template>\n  <div class="app"'.concat(s?' role="main"':"",">");return r.forEach((function(e){var n=e.name||e.type||"layout";l+='\n    <div class="'.concat(n,'-container">'),e.components&&e.components.length>0&&e.components.forEach((function(e){var n=t.find((function(n){return n.id===e}));n&&(l+="\n      <".concat(n.type.toLowerCase()),Object.entries(n.props||{}).forEach((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];l+="string"==typeof r?" ".concat(t,'="').concat(r,'"'):" :".concat(t,'="').concat(JSON.stringify(r),'"')})),l+=" />")})),l+="\n    </div>"})),l+="\n  </div>\n</template>\n\n<script".concat(i?' lang="ts"':"",">\nimport { defineComponent, ref, reactive } from 'vue';\n\nexport default defineComponent({\n  name: 'App',\n  setup() {"),a&&Object.keys(a).length>0&&Object.entries(a).forEach((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];l+="\n    const ".concat(t," = ref(").concat(JSON.stringify(r),");")})),l+="\n    \n    return {",a&&Object.keys(a).length>0&&Object.keys(a).forEach((function(e){l+="\n      ".concat(e,",")})),l+="\n    };\n  }\n});\n<\/script>\n\n<style scoped>\n.app {\n  min-height: 100vh;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  line-height: 1.6;\n  color: #333;\n}\n\n".concat(J(o,t,r),"\n</style>")},J=function(e,n,t){return Object.entries(e).map((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];return"".concat(t," {\n  ").concat(Object.entries(r).map((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];return"".concat(t,": ").concat(r,";")})).join("\n  "),"\n}")})).join("\n\n")};function V(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function H(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?V(Object(t),!0).forEach((function(n){(0,o.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):V(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var _=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=(n.packageManager,n.typescript),r=void 0!==t&&t,o=n.styleFramework,a=void 0===o?"styled-components":o,c={name:"app-builder-generated-app",version:"0.1.0",private:!0,description:"Generated by App Builder",author:"App Builder",license:"MIT"};switch(e){case"react-app":return JSON.stringify(H(H({},c),{},{scripts:{start:"react-scripts start",build:"react-scripts build",test:"react-scripts test",eject:"react-scripts eject",lint:"eslint src --ext .js,.jsx,.ts,.tsx","lint:fix":"eslint src --ext .js,.jsx,.ts,.tsx --fix"},dependencies:H(H(H(H({react:"^18.2.0","react-dom":"^18.2.0","react-scripts":"5.0.1","web-vitals":"^2.1.4"},"styled-components"===a&&{"styled-components":"^5.3.9"}),"emotion"===a&&{"@emotion/react":"^11.10.6","@emotion/styled":"^11.10.6"}),"tailwind"===a&&{tailwindcss:"^3.2.7"}),!r&&{"prop-types":"^15.8.1"}),devDependencies:H(H({"@testing-library/jest-dom":"^5.16.4","@testing-library/react":"^13.4.0","@testing-library/user-event":"^13.5.0"},r&&{"@types/react":"^18.0.28","@types/react-dom":"^18.0.11","@types/node":"^16.18.23",typescript:"^4.9.5"}),{},{eslint:"^8.36.0","eslint-plugin-react":"^7.32.2","eslint-plugin-react-hooks":"^4.6.0"}),browserslist:{production:[">0.2%","not dead","not op_mini all"],development:["last 1 chrome version","last 1 firefox version","last 1 safari version"]}}),null,2);case"nextjs-app":return JSON.stringify(H(H({},c),{},{scripts:{dev:"next dev",build:"next build",start:"next start",lint:"next lint",export:"next export"},dependencies:H(H({next:"^13.2.4",react:"^18.2.0","react-dom":"^18.2.0"},"styled-components"===a&&{"styled-components":"^5.3.9"}),"tailwind"===a&&{tailwindcss:"^3.2.7",autoprefixer:"^10.4.14",postcss:"^8.4.21"}),devDependencies:H(H({},r&&{"@types/node":"^18.15.3","@types/react":"^18.0.28","@types/react-dom":"^18.0.11",typescript:"^5.0.2"}),{},{eslint:"^8.36.0","eslint-config-next":"^13.2.4"})}),null,2);case"vue-app":return JSON.stringify(H(H({},c),{},{scripts:{serve:"vue-cli-service serve",build:"vue-cli-service build",test:"vue-cli-service test:unit",lint:"vue-cli-service lint"},dependencies:H({"core-js":"^3.8.3",vue:"^3.2.13"},r&&{"vue-tsc":"^1.2.0"}),devDependencies:H({"@babel/core":"^7.12.16","@babel/eslint-parser":"^7.12.16","@vue/cli-plugin-babel":"~5.0.0","@vue/cli-plugin-eslint":"~5.0.0","@vue/cli-service":"~5.0.0",eslint:"^7.32.0","eslint-plugin-vue":"^8.0.3"},r&&{"@vue/cli-plugin-typescript":"~5.0.0","@vue/eslint-config-typescript":"^9.1.0",typescript:"~4.5.5"})}),null,2);case"angular-app":return JSON.stringify(H(H({},c),{},{scripts:{ng:"ng",start:"ng serve",build:"ng build",watch:"ng build --watch --configuration development",test:"ng test"},dependencies:{"@angular/animations":"^15.2.0","@angular/common":"^15.2.0","@angular/compiler":"^15.2.0","@angular/core":"^15.2.0","@angular/forms":"^15.2.0","@angular/platform-browser":"^15.2.0","@angular/platform-browser-dynamic":"^15.2.0","@angular/router":"^15.2.0",rxjs:"~7.8.0",tslib:"^2.3.0","zone.js":"~0.12.0"},devDependencies:{"@angular-devkit/build-angular":"^15.2.4","@angular/cli":"~15.2.4","@angular/compiler-cli":"^15.2.0","@types/jasmine":"~4.3.0","@types/node":"^12.11.1",jasmine:"~4.5.0",karma:"~6.4.0","karma-chrome-launcher":"~3.1.0","karma-coverage":"~2.2.0","karma-jasmine":"~5.1.0","karma-jasmine-html-reporter":"~2.0.0",typescript:"~4.9.4"}}),null,2);default:return JSON.stringify(c,null,2)}},G=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=n.projectName,r=void 0===t?"Generated App":t,o=n.typescript,a=void 0!==o&&o;return"# ".concat(r,"\n\nThis project was generated using App Builder.\n\n## Framework: ").concat(e).concat(a?" with TypeScript":"","\n\n## Getting Started\n\n### Prerequisites\n\n- Node.js (version 14 or higher)\n- npm, yarn, or pnpm\n\n### Installation\n\n```bash\n# Install dependencies\nnpm install\n\n# Or with yarn\nyarn install\n\n# Or with pnpm\npnpm install\n```\n\n### Development\n\n```bash\n# Start development server\nnpm start\n\n# Or with yarn\nyarn start\n\n# Or with pnpm\npnpm start\n```\n\n### Building for Production\n\n```bash\n# Build for production\nnpm run build\n\n# Or with yarn\nyarn build\n\n# Or with pnpm\npnpm build\n```\n\n## Project Structure\n\n```\nsrc/\n├── components/     # Reusable components\n├── pages/         # Page components\n├── styles/        # Stylesheets\n├── utils/         # Utility functions\n└── App.").concat(a?"tsx":"jsx","        # Main application component\n```\n\n## Features\n\n- ✅ Modern ").concat(e," application\n- ✅ Responsive design\n- ✅ Accessibility features\n").concat(a?"- ✅ TypeScript support":"","\n- ✅ ESLint configuration\n- ✅ Production-ready build\n\n## Generated by App Builder\n\nThis application was automatically generated by App Builder. You can customize and extend it according to your needs.\n\n## Learn More\n\n- [").concat(e," Documentation](").concat(W(e),")\n- [App Builder Documentation](https://app-builder.example.com/docs)\n\n## License\n\nMIT\n")},Y=function(){return JSON.stringify({compilerOptions:{target:"es5",lib:["dom","dom.iterable","es6"],allowJs:!0,skipLibCheck:!0,esModuleInterop:!0,allowSyntheticDefaultImports:!0,strict:!0,forceConsistentCasingInFileNames:!0,noFallthroughCasesInSwitch:!0,module:"esnext",moduleResolution:"node",resolveJsonModule:!0,isolatedModules:!0,noEmit:!0,jsx:"react-jsx"},include:["src"],exclude:["node_modules"]},null,2)},W=function(e){return{React:"https://reactjs.org/docs",Vue:"https://vuejs.org/guide/",Angular:"https://angular.io/docs",Svelte:"https://svelte.dev/docs","Next.js":"https://nextjs.org/docs"}[e]||"https://developer.mozilla.org/"};function q(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function K(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?q(Object(t),!0).forEach((function(n){(0,o.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):q(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var Z,$,X,Q,ee,ne,te=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"react",t=K({typescript:!1,includeAccessibility:!0,includeTests:!1,includeStorybook:!1,styleFramework:"styled-components",stateManagement:"useState",projectStructure:"single-file",bundler:"vite",packageManager:"npm"},arguments.length>2&&void 0!==arguments[2]?arguments[2]:{});switch(n){case"react":return re(e,t);case"react-ts":return re(e,K(K({},t),{},{typescript:!0}));case"vue":return L(e,t);case"vue-ts":return L(e,K(K({},t),{},{typescript:!0}));case"angular":return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.components,r=e.layouts,o=e.styles,a=e.data,i=(n.typescript,n.includeAccessibility);if("full-project"===n.projectStructure)return generateAngularProject(e,n);var s="import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'Generated App';";a&&Object.keys(a).length>0&&Object.entries(a).forEach((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];s+="\n  ".concat(t," = ").concat(JSON.stringify(r),";")})),s+="\n}";var l='<div class="app"'.concat(i?' role="main"':"",">");r.forEach((function(e){var n=e.name||e.type||"layout";l+='\n  <div class="'.concat(n,'-container">'),e.components&&e.components.length>0&&e.components.forEach((function(e){var n=t.find((function(n){return n.id===e}));n&&(l+="\n    <app-".concat(n.type.toLowerCase()),Object.entries(n.props||{}).forEach((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];l+="string"==typeof r?" ".concat(t,'="').concat(r,'"'):" [".concat(t,']="').concat(JSON.stringify(r),'"')})),l+="></app-".concat(n.type.toLowerCase(),">"))})),l+="\n  </div>"})),l+="\n</div>";var p=".app {\n  min-height: 100vh;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  line-height: 1.6;\n  color: #333;\n}\n\n".concat(function(e){return Object.entries(e).map((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];return"".concat(t," {\n  ").concat(Object.entries(r).map((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];return"".concat(t,": ").concat(r,";")})).join("\n  "),"\n}")})).join("\n\n")}(o));return{"app.component.ts":s,"app.component.html":l,"app.component.css":p}}(e,t);case"svelte":return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.components,r=e.layouts,o=e.styles,a=e.data,i=n.typescript,s=n.includeAccessibility,l="<script".concat(i?' lang="ts"':"",">");return a&&Object.keys(a).length>0&&Object.entries(a).forEach((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];l+="\n  let ".concat(t," = ").concat(JSON.stringify(r),";")})),l+='\n<\/script>\n\n<div class="app"'.concat(s?' role="main"':"",">"),r.forEach((function(e){var n=e.name||e.type||"layout";l+='\n  <div class="'.concat(n,'-container">'),e.components&&e.components.length>0&&e.components.forEach((function(e){var n=t.find((function(n){return n.id===e}));n&&(l+="\n    <".concat(n.type),Object.entries(n.props||{}).forEach((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];l+="string"==typeof r?" ".concat(t,'="').concat(r,'"'):" ".concat(t,"={").concat(JSON.stringify(r),"}")})),l+=" />")})),l+="\n  </div>"})),l+="\n</div>\n\n<style>\n  .app {\n    min-height: 100vh;\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n    line-height: 1.6;\n    color: #333;\n  }\n\n  ".concat(function(e){return Object.entries(e).map((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];return"".concat(t," {\n    ").concat(Object.entries(r).map((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];return"".concat(t,": ").concat(r,";")})).join("\n    "),"\n  }")})).join("\n\n")}(o),"\n</style>")}(e,t);case"next":return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.components,r=e.layouts,a=e.styles,i=(e.data,n.typescript),s=n.includeAccessibility;if("full-project"===n.projectStructure)return generateNextJSProject(e,n);var l=i?"tsx":"jsx",p='import React from \'react\';\nimport Head from \'next/head\';\nimport styles from \'../styles/Home.module.css\';\n\nexport default function Home() {\n  return (\n    <div className={styles.container}>\n      <Head>\n        <title>Generated App</title>\n        <meta name="description" content="Generated by App Builder" />\n        <link rel="icon" href="/favicon.ico" />\n      </Head>\n\n      <main className={styles.main}'.concat(s?' role="main"':"",">");return r.forEach((function(e){var n=e.name||e.type||"layout";p+="\n        <div className={styles.".concat(n,"Container}>"),e.components&&e.components.length>0&&e.components.forEach((function(e){var n=t.find((function(n){return n.id===e}));n&&(p+="\n          <".concat(n.type),Object.entries(n.props||{}).forEach((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];p+="string"==typeof r?" ".concat(t,'="').concat(r,'"'):" ".concat(t,"={").concat(JSON.stringify(r),"}")})),p+=" />")})),p+="\n        </div>"})),p+="\n      </main>\n    </div>\n  );\n}",(0,o.A)((0,o.A)((0,o.A)({},"pages/index.".concat(l),p),"styles/Home.module.css",function(e){return".container {\n  padding: 0 2rem;\n}\n\n.main {\n  min-height: 100vh;\n  padding: 4rem 0;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n".concat(Object.entries(e).map((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];return"".concat(t.replace(/[^a-zA-Z0-9]/g,"")," {\n  ").concat(Object.entries(r).map((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];return"".concat(t,": ").concat(r,";")})).join("\n  "),"\n}")})).join("\n\n"))}(a)),"package.json",generatePackageJson("nextjs-app",n))}(e,t);case"nuxt":return generateNuxtCode(e,t);case"html":return se(e,t);case"css":return le(e,t);case"react-native":return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.components,r=e.layouts,o=(e.styles,e.data),a=(n.typescript,n.includeAccessibility,"import React".concat(o&&Object.keys(o).length>0?", { useState }":""," from 'react';\nimport {\n  SafeAreaView,\n  ScrollView,\n  StatusBar,\n  StyleSheet,\n  Text,\n  View,\n} from 'react-native';\n\nconst App = () => {"));return o&&Object.keys(o).length>0&&Object.entries(o).forEach((function(e){var n,t=(0,c.A)(e,2),r=t[0],o=t[1];a+="\n  const [".concat(r,", set").concat((n=r,n.replace(/(?:^|[\s-_])(\w)/g,(function(e,n){return n.toUpperCase()})).replace(/[\s-_]/g,"")),"] = useState(").concat(JSON.stringify(o),");")})),a+='\n  return (\n    <SafeAreaView style={styles.container}>\n      <StatusBar barStyle="dark-content" />\n      <ScrollView contentInsetAdjustmentBehavior="automatic">',r.forEach((function(e){a+="\n        <View style={styles.".concat(e.name||e.type||"layout","Container}>"),e.components&&e.components.length>0&&e.components.forEach((function(e){var n=t.find((function(n){return n.id===e}));n&&(a+="\n          <".concat({Button:"TouchableOpacity",Text:"Text",Input:"TextInput",Image:"Image",Card:"View",Section:"View",Header:"View"}[n.type]||"View"),Object.entries(n.props||{}).forEach((function(e){var t=(0,c.A)(e,2),r=t[0],o=t[1],i=function(e){return{text:"title",content:"children",src:"source"}[e]||e}(r,n.type);a+="string"==typeof o?" ".concat(i,'="').concat(o,'"'):" ".concat(i,"={").concat(JSON.stringify(o),"}")})),a+=" />")})),a+="\n        </View>"})),a+="\n      </ScrollView>\n    </SafeAreaView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#fff',\n  },\n  ".concat(function(e,n,t){return t.map((function(e){var n=e.name||e.type||"layout";return"".concat(n,"Container: {\n    padding: 16,\n    marginVertical: 8,\n  },")})).join("\n  ")}(0,0,r),"\n});\n\nexport default App;")}(e,t);case"flutter":return generateFlutterCode(e,t);case"ionic":return generateIonicCode(e,t);default:return JSON.stringify(e,null,2)}},re=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.components,r=e.layouts,a=e.styles,c=e.data,i=n.typescript,s=(n.includeAccessibility,n.projectStructure);if(n.styleFramework,n.stateManagement,"full-project"===s)return he(e,n);var l=oe(t,n),p="";i&&(p=ae(t,r));var u=ce(a,t,r,n),d=pe(t,n),m=ue(t,r,c,n),f=i?"tsx":"jsx",y="".concat(l,"\n").concat(p,"\n").concat(u,"\n").concat(d,"\n").concat(m);return"single-file"===s?y:K((0,o.A)((0,o.A)((0,o.A)({},"App.".concat(f),y),"package.json",_("react-app",n)),"README.md",G("React",n)),i&&{"tsconfig.json":Y()})},oe=function(e,n){var t=n.typescript,r=n.styleFramework,o=n.stateManagement,a="import React".concat("useState"===o?", { useState, useEffect }":""," from 'react';\n");return"styled-components"===r?a+="import styled from 'styled-components';\n":"emotion"===r&&(a+="import styled from '@emotion/styled';\n"),"redux"===o?a+="import { useSelector, useDispatch } from 'react-redux';\n":"zustand"===o&&(a+="import { useStore } from './store';\n"),t||(a+="import PropTypes from 'prop-types';\n"),a+"\n"},ae=function(e,n){var t="// TypeScript Interfaces\n";return(0,r.A)(new Set(e.map((function(e){return e.type})))).forEach((function(n){var r=e.find((function(e){return e.type===n}));r&&r.props&&(t+="interface ".concat(n,"Props {\n"),Object.entries(r.props).forEach((function(e){var n=(0,c.A)(e,2),r=n[0],o=n[1],a="string"==typeof o?"string":"number"==typeof o?"number":"boolean"==typeof o?"boolean":"any";t+="  ".concat(r,"?: ").concat(a,";\n")})),t+="  className?: string;\n",t+="  children?: React.ReactNode;\n",t+="}\n\n")})),t+="interface AppData {\n",t+="  components: ComponentData[];\n",t+="  layouts: LayoutData[];\n",t+="}\n\n"},ce=function(e,n,t,r){var o=r.styleFramework;return"styled-components"===o||"emotion"===o?ie(e,n,t):"tailwind"===o?"// Tailwind CSS classes will be used inline\n\n":ge(e)},ie=function(e,n,t){var o="// Styled Components\n";return o+="const AppContainer = styled.div`\n  min-height: 100vh;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  line-height: 1.6;\n  color: #333;\n`;\n\n",t.forEach((function(e){var n=e.name||e.type||"Layout";o+="const ".concat(de(n),"Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  padding: 1rem;\n  ").concat(e.styles?Object.entries(e.styles).map((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];return"".concat(me(t),": ").concat(r,";")})).join("\n  "):"","\n`;\n\n")})),(0,r.A)(new Set(n.map((function(e){return e.type})))).forEach((function(e){o+="const Styled".concat(e," = styled.div`\n  ").concat(ve(e),"\n`;\n\n")})),o},se=function(e){var n=e.components,t=e.layouts,r=e.styles,o="";Object.entries(r).forEach((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];o+="".concat(t," {\n"),Object.entries(r).forEach((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];o+="  ".concat(t,": ").concat(r,";\n")})),o+="}\n\n"}));var a="";return t.forEach((function(e){a+='<div class="'.concat(e.type,'">\n'),e.components.forEach((function(e){var t=n.find((function(n){return n.id===e}));if(t)switch(t.type){case"Button":a+='  <button class="'.concat(t.props.className||"",'">').concat(t.props.text||"Button","</button>\n");break;case"Input":a+='  <input type="'.concat(t.props.type||"text",'" placeholder="').concat(t.props.placeholder||"",'" class="').concat(t.props.className||"",'" />\n');break;case"Text":a+='  <p class="'.concat(t.props.className||"",'">').concat(t.props.content||"","</p>\n");break;case"Image":a+='  <img src="'.concat(t.props.src||"",'" alt="').concat(t.props.alt||"",'" class="').concat(t.props.className||"",'" />\n');break;default:a+='  <div class="'.concat(t.type.toLowerCase(),'">').concat(t.props.content||t.type,"</div>\n")}})),a+="</div>\n"})),'<!DOCTYPE html>\n<html lang="en">\n<head>\n  <meta charset="UTF-8">\n  <meta name="viewport" content="width=device-width, initial-scale=1.0">\n  <title>Generated App</title>\n  <style>\n'.concat(o,'\n  </style>\n</head>\n<body>\n  <div class="app">\n').concat(a,"\n  </div>\n</body>\n</html>\n")},le=function(e){var n=e.styles,t="";return Object.entries(n).forEach((function(e){var n=(0,c.A)(e,2),r=n[0],o=n[1];t+="".concat(r," {\n"),Object.entries(o).forEach((function(e){var n=(0,c.A)(e,2),r=n[0],o=n[1];t+="  ".concat(r,": ").concat(o,";\n")})),t+="}\n\n"})),t},pe=function(e,n){var t=n.typescript,o=n.includeAccessibility,a="// Component Definitions\n";return(0,r.A)(new Set(e.map((function(e){return e.type})))).forEach((function(n){var r=e.find((function(e){return e.type===n})),i=t?"".concat(n,"Props"):"";a+="const ".concat(n," = (").concat(t?"props: ".concat(i):"props",") => {\n"),a+="  const { ".concat(Object.keys(r.props||{}).join(", "),", className, children, ...rest } = props;\n\n"),a+="  return (\n",a+="    <Styled".concat(n,"\n"),a+="      className={className}\n",o&&(a+='      role="'.concat(fe(n),'"\n'),"Button"===n&&(a+="      aria-label={props['aria-label'] || props.text || 'Button'}\n")),a+="      {...rest}\n",a+="    >\n",a+="      ".concat(ye(n,r.props),"\n"),a+="      {children}\n",a+="    </Styled".concat(n,">\n"),a+="  );\n",a+="};\n\n",t||(a+="".concat(n,".propTypes = {\n"),Object.entries(r.props||{}).forEach((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1],o="string"==typeof r?"PropTypes.string":"number"==typeof r?"PropTypes.number":"boolean"==typeof r?"PropTypes.bool":"PropTypes.any";a+="  ".concat(t,": ").concat(o,",\n")})),a+="  className: PropTypes.string,\n",a+="  children: PropTypes.node\n",a+="};\n\n")})),a},ue=function(e,n,t,r){r.typescript;var o=r.stateManagement,a=r.includeAccessibility,i="// Main App Component\n";return"useState"===o&&t&&Object.keys(t).length>0?(i+="const App = () => {\n",Object.entries(t).forEach((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];i+="  const [".concat(t,", set").concat(de(t),"] = useState(").concat(JSON.stringify(r),");\n")})),i+="\n"):i+="const App = () => {\n",i+="  return (\n",i+="    <AppContainer".concat(a?' role="main"':"",">\n"),n.forEach((function(n){var t=n.name||n.type||"Layout";i+="      <".concat(de(t),"Container>\n"),n.components&&n.components.length>0?n.components.forEach((function(n){var t=e.find((function(e){return e.id===n}));t&&(i+="        <".concat(t.type),Object.entries(t.props||{}).forEach((function(e){var n=(0,c.A)(e,2),t=n[0],r=n[1];i+="string"==typeof r?"\n          ".concat(t,'="').concat(r,'"'):"\n          ".concat(t,"={").concat(JSON.stringify(r),"}")})),i+="\n        />\n")})):i+="        {/* Add components here */}\n",i+="      </".concat(de(t),"Container>\n")})),i+="    </AppContainer>\n",i+="  );\n",i+="};\n\n",i+="export default App;\n"},de=function(e){return e.replace(/(?:^|[\s-_])(\w)/g,(function(e,n){return n.toUpperCase()})).replace(/[\s-_]/g,"")},me=function(e){return e.replace(/-([a-z])/g,(function(e){return e[1].toUpperCase()}))},fe=function(e){return{Button:"button",Input:"textbox",Text:"text",Image:"img",Header:"banner",Section:"region",Card:"article",List:"list"}[e]||"generic"},ye=function(e,n){switch(e){case"Button":return(null==n?void 0:n.text)||"Button";case"Text":return(null==n?void 0:n.content)||"Text content";case"Input":case"Image":return"";default:return(null==n?void 0:n.content)||e}},ve=function(e){return{Button:"\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 0.25rem;\n  background-color: #007bff;\n  color: white;\n  cursor: pointer;\n  font-size: 1rem;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: #0056b3;\n  }\n\n  &:focus {\n    outline: 2px solid #007bff;\n    outline-offset: 2px;\n  }",Input:"\n  padding: 0.5rem;\n  border: 1px solid #ccc;\n  border-radius: 0.25rem;\n  font-size: 1rem;\n\n  &:focus {\n    outline: 2px solid #007bff;\n    border-color: #007bff;\n  }",Text:"\n  margin: 0;\n  line-height: 1.5;",Image:"\n  max-width: 100%;\n  height: auto;",Card:"\n  padding: 1rem;\n  border: 1px solid #e0e0e0;\n  border-radius: 0.5rem;\n  background-color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);"}[e]||"\n  /* Add styles for ".concat(e," */")},ge=function(e){var n="";return Object.entries(e).forEach((function(e){var t=(0,c.A)(e,2),r=t[0],o=t[1];n+="".concat(r," {\n"),Object.entries(o).forEach((function(e){var t=(0,c.A)(e,2),r=t[0],o=t[1];n+="  ".concat(r,": ").concat(o,";\n")})),n+="}\n\n"})),n},he=function(e,n){var t=n.typescript,a=n.bundler,c=void 0===a?"vite":a,i=t?"tsx":"jsx",s=(0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)((0,o.A)({"package.json":_("react-app",n),"README.md":G("React",n)},"src/App.".concat(i),re(e,K(K({},n),{},{projectStructure:"single-file"}))),"src/index.".concat(i),be(t)),"src/index.css",Ae()),"public/index.html",Ee()),".gitignore",Se()),".eslintrc.json",function(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t={env:{browser:!0,es2021:!0,node:!0},extends:["eslint:recommended"],parserOptions:{ecmaVersion:"latest",sourceType:"module"},rules:{"no-unused-vars":"warn","no-console":"warn","prefer-const":"error"}};return"react"===e&&(t.extends.push("plugin:react/recommended","plugin:react-hooks/recommended"),t.plugins=["react","react-hooks"],t.parserOptions.ecmaFeatures={jsx:!0},t.settings={react:{version:"detect"}}),n&&(t.extends.push("@typescript-eslint/recommended"),t.parser="@typescript-eslint/parser",t.plugins=[].concat((0,r.A)(t.plugins||[]),["@typescript-eslint"])),JSON.stringify(t,null,2)}("react",t)),"Dockerfile",'# Use official Node.js runtime as base image\nFROM node:18-alpine\n\n# Set working directory\nWORKDIR /app\n\n# Copy package files\nCOPY package*.json ./\n\n# Install dependencies\nRUN npm ci --only=production\n\n# Copy source code\nCOPY . .\n\n# Build the application\nRUN npm run build\n\n# Expose port\nEXPOSE 3000\n\n# Start the application\nCMD ["npm", "start"]\n'),"docker-compose.yml",'version: \'3.8\'\n\nservices:\n  app:\n    build: .\n    ports:\n      - "3000:3000"\n    environment:\n      - NODE_ENV=production\n    volumes:\n      - .:/app\n      - /app/node_modules\n    restart: unless-stopped\n\n  nginx:\n    image: nginx:alpine\n    ports:\n      - "80:80"\n    volumes:\n      - ./nginx.conf:/etc/nginx/nginx.conf\n    depends_on:\n      - app\n    restart: unless-stopped\n'),".github/workflows/ci.yml","name: CI/CD Pipeline\n\non:\n  push:\n    branches: [ main, develop ]\n  pull_request:\n    branches: [ main ]\n\njobs:\n  test:\n    runs-on: ubuntu-latest\n    \n    strategy:\n      matrix:\n        node-version: [16.x, 18.x]\n    \n    steps:\n    - uses: actions/checkout@v3\n    \n    - name: Use Node.js ${{ matrix.node-version }}\n      uses: actions/setup-node@v3\n      with:\n        node-version: ${{ matrix.node-version }}\n        cache: 'npm'\n    \n    - name: Install dependencies\n      run: npm ci\n    \n    - name: Run tests\n      run: npm test\n    \n    - name: Build application\n      run: npm run build\n\n  deploy:\n    needs: test\n    runs-on: ubuntu-latest\n    if: github.ref == 'refs/heads/main'\n    \n    steps:\n    - uses: actions/checkout@v3\n    \n    - name: Setup Node.js\n      uses: actions/setup-node@v3\n      with:\n        node-version: '18.x'\n        cache: 'npm'\n    \n    - name: Install dependencies\n      run: npm ci\n    \n    - name: Build application\n      run: npm run build\n    \n    - name: Deploy to production\n      run: echo \"Deploy to your hosting platform\"\n");return t&&(s["tsconfig.json"]=Y()),"vite"===c&&(s["vite.config.js"]="import { defineConfig } from 'vite'\n".concat("react"=="react"?"import react from '@vitejs/plugin-react'":"","\n").concat("","\n\nexport default defineConfig({\n  plugins: [").concat("react()","],\n  server: {\n    port: 3000,\n    open: true\n  },\n  build: {\n    outDir: 'dist',\n    sourcemap: true\n  }\n})\n")),s},be=function(e){return"import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root')".concat(e?" as HTMLElement":"","\n);\n\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);")},Ae=function(){return"body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n* {\n  box-sizing: border-box;\n}\n\nhtml {\n  scroll-behavior: smooth;\n}\n\nbutton {\n  cursor: pointer;\n}\n\ninput, textarea, select {\n  font-family: inherit;\n}\n\nimg {\n  max-width: 100%;\n  height: auto;\n}"},Ee=function(){return'<!DOCTYPE html>\n<html lang="en">\n  <head>\n    <meta charset="utf-8" />\n    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />\n    <meta name="viewport" content="width=device-width, initial-scale=1" />\n    <meta name="theme-color" content="#000000" />\n    <meta name="description" content="Generated by App Builder" />\n    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />\n    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />\n    <title>Generated App</title>\n  </head>\n  <body>\n    <noscript>You need to enable JavaScript to run this app.</noscript>\n    <div id="root"></div>\n  </body>\n</html>'},Se=function(){return"# Dependencies\nnode_modules/\n/.pnp\n.pnp.js\n\n# Testing\n/coverage\n\n# Production\n/build\n/dist\n\n# Environment variables\n.env\n.env.local\n.env.development.local\n.env.test.local\n.env.production.local\n\n# Logs\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\nlerna-debug.log*\n\n# Runtime data\npids\n*.pid\n*.seed\n*.pid.lock\n\n# IDE\n.vscode/\n.idea/\n*.swp\n*.swo\n\n# OS\n.DS_Store\nThumbs.db\n\n# Temporary folders\ntmp/\ntemp/"};function xe(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function we(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?xe(Object(t),!0).forEach((function(n){(0,o.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):xe(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var je=d.A.Title,Oe=d.A.Text,ke=d.A.Paragraph,Ce=m.A.TabPane,Pe=f.A.Option,Ie=y.A.Panel,Re=U.styled.div(Z||(Z=(0,i.A)(["\n  padding: ",";\n  max-width: 1200px;\n  margin: 0 auto;\n"])),F.Ay.spacing[4]),Te=U.styled.div($||($=(0,i.A)(["\n  background-color: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  padding: ",";\n  margin: "," 0;\n  max-height: 600px;\n  overflow: auto;\n"])),F.Ay.colors.neutral[50],F.Ay.colors.neutral[300],F.Ay.borderRadius.md,F.Ay.spacing[3],F.Ay.spacing[3]),Ne=U.styled.pre(X||(X=(0,i.A)(["\n  background-color: ",";\n  color: ",";\n  border-radius: ",";\n  padding: ",";\n  overflow: auto;\n  max-height: 500px;\n  font-family: ",";\n  font-size: ",";\n  line-height: 1.5;\n  margin: 0;\n"])),F.Ay.colors.neutral[900],F.Ay.colors.neutral[100],F.Ay.borderRadius.md,F.Ay.spacing[3],F.Ay.typography.fontFamily.code,F.Ay.typography.fontSize.sm),De=U.styled.div(Q||(Q=(0,i.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: ",";\n  margin: "," 0;\n"])),F.Ay.spacing[4],F.Ay.spacing[4]),Me=(0,U.styled)(v.A)(ee||(ee=(0,i.A)(["\n  .ant-card-body {\n    padding: ",";\n  }\n"])),F.Ay.spacing[3]);U.styled.div(ne||(ne=(0,i.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  margin-bottom: ",";\n"])),F.Ay.spacing[2],F.Ay.colors.neutral[200],F.Ay.borderRadius.sm,F.Ay.spacing[2]);const Be=function(){(0,u.wA)();var e=(0,u.d4)((function(e){var n;return(null==e||null===(n=e.app)||void 0===n?void 0:n.components)||[]})),n=(0,u.d4)((function(e){var n;return(null==e||null===(n=e.app)||void 0===n?void 0:n.layouts)||[]})),t=(0,u.d4)((function(e){var n;return(null==e||null===(n=e.themes)||void 0===n?void 0:n.activeTheme)||"default"})),i=(0,u.d4)((function(e){var n;return(null==e||null===(n=e.themes)||void 0===n?void 0:n.themes)||[]})),s=(0,u.d4)((function(e){var n;return(null==e||null===(n=e.websocket)||void 0===n?void 0:n.connected)||!1})),d=(0,u.d4)((function(e){return(null==e?void 0:e.templates)||{}})),U=(0,p.useState)("react"),F=(0,c.A)(U,2),L=F[0],J=F[1],V=(0,p.useState)({typescript:!1,includeAccessibility:!0,includeTests:!1,includeStorybook:!1,styleFramework:"styled-components",stateManagement:"useState",projectStructure:"single-file",bundler:"vite",packageManager:"npm",includeDocker:!1,includeCiCd:!1}),H=(0,c.A)(V,2),_=H[0],G=H[1],Y=(0,p.useState)("configure"),W=(0,c.A)(Y,2),q=W[0],K=W[1],Z=(0,p.useState)(""),$=(0,c.A)(Z,2),X=$[0],Q=$[1],ee=(0,p.useState)(!1),ne=(0,c.A)(ee,2),re=ne[0],oe=ne[1],ae=(0,p.useState)(!1),ce=(0,c.A)(ae,2),ie=ce[0],se=ce[1],le=(0,p.useState)([]),pe=(0,c.A)(le,2),ue=pe[0],de=pe[1],me=(0,p.useState)([]),fe=(0,c.A)(me,2),ye=fe[0],ve=fe[1],ge=(0,p.useState)([]),he=(0,c.A)(ge,2),be=he[0],Ae=he[1],Ee=(0,p.useState)(!1),Se=(0,c.A)(Ee,2),xe=(Se[0],Se[1],(0,p.useState)(0)),Be=(0,c.A)(xe,2),ze=Be[0],Ue=Be[1],Fe=(0,p.useState)(!1),Le=(0,c.A)(Fe,2),Je=(Le[0],Le[1],(0,p.useState)(!1)),Ve=(0,c.A)(Je,2),He=(Ve[0],Ve[1],(0,p.useState)(!1)),_e=(0,c.A)(He,2),Ge=(_e[0],_e[1],(0,p.useState)([])),Ye=(0,c.A)(Ge,2),We=(Ye[0],Ye[1],[{value:"react",label:"React",icon:"⚛️",description:"Modern React with hooks"},{value:"react-ts",label:"React + TypeScript",icon:"⚛️",description:"React with TypeScript support"},{value:"vue",label:"Vue.js",icon:"🟢",description:"Vue 3 with Composition API"},{value:"vue-ts",label:"Vue + TypeScript",icon:"🟢",description:"Vue 3 with TypeScript"},{value:"angular",label:"Angular",icon:"🔴",description:"Angular with TypeScript"},{value:"svelte",label:"Svelte",icon:"🧡",description:"Svelte with modern features"},{value:"next",label:"Next.js",icon:"⚫",description:"Next.js with SSR support"},{value:"nuxt",label:"Nuxt.js",icon:"🟢",description:"Nuxt.js for Vue"},{value:"html",label:"HTML/CSS/JS",icon:"🌐",description:"Vanilla web technologies"},{value:"react-native",label:"React Native",icon:"📱",description:"Mobile app with React Native"},{value:"flutter",label:"Flutter",icon:"🐦",description:"Cross-platform with Flutter"},{value:"ionic",label:"Ionic",icon:"⚡",description:"Hybrid mobile apps"}]);(0,p.useEffect)((function(){e.length>0&&0===ye.length&&ve(e.map((function(e){return e.id}))),n.length>0&&0===be.length&&Ae(n.map((function(e){return e.id})))}),[e,n,ye.length,be.length]),(0,p.useEffect)((function(){var e=localStorage.getItem("exportHistory");if(e)try{de(JSON.parse(e))}catch(e){console.error("Failed to load export history:",e)}}),[]);var qe=(0,p.useCallback)((0,a.A)(l().mark((function t(){var r,o;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:oe(!0);try{r={components:e.filter((function(e){return ye.includes(e.id)})),layouts:n.filter((function(e){return be.includes(e.id)})),styles:{},data:{}},o=te(r,L,_),Q("string"==typeof o?o:JSON.stringify(o,null,2))}catch(e){console.error("Preview generation failed:",e),g.Ay.error("Failed to generate preview"),Q("// Preview generation failed")}finally{oe(!1)}case 2:case"end":return t.stop()}}),t)}))),[e,n,ye,be,L,_]);(0,p.useEffect)((function(){"preview"===q&&qe()}),[q,qe]),(0,p.useEffect)((function(){if(s&&"preview"===q){var e={type:"export_config_update",data:{format:L,options:_,components:ye,layouts:be}};console.log("WebSocket export config update:",e)}}),[s,q,L,_,ye,be]);var Ke=function(e,n){G((function(t){return we(we({},t),{},(0,o.A)({},e,n))}))},Ze=function(){var o=(0,a.A)(l().mark((function o(){var a,c,s,p,u,m,f;return l().wrap((function(o){for(;;)switch(o.prev=o.next){case 0:return se(!0),Ue(0),o.prev=2,e.filter((function(e){return ye.includes(e.id)})),n.filter((function(e){return be.includes(e.id)})),d.layoutTemplates,d.appTemplates,i.find((function(e){return e.id===t})),a=setInterval((function(){Ue((function(e){return Math.min(e+10,90)}))}),200),o.next=7,fetch("/api/enhanced-export/",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify({app_id:1,format:L,options:_})});case 7:if(c=o.sent,clearInterval(a),Ue(100),c.ok){o.next=12;break}throw new Error("Export failed");case 12:return o.next=14,c.json();case 14:s=o.sent,p={id:Date.now(),format:L,options:_,timestamp:(new Date).toISOString(),status:"success",type:s.type,size:s.code?s.code.length:Object.keys(s.files||{}).length},u=[p].concat((0,r.A)(ue.slice(0,9))),de(u),localStorage.setItem("exportHistory",JSON.stringify(u)),"single-file"===s.type?$e(s.code,"app.".concat(en(L))):"multi-file"===s.type?Xe(s.files):"zip"===s.type&&Qe(s.zip_data,"app-export.zip"),g.Ay.success("Export completed successfully!"),o.next=31;break;case 23:o.prev=23,o.t0=o.catch(2),console.error("Export failed:",o.t0),g.Ay.error("Export failed. Please try again."),m={id:Date.now(),format:L,options:_,timestamp:(new Date).toISOString(),status:"failed",error:o.t0.message},f=[m].concat((0,r.A)(ue.slice(0,9))),de(f),localStorage.setItem("exportHistory",JSON.stringify(f));case 31:return o.prev=31,se(!1),Ue(0),o.finish(31);case 35:case"end":return o.stop()}}),o,null,[[2,23,31,35]])})));return function(){return o.apply(this,arguments)}}(),$e=function(e,n){var t=new Blob([e],{type:"text/plain"}),r=URL.createObjectURL(t),o=document.createElement("a");o.href=r,o.download=n,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(r)},Xe=function(){var e=(0,a.A)(l().mark((function e(n){var t;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:(t=n["App.jsx"]||n["App.tsx"]||n["index.html"]||Object.values(n)[0])&&$e(t,"App.jsx");case 2:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}(),Qe=function(e,n){for(var t=atob(e),r=new Uint8Array(t.length),o=0;o<t.length;o++)r[o]=t.charCodeAt(o);var a=new Blob([r],{type:"application/zip"}),c=URL.createObjectURL(a),i=document.createElement("a");i.href=c,i.download=n,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(c)},en=function(e){return{react:"jsx","react-ts":"tsx",vue:"vue","vue-ts":"vue",angular:"ts",svelte:"svelte",html:"html","react-native":"jsx",flutter:"dart",ionic:"ts"}[e]||"js"};return p.createElement(Re,null,p.createElement(je,{level:2},p.createElement(P.A,null)," Enhanced Code Exporter"),p.createElement(ke,null,"Export your application to multiple frameworks with advanced configuration options, preview functionality, and project management features."),p.createElement(m.A,{activeKey:q,onChange:K,size:"large"},p.createElement(Ce,{tab:p.createElement("span",null,p.createElement(I.A,null),"Configure"),key:"configure"},p.createElement(De,null,p.createElement(Me,{title:"Export Format",size:"small"},p.createElement(f.A,{value:L,onChange:J,style:{width:"100%"},size:"large"},We.map((function(e){return p.createElement(Pe,{key:e.value,value:e.value},p.createElement(h.A,null,p.createElement("span",null,e.icon),p.createElement("div",null,p.createElement("div",null,e.label),p.createElement(Oe,{type:"secondary",style:{fontSize:"12px"}},e.description))))})))),p.createElement(Me,{title:"Project Structure",size:"small"},p.createElement(b.Ay.Group,{value:_.projectStructure,onChange:function(e){return Ke("projectStructure",e.target.value)},style:{width:"100%"}},[{value:"single-file",label:"Single File",description:"All code in one file"},{value:"multi-file",label:"Multiple Files",description:"Organized file structure"},{value:"full-project",label:"Full Project",description:"Complete project with configs"}].map((function(e){return p.createElement(b.Ay,{key:e.value,value:e.value,style:{display:"block",marginBottom:8}},p.createElement("div",null,p.createElement("div",null,e.label),p.createElement(Oe,{type:"secondary",style:{fontSize:"12px"}},e.description)))})))),p.createElement(Me,{title:"Style Framework",size:"small"},p.createElement(f.A,{value:_.styleFramework,onChange:function(e){return Ke("styleFramework",e)},style:{width:"100%"}},[{value:"styled-components",label:"Styled Components",description:"CSS-in-JS with styled-components"},{value:"emotion",label:"Emotion",description:"CSS-in-JS with Emotion"},{value:"tailwind",label:"Tailwind CSS",description:"Utility-first CSS framework"},{value:"css-modules",label:"CSS Modules",description:"Scoped CSS with modules"},{value:"material-ui",label:"Material-UI",description:"React Material Design components"},{value:"chakra-ui",label:"Chakra UI",description:"Simple and modular React components"},{value:"bootstrap",label:"Bootstrap",description:"Popular CSS framework"}].map((function(e){return p.createElement(Pe,{key:e.value,value:e.value},p.createElement("div",null,p.createElement("div",null,e.label),p.createElement(Oe,{type:"secondary",style:{fontSize:"12px"}},e.description)))})))),p.createElement(Me,{title:"State Management",size:"small"},p.createElement(f.A,{value:_.stateManagement,onChange:function(e){return Ke("stateManagement",e)},style:{width:"100%"}},[{value:"useState",label:"React Hooks (useState)",description:"Built-in React state management"},{value:"redux",label:"Redux Toolkit",description:"Predictable state container"},{value:"zustand",label:"Zustand",description:"Lightweight state management"},{value:"context",label:"React Context",description:"Built-in context API"}].map((function(e){return p.createElement(Pe,{key:e.value,value:e.value},p.createElement("div",null,p.createElement("div",null,e.label),p.createElement(Oe,{type:"secondary",style:{fontSize:"12px"}},e.description)))}))))),p.createElement(v.A,{title:"Basic Options",size:"small",style:{marginBottom:16}},p.createElement(h.A,{direction:"vertical",style:{width:"100%"}},p.createElement(A.A,{checked:_.typescript,onChange:function(e){return Ke("typescript",e.target.checked)}},p.createElement(h.A,null,p.createElement("span",null,"TypeScript Support"),p.createElement(E.A,{title:"Generate TypeScript code with type definitions"},p.createElement(R.A,null)))),p.createElement(A.A,{checked:_.includeAccessibility,onChange:function(e){return Ke("includeAccessibility",e.target.checked)}},p.createElement(h.A,null,p.createElement("span",null,"Accessibility Features"),p.createElement(E.A,{title:"Include ARIA labels, roles, and other accessibility attributes"},p.createElement(R.A,null)))),p.createElement(A.A,{checked:_.includeTests,onChange:function(e){return Ke("includeTests",e.target.checked)}},p.createElement(h.A,null,p.createElement("span",null,"Include Tests"),p.createElement(E.A,{title:"Generate test files for components"},p.createElement(R.A,null)))),p.createElement(A.A,{checked:_.includeStorybook,onChange:function(e){return Ke("includeStorybook",e.target.checked)}},p.createElement(h.A,null,p.createElement("span",null,"Storybook Stories"),p.createElement(E.A,{title:"Generate Storybook stories for components"},p.createElement(R.A,null)))))),p.createElement(y.A,null,p.createElement(Ie,{header:"Advanced Options",key:"advanced"},p.createElement(h.A,{direction:"vertical",style:{width:"100%"}},p.createElement("div",null,p.createElement(Oe,{strong:!0},"Package Manager:"),p.createElement(b.Ay.Group,{value:_.packageManager,onChange:function(e){return Ke("packageManager",e.target.value)},style:{marginLeft:16}},p.createElement(b.Ay,{value:"npm"},"npm"),p.createElement(b.Ay,{value:"yarn"},"yarn"),p.createElement(b.Ay,{value:"pnpm"},"pnpm"))),p.createElement("div",null,p.createElement(Oe,{strong:!0},"Bundler:"),p.createElement(b.Ay.Group,{value:_.bundler,onChange:function(e){return Ke("bundler",e.target.value)},style:{marginLeft:16}},p.createElement(b.Ay,{value:"vite"},"Vite"),p.createElement(b.Ay,{value:"webpack"},"Webpack"),p.createElement(b.Ay,{value:"parcel"},"Parcel"))),p.createElement(A.A,{checked:_.includeDocker,onChange:function(e){return Ke("includeDocker",e.target.checked)}},"Include Docker Configuration"),p.createElement(A.A,{checked:_.includeCiCd,onChange:function(e){return Ke("includeCiCd",e.target.checked)}},"Include CI/CD Pipeline")))),p.createElement(S.A,null),p.createElement(h.A,null,p.createElement(x.Ay,{type:"primary",size:"large",icon:p.createElement(T.A,null),onClick:Ze,loading:ie},"Export Application"),p.createElement(x.Ay,{size:"large",icon:p.createElement(N.A,null),onClick:function(){return K("preview")}},"Preview Code")),ie&&p.createElement("div",{style:{marginTop:16}},p.createElement(w.A,{percent:ze,status:"active"}),p.createElement(Oe,{type:"secondary"},"Generating your application..."))),p.createElement(Ce,{tab:p.createElement("span",null,p.createElement(N.A,null),"Preview"),key:"preview"},p.createElement(h.A,{direction:"vertical",style:{width:"100%"}},p.createElement(j.A,{message:"Code Preview",description:"This is a preview of the generated code. Use the Configure tab to adjust export options.",type:"info",showIcon:!0,action:p.createElement(h.A,null,p.createElement(x.Ay,{size:"small",onClick:qe,loading:re},"Refresh"),p.createElement(x.Ay,{size:"small",icon:p.createElement(D.A,null),onClick:function(){navigator.clipboard.writeText(X).then((function(){g.Ay.success("Code copied to clipboard")})).catch((function(e){console.error("Failed to copy code:",e),g.Ay.error("Failed to copy code")}))}},"Copy"))}),p.createElement(Te,null,re?p.createElement("div",{style:{textAlign:"center",padding:"40px"}},p.createElement(O.A,{size:"large"}),p.createElement("div",{style:{marginTop:16}},"Generating preview...")):p.createElement(Ne,null,X)))),p.createElement(Ce,{tab:p.createElement("span",null,p.createElement(M.A,null),"History"),key:"history"},p.createElement(h.A,{direction:"vertical",style:{width:"100%"}},p.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},p.createElement(je,{level:4},"Export History"),p.createElement(x.Ay,{icon:p.createElement(B.A,null),onClick:function(){de([]),localStorage.removeItem("exportHistory"),g.Ay.success("Export history cleared")}},"Clear History")),0===ue.length?p.createElement("div",{style:{textAlign:"center",padding:"40px"}},p.createElement(Oe,{type:"secondary"},"No export history yet")):p.createElement(k.A,{dataSource:ue,renderItem:function(e){var n;return p.createElement(k.A.Item,{actions:[p.createElement(x.Ay,{size:"small",icon:p.createElement(z.A,null)},"Re-export")]},p.createElement(k.A.Item.Meta,{title:p.createElement(h.A,null,p.createElement("span",null,(null===(n=We.find((function(n){return n.value===e.format})))||void 0===n?void 0:n.label)||e.format),p.createElement(C.A,{color:"success"===e.status?"green":"red"},e.status)),description:p.createElement("div",null,p.createElement("div",null,"Exported on ",new Date(e.timestamp).toLocaleString()),e.size&&p.createElement("div",null,"Size: ",e.size," characters"),e.error&&p.createElement("div",{style:{color:"red"}},"Error: ",e.error))}))}})))))}}}]);