"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[6329],{86329:(e,t,n)=>{n.d(t,{A:()=>l});var s=n(10467),o=n(23029),a=n(92901),r=n(54756),i=n.n(r),c=new(function(){return(0,a.A)((function e(){(0,o.A)(this,e),this.ws=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=3e3,this.listeners=new Map,this.messageQueue=[],this.subscriptions=new Set;var t="https:"===window.location.protocol?"wss:":"ws:";this.wsUrl="".concat(t,"//").concat("localhost:8000","/ws/ai-suggestions/")}),[{key:"connect",value:function(){var e=this;return this.isConnected||this.ws?Promise.resolve():new Promise((function(t,n){try{e.ws=new WebSocket(e.wsUrl),e.ws.onopen=function(){console.log("AI WebSocket connected"),e.isConnected=!0,e.reconnectAttempts=0,e.processMessageQueue(),e.emit("connected"),t()},e.ws.onmessage=function(t){try{var n=JSON.parse(t.data);e.handleMessage(n)}catch(e){console.error("Error parsing AI WebSocket message:",e)}},e.ws.onclose=function(t){console.log("AI WebSocket disconnected:",t.code,t.reason),e.isConnected=!1,e.ws=null,e.emit("disconnected",{code:t.code,reason:t.reason}),1e3!==t.code&&e.reconnectAttempts<e.maxReconnectAttempts&&e.scheduleReconnect()},e.ws.onerror=function(t){console.error("AI WebSocket error:",t),e.emit("error",t),n(t)}}catch(e){console.error("Error creating AI WebSocket:",e),n(e)}}))}},{key:"disconnect",value:function(){this.ws&&(this.ws.close(1e3,"Client disconnect"),this.ws=null,this.isConnected=!1)}},{key:"scheduleReconnect",value:function(){var e=this;this.reconnectAttempts++;var t=this.reconnectInterval*Math.pow(2,this.reconnectAttempts-1);console.log("Scheduling AI WebSocket reconnect attempt ".concat(this.reconnectAttempts," in ").concat(t,"ms")),setTimeout((function(){e.isConnected||e.connect().catch((function(e){console.error("AI WebSocket reconnect failed:",e)}))}),t)}},{key:"send",value:function(e){this.isConnected&&this.ws?this.ws.send(JSON.stringify(e)):(this.messageQueue.push(e),this.isConnected||this.connect())}},{key:"processMessageQueue",value:function(){for(;this.messageQueue.length>0;){var e=this.messageQueue.shift();this.send(e)}}},{key:"handleMessage",value:function(e){var t=e.type;switch(t){case"connection_established":console.log("AI WebSocket connection established");break;case"layout_suggestions":this.emit("layoutSuggestions",e.suggestions);break;case"component_combinations":this.emit("componentCombinations",e.suggestions);break;case"app_analysis":this.emit("appAnalysis",e.analysis);break;case"layout_suggestions_broadcast":this.emit("layoutSuggestionsBroadcast",e.suggestions);break;case"component_combinations_broadcast":this.emit("componentCombinationsBroadcast",e.suggestions);break;case"ai_suggestion_update":this.emit("aiSuggestionUpdate",e);break;case"error":console.error("AI WebSocket error:",e.message),this.emit("error",new Error(e.message));break;case"pong":this.emit("pong",e);break;default:console.log("Unknown AI WebSocket message type:",t,e)}}},{key:"requestLayoutSuggestions",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=arguments.length>3&&void 0!==arguments[3]&&arguments[3];this.send({type:"get_layout_suggestions",components:e,layouts:t,context:n,broadcast:s,timestamp:(new Date).toISOString()})}},{key:"requestComponentCombinations",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=arguments.length>3&&void 0!==arguments[3]&&arguments[3];this.send({type:"get_component_combinations",components:e,selected_component:t,context:n,broadcast:s,timestamp:(new Date).toISOString()})}},{key:"requestAppAnalysis",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];this.send({type:"analyze_app_structure",components:e,layouts:t,timestamp:(new Date).toISOString()})}},{key:"subscribeToUpdates",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"all";this.subscriptions.add(e),this.send({type:"subscribe_to_updates",subscription_type:e,timestamp:(new Date).toISOString()})}},{key:"ping",value:function(){this.send({type:"ping",timestamp:(new Date).toISOString()})}},{key:"addEventListener",value:function(e,t){this.listeners.has(e)||this.listeners.set(e,new Set),this.listeners.get(e).add(t)}},{key:"removeEventListener",value:function(e,t){this.listeners.has(e)&&this.listeners.get(e).delete(t)}},{key:"emit",value:function(e,t){this.listeners.has(e)&&this.listeners.get(e).forEach((function(n){try{n(t)}catch(t){console.error("Error in AI WebSocket event listener for ".concat(e,":"),t)}}))}},{key:"getStatus",value:function(){return{connected:this.isConnected,reconnectAttempts:this.reconnectAttempts,subscriptions:Array.from(this.subscriptions),queuedMessages:this.messageQueue.length}}}])}());"undefined"!=typeof window&&setTimeout((function(){c.connect().catch((function(e){console.warn("Initial AI WebSocket connection failed:",e)}))}),1e3);const u=c,l=new(function(){return(0,a.A)((function e(){(0,o.A)(this,e),this.baseUrl="".concat("http://localhost:8000","/api/ai"),this.cache=new Map,this.cacheTimeout=3e5,this.useWebSocket=!0,this.wsService=u,this.setupWebSocketListeners()}),[{key:"setupWebSocketListeners",value:function(){var e=this;this.wsService.addEventListener("layoutSuggestions",(function(t){e.cache.set("ws_layout_suggestions",{data:{suggestions:t,status:"success"},timestamp:Date.now()})})),this.wsService.addEventListener("componentCombinations",(function(t){e.cache.set("ws_component_combinations",{data:{suggestions:t,status:"success"},timestamp:Date.now()})})),this.wsService.addEventListener("error",(function(e){console.warn("AI WebSocket error, falling back to HTTP:",e)}))}},{key:"generateLayoutSuggestions",value:(c=(0,s.A)(i().mark((function e(t){var n,s,o,a,r,c,u=arguments;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=u.length>1&&void 0!==u[1]?u[1]:[],s=u.length>2&&void 0!==u[2]?u[2]:{},o="layout_".concat(JSON.stringify({components:t,layouts:n,context:s})),!this.cache.has(o)){e.next=7;break}if(a=this.cache.get(o),!(Date.now()-a.timestamp<this.cacheTimeout)){e.next=7;break}return e.abrupt("return",a.data);case 7:if(!this.useWebSocket||!this.wsService.getStatus().connected){e.next=17;break}return e.prev=8,e.next=11,this._getLayoutSuggestionsViaWebSocket(t,n,s,o);case 11:return e.abrupt("return",e.sent);case 14:e.prev=14,e.t0=e.catch(8),console.warn("WebSocket request failed, falling back to HTTP:",e.t0);case 17:return e.prev=17,e.next=20,fetch("".concat(this.baseUrl,"/layout-suggestions/"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:this._getAuthHeader()},body:JSON.stringify({components:t,layouts:n,context:s})});case 20:if((r=e.sent).ok){e.next=23;break}throw new Error("HTTP error! status: ".concat(r.status));case 23:return e.next=25,r.json();case 25:return c=e.sent,this.cache.set(o,{data:c,timestamp:Date.now()}),e.abrupt("return",c);case 30:return e.prev=30,e.t1=e.catch(17),console.error("Error generating layout suggestions:",e.t1),e.abrupt("return",this._getFallbackLayoutSuggestions(t));case 34:case"end":return e.stop()}}),e,this,[[8,14],[17,30]])}))),function(e){return c.apply(this,arguments)})},{key:"_getLayoutSuggestionsViaWebSocket",value:(r=(0,s.A)(i().mark((function e(t,n,s,o){var a=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,r){var i=setTimeout((function(){a.wsService.removeEventListener("layoutSuggestions",c),r(new Error("WebSocket request timeout"))}),1e4),c=function(t){clearTimeout(i),a.wsService.removeEventListener("layoutSuggestions",c);var n={suggestions:t,status:"success"};a.cache.set(o,{data:n,timestamp:Date.now()}),e(n)};a.wsService.addEventListener("layoutSuggestions",c),a.wsService.requestLayoutSuggestions(t,n,s)})));case 1:case"end":return e.stop()}}),e)}))),function(e,t,n,s){return r.apply(this,arguments)})},{key:"generateComponentCombinations",value:(n=(0,s.A)(i().mark((function e(t){var n,s,o,a,r,c,u=arguments;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=u.length>1&&void 0!==u[1]?u[1]:null,s=u.length>2&&void 0!==u[2]?u[2]:{},o="combinations_".concat(JSON.stringify({components:t,selectedComponent:n,context:s})),!this.cache.has(o)){e.next=7;break}if(a=this.cache.get(o),!(Date.now()-a.timestamp<this.cacheTimeout)){e.next=7;break}return e.abrupt("return",a.data);case 7:if(!this.useWebSocket||!this.wsService.getStatus().connected){e.next=17;break}return e.prev=8,e.next=11,this._getComponentCombinationsViaWebSocket(t,n,s,o);case 11:return e.abrupt("return",e.sent);case 14:e.prev=14,e.t0=e.catch(8),console.warn("WebSocket request failed, falling back to HTTP:",e.t0);case 17:return e.prev=17,e.next=20,fetch("".concat(this.baseUrl,"/component-combinations/"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:this._getAuthHeader()},body:JSON.stringify({components:t,selected_component:n,context:s})});case 20:if((r=e.sent).ok){e.next=23;break}throw new Error("HTTP error! status: ".concat(r.status));case 23:return e.next=25,r.json();case 25:return c=e.sent,this.cache.set(o,{data:c,timestamp:Date.now()}),e.abrupt("return",c);case 30:return e.prev=30,e.t1=e.catch(17),console.error("Error generating component combinations:",e.t1),e.abrupt("return",this._getFallbackCombinationSuggestions(t,n));case 34:case"end":return e.stop()}}),e,this,[[8,14],[17,30]])}))),function(e){return n.apply(this,arguments)})},{key:"_getComponentCombinationsViaWebSocket",value:(t=(0,s.A)(i().mark((function e(t,n,s,o){var a=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,r){var i=setTimeout((function(){a.wsService.removeEventListener("componentCombinations",c),r(new Error("WebSocket request timeout"))}),1e4),c=function(t){clearTimeout(i),a.wsService.removeEventListener("componentCombinations",c);var n={suggestions:t,status:"success"};a.cache.set(o,{data:n,timestamp:Date.now()}),e(n)};a.wsService.addEventListener("componentCombinations",c),a.wsService.requestComponentCombinations(t,n,s)})));case 1:case"end":return e.stop()}}),e)}))),function(e,n,s,o){return t.apply(this,arguments)})},{key:"analyzeAppStructure",value:(e=(0,s.A)(i().mark((function e(t){var n,s,o=arguments;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:[],e.prev=1,e.next=4,fetch("".concat(this.baseUrl,"/analyze-structure/"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:this._getAuthHeader()},body:JSON.stringify({components:t,layouts:n})});case 4:if((s=e.sent).ok){e.next=7;break}throw new Error("HTTP error! status: ".concat(s.status));case 7:return e.next=9,s.json();case 9:return e.abrupt("return",e.sent);case 12:return e.prev=12,e.t0=e.catch(1),console.error("Error analyzing app structure:",e.t0),e.abrupt("return",this._getBasicAnalysis(t));case 16:case"end":return e.stop()}}),e,this,[[1,12]])}))),function(t){return e.apply(this,arguments)})},{key:"clearCache",value:function(){this.cache.clear()}},{key:"_getAuthHeader",value:function(){var e=localStorage.getItem("authToken")||sessionStorage.getItem("authToken");return e?"Bearer ".concat(e):""}},{key:"_getFallbackLayoutSuggestions",value:function(e){var t=e.length,n=[];return t<=3&&n.push({id:"simple_flex",name:"Simple Flexbox Layout",description:"Basic vertical layout for simple apps",score:80,explanation:"Perfect for apps with few components",structure:{display:"flex",flexDirection:"column"}}),t>3&&n.push({id:"grid_layout",name:"Grid Layout",description:"Organized grid for multiple components",score:85,explanation:"Grid layout works well for organizing many components",structure:{display:"grid",gap:"16px"}}),n.push({id:"header_footer",name:"Header-Footer Layout",description:"Classic layout with header and footer",score:75,explanation:"Traditional layout suitable for most applications",structure:{header:!0,footer:!0}}),{suggestions:n,status:"fallback",component_count:t}}},{key:"_getFallbackCombinationSuggestions",value:function(e,t){var n=[],s=e.map((function(e){return e.type}));if(t){var o=t.type;"button"!==o||s.includes("form")||n.push({id:"button_form",name:"Button + Form",description:"Add a form to go with your button",score:70,components:["button","form"],missing_components:["form"]}),"text"!==o||s.includes("image")||n.push({id:"text_image",name:"Text + Image",description:"Add an image to complement your text",score:65,components:["text","image"],missing_components:["image"]})}return s.includes("header")||n.push({id:"add_header",name:"Add Header",description:"Every app needs a header for navigation",score:80,components:["header"],missing_components:["header"]}),{suggestions:n,status:"fallback",component_count:e.length}}},{key:"_getBasicAnalysis",value:function(e){var t={};return e.forEach((function(e){var n=e.type||"unknown";t[n]=(t[n]||0)+1})),{analysis:{component_count:e.length,component_types:t,has_navigation:Object.keys(t).some((function(e){return["header","nav","menu"].includes(e)})),has_forms:Object.keys(t).some((function(e){return["form","input","button"].includes(e)})),has_media:Object.keys(t).some((function(e){return["image","video","gallery"].includes(e)})),complexity_score:2*Object.keys(t).length+e.length,app_type:"general"},status:"basic"}}}]);var e,t,n,r,c}())}}]);