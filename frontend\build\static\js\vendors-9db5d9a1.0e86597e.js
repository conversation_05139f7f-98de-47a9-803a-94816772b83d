/*! For license information please see vendors-9db5d9a1.0e86597e.js.LICENSE.txt */
(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[5890],{251:(e,t)=>{t.read=function(e,t,r,n,o){var i,c,a=8*o-n-1,s=(1<<a)-1,u=s>>1,f=-7,l=r?o-1:0,p=r?-1:1,_=e[t+l];for(l+=p,i=_&(1<<-f)-1,_>>=-f,f+=a;f>0;i=256*i+e[t+l],l+=p,f-=8);for(c=i&(1<<-f)-1,i>>=-f,f+=n;f>0;c=256*c+e[t+l],l+=p,f-=8);if(0===i)i=1-u;else{if(i===s)return c?NaN:1/0*(_?-1:1);c+=Math.pow(2,n),i-=u}return(_?-1:1)*c*Math.pow(2,i-n)},t.write=function(e,t,r,n,o,i){var c,a,s,u=8*i-o-1,f=(1<<u)-1,l=f>>1,p=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,_=n?0:i-1,h=n?1:-1,d=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,c=f):(c=Math.floor(Math.log(t)/Math.LN2),t*(s=Math.pow(2,-c))<1&&(c--,s*=2),(t+=c+l>=1?p/s:p*Math.pow(2,1-l))*s>=2&&(c++,s/=2),c+l>=f?(a=0,c=f):c+l>=1?(a=(t*s-1)*Math.pow(2,o),c+=l):(a=t*Math.pow(2,l-1)*Math.pow(2,o),c=0));o>=8;e[r+_]=255&a,_+=h,a/=256,o-=8);for(c=c<<o|a,u+=o;u>0;e[r+_]=255&c,_+=h,c/=256,u-=8);e[r+_-h]|=128*d}},1932:(e,t,r)=>{"use strict";var n=Symbol.for("immer-nothing"),o=Symbol.for("immer-draftable"),i=Symbol.for("immer-state");function c(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var a=Object.getPrototypeOf;function s(e){return!!e&&!!e[i]}function u(e){return!!e&&(l(e)||Array.isArray(e)||!!e[o]||!!e.constructor?.[o]||y(e)||b(e))}var f=Object.prototype.constructor.toString();function l(e){if(!e||"object"!=typeof e)return!1;const t=a(e);if(null===t)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function p(e,t){0===_(e)?Reflect.ownKeys(e).forEach((r=>{t(r,e[r],e)})):e.forEach(((r,n)=>t(n,r,e)))}function _(e){const t=e[i];return t?t.type_:Array.isArray(e)?1:y(e)?2:b(e)?3:0}function h(e,t){return 2===_(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function d(e,t,r){const n=_(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function y(e){return e instanceof Map}function b(e){return e instanceof Set}function w(e){return e.copy_||e.base_}function m(e,t){if(y(e))return new Map(e);if(b(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=l(e);if(!0===t||"class_only"===t&&!r){const t=Object.getOwnPropertyDescriptors(e);delete t[i];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){const o=r[n],i=t[o];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(t[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:e[o]})}return Object.create(a(e),t)}{const t=a(e);if(null!==t&&r)return{...e};const n=Object.create(t);return Object.assign(n,e)}}function v(e,t=!1){return P(e)||s(e)||!u(e)||(_(e)>1&&(e.set=e.add=e.clear=e.delete=g),Object.freeze(e),t&&Object.entries(e).forEach((([e,t])=>v(t,!0)))),e}function g(){c(2)}function P(e){return Object.isFrozen(e)}var S,z={};function O(e){const t=z[e];return t||c(0),t}function M(){return S}function j(e,t){t&&(O("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function F(e){A(e),e.drafts_.forEach(D),e.drafts_=null}function A(e){e===S&&(S=e.parent_)}function N(e){return S={drafts_:[],parent_:S,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function D(e){const t=e[i];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function C(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return void 0!==e&&e!==r?(r[i].modified_&&(F(t),c(4)),u(e)&&(e=k(t,e),t.parent_||R(t,e)),t.patches_&&O("Patches").generateReplacementPatches_(r[i].base_,e,t.patches_,t.inversePatches_)):e=k(t,r,[]),F(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==n?e:void 0}function k(e,t,r){if(P(t))return t;const n=t[i];if(!n)return p(t,((o,i)=>E(e,n,t,o,i,r))),t;if(n.scope_!==e)return t;if(!n.modified_)return R(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const t=n.copy_;let o=t,i=!1;3===n.type_&&(o=new Set(t),t.clear(),i=!0),p(o,((o,c)=>E(e,n,t,o,c,r,i))),R(e,t,!1),r&&e.patches_&&O("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function E(e,t,r,n,o,i,c){if(s(o)){const c=k(e,o,i&&t&&3!==t.type_&&!h(t.assigned_,n)?i.concat(n):void 0);if(d(r,n,c),!s(c))return;e.canAutoFreeze_=!1}else c&&r.add(o);if(u(o)&&!P(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;k(e,o),t&&t.scope_.parent_||"symbol"==typeof n||!Object.prototype.propertyIsEnumerable.call(r,n)||R(e,o)}}function R(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&v(t,r)}var K={get(e,t){if(t===i)return e;const r=w(e);if(!h(r,t))return function(e,t,r){const n=L(t,r);return n?"value"in n?n.value:n.get?.call(e.draft_):void 0}(e,r,t);const n=r[t];return e.finalized_||!u(n)?n:n===W(e.base_,t)?(I(e),e.copy_[t]=X(n,e)):n},has:(e,t)=>t in w(e),ownKeys:e=>Reflect.ownKeys(w(e)),set(e,t,r){const n=L(w(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const n=W(w(e),t),a=n?.[i];if(a&&a.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(((o=r)===(c=n)?0!==o||1/o==1/c:o!=o&&c!=c)&&(void 0!==r||h(e.base_,t)))return!0;I(e),U(e)}var o,c;return e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==W(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,I(e),U(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const r=w(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){c(11)},getPrototypeOf:e=>a(e.base_),setPrototypeOf(){c(12)}},x={};function W(e,t){const r=e[i];return(r?w(r):e)[t]}function L(e,t){if(!(t in e))return;let r=a(e);for(;r;){const e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=a(r)}}function U(e){e.modified_||(e.modified_=!0,e.parent_&&U(e.parent_))}function I(e){e.copy_||(e.copy_=m(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function X(e,t){const r=y(e)?O("MapSet").proxyMap_(e,t):b(e)?O("MapSet").proxySet_(e,t):function(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:M(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let o=n,i=K;r&&(o=[n],i=x);const{revoke:c,proxy:a}=Proxy.revocable(o,i);return n.draft_=a,n.revoke_=c,a}(e,t);return(t?t.scope_:M()).drafts_.push(r),r}function $(e){if(!u(e)||P(e))return e;const t=e[i];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=m(e,t.scope_.immer_.useStrictShallowCopy_)}else r=m(e,!0);return p(r,((e,t)=>{d(r,e,$(t))})),t&&(t.finalized_=!1),r}p(K,((e,t)=>{x[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),x.deleteProperty=function(e,t){return x.set.call(this,e,t,void 0)},x.set=function(e,t,r){return K.set.call(this,e[0],t,r,e[0])};var q=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{if("function"==typeof e&&"function"!=typeof t){const r=t;t=e;const n=this;return function(e=r,...o){return n.produce(e,(e=>t.call(this,e,...o)))}}let o;if("function"!=typeof t&&c(6),void 0!==r&&"function"!=typeof r&&c(7),u(e)){const n=N(this),i=X(e,void 0);let c=!0;try{o=t(i),c=!1}finally{c?F(n):A(n)}return j(n,r),C(o,n)}if(!e||"object"!=typeof e){if(o=t(e),void 0===o&&(o=e),o===n&&(o=void 0),this.autoFreeze_&&v(o,!0),r){const t=[],n=[];O("Patches").generateReplacementPatches_(e,o,t,n),r(t,n)}return o}c(1)},this.produceWithPatches=(e,t)=>{if("function"==typeof e)return(t,...r)=>this.produceWithPatches(t,(t=>e(t,...r)));let r,n;return[this.produce(e,t,((e,t)=>{r=e,n=t})),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){var t;u(e)||c(8),s(e)&&(s(t=e)||c(10),e=$(t));const r=N(this),n=X(e,void 0);return n[i].isManual_=!0,A(r),n}finishDraft(e,t){const r=e&&e[i];r&&r.isManual_||c(9);const{scope_:n}=r;return j(n,t),C(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));const n=O("Patches").applyPatches_;return s(e)?n(e,t):this.produce(e,(e=>n(e,t)))}};q.produce,q.produceWithPatches.bind(q),q.setAutoFreeze.bind(q),q.setUseStrictShallowCopy.bind(q),q.applyPatches.bind(q),q.createDraft.bind(q),q.finishDraft.bind(q)}}]);