"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[8552],{38552:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var n=r(10467),s=r(5544),a=r(54756),o=r.n(a),l=r(96540),i=r(1807),c=r(35346),d=r(11080),u=i.o5.Title,m=i.o5.Text;const p=function(){var e=(0,l.useState)(!1),t=(0,s.A)(e,2),r=t[0],a=t[1],p=(0,l.useState)(!0),w=(0,s.A)(p,2),f=w[0],v=w[1],h=i.lV.useForm(),E=(0,s.A)(h,1)[0],g=(0,d.g)().token,y=(0,d.Zp)();(0,l.useEffect)((function(){var e=function(){var e=(0,n.A)(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,new Promise((function(e){return setTimeout(e,500)}));case 3:(!g||g.length<10)&&(v(!1),i.iU.error("Invalid or expired password reset link")),e.next=11;break;case 6:e.prev=6,e.t0=e.catch(0),v(!1),i.iU.error("Failed to validate reset token"),console.error("Error validating token:",e.t0);case 11:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(){return e.apply(this,arguments)}}();e()}),[g]);var x=function(){var e=(0,n.A)(o().mark((function e(t){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,a(!0),e.next=4,new Promise((function(e){return setTimeout(e,1e3)}));case 4:i.iU.success("Password has been reset successfully"),y("/login"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),i.iU.error("Failed to reset password"),console.error("Error resetting password:",e.t0);case 12:return e.prev=12,a(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(t){return e.apply(this,arguments)}}();return f?l.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"100vh",padding:"20px"}},l.createElement(i.Zp,{style:{width:"100%",maxWidth:400}},l.createElement("div",{style:{textAlign:"center",marginBottom:24}},l.createElement(u,{level:3},"Reset Password"),l.createElement(m,{type:"secondary"},"Enter your new password below")),l.createElement(i.lV,{form:E,layout:"vertical",onFinish:x},l.createElement(i.lV.Item,{name:"password",rules:[{required:!0,message:"Please enter your new password"},{min:8,message:"Password must be at least 8 characters"}]},l.createElement(i.pd.Password,{prefix:l.createElement(c.sXv,null),placeholder:"New Password",size:"large"})),l.createElement(i.lV.Item,{name:"confirmPassword",dependencies:["password"],rules:[{required:!0,message:"Please confirm your password"},function(e){var t=e.getFieldValue;return{validator:function(e,r){return r&&t("password")!==r?Promise.reject(new Error("The two passwords do not match")):Promise.resolve()}}}]},l.createElement(i.pd.Password,{prefix:l.createElement(c.sXv,null),placeholder:"Confirm Password",size:"large"})),l.createElement(i.lV.Item,null,l.createElement(i.$n,{type:"primary",htmlType:"submit",loading:r,block:!0,size:"large"},"Reset Password")),l.createElement("div",{style:{textAlign:"center"}},l.createElement(d.N_,{to:"/login"},"Back to Login"))))):l.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"100vh",padding:"20px"}},l.createElement(i.Zp,{style:{width:"100%",maxWidth:400,textAlign:"center"}},l.createElement(u,{level:3},"Invalid Reset Link"),l.createElement(m,{type:"secondary"},"The password reset link is invalid or has expired."),l.createElement("div",{style:{marginTop:24}},l.createElement(d.N_,{to:"/forgot-password"},l.createElement(i.$n,{type:"primary"},"Request New Link")))))}}}]);