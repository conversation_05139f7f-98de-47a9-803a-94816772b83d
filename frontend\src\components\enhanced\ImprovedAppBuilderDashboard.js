import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import DashboardLayout from '../layout/DashboardLayout';
import ImprovedComponentBuilder from './ImprovedComponentBuilder';
import EnhancedLayoutDesigner from './LayoutDesigner';
import EnhancedThemeManager from './ThemeManager';
import ImprovedWebSocketManager from './ImprovedWebSocketManager';
import { styled } from '../../design-system';
import theme from '../../design-system/theme';
import { setCurrentView } from '../../redux/minimal-store';
import AccessibleCard from '../a11y/AccessibleCard';

const ContentContainer = styled.div`
  padding: ${theme.spacing[4]};
`;

const PreviewContainer = styled.div`
  padding: ${theme.spacing[4]};
  border: 1px solid ${theme.colors.neutral[200]};
  border-radius: ${theme.borderRadius.md};
  background-color: white;
  min-height: 400px;
`;

const PageTitle = styled.h1`
  font-size: ${theme.typography.fontSize.xl};
  font-weight: ${theme.typography.fontWeight.bold};
  color: ${theme.colors.neutral[800]};
  margin-bottom: ${theme.spacing[4]};
`;

/**
 * Improved App Builder Dashboard with better accessibility and code reuse
 */
const ImprovedAppBuilderDashboard = () => {
  const { currentView = 'components', previewMode = false } = useSelector(state => state.ui || {});
  const dispatch = useDispatch();

  // Set default view if none is selected
  useEffect(() => {
    if (!currentView) {
      dispatch(setCurrentView('components'));
    }
  }, [currentView, dispatch]);

  // Render content based on current view
  const renderContent = () => {
    if (previewMode) {
      return (
        <AccessibleCard title="Application Preview">
          <PreviewContainer>
            <h2>Preview Mode</h2>
            <p>This is a preview of your application. In a real implementation, this would render your actual app with the components, layouts, and themes you've created.</p>

            <div style={{ marginTop: theme.spacing[4] }}>
              <h3>Components</h3>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
                gap: theme.spacing[3],
                marginTop: theme.spacing[2]
              }}>
                {useSelector(state => state.app?.components || []).map(component => (
                  <div key={component.id} style={{
                    padding: theme.spacing[3],
                    border: `1px solid ${theme.colors.neutral[200]}`,
                    borderRadius: theme.borderRadius.md,
                    backgroundColor: theme.colors.neutral[50]
                  }}>
                    <div style={{ fontWeight: theme.typography.fontWeight.semibold }}>{component.name}</div>
                    <div style={{ fontSize: theme.typography.fontSize.sm, color: theme.colors.neutral[500] }}>
                      {component.type}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div style={{ marginTop: theme.spacing[4] }}>
              <h3>Active Theme</h3>
              {useSelector(state => state.themes || [])
                .filter(theme => theme.active)
                .map(theme => (
                  <div key={theme.id} style={{
                    padding: theme.spacing[3],
                    border: `1px solid ${theme.colors.neutral[200]}`,
                    borderRadius: theme.borderRadius.md,
                    backgroundColor: theme.colors.neutral[50]
                  }}>
                    <div style={{ fontWeight: theme.typography.fontWeight.semibold }}>{theme.name}</div>
                    <div style={{
                      display: 'flex',
                      gap: theme.spacing[2],
                      marginTop: theme.spacing[2]
                    }}>
                      {theme.colors && Object.entries(theme.colors).map(([name, color]) => (
                        <div key={name} style={{
                          width: '24px',
                          height: '24px',
                          backgroundColor: color,
                          borderRadius: '50%',
                          border: '1px solid #ddd'
                        }} title={`${name}: ${color}`} />
                      ))}
                    </div>
                  </div>
                ))}
            </div>
          </PreviewContainer>
        </AccessibleCard>
      );
    }

    switch (currentView) {
      case 'components':
        return (
          <>
            <PageTitle>Component Builder</PageTitle>
            <ImprovedComponentBuilder />
          </>
        );
      case 'layouts':
        return (
          <>
            <PageTitle>Layout Designer</PageTitle>
            <EnhancedLayoutDesigner />
          </>
        );
      case 'themes':
        return (
          <>
            <PageTitle>Theme Manager</PageTitle>
            <EnhancedThemeManager />
          </>
        );
      case 'websocket':
        return (
          <>
            <PageTitle>WebSocket Manager</PageTitle>
            <ImprovedWebSocketManager />
          </>
        );
      default:
        return (
          <>
            <PageTitle>Component Builder</PageTitle>
            <ImprovedComponentBuilder />
          </>
        );
    }
  };

  return (
    <DashboardLayout>
      <ContentContainer>
        <main id="main-content" role="main" aria-label={`App Builder - ${currentView}`}>
          {renderContent()}
        </main>
      </ContentContainer>
    </DashboardLayout>
  );
};

export default ImprovedAppBuilderDashboard;
