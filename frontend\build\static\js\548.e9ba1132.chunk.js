"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[548],{3548:(e,t,r)=>{r.r(t),r.d(t,{default:()=>A});var n=r(6540),l=r(4976),a=r(3016),i=r(2702),o=r(9249),c=r(7197),m=r(7152),p=r(6370),s=r(677),d=r(2977),g=r(462),u=r(1372),E=r(3652),y=r(2786),x=a.A.Title,f=a.A.Paragraph;const A=function(){return n.createElement("div",{style:{padding:"40px 20px",maxWidth:"1200px",margin:"0 auto"}},n.createElement("div",{style:{textAlign:"center",marginBottom:"60px"}},n.createElement(x,{level:1,style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",marginBottom:"20px"}},"App Builder 201 - MVP"),n.createElement(f,{style:{fontSize:"20px",color:"#666",maxWidth:"600px",margin:"0 auto"}},"Build your application with minimal setup. Start with our MVP version and create amazing apps in minutes."),n.createElement(i.A,{size:"large",style:{marginTop:"30px"}},n.createElement(l.N_,{to:"/mvp"},n.createElement(o.Ay,{type:"primary",size:"large",icon:n.createElement(d.A,null),style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"none",height:"50px",padding:"0 30px",fontSize:"16px"}},"Start Building Now")),n.createElement(l.N_,{to:"/app-builder"},n.createElement(o.Ay,{size:"large",style:{height:"50px",padding:"0 30px"}},"Full Version")))),n.createElement(c.A,{message:"🚀 MVP Version Available",description:"Try our streamlined MVP version for quick app prototyping. Perfect for getting started with core features.",type:"info",showIcon:!0,style:{marginBottom:"40px"},action:n.createElement(l.N_,{to:"/mvp"},n.createElement(o.Ay,{size:"small",type:"primary"},"Try MVP"))}),n.createElement(m.A,{gutter:[32,32],justify:"center"},n.createElement(p.A,{xs:24,sm:12,lg:8},n.createElement(s.A,{hoverable:!0,style:{textAlign:"center",height:"100%"},cover:n.createElement("div",{style:{padding:"40px",backgroundColor:"#f0f2f5"}},n.createElement(g.A,{style:{fontSize:"48px",color:"#1890ff"}}))},n.createElement(x,{level:3},"MVP Builder"),n.createElement(f,null,"Quick and simple app builder with essential features. Perfect for rapid prototyping and MVP development."),n.createElement(l.N_,{to:"/mvp"},n.createElement(o.Ay,{type:"primary",size:"large"},"Start MVP")))),n.createElement(p.A,{xs:24,sm:12,lg:8},n.createElement(s.A,{hoverable:!0,style:{textAlign:"center",height:"100%"},cover:n.createElement("div",{style:{padding:"40px",backgroundColor:"#f0f2f5"}},n.createElement(u.A,{style:{fontSize:"48px",color:"#52c41a"}}))},n.createElement(x,{level:3},"Full App Builder"),n.createElement(f,null,"Complete app builder with advanced features, themes, collaboration, and comprehensive tooling."),n.createElement(l.N_,{to:"/app-builder"},n.createElement(o.Ay,{type:"primary",size:"large"},"Full Builder")))),n.createElement(p.A,{xs:24,sm:12,lg:8},n.createElement(s.A,{hoverable:!0,style:{textAlign:"center",height:"100%"},cover:n.createElement("div",{style:{padding:"40px",backgroundColor:"#f0f2f5"}},n.createElement(E.A,{style:{fontSize:"48px",color:"#fa8c16"}}))},n.createElement(x,{level:3},"WebSocket Testing"),n.createElement(f,null,"Test real-time communication features and WebSocket connections for your applications."),n.createElement(l.N_,{to:"/websocket"},n.createElement(o.Ay,{type:"primary",size:"large"},"Test WebSocket"))))),n.createElement("div",{style:{textAlign:"center",marginTop:"60px"}},n.createElement(x,{level:2},"MVP Features"),n.createElement(f,{style:{fontSize:"16px",color:"#666",marginBottom:"40px"}},"Everything you need to build your first app"),n.createElement(m.A,{gutter:[24,24],style:{marginTop:"40px"}},n.createElement(p.A,{xs:24,md:6},n.createElement("div",{style:{textAlign:"center"}},n.createElement(y.A,{style:{fontSize:"32px",color:"#1890ff",marginBottom:"16px"}}),n.createElement(x,{level:4},"Component Creation"),n.createElement(f,null,"Add buttons, text, inputs, images, cards, and lists with simple configuration."))),n.createElement(p.A,{xs:24,md:6},n.createElement("div",{style:{textAlign:"center"}},n.createElement(y.A,{style:{fontSize:"32px",color:"#52c41a",marginBottom:"16px"}}),n.createElement(x,{level:4},"Layout Design"),n.createElement(f,null,"Organize components using Grid, Flex, and Stack layouts with responsive options."))),n.createElement(p.A,{xs:24,md:6},n.createElement("div",{style:{textAlign:"center"}},n.createElement(y.A,{style:{fontSize:"32px",color:"#fa8c16",marginBottom:"16px"}}),n.createElement(x,{level:4},"Real-time Updates"),n.createElement(f,null,"See changes instantly with WebSocket-powered live updates and collaboration."))),n.createElement(p.A,{xs:24,md:6},n.createElement("div",{style:{textAlign:"center"}},n.createElement(y.A,{style:{fontSize:"32px",color:"#722ed1",marginBottom:"16px"}}),n.createElement(x,{level:4},"Export & Save"),n.createElement(f,null,"Save your app configuration and export as JSON for further development."))))),n.createElement("div",{style:{textAlign:"center",marginTop:"60px",padding:"40px",background:"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",borderRadius:"12px"}},n.createElement(x,{level:2},"Ready to Build Your App?"),n.createElement(f,{style:{fontSize:"18px",marginBottom:"30px"}},"Start with our MVP version and create your first app in minutes."),n.createElement(l.N_,{to:"/mvp"},n.createElement(o.Ay,{type:"primary",size:"large",icon:n.createElement(d.A,null),style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"none",height:"50px",padding:"0 40px",fontSize:"16px"}},"Launch MVP Builder"))))}}}]);