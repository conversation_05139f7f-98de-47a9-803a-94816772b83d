"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[2403],{14884:(e,t,n)=>{n.d(t,{u6:()=>K,vj:()=>R,FA:()=>H,nF:()=>A,Ay:()=>j});var r=n(58168),o=n(60436),l=n(89379),a=n(5544),i=n(53986),u=n(82284),c=n(1397),s=n(3979),d=n(38820),f=n(12533),v=n(68210),p=n(96540);var h=n(7974),m=function(e){return!e||e.disabled||e.disableCheckbox||!1===e.checkable},y=function(e){return null==e},g=n(64467),b=n(82546);const A=function(){return null};var C=["children","value"];function w(e){return(0,b.A)(e).map((function(e){if(!p.isValidElement(e)||!e.type)return null;var t=e,n=t.key,r=t.props,o=r.children,a=r.value,u=(0,i.A)(r,C),c=(0,l.A)({key:n,value:a},u),s=w(o);return s.length&&(c.children=s),c})).filter((function(e){return e}))}function E(e){if(!e)return e;var t=(0,l.A)({},e);return"props"in t||Object.defineProperty(t,"props",{get:function(){return(0,v.Ay)(!1,"New `rc-tree-select` not support return node instance as argument anymore. Please consider to remove `props` access."),t}}),t}function k(e){var t=p.useRef();t.current=e;var n=p.useCallback((function(){return t.current.apply(t,arguments)}),[]);return n}function S(e,t,n){return p.useMemo((function(){if(e){if(n){var r=(0,l.A)({id:"id",pId:"pId",rootPId:null},"object"===(0,u.A)(n)?n:{});return function(e,t){var n=t.id,r=t.pId,o=t.rootPId,a=new Map,i=[];return e.forEach((function(e){var t=e[n],r=(0,l.A)((0,l.A)({},e),{},{key:e.key||t});a.set(t,r)})),a.forEach((function(e){var t=e[r],n=a.get(t);n?(n.children=n.children||[],n.children.push(e)):t!==o&&null!==o||i.push(e)})),i}(e,r)}return e}return w(t)}),[t,n,e])}const x=p.createContext(null);var N=n(24765),D=n(1444),I=n(16928),O=n(28104);const V=p.createContext(null);var M=n(81470),L={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},T=function(e,t){var n=(0,c.Vm)(),l=n.prefixCls,i=n.multiple,u=n.searchValue,s=n.toggleOpen,d=n.open,f=n.notFoundContent,v=p.useContext(V),h=v.virtual,y=v.listHeight,g=v.listItemHeight,b=v.listItemScrollOffset,A=v.treeData,C=v.fieldNames,w=v.onSelect,E=v.dropdownMatchSelectWidth,k=v.treeExpandAction,S=v.treeTitleRender,T=v.onPopupScroll,P=v.leftMaxCount,K=v.leafCountOnly,H=v.valueEntities,R=p.useContext(x),W=R.checkable,_=R.checkedKeys,F=R.halfCheckedKeys,j=R.treeExpandedKeys,U=R.treeDefaultExpandAll,B=R.treeDefaultExpandedKeys,G=R.onTreeExpand,X=R.treeIcon,Y=R.showTreeIcon,q=R.switcherIcon,z=R.treeLine,J=R.treeNodeFilterProp,Q=R.loadData,Z=R.treeLoadedKeys,$=R.treeMotion,ee=R.onTreeLoad,te=R.keyEntities,ne=p.useRef(),re=(0,O.A)((function(){return A}),[d,A],(function(e,t){return t[0]&&e[1]!==t[1]})),oe=p.useMemo((function(){return W?{checked:_,halfChecked:F}:null}),[W,_,F]);p.useEffect((function(){var e;d&&!i&&_.length&&(null===(e=ne.current)||void 0===e||e.scrollTo({key:_[0]}))}),[d]);var le=function(e){e.preventDefault()},ae=function(e,t){var n=t.node;W&&m(n)||(w(n.key,{selected:!_.includes(n.key)}),i||s(!1))},ie=p.useState(B),ue=(0,a.A)(ie,2),ce=ue[0],se=ue[1],de=p.useState(null),fe=(0,a.A)(de,2),ve=fe[0],pe=fe[1],he=p.useMemo((function(){return j?(0,o.A)(j):u?ve:ce}),[ce,ve,j,u]),me=String(u).toLowerCase(),ye=function(e){return!!me&&String(e[J]).toLowerCase().includes(me)};p.useEffect((function(){u&&pe(function(e,t){var n=[];return function e(r){r.forEach((function(r){var o=r[t.children];o&&(n.push(r[t.value]),e(o))}))}(e),n}(A,C))}),[u]);var ge=p.useState((function(){return new Map})),be=(0,a.A)(ge,2),Ae=be[0],Ce=be[1];p.useEffect((function(){P&&Ce(new Map)}),[P]);var we=(0,M._q)((function(e){var t=e[C.value];return!_.includes(t)&&null!==P&&(P<=0||!(!K||!P)&&function(e){var t=e[C.value];if(!Ae.has(t)){var n=H.get(t);if(0===(n.children||[]).length)Ae.set(t,!1);else{var r=n.children.filter((function(e){return!e.node.disabled&&!e.node.disableCheckbox&&!_.includes(e.node[C.value])})).length;Ae.set(t,r>P)}}return Ae.get(t)}(e))})),Ee=function e(t){var n,r=(0,N.A)(t);try{for(r.s();!(n=r.n()).done;){var o=n.value;if(!o.disabled&&!1!==o.selectable){if(!u)return o;if(ye(o))return o;if(o[C.children]){var l=e(o[C.children]);if(l)return l}}}}catch(e){r.e(e)}finally{r.f()}return null},ke=p.useState(null),Se=(0,a.A)(ke,2),xe=Se[0],Ne=Se[1],De=te[xe];p.useEffect((function(){if(d){var e,t;t=i||!_.length||u?(e=Ee(re))?e[C.value]:null:_[0],Ne(t)}}),[d,u]),p.useImperativeHandle(t,(function(){var e;return{scrollTo:null===(e=ne.current)||void 0===e?void 0:e.scrollTo,onKeyDown:function(e){var t;switch(e.which){case I.A.UP:case I.A.DOWN:case I.A.LEFT:case I.A.RIGHT:null===(t=ne.current)||void 0===t||t.onKeyDown(e);break;case I.A.ENTER:if(De){var n=we(De.node),r=(null==De?void 0:De.node)||{},o=r.selectable,l=r.value,a=r.disabled;!1===o||a||n||ae(0,{node:{key:xe},selected:!_.includes(l)})}break;case I.A.ESC:s(!1)}},onKeyUp:function(){}}}));var Ie=(0,O.A)((function(){return!u}),[u,j||ce],(function(e,t){var n=(0,a.A)(e,1)[0],r=(0,a.A)(t,2),o=r[0],l=r[1];return n!==o&&!(!o&&!l)}))?Q:null;if(0===re.length)return p.createElement("div",{role:"listbox",className:"".concat(l,"-empty"),onMouseDown:le},f);var Oe={fieldNames:C};return Z&&(Oe.loadedKeys=Z),he&&(Oe.expandedKeys=he),p.createElement("div",{onMouseDown:le},De&&d&&p.createElement("span",{style:L,"aria-live":"assertive"},De.node.value),p.createElement(D.QB.Provider,{value:{nodeDisabled:we}},p.createElement(D.Ay,(0,r.A)({ref:ne,focusable:!1,prefixCls:"".concat(l,"-tree"),treeData:re,height:y,itemHeight:g,itemScrollOffset:b,virtual:!1!==h&&!1!==E,multiple:i,icon:X,showIcon:Y,switcherIcon:q,showLine:z,loadData:Ie,motion:$,activeKey:xe,checkable:W,checkStrictly:!0,checkedKeys:oe,selectedKeys:W?[]:_,defaultExpandAll:U,titleRender:S},Oe,{onActiveChange:Ne,onSelect:ae,onCheck:ae,onExpand:function(e){se(e),pe(e),G&&G(e)},onLoad:ee,filterTreeNode:ye,expandAction:k,onScroll:T}))))};const P=p.forwardRef(T);var K="SHOW_ALL",H="SHOW_PARENT",R="SHOW_CHILD";function W(e,t,n,r){var o=new Set(e);return t===R?e.filter((function(e){var t=n[e];return!(t&&t.children&&t.children.some((function(e){var t=e.node;return o.has(t[r.value])}))&&t.children.every((function(e){var t=e.node;return m(t)||o.has(t[r.value])})))})):t===H?e.filter((function(e){var t=n[e],r=t?t.parent:null;return!r||m(r.node)||!o.has(r.key)})):e}var _=["id","prefixCls","value","defaultValue","onChange","onSelect","onDeselect","searchValue","inputValue","onSearch","autoClearSearchValue","filterTreeNode","treeNodeFilterProp","showCheckedStrategy","treeNodeLabelProp","multiple","treeCheckable","treeCheckStrictly","labelInValue","maxCount","fieldNames","treeDataSimpleMode","treeData","children","loadData","treeLoadedKeys","onTreeLoad","treeDefaultExpandAll","treeExpandedKeys","treeDefaultExpandedKeys","onTreeExpand","treeExpandAction","virtual","listHeight","listItemHeight","listItemScrollOffset","onDropdownVisibleChange","dropdownMatchSelectWidth","treeLine","treeIcon","showTreeIcon","switcherIcon","treeMotion","treeTitleRender","onPopupScroll"],F=p.forwardRef((function(e,t){var n,m,b=e.id,C=e.prefixCls,w=void 0===C?"rc-tree-select":C,N=e.value,D=e.defaultValue,I=e.onChange,O=e.onSelect,M=e.onDeselect,L=e.searchValue,T=e.inputValue,H=e.onSearch,F=e.autoClearSearchValue,j=void 0===F||F,U=e.filterTreeNode,B=e.treeNodeFilterProp,G=void 0===B?"value":B,X=e.showCheckedStrategy,Y=e.treeNodeLabelProp,q=e.multiple,z=e.treeCheckable,J=e.treeCheckStrictly,Q=e.labelInValue,Z=e.maxCount,$=e.fieldNames,ee=e.treeDataSimpleMode,te=e.treeData,ne=e.children,re=e.loadData,oe=e.treeLoadedKeys,le=e.onTreeLoad,ae=e.treeDefaultExpandAll,ie=e.treeExpandedKeys,ue=e.treeDefaultExpandedKeys,ce=e.onTreeExpand,se=e.treeExpandAction,de=e.virtual,fe=e.listHeight,ve=void 0===fe?200:fe,pe=e.listItemHeight,he=void 0===pe?20:pe,me=e.listItemScrollOffset,ye=void 0===me?0:me,ge=e.onDropdownVisibleChange,be=e.dropdownMatchSelectWidth,Ae=void 0===be||be,Ce=e.treeLine,we=e.treeIcon,Ee=e.showTreeIcon,ke=e.switcherIcon,Se=e.treeMotion,xe=e.treeTitleRender,Ne=e.onPopupScroll,De=(0,i.A)(e,_),Ie=(0,s.Ay)(b),Oe=z&&!J,Ve=z||J,Me=J||Q,Le=Ve||q,Te=(0,f.A)(D,{value:N}),Pe=(0,a.A)(Te,2),Ke=Pe[0],He=Pe[1],Re=p.useMemo((function(){return z?X||R:K}),[X,z]),We=p.useMemo((function(){return function(e){var t=e||{},n=t.label,r=t.value;return{_title:n?[n]:["title","label"],value:r||"value",key:r||"value",children:t.children||"children"}}($)}),[JSON.stringify($)]),_e=(0,f.A)("",{value:void 0!==L?L:T,postState:function(e){return e||""}}),Fe=(0,a.A)(_e,2),je=Fe[0],Ue=Fe[1],Be=S(te,ne,ee),Ge=function(e,t){return p.useMemo((function(){return(0,h.cG)(e,{fieldNames:t,initWrapper:function(e){return(0,l.A)((0,l.A)({},e),{},{valueEntities:new Map})},processEntity:function(e,n){var r=e.node[t.value];n.valueEntities.set(r,e)}})}),[e,t])}(Be,We),Xe=Ge.keyEntities,Ye=Ge.valueEntities,qe=p.useCallback((function(e){var t=[],n=[];return e.forEach((function(e){Ye.has(e)?n.push(e):t.push(e)})),{missingRawValues:t,existRawValues:n}}),[Ye]),ze=function(e,t,n){var r=n.fieldNames,o=n.treeNodeFilterProp,a=n.filterTreeNode,i=r.children;return p.useMemo((function(){if(!t||!1===a)return e;var n="function"==typeof a?a:function(e,n){return String(n[o]).toUpperCase().includes(t.toUpperCase())};return function e(r){var o=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return r.reduce((function(r,a){var u=a[i],c=o||n(t,E(a)),s=e(u||[],c);return(c||s.length)&&r.push((0,l.A)((0,l.A)({},a),{},(0,g.A)({isLeaf:void 0},i,s))),r}),[])}(e)}),[e,t,i,o,a])}(Be,je,{fieldNames:We,treeNodeFilterProp:G,filterTreeNode:U}),Je=p.useCallback((function(e){if(e){if(Y)return e[Y];for(var t=We._title,n=0;n<t.length;n+=1){var r=e[t[n]];if(void 0!==r)return r}}}),[We,Y]),Qe=p.useCallback((function(e){var t=function(e){return Array.isArray(e)?e:void 0!==e?[e]:[]}(e);return t.map((function(e){return function(e){return!e||"object"!==(0,u.A)(e)}(e)?{value:e}:e}))}),[]),Ze=p.useCallback((function(e){return Qe(e).map((function(e){var t,n,r=e.label,o=e.value,l=e.halfChecked,a=Ye.get(o);return a?(r=xe?xe(a.node):null!==(n=r)&&void 0!==n?n:Je(a.node),t=a.node.disabled):void 0===r&&(r=Qe(Ke).find((function(e){return e.value===o})).label),{label:r,value:o,halfChecked:l,disabled:t}}))}),[Ye,Je,Qe,Ke]),$e=p.useMemo((function(){return Qe(null===Ke?[]:Ke)}),[Qe,Ke]),et=p.useMemo((function(){var e=[],t=[];return $e.forEach((function(n){n.halfChecked?t.push(n):e.push(n)})),[e,t]}),[$e]),tt=(0,a.A)(et,2),nt=tt[0],rt=tt[1],ot=p.useMemo((function(){return nt.map((function(e){return e.value}))}),[nt]),lt=function(e,t,n,r){return p.useMemo((function(){var l=function(e){return e.map((function(e){return e.value}))},a=l(e),i=l(t),u=a.filter((function(e){return!r[e]})),c=a,s=i;if(n){var f=(0,d.p)(a,!0,r);c=f.checkedKeys,s=f.halfCheckedKeys}return[Array.from(new Set([].concat((0,o.A)(u),(0,o.A)(c)))),s]}),[e,t,n,r])}(nt,rt,Oe,Xe),at=(0,a.A)(lt,2),it=at[0],ut=at[1],ct=p.useMemo((function(){var e=W(it,Re,Xe,We).map((function(e){var t,n;return null!==(t=null===(n=Xe[e])||void 0===n||null===(n=n.node)||void 0===n?void 0:n[We.value])&&void 0!==t?t:e})).map((function(e){var t=nt.find((function(t){return t.value===e})),n=Q?null==t?void 0:t.label:null==xe?void 0:xe(t);return{value:e,label:n}})),t=Ze(e),n=t[0];return!Le&&n&&y(n.value)&&y(n.label)?[]:t.map((function(e){var t;return(0,l.A)((0,l.A)({},e),{},{label:null!==(t=e.label)&&void 0!==t?t:e.value})}))}),[We,Le,it,nt,Ze,Re,Xe]),st=(n=ct,m=p.useRef({valueLabels:new Map}),p.useMemo((function(){var e=m.current.valueLabels,t=new Map,r=n.map((function(n){var r=n.value,o=n.label,a=null!=o?o:e.get(r);return t.set(r,a),(0,l.A)((0,l.A)({},n),{},{label:a})}));return m.current.valueLabels=t,[r]}),[n])),dt=(0,a.A)(st,1)[0],ft=p.useMemo((function(){return!Le||"SHOW_CHILD"!==Re&&!J&&z?null:Z}),[Z,Le,J,Re,z]),vt=k((function(e,t,n){var r=W(e,Re,Xe,We);if(!(ft&&r.length>ft)){var l=Ze(e);if(He(l),j&&Ue(""),I){var a=e;Oe&&(a=r.map((function(e){var t=Ye.get(e);return t?t.node[We.value]:e})));var i=t||{triggerValue:void 0,selected:void 0},u=i.triggerValue,c=i.selected,s=a;if(J){var d=rt.filter((function(e){return!a.includes(e.value)}));s=[].concat((0,o.A)(s),(0,o.A)(d))}var f=Ze(s),h={preValue:nt,triggerValue:u},m=!0;(J||"selection"===n&&!c)&&(m=!1),function(e,t,n,r,o,l){var a=null,i=null;function u(){i||(i=[],function e(r){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"0",u=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return r.map((function(r,c){var s="".concat(o,"-").concat(c),d=r[l.value],f=n.includes(d),v=e(r[l.children]||[],s,f),h=p.createElement(A,r,v.map((function(e){return e.node})));if(t===d&&(a=h),f){var m={pos:s,node:h,children:v};return u||i.push(m),m}return null})).filter((function(e){return e}))}(r),i.sort((function(e,t){var r=e.node.props.value,o=t.node.props.value;return n.indexOf(r)-n.indexOf(o)})))}Object.defineProperty(e,"triggerNode",{get:function(){return(0,v.Ay)(!1,"`triggerNode` is deprecated. Please consider decoupling data with node."),u(),a}}),Object.defineProperty(e,"allCheckedNodes",{get:function(){return(0,v.Ay)(!1,"`allCheckedNodes` is deprecated. Please consider decoupling data with node."),u(),o?i:i.map((function(e){return e.node}))}})}(h,u,e,Be,m,We),Ve?h.checked=c:h.selected=c;var y=Me?f:f.map((function(e){return e.value}));I(Le?y:y[0],Me?null:f.map((function(e){return e.label})),h)}}})),pt=p.useCallback((function(e,t){var n,r=t.selected,l=t.source,a=Xe[e],i=null==a?void 0:a.node,u=null!==(n=null==i?void 0:i[We.value])&&void 0!==n?n:e;if(Le){var c=r?[].concat((0,o.A)(ot),[u]):it.filter((function(e){return e!==u}));if(Oe){var s,f=qe(c),v=f.missingRawValues,p=f.existRawValues.map((function(e){return Ye.get(e).key}));s=r?(0,d.p)(p,!0,Xe).checkedKeys:(0,d.p)(p,{checked:!1,halfCheckedKeys:ut},Xe).checkedKeys,c=[].concat((0,o.A)(v),(0,o.A)(s.map((function(e){return Xe[e].node[We.value]}))))}vt(c,{selected:r,triggerValue:u},l||"option")}else vt([u],{selected:!0,triggerValue:u},"option");r||!Le?null==O||O(u,E(i)):null==M||M(u,E(i))}),[qe,Ye,Xe,We,Le,ot,vt,Oe,O,M,it,ut,Z]),ht=p.useCallback((function(e){if(ge){var t={};Object.defineProperty(t,"documentClickClose",{get:function(){return(0,v.Ay)(!1,"Second param of `onDropdownVisibleChange` has been removed."),!1}}),ge(e,t)}}),[ge]),mt=k((function(e,t){var n=e.map((function(e){return e.value}));"clear"!==t.type?t.values.length&&pt(t.values[0].value,{selected:!1,source:"selection"}):vt(n,{},"selection")})),yt=p.useMemo((function(){return{virtual:de,dropdownMatchSelectWidth:Ae,listHeight:ve,listItemHeight:he,listItemScrollOffset:ye,treeData:ze,fieldNames:We,onSelect:pt,treeExpandAction:se,treeTitleRender:xe,onPopupScroll:Ne,leftMaxCount:void 0===Z?null:Z-dt.length,leafCountOnly:"SHOW_CHILD"===Re&&!J&&!!z,valueEntities:Ye}}),[de,Ae,ve,he,ye,ze,We,pt,se,xe,Ne,Z,dt.length,Re,J,z,Ye]),gt=p.useMemo((function(){return{checkable:Ve,loadData:re,treeLoadedKeys:oe,onTreeLoad:le,checkedKeys:it,halfCheckedKeys:ut,treeDefaultExpandAll:ae,treeExpandedKeys:ie,treeDefaultExpandedKeys:ue,onTreeExpand:ce,treeIcon:we,treeMotion:Se,showTreeIcon:Ee,switcherIcon:ke,treeLine:Ce,treeNodeFilterProp:G,keyEntities:Xe}}),[Ve,re,oe,le,it,ut,ae,ie,ue,ce,we,Se,Ee,ke,Ce,G,Xe]);return p.createElement(V.Provider,{value:yt},p.createElement(x.Provider,{value:gt},p.createElement(c.g3,(0,r.A)({ref:t},De,{id:Ie,prefixCls:w,mode:Le?"multiple":void 0,displayValues:dt,onDisplayValuesChange:mt,searchValue:je,onSearch:function(e){Ue(e),null==H||H(e)},OptionList:P,emptyOptions:!Be.length,onDropdownVisibleChange:ht,dropdownMatchSelectWidth:Ae}))))}));F.TreeNode=A,F.SHOW_ALL=K,F.SHOW_PARENT=H,F.SHOW_CHILD=R;const j=F},80427:(e,t,n)=>{n.d(t,{z:()=>a,A:()=>g});var r=n(46942),o=n.n(r),l=n(96540);function a(e){var t=e.children,n=e.prefixCls,r=e.id,a=e.overlayInnerStyle,i=e.bodyClassName,u=e.className,c=e.style;return l.createElement("div",{className:o()("".concat(n,"-content"),u),style:c},l.createElement("div",{className:o()("".concat(n,"-inner"),i),id:r,role:"tooltip",style:a},"function"==typeof t?t():t))}var i=n(58168),u=n(89379),c=n(53986),s=n(62427),d={shiftX:64,adjustY:1},f={adjustX:1,shiftY:!0},v=[0,0],p={left:{points:["cr","cl"],overflow:f,offset:[-4,0],targetOffset:v},right:{points:["cl","cr"],overflow:f,offset:[4,0],targetOffset:v},top:{points:["bc","tc"],overflow:d,offset:[0,-4],targetOffset:v},bottom:{points:["tc","bc"],overflow:d,offset:[0,4],targetOffset:v},topLeft:{points:["bl","tl"],overflow:d,offset:[0,-4],targetOffset:v},leftTop:{points:["tr","tl"],overflow:f,offset:[-4,0],targetOffset:v},topRight:{points:["br","tr"],overflow:d,offset:[0,-4],targetOffset:v},rightTop:{points:["tl","tr"],overflow:f,offset:[4,0],targetOffset:v},bottomRight:{points:["tr","br"],overflow:d,offset:[0,4],targetOffset:v},rightBottom:{points:["bl","br"],overflow:f,offset:[4,0],targetOffset:v},bottomLeft:{points:["tl","bl"],overflow:d,offset:[0,4],targetOffset:v},leftBottom:{points:["br","bl"],overflow:f,offset:[-4,0],targetOffset:v}},h=n(56855),m=["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","children","onVisibleChange","afterVisibleChange","transitionName","animation","motion","placement","align","destroyTooltipOnHide","defaultVisible","getTooltipContainer","overlayInnerStyle","arrowContent","overlay","id","showArrow","classNames","styles"],y=function(e,t){var n=e.overlayClassName,r=e.trigger,d=void 0===r?["hover"]:r,f=e.mouseEnterDelay,v=void 0===f?0:f,y=e.mouseLeaveDelay,g=void 0===y?.1:y,b=e.overlayStyle,A=e.prefixCls,C=void 0===A?"rc-tooltip":A,w=e.children,E=e.onVisibleChange,k=e.afterVisibleChange,S=e.transitionName,x=e.animation,N=e.motion,D=e.placement,I=void 0===D?"right":D,O=e.align,V=void 0===O?{}:O,M=e.destroyTooltipOnHide,L=void 0!==M&&M,T=e.defaultVisible,P=e.getTooltipContainer,K=e.overlayInnerStyle,H=(e.arrowContent,e.overlay),R=e.id,W=e.showArrow,_=void 0===W||W,F=e.classNames,j=e.styles,U=(0,c.A)(e,m),B=(0,h.A)(R),G=(0,l.useRef)(null);(0,l.useImperativeHandle)(t,(function(){return G.current}));var X,Y,q,z=(0,u.A)({},U);return"visible"in e&&(z.popupVisible=e.visible),l.createElement(s.A,(0,i.A)({popupClassName:o()(n,null==F?void 0:F.root),prefixCls:C,popup:function(){return l.createElement(a,{key:"content",prefixCls:C,id:B,bodyClassName:null==F?void 0:F.body,overlayInnerStyle:(0,u.A)((0,u.A)({},K),null==j?void 0:j.body)},H)},action:d,builtinPlacements:p,popupPlacement:I,ref:G,popupAlign:V,getPopupContainer:P,onPopupVisibleChange:E,afterPopupVisibleChange:k,popupTransitionName:S,popupAnimation:x,popupMotion:N,defaultPopupVisible:T,autoDestroy:L,mouseLeaveDelay:g,popupStyle:(0,u.A)((0,u.A)({},b),null==j?void 0:j.root),mouseEnterDelay:v,arrow:_},z),(Y=(null==(X=l.Children.only(w))?void 0:X.props)||{},q=(0,u.A)((0,u.A)({},Y),{},{"aria-describedby":H?B:null}),l.cloneElement(w,q)))};const g=(0,l.forwardRef)(y)}}]);