"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[8014],{16370:(e,t,n)=>{n.d(t,{A:()=>o});const o=n(36768).fv},19911:(e,t,n)=>{n.d(t,{Ol:()=>l,kf:()=>i});var o=n(23029),r=n(92901),a=n(71021);const l=(e,t)=>(null==e?void 0:e.replace(/[^\w/]/g,"").slice(0,t?8:6))||"";let i=function(){return(0,r.A)((function e(t){var n;if((0,o.A)(this,e),this.cleared=!1,t instanceof e)return this.metaColor=t.metaColor.clone(),this.colors=null===(n=t.colors)||void 0===n?void 0:n.map((t=>({color:new e(t.color),percent:t.percent}))),void(this.cleared=t.cleared);const r=Array.isArray(t);r&&t.length?(this.colors=t.map((({color:t,percent:n})=>({color:new e(t),percent:n}))),this.metaColor=new a.Q1(this.colors[0].color.metaColor)):this.metaColor=new a.Q1(r?"":t),(!t||r&&!this.colors)&&(this.metaColor=this.metaColor.setA(0),this.cleared=!0)}),[{key:"toHsb",value:function(){return this.metaColor.toHsb()}},{key:"toHsbString",value:function(){return this.metaColor.toHsbString()}},{key:"toHex",value:function(){return e=this.toHexString(),t=this.metaColor.a<1,e?l(e,t):"";var e,t}},{key:"toHexString",value:function(){return this.metaColor.toHexString()}},{key:"toRgb",value:function(){return this.metaColor.toRgb()}},{key:"toRgbString",value:function(){return this.metaColor.toRgbString()}},{key:"isGradient",value:function(){return!!this.colors&&!this.cleared}},{key:"getColors",value:function(){return this.colors||[{color:this,percent:0}]}},{key:"toCssString",value:function(){const{colors:e}=this;return e?`linear-gradient(90deg, ${e.map((e=>`${e.color.toRgbString()} ${e.percent}%`)).join(", ")})`:this.metaColor.toRgbString()}},{key:"equals",value:function(e){return!(!e||this.isGradient()!==e.isGradient())&&(this.isGradient()?this.colors.length===e.colors.length&&this.colors.every(((t,n)=>{const o=e.colors[n];return t.percent===o.percent&&t.color.equals(o.color)})):this.toHexString()===e.toHexString())}}])}()},35307:(e,t,n)=>{n.d(t,{A:()=>Se});var o=n(96540),r=n(46942),a=n.n(r),l=n(12533),i=n(62897),s=n(53425),c=n(58182),d=(n(18877),n(62279)),u=n(98119),g=n(20934),p=n(829),m=n(94241),h=n(28073),b=n(76327),f=n(19911),C=n(36552),$=n(60436),v=n(71021),x=n(30981),S=n(25339);const y=o.createContext({}),O=o.createContext({});var A=n(36058);const w=({prefixCls:e,value:t,onChange:n})=>o.createElement("div",{className:`${e}-clear`,onClick:()=>{if(n&&t&&!t.cleared){const e=t.toHsb();e.a=0;const o=(0,A.Z6)(e);o.cleared=!0,n(o)}}});var k=n(36492);const E="hex",j="rgb",P="hsb";var H=n(7142);const N=({prefixCls:e,min:t=0,max:n=100,value:r,onChange:l,className:i,formatter:s})=>{const c=`${e}-steppers`,[d,u]=(0,o.useState)(0),g=Number.isNaN(r)?d:r;return o.createElement(H.A,{className:a()(c,i),min:t,max:n,value:g,formatter:s,size:"small",onChange:e=>{u(e||0),null==l||l(e)}})},I=({prefixCls:e,value:t,onChange:n})=>{const r=`${e}-alpha-input`,[a,l]=(0,o.useState)((()=>(0,A.Z6)(t||"#000"))),i=t||a;return o.createElement(N,{value:(0,A.Gp)(i),prefixCls:e,formatter:e=>`${e}%`,className:r,onChange:e=>{const t=i.toHsb();t.a=(e||0)/100;const o=(0,A.Z6)(t);l(o),null==n||n(o)}})};var z=n(18017);const R=/(^#[\da-f]{6}$)|(^#[\da-f]{8}$)/i,M=({prefixCls:e,value:t,onChange:n})=>{const r=`${e}-hex-input`,[a,l]=(0,o.useState)((()=>t?(0,f.Ol)(t.toHexString()):void 0));return(0,o.useEffect)((()=>{t&&l((0,f.Ol)(t.toHexString()))}),[t]),o.createElement(z.A,{className:r,value:a,prefix:"#",onChange:e=>{const t=e.target.value;var o;l((0,f.Ol)(t)),o=(0,f.Ol)(t,!0),R.test(`#${o}`)&&(null==n||n((0,A.Z6)(t)))},size:"small"})},B=({prefixCls:e,value:t,onChange:n})=>{const r=`${e}-hsb-input`,[a,l]=(0,o.useState)((()=>(0,A.Z6)(t||"#000"))),i=t||a,s=(e,t)=>{const o=i.toHsb();o[t]="h"===t?e:(e||0)/100;const r=(0,A.Z6)(o);l(r),null==n||n(r)};return o.createElement("div",{className:r},o.createElement(N,{max:360,min:0,value:Number(i.toHsb().h),prefixCls:e,className:r,formatter:e=>(0,A.W)(e||0).toString(),onChange:e=>s(Number(e),"h")}),o.createElement(N,{max:100,min:0,value:100*Number(i.toHsb().s),prefixCls:e,className:r,formatter:e=>`${(0,A.W)(e||0)}%`,onChange:e=>s(Number(e),"s")}),o.createElement(N,{max:100,min:0,value:100*Number(i.toHsb().b),prefixCls:e,className:r,formatter:e=>`${(0,A.W)(e||0)}%`,onChange:e=>s(Number(e),"b")}))},D=({prefixCls:e,value:t,onChange:n})=>{const r=`${e}-rgb-input`,[a,l]=(0,o.useState)((()=>(0,A.Z6)(t||"#000"))),i=t||a,s=(e,t)=>{const o=i.toRgb();o[t]=e||0;const r=(0,A.Z6)(o);l(r),null==n||n(r)};return o.createElement("div",{className:r},o.createElement(N,{max:255,min:0,value:Number(i.toRgb().r),prefixCls:e,className:r,onChange:e=>s(Number(e),"r")}),o.createElement(N,{max:255,min:0,value:Number(i.toRgb().g),prefixCls:e,className:r,onChange:e=>s(Number(e),"g")}),o.createElement(N,{max:255,min:0,value:Number(i.toRgb().b),prefixCls:e,className:r,onChange:e=>s(Number(e),"b")}))},W=[E,P,j].map((e=>({value:e,label:e.toUpperCase()}))),F=e=>{const{prefixCls:t,format:n,value:r,disabledAlpha:a,onFormatChange:i,onChange:s,disabledFormat:c}=e,[d,u]=(0,l.A)(E,{value:n,onChange:i}),g=`${t}-input`,p=(0,o.useMemo)((()=>{const e={value:r,prefixCls:t,onChange:s};switch(d){case P:return o.createElement(B,Object.assign({},e));case j:return o.createElement(D,Object.assign({},e));default:return o.createElement(M,Object.assign({},e))}}),[d,t,r,s]);return o.createElement("div",{className:`${g}-container`},!c&&o.createElement(k.A,{value:d,variant:"borderless",getPopupContainer:e=>e,popupMatchSelectWidth:68,placement:"bottomRight",onChange:e=>{u(e)},className:`${t}-format-select`,size:"small",options:W}),o.createElement("div",{className:g},p),!a&&o.createElement(I,{prefixCls:t,value:r,onChange:s}))};var G=n(55168),q=n(26956),T=n(6531),X=n(87534);const Z=e=>{const{prefixCls:t,colors:n,type:r,color:l,range:i=!1,className:s,activeIndex:c,onActive:d,onDragStart:u,onDragChange:g,onKeyDelete:p}=e,m=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["prefixCls","colors","type","color","range","className","activeIndex","onActive","onDragStart","onDragChange","onKeyDelete"]),h=Object.assign(Object.assign({},m),{track:!1}),b=o.useMemo((()=>`linear-gradient(90deg, ${n.map((e=>`${e.color} ${e.percent}%`)).join(", ")})`),[n]),f=o.useMemo((()=>l&&r?"alpha"===r?l.toRgbString():`hsl(${l.toHsb().h}, 100%, 50%)`:null),[l,r]),C=(0,q.A)(u),$=(0,q.A)(g),v=o.useMemo((()=>({onDragStart:C,onDragChange:$})),[]),x=(0,q.A)(((e,l)=>{const{onFocus:i,style:s,className:u,onKeyDown:g}=e.props,m=Object.assign({},s);return"gradient"===r&&(m.background=(0,A.PU)(n,l.value)),o.cloneElement(e,{onFocus:e=>{null==d||d(l.index),null==i||i(e)},style:m,className:a()(u,{[`${t}-slider-handle-active`]:c===l.index}),onKeyDown:e=>{"Delete"!==e.key&&"Backspace"!==e.key||!p||p(l.index),null==g||g(e)}})})),S=o.useMemo((()=>({direction:"ltr",handleRender:x})),[]);return o.createElement(X.A.Provider,{value:S},o.createElement(G.Q.Provider,{value:v},o.createElement(T.A,Object.assign({},h,{className:a()(s,`${t}-slider`),tooltip:{open:!1},range:{editable:i,minCount:2},styles:{rail:{background:b},handle:f?{background:f}:{}},classNames:{rail:`${t}-slider-rail`,handle:`${t}-slider-handle`}}))))};function L(e){return(0,$.A)(e).sort(((e,t)=>e.percent-t.percent))}const Q=e=>{const{prefixCls:t,mode:n,onChange:r,onChangeComplete:a,onActive:l,activeIndex:i,onGradientDragging:s,colors:c}=e,d="gradient"===n,u=o.useMemo((()=>c.map((e=>({percent:e.percent,color:e.color.toRgbString()})))),[c]),g=o.useMemo((()=>u.map((e=>e.percent))),[u]),p=o.useRef(u);return d?o.createElement(Z,{min:0,max:100,prefixCls:t,className:`${t}-gradient-slider`,colors:u,color:null,value:g,range:!0,onChangeComplete:e=>{a(new f.kf(u)),i>=e.length&&l(e.length-1),s(!1)},disabled:!1,type:"gradient",activeIndex:i,onActive:l,onDragStart:({rawValues:e,draggingIndex:t,draggingValue:n})=>{if(e.length>u.length){const e=(0,A.PU)(u,n),o=(0,$.A)(u);o.splice(t,0,{percent:n,color:e}),p.current=o}else p.current=u;s(!0),r(new f.kf(L(p.current)),!0)},onDragChange:({deleteIndex:e,draggingIndex:t,draggingValue:n})=>{let o=(0,$.A)(p.current);-1!==e?o.splice(e,1):(o[t]=Object.assign(Object.assign({},o[t]),{percent:n}),o=L(o)),r(new f.kf(o),!0)},onKeyDelete:e=>{const t=(0,$.A)(u);t.splice(e,1);const n=new f.kf(t);r(n),a(n)}}):null},K=o.memo(Q);const U={slider:e=>{const{value:t,onChange:n,onChangeComplete:r}=e;return o.createElement(Z,Object.assign({},e,{value:[t],onChange:e=>n(e[0]),onChangeComplete:e=>r(e[0])}))}},V=()=>{const e=(0,o.useContext)(y),{mode:t,onModeChange:n,modeOptions:r,prefixCls:a,allowClear:l,value:i,disabledAlpha:s,onChange:c,onClear:d,onChangeComplete:u,activeIndex:g,gradientDragging:p}=e,m=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["mode","onModeChange","modeOptions","prefixCls","allowClear","value","disabledAlpha","onChange","onClear","onChangeComplete","activeIndex","gradientDragging"]),h=o.useMemo((()=>i.cleared?[{percent:0,color:new f.kf("")},{percent:100,color:new f.kf("")}]:i.getColors()),[i]),b=!i.isGradient(),[C,O]=o.useState(i);(0,x.A)((()=>{var e;b||O(null===(e=h[g])||void 0===e?void 0:e.color)}),[p,g]);const k=o.useMemo((()=>{var e;return b?i:p?C:null===(e=h[g])||void 0===e?void 0:e.color}),[i,g,b,C,p]),[E,j]=o.useState(k),[P,H]=o.useState(0),N=(null==E?void 0:E.equals(k))?k:E;(0,x.A)((()=>{j(k)}),[P,null==k?void 0:k.toHexString()]);const I=(e,n)=>{let o=(0,A.Z6)(e);if(i.cleared){const e=o.toRgb();if(e.r||e.g||e.b||!n)o=(0,A.E)(o);else{const{type:e,value:t=0}=n;o=new f.kf({h:"hue"===e?t:0,s:1,b:1,a:"alpha"===e?t/100:1})}}if("single"===t)return o;const r=(0,$.A)(h);return r[g]=Object.assign(Object.assign({},r[g]),{color:o}),new f.kf(r)};let z=null;const R=r.length>1;return(l||R)&&(z=o.createElement("div",{className:`${a}-operation`},R&&o.createElement(S.A,{size:"small",options:r,value:t,onChange:n}),o.createElement(w,Object.assign({prefixCls:a,value:i,onChange:e=>{c(e),null==d||d()}},m)))),o.createElement(o.Fragment,null,z,o.createElement(K,Object.assign({},e,{colors:h})),o.createElement(v.Ay,{prefixCls:a,value:null==N?void 0:N.toHsb(),disabledAlpha:s,onChange:(e,t)=>{((e,t,n)=>{const o=I(e,n);j(o.isGradient()?o.getColors()[g].color:o),c(o,!0)})(e,0,t)},onChangeComplete:(e,t)=>{((e,t)=>{u(I(e,t)),H((e=>e+1))})(e,t)},components:U}),o.createElement(F,Object.assign({value:k,onChange:e=>{c(I(e))},prefixCls:a,disabledAlpha:s},m)))};var Y=n(53596);const _=()=>{const{prefixCls:e,value:t,presets:n,onChange:r}=(0,o.useContext)(O);return Array.isArray(n)?o.createElement(Y.A,{value:t,presets:n,prefixCls:e,onChange:r}):null},J=e=>{const{prefixCls:t,presets:n,panelRender:r,value:a,onChange:l,onClear:i,allowClear:s,disabledAlpha:c,mode:d,onModeChange:u,modeOptions:g,onChangeComplete:p,activeIndex:m,onActive:h,format:b,onFormatChange:f,gradientDragging:$,onGradientDragging:v,disabledFormat:x}=e,S=`${t}-inner`,A=o.useMemo((()=>({prefixCls:t,value:a,onChange:l,onClear:i,allowClear:s,disabledAlpha:c,mode:d,onModeChange:u,modeOptions:g,onChangeComplete:p,activeIndex:m,onActive:h,format:b,onFormatChange:f,gradientDragging:$,onGradientDragging:v,disabledFormat:x})),[t,a,l,i,s,c,d,u,g,p,m,h,b,f,$,v,x]),w=o.useMemo((()=>({prefixCls:t,value:a,presets:n,onChange:l})),[t,a,n,l]),k=o.createElement("div",{className:`${S}-content`},o.createElement(V,null),Array.isArray(n)&&o.createElement(C.A,null),o.createElement(_,null));return o.createElement(y.Provider,{value:A},o.createElement(O.Provider,{value:w},o.createElement("div",{className:S},"function"==typeof r?r(k,{components:{Picker:V,Presets:_}}):k)))};var ee=n(72065),te=n(21282);const ne=(0,o.forwardRef)(((e,t)=>{const{color:n,prefixCls:r,open:l,disabled:i,format:s,className:c,showText:d,activeIndex:u}=e,g=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["color","prefixCls","open","disabled","format","className","showText","activeIndex"]),p=`${r}-trigger`,m=`${p}-text`,h=`${m}-cell`,[b]=(0,te.Ym)("ColorPicker"),f=o.useMemo((()=>{if(!d)return"";if("function"==typeof d)return d(n);if(n.cleared)return b.transparent;if(n.isGradient())return n.getColors().map(((e,t)=>{const n=-1!==u&&u!==t;return o.createElement("span",{key:t,className:a()(h,n&&`${h}-inactive`)},e.color.toRgbString()," ",e.percent,"%")}));const e=n.toHexString().toUpperCase(),t=(0,A.Gp)(n);switch(s){case"rgb":return n.toRgbString();case"hsb":return n.toHsbString();default:return t<100?`${e.slice(0,7)},${t}%`:e}}),[n,s,d,u]),C=(0,o.useMemo)((()=>n.cleared?o.createElement(w,{prefixCls:r}):o.createElement(v.ZC,{prefixCls:r,color:n.toCssString()})),[n,r]);return o.createElement("div",Object.assign({ref:t,className:a()(p,c,{[`${p}-active`]:l,[`${p}-disabled`]:i})},(0,ee.A)(g)),C,d&&o.createElement("div",{className:m},f))})),oe=ne;var re=n(36891),ae=n(55974),le=n(51113);const ie=(e,t)=>({backgroundImage:`conic-gradient(${t} 25%, transparent 25% 50%, ${t} 50% 75%, transparent 75% 100%)`,backgroundSize:`${e} ${e}`}),se=(e,t)=>{const{componentCls:n,borderRadiusSM:o,colorPickerInsetShadow:r,lineWidth:a,colorFillSecondary:l}=e;return{[`${n}-color-block`]:Object.assign(Object.assign({position:"relative",borderRadius:o,width:t,height:t,boxShadow:r,flex:"none"},ie("50%",e.colorFillSecondary)),{[`${n}-color-block-inner`]:{width:"100%",height:"100%",boxShadow:`inset 0 0 0 ${(0,re.zA)(a)} ${l}`,borderRadius:"inherit"}})}},ce=e=>{const{componentCls:t,antCls:n,fontSizeSM:o,lineHeightSM:r,colorPickerAlphaInputWidth:a,marginXXS:l,paddingXXS:i,controlHeightSM:s,marginXS:c,fontSizeIcon:d,paddingXS:u,colorTextPlaceholder:g,colorPickerInputNumberHandleWidth:p,lineWidth:m}=e;return{[`${t}-input-container`]:{display:"flex",[`${t}-steppers${n}-input-number`]:{fontSize:o,lineHeight:r,[`${n}-input-number-input`]:{paddingInlineStart:i,paddingInlineEnd:0},[`${n}-input-number-handler-wrap`]:{width:p}},[`${t}-steppers${t}-alpha-input`]:{flex:`0 0 ${(0,re.zA)(a)}`,marginInlineStart:l},[`${t}-format-select${n}-select`]:{marginInlineEnd:c,width:"auto","&-single":{[`${n}-select-selector`]:{padding:0,border:0},[`${n}-select-arrow`]:{insetInlineEnd:0},[`${n}-select-selection-item`]:{paddingInlineEnd:e.calc(d).add(l).equal(),fontSize:o,lineHeight:(0,re.zA)(s)},[`${n}-select-item-option-content`]:{fontSize:o,lineHeight:r},[`${n}-select-dropdown`]:{[`${n}-select-item`]:{minHeight:"auto"}}}},[`${t}-input`]:{gap:l,alignItems:"center",flex:1,width:0,[`${t}-hsb-input,${t}-rgb-input`]:{display:"flex",gap:l,alignItems:"center"},[`${t}-steppers`]:{flex:1},[`${t}-hex-input${n}-input-affix-wrapper`]:{flex:1,padding:`0 ${(0,re.zA)(u)}`,[`${n}-input`]:{fontSize:o,textTransform:"uppercase",lineHeight:(0,re.zA)(e.calc(s).sub(e.calc(m).mul(2)).equal())},[`${n}-input-prefix`]:{color:g}}}}}},de=e=>{const{componentCls:t,controlHeightLG:n,borderRadiusSM:o,colorPickerInsetShadow:r,marginSM:a,colorBgElevated:l,colorFillSecondary:i,lineWidthBold:s,colorPickerHandlerSize:c}=e;return{userSelect:"none",[`${t}-select`]:{[`${t}-palette`]:{minHeight:e.calc(n).mul(4).equal(),overflow:"hidden",borderRadius:o},[`${t}-saturation`]:{position:"absolute",borderRadius:"inherit",boxShadow:r,inset:0},marginBottom:a},[`${t}-handler`]:{width:c,height:c,border:`${(0,re.zA)(s)} solid ${l}`,position:"relative",borderRadius:"50%",cursor:"pointer",boxShadow:`${r}, 0 0 0 1px ${i}`}}},ue=e=>{const{componentCls:t,antCls:n,colorTextQuaternary:o,paddingXXS:r,colorPickerPresetColorSize:a,fontSizeSM:l,colorText:i,lineHeightSM:s,lineWidth:c,borderRadius:d,colorFill:u,colorWhite:g,marginXXS:p,paddingXS:m,fontHeightSM:h}=e;return{[`${t}-presets`]:{[`${n}-collapse-item > ${n}-collapse-header`]:{padding:0,[`${n}-collapse-expand-icon`]:{height:h,color:o,paddingInlineEnd:r}},[`${n}-collapse`]:{display:"flex",flexDirection:"column",gap:p},[`${n}-collapse-item > ${n}-collapse-content > ${n}-collapse-content-box`]:{padding:`${(0,re.zA)(m)} 0`},"&-label":{fontSize:l,color:i,lineHeight:s},"&-items":{display:"flex",flexWrap:"wrap",gap:e.calc(p).mul(1.5).equal(),[`${t}-presets-color`]:{position:"relative",cursor:"pointer",width:a,height:a,"&::before":{content:'""',pointerEvents:"none",width:e.calc(a).add(e.calc(c).mul(4)).equal(),height:e.calc(a).add(e.calc(c).mul(4)).equal(),position:"absolute",top:e.calc(c).mul(-2).equal(),insetInlineStart:e.calc(c).mul(-2).equal(),borderRadius:d,border:`${(0,re.zA)(c)} solid transparent`,transition:`border-color ${e.motionDurationMid} ${e.motionEaseInBack}`},"&:hover::before":{borderColor:u},"&::after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"21.5%",display:"table",width:e.calc(a).div(13).mul(5).equal(),height:e.calc(a).div(13).mul(8).equal(),border:`${(0,re.zA)(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`},[`&${t}-presets-color-checked`]:{"&::after":{opacity:1,borderColor:g,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`transform ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`},[`&${t}-presets-color-bright`]:{"&::after":{borderColor:"rgba(0, 0, 0, 0.45)"}}}}},"&-empty":{fontSize:l,color:o}}}},ge=e=>{const{componentCls:t,colorPickerInsetShadow:n,colorBgElevated:o,colorFillSecondary:r,lineWidthBold:a,colorPickerHandlerSizeSM:l,colorPickerSliderHeight:i,marginSM:s,marginXS:c}=e,d=e.calc(l).sub(e.calc(a).mul(2).equal()).equal(),u=e.calc(l).add(e.calc(a).mul(2).equal()).equal(),g={"&:after":{transform:"scale(1)",boxShadow:`${n}, 0 0 0 1px ${e.colorPrimaryActive}`}};return{[`${t}-slider`]:[ie((0,re.zA)(i),e.colorFillSecondary),{margin:0,padding:0,height:i,borderRadius:e.calc(i).div(2).equal(),"&-rail":{height:i,borderRadius:e.calc(i).div(2).equal(),boxShadow:n},[`& ${t}-slider-handle`]:{width:d,height:d,top:0,borderRadius:"100%","&:before":{display:"block",position:"absolute",background:"transparent",left:{_skip_check_:!0,value:"50%"},top:"50%",transform:"translate(-50%, -50%)",width:u,height:u,borderRadius:"100%"},"&:after":{width:l,height:l,border:`${(0,re.zA)(a)} solid ${o}`,boxShadow:`${n}, 0 0 0 1px ${r}`,outline:"none",insetInlineStart:e.calc(a).mul(-1).equal(),top:e.calc(a).mul(-1).equal(),background:"transparent",transition:"none"},"&:focus":g}}],[`${t}-slider-container`]:{display:"flex",gap:s,marginBottom:s,[`${t}-slider-group`]:{flex:1,flexDirection:"column",justifyContent:"space-between",display:"flex","&-disabled-alpha":{justifyContent:"center"}}},[`${t}-gradient-slider`]:{marginBottom:c,[`& ${t}-slider-handle`]:{"&:after":{transform:"scale(0.8)"},"&-active, &:focus":g}}}},pe=(e,t,n)=>({borderInlineEndWidth:e.lineWidth,borderColor:t,boxShadow:`0 0 0 ${(0,re.zA)(e.controlOutlineWidth)} ${n}`,outline:0}),me=e=>{const{componentCls:t}=e;return{"&-rtl":{[`${t}-presets-color`]:{"&::after":{direction:"ltr"}},[`${t}-clear`]:{"&::after":{direction:"ltr"}}}}},he=(e,t,n)=>{const{componentCls:o,borderRadiusSM:r,lineWidth:a,colorSplit:l,colorBorder:i,red6:s}=e;return{[`${o}-clear`]:Object.assign(Object.assign({width:t,height:t,borderRadius:r,border:`${(0,re.zA)(a)} solid ${l}`,position:"relative",overflow:"hidden",cursor:"inherit",transition:`all ${e.motionDurationFast}`},n),{"&::after":{content:'""',position:"absolute",insetInlineEnd:e.calc(a).mul(-1).equal(),top:e.calc(a).mul(-1).equal(),display:"block",width:40,height:2,transformOrigin:"calc(100% - 1px) 1px",transform:"rotate(-45deg)",backgroundColor:s},"&:hover":{borderColor:i}})}},be=e=>{const{componentCls:t,colorError:n,colorWarning:o,colorErrorHover:r,colorWarningHover:a,colorErrorOutline:l,colorWarningOutline:i}=e;return{[`&${t}-status-error`]:{borderColor:n,"&:hover":{borderColor:r},[`&${t}-trigger-active`]:Object.assign({},pe(e,n,l))},[`&${t}-status-warning`]:{borderColor:o,"&:hover":{borderColor:a},[`&${t}-trigger-active`]:Object.assign({},pe(e,o,i))}}},fe=e=>{const{componentCls:t,controlHeightLG:n,controlHeightSM:o,controlHeight:r,controlHeightXS:a,borderRadius:l,borderRadiusSM:i,borderRadiusXS:s,borderRadiusLG:c,fontSizeLG:d}=e;return{[`&${t}-lg`]:{minWidth:n,minHeight:n,borderRadius:c,[`${t}-color-block, ${t}-clear`]:{width:r,height:r,borderRadius:l},[`${t}-trigger-text`]:{fontSize:d}},[`&${t}-sm`]:{minWidth:o,minHeight:o,borderRadius:i,[`${t}-color-block, ${t}-clear`]:{width:a,height:a,borderRadius:s},[`${t}-trigger-text`]:{lineHeight:(0,re.zA)(a)}}}},Ce=e=>{const{antCls:t,componentCls:n,colorPickerWidth:o,colorPrimary:r,motionDurationMid:a,colorBgElevated:l,colorTextDisabled:i,colorText:s,colorBgContainerDisabled:c,borderRadius:d,marginXS:u,marginSM:g,controlHeight:p,controlHeightSM:m,colorBgTextActive:h,colorPickerPresetColorSize:b,colorPickerPreviewSize:f,lineWidth:C,colorBorder:$,paddingXXS:v,fontSize:x,colorPrimaryHover:S,controlOutline:y}=e;return[{[n]:Object.assign({[`${n}-inner`]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({"&-content":{display:"flex",flexDirection:"column",width:o,[`& > ${t}-divider`]:{margin:`${(0,re.zA)(g)} 0 ${(0,re.zA)(u)}`}},[`${n}-panel`]:Object.assign({},de(e))},ge(e)),se(e,f)),ce(e)),ue(e)),he(e,b,{marginInlineStart:"auto"})),{[`${n}-operation`]:{display:"flex",justifyContent:"space-between",marginBottom:u}}),"&-trigger":Object.assign(Object.assign(Object.assign(Object.assign({minWidth:p,minHeight:p,borderRadius:d,border:`${(0,re.zA)(C)} solid ${$}`,cursor:"pointer",display:"inline-flex",alignItems:"flex-start",justifyContent:"center",transition:`all ${a}`,background:l,padding:e.calc(v).sub(C).equal(),[`${n}-trigger-text`]:{marginInlineStart:u,marginInlineEnd:e.calc(u).sub(e.calc(v).sub(C)).equal(),fontSize:x,color:s,alignSelf:"center","&-cell":{"&:not(:last-child):after":{content:'", "'},"&-inactive":{color:i}}},"&:hover":{borderColor:S},[`&${n}-trigger-active`]:Object.assign({},pe(e,r,y)),"&-disabled":{color:i,background:c,cursor:"not-allowed","&:hover":{borderColor:h},[`${n}-trigger-text`]:{color:i}}},he(e,m)),se(e,m)),be(e)),fe(e))},me(e))},(0,ae.G)(e,{focusElCls:`${n}-trigger-active`})]},$e=(0,le.OF)("ColorPicker",(e=>{const{colorTextQuaternary:t,marginSM:n}=e,o=(0,le.oX)(e,{colorPickerWidth:234,colorPickerHandlerSize:16,colorPickerHandlerSizeSM:12,colorPickerAlphaInputWidth:44,colorPickerInputNumberHandleWidth:16,colorPickerPresetColorSize:24,colorPickerInsetShadow:`inset 0 0 1px 0 ${t}`,colorPickerSliderHeight:8,colorPickerPreviewSize:e.calc(8).mul(2).add(n).equal()});return[Ce(o)]}));const ve=e=>{const{mode:t,value:n,defaultValue:r,format:s,defaultFormat:C,allowClear:$=!1,presets:v,children:x,trigger:S="click",open:y,disabled:O,placement:w="bottomLeft",arrow:k=!0,panelRender:E,showText:j,style:P,className:H,size:N,rootClassName:I,prefixCls:z,styles:R,disabledAlpha:M=!1,onFormatChange:B,onChange:D,onClear:W,onOpenChange:F,onChangeComplete:G,getPopupContainer:T,autoAdjustOverflow:X=!0,destroyTooltipOnHide:Z,destroyOnHidden:L,disabledFormat:Q}=e,K=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["mode","value","defaultValue","format","defaultFormat","allowClear","presets","children","trigger","open","disabled","placement","arrow","panelRender","showText","style","className","size","rootClassName","prefixCls","styles","disabledAlpha","onFormatChange","onChange","onClear","onOpenChange","onChangeComplete","getPopupContainer","autoAdjustOverflow","destroyTooltipOnHide","destroyOnHidden","disabledFormat"]),{getPrefixCls:U,direction:V,colorPicker:Y}=(0,o.useContext)(d.QO),_=(0,o.useContext)(u.A),ee=null!=O?O:_,[ne,re]=(0,l.A)(!1,{value:y,postState:e=>!ee&&e,onChange:F}),[ae,le]=(0,l.A)(s,{value:s,defaultValue:C,onChange:B}),ie=U("color-picker",z),[se,ce,de,ue,ge]=function(e,t,n){const[r]=(0,te.Ym)("ColorPicker"),[a,i]=(0,l.A)(e,{value:t}),[s,c]=o.useState("single"),[d,u]=o.useMemo((()=>{const e=(Array.isArray(n)?n:[n]).filter((e=>e));e.length||e.push("single");const t=new Set(e),o=[],a=(e,n)=>{t.has(e)&&o.push({label:n,value:e})};return a("single",r.singleColor),a("gradient",r.gradientColor),[o,t]}),[n]),[g,p]=o.useState(null),m=(0,q.A)((e=>{p(e),i(e)})),h=o.useMemo((()=>{const e=(0,A.Z6)(a||"");return e.equals(g)?g:e}),[a,g]),b=o.useMemo((()=>{var e;return u.has(s)?s:null===(e=d[0])||void 0===e?void 0:e.value}),[u,s,d]);return o.useEffect((()=>{c(h.isGradient()?"gradient":"single")}),[h]),[h,m,b,c,d]}(r,n,t),pe=(0,o.useMemo)((()=>(0,A.Gp)(se)<100),[se]),[me,he]=o.useState(null),be=e=>{if(G){let t=(0,A.Z6)(e);M&&pe&&(t=(0,A.E)(e)),G(t)}},fe=(e,t)=>{let n=(0,A.Z6)(e);M&&pe&&(n=(0,A.E)(n)),ce(n),he(null),D&&D(n,n.toCssString()),t||be(n)},[Ce,ve]=o.useState(0),[xe,Se]=o.useState(!1),{status:ye}=o.useContext(m.$W),{compactSize:Oe,compactItemClassnames:Ae}=(0,b.RQ)(ie,V),we=(0,p.A)((e=>{var t;return null!==(t=null!=N?N:Oe)&&void 0!==t?t:e})),ke=(0,g.A)(ie),[Ee,je,Pe]=$e(ie,ke),He={[`${ie}-rtl`]:V},Ne=a()(I,Pe,ke,He),Ie=a()((0,c.L)(ie,ye),{[`${ie}-sm`]:"small"===we,[`${ie}-lg`]:"large"===we},Ae,null==Y?void 0:Y.className,Ne,H,je),ze=a()(ie,Ne),Re={open:ne,trigger:S,placement:w,arrow:k,rootClassName:I,getPopupContainer:T,autoAdjustOverflow:X,destroyOnHidden:null!=L?L:!!Z},Me=Object.assign(Object.assign({},null==Y?void 0:Y.style),P);return Ee(o.createElement(h.A,Object.assign({style:null==R?void 0:R.popup,styles:{body:null==R?void 0:R.popupOverlayInner},onOpenChange:e=>{e&&ee||re(e)},content:o.createElement(i.A,{form:!0},o.createElement(J,{mode:de,onModeChange:e=>{if(ue(e),"single"===e&&se.isGradient())ve(0),fe(new f.kf(se.getColors()[0].color)),he(se);else if("gradient"===e&&!se.isGradient()){const e=pe?(0,A.E)(se):se;fe(new f.kf(me||[{percent:0,color:e},{percent:100,color:e}]))}},modeOptions:ge,prefixCls:ie,value:se,allowClear:$,disabled:ee,disabledAlpha:M,presets:v,panelRender:E,format:ae,onFormatChange:le,onChange:fe,onChangeComplete:be,onClear:W,activeIndex:Ce,onActive:ve,gradientDragging:xe,onGradientDragging:Se,disabledFormat:Q})),classNames:{root:ze}},Re),x||o.createElement(oe,Object.assign({activeIndex:ne?Ce:-1,open:ne,className:Ie,style:Me,prefixCls:ie,disabled:ee,showText:j,format:ae},K,{color:se}))))},xe=(0,s.A)(ve,void 0,(e=>Object.assign(Object.assign({},e),{placement:"bottom",autoAdjustOverflow:!1})),"color-picker",(e=>e));ve._InternalPanelDoNotUseOrYouWillBeFired=xe;const Se=ve},36058:(e,t,n)=>{n.d(t,{E:()=>c,Gp:()=>s,PU:()=>d,W:()=>i,Z6:()=>l});var o=n(60436),r=n(71021),a=n(19911);const l=e=>e instanceof a.kf?e:new a.kf(e),i=e=>Math.round(Number(e||0)),s=e=>i(100*e.toHsb().a),c=(e,t)=>{const n=e.toRgb();if(!n.r&&!n.g&&!n.b){const n=e.toHsb();return n.a=t||1,l(n)}return n.a=t||1,l(n)},d=(e,t)=>{const n=[{percent:0,color:e[0].color}].concat((0,o.A)(e),[{percent:100,color:e[e.length-1].color}]);for(let e=0;e<n.length-1;e+=1){const o=n[e].percent,a=n[e+1].percent,l=n[e].color,i=n[e+1].color;if(o<=t&&t<=a){const e=a-o;if(0===e)return l;const n=(t-o)/e*100,s=new r.Q1(l),c=new r.Q1(i);return s.mix(c,n).toRgbString()}}return""}},39356:(e,t,n)=>{n.d(t,{A:()=>w});var o=n(96540),r=n(14588),a=n(46942),l=n.n(a),i=n(71057),s=n(82546),c=n(19853),d=n(23723),u=n(40682),g=(n(18877),n(62279)),p=n(829),m=n(38674);const h=o.forwardRef(((e,t)=>{const{getPrefixCls:n}=o.useContext(m.QO),{prefixCls:r,className:a,showArrow:s=!0}=e,c=n("collapse",r),d=l()({[`${c}-no-arrow`]:!s},a);return o.createElement(i.A.Panel,Object.assign({ref:t},e,{prefixCls:c,className:d}))}));var b=n(36891),f=n(25905),C=n(38328),$=n(51113);const v=e=>{const{componentCls:t,contentBg:n,padding:o,headerBg:r,headerPadding:a,collapseHeaderPaddingSM:l,collapseHeaderPaddingLG:i,collapsePanelBorderRadius:s,lineWidth:c,lineType:d,colorBorder:u,colorText:g,colorTextHeading:p,colorTextDisabled:m,fontSizeLG:h,lineHeight:C,lineHeightLG:$,marginSM:v,paddingSM:x,paddingLG:S,paddingXS:y,motionDurationSlow:O,fontSizeIcon:A,contentPadding:w,fontHeight:k,fontHeightLG:E}=e,j=`${(0,b.zA)(c)} ${d} ${u}`;return{[t]:Object.assign(Object.assign({},(0,f.dF)(e)),{backgroundColor:r,border:j,borderRadius:s,"&-rtl":{direction:"rtl"},[`& > ${t}-item`]:{borderBottom:j,"&:first-child":{[`\n            &,\n            & > ${t}-header`]:{borderRadius:`${(0,b.zA)(s)} ${(0,b.zA)(s)} 0 0`}},"&:last-child":{[`\n            &,\n            & > ${t}-header`]:{borderRadius:`0 0 ${(0,b.zA)(s)} ${(0,b.zA)(s)}`}},[`> ${t}-header`]:Object.assign(Object.assign({position:"relative",display:"flex",flexWrap:"nowrap",alignItems:"flex-start",padding:a,color:p,lineHeight:C,cursor:"pointer",transition:`all ${O}, visibility 0s`},(0,f.K8)(e)),{[`> ${t}-header-text`]:{flex:"auto"},[`${t}-expand-icon`]:{height:k,display:"flex",alignItems:"center",paddingInlineEnd:v},[`${t}-arrow`]:Object.assign(Object.assign({},(0,f.Nk)()),{fontSize:A,transition:`transform ${O}`,svg:{transition:`transform ${O}`}}),[`${t}-header-text`]:{marginInlineEnd:"auto"}}),[`${t}-collapsible-header`]:{cursor:"default",[`${t}-header-text`]:{flex:"none",cursor:"pointer"}},[`${t}-collapsible-icon`]:{cursor:"unset",[`${t}-expand-icon`]:{cursor:"pointer"}}},[`${t}-content`]:{color:g,backgroundColor:n,borderTop:j,[`& > ${t}-content-box`]:{padding:w},"&-hidden":{display:"none"}},"&-small":{[`> ${t}-item`]:{[`> ${t}-header`]:{padding:l,paddingInlineStart:y,[`> ${t}-expand-icon`]:{marginInlineStart:e.calc(x).sub(y).equal()}},[`> ${t}-content > ${t}-content-box`]:{padding:x}}},"&-large":{[`> ${t}-item`]:{fontSize:h,lineHeight:$,[`> ${t}-header`]:{padding:i,paddingInlineStart:o,[`> ${t}-expand-icon`]:{height:E,marginInlineStart:e.calc(S).sub(o).equal()}},[`> ${t}-content > ${t}-content-box`]:{padding:S}}},[`${t}-item:last-child`]:{borderBottom:0,[`> ${t}-content`]:{borderRadius:`0 0 ${(0,b.zA)(s)} ${(0,b.zA)(s)}`}},[`& ${t}-item-disabled > ${t}-header`]:{"\n          &,\n          & > .arrow\n        ":{color:m,cursor:"not-allowed"}},[`&${t}-icon-position-end`]:{[`& > ${t}-item`]:{[`> ${t}-header`]:{[`${t}-expand-icon`]:{order:1,paddingInlineEnd:0,paddingInlineStart:v}}}}})}},x=e=>{const{componentCls:t}=e,n=`> ${t}-item > ${t}-header ${t}-arrow`;return{[`${t}-rtl`]:{[n]:{transform:"rotate(180deg)"}}}},S=e=>{const{componentCls:t,headerBg:n,borderlessContentPadding:o,borderlessContentBg:r,colorBorder:a}=e;return{[`${t}-borderless`]:{backgroundColor:n,border:0,[`> ${t}-item`]:{borderBottom:`1px solid ${a}`},[`\n        > ${t}-item:last-child,\n        > ${t}-item:last-child ${t}-header\n      `]:{borderRadius:0},[`> ${t}-item:last-child`]:{borderBottom:0},[`> ${t}-item > ${t}-content`]:{backgroundColor:r,borderTop:0},[`> ${t}-item > ${t}-content > ${t}-content-box`]:{padding:o}}}},y=e=>{const{componentCls:t,paddingSM:n}=e;return{[`${t}-ghost`]:{backgroundColor:"transparent",border:0,[`> ${t}-item`]:{borderBottom:0,[`> ${t}-content`]:{backgroundColor:"transparent",border:0,[`> ${t}-content-box`]:{paddingBlock:n}}}}}},O=(0,$.OF)("Collapse",(e=>{const t=(0,$.oX)(e,{collapseHeaderPaddingSM:`${(0,b.zA)(e.paddingXS)} ${(0,b.zA)(e.paddingSM)}`,collapseHeaderPaddingLG:`${(0,b.zA)(e.padding)} ${(0,b.zA)(e.paddingLG)}`,collapsePanelBorderRadius:e.borderRadiusLG});return[v(t),S(t),y(t),x(t),(0,C.eG)(t)]}),(e=>({headerPadding:`${e.paddingSM}px ${e.padding}px`,headerBg:e.colorFillAlter,contentPadding:`${e.padding}px 16px`,contentBg:e.colorBgContainer,borderlessContentPadding:`${e.paddingXXS}px 16px ${e.padding}px`,borderlessContentBg:"transparent"}))),A=o.forwardRef(((e,t)=>{const{getPrefixCls:n,direction:a,expandIcon:m,className:h,style:b}=(0,g.TP)("collapse"),{prefixCls:f,className:C,rootClassName:$,style:v,bordered:x=!0,ghost:S,size:y,expandIconPosition:A="start",children:w,destroyInactivePanel:k,destroyOnHidden:E,expandIcon:j}=e,P=(0,p.A)((e=>{var t;return null!==(t=null!=y?y:e)&&void 0!==t?t:"middle"})),H=n("collapse",f),N=n(),[I,z,R]=O(H),M=o.useMemo((()=>"left"===A?"start":"right"===A?"end":A),[A]),B=null!=j?j:m,D=o.useCallback(((e={})=>{const t="function"==typeof B?B(e):o.createElement(r.A,{rotate:e.isActive?"rtl"===a?-90:90:void 0,"aria-label":e.isActive?"expanded":"collapsed"});return(0,u.Ob)(t,(()=>{var e;return{className:l()(null===(e=null==t?void 0:t.props)||void 0===e?void 0:e.className,`${H}-arrow`)}}))}),[B,H]),W=l()(`${H}-icon-position-${M}`,{[`${H}-borderless`]:!x,[`${H}-rtl`]:"rtl"===a,[`${H}-ghost`]:!!S,[`${H}-${P}`]:"middle"!==P},h,C,$,z,R),F=Object.assign(Object.assign({},(0,d.A)(N)),{motionAppear:!1,leavedClassName:`${H}-content-hidden`}),G=o.useMemo((()=>w?(0,s.A)(w).map(((e,t)=>{var n,o;const r=e.props;if(null==r?void 0:r.disabled){const a=null!==(n=e.key)&&void 0!==n?n:String(t),l=Object.assign(Object.assign({},(0,c.A)(e.props,["disabled"])),{key:a,collapsible:null!==(o=r.collapsible)&&void 0!==o?o:"disabled"});return(0,u.Ob)(e,l)}return e})):null),[w]);return I(o.createElement(i.A,Object.assign({ref:t,openMotion:F},(0,c.A)(e,["rootClassName"]),{expandIcon:D,prefixCls:H,className:W,style:Object.assign(Object.assign({},b),v),destroyInactivePanel:null!=E?E:k}),G))})),w=Object.assign(A,{Panel:h})},53596:(e,t,n)=>{n.d(t,{A:()=>h,z:()=>p});var o=n(96540),r=n(71021),a=n(46942),l=n.n(a),i=n(12533),s=n(39356),c=n(21282),d=n(51113),u=n(36058);const g=e=>e.map((e=>(e.colors=e.colors.map(u.Z6),e))),p=(e,t)=>{const{r:n,g:o,b:a,a:l}=e.toRgb(),i=new r.Q1(e.toRgbString()).onBackground(t).toHsv();return l<=.5?i.v>.5:.299*n+.587*o+.114*a>192},m=(e,t)=>{var n;return`panel-${null!==(n=e.key)&&void 0!==n?n:t}`},h=({prefixCls:e,presets:t,value:n,onChange:a})=>{const[h]=(0,c.Ym)("ColorPicker"),[,b]=(0,d.rd)(),[f]=(0,i.A)(g(t),{value:g(t),postState:g}),C=`${e}-presets`,$=(0,o.useMemo)((()=>f.reduce(((e,t,n)=>{const{defaultOpen:o=!0}=t;return o&&e.push(m(t,n)),e}),[])),[f]),v=f.map(((t,i)=>{var s;return{key:m(t,i),label:o.createElement("div",{className:`${C}-label`},null==t?void 0:t.label),children:o.createElement("div",{className:`${C}-items`},Array.isArray(null==t?void 0:t.colors)&&(null===(s=t.colors)||void 0===s?void 0:s.length)>0?t.colors.map(((t,i)=>o.createElement(r.ZC,{key:`preset-${i}-${t.toHexString()}`,color:(0,u.Z6)(t).toRgbString(),prefixCls:e,className:l()(`${C}-color`,{[`${C}-color-checked`]:t.toHexString()===(null==n?void 0:n.toHexString()),[`${C}-color-bright`]:p(t,b.colorBgElevated)}),onClick:()=>{null==a||a(t)}}))):o.createElement("span",{className:`${C}-empty`},h.presetEmpty))}}));return o.createElement("div",{className:C},o.createElement(s.A,{defaultActiveKey:$,ghost:!0,items:v}))}}}]);