/**
 * Resource Hints Tester
 * 
 * This utility tests resource hints implementation, including:
 * - Preload, prefetch, and preconnect directives
 * - Intelligent prefetching
 */

// Configuration
const CONFIG = {
  // Whether to enable detailed logging
  debug: true,
  // Expected resource hints
  expectedHints: {
    preload: [
      { as: 'script', pattern: /\.js$/ },
      { as: 'style', pattern: /\.css$/ },
      { as: 'font', pattern: /\.(woff2?|ttf|eot|otf)$/ },
    ],
    prefetch: [
      { as: 'document', pattern: /\// },
    ],
    preconnect: [
      { href: 'https://fonts.googleapis.com' },
      { href: 'https://fonts.gstatic.com' },
    ],
    'dns-prefetch': [
      { href: 'https://fonts.googleapis.com' },
    ],
  },
};

// Valid 'as' values according to MDN and spec
const VALID_AS_VALUES = {
  audio: ['.mp3', '.wav', '.ogg'],
  document: ['.html', '.htm', '/'],
  embed: ['.pdf'],
  fetch: ['*'],  // Generic requests
  font: ['.woff', '.woff2', '.ttf', '.otf', '.eot'],
  image: ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.ico', '.avif'],
  object: ['*'],
  script: ['.js', '.mjs', '.jsx'],
  style: ['.css'],
  track: ['.vtt', '.srt'],
  video: ['.mp4', '.webm', '.ogv'],
  worker: ['.js', '.mjs']
};

// Helper to determine appropriate 'as' value based on file extension
function determineAsValue(url) {
  const extension = url.split('?')[0].toLowerCase().match(/\.[^.]+$|\/$/)?.[0];

  if (!extension) return null;

  for (const [asValue, extensions] of Object.entries(VALID_AS_VALUES)) {
    if (extensions.includes('*') || extensions.includes(extension)) {
      return asValue;
    }
  }

  return null;
}

// Validate preload directive
function validatePreload(link) {
  const href = link.getAttribute('href');
  const asValue = link.getAttribute('as');

  if (!href || !asValue) {
    console.error(`Preload missing required attributes: ${link.outerHTML}`);
    return false;
  }

  const expectedAsValue = determineAsValue(href);
  if (!expectedAsValue) {
    console.warn(`Could not determine appropriate 'as' value for: ${href}`);
    return true; // Don't fail validation but warn
  }

  if (expectedAsValue !== asValue) {
    console.error(`Incorrect 'as' value for ${href}. Expected '${expectedAsValue}', got '${asValue}'`);
    return false;
  }

  // Validate additional attributes based on 'as' value
  if (asValue === 'font') {
    if (!link.hasAttribute('crossorigin')) {
      console.error(`Font preload missing crossorigin attribute: ${href}`);
      return false;
    }
  }

  return true;
}

// Test results
const testResults = {
  resourceHints: {
    preload: {
      valid: 0,
      invalid: 0,
      details: []
    }
  },
  prefetching: {},
};

/**
 * Initialize the resource hints tester
 */
export function initResourceHintsTester() {
  if (typeof window === 'undefined') return;

  // Test resource hints
  testResourceHints();

  // Test intelligent prefetching
  testIntelligentPrefetching();

  // Log results
  logResults();
}

/**
 * Test resource hints
 */
function testResourceHints() {
  // Get all link elements
  const links = Array.from(document.getElementsByTagName('link'));

  // Group by rel attribute
  const hintsByType = {};

  links.forEach(link => {
    const rel = link.rel;

    if (!hintsByType[rel]) {
      hintsByType[rel] = [];
    }

    hintsByType[rel].push({
      href: link.href,
      as: link.as,
      crossOrigin: link.crossOrigin,
      media: link.media,
    });
  });

  // Check expected hints
  const expectedTypes = Object.keys(CONFIG.expectedHints);

  expectedTypes.forEach(type => {
    const expectedHints = CONFIG.expectedHints[type];
    const actualHints = hintsByType[type] || [];

    testResults.resourceHints[type] = {
      count: actualHints.length,
      expected: expectedHints.length,
      items: actualHints,
      missing: [],
    };

    // Check for missing hints
    expectedHints.forEach(expected => {
      if (expected.href) {
        // Check for exact href match
        const found = actualHints.some(actual => actual.href.includes(expected.href));

        if (!found) {
          testResults.resourceHints[type].missing.push(expected);
        }
      } else if (expected.pattern) {
        // Check for pattern match
        const found = actualHints.some(actual => expected.pattern.test(actual.href));

        if (!found) {
          testResults.resourceHints[type].missing.push(expected);
        }
      }
    });
  });

  if (CONFIG.debug) {
    console.log('Resource hints:', testResults.resourceHints);
  }
}

/**
 * Test intelligent prefetching
 */
function testIntelligentPrefetching() {
  // Check if the prefetching utility is loaded
  const prefetchingLoaded = typeof window.initPrefetcher !== 'undefined' ||
    typeof window.prefetch !== 'undefined';

  testResults.prefetching = {
    utilityLoaded: prefetchingLoaded,
  };

  // Monitor prefetch link additions
  const originalCreateElement = document.createElement;
  document.createElement = function (tagName) {
    const element = originalCreateElement.call(document, tagName);

    if (tagName.toLowerCase() === 'link') {
      console.log('Resource hint added:', {
        rel: element.rel,
        href: element.href,
        as: element.as,
        time: Date.now()
      });
    }

    return element;
  };

  // Trigger some user interactions to test prefetching
  setTimeout(() => {
    // Simulate mouse movement
    document.dispatchEvent(new MouseEvent('mousemove', {
      bubbles: true,
      cancelable: true,
      view: window,
    }));

    // Find a link to hover over
    const links = document.querySelectorAll('a[href]');
    if (links.length > 0) {
      links[0].dispatchEvent(new MouseEvent('mouseover', {
        bubbles: true,
        cancelable: true,
        view: window,
      }));
    }

    // Check results after a delay
    setTimeout(() => {
      testResults.prefetching.addedLinks = addedPrefetchLinks;
      testResults.prefetching.working = addedPrefetchLinks.length > 0;

      // Restore original createElement
      document.createElement = originalCreateElement;

      if (CONFIG.debug) {
        console.log('Prefetching test results:', testResults.prefetching);
      }

      // Update the logged results
      logResults();
    }, 1000);
  }, 1000);
}

/**
 * Log test results
 */
function logResults() {
  console.group('Resource Hints Test Results');

  // Log resource hints
  console.log('Resource Hints:');
  Object.entries(testResults.resourceHints).forEach(([type, result]) => {
    console.log(`  ${type}: ${result.count}/${result.expected} ${result.count >= result.expected ? '✅' : '❌'}`);

    if (result.missing.length > 0) {
      console.log('  Missing:');
      result.missing.forEach(missing => {
        if (missing.href) {
          console.log(`    - ${missing.href}`);
        } else if (missing.pattern) {
          console.log(`    - ${missing.pattern}`);
        }
      });
    }
  });

  // Log prefetching
  console.log('\nIntelligent Prefetching:');
  console.log(`  Utility Loaded: ${testResults.prefetching.utilityLoaded ? '✅' : '❌'}`);

  if (testResults.prefetching.utilityLoaded) {
    if (testResults.prefetching.addedLinks) {
      console.log(`  Working: ${testResults.prefetching.working ? '✅' : '❌'}`);
      console.log(`  Added Links: ${testResults.prefetching.addedLinks.length}`);

      if (CONFIG.debug && testResults.prefetching.addedLinks.length > 0) {
        console.log('  Links:');
        testResults.prefetching.addedLinks.forEach(link => {
          console.log(`    - ${link.href} (${link.as || 'document'})`);
        });
      }
    } else {
      console.log('  Still testing...');
    }
  }

  console.groupEnd();
}

/**
 * Get the resource hints test results
 * @returns {Object} - Test results
 */
export function getResourceHintsTestResults() {
  return { ...testResults };
}

export default {
  initResourceHintsTester,
  getResourceHintsTestResults,
};

// Additional validation rules and mime types
const MIME_TYPES = {
  script: ['application/javascript', 'module', 'text/javascript'],
  style: ['text/css'],
  font: ['font/woff2', 'font/woff', 'font/ttf', 'application/font-woff2'],
  image: ['image/jpeg', 'image/png', 'image/svg+xml', 'image/webp', 'image/avif'],
  audio: ['audio/mpeg', 'audio/ogg'],
  video: ['video/mp4', 'video/webm']
};

// Priority hints mapping
const PRIORITY_HINTS = {
  high: ['style', 'script', 'font'],
  low: ['image', 'audio', 'video'],
  auto: ['fetch', 'object']
};

class PreloadValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
  }

  validateType(link) {
    const asValue = link.getAttribute('as');
    const type = link.getAttribute('type');

    if (type && MIME_TYPES[asValue]) {
      if (!MIME_TYPES[asValue].includes(type)) {
        this.errors.push(`Invalid type '${type}' for as='${asValue}' in: ${link.outerHTML}`);
        return false;
      }
    }
    return true;
  }

  validatePriority(link) {
    const asValue = link.getAttribute('as');
    const importance = link.getAttribute('importance');

    if (importance) {
      const validPriorities = Object.keys(PRIORITY_HINTS);
      if (!validPriorities.includes(importance)) {
        this.errors.push(`Invalid importance value '${importance}' in: ${link.outerHTML}`);
        return false;
      }

      // Check if priority makes sense for resource type
      const resourceCategory = Object.entries(PRIORITY_HINTS)
        .find(([_, types]) => types.includes(asValue))?.[0];

      if (resourceCategory && importance !== resourceCategory) {
        this.warnings.push(
          `Unusual importance='${importance}' for as='${asValue}'. Consider '${resourceCategory}' instead.`
        );
      }
    }
    return true;
  }

  validateMediaPreload(link) {
    const asValue = link.getAttribute('as');
    const media = link.getAttribute('media');

    if (['video', 'audio'].includes(asValue)) {
      if (!link.hasAttribute('preload')) {
        this.warnings.push(`Missing preload attribute for media resource: ${link.outerHTML}`);
      }
    }

    if (media) {
      try {
        window.matchMedia(media);
      } catch (e) {
        this.errors.push(`Invalid media query '${media}' in: ${link.outerHTML}`);
        return false;
      }
    }
    return true;
  }

  validateFetchPriority(link) {
    const fetchPriority = link.getAttribute('fetchpriority');
    if (fetchPriority && !['high', 'low', 'auto'].includes(fetchPriority)) {
      this.errors.push(`Invalid fetchpriority '${fetchPriority}' in: ${link.outerHTML}`);
      return false;
    }
    return true;
  }

  validatePreloadOrder(links) {
    const criticalResources = links.filter(link =>
      ['style', 'script', 'font'].includes(link.getAttribute('as'))
    );

    const nonCriticalResources = links.filter(link =>
      ['image', 'video', 'audio'].includes(link.getAttribute('as'))
    );

    // Check if any non-critical resources appear before critical ones
    const allLinks = Array.from(document.querySelectorAll('link[rel="preload"]'));
    const firstNonCriticalIndex = allLinks.findIndex(link =>
      nonCriticalResources.includes(link)
    );
    const lastCriticalIndex = allLinks.findIndex(link =>
      criticalResources.includes(link)
    );

    if (firstNonCriticalIndex !== -1 && lastCriticalIndex > firstNonCriticalIndex) {
      this.warnings.push('Non-critical resources are preloaded before critical resources');
    }
  }

  generateReport() {
    return {
      valid: this.errors.length === 0,
      errors: this.errors,
      warnings: this.warnings,
      summary: {
        errorCount: this.errors.length,
        warningCount: this.warnings.length,
        status: this.errors.length === 0 ? 'PASS' : 'FAIL'
      }
    };
  }
}

// Enhanced test function
function testPreloads() {
  const validator = new PreloadValidator();
  const preloads = document.querySelectorAll('link[rel="preload"]');
  const preloadArray = Array.from(preloads);

  preloadArray.forEach(link => {
    validatePreload(link);
    validator.validateType(link);
    validator.validatePriority(link);
    validator.validateMediaPreload(link);
    validator.validateFetchPriority(link);
  });

  validator.validatePreloadOrder(preloadArray);

  return {
    ...testResults,
    validationReport: validator.generateReport()
  };
}

// Export for use in build scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testPreloads,
    determineAsValue,
    VALID_AS_VALUES,
    MIME_TYPES,
    PRIORITY_HINTS
  };
}

