"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[1934],{91934:(e,t,n)=>{n.r(t),n.d(t,{default:()=>h});var a=n(60436),r=n(64467),l=n(5544),c=n(96540),s=n(1807),o=n(35346),i=n(71468);const m={colors:{primary:{main:"#2563EB",light:"#DBEAFE",dark:"#1E40AF",contrastText:"#FFFFFF"},secondary:{main:"#10B981",light:"#D1FAE5",dark:"#047857",contrastText:"#FFFFFF"},background:{default:"#F9FAFB",paper:"#FFFFFF",secondary:"#f0f2f5"},text:{primary:"#111827",secondary:"#4B5563",disabled:"#9CA3AF"},error:{main:"#DC2626",light:"#FEE2E2",dark:"#B91C1C"},warning:{main:"#FBBF24",light:"#FEF3C7",dark:"#D97706"},success:{main:"#10B981",light:"#D1FAE5",dark:"#047857"}},spacing:{xs:"0.5rem",sm:"1rem",md:"1.5rem",lg:"2rem",xl:"2.5rem"},typography:{fontFamily:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",fontWeights:{light:300,regular:400,medium:500,semibold:600,bold:700},sizes:{xs:"0.75rem",sm:"0.875rem",base:"1rem",lg:"1.125rem",xl:"1.25rem"}},borderRadius:{sm:"0.25rem",md:"0.375rem",lg:"0.5rem"},shadows:{sm:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1)"},breakpoints:{xs:"0px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px"}},u=(0,c.createContext)(m);var d=n(85331),p=n(17053),E=n(66894),g=n(38812);function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=s.tU.TabPane;const h=function(){var e,t=(0,c.useContext)(u)||m,n=(0,i.wA)(),r=(0,c.useState)(!0),y=(0,l.A)(r,2),h=y[0],b=y[1],A=(0,c.useState)(p.A.getConnectionState?p.A.getConnectionState():{status:"disconnected"}),S=(0,l.A)(A,2),x=S[0],k=S[1],C=(0,c.useState)({sent:0,received:0,errors:0,latency:[]}),M=(0,l.A)(C,2),F=M[0],O=M[1],w=(0,c.useState)({cpu:30*Math.random()+10,memory:40*Math.random()+20,disk:20*Math.random()+5,uptime:Math.floor(1e3*Math.random())+100}),B=(0,l.A)(w,2),j=B[0],D=B[1],I=(0,c.useState)("1"),z=(0,l.A)(I,2),L=z[0],P=z[1],T={backgroundColor:t.colors.background.default,padding:t.spacing.md},R={backgroundColor:t.colors.background.paper,borderRadius:t.borderRadius.md,boxShadow:t.shadows.sm};(0,c.useEffect)((function(){n((0,d.tI)("dashboard"));var e=setTimeout((function(){b(!1)}),1500);return function(){return clearTimeout(e)}}),[n]);var W=function(){k(p.A.getConnectionState?p.A.getConnectionState():{status:"disconnected"})},U=function(){k(p.A.getConnectionState?p.A.getConnectionState():{status:"disconnected"})},K=function(){k(p.A.getConnectionState?p.A.getConnectionState():{status:"disconnected"}),O((function(e){return f(f({},e),{},{errors:e.errors+1})}))},J=function(e){O((function(t){return f(f({},t),{},{received:t.received+1,latency:[].concat((0,a.A)(t.latency.slice(-50)),[{time:(new Date).toISOString(),value:e.latency||0}])})}))},Q=function(){O((function(e){return f(f({},e),{},{sent:e.sent+1})}))};(0,c.useEffect)((function(){p.A.addListener("connect",W),p.A.addListener("disconnect",U),p.A.addListener("error",K),p.A.addListener("message",J),p.A.addListener("sent",Q),k(p.A.getConnectionState?p.A.getConnectionState():{status:"disconnected"});var e=setInterval((function(){D((function(e){return{cpu:Math.min(Math.max(e.cpu+10*(Math.random()-.5),0),100),memory:Math.min(Math.max(e.memory+10*(Math.random()-.5),0),100),disk:Math.min(Math.max(e.disk+5*(Math.random()-.5),0),100),uptime:e.uptime+1}}))}),5e3);return function(){p.A.off("connect",W),p.A.off("disconnect",U),p.A.off("error",K),p.A.off("message",J),p.A.off("sent",Q),clearInterval(e)}}),[]);var N,$,H,V,Y=function(e){return e<50?"success":e<80?"warning":"exception"};return c.createElement(E.LN,{style:T},h?c.createElement(g.O2,null):c.createElement("div",null,c.createElement(E.p1,{level:2,style:{color:t.colors.text.primary,fontSize:t.typography.sizes.xl,fontWeight:t.typography.fontWeights.semibold}},"Dashboard"),c.createElement(s.Zp,{style:R},c.createElement(s.tU,{activeKey:L,onChange:P},c.createElement(v,{tab:c.createElement("span",null,c.createElement(o.zpd,null),"Overview"),key:"1"},c.createElement(E.JT,{columns:3,columnsMd:2,columnsSm:1},c.createElement(E.pK,{title:"WebSocket Status"},c.createElement(E.n5,{direction:"column",gap:16},c.createElement("div",{style:{fontSize:16}},x.connected?c.createElement(s.Ex,{status:"success",text:"Connected"}):0===x.readyState?c.createElement(s.Ex,{status:"processing",text:"Connecting"}):c.createElement(s.Ex,{status:"error",text:"Disconnected"})),c.createElement("div",null,c.createElement("div",null,"URL: ",x.url||"Not connected"),c.createElement("div",null,"Reconnect Attempts: ",x.reconnectAttempts||0)),c.createElement("div",null,c.createElement(E.jn,{onClick:function(){return p.A.reconnect()},disabled:x.connected},x.connected?"Connected":"Reconnect")))),c.createElement(E.pK,{title:"Message Statistics"},c.createElement(E.n5,{direction:"column",gap:16},c.createElement(E.n5,{justify:"space-between"},c.createElement(s.jL,{title:"Sent",value:F.sent,valueStyle:{color:"#3f8600"},prefix:c.createElement(o.lu9,null)}),c.createElement(s.jL,{title:"Received",value:F.received,valueStyle:{color:"#1890ff"},prefix:c.createElement(o.Axk,null)})),c.createElement(s.jL,{title:"Errors",value:F.errors,valueStyle:{color:F.errors>0?"#cf1322":"#3f8600"},prefix:F.errors>0?c.createElement(o.v7y,null):c.createElement(o.hWy,null)}))),c.createElement(E.pK,{title:"System Health"},c.createElement(E.n5,{direction:"column",gap:8},c.createElement("div",null,c.createElement("div",{style:{marginBottom:4}},"CPU Usage"),c.createElement(s.ke,{percent:Math.round(j.cpu),status:Y(j.cpu),size:"small"})),c.createElement("div",null,c.createElement("div",{style:{marginBottom:4}},"Memory Usage"),c.createElement(s.ke,{percent:Math.round(j.memory),status:Y(j.memory),size:"small"})),c.createElement("div",null,c.createElement("div",{style:{marginBottom:4}},"Disk Usage"),c.createElement(s.ke,{percent:Math.round(j.disk),status:Y(j.disk),size:"small"})),c.createElement("div",{style:{marginTop:8}},c.createElement(s.jL,{title:"Uptime",value:(N=j.uptime,$=Math.floor(N/1440),H=Math.floor(N%1440/60),V=N%60,$>0?"".concat($,"d ").concat(H,"h ").concat(V,"m"):H>0?"".concat(H,"h ").concat(V,"m"):"".concat(V,"m")),prefix:c.createElement(o.L8Y,null)})))),c.createElement(E.pK,{title:"Latency",extra:c.createElement(s.$n,{type:"text",icon:c.createElement(o.BdS,null),onClick:function(){p.A.send({type:"ping",timestamp:Date.now()})},disabled:!x.connected},"Ping"),style:{gridColumn:"span 2"}},c.createElement("div",{style:{height:200,display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",gap:"10px"}},F.latency.length>0?c.createElement(c.Fragment,null,c.createElement("div",null,"Latest latency: ",(null===(e=F.latency[F.latency.length-1])||void 0===e?void 0:e.value)||0," ms"),c.createElement("div",null,"Average latency: ",F.latency.length>0?Math.round(F.latency.reduce((function(e,t){return e+t.value}),0)/F.latency.length):0," ms"),c.createElement("div",null,"Samples: ",F.latency.length)):c.createElement("span",null,'No latency data available. Click "Ping" to measure latency.'))),c.createElement(E.pK,{title:"Recent Activity"},c.createElement(s.B8,{size:"small",dataSource:[{title:"WebSocket Connected",time:"2 minutes ago",type:"success"},{title:"Message Received",time:"5 minutes ago",type:"info"},{title:"Configuration Updated",time:"10 minutes ago",type:"warning"},{title:"Application Started",time:"1 hour ago",type:"success"}],renderItem:function(e){return c.createElement(s.B8.Item,null,c.createElement(s.B8.Item.Meta,{title:c.createElement(E.n5,{justify:"space-between"},c.createElement("span",null,e.title),c.createElement(s.vw,{color:"success"===e.type?"success":"warning"===e.type?"warning":"error"===e.type?"error":"blue"},e.time))}))}}))),x.lastError&&c.createElement(s.Fc,{type:"error",message:"WebSocket Error",description:c.createElement("div",null,c.createElement("div",null,x.lastError.message),x.lastError.timestamp&&c.createElement("div",{style:{color:"rgba(0, 0, 0, 0.45)",marginTop:8}},new Date(x.lastError.timestamp).toLocaleString())),showIcon:!0,style:{marginTop:24}})),c.createElement(v,{tab:c.createElement("span",null,c.createElement(o.bfv,null),"API Status"),key:"2"},c.createElement(E.JT,{columns:1},c.createElement(E.pK,{title:"API Endpoints"},c.createElement(s.B8,{size:"large",dataSource:[{name:"WebSocket API",status:x.connected?"online":"offline",latency:F.latency.length>0?Math.round(F.latency.reduce((function(e,t){return e+t.value}),0)/F.latency.length):null,url:x.url||"ws://localhost:8000/ws"},{name:"REST API",status:"online",latency:45,url:"http://localhost:8000/api"},{name:"Authentication Service",status:"online",latency:78,url:"http://localhost:8000/auth"},{name:"Storage Service",status:"online",latency:112,url:"http://localhost:8000/storage"}],renderItem:function(e){return c.createElement(s.B8.Item,{actions:[c.createElement(s.$n,{key:"test",size:"small"},"Test"),c.createElement(s.$n,{key:"details",size:"small"},"Details")]},c.createElement(s.B8.Item.Meta,{title:c.createElement(E.n5,{justify:"space-between"},c.createElement("span",null,e.name),c.createElement(s.Ex,{status:"online"===e.status?"success":"error",text:e.status})),description:c.createElement(E.n5,{direction:"column",gap:4},c.createElement("div",null,e.url),e.latency&&c.createElement("div",null,"Latency: ",c.createElement("span",{style:{color:e.latency<100?"#3f8600":e.latency<300?"#faad14":"#cf1322"}},e.latency," ms")))}))}})))),c.createElement(v,{tab:c.createElement("span",null,c.createElement(o.JO7,null),"Configuration"),key:"3"},c.createElement(E.JT,{columns:2,columnsSm:1},c.createElement(E.pK,{title:"WebSocket Configuration"},c.createElement(s.B8,{size:"small",dataSource:[{name:"Reconnect Attempts",value:p.A.maxReconnectAttempts},{name:"Message Queue Size",value:p.A.messageQueueMaxSize},{name:"Compression",value:p.A.performanceOptions.compression.enabled?"Enabled":"Disabled"},{name:"Compression Threshold",value:"".concat(p.A.performanceOptions.compression.threshold," bytes")},{name:"Batching",value:p.A.performanceOptions.batchingEnabled?"Enabled":"Disabled"},{name:"Offline Queue",value:p.A.performanceOptions.offlineQueueEnabled?"Enabled":"Disabled"}],renderItem:function(e){return c.createElement(s.B8.Item,null,c.createElement(s.B8.Item.Meta,{title:c.createElement(E.n5,{justify:"space-between"},c.createElement("span",null,e.name),c.createElement("span",{style:{fontWeight:"normal"}},e.value))}))}}),c.createElement("div",{style:{marginTop:16}},c.createElement(E.jn,null,"Edit Configuration"))),c.createElement(E.pK,{title:"Security Configuration"},c.createElement(s.B8,{size:"small",dataSource:[{name:"Message Validation",value:p.A.securityOptions.validateMessages?"Enabled":"Disabled"},{name:"Message Sanitization",value:p.A.securityOptions.sanitizeMessages?"Enabled":"Disabled"},{name:"Rate Limiting",value:p.A.securityOptions.rateLimiting.enabled?"Enabled":"Disabled"},{name:"Max Messages Per Second",value:p.A.securityOptions.rateLimiting.maxMessagesPerSecond},{name:"Burst Size",value:p.A.securityOptions.rateLimiting.burstSize},{name:"Authentication",value:p.A.authToken?"Enabled":"Disabled"}],renderItem:function(e){return c.createElement(s.B8.Item,null,c.createElement(s.B8.Item.Meta,{title:c.createElement(E.n5,{justify:"space-between"},c.createElement("span",null,e.name),c.createElement("span",{style:{fontWeight:"normal"}},e.value))}))}}),c.createElement("div",{style:{marginTop:16}},c.createElement(E.jn,null,"Edit Security Settings")))))))))}}}]);