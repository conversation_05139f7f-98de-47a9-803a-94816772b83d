module.exports = {
  presets: [
    ['@babel/preset-env', {
      targets: {
        browsers: ['last 2 versions', 'not dead', 'not op_mini all'],
        node: 'current'
      },
      useBuiltIns: 'usage',
      corejs: 3
    }],
    ['@babel/preset-react', {
      runtime: 'automatic',
      development: process.env.NODE_ENV === 'development',
      throwIfNamespace: false
    }]
  ],
  plugins: [
    '@babel/plugin-transform-runtime',
    '@babel/plugin-proposal-class-properties',
    '@babel/plugin-proposal-object-rest-spread',
    // Ant Design tree-shaking optimization
    ['import', {
      libraryName: 'antd',
      libraryDirectory: 'es',
      style: true
    }, 'antd'],
    // Ant Design icons optimization
    ['import', {
      libraryName: '@ant-design/icons',
      libraryDirectory: 'es/icons',
      camel2DashComponentName: false
    }, 'ant-design-icons']
  ]
};
