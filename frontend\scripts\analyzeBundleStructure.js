const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

/**
 * Bundle Structure Analyzer
 * Analyzes the webpack build output to identify large chunks and optimization opportunities
 */

const BUNDLE_SIZE_LIMIT = 244 * 1024; // 244 KB target
const BUILD_DIR = path.join(__dirname, '../build/static/js');

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeJSFiles() {
  if (!fs.existsSync(BUILD_DIR)) {
    console.error(chalk.red('Build directory not found. Run "npm run build" first.'));
    return;
  }

  const files = fs.readdirSync(BUILD_DIR)
    .filter(file => file.endsWith('.js') && !file.endsWith('.LICENSE.txt'))
    .map(file => {
      const filePath = path.join(BUILD_DIR, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        size: stats.size,
        path: filePath
      };
    })
    .sort((a, b) => b.size - a.size);

  console.log(chalk.blue.bold('\n📊 Bundle Structure Analysis\n'));
  console.log(chalk.yellow(`Target: ${formatBytes(BUNDLE_SIZE_LIMIT)}`));
  console.log(chalk.yellow(`Current Total: ${formatBytes(files.reduce((sum, file) => sum + file.size, 0))}\n`));

  // Categorize files
  const categories = {
    main: files.filter(f => f.name.includes('main')),
    antd: files.filter(f => f.name.includes('antd')),
    vendors: files.filter(f => f.name.includes('vendors')),
    react: files.filter(f => f.name.includes('react')),
    common: files.filter(f => f.name.includes('common')),
    runtime: files.filter(f => f.name.includes('runtime')),
    chunks: files.filter(f => /^\d+\./.test(f.name))
  };

  // Display analysis by category
  Object.entries(categories).forEach(([category, categoryFiles]) => {
    if (categoryFiles.length === 0) return;
    
    const totalSize = categoryFiles.reduce((sum, file) => sum + file.size, 0);
    const isOverLimit = totalSize > BUNDLE_SIZE_LIMIT;
    
    console.log(chalk.bold(`\n${category.toUpperCase()} (${formatBytes(totalSize)}):`));
    
    categoryFiles.slice(0, 5).forEach(file => {
      const sizeColor = file.size > BUNDLE_SIZE_LIMIT ? chalk.red : 
                       file.size > BUNDLE_SIZE_LIMIT * 0.5 ? chalk.yellow : chalk.green;
      console.log(`  ${sizeColor(formatBytes(file.size).padEnd(10))} ${file.name}`);
    });
    
    if (categoryFiles.length > 5) {
      console.log(`  ... and ${categoryFiles.length - 5} more files`);
    }
  });

  // Identify optimization opportunities
  console.log(chalk.blue.bold('\n🎯 Optimization Opportunities:\n'));

  const largeFiles = files.filter(f => f.size > BUNDLE_SIZE_LIMIT * 0.1);
  if (largeFiles.length > 0) {
    console.log(chalk.yellow('Large files that could benefit from lazy loading:'));
    largeFiles.forEach(file => {
      console.log(`  • ${chalk.red(formatBytes(file.size))} - ${file.name}`);
    });
  }

  const antdTotal = categories.antd.reduce((sum, file) => sum + file.size, 0);
  if (antdTotal > BUNDLE_SIZE_LIMIT) {
    console.log(chalk.yellow(`\nAnt Design bundle is ${formatBytes(antdTotal)} - consider tree shaking optimization`));
  }

  const vendorTotal = categories.vendors.reduce((sum, file) => sum + file.size, 0);
  if (vendorTotal > BUNDLE_SIZE_LIMIT * 2) {
    console.log(chalk.yellow(`\nVendor bundle is ${formatBytes(vendorTotal)} - consider splitting into smaller chunks`));
  }

  // Calculate potential savings
  const potentialSavings = calculatePotentialSavings(files);
  console.log(chalk.green.bold(`\n💡 Potential bundle size reduction: ${formatBytes(potentialSavings)}`));

  return {
    totalSize: files.reduce((sum, file) => sum + file.size, 0),
    categories,
    largeFiles,
    potentialSavings
  };
}

function calculatePotentialSavings(files) {
  // Estimate potential savings from lazy loading large components
  const largeChunks = files.filter(f => f.size > 50 * 1024); // Files > 50KB
  const estimatedSavings = largeChunks.reduce((sum, file) => {
    // Assume we can lazy load 70% of large chunks
    return sum + (file.size * 0.7);
  }, 0);
  
  return estimatedSavings;
}

function generateLazyLoadingPlan(analysis) {
  console.log(chalk.blue.bold('\n📋 Lazy Loading Implementation Plan:\n'));

  const plan = [
    {
      priority: 1,
      component: 'Tutorial System',
      estimatedSaving: '150-200 KB',
      implementation: 'React.lazy() for TutorialAssistant and related components'
    },
    {
      priority: 2,
      component: 'AI Suggestions',
      estimatedSaving: '100-150 KB',
      implementation: 'Dynamic import for AIDesignSuggestions'
    },
    {
      priority: 3,
      component: 'Template Manager',
      estimatedSaving: '80-120 KB',
      implementation: 'Lazy load template management UI'
    },
    {
      priority: 4,
      component: 'Code Exporter',
      estimatedSaving: '60-100 KB',
      implementation: 'Dynamic import for export functionality'
    },
    {
      priority: 5,
      component: 'Advanced Property Editors',
      estimatedSaving: '50-80 KB',
      implementation: 'Lazy load color pickers, spacing editors'
    }
  ];

  plan.forEach(item => {
    console.log(`${chalk.green(`Priority ${item.priority}:`)} ${chalk.bold(item.component)}`);
    console.log(`  Estimated saving: ${chalk.yellow(item.estimatedSaving)}`);
    console.log(`  Implementation: ${item.implementation}\n`);
  });

  return plan;
}

// Run analysis
if (require.main === module) {
  const analysis = analyzeJSFiles();
  if (analysis) {
    generateLazyLoadingPlan(analysis);
  }
}

module.exports = {
  analyzeJSFiles,
  generateLazyLoadingPlan,
  formatBytes
};
