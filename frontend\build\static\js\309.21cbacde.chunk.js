"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[309],{1096:(e,t,n)=>{n.d(t,{A:()=>lr});var r=n(4467),o=n(5544),a=n(7528),l=n(6540),i=n(5448),c=n(9740),s=n(7977),u=n(9249),p=n(2702),d=n(9029),m=n(741),f=n(149),g=n(3903),y=n(234),v=n(8602),b=n(5061),h=n(8027),x=n(1468),A=n(1250),E=n(436),w=n(467),C=n(4756),k=n.n(C),S=n(7355),O=n(3016),z=n(9356),P=n(677),j=n(5039),T=n(2120),D=n(9277),I=n(5824),R=n(1372),M=n(3350),L=n(321),F=n(7011),B=n(3674),_=n(5710),H=n(8620),W=n(9932),N=n(7713),U=n(6362),V=n(7265),J=n(3341),q=n(845),G=n(2877),X=n(1592),Y=n(4103),Z=n(8937),$=n(9748),Q=n(462),K=n(9248),ee=n(3029),te=n(2901),ne=function(){return(0,te.A)((function e(){(0,ee.A)(this,e),this.ws=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=3e3,this.listeners=new Map,this.messageQueue=[],this.subscriptions=new Set;var t="https:"===window.location.protocol?"wss:":"ws:";this.wsUrl="".concat(t,"//").concat("localhost:8000","/ws/ai-suggestions/")}),[{key:"connect",value:function(){var e=this;return this.isConnected||this.ws?Promise.resolve():new Promise((function(t,n){try{e.ws=new WebSocket(e.wsUrl),e.ws.onopen=function(){console.log("AI WebSocket connected"),e.isConnected=!0,e.reconnectAttempts=0,e.processMessageQueue(),e.emit("connected"),t()},e.ws.onmessage=function(t){try{var n=JSON.parse(t.data);e.handleMessage(n)}catch(e){console.error("Error parsing AI WebSocket message:",e)}},e.ws.onclose=function(t){console.log("AI WebSocket disconnected:",t.code,t.reason),e.isConnected=!1,e.ws=null,e.emit("disconnected",{code:t.code,reason:t.reason}),1e3!==t.code&&e.reconnectAttempts<e.maxReconnectAttempts&&e.scheduleReconnect()},e.ws.onerror=function(t){console.error("AI WebSocket error:",t),e.emit("error",t),n(t)}}catch(e){console.error("Error creating AI WebSocket:",e),n(e)}}))}},{key:"disconnect",value:function(){this.ws&&(this.ws.close(1e3,"Client disconnect"),this.ws=null,this.isConnected=!1)}},{key:"scheduleReconnect",value:function(){var e=this;this.reconnectAttempts++;var t=this.reconnectInterval*Math.pow(2,this.reconnectAttempts-1);console.log("Scheduling AI WebSocket reconnect attempt ".concat(this.reconnectAttempts," in ").concat(t,"ms")),setTimeout((function(){e.isConnected||e.connect().catch((function(e){console.error("AI WebSocket reconnect failed:",e)}))}),t)}},{key:"send",value:function(e){this.isConnected&&this.ws?this.ws.send(JSON.stringify(e)):(this.messageQueue.push(e),this.isConnected||this.connect())}},{key:"processMessageQueue",value:function(){for(;this.messageQueue.length>0;){var e=this.messageQueue.shift();this.send(e)}}},{key:"handleMessage",value:function(e){var t=e.type;switch(t){case"connection_established":console.log("AI WebSocket connection established");break;case"layout_suggestions":this.emit("layoutSuggestions",e.suggestions);break;case"component_combinations":this.emit("componentCombinations",e.suggestions);break;case"app_analysis":this.emit("appAnalysis",e.analysis);break;case"layout_suggestions_broadcast":this.emit("layoutSuggestionsBroadcast",e.suggestions);break;case"component_combinations_broadcast":this.emit("componentCombinationsBroadcast",e.suggestions);break;case"ai_suggestion_update":this.emit("aiSuggestionUpdate",e);break;case"error":console.error("AI WebSocket error:",e.message),this.emit("error",new Error(e.message));break;case"pong":this.emit("pong",e);break;default:console.log("Unknown AI WebSocket message type:",t,e)}}},{key:"requestLayoutSuggestions",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];this.send({type:"get_layout_suggestions",components:e,layouts:t,context:n,broadcast:r,timestamp:(new Date).toISOString()})}},{key:"requestComponentCombinations",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];this.send({type:"get_component_combinations",components:e,selected_component:t,context:n,broadcast:r,timestamp:(new Date).toISOString()})}},{key:"requestAppAnalysis",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];this.send({type:"analyze_app_structure",components:e,layouts:t,timestamp:(new Date).toISOString()})}},{key:"subscribeToUpdates",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"all";this.subscriptions.add(e),this.send({type:"subscribe_to_updates",subscription_type:e,timestamp:(new Date).toISOString()})}},{key:"ping",value:function(){this.send({type:"ping",timestamp:(new Date).toISOString()})}},{key:"addEventListener",value:function(e,t){this.listeners.has(e)||this.listeners.set(e,new Set),this.listeners.get(e).add(t)}},{key:"removeEventListener",value:function(e,t){this.listeners.has(e)&&this.listeners.get(e).delete(t)}},{key:"emit",value:function(e,t){this.listeners.has(e)&&this.listeners.get(e).forEach((function(n){try{n(t)}catch(t){console.error("Error in AI WebSocket event listener for ".concat(e,":"),t)}}))}},{key:"getStatus",value:function(){return{connected:this.isConnected,reconnectAttempts:this.reconnectAttempts,subscriptions:Array.from(this.subscriptions),queuedMessages:this.messageQueue.length}}}])}(),re=new ne;"undefined"!=typeof window&&setTimeout((function(){re.connect().catch((function(e){console.warn("Initial AI WebSocket connection failed:",e)}))}),1e3);const oe=re,ae=new(function(){return(0,te.A)((function e(){(0,ee.A)(this,e),this.baseUrl="".concat("http://localhost:8000","/api/ai"),this.cache=new Map,this.cacheTimeout=3e5,this.useWebSocket=!0,this.wsService=oe,this.setupWebSocketListeners()}),[{key:"setupWebSocketListeners",value:function(){var e=this;this.wsService.addEventListener("layoutSuggestions",(function(t){e.cache.set("ws_layout_suggestions",{data:{suggestions:t,status:"success"},timestamp:Date.now()})})),this.wsService.addEventListener("componentCombinations",(function(t){e.cache.set("ws_component_combinations",{data:{suggestions:t,status:"success"},timestamp:Date.now()})})),this.wsService.addEventListener("error",(function(e){console.warn("AI WebSocket error, falling back to HTTP:",e)}))}},{key:"generateLayoutSuggestions",value:(o=(0,w.A)(k().mark((function e(t){var n,r,o,a,l,i,c=arguments;return k().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=c.length>1&&void 0!==c[1]?c[1]:[],r=c.length>2&&void 0!==c[2]?c[2]:{},o="layout_".concat(JSON.stringify({components:t,layouts:n,context:r})),!this.cache.has(o)){e.next=7;break}if(a=this.cache.get(o),!(Date.now()-a.timestamp<this.cacheTimeout)){e.next=7;break}return e.abrupt("return",a.data);case 7:if(!this.useWebSocket||!this.wsService.getStatus().connected){e.next=17;break}return e.prev=8,e.next=11,this._getLayoutSuggestionsViaWebSocket(t,n,r,o);case 11:return e.abrupt("return",e.sent);case 14:e.prev=14,e.t0=e.catch(8),console.warn("WebSocket request failed, falling back to HTTP:",e.t0);case 17:return e.prev=17,e.next=20,fetch("".concat(this.baseUrl,"/layout-suggestions/"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:this._getAuthHeader()},body:JSON.stringify({components:t,layouts:n,context:r})});case 20:if((l=e.sent).ok){e.next=23;break}throw new Error("HTTP error! status: ".concat(l.status));case 23:return e.next=25,l.json();case 25:return i=e.sent,this.cache.set(o,{data:i,timestamp:Date.now()}),e.abrupt("return",i);case 30:return e.prev=30,e.t1=e.catch(17),console.error("Error generating layout suggestions:",e.t1),e.abrupt("return",this._getFallbackLayoutSuggestions(t));case 34:case"end":return e.stop()}}),e,this,[[8,14],[17,30]])}))),function(e){return o.apply(this,arguments)})},{key:"_getLayoutSuggestionsViaWebSocket",value:(r=(0,w.A)(k().mark((function e(t,n,r,o){var a=this;return k().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,l){var i=setTimeout((function(){a.wsService.removeEventListener("layoutSuggestions",c),l(new Error("WebSocket request timeout"))}),1e4),c=function(t){clearTimeout(i),a.wsService.removeEventListener("layoutSuggestions",c);var n={suggestions:t,status:"success"};a.cache.set(o,{data:n,timestamp:Date.now()}),e(n)};a.wsService.addEventListener("layoutSuggestions",c),a.wsService.requestLayoutSuggestions(t,n,r)})));case 1:case"end":return e.stop()}}),e)}))),function(e,t,n,o){return r.apply(this,arguments)})},{key:"generateComponentCombinations",value:(n=(0,w.A)(k().mark((function e(t){var n,r,o,a,l,i,c=arguments;return k().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=c.length>1&&void 0!==c[1]?c[1]:null,r=c.length>2&&void 0!==c[2]?c[2]:{},o="combinations_".concat(JSON.stringify({components:t,selectedComponent:n,context:r})),!this.cache.has(o)){e.next=7;break}if(a=this.cache.get(o),!(Date.now()-a.timestamp<this.cacheTimeout)){e.next=7;break}return e.abrupt("return",a.data);case 7:if(!this.useWebSocket||!this.wsService.getStatus().connected){e.next=17;break}return e.prev=8,e.next=11,this._getComponentCombinationsViaWebSocket(t,n,r,o);case 11:return e.abrupt("return",e.sent);case 14:e.prev=14,e.t0=e.catch(8),console.warn("WebSocket request failed, falling back to HTTP:",e.t0);case 17:return e.prev=17,e.next=20,fetch("".concat(this.baseUrl,"/component-combinations/"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:this._getAuthHeader()},body:JSON.stringify({components:t,selected_component:n,context:r})});case 20:if((l=e.sent).ok){e.next=23;break}throw new Error("HTTP error! status: ".concat(l.status));case 23:return e.next=25,l.json();case 25:return i=e.sent,this.cache.set(o,{data:i,timestamp:Date.now()}),e.abrupt("return",i);case 30:return e.prev=30,e.t1=e.catch(17),console.error("Error generating component combinations:",e.t1),e.abrupt("return",this._getFallbackCombinationSuggestions(t,n));case 34:case"end":return e.stop()}}),e,this,[[8,14],[17,30]])}))),function(e){return n.apply(this,arguments)})},{key:"_getComponentCombinationsViaWebSocket",value:(t=(0,w.A)(k().mark((function e(t,n,r,o){var a=this;return k().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,l){var i=setTimeout((function(){a.wsService.removeEventListener("componentCombinations",c),l(new Error("WebSocket request timeout"))}),1e4),c=function(t){clearTimeout(i),a.wsService.removeEventListener("componentCombinations",c);var n={suggestions:t,status:"success"};a.cache.set(o,{data:n,timestamp:Date.now()}),e(n)};a.wsService.addEventListener("componentCombinations",c),a.wsService.requestComponentCombinations(t,n,r)})));case 1:case"end":return e.stop()}}),e)}))),function(e,n,r,o){return t.apply(this,arguments)})},{key:"analyzeAppStructure",value:(e=(0,w.A)(k().mark((function e(t){var n,r,o=arguments;return k().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:[],e.prev=1,e.next=4,fetch("".concat(this.baseUrl,"/analyze-structure/"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:this._getAuthHeader()},body:JSON.stringify({components:t,layouts:n})});case 4:if((r=e.sent).ok){e.next=7;break}throw new Error("HTTP error! status: ".concat(r.status));case 7:return e.next=9,r.json();case 9:return e.abrupt("return",e.sent);case 12:return e.prev=12,e.t0=e.catch(1),console.error("Error analyzing app structure:",e.t0),e.abrupt("return",this._getBasicAnalysis(t));case 16:case"end":return e.stop()}}),e,this,[[1,12]])}))),function(t){return e.apply(this,arguments)})},{key:"clearCache",value:function(){this.cache.clear()}},{key:"_getAuthHeader",value:function(){var e=localStorage.getItem("authToken")||sessionStorage.getItem("authToken");return e?"Bearer ".concat(e):""}},{key:"_getFallbackLayoutSuggestions",value:function(e){var t=e.length,n=[];return t<=3&&n.push({id:"simple_flex",name:"Simple Flexbox Layout",description:"Basic vertical layout for simple apps",score:80,explanation:"Perfect for apps with few components",structure:{display:"flex",flexDirection:"column"}}),t>3&&n.push({id:"grid_layout",name:"Grid Layout",description:"Organized grid for multiple components",score:85,explanation:"Grid layout works well for organizing many components",structure:{display:"grid",gap:"16px"}}),n.push({id:"header_footer",name:"Header-Footer Layout",description:"Classic layout with header and footer",score:75,explanation:"Traditional layout suitable for most applications",structure:{header:!0,footer:!0}}),{suggestions:n,status:"fallback",component_count:t}}},{key:"_getFallbackCombinationSuggestions",value:function(e,t){var n=[],r=e.map((function(e){return e.type}));if(t){var o=t.type;"button"!==o||r.includes("form")||n.push({id:"button_form",name:"Button + Form",description:"Add a form to go with your button",score:70,components:["button","form"],missing_components:["form"]}),"text"!==o||r.includes("image")||n.push({id:"text_image",name:"Text + Image",description:"Add an image to complement your text",score:65,components:["text","image"],missing_components:["image"]})}return r.includes("header")||n.push({id:"add_header",name:"Add Header",description:"Every app needs a header for navigation",score:80,components:["header"],missing_components:["header"]}),{suggestions:n,status:"fallback",component_count:e.length}}},{key:"_getBasicAnalysis",value:function(e){var t={};return e.forEach((function(e){var n=e.type||"unknown";t[n]=(t[n]||0)+1})),{analysis:{component_count:e.length,component_types:t,has_navigation:Object.keys(t).some((function(e){return["header","nav","menu"].includes(e)})),has_forms:Object.keys(t).some((function(e){return["form","input","button"].includes(e)})),has_media:Object.keys(t).some((function(e){return["image","video","gallery"].includes(e)})),complexity_score:2*Object.keys(t).length+e.length,app_type:"general"},status:"basic"}}}]);var e,t,n,r,o}());var le=n(1616);function ie(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ce(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ie(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ie(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var se=n(5556),ue=n.n(se),pe=n(1427),de=n(8295),me=n(6914),fe=n(9237),ge=n(6067),ye=O.A.Text,ve=O.A.Paragraph,be=function(e){var t,n,r,a=e.suggestion,i=e.onApply,c=e.onPreview,d=e.applied,m=void 0!==d&&d,f=e.showPreview,g=void 0===f||f,v=e.showScore,b=void 0===v||v,h=e.compact,x=void 0!==h&&h,A=(0,l.useState)(!1),C=(0,o.A)(A,2),S=C[0],O=C[1],z=(0,l.useState)(!1),j=(0,o.A)(z,2),D=j[0],I=j[1],M=function(){var e=(0,w.A)(k().mark((function e(){return k().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!m&&!D){e.next=2;break}return e.abrupt("return");case 2:if(I(!0),e.prev=3,!i){e.next=7;break}return e.next=7,i(a);case 7:e.next=12;break;case 9:e.prev=9,e.t0=e.catch(3),console.error("Error applying component combination:",e.t0);case 12:return e.prev=12,I(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[3,9,12,15]])})));return function(){return e.apply(this,arguments)}}(),L=function(){c?c(a):O(!0)},F=function(e){return e>=80?"#52c41a":e>=60?"#1890ff":e>=40?"#faad14":"#ff4d4f"},B=function(e){return{button:"🔘",form:"📝",input:"📝",text:"📄",image:"🖼️",card:"🃏",header:"📋",nav:"🧭",list:"📋",divider:"➖",section:"📦",modal:"🪟",table:"📊",chart:"📈"}[e]||"🔧"},_=function(){var e=a.components,t=void 0===e?[]:e,n=a.missing_components,r=void 0===n?[]:n,o=(0,E.A)(t);return l.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",padding:"16px",background:"#fafafa",borderRadius:"6px",border:"1px solid #d9d9d9",minHeight:"80px"}},l.createElement(p.A,{size:"small",wrap:!0},o.map((function(e,t){return l.createElement("div",{key:t,style:{textAlign:"center"}},l.createElement(pe.A,{size:"small",style:{backgroundColor:r.includes(e)?"#ff4d4f":"#1890ff",color:"white",fontSize:"12px"}},B(e)),l.createElement("div",{style:{fontSize:"10px",marginTop:"2px",color:r.includes(e)?"#ff4d4f":"#666"}},e))})),r.length>0&&l.createElement(l.Fragment,null,l.createElement(fe.A,{style:{color:"#999",margin:"0 4px"}}),r.map((function(e,t){return l.createElement("div",{key:"missing-".concat(t),style:{textAlign:"center"}},l.createElement(pe.A,{size:"small",style:{backgroundColor:"#52c41a",color:"white",fontSize:"12px",border:"2px dashed #52c41a"}},B(e)),l.createElement("div",{style:{fontSize:"10px",marginTop:"2px",color:"#52c41a"}},"+",e))})))))},H=function(){return l.createElement(de.A,{title:l.createElement(p.A,null,l.createElement(R.A,null),a.name,b&&l.createElement(T.A,{count:a.score,style:{backgroundColor:F(a.score)}})),open:S,onCancel:function(){return O(!1)},footer:[l.createElement(u.Ay,{key:"cancel",onClick:function(){return O(!1)}},"Close"),l.createElement(u.Ay,{key:"apply",type:"primary",onClick:function(){O(!1),M()},disabled:m,loading:D},m?"Applied":"Add Components")],width:600},l.createElement(p.A,{direction:"vertical",style:{width:"100%"}},l.createElement("div",{style:{marginBottom:"16px"}},_()),l.createElement(ve,null,a.description),l.createElement("div",null,l.createElement(ye,{strong:!0},"Why this combination?"),l.createElement(ve,{type:"secondary"},a.explanation)),a.missing_components&&a.missing_components.length>0&&l.createElement("div",null,l.createElement(ye,{strong:!0},"Components to add:"),l.createElement("div",{style:{marginTop:"8px"}},a.missing_components.map((function(e,t){return l.createElement(me.A,{key:t,color:"green",style:{marginBottom:"4px"}},l.createElement(fe.A,{style:{marginRight:"4px"}}),e)})))),a.use_cases&&l.createElement("div",null,l.createElement(ye,{strong:!0},"Best for:"),l.createElement("div",{style:{marginTop:"8px"}},a.use_cases.map((function(e,t){return l.createElement(me.A,{key:t,color:"blue",style:{marginBottom:"4px"}},e.replace("_"," "))}))))))};return x?l.createElement(l.Fragment,null,l.createElement("div",{style:{display:"flex",alignItems:"center",padding:"8px",border:"1px solid #d9d9d9",borderRadius:"6px",marginBottom:"8px",background:m?"#f6ffed":"white"}},l.createElement("div",{style:{width:"60px",marginRight:"12px"}},l.createElement(p.A,{size:"small"},null===(t=a.components)||void 0===t?void 0:t.slice(0,2).map((function(e,t){return l.createElement(pe.A,{key:t,size:"small",style:{backgroundColor:"#1890ff",fontSize:"10px"}},B(e))})),(null===(n=a.missing_components)||void 0===n?void 0:n.length)>0&&l.createElement(pe.A,{size:"small",style:{backgroundColor:"#52c41a",fontSize:"10px"}},"+"))),l.createElement("div",{style:{flex:1}},l.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"2px"}},l.createElement(ye,{strong:!0,style:{fontSize:"12px"}},a.name),b&&l.createElement(T.A,{count:a.score,style:{backgroundColor:F(a.score),marginLeft:"8px"}})),l.createElement(ye,{type:"secondary",style:{fontSize:"11px"}},(null===(r=a.missing_components)||void 0===r?void 0:r.length)>0?"Add ".concat(a.missing_components.join(", ")):a.explanation)),l.createElement(p.A,null,g&&l.createElement(s.A,{title:"Preview"},l.createElement(u.Ay,{type:"text",size:"small",icon:l.createElement(y.A,null),onClick:L})),l.createElement(u.Ay,{type:m?"default":"primary",size:"small",icon:m?l.createElement(ge.A,null):l.createElement(fe.A,null),onClick:M,disabled:m,loading:D},m?"Added":"Add"))),H()):l.createElement(l.Fragment,null,l.createElement(P.A,{size:"small",style:{marginBottom:"12px",border:m?"2px solid #52c41a":"1px solid #d9d9d9"},title:l.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"}},l.createElement(p.A,null,l.createElement(ye,{strong:!0},a.name),b&&l.createElement(T.A,{count:a.score,style:{backgroundColor:F(a.score)}})),m&&l.createElement(ge.A,{style:{color:"#52c41a"}})),extra:l.createElement(p.A,null,g&&l.createElement(s.A,{title:"Preview combination"},l.createElement(u.Ay,{type:"text",size:"small",icon:l.createElement(y.A,null),onClick:L})),l.createElement(u.Ay,{type:m?"default":"primary",size:"small",icon:m?l.createElement(ge.A,null):l.createElement(fe.A,null),onClick:M,disabled:m,loading:D},m?"Added":"Add Components"))},l.createElement("div",{style:{marginBottom:"12px"}},_()),l.createElement(ve,{style:{margin:"0 0 8px 0",fontSize:"12px",color:"#666"}},a.description),a.missing_components&&a.missing_components.length>0&&l.createElement("div",{style:{marginBottom:"8px"}},l.createElement(ye,{strong:!0,style:{fontSize:"11px"}},"Missing components: "),l.createElement(p.A,{size:"small",wrap:!0},a.missing_components.map((function(e,t){return l.createElement(me.A,{key:t,color:"green",size:"small"},e)})))),l.createElement("div",{style:{display:"flex",alignItems:"center"}},l.createElement(K.A,{style:{marginRight:"4px",color:"#1890ff"}}),l.createElement(ye,{style:{fontSize:"11px",fontStyle:"italic"}},a.explanation))),H())};be.propTypes={suggestion:ue().shape({id:ue().string.isRequired,name:ue().string.isRequired,description:ue().string.isRequired,score:ue().number.isRequired,explanation:ue().string.isRequired,components:ue().array,missing_components:ue().array,use_cases:ue().array}).isRequired,onApply:ue().func,onPreview:ue().func,applied:ue().bool,showPreview:ue().bool,showScore:ue().bool,compact:ue().bool};var he,xe,Ae,Ee,we,Ce,ke,Se,Oe,ze,Pe=function(e){var t=e.suggestions,n=void 0===t?[]:t,r=e.onApply,o=e.onPreview,a=e.appliedSuggestions,i=void 0===a?new Set:a,c=e.loading,s=void 0!==c&&c,u=e.compact,p=void 0!==u&&u,m=e.showScore,f=void 0===m||m,g=e.showPreview,y=void 0===g||g,v=e.emptyMessage,b=void 0===v?"No component combinations available":v;return s?l.createElement("div",{style:{textAlign:"center",padding:"20px"}},l.createElement(d.A,{tip:"Finding component combinations..."})):0===n.length?l.createElement("div",{style:{textAlign:"center",padding:"20px",color:"#999"}},l.createElement(R.A,{style:{fontSize:"48px",marginBottom:"16px"}}),l.createElement("div",null,b)):l.createElement("div",{style:{maxHeight:"400px",overflowY:"auto"}},n.map((function(e){return l.createElement(be,{key:e.id,suggestion:e,onApply:r,onPreview:o,applied:i.has(e.id),compact:p,showScore:f,showPreview:y})})))};function je(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Te(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?je(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):je(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}Pe.propTypes={suggestions:ue().array,onApply:ue().func,onPreview:ue().func,appliedSuggestions:ue().instanceOf(Set),loading:ue().bool,compact:ue().bool,showScore:ue().bool,showPreview:ue().bool,emptyMessage:ue().string};var De=S.A.Search,Ie=O.A.Text,Re=O.A.Title,Me=(z.A.Panel,A.Ay.div(he||(he=(0,a.A)(["\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n"])))),Le=A.Ay.div(xe||(xe=(0,a.A)(["\n  padding: 16px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  \n  .ant-typography {\n    color: white !important;\n    margin-bottom: 8px;\n  }\n"]))),Fe=A.Ay.div(Ae||(Ae=(0,a.A)(["\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n"]))),Be=(0,A.Ay)(P.A)(Ee||(Ee=(0,a.A)(["\n  margin: 4px;\n  cursor: grab;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: ",";\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n    border-color: #1890ff;\n  }\n  \n  &:active {\n    cursor: grabbing;\n    transform: scale(0.98);\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    text-align: center;\n  }\n"])),(function(e){return e.isDragging?"#e6f7ff":"#fff"})),_e=A.Ay.div(we||(we=(0,a.A)(["\n  font-size: 24px;\n  color: #1890ff;\n  margin-bottom: 8px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: rgba(24, 144, 255, 0.1);\n  margin: 0 auto 8px;\n  transition: all 0.3s ease;\n  \n  ",":hover & {\n    background: rgba(24, 144, 255, 0.2);\n    transform: scale(1.1);\n  }\n"])),Be),He=A.Ay.div(Ce||(Ce=(0,a.A)(["\n  font-size: 12px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n"]))),We=A.Ay.div(ke||(ke=(0,a.A)(["\n  font-size: 10px;\n  color: #666;\n  line-height: 1.2;\n"]))),Ne=A.Ay.div(Se||(Se=(0,a.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 16px;\n  background: #f0f2f5;\n  border-bottom: 1px solid #d9d9d9;\n  font-weight: 600;\n  color: #333;\n"]))),Ue=A.Ay.div(Oe||(Oe=(0,a.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\n  gap: 8px;\n  padding: 16px;\n"]))),Ve=A.Ay.div(ze||(ze=(0,a.A)(["\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  color: #bbb;\n  font-size: 12px;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  \n  ",":hover & {\n    opacity: 1;\n  }\n"])),Be);const Je=function(e){var t=e.onAddComponent,n=e.onDragStart,r=e.onDragEnd,a=e.components,i=void 0===a?[]:a,d=e.selectedComponent,m=void 0===d?null:d,f=e.showAISuggestions,g=void 0===f||f,y=(0,l.useState)(""),v=(0,o.A)(y,2),b=v[0],h=v[1],A=(0,l.useState)(["AI Suggestions","Layout","Basic Components"]),C=(0,o.A)(A,2),S=C[0],O=C[1],z=(0,l.useState)(!0),P=(0,o.A)(z,2),ee=P[0],te=P[1],ne=(0,l.useState)(null),re=(0,o.A)(ne,2),oe=re[0],ie=re[1],se=(0,l.useRef)(null),ue=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.autoRefresh,n=void 0===t||t,r=e.refreshInterval,a=void 0===r?3e4:r,i=e.enableCache,c=void 0===i||i,s=e.context,u=void 0===s?{}:s,p=(0,x.wA)(),d=(0,x.d4)((function(e){var t;return(null===(t=e.app)||void 0===t?void 0:t.components)||e.components||[]})),m=(0,x.d4)((function(e){var t;return(null===(t=e.app)||void 0===t?void 0:t.layouts)||e.layouts||[]})),f=(0,x.d4)((function(e){var t;return(null===(t=e.ui)||void 0===t?void 0:t.selectedComponent)||null})),g=(0,l.useState)({layout:[],combinations:[],analysis:null}),y=(0,o.A)(g,2),v=y[0],b=y[1],h=(0,l.useState)({layout:!1,combinations:!1,analysis:!1}),A=(0,o.A)(h,2),E=A[0],C=A[1],S=(0,l.useState)(null),O=(0,o.A)(S,2),z=O[0],P=O[1],j=(0,l.useState)(null),T=(0,o.A)(j,2),D=T[0],I=T[1],R=(0,l.useRef)(null),M=(0,l.useRef)(null),L=(0,l.useCallback)((0,w.A)(k().mark((function e(){var t,n,r,a,l,i,s,p,g,y=arguments;return k().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=y.length>0&&void 0!==y[0]&&y[0],d&&0!==d.length){e.next=4;break}return b({layout:[],combinations:[],analysis:null}),e.abrupt("return");case 4:return M.current&&M.current.abort(),M.current=new AbortController,P(null),C({layout:!0,combinations:!0,analysis:!0}),e.prev=8,t&&c&&ae.clearCache(),e.next=12,Promise.allSettled([ae.generateLayoutSuggestions(d,m,u),ae.generateComponentCombinations(d,f,u),ae.analyzeAppStructure(d,m)]);case 12:n=e.sent,r=(0,o.A)(n,3),a=r[0],l=r[1],i=r[2],s="fulfilled"===a.status&&a.value.suggestions||[],p="fulfilled"===l.status&&l.value.suggestions||[],g="fulfilled"===i.status&&i.value.analysis||null,b({layout:s,combinations:p,analysis:g}),I(new Date),[a,l,i].forEach((function(e,t){"rejected"===e.status&&console.warn("Failed to load ".concat(["layout","combinations","analysis"][t]," suggestions:"),e.reason)})),e.next=28;break;case 25:e.prev=25,e.t0=e.catch(8),"AbortError"!==e.t0.name&&P("Failed to load suggestions: ".concat(e.t0.message));case 28:return e.prev=28,C({layout:!1,combinations:!1,analysis:!1}),e.finish(28);case 31:case"end":return e.stop()}}),e,null,[[8,25,28,31]])}))),[d,m,f,u,c]),F=(0,l.useCallback)((0,w.A)(k().mark((function e(){var t;return k().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(d&&0!==d.length){e.next=2;break}return e.abrupt("return");case 2:return C((function(e){return ce(ce({},e),{},{layout:!0})})),P(null),e.prev=4,e.next=7,ae.generateLayoutSuggestions(d,m,u);case 7:t=e.sent,b((function(e){return ce(ce({},e),{},{layout:t.suggestions||[]})})),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(4),P("Failed to load layout suggestions: ".concat(e.t0.message));case 14:return e.prev=14,C((function(e){return ce(ce({},e),{},{layout:!1})})),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[4,11,14,17]])}))),[d,m,u]),B=(0,l.useCallback)((0,w.A)(k().mark((function e(){var t;return k().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(d&&0!==d.length){e.next=2;break}return e.abrupt("return");case 2:return C((function(e){return ce(ce({},e),{},{combinations:!0})})),P(null),e.prev=4,e.next=7,ae.generateComponentCombinations(d,f,u);case 7:t=e.sent,b((function(e){return ce(ce({},e),{},{combinations:t.suggestions||[]})})),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(4),P("Failed to load combination suggestions: ".concat(e.t0.message));case 14:return e.prev=14,C((function(e){return ce(ce({},e),{},{combinations:!1})})),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[4,11,14,17]])}))),[d,f,u]),_=(0,l.useCallback)((function(e){try{return console.log("Applying layout suggestion:",e),!0}catch(e){return P("Failed to apply layout suggestion: ".concat(e.message)),!1}}),[]),H=(0,l.useCallback)((function(e){try{return e.missing_components&&e.missing_components.length>0&&e.missing_components.forEach((function(e){var t={type:e,props:{},id:"".concat(e,"-").concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9))};p((0,le.X8)(t.type,t.props))})),console.log("Applied component combination:",e),!0}catch(e){return P("Failed to apply component combination: ".concat(e.message)),!1}}),[p]),W=(0,l.useCallback)((function(){L(!0)}),[L]),N=(0,l.useCallback)((function(){P(null)}),[]);return(0,l.useEffect)((function(){if(n&&a>0)return R.current=setInterval((function(){L()}),a),function(){R.current&&clearInterval(R.current)}}),[n,a,L]),(0,l.useEffect)((function(){L()}),[L]),(0,l.useEffect)((function(){return function(){R.current&&clearInterval(R.current),M.current&&M.current.abort()}}),[]),{suggestions:v,loading:E,error:z,lastRefresh:D,loadSuggestions:L,loadLayoutSuggestions:F,loadCombinationSuggestions:B,applyLayoutSuggestion:_,applyComponentCombination:H,refresh:W,clearError:N,hasLayoutSuggestions:v.layout.length>0,hasCombinationSuggestions:v.combinations.length>0,hasAnalysis:null!==v.analysis,isLoading:E.layout||E.combinations||E.analysis,componentCount:d.length,layoutCount:m.length,selectedComponentType:(null==f?void 0:f.type)||null}}({autoRefresh:!0,context:{selectedComponent:m}}),pe=ue.suggestions,de=ue.loading,me=ue.applyComponentCombination,fe=(ue.hasLayoutSuggestions,ue.hasCombinationSuggestions),ge=function(){var e=(0,w.A)(k().mark((function e(n){return k().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!me(n)){e.next=6;break}return n.missing_components&&n.missing_components.forEach((function(e){t(e)})),c.Ay.success("Applied AI suggestion: ".concat(n.name)),e.abrupt("return",!0);case 6:e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("Error applying AI combination:",e.t0),c.Ay.error("Failed to apply AI suggestion");case 12:return e.abrupt("return",!1);case 13:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),ye=function(){if(!m)return[];var e=m.type,t=i.map((function(e){return e.type})),n=[];return"button"!==e||t.includes("form")||n.push({type:"form",reason:"Buttons often work with forms",priority:"high",icon:l.createElement(D.A,null),label:"Form"}),"form"!==e||t.includes("input")||n.push({type:"input",reason:"Forms need input fields",priority:"high",icon:l.createElement(D.A,null),label:"Input"}),"text"!==e||t.includes("image")||n.push({type:"image",reason:"Text and images work well together",priority:"medium",icon:l.createElement(I.A,null),label:"Image"}),"card"!==e||t.includes("button")||n.push({type:"button",reason:"Cards often need action buttons",priority:"medium",icon:l.createElement(R.A,null),label:"Button"}),n}(),ve=[].concat((0,E.A)(g&&(fe||ye.length>0)?[{title:"AI Suggestions",description:"Smart component recommendations based on your current app",color:"#1890ff",isAI:!0,components:(0,E.A)(ye.map((function(e){return{type:e.type,icon:e.icon,label:e.label,description:e.reason,usage:"Recommended for your ".concat(null==m?void 0:m.type),tags:["ai","smart","contextual"],priority:e.priority,isAISuggestion:!0}})))}]:[]),[{title:"Layout",description:"Structural components for organizing content",color:"#52c41a",components:[{type:"header",icon:l.createElement(M.A,null),label:"Header",description:"Page or section header with title and navigation",usage:"Use for page titles, navigation bars, or section headers",tags:["layout","navigation","title"]},{type:"section",icon:l.createElement(L.A,null),label:"Section",description:"Container for grouping related content",usage:"Organize content into logical sections",tags:["layout","container","organization"]},{type:"card",icon:l.createElement(F.A,null),label:"Card",description:"Flexible content container with optional header and footer",usage:"Display content in a clean, contained format",tags:["layout","container","content"]},{type:"tabs",icon:l.createElement(B.A,null),label:"Tabs",description:"Tabbed interface for organizing content",usage:"Switch between different views or content sections",tags:["layout","navigation","organization"]},{type:"divider",icon:l.createElement(B.A,null),label:"Divider",description:"Visual separator between content sections",usage:"Separate content sections visually",tags:["layout","separator","visual"]}]},{title:"Basic Components",description:"Essential UI elements for content and interaction",color:"#1890ff",components:[{type:"text",icon:l.createElement(_.A,null),label:"Text",description:"Formatted text content with typography options",usage:"Display paragraphs, headings, and formatted text",tags:["content","text","typography"]},{type:"button",icon:l.createElement(R.A,null),label:"Button",description:"Interactive button for user actions",usage:"Trigger actions, submit forms, or navigate",tags:["interaction","action","click"]},{type:"image",icon:l.createElement(I.A,null),label:"Image",description:"Display images with responsive sizing",usage:"Show photos, illustrations, or graphics",tags:["media","visual","content"]},{type:"list",icon:l.createElement(H.A,null),label:"List",description:"Ordered or unordered list of items",usage:"Display collections of related items",tags:["content","organization","items"]},{type:"tag",icon:l.createElement(W.A,null),label:"Tag",description:"Small label for categorization or status",usage:"Label content, show status, or categorize",tags:["label","status","category"]}]},{title:"Form Components",description:"Interactive elements for user input and data collection",color:"#722ed1",components:[{type:"form",icon:l.createElement(D.A,null),label:"Form",description:"Container for form fields with validation",usage:"Collect user input with validation and submission",tags:["input","validation","data"]},{type:"input",icon:l.createElement(D.A,null),label:"Input",description:"Text input field for user data entry",usage:"Collect text, numbers, or other typed input",tags:["input","text","data"]},{type:"select",icon:l.createElement(D.A,null),label:"Select",description:"Dropdown selection from predefined options",usage:"Choose from a list of predefined options",tags:["input","selection","dropdown"]},{type:"checkbox",icon:l.createElement(N.A,null),label:"Checkbox",description:"Boolean input for yes/no or multiple selections",usage:"Select multiple options or toggle settings",tags:["input","boolean","selection"]},{type:"datepicker",icon:l.createElement(U.A,null),label:"Date Picker",description:"Calendar interface for date selection",usage:"Select dates, date ranges, or schedule events",tags:["input","date","calendar"]},{type:"slider",icon:l.createElement(V.A,null),label:"Slider",description:"Range input with visual slider interface",usage:"Select numeric values within a range",tags:["input","range","numeric"]}]},{title:"Data Components",description:"Components for displaying and visualizing data",color:"#fa8c16",components:[{type:"table",icon:l.createElement(J.A,null),label:"Table",description:"Structured data display with sorting and filtering",usage:"Display tabular data with advanced features",tags:["data","table","structured"]},{type:"chart",icon:l.createElement(q.A,null),label:"Chart",description:"Visual data representation with multiple chart types",usage:"Visualize data trends and comparisons",tags:["data","visualization","analytics"]},{type:"statistic",icon:l.createElement(q.A,null),label:"Statistic",description:"Highlighted numeric data with formatting",usage:"Display key metrics and KPIs prominently",tags:["data","metrics","numbers"]}]}]),be=(0,l.useMemo)((function(){return b?ve.map((function(e){return Te(Te({},e),{},{components:e.components.filter((function(e){return e.label.toLowerCase().includes(b.toLowerCase())||e.description.toLowerCase().includes(b.toLowerCase())||e.tags.some((function(e){return e.toLowerCase().includes(b.toLowerCase())}))}))})})).filter((function(e){return e.components.length>0})):ve}),[b]),he=function(e){ie(null),r&&r()};return l.createElement(Me,null,l.createElement(Le,null,l.createElement(Re,{level:5,style:{margin:0,color:"white"}},"Component Palette"),l.createElement(Ie,{style:{color:"rgba(255, 255, 255, 0.8)"}},"Drag components to the canvas or click to add")),l.createElement(Fe,null,l.createElement(De,{placeholder:"Search components...",value:b,onChange:function(e){return h(e.target.value)},prefix:l.createElement(G.A,null),allowClear:!0,style:{marginBottom:8}}),l.createElement(p.A,null,l.createElement(Ie,{style:{fontSize:12}},"Show descriptions:"),l.createElement(j.A,{size:"small",checked:ee,onChange:te}))),be.map((function(e){return l.createElement("div",{key:e.title},l.createElement(Ne,{onClick:function(){return t=e.title,void O((function(e){return e.includes(t)?e.filter((function(e){return e!==t})):[].concat((0,E.A)(e),[t])}));var t}},l.createElement(p.A,null,l.createElement("div",{style:{width:12,height:12,borderRadius:"50%",backgroundColor:e.color}}),e.isAI&&l.createElement(X.A,{style:{color:e.color}}),l.createElement("span",null,e.title),l.createElement(T.A,{count:e.components.length,size:"small"}),e.isAI&&de.combinations&&l.createElement(T.A,{status:"processing"})),l.createElement(Y.A,{style:{transform:S.includes(e.title)?"rotate(180deg)":"rotate(0deg)",transition:"transform 0.3s ease"}})),S.includes(e.title)&&l.createElement(l.Fragment,null,e.isAI&&fe&&l.createElement("div",{style:{padding:"16px",borderBottom:"1px solid #f0f0f0"}},l.createElement(Ie,{type:"secondary",style:{fontSize:"12px",display:"block",marginBottom:"8px"}},l.createElement(Z.A,null)," AI-powered component combinations:"),l.createElement(Pe,{suggestions:pe.combinations,onApply:ge,loading:de.combinations,compact:!0,showScore:!1,showPreview:!1,emptyMessage:"No AI combinations available"})),l.createElement(Ue,null,e.components.map((function(e){return l.createElement(Be,{key:e.type,size:"small",hoverable:!0,isDragging:(null==oe?void 0:oe.type)===e.type,draggable:!0,onDragStart:function(t){return function(e,t){if(ie(t),e.dataTransfer.setData("application/json",JSON.stringify({type:t.type,label:t.label,source:"palette"})),e.dataTransfer.effectAllowed="copy",se.current){var r=se.current.cloneNode(!0);r.style.transform="rotate(5deg)",r.style.opacity="0.8",e.dataTransfer.setDragImage(r,60,30)}n&&n(t)}(t,e)},onDragEnd:he,onClick:function(){return t(e.type)},ref:(null==oe?void 0:oe.type)===e.type?se:null,style:{border:e.isAISuggestion?"2px solid #52c41a":void 0,background:e.isAISuggestion?"#f6ffed":void 0}},l.createElement(Ve,null,l.createElement($.A,null)),e.isAISuggestion&&l.createElement("div",{style:{position:"absolute",top:4,right:4,background:"high"===e.priority?"#52c41a":"#1890ff",color:"white",borderRadius:"50%",width:16,height:16,display:"flex",alignItems:"center",justifyContent:"center",fontSize:10}},l.createElement(Q.A,null)),l.createElement(_e,{style:{background:e.isAISuggestion?"high"===e.priority?"rgba(82, 196, 26, 0.1)":"rgba(24, 144, 255, 0.1)":void 0}},e.icon),l.createElement(He,null,e.label),ee&&l.createElement(We,null,e.description),l.createElement(s.A,{title:l.createElement("div",null,l.createElement("div",{style:{fontWeight:"bold",marginBottom:4}},e.label,e.isAISuggestion&&l.createElement(T.A,{count:"AI",style:{backgroundColor:"#52c41a",marginLeft:8}})),l.createElement("div",{style:{marginBottom:8}},e.description),l.createElement("div",{style:{fontSize:11,color:"#ccc"}},l.createElement("strong",null,"Usage:")," ",e.usage),l.createElement("div",{style:{fontSize:11,color:"#ccc",marginTop:4}},l.createElement("strong",null,"Tags:")," ",e.tags.join(", "))),placement:"right"},l.createElement(K.A,{style:{position:"absolute",top:4,left:4,fontSize:10,color:e.isAISuggestion?"#52c41a":"#bbb",opacity:.7}})))})))))})),0===be.length&&l.createElement("div",{style:{padding:32,textAlign:"center",color:"#999"}},l.createElement(G.A,{style:{fontSize:24,marginBottom:8}}),l.createElement("div",null,'No components found matching "',b,'"'),l.createElement(u.Ay,{type:"link",size:"small",onClick:function(){return h("")},style:{padding:0,marginTop:8}},"Clear search")))};var qe=n(8168),Ge=n(4358),Xe=n(6552),Ye=n(9467),Ze=n(6955),$e=n(7308),Qe=n(9091),Ke=n(448),et=n(2648),tt=n(261),nt=n(7046),rt=n(1656),ot=n(5163),at=n(3598),lt=n(1005),it=n(7826),ct=n(9445),st=n(2454),ut=n(2543),pt=n(9459);function dt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var mt,ft,gt,yt,vt,bt,ht,xt,At,Et,wt,Ct,kt;function St(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ot(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?St(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):St(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var zt=O.A.Title,Pt=O.A.Text,jt=(O.A.Paragraph,Ge.A.Option,{mobile:{name:"Mobile",icon:l.createElement(Qe.A,null),width:375,height:667,scale:.8,frame:!0},tablet:{name:"Tablet",icon:l.createElement(Ke.A,null),width:768,height:1024,scale:.7,frame:!0},desktop:{name:"Desktop",icon:l.createElement(et.A,null),width:1200,height:800,scale:1,frame:!1}}),Tt=A.Ay.div(mt||(mt=(0,a.A)(["\n  position: relative;\n  height: 100%;\n  background: #f5f5f5;\n  border-radius: 8px;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n"]))),Dt=A.Ay.div(ft||(ft=(0,a.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 16px;\n  background: white;\n  border-bottom: 1px solid #e8e8e8;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  z-index: 10;\n  flex-wrap: wrap;\n  gap: 8px;\n\n  @media (max-width: 768px) {\n    padding: 6px 12px;\n    gap: 6px;\n  }\n"]))),It=A.Ay.div(gt||(gt=(0,a.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 4px;\n  background: #f5f5f5;\n  border-radius: 6px;\n"]))),Rt=(0,A.Ay)(u.Ay)(yt||(yt=(0,a.A)(["\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  border: none;\n  background: ",";\n  color: ",";\n  box-shadow: none;\n\n  &:hover {\n    background: ",";\n    color: ",";\n  }\n"])),(function(e){return e.active?"#1890ff":"transparent"}),(function(e){return e.active?"white":"#666"}),(function(e){return e.active?"#40a9ff":"#e6f7ff"}),(function(e){return e.active?"white":"#1890ff"})),Mt=A.Ay.div(vt||(vt=(0,a.A)(["\n  position: relative;\n  margin: 20px auto;\n  background: ",";\n  border-radius: ",";\n  padding: ",";\n  box-shadow: ",";\n  transition: all 0.3s ease;\n\n  ","\n\n  ","\n"])),(function(e){return"mobile"===e.deviceType?"#333":"tablet"===e.deviceType?"#444":"transparent"}),(function(e){return"mobile"===e.deviceType?"25px":"tablet"===e.deviceType?"15px":"0"}),(function(e){return"mobile"===e.deviceType?"20px 10px":"tablet"===e.deviceType?"15px":"0"}),(function(e){return e.frame?"0 8px 32px rgba(0, 0, 0, 0.3)":"none"}),(function(e){return"mobile"===e.deviceType&&"\n    &::before {\n      content: '';\n      position: absolute;\n      top: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 60px;\n      height: 4px;\n      background: #666;\n      border-radius: 2px;\n    }\n\n    &::after {\n      content: '';\n      position: absolute;\n      bottom: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 40px;\n      height: 40px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  "}),(function(e){return"tablet"===e.deviceType&&"\n    &::before {\n      content: '';\n      position: absolute;\n      bottom: 6px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 30px;\n      height: 30px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  "})),Lt=A.Ay.div(bt||(bt=(0,a.A)(["\n  width: ","px;\n  height: ","px;\n  max-width: 100%;\n  max-height: 100%;\n  background: white;\n  border-radius: ",";\n  overflow: auto;\n  position: relative;\n  transform: scale(",");\n  transform-origin: top center;\n  transition: all 0.3s ease;\n\n  @media (max-width: 1200px) {\n    transform: scale(",");\n  }\n\n  @media (max-width: 768px) {\n    transform: scale(",");\n  }\n"])),(function(e){return e.deviceWidth}),(function(e){return e.deviceHeight}),(function(e){return"mobile"===e.deviceType?"8px":"tablet"===e.deviceType?"6px":"0"}),(function(e){return e.scale}),(function(e){return Math.min(e.scale,.8)}),(function(e){return Math.min(e.scale,.6)})),Ft=A.Ay.div(ht||(ht=(0,a.A)(["\n  flex: 1;\n  position: relative;\n  overflow: auto;\n  background: ",";\n  background-size: ","px ","px;\n  background-position: ","px ","px;\n"])),(function(e){return e.showGrid?"radial-gradient(circle, #ddd 1px, transparent 1px)":"#f5f5f5"}),(function(e){return e.gridSize||20}),(function(e){return e.gridSize||20}),(function(e){var t;return(null===(t=e.gridOffset)||void 0===t?void 0:t.x)||0}),(function(e){var t;return(null===(t=e.gridOffset)||void 0===t?void 0:t.y)||0})),Bt=(A.Ay.div(xt||(xt=(0,a.A)(["\n  min-height: 100%;\n  min-width: 100%;\n  position: relative;\n  transform: scale(",");\n  transform-origin: top left;\n  transition: transform 0.2s ease;\n  padding: ",";\n"])),(function(e){return e.zoom||1}),(function(e){return e.previewMode?"0":"32px"})),A.Ay.div(At||(At=(0,a.A)(["\n  position: relative;\n  margin: 8px 0;\n  border: ",";\n  border-radius: 4px;\n  background: ",";\n  transition: all 0.3s ease;\n  cursor: ",";\n  \n  &:hover {\n    border-color: ",";\n    box-shadow: ",";\n    transform: ",";\n  }\n  \n  ","\n"])),(function(e){return e.isSelected?"2px solid #1890ff":"1px dashed transparent"}),(function(e){return e.isSelected?"rgba(24, 144, 255, 0.05)":"white"}),(function(e){return e.previewMode?"default":"pointer"}),(function(e){return e.previewMode?"transparent":"#1890ff"}),(function(e){return e.previewMode?"none":"0 2px 8px rgba(24, 144, 255, 0.2)"}),(function(e){return e.previewMode?"none":"translateY(-1px)"}),(function(e){return e.isDragOver&&"\n    border-color: #52c41a !important;\n    background: rgba(82, 196, 26, 0.1) !important;\n    transform: scale(1.02);\n  "}))),_t=A.Ay.div(Et||(Et=(0,a.A)(["\n  position: absolute;\n  top: -2px;\n  right: -2px;\n  display: flex;\n  gap: 4px;\n  background: rgba(255, 255, 255, 0.95);\n  padding: 4px;\n  border-radius: 4px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n  opacity: ",";\n  transform: translateY(",");\n  transition: all 0.3s ease;\n  z-index: 5;\n"])),(function(e){return e.visible?1:0}),(function(e){return e.visible?"0":"-10px"})),Ht=(0,A.Ay)(u.Ay)(wt||(wt=(0,a.A)(["\n  width: 24px;\n  height: 24px;\n  padding: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n  box-shadow: none;\n  \n  &:hover {\n    background: #f0f0f0;\n    transform: scale(1.1);\n  }\n"]))),Wt=A.Ay.div(Ct||(Ct=(0,a.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed #d9d9d9;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.8);\n  opacity: ",";\n  pointer-events: ",";\n  transition: all 0.3s ease;\n  z-index: 1;\n  \n  ","\n"])),(function(e){return e.visible?1:0}),(function(e){return e.visible?"auto":"none"}),(function(e){return e.isActive&&"\n    border-color: #52c41a;\n    background: rgba(82, 196, 26, 0.1);\n    \n    &::before {\n      content: 'Drop component here';\n      color: #52c41a;\n      font-weight: 600;\n      font-size: 16px;\n    }\n  "})),Nt=A.Ay.div(kt||(kt=(0,a.A)(["\n  position: absolute;\n  background: rgba(255, 255, 255, 0.9);\n  border: 1px solid #e8e8e8;\n  font-size: 10px;\n  color: #666;\n  z-index: 5;\n  \n  ","\n  \n  ","\n"])),(function(e){return"horizontal"===e.orientation&&"\n    top: 0;\n    left: 32px;\n    right: 0;\n    height: 20px;\n    border-bottom: 1px solid #e8e8e8;\n    background-image: repeating-linear-gradient(\n      to right,\n      transparent,\n      transparent 9px,\n      #e8e8e8 9px,\n      #e8e8e8 10px\n    );\n  "}),(function(e){return"vertical"===e.orientation&&"\n    top: 20px;\n    left: 0;\n    bottom: 0;\n    width: 32px;\n    border-right: 1px solid #e8e8e8;\n    background-image: repeating-linear-gradient(\n      to bottom,\n      transparent,\n      transparent 9px,\n      #e8e8e8 9px,\n      #e8e8e8 10px\n    );\n  "}));const Ut=function(e){var t=e.components,n=void 0===t?[]:t,a=e.onSelectComponent,i=e.onDeleteComponent,c=e.onUpdateComponent,d=e.onMoveComponent,m=e.previewMode,f=void 0!==m&&m,g=e.selectedComponentId,y=e.onDrop,v=e.onDragOver,b=e.onDragLeave,h=e.realTimeUpdates,A=void 0===h||h,E=e.websocketConnected,w=void 0!==E&&E,C=(0,l.useState)("desktop"),k=(0,o.A)(C,2),O=k[0],z=k[1],D=(0,l.useState)(1),I=(0,o.A)(D,2),R=I[0],M=I[1],L=(0,l.useState)(!0),F=(0,o.A)(L,2),B=F[0],_=F[1],H=(0,l.useState)(20),W=(0,o.A)(H,2),N=W[0],U=(W[1],(0,l.useState)(!1)),V=(0,o.A)(U,2),J=V[0],q=V[1],G=(0,l.useState)(!0),X=(0,o.A)(G,2),Y=X[0],Z=X[1],$=(0,l.useState)(!1),Q=(0,o.A)($,2),K=Q[0],ee=Q[1],te=(0,l.useState)(null),ne=(0,o.A)(te,2),re=ne[0],oe=ne[1],ae=(0,l.useState)(null),le=(0,o.A)(ae,2),ie=le[0],ce=le[1],se=(0,l.useState)(!1),ue=(0,o.A)(se,2),pe=ue[0],de=ue[1],me=(0,l.useState)(null),fe=(0,o.A)(me,2),ge=fe[0],ye=fe[1],ve=(0,l.useRef)(null),be=(0,l.useRef)(null),he=((0,x.wA)(),(0,x.d4)((function(e){return e.websocket||{}})),(0,x.d4)((function(e){var t;return null===(t=e.websocket)||void 0===t?void 0:t.service}))),xe=jt[O],Ae=(0,l.useMemo)((function(){return{width:xe.width,height:xe.height,scale:xe.scale}}),[xe]),Ee=(0,pt.A)({components:n,onUpdateComponent:c,onAddComponent:function(e){c&&c(e)},onDeleteComponent:i,websocketService:he,enableWebSocket:A&&w}),we=Ee.isUpdating,Ce=Ee.lastUpdateTime,ke=Ee.websocketConnected,Se=Ee.updateComponent,Oe=(Ee.addComponent,Ee.deleteComponent,Ee.getAllComponents),ze=(Ee.forceUpdate,function(e){var t=e.components,n=void 0===t?[]:t,a=e.containerHeight,i=void 0===a?600:a,c=e.itemHeight,s=void 0===c?100:c,u=e.overscan,p=void 0===u?5:u,d=e.enableVirtualization,m=void 0===d||d,f=e.enablePerformanceMonitoring,g=void 0===f||f,y=(0,l.useState)(0),v=(0,o.A)(y,2),b=v[0],h=v[1],x=(0,l.useState)(null),A=(0,o.A)(x,2),E=A[0],w=A[1],C=(0,l.useState)({start:0,end:0}),k=(0,o.A)(C,2),S=k[0],O=k[1],z=(0,l.useState)(0),P=(0,o.A)(z,2),j=P[0],T=P[1],D=(0,l.useState)(60),I=(0,o.A)(D,2),R=I[0],M=I[1],L=(0,l.useState)(0),F=(0,o.A)(L,2),B=F[0],_=F[1],H=(0,l.useRef)(0),W=(0,l.useRef)(0),N=(0,l.useRef)(performance.now()),U=(0,l.useRef)(new Map),V=(0,l.useRef)(null),J=(0,l.useCallback)((function(){if(!m||!E||0===n.length)return{start:0,end:n.length};var e=Math.floor(b/s),t=Math.min(e+Math.ceil(i/s)+p,n.length);return{start:Math.max(0,e-p),end:t}}),[b,s,i,p,n.length,m,E]);(0,l.useEffect)((function(){var e=J();O(e)}),[J]);var q=(0,l.useCallback)((0,ut.throttle)((function(e){e.target&&h(e.target.scrollTop)}),16),[]),G=(0,l.useMemo)((function(){return m?n.slice(S.start,S.end).map((function(e,t){return{component:e,index:S.start+t}})):n.map((function(e,t){return{component:e,index:t}}))}),[n,S,m]),X=(0,l.useCallback)((function(e,t){var r="".concat(e,"_").concat(JSON.stringify(n.find((function(t){return t.id===e}))));if(U.current.has(r))return U.current.get(r);var o=t();if(U.current.set(r,o),U.current.size>100){var a=U.current.keys().next().value;U.current.delete(a)}return o}),[n]),Y=(0,l.useCallback)((function(){g&&(H.current=performance.now())}),[g]),Z=(0,l.useCallback)((function(){if(g&&H.current>0){var e=performance.now()-H.current;T(e),H.current=0}}),[g]);(0,l.useEffect)((function(){if(g){var e,t=function(){var n=performance.now(),r=n-N.current;if(r>=1e3){var o=Math.round(1e3*W.current/r);M(o),W.current=0,N.current=n}else W.current++;e=requestAnimationFrame(t)};return e=requestAnimationFrame(t),function(){e&&cancelAnimationFrame(e)}}}),[g]),(0,l.useEffect)((function(){if(g&&performance.memory){var e=function(){var e=performance.memory,t=Math.round(e.usedJSHeapSize/1024/1024);_(t)},t=setInterval(e,5e3);return e(),function(){return clearInterval(t)}}}),[g]),(0,l.useEffect)((function(){if(m)return V.current=new IntersectionObserver((function(e){e.forEach((function(e){e.isIntersecting&&e.target.dataset.componentId}))}),{root:E,rootMargin:"".concat(p*s,"px"),threshold:.1}),function(){V.current&&V.current.disconnect()}}),[E,p,s,m]),(0,l.useEffect)((function(){var e=new Set(n.map((function(e){return e.id})));new Set(Array.from(U.current.keys()).map((function(e){return e.split("_")[0]}))).forEach((function(t){e.has(t)||Array.from(U.current.keys()).filter((function(e){return e.startsWith(t)})).forEach((function(e){return U.current.delete(e)}))}))}),[n]);var $=(0,l.useCallback)((function(){return m?{ref:w,onScroll:q,style:{height:i,overflow:"auto",position:"relative"}}:{}}),[m,i,q]),Q=(0,l.useCallback)((function(){if(!m)return{before:{},after:{}};var e=n.length*s;return{before:{style:{height:S.start*s,width:"100%"}},after:{style:{height:e-S.end*s,width:"100%"}}}}),[m,n.length,s,S]);return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?dt(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):dt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({visibleComponents:G,visibleRange:S,getContainerProps:$,getSpacerProps:Q,renderTime:j,frameRate:R,memoryUsage:B,startRenderMeasurement:Y,endRenderMeasurement:Z,getCachedComponent:X},{clearCache:function(){return U.current.clear()},getCacheSize:function(){return U.current.size},getPerformanceMetrics:function(){return{renderTime:j,frameRate:R,memoryUsage:B,cacheSize:U.current.size,visibleComponents:G.length,totalComponents:n.length}},shouldRender:function(e){if(!m)return!0;var t=n.findIndex((function(t){return t.id===e}));return t>=S.start&&t<S.end}})}({components:Oe(),containerHeight:Ae.height,itemHeight:"mobile"===O?60:"tablet"===O?80:100,enableVirtualization:n.length>20,enablePerformanceMonitoring:!0})),Pe=ze.visibleComponents,je=ze.getContainerProps,Te=ze.getSpacerProps,De=(ze.renderTime,ze.frameRate,ze.memoryUsage,ze.startRenderMeasurement),Ie=ze.endRenderMeasurement,Re=ze.getCachedComponent,Me=(0,l.useCallback)((function(e){z(e);var t=jt[e];t.scale!==R&&M(t.scale)}),[R]),Le=(0,l.useCallback)((function(e,t){A&&(Se(e,t,!(arguments.length>2&&void 0!==arguments[2])||arguments[2]),de(!0),ye(new Date),setTimeout((function(){return de(!1)}),500))}),[A,Se]);(0,l.useEffect)((function(){we!==pe&&de(we),Ce&&Ce!==ge&&ye(Ce)}),[we,Ce,pe,ge]),(0,l.useEffect)((function(){return function(){be.current&&clearTimeout(be.current)}}),[]);var Fe=(0,l.useCallback)((function(e){e.preventDefault(),ee(!0)}),[]),Be=(0,l.useCallback)((function(e){e.preventDefault(),e.dataTransfer.dropEffect="copy",v&&v(e)}),[v]),_e=(0,l.useCallback)((function(e){e.preventDefault(),e.currentTarget.contains(e.relatedTarget)||(ee(!1),oe(null),b&&b(e))}),[b]),He=(0,l.useCallback)((function(e){if(e.preventDefault(),ee(!1),oe(null),y){var t,n=null===(t=ve.current)||void 0===t?void 0:t.getBoundingClientRect();if(n){var r=(e.clientX-n.left)/R,o=(e.clientY-n.top)/R,a=Y?Math.round(r/N)*N:r,l=Y?Math.round(o/N)*N:o;y(e,{x:a,y:l})}}}),[y,R,Y,N]),We=(0,l.useCallback)((function(e,t){e.preventDefault(),e.stopPropagation(),oe(t)}),[]),Ne=(0,l.useCallback)((function(e,t){e.preventDefault(),e.stopPropagation(),e.currentTarget.contains(e.relatedTarget)||oe(null)}),[]),Ue=(0,l.useCallback)((function(e,t){var n=e.component||e;return void 0!==e.index&&e.index,Re(n.id,(function(){De();var e=n.id===g,t=ie===n.id,r=re===n.id,o=function(){var e,t,r,o,a,i,c,s,p,d,m,g={fontSize:"mobile"===O?"14px":"tablet"===O?"15px":"16px",padding:"mobile"===O?"8px":"tablet"===O?"12px":"16px"};switch(n.type){case"text":return l.createElement(Pt,{style:g},(null===(e=n.props)||void 0===e?void 0:e.content)||"Sample text");case"button":return l.createElement(u.Ay,{type:(null===(t=n.props)||void 0===t?void 0:t.type)||"default",size:"mobile"===O?"small":"middle",style:{fontSize:g.fontSize}},(null===(r=n.props)||void 0===r?void 0:r.text)||"Button");case"header":return l.createElement(zt,{level:(null===(o=n.props)||void 0===o?void 0:o.level)||("mobile"===O?4:2),style:g},(null===(a=n.props)||void 0===a?void 0:a.text)||"Header");case"card":return l.createElement(P.A,{title:(null===(i=n.props)||void 0===i?void 0:i.title)||"Card Title",size:"mobile"===O?"small":"default",style:{fontSize:g.fontSize}},(null===(c=n.props)||void 0===c?void 0:c.content)||"Card content");case"image":return l.createElement("img",{src:(null===(s=n.props)||void 0===s?void 0:s.src)||"https://via.placeholder.com/150",alt:(null===(p=n.props)||void 0===p?void 0:p.alt)||"Image",style:{maxWidth:"100%",height:"auto",borderRadius:"mobile"===O?"4px":"6px"}});case"divider":return l.createElement(Xe.A,{style:g},null===(d=n.props)||void 0===d?void 0:d.text);case"input":return l.createElement(S.A,{placeholder:(null===(m=n.props)||void 0===m?void 0:m.placeholder)||"Enter text",disabled:!f,size:"mobile"===O?"small":"middle",style:g});case"form":return l.createElement(Ye.A,{layout:"vertical",size:"mobile"===O?"small":"middle"},l.createElement(Ye.A.Item,{label:"Sample Field"},l.createElement(S.A,{placeholder:"Sample input",disabled:!f,style:g})));case"table":return l.createElement(Ze.A,{columns:[{title:"Name",dataIndex:"name",key:"name"},{title:"Age",dataIndex:"age",key:"age"}],dataSource:[{key:"1",name:"John",age:32},{key:"2",name:"Jane",age:28}],size:"mobile"===O?"small":"middle",scroll:"mobile"===O?{x:!0}:void 0});default:return l.createElement("div",{style:{padding:g.padding,border:"1px dashed #ccc",textAlign:"center",fontSize:g.fontSize,borderRadius:"mobile"===O?"4px":"6px"}},n.type," Component")}};return l.createElement(Bt,{key:n.id,isSelected:e,previewMode:f,isDragOver:r,onClick:function(e){e.stopPropagation(),!f&&a&&a(n)},onMouseEnter:function(){return ce(n.id)},onMouseLeave:function(){return ce(null)},onDragOver:function(e){return We(e,n.id)},onDragLeave:function(e){return Ne(e,n.id)},style:{padding:"mobile"===O?"8px":"tablet"===O?"12px":"16px",position:"relative",margin:"mobile"===O?"4px 0":"8px 0"}},l.createElement(o,null),!f&&(e||t)&&l.createElement(_t,{visible:e||t},l.createElement(s.A,{title:"Edit"},l.createElement(Ht,{icon:l.createElement(tt.A,null),size:"small",onClick:function(e){e.stopPropagation(),A&&Le(n.id,{editing:!0})}})),l.createElement(s.A,{title:"Copy"},l.createElement(Ht,{icon:l.createElement(nt.A,null),size:"small",onClick:function(e){if(e.stopPropagation(),A){var t=Ot(Ot({},n),{},{id:Date.now().toString()});Le(t.id,t)}}})),"desktop"===O&&l.createElement(l.Fragment,null,l.createElement(s.A,{title:"Move Up"},l.createElement(Ht,{icon:l.createElement(rt.A,null),size:"small",onClick:function(e){e.stopPropagation(),d&&d(n.id,"up"),A&&Le(n.id,{moved:"up"})}})),l.createElement(s.A,{title:"Move Down"},l.createElement(Ht,{icon:l.createElement(ot.A,null),size:"small",onClick:function(e){e.stopPropagation(),d&&d(n.id,"down"),A&&Le(n.id,{moved:"down"})}}))),l.createElement(s.A,{title:"Delete"},l.createElement(Ht,{icon:l.createElement(at.A,null),size:"small",danger:!0,onClick:function(e){e.stopPropagation(),i&&i(n.id),A&&Le(n.id,{deleted:!0})}}))))}))}),[g,ie,re,O,f,A,Re,De,Ie,Le,d,i]);return l.createElement(Tt,null,l.createElement(Dt,null,l.createElement(p.A,null,l.createElement(Pt,{strong:!0},"Preview"),l.createElement(Xe.A,{type:"vertical"}),l.createElement(It,null,Object.entries(jt).map((function(e){var t=(0,o.A)(e,2),n=t[0],r=t[1];return l.createElement(Rt,{key:n,size:"small",active:O===n,onClick:function(){return Me(n)},icon:r.icon},!f&&r.name)})))),l.createElement(p.A,null,A&&l.createElement(l.Fragment,null,l.createElement(T.A,{status:ke?"success":"error",text:ke?"Live":"Offline"}),(pe||we)&&l.createElement(lt.A,{spin:!0}),!1,l.createElement(Xe.A,{type:"vertical"})),!f&&l.createElement(l.Fragment,null,l.createElement(s.A,{title:"Zoom Out"},l.createElement(u.Ay,{icon:l.createElement(it.A,null),size:"small",onClick:function(){return M((function(e){return Math.max(e-.1,.5)}))},disabled:R<=.5})),l.createElement(Pt,{style:{minWidth:40,textAlign:"center"}},Math.round(100*R),"%"),l.createElement(s.A,{title:"Zoom In"},l.createElement(u.Ay,{icon:l.createElement(ct.A,null),size:"small",onClick:function(){return M((function(e){return Math.min(e+.1,2)}))},disabled:R>=2})),l.createElement(u.Ay,{size:"small",onClick:function(){return M(xe.scale)}},"Reset"),l.createElement(Xe.A,{type:"vertical"}))),!f&&l.createElement(p.A,null,l.createElement(s.A,{title:"Toggle Grid"},l.createElement(j.A,{checked:B,onChange:_,checkedChildren:l.createElement(st.A,null),unCheckedChildren:l.createElement(st.A,null),size:"small"})),l.createElement(s.A,{title:"Toggle Rulers"},l.createElement(j.A,{checked:J,onChange:q,size:"small"})),l.createElement(s.A,{title:"Snap to Grid"},l.createElement(j.A,{checked:Y,onChange:Z,size:"small"}))),A&&ge&&l.createElement(p.A,null,l.createElement(Pt,{type:"secondary",style:{fontSize:"12px"}},"Updated: ",ge.toLocaleTimeString()))),l.createElement(Ft,{showGrid:B&&!f&&"desktop"===O,gridSize:N,onDragEnter:Fe,onDragOver:Be,onDragLeave:_e,onDrop:He},J&&!f&&"desktop"===O&&l.createElement(l.Fragment,null,l.createElement(Nt,{orientation:"horizontal"}),l.createElement(Nt,{orientation:"vertical"})),l.createElement(Mt,{deviceType:O,frame:xe.frame},l.createElement(Lt,(0,qe.A)({},je(),{ref:ve,deviceWidth:Ae.width,deviceHeight:Ae.height,deviceType:O,scale:R,onClick:function(){return a&&a(null)}}),!1,l.createElement("div",Te().before),Pe.length>0?Pe.map((function(e,t){return Ue(e,t)})):0===n.length?l.createElement($e.A,{description:l.createElement("span",null,"No components added yet.",l.createElement("br",null),f?"Add components to see them here.":"Drag components from the palette to get started."),style:{margin:"mobile"===O?"50px 20px":"100px 0",fontSize:"mobile"===O?"14px":"16px"}}):null,l.createElement("div",Te().after))),l.createElement(Wt,{visible:K&&!f,isActive:K})))};var Vt,Jt,qt,Gt,Xt,Yt,Zt,$t,Qt,Kt,en,tn,nn,rn,on=n(1295),an=n(9552),ln=(0,A.i7)(Vt||(Vt=(0,a.A)(["\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.7;\n    transform: scale(1.05);\n  }\n"]))),cn=(0,A.i7)(Jt||(Jt=(0,a.A)(["\n  0%, 100% { transform: translateX(0); }\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }\n  20%, 40%, 60%, 80% { transform: translateX(3px); }\n"]))),sn=(0,A.i7)(qt||(qt=(0,a.A)(["\n  0%, 20%, 53%, 80%, 100% {\n    transform: translate3d(0, 0, 0);\n  }\n  40%, 43% {\n    transform: translate3d(0, -8px, 0);\n  }\n  70% {\n    transform: translate3d(0, -4px, 0);\n  }\n  90% {\n    transform: translate3d(0, -2px, 0);\n  }\n"]))),un=(0,A.i7)(Gt||(Gt=(0,a.A)(["\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n"]))),pn=(0,A.i7)(Xt||(Xt=(0,a.A)(["\n  0%, 100% {\n    box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);\n  }\n  50% {\n    box-shadow: 0 0 20px rgba(24, 144, 255, 0.8);\n  }\n"]))),dn=A.Ay.div(Yt||(Yt=(0,a.A)(["\n  position: absolute;\n  width: 100%;\n  height: 4px;\n  background: ",";\n  border-radius: 2px;\n  z-index: 1000;\n  animation: "," 1.5s ease-in-out infinite;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: -6px;\n    top: -2px;\n    width: 8px;\n    height: 8px;\n    background: ",";\n    border-radius: 50%;\n    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    right: -6px;\n    top: -2px;\n    width: 8px;\n    height: 8px;\n    background: ",";\n    border-radius: 50%;\n    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);\n  }\n"])),(function(e){return e.isValid?"#52c41a":"#ff4d4f"}),ln,(function(e){return e.isValid?"#52c41a":"#ff4d4f"}),(function(e){return e.isValid?"#52c41a":"#ff4d4f"})),mn=A.Ay.div(Zt||(Zt=(0,a.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed ",";\n  border-radius: 8px;\n  background: ",";\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n  animation: "," 0.3s ease-out;\n  \n  ","\n  \n  ","\n"])),(function(e){return e.isValid?"#52c41a":"#ff4d4f"}),(function(e){return e.isValid?"rgba(82, 196, 26, 0.1)":"rgba(255, 77, 79, 0.1)"}),un,(function(e){return e.isValid&&(0,A.AH)($t||($t=(0,a.A)(["\n    animation: "," 1.5s ease-in-out infinite;\n  "])),ln)}),(function(e){return!e.isValid&&(0,A.AH)(Qt||(Qt=(0,a.A)(["\n    animation: "," 0.5s ease-in-out;\n  "])),cn)})),fn=A.Ay.div(Kt||(Kt=(0,a.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 16px;\n  background: white;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  font-weight: 600;\n  color: ",";\n  animation: "," 0.3s ease-out 0.1s both;\n"])),(function(e){return e.isValid?"#52c41a":"#ff4d4f"}),un),gn=A.Ay.div(en||(en=(0,a.A)(["\n  position: fixed;\n  pointer-events: none;\n  z-index: 9999;\n  opacity: 0.7;\n  transform: rotate(5deg) scale(0.9);\n  filter: blur(1px);\n  transition: all 0.1s ease-out;\n  border: 2px solid #1890ff;\n  border-radius: 4px;\n  background: rgba(255, 255, 255, 0.9);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n"]))),yn=A.Ay.div(tn||(tn=(0,a.A)(["\n  padding: 8px 12px;\n  background: white;\n  border: 2px solid #1890ff;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n  color: #1890ff;\n  animation: "," 2s ease-in-out infinite;\n"])),pn),vn=A.Ay.div(nn||(nn=(0,a.A)(["\n  position: absolute;\n  top: -2px;\n  left: -2px;\n  right: -2px;\n  bottom: -2px;\n  border: 2px solid #1890ff;\n  border-radius: 6px;\n  background: rgba(24, 144, 255, 0.05);\n  pointer-events: none;\n  z-index: 10;\n  animation: "," 0.2s ease-out;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: -4px;\n    left: -4px;\n    right: -4px;\n    bottom: -4px;\n    border: 1px solid rgba(24, 144, 255, 0.3);\n    border-radius: 8px;\n    animation: "," 2s ease-in-out infinite;\n  }\n"])),un,ln),bn=A.Ay.div(rn||(rn=(0,a.A)(["\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: #52c41a;\n  color: white;\n  padding: 8px 12px;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-weight: 600;\n  z-index: 1001;\n  animation: "," 0.6s ease-out;\n  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);\n"])),sn),hn=function(e){var t=e.position,n=e.isValid,r=void 0===n||n,o=e.visible;return void 0!==o&&o&&t?l.createElement(dn,{isValid:r,style:{top:t.y,left:t.x,width:t.width||"100%"}}):null},xn=function(e){var t=e.isValid,n=void 0===t||t,r=e.visible,o=void 0!==r&&r,a=e.message;return o?l.createElement(mn,{isValid:n},l.createElement(fn,{isValid:n},n?l.createElement(on.A,null):l.createElement(an.A,null),a||(n?"Drop here to add component":"Invalid drop target"))):null},An=function(e){var t=e.visible,n=void 0!==t&&t,r=e.position,o=e.children,a=e.componentType,i=(0,l.useRef)(null);return(0,l.useEffect)((function(){i.current&&n&&r&&(i.current.style.left="".concat(r.x,"px"),i.current.style.top="".concat(r.y,"px"))}),[n,r]),n?l.createElement(gn,{ref:i},o||l.createElement(yn,null,l.createElement($.A,null),a||"Component")):null},En=function(e){var t=e.visible,n=void 0!==t&&t,r=e.targetRef,o=(0,l.useRef)(null);return(0,l.useEffect)((function(){if(o.current&&null!=r&&r.current&&n){var e=r.current.getBoundingClientRect(),t=o.current;t.style.position="fixed",t.style.top="".concat(e.top,"px"),t.style.left="".concat(e.left,"px"),t.style.width="".concat(e.width,"px"),t.style.height="".concat(e.height,"px")}}),[n,r]),n?l.createElement(vn,{ref:o}):null},wn=function(e){var t=e.visible,n=void 0!==t&&t,r=e.message,o=void 0===r?"Component added!":r;return n?l.createElement(bn,null,l.createElement(on.A,null),o):null};const Cn=function(e){var t=e.isDragging,n=void 0!==t&&t,r=e.isOver,o=void 0!==r&&r,a=e.isValid,i=void 0===a||a,c=e.dropPosition,s=e.ghostPosition,u=e.hoveredElement,p=e.draggedComponent,d=e.showSuccess,m=void 0!==d&&d,f=e.successMessage,g=e.dropMessage,y=e.children;return l.createElement(l.Fragment,null,l.createElement(hn,{position:c,isValid:i,visible:n&&o&&c}),l.createElement(xn,{isValid:i,visible:n&&o,message:g}),l.createElement(An,{visible:n,position:s,componentType:null==p?void 0:p.type}),l.createElement(En,{visible:!n&&u,targetRef:u}),l.createElement(wn,{visible:m,message:f}),y)};var kn,Sn,On,zn,Pn=n(7206),jn=n(8597),Tn=n(4046),Dn=n(5763),In=n(1190),Rn=n(4463),Mn=n(2630),Ln=A.Ay.div(kn||(kn=(0,a.A)(["\n  position: fixed;\n  z-index: 10000;\n  background: white;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  border: 1px solid #e8e8e8;\n  min-width: 180px;\n  overflow: hidden;\n  \n  .ant-menu {\n    border: none;\n    box-shadow: none;\n  }\n  \n  .ant-menu-item {\n    margin: 0;\n    padding: 8px 16px;\n    height: auto;\n    line-height: 1.4;\n    \n    &:hover {\n      background: #f0f2f5;\n    }\n    \n    &.ant-menu-item-disabled {\n      color: #bfbfbf;\n      cursor: not-allowed;\n      \n      &:hover {\n        background: transparent;\n      }\n    }\n  }\n  \n  .ant-menu-item-icon {\n    margin-right: 8px;\n    font-size: 14px;\n  }\n"]))),Fn=A.Ay.div(Sn||(Sn=(0,a.A)(["\n  padding: 4px 0;\n  \n  &:not(:last-child) {\n    border-bottom: 1px solid #f0f0f0;\n  }\n"]))),Bn=A.Ay.div(On||(On=(0,a.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n"]))),_n=A.Ay.span(zn||(zn=(0,a.A)(["\n  font-size: 11px;\n  color: #999;\n  margin-left: 16px;\n"])));const Hn=function(e){var t=e.visible,n=e.x,r=e.y,o=e.onClose,a=e.selectedComponent,i=e.selectedComponents,c=void 0===i?[]:i,s=e.onCopy,u=e.onPaste,p=e.onDelete,d=e.onEdit,m=e.onDuplicate,f=e.onMoveUp,g=e.onMoveDown,b=e.onToggleVisibility,h=e.onToggleLock,x=e.onGroup,A=e.onUngroup,E=e.onCopyStyle,w=e.onPasteStyle,C=e.onProperties,k=e.clipboardHasData,S=void 0!==k&&k,O=e.canMoveUp,z=void 0===O||O,P=e.canMoveDown,j=void 0===P||P,T=e.canGroup,D=void 0!==T&&T,I=e.canUngroup,R=void 0!==I&&I,M=(0,l.useRef)(null);if((0,l.useEffect)((function(){if(t&&M.current){var e=M.current,o=e.getBoundingClientRect(),a=window.innerWidth,l=window.innerHeight,i=n,c=r;n+o.width>a&&(i=a-o.width-10),r+o.height>l&&(c=l-o.height-10),e.style.left="".concat(Math.max(10,i),"px"),e.style.top="".concat(Math.max(10,c),"px")}}),[t,n,r]),(0,l.useEffect)((function(){var e=function(e){"Escape"===e.key&&t&&o()};return document.addEventListener("keydown",e),function(){return document.removeEventListener("keydown",e)}}),[t,o]),!t)return null;var L=c.length>1,F=a||c.length>0,B=[{section:"edit",items:[{key:"edit",icon:l.createElement(tt.A,null),label:"Edit Properties",shortcut:"Enter",disabled:!F||L,onClick:function(){null==d||d(a),o()}},{key:"copy",icon:l.createElement(nt.A,null),label:L?"Copy ".concat(c.length," Components"):"Copy",shortcut:"Ctrl+C",disabled:!F,onClick:function(){null==s||s(L?c:a),o()}},{key:"paste",icon:l.createElement(nt.A,{style:{transform:"scaleX(-1)"}}),label:"Paste",shortcut:"Ctrl+V",disabled:!S,onClick:function(){null==u||u(),o()}},{key:"duplicate",icon:l.createElement(nt.A,null),label:L?"Duplicate Selection":"Duplicate",shortcut:"Ctrl+D",disabled:!F,onClick:function(){null==m||m(L?c:a),o()}}]},{section:"arrange",items:[{key:"move-up",icon:l.createElement(rt.A,null),label:"Move Up",shortcut:"Ctrl+↑",disabled:!F||!z,onClick:function(){L?c.forEach((function(e){return null==f?void 0:f(e)})):null==f||f(a),o()}},{key:"move-down",icon:l.createElement(ot.A,null),label:"Move Down",shortcut:"Ctrl+↓",disabled:!F||!j,onClick:function(){L?c.forEach((function(e){return null==g?void 0:g(e)})):null==g||g(a),o()}}]},{section:"visibility",items:[{key:"toggle-visibility",icon:!1!==(null==a?void 0:a.visible)?l.createElement(jn.A,null):l.createElement(y.A,null),label:!1!==(null==a?void 0:a.visible)?"Hide":"Show",shortcut:"Ctrl+H",disabled:!F,onClick:function(){L?c.forEach((function(e){return null==b?void 0:b(e)})):null==b||b(a),o()}},{key:"toggle-lock",icon:null!=a&&a.locked?l.createElement(Tn.A,null):l.createElement(Dn.A,null),label:null!=a&&a.locked?"Unlock":"Lock",shortcut:"Ctrl+L",disabled:!F,onClick:function(){L?c.forEach((function(e){return null==h?void 0:h(e)})):null==h||h(a),o()}}]},{section:"group",items:[{key:"group",icon:l.createElement(In.A,null),label:"Group",shortcut:"Ctrl+G",disabled:!D||c.length<2,onClick:function(){null==x||x(c),o()}},{key:"ungroup",icon:l.createElement(Rn.A,null),label:"Ungroup",shortcut:"Ctrl+Shift+G",disabled:!R,onClick:function(){null==A||A(a),o()}}]},{section:"style",items:[{key:"copy-style",icon:l.createElement(Mn.A,null),label:"Copy Style",disabled:!F||L,onClick:function(){null==E||E(a),o()}},{key:"paste-style",icon:l.createElement(Mn.A,{style:{transform:"scaleX(-1)"}}),label:"Paste Style",disabled:!F,onClick:function(){L?c.forEach((function(e){return null==w?void 0:w(e)})):null==w||w(a),o()}}]},{section:"actions",items:[{key:"properties",icon:l.createElement(v.A,null),label:"Properties",shortcut:"F4",disabled:!F||L,onClick:function(){null==C||C(a),o()}},{key:"delete",icon:l.createElement(at.A,null),label:L?"Delete ".concat(c.length," Components"):"Delete",shortcut:"Delete",disabled:!F,danger:!0,onClick:function(){L?c.forEach((function(e){return null==p?void 0:p(e)})):null==p||p(a),o()}}]}];return l.createElement(Ln,{ref:M,style:{left:n,top:r},onClick:function(e){return e.stopPropagation()}},l.createElement(Pn.A,{mode:"vertical",selectable:!1},B.map((function(e,t){return l.createElement(Fn,{key:e.section},e.items.map((function(e){return l.createElement(Pn.A.Item,{key:e.key,icon:e.icon,disabled:e.disabled,danger:e.danger,onClick:e.onClick},l.createElement(Bn,null,l.createElement("span",null,e.label),e.shortcut&&l.createElement(_n,null,e.shortcut)))})))}))))};function Wn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Nn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Wn(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Wn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Un,Vn,Jn,qn,Gn,Xn,Yn=n(4816);function Zn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function $n(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Zn(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Zn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Qn=i.A.Sider,Kn=i.A.Content,er=(0,A.Ay)(i.A)(Un||(Un=(0,a.A)(["\n  height: 100vh;\n  background: #f5f5f5;\n"]))),tr=A.Ay.div(Vn||(Vn=(0,a.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 16px;\n  background: white;\n  border-bottom: 1px solid #e8e8e8;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  z-index: 100;\n"]))),nr=A.Ay.div(Jn||(Jn=(0,a.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"]))),rr=(0,A.Ay)(Qn)(qn||(qn=(0,a.A)(["\n  background: white;\n  border-right: 1px solid #e8e8e8;\n  overflow: auto;\n  \n  .ant-layout-sider-children {\n    padding: 16px;\n  }\n"]))),or=(0,A.Ay)(Kn)(Gn||(Gn=(0,a.A)(["\n  display: flex;\n  flex-direction: column;\n  background: #f5f5f5;\n  position: relative;\n"]))),ar=A.Ay.div(Xn||(Xn=(0,a.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  backdrop-filter: blur(2px);\n"])));const lr=function(){var e,t,n,r,a=(0,x.wA)(),A=(0,x.d4)((function(e){var t;return(null===(t=e.app)||void 0===t?void 0:t.components)||[]})),E=(0,l.useState)(!1),w=(0,o.A)(E,2),C=w[0],k=w[1],S=(0,l.useState)(!1),O=(0,o.A)(S,2),z=O[0],P=O[1],j=(0,l.useState)(null),T=(0,o.A)(j,2),D=T[0],I=T[1],R=(0,l.useState)(!1),M=(0,o.A)(R,2),L=M[0],F=M[1],B=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,n=(0,l.useState)([e]),r=(0,o.A)(n,2),a=r[0],i=r[1],c=(0,l.useState)(0),s=(0,o.A)(c,2),u=s[0],p=s[1],d=(0,l.useRef)(!1),m=a[u],f=(0,l.useCallback)((function(e){d.current?d.current=!1:(i((function(n){var r=n.slice(0,u+1);return r.push(e),r.length>t?(r.shift(),r):r})),p((function(e){return Math.min(e+1,t-1)})))}),[u,t]),g=(0,l.useCallback)((function(){return u>0?(d.current=!0,p((function(e){return e-1})),a[u-1]):m}),[u,a,m]),y=(0,l.useCallback)((function(){return u<a.length-1?(d.current=!0,p((function(e){return e+1})),a[u+1]):m}),[u,a,m]),v=u>0,b=u<a.length-1,h=(0,l.useCallback)((function(){i([m]),p(0)}),[m]),x=(0,l.useCallback)((function(){return{totalStates:a.length,currentIndex:u,canUndo:v,canRedo:b}}),[a.length,u,v,b]);return{state:m,pushState:f,undo:g,redo:y,canUndo:v,canRedo:b,clearHistory:h,getHistoryInfo:x}}(A),_=B.state,H=B.pushState,W=B.undo,N=B.redo,U=B.canUndo,V=B.canRedo,J=function(){var e=(0,l.useState)({visible:!1,x:0,y:0,items:[]}),t=(0,o.A)(e,2),n=t[0],r=t[1],a=(0,l.useCallback)((function(e,t){e.preventDefault(),r({visible:!0,x:e.clientX,y:e.clientY,items:t||[]})}),[]),i=(0,l.useCallback)((function(){r((function(e){return Nn(Nn({},e),{},{visible:!1})}))}),[]);return(0,l.useEffect)((function(){var e=function(){n.visible&&i()};return document.addEventListener("click",e),function(){document.removeEventListener("click",e)}}),[n.visible,i]),{contextMenu:n,showContextMenu:a,hideContextMenu:i}}(),q=J.contextMenu,G=J.showContextMenu,X=J.hideContextMenu,Y=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:200,t=(0,l.useState)(!1),n=(0,o.A)(t,2),r=n[0],a=n[1],i=(0,l.useState)(""),c=(0,o.A)(i,2),s=c[0],u=c[1],p=(0,l.useRef)(null),d=(0,l.useCallback)((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";p.current&&clearTimeout(p.current),p.current=setTimeout((function(){a(!0),u(t)}),e)}),[e]),m=(0,l.useCallback)((function(){p.current&&(clearTimeout(p.current),p.current=null),a(!1),u("")}),[]);return(0,l.useEffect)((function(){return function(){p.current&&clearTimeout(p.current)}}),[]),{isLoading:r,loadingMessage:s,startLoading:d,stopLoading:m}}(),Z=Y.isLoading,$=Y.loadingMessage,Q=Y.startLoading,K=Y.stopLoading,ee=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=(0,l.useState)(new Set),n=(0,o.A)(t,2),r=n[0],a=n[1],i=(0,l.useState)(-1),c=(0,o.A)(i,2),s=c[0],u=c[1],p=(0,l.useCallback)((function(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=e.findIndex((function(e){return e.id===t.id}));a((function(e){var r=new Set(n?e:[]);return r.has(t.id)?r.delete(t.id):r.add(t.id),r})),u(r)}),[e]),d=(0,l.useCallback)((function(t){var n=e.findIndex((function(e){return e.id===t.id}));if(-1!==s){var r=Math.min(s,n),o=Math.max(s,n);a((function(t){for(var n=new Set(t),a=r;a<=o;a++)e[a]&&n.add(e[a].id);return n}))}else p(t)}),[e,s,p]),m=(0,l.useCallback)((function(){a(new Set(e.map((function(e){return e.id}))))}),[e]),f=(0,l.useCallback)((function(){a(new Set),u(-1)}),[]),g=(0,l.useCallback)((function(e){return r.has(e)}),[r]),y=(0,l.useCallback)((function(){return e.filter((function(e){return r.has(e.id)}))}),[e,r]);return{selectedItems:Array.from(r),selectItem:p,selectRange:d,selectAll:m,clearSelection:f,isSelected:g,getSelectedItems:y,selectedCount:r.size}}(A),te=ee.selectedItems,ne=ee.selectItem,re=ee.clearSelection,oe=(ee.isSelected,e=(0,l.useState)(null),t=(0,o.A)(e,2),n=t[0],r=t[1],{copy:(0,l.useCallback)((function(e){r(e),navigator.clipboard&&"string"==typeof e&&navigator.clipboard.writeText(e).catch(console.error)}),[]),paste:(0,l.useCallback)((function(){return n}),[n]),clear:(0,l.useCallback)((function(){r(null)}),[]),hasData:null!==n,data:n}),ae=oe.copy,le=oe.paste,ie=oe.hasData,ce=(0,l.useRef)(null),se=(0,l.useCallback)((function(e,t,n){if(t){Q("Adding component...");try{var r={id:"".concat(t.type,"-").concat(Date.now()),type:t.type,props:$n({x:n.x,y:n.y},be(t.type)),createdAt:(new Date).toISOString()};a((0,Yn.addComponent)(r)),F(!0),setTimeout((function(){return F(!1)}),2e3),c.Ay.success("".concat(t.label||t.type," component added"))}catch(e){c.Ay.error("Failed to add component"),console.error("Error adding component:",e)}finally{K()}}}),[a,Q,K]),ue=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.onDrop,n=e.onDragStart,r=e.onDragEnd,a=e.onDragOver,i=e.onDragLeave,c=e.snapToGrid,s=void 0!==c&&c,u=e.gridSize,p=void 0===u?20:u,d=(e.showDropZones,e.acceptedTypes),m=void 0===d?["application/json"]:d,f=(0,l.useState)(!1),g=(0,o.A)(f,2),y=g[0],v=g[1],b=(0,l.useState)(!1),h=(0,o.A)(b,2),x=h[0],A=h[1],E=(0,l.useState)(null),w=(0,o.A)(E,2),C=w[0],k=w[1],S=(0,l.useState)({x:0,y:0}),O=(0,o.A)(S,2),z=O[0],P=O[1],j=(0,l.useState)(!0),T=(0,o.A)(j,2),D=T[0],I=T[1],R=(0,l.useState)(null),M=(0,o.A)(R,2),L=(M[0],M[1],(0,l.useRef)(null)),F=(0,l.useRef)(null),B=(0,l.useCallback)((function(e,t){if(v(!0),k(t),t&&e.dataTransfer.setData("application/json",JSON.stringify(t)),e.dataTransfer.effectAllowed="copy",F.current){var r=F.current.cloneNode(!0);r.style.position="absolute",r.style.top="-1000px",r.style.left="-1000px",r.style.opacity="0.8",r.style.transform="rotate(5deg) scale(0.9)",r.style.pointerEvents="none",r.style.zIndex="9999",document.body.appendChild(r),e.dataTransfer.setDragImage(r,50,25),setTimeout((function(){document.body.contains(r)&&document.body.removeChild(r)}),0)}n&&n(e,t)}),[n]),_=(0,l.useCallback)((function(e){v(!1),k(null),P({x:0,y:0}),I(!0),r&&r(e)}),[r]),H=(0,l.useCallback)((function(e){e.preventDefault(),A(!0)}),[]),W=(0,l.useCallback)((function(e){if(e.preventDefault(),L.current){var t=L.current.getBoundingClientRect(),n=e.clientX-t.left,r=e.clientY-t.top;s&&(n=Math.round(n/p)*p,r=Math.round(r/p)*p),P({x:n,y:r})}var o=e.dataTransfer.types[0],l=m.includes(o)||0===m.length;I(l),e.dataTransfer.dropEffect=l?"copy":"none",a&&a(e)}),[s,p,m,a]),N=(0,l.useCallback)((function(e){e.preventDefault(),e.currentTarget.contains(e.relatedTarget)||(A(!1),I(!0),i&&i(e))}),[i]),U=(0,l.useCallback)((function(e){e.preventDefault(),A(!1),I(!0);try{var n=e.dataTransfer.getData("application/json"),r=null;n&&(r=JSON.parse(n));var o=z;s&&(o={x:Math.round(z.x/p)*p,y:Math.round(z.y/p)*p}),t&&t(e,r,o)}catch(e){console.error("Error handling drop:",e)}}),[z,s,p,t]);return(0,l.useEffect)((function(){var e=L.current;if(e)return e.addEventListener("dragenter",H),e.addEventListener("dragover",W),e.addEventListener("dragleave",N),e.addEventListener("drop",U),function(){e.removeEventListener("dragenter",H),e.removeEventListener("dragover",W),e.removeEventListener("dragleave",N),e.removeEventListener("drop",U)}}),[H,W,N,U]),{isDragging:y,isOver:x,dragData:C,dropPosition:z,validDropZone:D,dropZoneRef:L,dragPreviewRef:F,handleDragStart:B,handleDragEnd:_,reset:function(){v(!1),A(!1),k(null),P({x:0,y:0}),I(!0)}}}({onDrop:se,snapToGrid:!0,gridSize:20}),pe=ue.isDragging,de=ue.isOver,me=ue.validDropZone,fe=ue.dropPosition,ge=ue.dropZoneRef,ye=ue.handleDragStart,ve=ue.handleDragEnd;(0,l.useEffect)((function(){JSON.stringify(A)!==JSON.stringify(_)&&H(A)}),[A,_,H]);var be=function(e){return{text:{content:"Sample text",fontSize:14},button:{text:"Button",type:"default"},header:{text:"Header",level:2},card:{title:"Card Title",content:"Card content"},image:{src:"https://via.placeholder.com/150",alt:"Image"},input:{placeholder:"Enter text"},form:{layout:"vertical"}}[e]||{}},he=(0,l.useCallback)((function(e){e?ne(e):re()}),[ne,re]),xe=(0,l.useCallback)((function(e){Q("Deleting component...");try{a((0,Yn.removeComponent)(e)),re(),c.Ay.success("Component deleted")}catch(e){c.Ay.error("Failed to delete component")}finally{K()}}),[a,re,Q,K]),Ae=(0,l.useCallback)((function(e,t){Q("Updating component...");try{a((0,Yn.updateComponent)(e,t)),c.Ay.success("Component updated")}catch(e){c.Ay.error("Failed to update component")}finally{K()}}),[a,Q,K]),Ee=(0,l.useCallback)((function(){W()&&c.Ay.success("Undone")}),[W]),we=(0,l.useCallback)((function(){N()&&c.Ay.success("Redone")}),[N]),Ce=(0,l.useCallback)((function(e){ae(e),c.Ay.success("Component copied")}),[ae]),ke=(0,l.useCallback)((function(){var e=le();if(e){var t=$n($n({},e),{},{id:"".concat(e.type,"-").concat(Date.now()),props:$n($n({},e.props),{},{x:(e.props.x||0)+20,y:(e.props.y||0)+20})});a((0,Yn.addComponent)(t)),c.Ay.success("Component pasted")}}),[le,a]);return(0,l.useCallback)((function(e,t){e.preventDefault(),G(e,[{key:"edit",label:"Edit",icon:"edit"},{key:"copy",label:"Copy",icon:"copy"},{key:"delete",label:"Delete",icon:"delete",danger:!0}])}),[G]),function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];(0,l.useEffect)((function(){var t=function(t){var n=t.ctrlKey,r=t.metaKey,o=t.shiftKey,a=t.altKey,l=t.key,i=[];(n||r)&&i.push("ctrl"),o&&i.push("shift"),a&&i.push("alt");var c=[].concat(i,[l.toLowerCase()]).join("+");e[c]&&(t.preventDefault(),e[c](t))};return document.addEventListener("keydown",t),function(){document.removeEventListener("keydown",t)}}),t)}({"ctrl+z":Ee,"ctrl+y":we,"ctrl+c":function(){te.length>0&&Ce(A.find((function(e){return e.id===te[0]})))},"ctrl+v":ke,delete:function(){te.length>0&&te.forEach((function(e){return xe(e)}))},escape:function(){re(),X()},f11:function(e){e.preventDefault(),P(!z)}},[te,A,Ee,we,Ce,ke,xe,re,X,z]),l.createElement(er,{ref:ce,className:z?"fullscreen":""},l.createElement(tr,null,l.createElement(nr,null,l.createElement(s.A,{title:"Undo (Ctrl+Z)"},l.createElement(u.Ay,{icon:l.createElement(m.A,null),disabled:!U,onClick:Ee})),l.createElement(s.A,{title:"Redo (Ctrl+Y)"},l.createElement(u.Ay,{icon:l.createElement(f.A,null),disabled:!V,onClick:we})),l.createElement(u.Ay,{icon:l.createElement(g.A,null)},"Save")),l.createElement(nr,null,l.createElement("span",{style:{fontSize:16,fontWeight:600}},"Enhanced Component Builder")),l.createElement(nr,null,l.createElement(s.A,{title:"Toggle Preview Mode"},l.createElement(u.Ay,{icon:l.createElement(y.A,null),type:C?"primary":"default",onClick:function(){return k(!C)}},"Preview")),l.createElement(s.A,{title:"Settings"},l.createElement(u.Ay,{icon:l.createElement(v.A,null)})),l.createElement(s.A,{title:"Fullscreen (F11)"},l.createElement(u.Ay,{icon:z?l.createElement(b.A,null):l.createElement(h.A,null),onClick:function(){return P(!z)}})))),l.createElement(i.A,null,!C&&l.createElement(rr,{width:300,theme:"light"},l.createElement(Je,{onAddComponent:function(e){var t={id:"".concat(e,"-").concat(Date.now()),type:e,props:be(e),createdAt:(new Date).toISOString()};a((0,Yn.addComponent)(t)),c.Ay.success("".concat(e," component added"))},onDragStart:function(e){I(e),ye(null,e)},onDragEnd:function(){I(null),ve()}})),l.createElement(or,{ref:ge},l.createElement(Ut,{components:A,onSelectComponent:he,onDeleteComponent:xe,onUpdateComponent:Ae,previewMode:C,selectedComponentId:te[0],onDrop:se}),Z&&l.createElement(ar,null,l.createElement(p.A,{direction:"vertical",align:"center"},l.createElement(d.A,{size:"large"}),l.createElement("span",null,$))))),l.createElement(Cn,{isDragging:pe,isOver:de,isValid:me,dropPosition:fe,draggedComponent:D,showSuccess:L,successMessage:"Component added successfully!"}),l.createElement(Hn,{visible:q.visible,x:q.x,y:q.y,onClose:X,selectedComponent:A.find((function(e){return e.id===te[0]})),selectedComponents:A.filter((function(e){return te.includes(e.id)})),onCopy:Ce,onPaste:ke,onDelete:xe,clipboardHasData:ie}))}},4309:(e,t,n)=>{n.r(t),n.d(t,{default:()=>Rr});var r,o,a,l,i=n(4467),c=n(467),s=n(5544),u=n(7528),p=n(4756),d=n.n(p),m=n(6540),f=n(1468),g=n(7852),y=n(3903),v=n(9237),b=n(1372),h=n(7046),x=n(261),A=n(3598),E=n(4816),w=n(6191),C=n(6020),k=n(8168),S=n(3986),O=n(3016),z=n(7142),P=n(7977),j=n(6531),T=n(9248),D=["value","onChange","min","max","step","unit","units","showSlider","showUnit","placeholder","tooltip","precision"],I=O.A.Text,R=w.styled.div(r||(r=(0,u.A)(["\n  width: 100%;\n"]))),M=w.styled.div(o||(o=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"]))),L=w.styled.div(a||(a=(0,u.A)(["\n  flex: 1;\n  margin-left: 8px;\n"]))),F=w.styled.select(l||(l=(0,u.A)(["\n  padding: 4px 8px;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  background: white;\n  font-size: 12px;\n  min-width: 50px;\n"])));const B=function(e){var t=e.value,n=e.onChange,r=e.min,o=void 0===r?0:r,a=e.max,l=void 0===a?100:a,i=e.step,c=void 0===i?1:i,u=e.unit,p=void 0===u?"px":u,d=e.units,f=void 0===d?["px","%","rem","em","vh","vw"]:d,g=e.showSlider,y=void 0!==g&&g,v=e.showUnit,b=void 0===v||v,h=e.placeholder,x=void 0===h?"Enter value":h,A=e.tooltip,E=e.precision,w=void 0===E?0:E,C=(0,S.A)(e,D),O=(0,m.useState)(0),B=(0,s.A)(O,2),_=B[0],H=B[1],W=(0,m.useState)(p),N=(0,s.A)(W,2),U=N[0],V=N[1];(0,m.useEffect)((function(){if(t){var e=J(t);H(e.number),V(e.unit)}}),[t]);var J=function(e){if("number"==typeof e)return{number:e,unit:U};if("string"==typeof e){var t=e.match(/^(-?\d*\.?\d+)(.*)$/);if(t)return{number:parseFloat(t[1]),unit:t[2]||U}}return{number:0,unit:U}},q=function(e,t){return b&&t?"".concat(e).concat(t):e},G=function(e){if(null!=e){H(e);var t=q(e,U);null==n||n(t)}};return m.createElement(R,null,m.createElement(M,null,m.createElement(z.A,(0,k.A)({value:_,onChange:G,min:o,max:l,step:c,precision:w,placeholder:x,style:{flex:1}},C)),b&&m.createElement(F,{value:U,onChange:function(e){var t=e.target.value;V(t);var r=q(_,t);null==n||n(r)}},f.map((function(e){return m.createElement("option",{key:e,value:e},e)}))),A&&m.createElement(P.A,{title:A},m.createElement(T.A,{style:{color:"#8c8c8c"}}))),y&&m.createElement(L,null,m.createElement(j.A,{value:_,onChange:G,min:o,max:l,step:c,tooltip:{formatter:function(e){return"".concat(e).concat(U)}}})),(void 0!==o||void 0!==l)&&m.createElement(I,{type:"secondary",style:{fontSize:"12px"}},"Range: ",o," - ",l))};var _,H,W,N,U,V,J=n(436),q=n(9249),G=n(6552),X=n(9779),Y=n(2702),Z=n(8073),$=n(7355),Q=n(778),K=["value","onChange","showPresets","showModeToggle","presets","placeholder"],ee=O.A.Text,te=w.styled.div(_||(_=(0,u.A)(["\n  width: 100%;\n"]))),ne=w.styled.div(H||(H=(0,u.A)(["\n  width: 32px;\n  height: 32px;\n  border-radius: 4px;\n  border: 1px solid #d9d9d9;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: ",";\n  position: relative;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(45deg, #ccc 25%, transparent 25%), \n                linear-gradient(-45deg, #ccc 25%, transparent 25%), \n                linear-gradient(45deg, transparent 75%, #ccc 75%), \n                linear-gradient(-45deg, transparent 75%, #ccc 75%);\n    background-size: 8px 8px;\n    background-position: 0 0, 0 4px, 4px -4px, -4px 0px;\n    z-index: -1;\n  }\n"])),(function(e){return e.color||"#ffffff"})),re=w.styled.div(W||(W=(0,u.A)(["\n  display: flex;\n  gap: 4px;\n  margin-bottom: 8px;\n"]))),oe=(0,w.styled)(q.Ay)(N||(N=(0,u.A)(["\n  font-size: 12px;\n  height: 24px;\n  padding: 0 8px;\n"]))),ae=w.styled.div(U||(U=(0,u.A)(["\n  display: grid;\n  grid-template-columns: repeat(8, 1fr);\n  gap: 4px;\n  margin-top: 8px;\n"]))),le=w.styled.div(V||(V=(0,u.A)(["\n  width: 24px;\n  height: 24px;\n  border-radius: 2px;\n  border: 1px solid #d9d9d9;\n  cursor: pointer;\n  background: ",";\n  \n  &:hover {\n    border-color: #1890ff;\n    transform: scale(1.1);\n  }\n"])),(function(e){return e.color}));const ie=function(e){var t=e.value,n=e.onChange,r=e.showPresets,o=void 0===r||r,a=e.showModeToggle,l=void 0===a||a,i=e.presets,c=void 0===i?[]:i,u=e.placeholder,p=void 0===u?"Enter color":u,d=(0,S.A)(e,K),f=(0,m.useState)("hex"),g=(0,s.A)(f,2),y=g[0],v=g[1],b=(0,m.useState)(t||"#ffffff"),h=(0,s.A)(b,2),x=h[0],A=h[1],E=(0,m.useState)(""),w=(0,s.A)(E,2),O=w[0],z=w[1],P=["#ffffff","#f5f5f5","#d9d9d9","#bfbfbf","#8c8c8c","#595959","#262626","#000000","#ff4d4f","#ff7a45","#ffa940","#ffec3d","#bae637","#73d13d","#40a9ff","#597ef7","#9254de","#f759ab","#ff85c0","#ffc069"].concat((0,J.A)(C.Ay.colors.primary?[C.Ay.colors.primary[500]]:[]),(0,J.A)(C.Ay.colors.secondary?[C.Ay.colors.secondary[500]]:[]),(0,J.A)(c));(0,m.useEffect)((function(){t&&(A(t),z(j(t,y)))}),[t,y]);var j=function(e,t){if(!e)return"";try{switch(t){case"hex":return e.startsWith("#")?e:"#".concat(e);case"rgb":return T(e);case"hsl":return D(e);default:return e}}catch(t){return e}},T=function(e){if(!e.startsWith("#"))return e;var t=parseInt(e.slice(1,3),16),n=parseInt(e.slice(3,5),16),r=parseInt(e.slice(5,7),16);return"rgb(".concat(t,", ").concat(n,", ").concat(r,")")},D=function(e){return e.startsWith("#")?"hsl(0, 0%, 50%)":e},I=m.createElement("div",{style:{width:280}},l&&m.createElement(m.Fragment,null,m.createElement(re,null,m.createElement(oe,{type:"hex"===y?"primary":"default",size:"small",onClick:function(){return v("hex")}},"HEX"),m.createElement(oe,{type:"rgb"===y?"primary":"default",size:"small",onClick:function(){return v("rgb")}},"RGB"),m.createElement(oe,{type:"hsl"===y?"primary":"default",size:"small",onClick:function(){return v("hsl")}},"HSL")),m.createElement(G.A,{style:{margin:"8px 0"}})),m.createElement(X.A,(0,k.A)({value:x,onChange:function(e){var t=e.toHexString();A(t),z(j(t,y)),null==n||n(t)},showText:!0,size:"large"},d)),o&&m.createElement(m.Fragment,null,m.createElement(G.A,{style:{margin:"8px 0"}}),m.createElement(ee,{strong:!0,style:{fontSize:"12px"}},"Color Presets"),m.createElement(ae,null,P.slice(0,24).map((function(e,t){return m.createElement(le,{key:t,color:e,onClick:function(){return A(t=e),z(j(t,y)),void(null==n||n(t));var t},title:e})})))));return m.createElement(te,null,m.createElement(Y.A.Compact,{style:{width:"100%"}},m.createElement(Z.A,{content:I,trigger:"click",placement:"bottomLeft"},m.createElement(ne,{color:x},m.createElement(Q.A,{style:{color:"rgba(0,0,0,0.3)"}}))),m.createElement($.A,{value:O,onChange:function(e){var t,r=e.target.value;z(r),(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(t=r)||/^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/.test(t)||/^hsl\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)$/.test(t))&&(A(r),null==n||n(r))},placeholder:p,style:{flex:1}})))};var ce,se,ue,pe,de,me,fe,ge,ye,ve,be,he=n(2284),xe=n(385),Ae=n(4046),Ee=["value","onChange","type","showVisual","showPresets","unit"];function we(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var Ce=O.A.Text,ke=w.styled.div(ce||(ce=(0,u.A)(["\n  width: 100%;\n"]))),Se=w.styled.div(se||(se=(0,u.A)(["\n  position: relative;\n  width: 120px;\n  height: 120px;\n  margin: 16px auto;\n  background: #f5f5f5;\n  border: 2px dashed #d9d9d9;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n"]))),Oe=w.styled.div(ue||(ue=(0,u.A)(["\n  width: 60px;\n  height: 60px;\n  background: #1890ff;\n  opacity: 0.3;\n  border-radius: 4px;\n  position: relative;\n"]))),ze=w.styled.div(pe||(pe=(0,u.A)(["\n  position: absolute;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n"]))),Pe=(0,w.styled)(ze)(de||(de=(0,u.A)(["\n  top: -30px;\n  left: 50%;\n  transform: translateX(-50%);\n"]))),je=(0,w.styled)(ze)(me||(me=(0,u.A)(["\n  right: -60px;\n  top: 50%;\n  transform: translateY(-50%);\n"]))),Te=(0,w.styled)(ze)(fe||(fe=(0,u.A)(["\n  bottom: -30px;\n  left: 50%;\n  transform: translateX(-50%);\n"]))),De=(0,w.styled)(ze)(ge||(ge=(0,u.A)(["\n  left: -60px;\n  top: 50%;\n  transform: translateY(-50%);\n"]))),Ie=w.styled.div(ye||(ye=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"]))),Re=w.styled.div(ve||(ve=(0,u.A)(["\n  display: flex;\n  gap: 4px;\n  margin-top: 8px;\n"]))),Me=(0,w.styled)(q.Ay)(be||(be=(0,u.A)(["\n  font-size: 12px;\n  height: 24px;\n  padding: 0 8px;\n"])));const Le=function(e){var t=e.value,n=e.onChange,r=e.type,o=void 0===r?"margin":r,a=e.showVisual,l=void 0===a||a,c=e.showPresets,u=void 0===c||c,p=e.unit,d=void 0===p?"px":p,f=((0,S.A)(e,Ee),(0,m.useState)({top:0,right:0,bottom:0,left:0})),g=(0,s.A)(f,2),y=g[0],v=g[1],b=(0,m.useState)(!1),h=(0,s.A)(b,2),x=h[0],A=h[1];(0,m.useEffect)((function(){if(t){var e=E(t);v(e)}}),[t]);var E=function(e){if(!e)return{top:0,right:0,bottom:0,left:0};if("object"===(0,he.A)(e))return{top:parseFloat(e.top)||0,right:parseFloat(e.right)||0,bottom:parseFloat(e.bottom)||0,left:parseFloat(e.left)||0};var t=e.toString().split(/\s+/).map((function(e){return parseFloat(e.replace(/[^\d.-]/g,""))||0}));switch(t.length){case 1:return{top:t[0],right:t[0],bottom:t[0],left:t[0]};case 2:return{top:t[0],right:t[1],bottom:t[0],left:t[1]};case 3:return{top:t[0],right:t[1],bottom:t[2],left:t[1]};case 4:return{top:t[0],right:t[1],bottom:t[2],left:t[3]};default:return{top:0,right:0,bottom:0,left:0}}},w=function(e){var t=e.top,n=e.right,r=e.bottom,o=e.left;return t===n&&n===r&&r===o?"".concat(t).concat(d):t===r&&o===n?"".concat(t).concat(d," ").concat(n).concat(d):"".concat(t).concat(d," ").concat(n).concat(d," ").concat(r).concat(d," ").concat(o).concat(d)},C=function(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?we(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):we(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},y);x?(r.top=t,r.right=t,r.bottom=t,r.left=t):r[e]=t,v(r),null==n||n(w(r))};return m.createElement(ke,null,m.createElement(Ie,null,m.createElement(Ce,{strong:!0,style:{fontSize:"12px"}},"margin"===o?"Margin":"Padding"),m.createElement(q.Ay,{type:x?"primary":"default",size:"small",icon:x?m.createElement(xe.A,null):m.createElement(Ae.A,null),onClick:function(){A(!x)},title:x?"Unlink sides":"Link all sides"})),l&&m.createElement(Se,null,m.createElement(Pe,null,m.createElement(z.A,{size:"small",value:y.top,onChange:function(e){return C("top",e||0)},style:{width:50},min:0})),m.createElement(je,null,m.createElement(z.A,{size:"small",value:y.right,onChange:function(e){return C("right",e||0)},style:{width:50},min:0})),m.createElement(Te,null,m.createElement(z.A,{size:"small",value:y.bottom,onChange:function(e){return C("bottom",e||0)},style:{width:50},min:0})),m.createElement(De,null,m.createElement(z.A,{size:"small",value:y.left,onChange:function(e){return C("left",e||0)},style:{width:50},min:0})),m.createElement(Oe,null)),m.createElement(Y.A,{direction:"vertical",style:{width:"100%"}},m.createElement(Ie,null,m.createElement(Ce,{style:{fontSize:"12px",minWidth:"30px"}},"Top:"),m.createElement(z.A,{value:y.top,onChange:function(e){return C("top",e||0)},style:{flex:1},min:0,addonAfter:d})),m.createElement(Ie,null,m.createElement(Ce,{style:{fontSize:"12px",minWidth:"30px"}},"Right:"),m.createElement(z.A,{value:y.right,onChange:function(e){return C("right",e||0)},style:{flex:1},min:0,addonAfter:d})),m.createElement(Ie,null,m.createElement(Ce,{style:{fontSize:"12px",minWidth:"30px"}},"Bottom:"),m.createElement(z.A,{value:y.bottom,onChange:function(e){return C("bottom",e||0)},style:{flex:1},min:0,addonAfter:d})),m.createElement(Ie,null,m.createElement(Ce,{style:{fontSize:"12px",minWidth:"30px"}},"Left:"),m.createElement(z.A,{value:y.left,onChange:function(e){return C("left",e||0)},style:{flex:1},min:0,addonAfter:d}))),u&&m.createElement(Re,null,m.createElement(Ce,{style:{fontSize:"12px",marginRight:"8px"}},"Presets:"),["0px","4px","8px","12px","16px","24px","32px","8px 16px","16px 24px"].map((function(e,t){return m.createElement(Me,{key:t,onClick:function(){return function(e){var t=E(e);v(t),null==n||n(w(t))}(e)},title:"Apply ".concat(e)},e)}))))};var Fe,Be,_e,He,We=n(4358),Ne=n(2454),Ue=["value","onChange","showPreview"],Ve=O.A.Text,Je=We.A.Option,qe=w.styled.div(Fe||(Fe=(0,u.A)(["\n  width: 100%;\n"]))),Ge=w.styled.div(Be||(Be=(0,u.A)(["\n  width: 100%;\n  height: 60px;\n  margin: 12px 0;\n  background: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 4px;\n  border: ",";\n"])),(function(e){return e.borderStyle||"1px solid #d9d9d9"})),Xe=w.styled.div(_e||(_e=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"]))),Ye=(0,w.styled)(Ve)(He||(He=(0,u.A)(["\n  min-width: 60px;\n  font-size: 12px;\n  font-weight: 500;\n"])));const Ze=function(e){var t=e.value,n=e.onChange,r=e.showPreview,o=void 0===r||r,a=((0,S.A)(e,Ue),(0,m.useState)("solid")),l=(0,s.A)(a,2),i=l[0],c=l[1],u=(0,m.useState)("1px"),p=(0,s.A)(u,2),d=p[0],f=p[1],g=(0,m.useState)("#d9d9d9"),y=(0,s.A)(g,2),v=y[0],b=y[1];(0,m.useEffect)((function(){if(t){var e=h(t);c(e.style),f(e.width),b(e.color)}}),[t]);var h=function(e){if(!e)return{style:"solid",width:"1px",color:"#d9d9d9"};if("object"===(0,he.A)(e))return{style:e.style||"solid",width:e.width||"1px",color:e.color||"#d9d9d9"};if("string"==typeof e){var t=e.split(/\s+/),n="1px",r="solid",o="#d9d9d9";return t.forEach((function(e){e.match(/^\d+(\.\d+)?(px|em|rem|%)$/)?n=e:["none","solid","dashed","dotted","double","groove","ridge","inset","outset"].includes(e)?r=e:(e.startsWith("#")||e.startsWith("rgb")||e.startsWith("hsl")||x(e))&&(o=e)})),{style:r,width:n,color:o}}return{style:"solid",width:"1px",color:"#d9d9d9"}},x=function(e){return["black","white","red","green","blue","yellow","orange","purple","pink","brown","gray","grey","transparent"].includes(e.toLowerCase())},A=function(e,t,n){return"none"===e?"none":"".concat(t," ").concat(e," ").concat(n)},E=A(i,d,v);return m.createElement(qe,null,m.createElement(Y.A,{direction:"vertical",style:{width:"100%"}},m.createElement(Xe,null,m.createElement(Ye,null,"Style:"),m.createElement(We.A,{value:i,onChange:function(e){c(e);var t=A(e,d,v);null==n||n(t)},style:{flex:1},size:"small"},[{value:"none",label:"None"},{value:"solid",label:"Solid"},{value:"dashed",label:"Dashed"},{value:"dotted",label:"Dotted"},{value:"double",label:"Double"},{value:"groove",label:"Groove"},{value:"ridge",label:"Ridge"},{value:"inset",label:"Inset"},{value:"outset",label:"Outset"}].map((function(e){return m.createElement(Je,{key:e.value,value:e.value},e.label)})))),"none"!==i&&m.createElement(m.Fragment,null,m.createElement(Xe,null,m.createElement(Ye,null,"Width:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:d,onChange:function(e){f(e);var t=A(i,e,v);null==n||n(t)},min:0,max:20,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement(Xe,null,m.createElement(Ye,null,"Color:"),m.createElement("div",{style:{flex:1}},m.createElement(ie,{value:v,onChange:function(e){b(e);var t=A(i,d,e);null==n||n(t)},placeholder:"Border color"})))),o&&m.createElement(m.Fragment,null,m.createElement(G.A,{style:{margin:"8px 0"}}),m.createElement(Ve,{style:{fontSize:"12px",marginBottom:"4px"}},"Preview:"),m.createElement(Ge,{borderStyle:E},m.createElement(Ne.A,{style:{fontSize:"24px",color:"#8c8c8c"}})),m.createElement(Ve,{type:"secondary",style:{fontSize:"11px",textAlign:"center"}},E))))};var $e,Qe,Ke,et,tt,nt=n(5039),rt=["value","onChange","showPreview"],ot=O.A.Text,at=w.styled.div($e||($e=(0,u.A)(["\n  width: 100%;\n"]))),lt=w.styled.div(Qe||(Qe=(0,u.A)(["\n  width: 100%;\n  height: 80px;\n  margin: 12px 0;\n  background: #ffffff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 8px;\n  box-shadow: ",";\n  border: 1px solid #f0f0f0;\n"])),(function(e){return e.shadowStyle||"none"})),it=w.styled.div(Ke||(Ke=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"]))),ct=(0,w.styled)(ot)(et||(et=(0,u.A)(["\n  min-width: 80px;\n  font-size: 12px;\n  font-weight: 500;\n"]))),st=w.styled.div(tt||(tt=(0,u.A)(["\n  width: 60px;\n  height: 40px;\n  background: #1890ff;\n  border-radius: 4px;\n  opacity: 0.8;\n"])));const ut=function(e){var t=e.value,n=e.onChange,r=e.showPreview,o=void 0===r||r,a=((0,S.A)(e,rt),(0,m.useState)("0px")),l=(0,s.A)(a,2),i=l[0],c=l[1],u=(0,m.useState)("2px"),p=(0,s.A)(u,2),d=p[0],f=p[1],g=(0,m.useState)("4px"),y=(0,s.A)(g,2),v=y[0],b=y[1],h=(0,m.useState)("0px"),x=(0,s.A)(h,2),A=x[0],E=x[1],w=(0,m.useState)("rgba(0, 0, 0, 0.1)"),C=(0,s.A)(w,2),k=C[0],O=C[1],z=(0,m.useState)(!1),P=(0,s.A)(z,2),j=P[0],T=P[1];(0,m.useEffect)((function(){if(t){var e=D(t);c(e.offsetX),f(e.offsetY),b(e.blurRadius),E(e.spreadRadius),O(e.color),T(e.inset)}}),[t]);var D=function(e){if(!e||"none"===e)return{offsetX:"0px",offsetY:"2px",blurRadius:"4px",spreadRadius:"0px",color:"rgba(0, 0, 0, 0.1)",inset:!1};if("object"===(0,he.A)(e))return{offsetX:e.offsetX||"0px",offsetY:e.offsetY||"2px",blurRadius:e.blurRadius||"4px",spreadRadius:e.spreadRadius||"0px",color:e.color||"rgba(0, 0, 0, 0.1)",inset:e.inset||!1};if("string"==typeof e){var t=e.trim(),n=!1;t.startsWith("inset ")&&(n=!0,t=t.replace("inset ",""));var r="rgba(0, 0, 0, 0.1)",o=t.match(/(rgba?\([^)]+\)|hsla?\([^)]+\)|#[a-fA-F0-9]{3,8}|\b\w+\b)$/);o&&(r=o[1],t=t.replace(o[1],"").trim());var a=t.split(/\s+/).filter((function(e){return e}));return{offsetX:a[0]||"0px",offsetY:a[1]||"2px",blurRadius:a[2]||"4px",spreadRadius:a[3]||"0px",color:r,inset:n}}return{offsetX:"0px",offsetY:"2px",blurRadius:"4px",spreadRadius:"0px",color:"rgba(0, 0, 0, 0.1)",inset:!1}},I=function(e,t,n,r,o,a){var l=[e,t,n,r,o].join(" ");return a?"inset ".concat(l):l},R=function(e,t){var r=i,o=d,a=v,l=A,s=k,u=j;switch(e){case"offsetX":r=t,c(t);break;case"offsetY":o=t,f(t);break;case"blurRadius":a=t,b(t);break;case"spreadRadius":l=t,E(t);break;case"color":s=t,O(t);break;case"inset":u=t,T(t)}var p=I(r,o,a,l,s,u);null==n||n(p)},M=I(i,d,v,A,k,j);return m.createElement(at,null,m.createElement(Y.A,{direction:"vertical",style:{width:"100%"}},m.createElement(it,null,m.createElement(ct,null,"Inset:"),m.createElement(nt.A,{checked:j,onChange:function(e){return R("inset",e)},size:"small"})),m.createElement(it,null,m.createElement(ct,null,"Offset X:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:i,onChange:function(e){return R("offsetX",e)},min:-50,max:50,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement(it,null,m.createElement(ct,null,"Offset Y:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:d,onChange:function(e){return R("offsetY",e)},min:-50,max:50,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement(it,null,m.createElement(ct,null,"Blur:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:v,onChange:function(e){return R("blurRadius",e)},min:0,max:100,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement(it,null,m.createElement(ct,null,"Spread:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:A,onChange:function(e){return R("spreadRadius",e)},min:-50,max:50,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement(it,null,m.createElement(ct,null,"Color:"),m.createElement("div",{style:{flex:1}},m.createElement(ie,{value:k,onChange:function(e){return R("color",e)},placeholder:"Shadow color"}))),o&&m.createElement(m.Fragment,null,m.createElement(G.A,{style:{margin:"8px 0"}}),m.createElement(ot,{style:{fontSize:"12px",marginBottom:"4px"}},"Preview:"),m.createElement(lt,{shadowStyle:M},m.createElement(st,null)),m.createElement(ot,{type:"secondary",style:{fontSize:"11px",textAlign:"center"}},M))))};var pt,dt,mt,ft,gt=n(3350),yt=["value","onChange","showPreview"],vt=O.A.Text,bt=We.A.Option,ht=w.styled.div(pt||(pt=(0,u.A)(["\n  width: 100%;\n"]))),xt=w.styled.div(dt||(dt=(0,u.A)(["\n  width: 100%;\n  padding: 16px;\n  margin: 12px 0;\n  background: #f5f5f5;\n  border-radius: 4px;\n  border: 1px solid #d9d9d9;\n  font-family: ",";\n  font-size: ",";\n  font-weight: ",";\n  line-height: ",";\n  text-align: center;\n"])),(function(e){return e.fontFamily||"inherit"}),(function(e){return e.fontSize||"16px"}),(function(e){return e.fontWeight||"normal"}),(function(e){return e.lineHeight||"1.5"})),At=w.styled.div(mt||(mt=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"]))),Et=(0,w.styled)(vt)(ft||(ft=(0,u.A)(["\n  min-width: 80px;\n  font-size: 12px;\n  font-weight: 500;\n"])));const wt=function(e){var t=e.value,n=e.onChange,r=e.showPreview,o=void 0===r||r,a=((0,S.A)(e,yt),(0,m.useState)("inherit")),l=(0,s.A)(a,2),i=l[0],c=l[1],u=(0,m.useState)("16px"),p=(0,s.A)(u,2),d=p[0],f=p[1],g=(0,m.useState)("normal"),y=(0,s.A)(g,2),v=y[0],b=y[1],h=(0,m.useState)("1.5"),x=(0,s.A)(h,2),A=x[0],E=x[1];(0,m.useEffect)((function(){if(t){var e=w(t);c(e.family),f(e.size),b(e.weight),E(e.lineHeight)}}),[t]);var w=function(e){return e?"object"===(0,he.A)(e)?{family:e.fontFamily||e.family||"inherit",size:e.fontSize||e.size||"16px",weight:e.fontWeight||e.weight||"normal",lineHeight:e.lineHeight||"1.5"}:{family:e,size:d,weight:v,lineHeight:A}:{family:"inherit",size:"16px",weight:"normal",lineHeight:"1.5"}},C=function(e,t){var r=i,o=d,a=v,l=A;switch(e){case"family":r=t,c(t);break;case"size":o=t,f(t);break;case"weight":a=t,b(t);break;case"lineHeight":l=t,E(t)}null==n||n({fontFamily:r,fontSize:o,fontWeight:a,lineHeight:l})};return m.createElement(ht,null,m.createElement(Y.A,{direction:"vertical",style:{width:"100%"}},m.createElement(At,null,m.createElement(Et,null,"Family:"),m.createElement(We.A,{value:i,onChange:function(e){return C("family",e)},style:{flex:1},size:"small",showSearch:!0,placeholder:"Select font family"},[{value:"inherit",label:"Inherit"},{value:"Arial, sans-serif",label:"Arial"},{value:"Helvetica, Arial, sans-serif",label:"Helvetica"},{value:'"Times New Roman", Times, serif',label:"Times New Roman"},{value:"Georgia, serif",label:"Georgia"},{value:'"Courier New", Courier, monospace',label:"Courier New"},{value:"Verdana, Geneva, sans-serif",label:"Verdana"},{value:'"Trebuchet MS", Helvetica, sans-serif',label:"Trebuchet MS"},{value:'"Lucida Sans Unicode", "Lucida Grande", sans-serif',label:"Lucida Sans"},{value:"Impact, Charcoal, sans-serif",label:"Impact"},{value:'"Comic Sans MS", cursive',label:"Comic Sans MS"},{value:'"Palatino Linotype", "Book Antiqua", Palatino, serif',label:"Palatino"},{value:'"Inter", -apple-system, BlinkMacSystemFont, sans-serif',label:"Inter"},{value:'"Roboto", sans-serif',label:"Roboto"},{value:'"Open Sans", sans-serif',label:"Open Sans"},{value:'"Lato", sans-serif',label:"Lato"},{value:'"Montserrat", sans-serif',label:"Montserrat"},{value:'"Source Sans Pro", sans-serif',label:"Source Sans Pro"}].map((function(e){return m.createElement(bt,{key:e.value,value:e.value},m.createElement("span",{style:{fontFamily:e.value}},e.label))})))),m.createElement(At,null,m.createElement(Et,null,"Size:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:d,onChange:function(e){return C("size",e)},min:8,max:72,step:1,unit:"px",units:["px","em","rem","%"],size:"small"}))),m.createElement(At,null,m.createElement(Et,null,"Weight:"),m.createElement(We.A,{value:v,onChange:function(e){return C("weight",e)},style:{flex:1},size:"small"},[{value:"100",label:"Thin (100)"},{value:"200",label:"Extra Light (200)"},{value:"300",label:"Light (300)"},{value:"normal",label:"Normal (400)"},{value:"500",label:"Medium (500)"},{value:"600",label:"Semi Bold (600)"},{value:"bold",label:"Bold (700)"},{value:"800",label:"Extra Bold (800)"},{value:"900",label:"Black (900)"}].map((function(e){return m.createElement(bt,{key:e.value,value:e.value},e.label)})))),m.createElement(At,null,m.createElement(Et,null,"Line Height:"),m.createElement("div",{style:{flex:1}},m.createElement(B,{value:A,onChange:function(e){return C("lineHeight",e)},min:.5,max:3,step:.1,precision:1,showUnit:!1,size:"small",tooltip:"Line height as a multiplier (e.g., 1.5 = 150%)"}))),o&&m.createElement(m.Fragment,null,m.createElement(G.A,{style:{margin:"8px 0"}}),m.createElement(vt,{style:{fontSize:"12px",marginBottom:"4px"}},"Preview:"),m.createElement(xt,{fontFamily:i,fontSize:d,fontWeight:v,lineHeight:A},m.createElement(gt.A,{style:{marginRight:"8px"}}),"The quick brown fox jumps over the lazy dog"),m.createElement(vt,{type:"secondary",style:{fontSize:"11px",textAlign:"center"}},i," • ",d," • ",v," • ",A))))};var Ct="text",kt="number",St="boolean",Ot="color",zt="select",Pt="spacing",jt="border",Tt="shadow",Dt="font",It="array",Rt="object",Mt={button:{text:{type:Ct,label:"Button Text",placeholder:"Enter button text"},variant:{type:zt,label:"Variant",options:[{value:"primary",label:"Primary"},{value:"secondary",label:"Secondary"},{value:"text",label:"Text"},{value:"link",label:"Link"},{value:"ghost",label:"Ghost"},{value:"dashed",label:"Dashed"}]},size:{type:zt,label:"Size",options:[{value:"small",label:"Small"},{value:"medium",label:"Medium"},{value:"large",label:"Large"}]},disabled:{type:St,label:"Disabled"},block:{type:St,label:"Full Width"},onClick:{type:Ct,label:"onClick Handler",placeholder:"Enter function name"}},text:{content:{type:Ct,label:"Text Content",multiline:!0,placeholder:"Enter text content"},variant:{type:zt,label:"Variant",options:[{value:"h1",label:"Heading 1"},{value:"h2",label:"Heading 2"},{value:"h3",label:"Heading 3"},{value:"h4",label:"Heading 4"},{value:"h5",label:"Heading 5"},{value:"h6",label:"Heading 6"},{value:"p",label:"Paragraph"},{value:"span",label:"Span"}]},color:{type:Ot,label:"Text Color"},align:{type:zt,label:"Text Alignment",options:[{value:"left",label:"Left"},{value:"center",label:"Center"},{value:"right",label:"Right"},{value:"justify",label:"Justify"}]}},input:{label:{type:Ct,label:"Input Label",placeholder:"Enter input label"},placeholder:{type:Ct,label:"Placeholder",placeholder:"Enter placeholder text"},type:{type:zt,label:"Input Type",options:[{value:"text",label:"Text"},{value:"password",label:"Password"},{value:"email",label:"Email"},{value:"number",label:"Number"},{value:"tel",label:"Telephone"},{value:"url",label:"URL"}]},required:{type:St,label:"Required"},disabled:{type:St,label:"Disabled"},validation:{type:zt,label:"Validation",options:[{value:"none",label:"None"},{value:"email",label:"Email"},{value:"url",label:"URL"},{value:"phone",label:"Phone"},{value:"custom",label:"Custom"}]}},card:{title:{type:Ct,label:"Card Title",placeholder:"Enter card title"},description:{type:Ct,label:"Description",multiline:!0,placeholder:"Enter card description"},image:{type:Ct,label:"Image URL",placeholder:"Enter image URL"},elevation:{type:zt,label:"Elevation",options:[{value:"none",label:"None"},{value:"sm",label:"Small"},{value:"md",label:"Medium"},{value:"lg",label:"Large"}]},bordered:{type:St,label:"Bordered"}}},Lt={width:{type:Ct,label:"Width",placeholder:"e.g., 100%, 200px",group:"dimensions"},height:{type:Ct,label:"Height",placeholder:"e.g., 100%, 200px",group:"dimensions"},minWidth:{type:Ct,label:"Min Width",placeholder:"e.g., 100px",group:"dimensions"},maxWidth:{type:Ct,label:"Max Width",placeholder:"e.g., 500px",group:"dimensions"},minHeight:{type:Ct,label:"Min Height",placeholder:"e.g., 100px",group:"dimensions"},maxHeight:{type:Ct,label:"Max Height",placeholder:"e.g., 500px",group:"dimensions"},margin:{type:Pt,label:"Margin",group:"spacing"},padding:{type:Pt,label:"Padding",group:"spacing"},fontSize:{type:kt,label:"Font Size",min:8,max:72,unit:"px",units:["px","em","rem","%"],group:"typography"},fontWeight:{type:zt,label:"Font Weight",options:[{value:"normal",label:"Normal"},{value:"bold",label:"Bold"},{value:"lighter",label:"Lighter"},{value:"bolder",label:"Bolder"},{value:"100",label:"100"},{value:"200",label:"200"},{value:"300",label:"300"},{value:"400",label:"400"},{value:"500",label:"500"},{value:"600",label:"600"},{value:"700",label:"700"},{value:"800",label:"800"},{value:"900",label:"900"}],group:"typography"},lineHeight:{type:kt,label:"Line Height",min:.5,max:3,step:.1,precision:1,showUnit:!1,group:"typography"},fontFamily:{type:Dt,label:"Font Family",group:"typography"},color:{type:Ot,label:"Text Color",group:"colors"},backgroundColor:{type:Ot,label:"Background Color",group:"colors"},border:{type:jt,label:"Border",group:"border"},borderTop:{type:jt,label:"Border Top",group:"border"},borderRight:{type:jt,label:"Border Right",group:"border"},borderBottom:{type:jt,label:"Border Bottom",group:"border"},borderLeft:{type:jt,label:"Border Left",group:"border"},borderRadius:{type:kt,label:"Border Radius",min:0,max:50,unit:"px",units:["px","em","rem","%"],group:"border"},boxShadow:{type:Tt,label:"Box Shadow",group:"shadow"},textShadow:{type:Tt,label:"Text Shadow",group:"shadow"},display:{type:zt,label:"Display",options:[{value:"block",label:"Block"},{value:"inline",label:"Inline"},{value:"inline-block",label:"Inline Block"},{value:"flex",label:"Flex"},{value:"grid",label:"Grid"},{value:"none",label:"None"}],group:"layout"},position:{type:zt,label:"Position",options:[{value:"static",label:"Static"},{value:"relative",label:"Relative"},{value:"absolute",label:"Absolute"},{value:"fixed",label:"Fixed"},{value:"sticky",label:"Sticky"}],group:"layout"}},Ft=function(e){return e.replace(/([A-Z])/g," $1").replace(/^./,(function(e){return e.toUpperCase()})).trim()},Bt=["propertyName","value","onChange","componentType","schema","showValidation"],_t=$.A.TextArea,Ht=We.A.Option,Wt=O.A.Text;const Nt=function(e){var t,n=e.propertyName,r=e.value,o=e.onChange,a=e.componentType,l=e.schema,i=e.showValidation,c=void 0===i||i,s=(0,S.A)(e,Bt),u=l||function(e,t,n){return n&&Mt[n]&&Mt[n][e]?Mt[n][e]:Lt[e]?Lt[e]:function(e,t){var n=e.toLowerCase();return n.includes("color")||n.includes("background")?{type:Ot,label:Ft(e)}:"number"==typeof t||"string"==typeof t&&/^\d+(\.\d+)?(px|em|rem|%)?$/.test(t)?{type:kt,label:Ft(e)}:"boolean"==typeof t?{type:St,label:Ft(e)}:n.includes("margin")||n.includes("padding")?{type:Pt,label:Ft(e)}:n.includes("border")&&!n.includes("radius")?{type:jt,label:Ft(e)}:n.includes("shadow")?{type:Tt,label:Ft(e)}:!n.includes("font")||n.includes("size")||n.includes("weight")?Array.isArray(t)?{type:It,label:Ft(e)}:"object"===(0,he.A)(t)&&null!==t?{type:Rt,label:Ft(e)}:{type:Ct,label:Ft(e)}:{type:Dt,label:Ft(e)}}(e,t)}(n,r,a),p=c?function(e,t){if(!t)return{valid:!0};switch(t.type){case kt:var n=parseFloat(e);if(isNaN(n))return{valid:!1,error:"Must be a valid number"};if(void 0!==t.min&&n<t.min)return{valid:!1,error:"Must be at least ".concat(t.min)};if(void 0!==t.max&&n>t.max)return{valid:!1,error:"Must be at most ".concat(t.max)};break;case Ot:if(e&&!/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/.test(e))return{valid:!1,error:"Must be a valid color value"};break;case zt:if(t.options&&e&&!t.options.some((function(t){return t.value===e})))return{valid:!1,error:"Must be one of the available options"}}return{valid:!0}}(r,u):{valid:!0},d=function(e){o&&o(e,n,u)},f=function(){return!c||p.valid?null:m.createElement(Wt,{type:"danger",style:{fontSize:"12px",display:"block",marginTop:"4px"}},p.error)},g=function(){return u.description||u.tooltip?m.createElement(P.A,{title:u.description||u.tooltip},m.createElement(T.A,{style:{color:"#8c8c8c",marginLeft:"4px"}})):null};switch(u.type){case Ct:return u.multiline?m.createElement("div",null,m.createElement(_t,(0,k.A)({value:r,onChange:function(e){return d(e.target.value)},placeholder:u.placeholder,rows:u.rows||3,status:p.valid?"":"error"},s)),g(),f()):m.createElement("div",null,m.createElement($.A,(0,k.A)({value:r,onChange:function(e){return d(e.target.value)},placeholder:u.placeholder,status:p.valid?"":"error"},s)),g(),f());case kt:return m.createElement("div",null,m.createElement(B,(0,k.A)({value:r,onChange:d,min:u.min,max:u.max,step:u.step,precision:u.precision,unit:u.unit,units:u.units,showSlider:u.showSlider,showUnit:u.showUnit,placeholder:u.placeholder,tooltip:u.tooltip},s)),g(),f());case St:return m.createElement("div",{style:{display:"flex",alignItems:"center",gap:"8px"}},m.createElement(nt.A,(0,k.A)({checked:r,onChange:d},s)),g(),f());case Ot:return m.createElement("div",null,m.createElement(ie,(0,k.A)({value:r,onChange:d,showPresets:u.showPresets,showModeToggle:u.showModeToggle,presets:u.presets,placeholder:u.placeholder},s)),g(),f());case zt:return m.createElement("div",null,m.createElement(We.A,(0,k.A)({value:r,onChange:d,placeholder:u.placeholder,style:{width:"100%"},status:p.valid?"":"error"},s),null===(t=u.options)||void 0===t?void 0:t.map((function(e){return m.createElement(Ht,{key:e.value,value:e.value},e.label)}))),g(),f());case Pt:return m.createElement("div",null,m.createElement(Le,(0,k.A)({value:r,onChange:d,type:n.includes("margin")?"margin":"padding",showVisual:u.showVisual,showPresets:u.showPresets,unit:u.unit},s)),g(),f());case jt:return m.createElement("div",null,m.createElement(Ze,(0,k.A)({value:r,onChange:d,showPreview:u.showPreview},s)),g(),f());case Tt:return m.createElement("div",null,m.createElement(ut,(0,k.A)({value:r,onChange:d,showPreview:u.showPreview},s)),g(),f());case Dt:return m.createElement("div",null,m.createElement(wt,(0,k.A)({value:r,onChange:d,showPreview:u.showPreview},s)),g(),f());case It:return m.createElement("div",null,m.createElement(_t,(0,k.A)({value:Array.isArray(r)?JSON.stringify(r,null,2):r,onChange:function(e){try{var t=JSON.parse(e.target.value);d(t)}catch(t){d(e.target.value)}},placeholder:u.placeholder||"Enter array as JSON",rows:4,status:p.valid?"":"error"},s)),g(),f());case Rt:return m.createElement("div",null,m.createElement(_t,(0,k.A)({value:"object"===(0,he.A)(r)?JSON.stringify(r,null,2):r,onChange:function(e){try{var t=JSON.parse(e.target.value);d(t)}catch(t){d(e.target.value)}},placeholder:u.placeholder||"Enter object as JSON",rows:6,status:p.valid?"":"error"},s)),g(),f());case"json":return m.createElement("div",null,m.createElement(_t,(0,k.A)({value:"string"==typeof r?r:JSON.stringify(r,null,2),onChange:function(e){return d(e.target.value)},placeholder:u.placeholder||"Enter JSON",rows:8,status:p.valid?"":"error",style:{fontFamily:"monospace"}},s)),g(),f());default:return m.createElement("div",null,m.createElement($.A,(0,k.A)({value:r,onChange:function(e){return d(e.target.value)},placeholder:u.placeholder||"Enter value",status:p.valid?"":"error"},s)),g(),f())}};var Ut,Vt,Jt,qt=n(6914),Gt=n(2877),Xt=n(7345),Yt=n(3108),Zt=["properties","onFilter","showGroupFilter","showTypeFilter","placeholder"],$t=O.A.Text,Qt=We.A.Option,Kt=w.styled.div(Ut||(Ut=(0,u.A)(["\n  padding: 12px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n"]))),en=w.styled.div(Vt||(Vt=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"]))),tn=w.styled.div(Jt||(Jt=(0,u.A)(["\n  display: flex;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-top: 8px;\n"])));const nn=function(e){var t=e.properties,n=void 0===t?{}:t,r=e.onFilter,o=e.showGroupFilter,a=void 0===o||o,l=e.showTypeFilter,i=void 0===l||l,c=e.placeholder,u=void 0===c?"Search properties...":c,p=((0,S.A)(e,Zt),(0,m.useState)("")),d=(0,s.A)(p,2),f=d[0],g=d[1],y=(0,m.useState)("all"),v=(0,s.A)(y,2),b=v[0],h=v[1],x=(0,m.useState)("all"),A=(0,s.A)(x,2),E=A[0],w=A[1],C=(0,m.useMemo)((function(){var e=new Set,t=new Set;return Object.values(n).forEach((function(n){n.group&&e.add(n.group),n.type&&t.add(n.type)})),{groups:Array.from(e).sort(),types:Array.from(t).sort()}}),[n]),k=C.groups,O=C.types,z=(0,m.useMemo)((function(){var e={};return Object.entries(n).forEach((function(t){var n=(0,s.A)(t,2),r=n[0],o=n[1],a=!f||r.toLowerCase().includes(f.toLowerCase())||o.label&&o.label.toLowerCase().includes(f.toLowerCase())||o.description&&o.description.toLowerCase().includes(f.toLowerCase()),l="all"===b||o.group===b,i="all"===E||o.type===E;a&&l&&i&&(e[r]=o)})),e}),[n,f,b,E]),P=[f&&"search","all"!==b&&"group","all"!==E&&"type"].filter(Boolean),j=function(e){return e.charAt(0).toUpperCase()+e.slice(1).replace(/([A-Z])/g," $1")};return m.createElement(Kt,null,m.createElement(en,null,m.createElement($.A,{prefix:m.createElement(Gt.A,null),placeholder:u,value:f,onChange:function(e){var t=e.target.value;g(t),null==r||r(z,{searchTerm:t,group:b,type:E})},allowClear:!0,style:{flex:1}}),a&&k.length>0&&m.createElement(We.A,{value:b,onChange:function(e){h(e),null==r||r(z,{searchTerm:f,group:e,type:E})},style:{minWidth:120},size:"small"},m.createElement(Qt,{value:"all"},"All Groups"),k.map((function(e){return m.createElement(Qt,{key:e,value:e},j(e))}))),i&&O.length>0&&m.createElement(We.A,{value:E,onChange:function(e){w(e),null==r||r(z,{searchTerm:f,group:b,type:e})},style:{minWidth:100},size:"small"},m.createElement(Qt,{value:"all"},"All Types"),O.map((function(e){return m.createElement(Qt,{key:e,value:e},j(e))}))),P.length>0&&m.createElement(Xt.A,{onClick:function(){g(""),h("all"),w("all"),null==r||r(n,{searchTerm:"",group:"all",type:"all"})},style:{cursor:"pointer",color:"#8c8c8c"},title:"Clear all filters"})),P.length>0&&m.createElement("div",null,m.createElement(Y.A,{size:4,style:{marginBottom:4}},m.createElement(Yt.A,{style:{fontSize:"12px",color:"#8c8c8c"}}),m.createElement($t,{type:"secondary",style:{fontSize:"12px"}},"Active filters:")),m.createElement(tn,null,f&&m.createElement(qt.A,{closable:!0,onClose:function(){g(""),null==r||r(z,{searchTerm:"",group:b,type:E})},size:"small"},'Search: "',f,'"'),"all"!==b&&m.createElement(qt.A,{closable:!0,onClose:function(){h("all"),null==r||r(z,{searchTerm:f,group:"all",type:E})},size:"small"},"Group: ",j(b)),"all"!==E&&m.createElement(qt.A,{closable:!0,onClose:function(){w("all"),null==r||r(z,{searchTerm:f,group:b,type:"all"})},size:"small"},"Type: ",j(E)))),m.createElement(Y.A,{style:{marginTop:8,fontSize:"12px"}},m.createElement($t,{type:"secondary"},"Showing ",Object.keys(z).length," of ",Object.keys(n).length," properties")))};var rn,on,an,ln,cn,sn,un,pn,dn=n(9356),mn=n(2120),fn=n(8602),gn=n(1143),yn=n(321),vn=n(4700),bn=n(4103),hn=n(8e3),xn=n(741),An=["groupName","properties","values","onChange","componentType","collapsible","defaultExpanded","showResetAll","showPropertyCount"],En=(dn.A.Panel,O.A.Text),wn=w.styled.div(rn||(rn=(0,u.A)(["\n  margin-bottom: 16px;\n"]))),Cn=w.styled.div(on||(on=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 12px;\n  background: #fafafa;\n  border: 1px solid #f0f0f0;\n  border-radius: 4px;\n  cursor: pointer;\n  user-select: none;\n  \n  &:hover {\n    background: #f5f5f5;\n  }\n"]))),kn=w.styled.div(an||(an=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"]))),Sn=w.styled.div(ln||(ln=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 4px;\n"]))),On=w.styled.div(cn||(cn=(0,u.A)(["\n  padding: 12px;\n  border-bottom: 1px solid #f0f0f0;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n"]))),zn=w.styled.div(sn||(sn=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 8px;\n"]))),Pn=(0,w.styled)(En)(un||(un=(0,u.A)(["\n  font-weight: 500;\n  font-size: 13px;\n"]))),jn=(0,w.styled)(En)(pn||(pn=(0,u.A)(["\n  font-size: 12px;\n  color: #8c8c8c;\n  display: block;\n  margin-top: 2px;\n"])));const Tn=function(e){var t,n=e.groupName,r=e.properties,o=void 0===r?{}:r,a=e.values,l=void 0===a?{}:a,i=e.onChange,c=e.componentType,u=e.collapsible,p=void 0===u||u,d=e.defaultExpanded,f=void 0===d||d,g=e.showResetAll,y=void 0===g||g,v=e.showPropertyCount,b=void 0===v||v,h=(0,S.A)(e,An),x=(0,m.useState)(f),A=(0,s.A)(x,2),E=A[0],w=A[1],C=function(e,t,n){i&&i(t,e,n)},O=Object.keys(o).filter((function(e){var t=l[e];return t!==(o[e].defaultValue||"")&&""!==t&&null!=t})).length,z=Object.keys(o).length;return 0===z?null:m.createElement(wn,null,m.createElement(Cn,{onClick:function(){p&&w(!E)}},m.createElement(kn,null,p&&(E?m.createElement(bn.A,{style:{fontSize:"12px"}}):m.createElement(hn.A,{style:{fontSize:"12px"}})),(t=n,{basic:m.createElement(fn.A,null),dimensions:m.createElement(gn.A,null),spacing:m.createElement(yn.A,null),typography:m.createElement(gt.A,null),colors:m.createElement(Q.A,null),border:m.createElement(Ne.A,null),shadow:m.createElement(vn.A,null),layout:m.createElement(yn.A,null)}[t.toLowerCase()]||m.createElement(fn.A,null)),m.createElement(En,{strong:!0,style:{fontSize:"14px"}},function(e){return e.charAt(0).toUpperCase()+e.slice(1).replace(/([A-Z])/g," $1")}(n)),b&&m.createElement(Y.A,{size:4},m.createElement(mn.A,{count:z,size:"small",color:"#f0f0f0",style:{color:"#8c8c8c"}}),O>0&&m.createElement(mn.A,{count:O,size:"small",color:"#1890ff"}))),m.createElement(Sn,null,y&&O>0&&m.createElement(P.A,{title:"Reset all properties in this group"},m.createElement(q.Ay,{type:"text",size:"small",icon:m.createElement(xn.A,null),onClick:function(e){e.stopPropagation(),Object.keys(o).forEach((function(e){var t=o[e],n=t.defaultValue||"";C(n,e,t)}))},style:{fontSize:"12px"}})))),E&&m.createElement("div",{style:{border:"1px solid #f0f0f0",borderTop:"none",borderRadius:"0 0 4px 4px"}},Object.entries(o).map((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1],a=l[n],i=void 0!==a&&""!==a&&null!==a&&a!==(r.defaultValue||"");return m.createElement(On,{key:n},m.createElement(zn,null,m.createElement("div",null,m.createElement(Pn,null,r.label||n,r.required&&m.createElement(En,{type:"danger"}," *")),r.description&&m.createElement(jn,null,r.description)),i&&m.createElement(P.A,{title:"Reset to default"},m.createElement(q.Ay,{type:"text",size:"small",icon:m.createElement(xn.A,null),onClick:function(e){return function(e,t){t.stopPropagation();var n=o[e],r=n.defaultValue||"";C(r,e,n)}(n,e)},style:{fontSize:"12px"}}))),m.createElement(Nt,(0,k.A)({propertyName:n,value:a,onChange:C,componentType:c,schema:r,size:"small"},h)))}))))};var Dn,In,Rn,Mn,Ln,Fn=n(677),Bn=n(7197),_n=n(234),Hn=n(8597),Wn=n(6008),Nn=["component","properties","values","showPreview","showCode","showValidation","onReset"];function Un(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Vn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Un(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Un(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Jn=O.A.Text,qn=(O.A.Title,w.styled.div(Dn||(Dn=(0,u.A)(["\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  background: white;\n  border-bottom: 1px solid #f0f0f0;\n"])))),Gn=w.styled.div(In||(In=(0,u.A)(["\n  min-height: 120px;\n  padding: 16px;\n  background: ",";\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  overflow: hidden;\n"])),(function(e){return e.background||"#f5f5f5"})),Xn=w.styled.div(Rn||(Rn=(0,u.A)(["\n  transition: all 0.2s ease;\n  ","\n"])),(function(e){return e.styles||""})),Yn=w.styled.pre(Mn||(Mn=(0,u.A)(["\n  background: #f6f8fa;\n  border: 1px solid #e1e4e8;\n  border-radius: 4px;\n  padding: 12px;\n  font-size: 12px;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  overflow-x: auto;\n  max-height: 200px;\n  margin: 0;\n"]))),Zn=w.styled.div(Ln||(Ln=(0,u.A)(["\n  margin-top: 8px;\n"])));const $n=function(e){var t=e.component,n=e.properties,r=void 0===n?{}:n,o=e.values,a=void 0===o?{}:o,l=e.showPreview,i=void 0===l||l,c=e.showCode,u=void 0!==c&&c,p=e.showValidation,d=void 0===p||p,f=e.onReset,g=((0,S.A)(e,Nn),(0,m.useState)(i)),y=(0,s.A)(g,2),v=y[0],b=y[1],h=(0,m.useState)(u),x=(0,s.A)(h,2),A=x[0],E=x[1],w=(0,m.useState)({}),C=(0,s.A)(w,2),O=C[0],z=C[1],P=(0,m.useMemo)((function(){var e={};return Object.entries(a).forEach((function(t){var n=(0,s.A)(t,2),r=n[0],o=n[1];if(null!=o&&""!==o){var a=r.replace(/([A-Z])/g,"-$1").toLowerCase();"object"===(0,he.A)(o)&&null!==o?"font"===r||"fontFamily"===r?Object.entries(o).forEach((function(t){var n=(0,s.A)(t,2),r=n[0],o=n[1],a=r.replace(/([A-Z])/g,"-$1").toLowerCase();e[a]=o})):e[a]=JSON.stringify(o):e[a]=o}})),e}),[a]),j=(0,m.useMemo)((function(){var e=Object.entries(P).map((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1];return"  ".concat(n,": ").concat(r,";")}));return".component {\n".concat(e.join("\n"),"\n}")}),[P]),T=(0,m.useMemo)((function(){var e={};return Object.entries(P).forEach((function(t){var n=(0,s.A)(t,2),r=n[0],o=n[1],a=r.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}));e[a]=o})),e}),[P]);(0,m.useEffect)((function(){var e={};Object.entries(a).forEach((function(t){var n=(0,s.A)(t,2),o=n[0],a=n[1],l=r[o];if(l&&null!=a&&""!==a)if("number"===l.type){var i=parseFloat(a);isNaN(i)?e[o]="Must be a valid number":void 0!==l.min&&i<l.min?e[o]="Must be at least ".concat(l.min):void 0!==l.max&&i>l.max&&(e[o]="Must be at most ".concat(l.max))}else"color"===l.type&&(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/.test(a)||(e[o]="Must be a valid color value"))})),z(e)}),[a,r]);var D=Object.keys(O).length>0,I=Object.keys(a).some((function(e){return void 0!==a[e]&&""!==a[e]&&null!==a[e]}));return m.createElement(qn,null,m.createElement(Fn.A,{size:"small",title:"Property Preview"},m.createElement(Y.A,{direction:"vertical",style:{width:"100%"}},m.createElement(Y.A,{style:{width:"100%",justifyContent:"space-between"}},m.createElement(Y.A,null,m.createElement(nt.A,{checked:v,onChange:b,checkedChildren:m.createElement(_n.A,null),unCheckedChildren:m.createElement(Hn.A,null),size:"small"}),m.createElement(Jn,{style:{fontSize:"12px"}},"Live Preview")),m.createElement(Y.A,null,m.createElement(q.Ay,{type:"text",size:"small",icon:m.createElement(Wn.A,null),onClick:function(){return E(!A)},style:{fontSize:"12px"}},A?"Hide":"Show"," CSS"),I&&m.createElement(q.Ay,{type:"text",size:"small",icon:m.createElement(xn.A,null),onClick:f,style:{fontSize:"12px"}},"Reset All"))),v&&m.createElement(Gn,null,m.createElement(Xn,{styles:Object.entries(T).map((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1];return"".concat(n,": ").concat(r,";")})).join(" ")},function(){var e={style:T,className:"preview-element"};switch(null==t?void 0:t.type){case"button":return m.createElement("button",e,a.text||"Button");case"text":var n=a.variant||"p";return m.createElement(n,e,a.content||"Sample text");case"input":return m.createElement("input",(0,k.A)({},e,{type:a.type||"text",placeholder:a.placeholder||"Enter text",disabled:a.disabled}));case"card":return m.createElement("div",(0,k.A)({},e,{style:Vn(Vn({},T),{},{border:"1px solid #d9d9d9",borderRadius:"4px",padding:"16px",minWidth:"200px"})}),a.title&&m.createElement("h4",{style:{margin:"0 0 8px 0"}},a.title),a.description&&m.createElement("p",{style:{margin:0,color:"#666"}},a.description));default:return m.createElement("div",(0,k.A)({},e,{style:Vn(Vn({},T),{},{padding:"16px",background:"#fff",border:"1px solid #d9d9d9",borderRadius:"4px"})}),(null==t?void 0:t.name)||"Component"," Preview")}}())),A&&m.createElement(m.Fragment,null,m.createElement(G.A,{style:{margin:"8px 0"}}),m.createElement("div",null,m.createElement(Jn,{strong:!0,style:{fontSize:"12px"}},"Generated CSS:"),m.createElement(Yn,null,j))),d&&D&&m.createElement(Zn,null,m.createElement(Bn.A,{message:"Validation Errors",description:m.createElement("ul",{style:{margin:0,paddingLeft:"16px"}},Object.entries(O).map((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1];return m.createElement("li",{key:n,style:{fontSize:"12px"}},m.createElement("strong",null,n,":")," ",r)}))),type:"error",size:"small",showIcon:!0})),d&&!D&&I&&m.createElement(Zn,null,m.createElement(Bn.A,{message:"All properties are valid",type:"success",size:"small",showIcon:!0})))))};var Qn,Kn,er,tr,nr=n(2395),rr=n(1005),or=n(1616),ar=n(2543),lr=["name"];function ir(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function cr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ir(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ir(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}O.A.Title;var sr=O.A.Text,ur=nr.A.TabPane,pr=w.styled.div(Qn||(Qn=(0,u.A)(["\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n"]))),dr=w.styled.div(Kn||(Kn=(0,u.A)(["\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n"]))),mr=w.styled.div(er||(er=(0,u.A)(["\n  padding: 16px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n"]))),fr=w.styled.div(tr||(tr=(0,u.A)(["\n  padding: 0;\n"])));const gr=function(e){var t=e.component,n=e.onUpdate,r=e.onRealTimeUpdate,o=e.enableRealTimePreview,a=void 0===o||o,l=e.enableCollaboration,c=void 0!==l&&l,u=e.collaborativeSession,p=void 0===u?null:u,d=(0,f.wA)(),g=(0,m.useState)("basic"),v=(0,s.A)(g,2),b=v[0],h=v[1],x=(0,m.useState)({}),A=(0,s.A)(x,2),E=A[0],w=A[1],C=(0,m.useState)({}),k=(0,s.A)(C,2),O=(k[0],k[1]),z=(0,m.useState)(!1),j=(0,s.A)(z,2),T=j[0],D=j[1],I=(0,m.useState)(a),R=(0,s.A)(I,2),M=R[0],L=R[1],F=(0,m.useState)(!1),B=(0,s.A)(F,2),_=B[0],H=B[1],W=(0,m.useState)(null),N=(0,s.A)(W,2),U=N[0],V=N[1],J=(0,m.useRef)(null),G=(0,m.useRef)({});(0,m.useEffect)((function(){if(t){var e=cr(cr({name:t.name},t.props),t.style);w(e),D(!1)}}),[t]);var X=(0,m.useMemo)((function(){return null!=t&&t.type?(e=t.type,Mt[e]||{}):{};var e}),[null==t?void 0:t.type]),Z=(0,m.useMemo)((function(){return e={},Object.entries(Lt).forEach((function(t){var n=(0,s.A)(t,2),r=n[0],o=n[1],a=o.group||"other";e[a]||(e[a]={}),e[a][r]=o})),e;var e}),[]),$=(0,m.useMemo)((function(){return(0,ar.debounce)((function(e){M&&r&&(H(!0),r(e),V(new Date),setTimeout((function(){return H(!1)}),300))}),300)}),[M,r]),Q=(0,m.useCallback)((function(e){n&&n(e)}),[n]),K=(0,m.useCallback)((function(e,n,r){var o=cr(cr({},E),{},(0,i.A)({},e,n));w(o),D(!0);var a=cr(cr({},t),{},{name:"name"===e?n:o.name,props:cr(cr({},t.props),"name"===e||Lt[e]?{}:(0,i.A)({},e,n)),style:cr(cr({},t.style),Lt[e]?(0,i.A)({},e,n):{})});Q(a),M&&$(a),G.current=o}),[E,t,M,Q,$]),ee=function(){if(t){var e=cr(cr({name:t.name},t.props),t.style);w(e),D(!1)}},te=function(e,t){O(e)};(0,m.useEffect)((function(){return function(){J.current&&clearTimeout(J.current),$.cancel()}}),[$]);var ne=(0,m.useCallback)((function(e){if(L(e),e&&T){var n=cr(cr({},t),{},{name:E.name||t.name,props:cr({},t.props),style:cr({},t.style)});Object.entries(E).forEach((function(e){var t=(0,s.A)(e,2),r=t[0],o=t[1];"name"!==r&&(Lt[r]?n.style[r]=o:n.props[r]=o)})),$(n)}}),[T,t,E,$]);return t?m.createElement(pr,null,m.createElement("div",{style:{padding:"12px 16px",borderBottom:"1px solid #f0f0f0",background:"#fafafa",display:"flex",alignItems:"center",justifyContent:"space-between"}},m.createElement(Y.A,null,m.createElement(sr,{strong:!0,style:{fontSize:"14px"}},t.name||t.type," Properties"),_&&m.createElement(rr.A,{spin:!0,style:{color:"#1890ff"}})),m.createElement(Y.A,null,a&&m.createElement(P.A,{title:"Toggle real-time preview updates"},m.createElement(nt.A,{size:"small",checked:M,onChange:ne,checkedChildren:m.createElement(_n.A,null),unCheckedChildren:m.createElement(_n.A,null)})),U&&M&&m.createElement(mn.A,{status:"success",text:"Updated ".concat(U.toLocaleTimeString()),style:{fontSize:"11px"}}),c&&p&&m.createElement(mn.A,{count:p.collaboratorCount||0,showZero:!1,style:{backgroundColor:"#52c41a"}},m.createElement(P.A,{title:"Active collaborators"},m.createElement(q.Ay,{size:"small",type:"text",icon:m.createElement(rr.A,null)}))))),m.createElement($n,{component:t,properties:cr(cr({},X),Lt),values:E,onReset:ee,showPreview:!0,showCode:!1,showValidation:!0,realTimeEnabled:M}),m.createElement(dr,null,m.createElement(nr.A,{activeKey:b,onChange:h,size:"small"},m.createElement(ur,{tab:"Properties",key:"basic"},m.createElement(fr,null,m.createElement(nn,{properties:X,onFilter:te,placeholder:"Search component properties..."}),m.createElement(Tn,{groupName:"basic",properties:{name:{type:"text",label:"Component Name",required:!0,placeholder:"Enter component name"}},values:E,onChange:K,componentType:t.type,defaultExpanded:!0}),Object.keys(X).length>0&&m.createElement(Tn,{groupName:"component",properties:X,values:E,onChange:K,componentType:t.type,defaultExpanded:!0}))),m.createElement(ur,{tab:"Styling",key:"style"},m.createElement(fr,null,m.createElement(nn,{properties:Lt,onFilter:te,placeholder:"Search style properties..."}),Object.entries(Z).map((function(e){var n=(0,s.A)(e,2),r=n[0],o=n[1];return m.createElement(Tn,{key:r,groupName:r,properties:o,values:E,onChange:K,componentType:t.type,defaultExpanded:"dimensions"===r||"colors"===r})})))),m.createElement(ur,{tab:"Advanced",key:"advanced"},m.createElement(fr,null,m.createElement(Tn,{groupName:"advanced",properties:{customProps:{type:"json",label:"Custom Properties",placeholder:"Enter custom properties as JSON",description:"Additional properties not covered by the standard options"},customStyles:{type:"json",label:"Custom Styles",placeholder:"Enter custom styles as JSON",description:"Additional CSS styles not covered by the standard options"}},values:E,onChange:K,componentType:t.type,defaultExpanded:!0}))))),m.createElement(mr,null,m.createElement(Y.A,{style:{width:"100%",justifyContent:"space-between"}},m.createElement(Y.A,null,T&&m.createElement(sr,{type:"warning",style:{fontSize:"12px"}},"Unsaved changes")),m.createElement(Y.A,null,m.createElement(q.Ay,{size:"small",icon:m.createElement(xn.A,null),onClick:ee,disabled:!T},"Reset"),m.createElement(q.Ay,{type:"primary",size:"small",icon:m.createElement(y.A,null),onClick:function(){if(t){var e=E.name,r=(0,S.A)(E,lr),o={},a={};Object.entries(r).forEach((function(e){var t=(0,s.A)(e,2),n=t[0],r=t[1];Lt[n]?a[n]=r:o[n]=r}));var l=cr(cr({},t),{},{name:e||t.name,props:cr(cr({},t.props),o),style:cr(cr({},t.style),a)});d((0,or.ZP)(l)),D(!1),n&&n(l)}},disabled:!T},"Apply Changes"))))):m.createElement(pr,null,m.createElement("div",{style:{padding:"24px",textAlign:"center"}},m.createElement(sr,{type:"secondary"},"Select a component to edit its properties")))};var yr,vr,br,hr,xr,Ar,Er,wr;function Cr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function kr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Cr(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Cr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}try{wr=n(1096).A}catch(e){console.warn("Enhanced component builder not available, using fallback"),wr=null}var Sr=w.styled.div(yr||(yr=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),C.Ay.spacing[4]),Or=w.styled.div(vr||(vr=(0,u.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: ",";\n"])),C.Ay.spacing[4]),zr=w.styled.div(br||(br=(0,u.A)(["\n  padding: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  background-color: white;\n"])),C.Ay.spacing[4],C.Ay.colors.neutral[200],C.Ay.borderRadius.md),Pr=w.styled.div(hr||(hr=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),C.Ay.spacing[3]),jr=w.styled.div(xr||(xr=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),C.Ay.spacing[2]),Tr=w.styled.div(Ar||(Ar=(0,u.A)(["\n  cursor: pointer;\n  transition: ",";\n  border: 2px solid ",";\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ",";\n  }\n"])),C.Ay.transitions.default,(function(e){return e.isSelected?C.Ay.colors.primary.main:"transparent"}),C.Ay.shadows.md),Dr=w.styled.div(Er||(Er=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ",";\n  background-color: ",";\n  border-radius: ",";\n  text-align: center;\n"])),C.Ay.spacing[8],C.Ay.colors.neutral[100],C.Ay.borderRadius.md),Ir=[{value:"container",label:"Container"},{value:"text",label:"Text"},{value:"button",label:"Button"},{value:"input",label:"Input Field"},{value:"image",label:"Image"},{value:"card",label:"Card"},{value:"list",label:"List"},{value:"custom",label:"Custom"}];const Rr=function(){var e=(0,m.useState)(null!==wr);if((0,s.A)(e,1)[0]&&wr)return m.createElement(wr,null);var t=(0,f.wA)(),n=(0,f.d4)((function(e){var t,n;return(null===(t=e.app)||void 0===t?void 0:t.components)||(null===(n=e.appData)||void 0===n?void 0:n.components)||[]})),r=(0,m.useState)(!0),o=(0,s.A)(r,2),a=o[0],l=o[1],i=(0,m.useState)(""),u=(0,s.A)(i,2),p=u[0],k=u[1],S=(0,m.useState)("container"),O=(0,s.A)(S,2),z=O[0],P=O[1],j=(0,m.useState)("{}"),T=(0,s.A)(j,2),D=T[0],I=T[1],R=(0,m.useState)(null),M=(0,s.A)(R,2),L=M[0],F=M[1],B=(0,m.useState)(!1),_=(0,s.A)(B,2),H=_[0],W=_[1],N=(0,m.useState)({}),U=(0,s.A)(N,2),V=U[0],J=U[1];if((0,m.useEffect)((function(){var e=function(){var e=(0,c.A)(d().mark((function e(){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{l(!0),0===n.length&&[{id:"button-1",name:"Primary Button",type:"button",props:{text:"Click Me",variant:"primary",size:"medium",onClick:"handleButtonClick"},createdAt:(new Date).toISOString()},{id:"text-1",name:"Header Text",type:"text",props:{content:"Welcome to App Builder",variant:"h1",color:"#2563EB",align:"center"},createdAt:(new Date).toISOString()},{id:"input-1",name:"Email Input",type:"input",props:{label:"Email Address",placeholder:"Enter your email",type:"email",required:!0,validation:"email"},createdAt:(new Date).toISOString()},{id:"card-1",name:"Feature Card",type:"card",props:{title:"Easy to Use",description:"Build applications with a simple drag-and-drop interface",image:"https://via.placeholder.com/150",elevation:"md"},createdAt:(new Date).toISOString()}].forEach((function(e){t((0,E.addComponent)(e))})),l(!1)}catch(e){console.error("Failed to initialize ComponentBuilder:",e),l(!1)}case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[n.length,t]),(0,m.useEffect)((function(){return console.log("ComponentBuilder mounting..."),function(){console.log("ComponentBuilder unmounting...")}}),[]),(0,m.useEffect)((function(){console.log("Components updated:",n)}),[n]),(0,m.useEffect)((function(){Object.keys(V).length>0&&console.error("ComponentBuilder errors:",V)}),[V]),a)return m.createElement("div",null,"Loading ComponentBuilder...");var q=function(){var e={};p.trim()||(e.name="Component name is required");try{D&&JSON.parse(D)}catch(t){e.props="Invalid JSON format"}return J(e),0===Object.keys(e).length},G=function(e){F(e),k(e.name),P(e.type),I(JSON.stringify(e.props,null,2)),W(!0),J({})};return m.createElement(Sr,null,m.createElement(w.Card,null,m.createElement(w.Card.Header,null,m.createElement(w.Card.Title,null,H?"Edit Component":"Create Component"),H&&m.createElement(w.Button,{variant:"text",size:"small",onClick:function(){k(""),P("container"),I("{}"),F(null),W(!1),J({})},startIcon:m.createElement(g.A,null)},"Cancel")),m.createElement(w.Card.Content,null,m.createElement(Pr,null,m.createElement(jr,null,m.createElement(w.Input,{label:"Component Name",value:p,onChange:function(e){return k(e.target.value)},placeholder:"Enter component name",fullWidth:!0,error:!!V.name,helperText:V.name})),m.createElement(jr,null,m.createElement(w.Select,{label:"Component Type",value:z,onChange:function(e){return P(e.target.value)},options:Ir,fullWidth:!0})),m.createElement(jr,null,m.createElement(w.Input,{label:"Component Props (JSON)",value:D,onChange:function(e){return I(e.target.value)},placeholder:'{"text": "Hello", "color": "blue"}',fullWidth:!0,error:!!V.props,helperText:V.props,as:"textarea",rows:5,style:{fontFamily:C.Ay.typography.fontFamily.code}})))),m.createElement(w.Card.Footer,null,H?m.createElement(w.Button,{variant:"primary",onClick:function(){if(L&&q())try{var e=D?JSON.parse(D):{},n=kr(kr({},L),{},{name:p.trim(),type:z,props:e,updatedAt:(new Date).toISOString()});t((0,E.updateComponent)(n)),k(""),P("container"),I("{}"),F(null),W(!1),J({})}catch(e){J(kr(kr({},V),{},{props:e.message}))}},startIcon:m.createElement(y.A,null)},"Update Component"):m.createElement(w.Button,{variant:"primary",onClick:function(){if(q())try{var e=D?JSON.parse(D):{},n={id:Date.now().toString(),name:p.trim(),type:z,props:e,createdAt:(new Date).toISOString()};t((0,E.addComponent)(n)).then((function(){k(""),P("container"),I("{}"),J({})})).catch((function(e){console.error("Failed to add component:",e),J({submit:"Failed to add component"})}))}catch(e){J(kr(kr({},V),{},{props:e.message}))}},startIcon:m.createElement(v.A,null)},"Add Component"))),m.createElement(w.Card,null,m.createElement(w.Card.Header,null,m.createElement(w.Card.Title,null,"Component Library")),m.createElement(w.Card.Content,null,0===n.length?m.createElement(Dr,null,m.createElement("div",{style:{fontSize:"48px",color:C.Ay.colors.neutral[400],marginBottom:C.Ay.spacing[4]}},m.createElement(b.A,null)),m.createElement("h3",null,"No Components Yet"),m.createElement("p",null,"Create your first component to get started")):m.createElement(Or,null,n.map((function(e){return m.createElement(Tr,{key:e.id,isSelected:L&&L.id===e.id},m.createElement(w.Card,{elevation:"sm"},m.createElement(w.Card.Header,null,m.createElement("div",null,m.createElement("div",{style:{fontWeight:C.Ay.typography.fontWeight.semibold}},e.name),m.createElement("div",{style:{fontSize:C.Ay.typography.fontSize.sm,color:C.Ay.colors.neutral[500]}},e.type)),m.createElement("div",{style:{display:"flex",gap:C.Ay.spacing[1]}},m.createElement(w.Button,{variant:"text",size:"small",onClick:function(n){n.stopPropagation(),function(e){var n=kr(kr({},e),{},{id:Date.now().toString(),name:"".concat(e.name," (Copy)"),createdAt:(new Date).toISOString()});t((0,E.addComponent)(n))}(e)}},m.createElement(h.A,null)),m.createElement(w.Button,{variant:"text",size:"small",onClick:function(t){t.stopPropagation(),G(e)}},m.createElement(x.A,null)),m.createElement(w.Button,{variant:"text",size:"small",onClick:function(n){var r;n.stopPropagation(),r=e.id,t((0,E.removeComponent)(r)),L&&L.id===r&&(k(""),P("container"),I("{}"),F(null),W(!1))}},m.createElement(A.A,null)))),m.createElement(w.Card.Content,{onClick:function(){return G(e)}},m.createElement(zr,null,m.createElement("pre",{style:{margin:0,overflow:"auto",maxHeight:"100px"}},JSON.stringify(e.props,null,2))))))}))))),L&&m.createElement(w.Card,null,m.createElement(w.Card.Header,null,m.createElement(w.Card.Title,null,"Component Properties")),m.createElement(w.Card.Content,null,m.createElement(gr,{component:L,onUpdate:function(e){t((0,E.updateComponent)(e))}}))))}}}]);