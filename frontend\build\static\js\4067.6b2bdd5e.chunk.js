"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[4067],{94067:(e,t,n)=>{n.r(t),n.d(t,{default:()=>G});var r,a,l,s,i,o,c,m=n(60436),u=n(10467),d=n(5544),p=n(57528),g=n(54756),f=n.n(g),y=n(96540),E=n(1807),h=n(35346),w=n(11080),A=n(71468),v=(n(81616),n(49391)),x=n(79146),b=n(86020),k=E.o5.Title,P=E.o5.Text,I=x.styled.div(r||(r=(0,p.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  padding: ",";\n  background-color: ",";\n"])),b.Ay.spacing[4],b.Ay.colors.neutral[100]),V=(0,x.styled)(E.Zp)(a||(a=(0,p.A)(["\n  width: 100%;\n  max-width: 480px;\n  box-shadow: ",";\n  border-radius: ",";\n"])),b.Ay.shadows.lg,b.Ay.borderRadius.lg),C=(0,x.styled)(E.lV)(l||(l=(0,p.A)(["\n  .ant-form-item-label {\n    text-align: left;\n  }\n"]))),N=(0,x.styled)(E.$n)(s||(s=(0,p.A)(["\n  width: 100%;\n"]))),F=(0,x.styled)(w.N_)(i||(i=(0,p.A)(["\n  float: right;\n"]))),S=((0,x.styled)(P)(o||(o=(0,p.A)(["\n  display: block;\n  text-align: center;\n  margin-top: 16px;\n"]))),(0,x.styled)(E.$n)(c||(c=(0,p.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n"]))));const G=function(){var e,t=(0,w.Zp)(),n=(0,w.zy)(),r=((0,A.wA)(),(0,v.As)()),a=r.login,l=r.register,s=r.isAuthenticated,i=r.isLoading,o=r.error,c=E.lV.useForm(),p=(0,d.A)(c,1)[0],g=(0,y.useState)(!0),x=(0,d.A)(g,2),G=x[0],q=x[1],B=(0,y.useState)(null),L=(0,d.A)(B,2),R=L[0],$=L[1],j=(null===(e=n.state)||void 0===e||null===(e=e.from)||void 0===e?void 0:e.pathname)||"/dashboard";(0,y.useEffect)((function(){s&&t(j,{replace:!0})}),[s,t,j]),(0,y.useEffect)((function(){$(o)}),[o]);var z=function(){var e=(0,u.A)(f().mark((function e(n){var r,s,i;return f().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if($(null),e.prev=1,!G){e.next=9;break}return e.next=5,a(n.email,n.password);case 5:(r=e.sent).success?(E.iU.success("Login successful!"),t(j,{replace:!0})):$(r.error||"Login failed"),e.next=14;break;case 9:return s={username:n.email.split("@")[0],email:n.email,password:n.password,first_name:n.firstName,last_name:n.lastName},e.next=12,l(s);case 12:(i=e.sent).success?(E.iU.success("Registration successful!"),t(j,{replace:!0})):$(i.error||"Registration failed");case 14:e.next=20;break;case 16:e.prev=16,e.t0=e.catch(1),console.error("Authentication error:",e.t0),$(e.t0.message||"An error occurred during authentication");case 20:case"end":return e.stop()}}),e,null,[[1,16]])})));return function(t){return e.apply(this,arguments)}}(),T=function(e){E.iU.info("".concat(e," login is not implemented in this demo"))};return y.createElement(I,null,y.createElement(V,null,y.createElement("div",{style:{textAlign:"center",marginBottom:b.Ay.spacing[4]}},y.createElement(k,{level:2,style:{margin:0}},G?"Welcome Back":"Create Account"),y.createElement(P,{type:"secondary"},G?"Sign in to continue to App Builder":"Register to start building amazing applications")),R&&y.createElement(E.Fc,{message:"Authentication Error",description:R,type:"error",showIcon:!0,style:{marginBottom:b.Ay.spacing[4]}}),y.createElement(C,{form:p,name:"auth_form",layout:"vertical",onFinish:z,initialValues:{remember:!0}},!G&&y.createElement(E.$x,{style:{display:"flex",gap:b.Ay.spacing[3]}},y.createElement(E.lV.Item,{name:"firstName",label:"First Name",rules:[{required:!0,message:"Please enter your first name"}],style:{flex:1}},y.createElement(E.pd,{placeholder:"First Name"})),y.createElement(E.lV.Item,{name:"lastName",label:"Last Name",rules:[{required:!0,message:"Please enter your last name"}],style:{flex:1}},y.createElement(E.pd,{placeholder:"Last Name"}))),y.createElement(E.lV.Item,{name:"email",label:G?"Email or Username":"Email",rules:[{required:!0,message:"Please enter your email"}].concat((0,m.A)(G?[]:[{type:"email",message:"Please enter a valid email"}]))},y.createElement(E.pd,{prefix:y.createElement(h.qmv,null),placeholder:"Email"})),y.createElement(E.lV.Item,{name:"password",label:"Password",rules:[{required:!0,message:"Please enter your password"}]},y.createElement(E.pd.Password,{prefix:y.createElement(h.sXv,null),placeholder:"Password"})),!G&&y.createElement(E.lV.Item,{name:"confirmPassword",label:"Confirm Password",dependencies:["password"],rules:[{required:!0,message:"Please confirm your password"},function(e){var t=e.getFieldValue;return{validator:function(e,n){return n&&t("password")!==n?Promise.reject(new Error("The two passwords do not match")):Promise.resolve()}}}]},y.createElement(E.pd.Password,{prefix:y.createElement(h.sXv,null),placeholder:"Confirm Password"})),G&&y.createElement(E.lV.Item,null,y.createElement(E.lV.Item,{name:"remember",valuePropName:"checked",noStyle:!0},y.createElement(E.Sc,null,"Remember me")),y.createElement(F,{to:"/forgot-password"},"Forgot password?")),y.createElement(E.lV.Item,null,y.createElement(N,{type:"primary",htmlType:"submit",size:"large",loading:i},G?"Sign In":"Create Account"))),y.createElement(E.cG,null,y.createElement(P,{type:"secondary"},"Or continue with")),y.createElement(E.$x,{direction:"horizontal",style:{width:"100%",justifyContent:"center",gap:b.Ay.spacing[3],marginBottom:b.Ay.spacing[4]}},y.createElement(S,{icon:y.createElement(h.IhG,null),onClick:function(){return T("Google")}},"Google"),y.createElement(S,{icon:y.createElement(h.yGI,null),onClick:function(){return T("GitHub")}},"GitHub")),y.createElement("div",{style:{textAlign:"center"}},y.createElement(P,{type:"secondary"},G?"Don't have an account? ":"Already have an account? ",y.createElement(E.$n,{type:"link",onClick:function(){p.resetFields(),q(!G),$(null)},style:{padding:0}},G?"Sign up now":"Sign in")))))}}}]);