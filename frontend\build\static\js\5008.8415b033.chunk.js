"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[5008],{75008:(e,n,t)=>{t.r(n),t.d(n,{default:()=>b});var o,c,a,l,r=t(5544),s=t(57528),i=t(96540),u=t(71468),d=t(1807),m=t(35346),p=t(79146),y=t(86020),f=d.o5.Title,g=d.o5.Text,v=d.o5.Paragraph,h=(d.tU.TabPane,d.l6.Option),E=p.styled.div(o||(o=(0,s.A)(["\n  padding: ",";\n"])),y.Ay.spacing[3]),x=p.styled.pre(c||(c=(0,s.A)(["\n  background-color: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  padding: ",";\n  overflow: auto;\n  max-height: 500px;\n  font-family: ",";\n  font-size: ",";\n  line-height: 1.5;\n"])),y.Ay.colors.neutral[100],y.Ay.colors.neutral[300],y.Ay.borderRadius.md,y.Ay.spacing[3],y.Ay.typography.fontFamily.code,y.Ay.typography.fontSize.sm),A=p.styled.div(a||(a=(0,s.A)(["\n  margin-bottom: ",";\n"])),y.Ay.spacing[4]),C=p.styled.div(l||(l=(0,s.A)(["\n  margin-bottom: ",";\n"])),y.Ay.spacing[3]);const b=function(){var e=(0,u.d4)((function(e){var n;return(null===(n=e.app)||void 0===n?void 0:n.components)||[]})),n=(0,u.d4)((function(e){var n;return(null===(n=e.app)||void 0===n?void 0:n.layouts)||[]})),t=(0,u.d4)((function(e){var n;return(null===(n=e.themes)||void 0===n?void 0:n.activeTheme)||"default"})),o=(0,u.d4)((function(e){var n;return(null===(n=e.themes)||void 0===n?void 0:n.themes)||[]})),c=(0,i.useState)("react"),a=(0,r.A)(c,2),l=a[0],s=a[1],p=(0,i.useState)(!0),b=(0,r.A)(p,2),S=b[0],k=b[1],w=(0,i.useState)(!0),L=(0,r.A)(w,2),T=L[0],N=L[1],I=(0,i.useState)("components"),U=(0,r.A)(I,2),j=U[0],R=U[1],Y=(0,i.useState)([]),B=(0,r.A)(Y,2),O=B[0],F=B[1],$=(0,i.useState)([]),G=(0,r.A)($,2),H=G[0],M=G[1],P=(0,i.useState)(""),V=(0,r.A)(P,2),z=V[0],q=V[1],D=(0,i.useState)(!1),J=(0,r.A)(D,2),W=J[0],K=J[1];(0,i.useEffect)((function(){e.length>0&&0===O.length&&F(e.map((function(e){return e.id}))),n.length>0&&0===H.length&&M(n.map((function(e){return e.id})))}),[e,n,O.length,H.length]),(0,i.useEffect)((function(){Q()}),[l,S,T,j,O,H]);var Q=function(){var c=e.filter((function(e){return O.includes(e.id)})),a=n.filter((function(e){return H.includes(e.id)})),r=o.find((function(e){return e.id===t}))||o[0],s="";switch(l){case"react":default:s=X(c,a,r);break;case"vue":s=Z(c,a,r);break;case"angular":s=_(c,a,r);break;case"html":s=ee(c,a,r)}q(s)},X=function(e,n,t){var o="";return o+="import React from 'react';\n",S&&(o+="import styled from 'styled-components';\n"),o+="\n",T&&t&&(o+="// Theme definition\n",o+="const theme = ".concat(JSON.stringify(t,null,2),";\n\n")),"components"!==j&&"all"!==j||e.forEach((function(e){o+="// ".concat(e.name," component\n"),S&&e.props.customStyles&&(o+="const Styled".concat(e.name.replace(/\s/g,"")," = styled.div`\n"),o+="  ".concat(e.props.customStyles,"\n"),o+="`;\n\n"),o+="const ".concat(e.name.replace(/\s/g,"")," = (props) => {\n"),o+="  return (\n",S&&e.props.customStyles?(o+="    <Styled".concat(e.name.replace(/\s/g,""),">\n"),o+="      <div>".concat(e.name,"</div>\n"),o+="    </Styled".concat(e.name.replace(/\s/g,""),">\n")):(o+='    <div className="'.concat(e.name.toLowerCase().replace(/\s/g,"-"),'">\n'),o+="      <div>".concat(e.name,"</div>\n"),o+="    </div>\n"),o+="  );\n",o+="};\n\n"})),"layouts"!==j&&"all"!==j||n.forEach((function(n){var t,c,a,l;o+="// ".concat(n.name||"Layout"," layout\n"),S&&(o+="const ".concat((null===(c=n.name)||void 0===c?void 0:c.replace(/\s/g,""))||"Layout","Container = styled.div`\n"),o+="  display: grid;\n",o+="  grid-template-columns: repeat(12, 1fr);\n",o+="  gap: 16px;\n",o+="`;\n\n"),o+="const ".concat((null===(t=n.name)||void 0===t?void 0:t.replace(/\s/g,""))||"Layout"," = () => {\n"),o+="  return (\n",S?(o+="    <".concat((null===(a=n.name)||void 0===a?void 0:a.replace(/\s/g,""))||"Layout","Container>\n"),n.components&&n.components.length>0?n.components.forEach((function(n){var t=e.find((function(e){return e.id===n}));t&&(o+="      <".concat(t.name.replace(/\s/g,"")," />\n"))})):o+="      {/* Add components here */}\n",o+="    </".concat((null===(l=n.name)||void 0===l?void 0:l.replace(/\s/g,""))||"Layout","Container>\n")):(o+='    <div className="'.concat((n.name||"layout").toLowerCase().replace(/\s/g,"-"),'-container">\n'),n.components&&n.components.length>0?n.components.forEach((function(n){var t=e.find((function(e){return e.id===n}));t&&(o+="      <".concat(t.name.replace(/\s/g,"")," />\n"))})):o+="      {/* Add components here */}\n",o+="    </div>\n"),o+="  );\n",o+="};\n\n"})),o+="// Exports\n","components"!==j&&"all"!==j||e.forEach((function(e){o+="export { ".concat(e.name.replace(/\s/g,"")," };\n")})),"layouts"!==j&&"all"!==j||n.forEach((function(e){var n;o+="export { ".concat((null===(n=e.name)||void 0===n?void 0:n.replace(/\s/g,""))||"Layout"," };\n")})),o},Z=function(e,n,t){return"// Vue.js code export is coming soon\n// Selected ".concat(e.length," components and ").concat(n.length," layouts\n// Export format: Vue.js\n// Include styles: ").concat(S?"Yes":"No","\n// Include theme: ").concat(T?"Yes":"No","\n// Bundle type: ").concat(j)},_=function(e,n,t){return"// Angular code export is coming soon\n// Selected ".concat(e.length," components and ").concat(n.length," layouts\n// Export format: Angular\n// Include styles: ").concat(S?"Yes":"No","\n// Include theme: ").concat(T?"Yes":"No","\n// Bundle type: ").concat(j)},ee=function(e,n,t){return"\x3c!-- HTML code export is coming soon --\x3e\n\x3c!-- Selected ".concat(e.length," components and ").concat(n.length," layouts --\x3e\n\x3c!-- Export format: HTML --\x3e\n\x3c!-- Include styles: ").concat(S?"Yes":"No"," --\x3e\n\x3c!-- Include theme: ").concat(T?"Yes":"No"," --\x3e\n\x3c!-- Bundle type: ").concat(j," --\x3e")};return i.createElement(E,null,i.createElement(f,{level:4},"Export Code"),i.createElement(v,null,"Export your components and layouts as code in various formats."),i.createElement(d.cG,null),i.createElement(A,null,i.createElement(C,null,i.createElement(g,{strong:!0},"Export Format"),i.createElement(d.l6,{value:l,onChange:s,style:{width:"100%",marginTop:y.Ay.spacing[1]}},i.createElement(h,{value:"react"},"React"),i.createElement(h,{value:"vue"},"Vue.js"),i.createElement(h,{value:"angular"},"Angular"),i.createElement(h,{value:"html"},"HTML"))),i.createElement(C,null,i.createElement(g,{strong:!0},"Bundle Type"),i.createElement(d.sx.Group,{value:j,onChange:function(e){return R(e.target.value)},style:{display:"block",marginTop:y.Ay.spacing[1]}},i.createElement(d.sx,{value:"components"},"Components Only"),i.createElement(d.sx,{value:"layouts"},"Layouts Only"),i.createElement(d.sx,{value:"all"},"Components & Layouts"))),i.createElement(C,null,i.createElement(d.$x,{direction:"vertical"},i.createElement(d.Sc,{checked:S,onChange:function(e){return k(e.target.checked)}},"Include Styles"),i.createElement(d.Sc,{checked:T,onChange:function(e){return N(e.target.checked)}},"Include Theme")))),i.createElement(d.Fc,{message:"Export Preview",description:"This is a preview of the exported code. You can copy it to clipboard or download it as a file.",type:"info",showIcon:!0,icon:i.createElement(m.rUN,null),style:{marginBottom:y.Ay.spacing[3]}}),i.createElement(x,null,z),i.createElement(d.cG,null),i.createElement(d.$x,null,i.createElement(d.$n,{type:"primary",icon:i.createElement(m.wq3,null),onClick:function(){navigator.clipboard.writeText(z).then((function(){K(!0),d.iU.success("Code copied to clipboard"),setTimeout((function(){K(!1)}),2e3)})).catch((function(e){console.error("Failed to copy code:",e),d.iU.error("Failed to copy code")}))}},W?"Copied!":"Copy Code"),i.createElement(d.$n,{icon:i.createElement(m.jsW,null),onClick:function(){var e="app-builder-export.".concat("html"===l?"html":"js"),n=new Blob([z],{type:"text/plain"}),t=URL.createObjectURL(n),o=document.createElement("a");o.href=t,o.download=e,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(t),d.iU.success("Code downloaded as ".concat(e))}},"Download")))}}}]);