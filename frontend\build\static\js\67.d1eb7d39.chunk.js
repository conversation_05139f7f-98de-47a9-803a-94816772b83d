"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[67],{4067:(e,t,n)=>{n.r(t),n.d(t,{default:()=>W});var r,a,l,s,i,o,c,m=n(436),u=n(467),d=n(5544),p=n(7528),A=n(4756),y=n.n(A),g=n(6540),f=n(3016),E=n(677),h=n(9467),w=n(9249),b=n(9740),v=n(7197),x=n(2702),k=n(7355),P=n(1196),C=n(6552),I=n(7450),N=n(5763),F=n(7851),S=n(4741),q=n(4976),B=n(7767),L=n(1468),R=(n(1616),n(9391)),j=n(6191),z=n(6020),G=f.A.Title,T=f.A.Text,_=j.styled.div(r||(r=(0,p.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  padding: ",";\n  background-color: ",";\n"])),z.Ay.spacing[4],z.Ay.colors.neutral[100]),H=(0,j.styled)(E.A)(a||(a=(0,p.A)(["\n  width: 100%;\n  max-width: 480px;\n  box-shadow: ",";\n  border-radius: ",";\n"])),z.Ay.shadows.lg,z.Ay.borderRadius.lg),V=(0,j.styled)(h.A)(l||(l=(0,p.A)(["\n  .ant-form-item-label {\n    text-align: left;\n  }\n"]))),D=(0,j.styled)(w.Ay)(s||(s=(0,p.A)(["\n  width: 100%;\n"]))),O=(0,j.styled)(q.N_)(i||(i=(0,p.A)(["\n  float: right;\n"]))),U=((0,j.styled)(T)(o||(o=(0,p.A)(["\n  display: block;\n  text-align: center;\n  margin-top: 16px;\n"]))),(0,j.styled)(w.Ay)(c||(c=(0,p.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n"]))));const W=function(){var e,t=(0,B.Zp)(),n=(0,B.zy)(),r=((0,L.wA)(),(0,R.As)()),a=r.login,l=r.register,s=r.isAuthenticated,i=r.isLoading,o=r.error,c=h.A.useForm(),p=(0,d.A)(c,1)[0],A=(0,g.useState)(!0),f=(0,d.A)(A,2),E=f[0],q=f[1],j=(0,g.useState)(null),W=(0,d.A)(j,2),Z=W[0],J=W[1],K=(null===(e=n.state)||void 0===e||null===(e=e.from)||void 0===e?void 0:e.pathname)||"/dashboard";(0,g.useEffect)((function(){s&&t(K,{replace:!0})}),[s,t,K]),(0,g.useEffect)((function(){J(o)}),[o]);var M=function(){var e=(0,u.A)(y().mark((function e(n){var r,s,i;return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(J(null),e.prev=1,!E){e.next=9;break}return e.next=5,a(n.email,n.password);case 5:(r=e.sent).success?(b.Ay.success("Login successful!"),t(K,{replace:!0})):J(r.error||"Login failed"),e.next=14;break;case 9:return s={username:n.email.split("@")[0],email:n.email,password:n.password,first_name:n.firstName,last_name:n.lastName},e.next=12,l(s);case 12:(i=e.sent).success?(b.Ay.success("Registration successful!"),t(K,{replace:!0})):J(i.error||"Registration failed");case 14:e.next=20;break;case 16:e.prev=16,e.t0=e.catch(1),console.error("Authentication error:",e.t0),J(e.t0.message||"An error occurred during authentication");case 20:case"end":return e.stop()}}),e,null,[[1,16]])})));return function(t){return e.apply(this,arguments)}}(),Q=function(e){b.Ay.info("".concat(e," login is not implemented in this demo"))};return g.createElement(_,null,g.createElement(H,null,g.createElement("div",{style:{textAlign:"center",marginBottom:z.Ay.spacing[4]}},g.createElement(G,{level:2,style:{margin:0}},E?"Welcome Back":"Create Account"),g.createElement(T,{type:"secondary"},E?"Sign in to continue to App Builder":"Register to start building amazing applications")),Z&&g.createElement(v.A,{message:"Authentication Error",description:Z,type:"error",showIcon:!0,style:{marginBottom:z.Ay.spacing[4]}}),g.createElement(V,{form:p,name:"auth_form",layout:"vertical",onFinish:M,initialValues:{remember:!0}},!E&&g.createElement(x.A,{style:{display:"flex",gap:z.Ay.spacing[3]}},g.createElement(h.A.Item,{name:"firstName",label:"First Name",rules:[{required:!0,message:"Please enter your first name"}],style:{flex:1}},g.createElement(k.A,{placeholder:"First Name"})),g.createElement(h.A.Item,{name:"lastName",label:"Last Name",rules:[{required:!0,message:"Please enter your last name"}],style:{flex:1}},g.createElement(k.A,{placeholder:"Last Name"}))),g.createElement(h.A.Item,{name:"email",label:E?"Email or Username":"Email",rules:[{required:!0,message:"Please enter your email"}].concat((0,m.A)(E?[]:[{type:"email",message:"Please enter a valid email"}]))},g.createElement(k.A,{prefix:g.createElement(I.A,null),placeholder:"Email"})),g.createElement(h.A.Item,{name:"password",label:"Password",rules:[{required:!0,message:"Please enter your password"}]},g.createElement(k.A.Password,{prefix:g.createElement(N.A,null),placeholder:"Password"})),!E&&g.createElement(h.A.Item,{name:"confirmPassword",label:"Confirm Password",dependencies:["password"],rules:[{required:!0,message:"Please confirm your password"},function(e){var t=e.getFieldValue;return{validator:function(e,n){return n&&t("password")!==n?Promise.reject(new Error("The two passwords do not match")):Promise.resolve()}}}]},g.createElement(k.A.Password,{prefix:g.createElement(N.A,null),placeholder:"Confirm Password"})),E&&g.createElement(h.A.Item,null,g.createElement(h.A.Item,{name:"remember",valuePropName:"checked",noStyle:!0},g.createElement(P.A,null,"Remember me")),g.createElement(O,{to:"/forgot-password"},"Forgot password?")),g.createElement(h.A.Item,null,g.createElement(D,{type:"primary",htmlType:"submit",size:"large",loading:i},E?"Sign In":"Create Account"))),g.createElement(C.A,null,g.createElement(T,{type:"secondary"},"Or continue with")),g.createElement(x.A,{direction:"horizontal",style:{width:"100%",justifyContent:"center",gap:z.Ay.spacing[3],marginBottom:z.Ay.spacing[4]}},g.createElement(U,{icon:g.createElement(F.A,null),onClick:function(){return Q("Google")}},"Google"),g.createElement(U,{icon:g.createElement(S.A,null),onClick:function(){return Q("GitHub")}},"GitHub")),g.createElement("div",{style:{textAlign:"center"}},g.createElement(T,{type:"secondary"},E?"Don't have an account? ":"Already have an account? ",g.createElement(w.Ay,{type:"link",onClick:function(){p.resetFields(),q(!E),J(null)},style:{padding:0}},E?"Sign up now":"Sign in")))))}}}]);