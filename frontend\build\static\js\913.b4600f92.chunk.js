"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[913],{3913:(e,t,n)=>{n.r(t),n.d(t,{default:()=>h});var r=n(4467),c=n(5544),a=n(6540),l=n(7197),o=n(2652),s=n(9249),i=n(6914),m=n(6552),u=n(7122),E=n(3740),p=n(8602),d=n(8e3),A=n(1295),b=n(378),f=n(8990),v=n(4976),y=n(1468),g=n(5331),w=n(7053),k=n(6894),S=n(8812);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function j(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const h=function(){var e=(0,y.wA)(),t=(0,a.useState)(!0),n=(0,c.A)(t,2),r=n[0],O=n[1],h=(0,a.useState)(w.A.getConnectionState()),D=(0,c.A)(h,2),L=D[0],N=D[1],T=(0,a.useState)({websocket:L.connected,api:!0,database:!0,storage:Math.random()>.2}),P=(0,c.A)(T,2),C=P[0],I=P[1];(0,a.useEffect)((function(){e((0,g.tI)("home"));var t=setTimeout((function(){O(!1)}),1e3);return function(){return clearTimeout(t)}}),[e]),(0,a.useEffect)((function(){var e=function(){var e=w.A.getConnectionState();N(e),I((function(e){return j(j({},e),{},{websocket:!0})}))},t=function(){var e=w.A.getConnectionState();N(e),I((function(e){return j(j({},e),{},{websocket:!1})}))};return w.A.addEventListener("connect",e),w.A.addEventListener("disconnect",t),N(w.A.getConnectionState()),I((function(e){return j(j({},e),{},{websocket:L.connected})})),function(){w.A.removeEventListener("connect",e),w.A.removeEventListener("disconnect",t)}}),[]);var W,_=(W=Object.values(C)).every((function(e){return!0===e}))?{status:"success",text:"All Systems Operational"}:W.filter((function(e){return!1===e})).length>1?{status:"error",text:"Multiple Systems Down"}:{status:"warning",text:"Partial System Outage"};return r?a.createElement(k.LN,null,a.createElement(k.p1,{level:2},"Welcome to App Builder"),a.createElement(k.T6,null,"Build, test, and deploy your applications with ease."),a.createElement(S.O2,{cards:4})):a.createElement(k.LN,null,a.createElement(k.p1,{level:2},"Welcome to App Builder"),a.createElement(k.T6,null,"Build, test, and deploy your applications with ease. Monitor your WebSocket connections, diagnose network issues, and optimize performance."),a.createElement(l.A,{type:_.status,message:_.text,showIcon:!0,style:{marginBottom:24}}),a.createElement(k.JT,{columns:2,columnsSm:1},a.createElement(k.Lv,null,a.createElement(k.p1,{level:4},"System Status"),a.createElement(o.A,{size:"large",dataSource:[{name:"WebSocket Service",status:C.websocket,icon:a.createElement(E.A,null),route:"/websocket-diagnostics"},{name:"API Service",status:C.api,icon:a.createElement(E.A,null),route:"/dashboard"},{name:"Database",status:C.database,icon:a.createElement(p.A,null),route:"/dashboard"},{name:"Storage Service",status:C.storage,icon:a.createElement(p.A,null),route:"/dashboard"}],renderItem:function(e){return a.createElement(o.A.Item,{actions:[a.createElement(v.N_,{to:e.route},a.createElement(s.Ay,{type:"link",icon:a.createElement(d.A,null)},"Details"))]},a.createElement(o.A.Item.Meta,{avatar:e.icon,title:a.createElement(k.n5,{justify:"space-between"},a.createElement("span",null,e.name),e.status?a.createElement(i.A,{color:"success",icon:a.createElement(A.A,null)},"Operational"):a.createElement(i.A,{color:"error",icon:a.createElement(b.A,null)},"Down"))}))}})),a.createElement(k.Lv,null,a.createElement(k.p1,{level:4},"Quick Actions"),a.createElement(k.n5,{direction:"column",gap:16},a.createElement(v.N_,{to:"/"},a.createElement(k.jn,{icon:a.createElement(f.A,null),block:!0},"App Builder")),a.createElement(v.N_,{to:"/app-builder"},a.createElement(k.jn,{icon:a.createElement(f.A,null),block:!0},"App Builder (Alternative)")),a.createElement(v.N_,{to:"/websocket"},a.createElement(k.jn,{icon:a.createElement(E.A,null),block:!0},"WebSocket Manager")),a.createElement(v.N_,{to:"/websocket-diagnostics"},a.createElement(k.jn,{icon:a.createElement(E.A,null),block:!0},"WebSocket Diagnostics")),a.createElement(v.N_,{to:"/network-diagnostic"},a.createElement(k.jn,{icon:a.createElement(E.A,null),block:!0},"Network Diagnostics"))))),a.createElement(m.A,{style:{margin:"32px 0 24px"}}),a.createElement(k.JT,{columns:3,columnsMd:2,columnsSm:1},a.createElement(k.Lv,null,a.createElement(u.A,{title:"WebSocket Status",value:L.connected?"Connected":"Disconnected",valueStyle:{color:L.connected?"#3f8600":"#cf1322"}}),a.createElement("div",{style:{marginTop:16}},a.createElement(v.N_,{to:"/websocket-diagnostics"},a.createElement(s.Ay,{type:"primary",size:"small"},"View Details")))),a.createElement(k.Lv,null,a.createElement(u.A,{title:"Connection Uptime",value:L.connected?"Active":"Inactive",suffix:L.connected?"now":"",valueStyle:{color:L.connected?"#3f8600":"#cf1322"}}),a.createElement("div",{style:{marginTop:16}},a.createElement(v.N_,{to:"/websocket-diagnostics"},a.createElement(s.Ay,{type:"primary",size:"small"},"View Details")))),a.createElement(k.Lv,null,a.createElement(u.A,{title:"Reconnect Attempts",value:L.reconnectAttempts||0,valueStyle:{color:L.reconnectAttempts>0?"#faad14":"#3f8600"}}),a.createElement("div",{style:{marginTop:16}},a.createElement(s.Ay,{type:"primary",size:"small",onClick:function(){return w.A.reconnect()},disabled:L.connected},"Reconnect")))),L.lastError&&a.createElement(l.A,{type:"error",message:"WebSocket Error",description:a.createElement("div",null,a.createElement("div",null,L.lastError.message),L.lastError.timestamp&&a.createElement("div",{style:{color:"rgba(0, 0, 0, 0.45)",marginTop:8}},new Date(L.lastError.timestamp).toLocaleString())),showIcon:!0,style:{marginTop:24}}))}}}]);