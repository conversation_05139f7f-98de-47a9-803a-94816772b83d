"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[6618],{6827:(n,e,t)=>{t.d(e,{A:()=>O});var r,o,a,i,l,c,s,d,p,m=t(5544),u=t(57528),f=t(96540),g=t(1807),h=t(35346),x=t(70572),v=t(82569),b=t(57683),y=g.PE.Header,w=g.o5.Title,E=(0,x.Ay)(y)(r||(r=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 24px;\n  background-color: var(--color-surface);\n  border-bottom: 1px solid var(--color-border-light);\n  box-shadow: var(--shadow-md);\n  position: sticky;\n  top: 0;\n  z-index: var(--z-sticky);\n  transition: all 0.3s ease;\n  min-height: 64px;\n\n  /* Ensure proper contrast for header background */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: var(--color-surface);\n    opacity: 0.98;\n    z-index: -1;\n  }\n\n  /* Focus management for accessibility */\n  &:focus-within {\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: 0 16px;\n    min-height: 56px;\n  }\n\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n  }\n"]))),A=x.Ay.div(o||(o=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  z-index: 1;\n\n  /* Ensure proper focus styles for accessibility */\n  &:focus {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 2px;\n    border-radius: 4px;\n  }\n\n  &:hover {\n    transform: scale(1.02);\n  }\n\n  &:active {\n    transform: scale(0.98);\n  }\n\n  .logo-icon {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 40px;\n    height: 40px;\n    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));\n    border-radius: 8px;\n    color: white;\n    font-size: 20px;\n    box-shadow: var(--shadow-sm);\n    transition: all 0.3s ease;\n\n    /* Ensure icon contrast meets WCAG AA standards */\n    filter: contrast(1.1);\n\n    &:hover {\n      box-shadow: var(--shadow-md);\n      transform: translateY(-1px);\n    }\n  }\n\n  .logo-text {\n    color: var(--color-text);\n    margin: 0;\n    font-weight: 600;\n    font-size: 20px;\n    line-height: 1.2;\n\n    /* Ensure text contrast meets WCAG AA standards (4.5:1) */\n    text-shadow: 0 0 1px var(--color-background);\n\n    @media (max-width: 480px) {\n      display: none;\n    }\n\n    /* High contrast mode support */\n    @media (prefers-contrast: high) {\n      font-weight: 700;\n      text-shadow: 1px 1px 2px var(--color-background);\n    }\n  }\n\n  /* Keyboard navigation support */\n  &[tabindex] {\n    border-radius: 4px;\n  }\n\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n\n    &:hover {\n      transform: none;\n    }\n  }\n"]))),k=x.Ay.div(a||(a=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  position: relative;\n  z-index: 1;\n\n  @media (max-width: 768px) {\n    gap: 8px;\n  }\n"]))),S=(0,x.Ay)(g.$n)(i||(i=(0,u.A)(["\n  display: none;\n\n  @media (max-width: 768px) {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 40px;\n    height: 40px;\n    border-radius: 8px;\n    border: 1px solid var(--color-border);\n    background-color: var(--color-surface);\n    color: var(--color-text);\n    transition: all 0.3s ease;\n\n    /* Ensure button meets WCAG contrast requirements */\n    &:hover, &:focus {\n      border-color: var(--color-primary);\n      color: var(--color-primary);\n      background-color: var(--color-background-secondary);\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &:active {\n      transform: scale(0.95);\n    }\n\n    /* High contrast mode support */\n    @media (prefers-contrast: high) {\n      border-width: 2px;\n      font-weight: 600;\n    }\n\n    /* Keyboard focus indicator */\n    &:focus-visible {\n      outline: 2px solid var(--color-primary);\n      outline-offset: 2px;\n    }\n  }\n"]))),C=x.Ay.div(l||(l=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 16px;\n\n  @media (max-width: 768px) {\n    display: none;\n  }\n"]))),z=x.Ay.div(c||(c=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  border-radius: 20px;\n  background-color: var(--color-background-secondary);\n  border: 1px solid var(--color-border-light);\n  font-size: 12px;\n  color: var(--color-text);\n  font-weight: 500;\n  transition: all 0.3s ease;\n\n  /* Ensure status text meets WCAG AA contrast ratio (4.5:1) */\n  text-shadow: 0 0 1px var(--color-background-secondary);\n\n  /* Enhanced visual hierarchy */\n  box-shadow: var(--shadow-sm);\n\n  &:hover {\n    background-color: var(--color-background-tertiary);\n    box-shadow: var(--shadow-md);\n  }\n\n  .status-dot {\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background-color: var(--color-success);\n    box-shadow: 0 0 4px var(--color-success);\n    animation: pulse 2s infinite;\n    position: relative;\n\n    /* Add a subtle glow for better visibility */\n    &::after {\n      content: '';\n      position: absolute;\n      top: -2px;\n      left: -2px;\n      right: -2px;\n      bottom: -2px;\n      border-radius: 50%;\n      background-color: var(--color-success);\n      opacity: 0.3;\n      animation: pulse 2s infinite;\n    }\n  }\n\n  /* High contrast mode adjustments */\n  @media (prefers-contrast: high) {\n    border-width: 2px;\n    font-weight: 600;\n    background-color: var(--color-surface);\n\n    .status-dot {\n      border: 2px solid var(--color-text);\n    }\n  }\n\n  @keyframes pulse {\n    0% {\n      opacity: 1;\n      transform: scale(1);\n    }\n    50% {\n      opacity: 0.7;\n      transform: scale(1.1);\n    }\n    100% {\n      opacity: 1;\n      transform: scale(1);\n    }\n  }\n\n  @media (prefers-reduced-motion: reduce) {\n    .status-dot {\n      animation: none;\n\n      &::after {\n        animation: none;\n      }\n    }\n  }\n"]))),P=(0,x.Ay)(g._s)(s||(s=(0,u.A)(["\n  .ant-drawer-content {\n    background-color: var(--color-surface);\n    border-left: 1px solid var(--color-border-light);\n  }\n\n  .ant-drawer-header {\n    background-color: var(--color-surface);\n    border-bottom: 1px solid var(--color-border-light);\n    padding: 16px 24px;\n\n    .ant-drawer-title {\n      color: var(--color-text);\n      font-weight: 600;\n      font-size: 18px;\n    }\n\n    .ant-drawer-close {\n      color: var(--color-text-secondary);\n      transition: all 0.3s ease;\n\n      &:hover {\n        color: var(--color-primary);\n        background-color: var(--color-background-secondary);\n      }\n\n      &:focus {\n        outline: 2px solid var(--color-primary);\n        outline-offset: 2px;\n      }\n    }\n  }\n\n  .ant-drawer-body {\n    padding: 24px;\n    background-color: var(--color-surface);\n  }\n\n  /* Ensure proper contrast for drawer overlay */\n  .ant-drawer-mask {\n    background-color: rgba(0, 0, 0, 0.45);\n    backdrop-filter: blur(4px);\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    .ant-drawer-content {\n      border-left-width: 3px;\n    }\n\n    .ant-drawer-header {\n      border-bottom-width: 2px;\n    }\n  }\n"]))),T=x.Ay.div(d||(d=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n"]))),j=x.Ay.div(p||(p=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  padding: 16px;\n  border-radius: 8px;\n  background-color: var(--color-background-secondary);\n  border: 1px solid var(--color-border-light);\n  transition: all 0.3s ease;\n\n  &:hover {\n    background-color: var(--color-background-tertiary);\n    box-shadow: var(--shadow-sm);\n  }\n\n  .section-title {\n    font-size: 16px;\n    font-weight: 600;\n    color: var(--color-text);\n    margin-bottom: 8px;\n\n    /* Ensure title text meets WCAG AA contrast */\n    text-shadow: 0 0 1px var(--color-background-secondary);\n  }\n\n  /* High contrast mode adjustments */\n  @media (prefers-contrast: high) {\n    border-width: 2px;\n\n    .section-title {\n      font-weight: 700;\n      text-shadow: 1px 1px 2px var(--color-background);\n    }\n  }\n"])));const O=function(n){var e=n.title,t=void 0===e?"App Builder 201":e,r=n.showStatus,o=void 0===r||r,a=n.onLogoClick,i=n.children,l=(0,v.ZV)(),c=l.isDarkMode,s=l.themeMode,d=(0,f.useState)(!1),p=(0,m.A)(d,2),u=p[0],g=p[1],x=function(n){"keydown"===n.type&&"Enter"!==n.key&&" "!==n.key||(n.preventDefault(),a?a():window.scrollTo({top:0,behavior:"smooth"}))},y=function(){g(!1)},O=function(n){"Escape"===n.key&&u&&y()};return f.useEffect((function(){return document.addEventListener("keydown",O),function(){document.removeEventListener("keydown",O)}}),[u]),f.createElement(f.Fragment,null,f.createElement(E,{role:"banner","aria-label":"Main navigation"},f.createElement(A,{onClick:x,onKeyDown:x,tabIndex:0,role:"button","aria-label":"".concat(t," - Go to homepage")},f.createElement("div",{className:"logo-icon","aria-hidden":"true"},f.createElement(h.rS9,null)),f.createElement(w,{level:4,className:"logo-text","aria-hidden":"true"},t)),f.createElement(k,{role:"toolbar","aria-label":"Header actions"},f.createElement(C,null,o&&f.createElement(z,{role:"status","aria-label":"Application status: Online",title:"Application is online and ready"},f.createElement("div",{className:"status-dot","aria-hidden":"true"}),f.createElement("span",null,"Online")),f.createElement("div",{role:"group","aria-label":"Theme controls"},f.createElement(b.A,null)),i&&f.createElement("div",{role:"group","aria-label":"Additional actions"},i)),f.createElement(S,{type:"text",icon:f.createElement(h.imj,null),onClick:function(){g(!u)},"aria-label":"".concat(u?"Close":"Open"," mobile menu"),"aria-expanded":u,"aria-controls":"mobile-navigation-drawer"}))),f.createElement(P,{title:"Navigation Menu",placement:"right",onClose:y,open:u,width:280,id:"mobile-navigation-drawer","aria-label":"Mobile navigation menu",closeIcon:f.createElement("span",{"aria-label":"Close menu"},"×"),maskClosable:!0,keyboard:!0,destroyOnClose:!1},f.createElement(T,{role:"navigation","aria-label":"Mobile menu content"},f.createElement(j,null,f.createElement("div",{className:"section-title",id:"theme-section"},"Theme Settings"),f.createElement("div",{role:"group","aria-labelledby":"theme-section"},f.createElement(b.A,{showDropdown:!1}),f.createElement("div",{style:{marginTop:"8px",fontSize:"12px",color:"var(--color-text-secondary)"}},"Current: ","system"===s?"System (".concat(c?"Dark":"Light",")"):"dark"===s?"Dark":"Light"))),o&&f.createElement(j,null,f.createElement("div",{className:"section-title",id:"status-section"},"Application Status"),f.createElement("div",{role:"group","aria-labelledby":"status-section"},f.createElement(z,{role:"status","aria-label":"Application status: Online"},f.createElement("div",{className:"status-dot","aria-hidden":"true"}),f.createElement("span",null,"Online")))),i&&f.createElement(j,null,f.createElement("div",{className:"section-title",id:"actions-section"},"Additional Actions"),f.createElement("div",{role:"group","aria-labelledby":"actions-section"},i)))))}},9341:(n,e,t)=>{t.d(e,{r3:()=>D});var r=t(64467),o=t(60436),a=t(96540);t(10467);var i,l,c,s,d,p=t(5544),m=(t(53986),t(57528)),u=(t(54756),t(1807)),f=t(70572);f.Ay.div(i||(i=(0,m.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: ",";\n  width: 100%;\n"])),(function(n){return n.minHeight||"200px"})),f.Ay.div(l||(l=(0,m.A)(["\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: ",";\n  width: 100%;\n  color: ",";\n  text-align: center;\n  padding: 16px;\n"])),(function(n){return n.minHeight||"200px"}),(function(n){return n.theme.colorPalette.error})),f.Ay.button(c||(c=(0,m.A)(["\n  margin-top: 16px;\n  padding: 8px 16px;\n  background-color: ",";\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  \n  &:hover {\n    background-color: ",";\n  }\n"])),(function(n){return n.theme.colorPalette.primary}),(function(n){return n.theme.colorPalette.primaryDark})),t(58168),t(82284),f.Ay.div(s||(s=(0,m.A)(["\n  position: relative;\n  overflow-y: auto;\n  width: 100%;\n  height: ",";\n"])),(function(n){return n.height||"400px"})),f.Ay.div(d||(d=(0,m.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n"])));var g,h,x,v,b,y,w,E=t(35346);function A(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),t.push.apply(t,r)}return t}function k(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?A(Object(t),!0).forEach((function(e){(0,r.A)(n,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):A(Object(t)).forEach((function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(t,e))}))}return n}t(55876),u.o5.Title;var S=u.o5.Text,C=u.o5.Paragraph,z=(0,f.Ay)(u.Zp)(g||(g=(0,m.A)(["\n  position: fixed;\n  bottom: ",";\n  top: ",";\n  right: 20px;\n  width: ",";\n  z-index: 1000;\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);\n  transition: all 0.3s ease;\n"])),(function(n){return n.expanded?"20px":"auto"}),(function(n){return n.expanded?"auto":"20px"}),(function(n){return n.expanded?"600px":"300px"})),P=f.Ay.div(h||(h=(0,m.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n"]))),T=f.Ay.div(x||(x=(0,m.A)(["\n  display: flex;\n  align-items: center;\n"]))),j=f.Ay.div(v||(v=(0,m.A)(["\n  max-height: 150px;\n  overflow-y: auto;\n"]))),O=f.Ay.div(b||(b=(0,m.A)(["\n  margin-bottom: 8px;\n"]))),M=(0,f.Ay)(S)(y||(y=(0,m.A)(["\n  font-size: 12px;\n"]))),L=(0,f.Ay)(C)(w||(w=(0,m.A)(["\n  font-size: 12px;\n  text-align: center;\n"])));const D=function(n){var e=n.visible,t=void 0!==e&&e,r=(0,a.useState)({fps:0,memory:{used:0,total:0,limit:0},timing:{domComplete:0,domInteractive:0,loadEvent:0,firstContentfulPaint:0,largestContentfulPaint:0},resources:{count:0,totalSize:0},longTasks:[]}),i=(0,p.A)(r,2),l=i[0],c=i[1],s=(0,a.useState)(!1),d=(0,p.A)(s,2),m=d[0],f=d[1],g=(0,a.useState)(t),h=(0,p.A)(g,2),x=h[0],v=h[1];(0,a.useEffect)((function(){if(x)return function(){var n=0,e=performance.now(),t=function(){var r=performance.now(),o=r-e;if(o>=1e3){var a=Math.round(1e3*n/o);c((function(n){return k(k({},n),{},{fps:a})})),n=0,e=r}n++,requestAnimationFrame(t)},r=requestAnimationFrame(t);if(window.performance&&window.performance.memory&&c((function(n){return k(k({},n),{},{memory:{used:Math.round(window.performance.memory.usedJSHeapSize/1048576),total:Math.round(window.performance.memory.totalJSHeapSize/1048576),limit:Math.round(window.performance.memory.jsHeapSizeLimit/1048576)}})})),performance.timing){var a=performance.timing;c((function(n){return k(k({},n),{},{timing:{domComplete:a.domComplete-a.navigationStart,domInteractive:a.domInteractive-a.navigationStart,loadEvent:a.loadEventEnd-a.navigationStart,firstContentfulPaint:0,largestContentfulPaint:0}})}))}var i=performance.getEntriesByType("resource");c((function(n){return k(k({},n),{},{resources:{count:i.length,totalSize:i.reduce((function(n,e){return n+(e.transferSize||0)}),0)/1048576}})})),performance.getEntriesByType("paint").forEach((function(n){"first-contentful-paint"===n.name&&c((function(e){return k(k({},e),{},{timing:k(k({},e.timing),{},{firstContentfulPaint:n.startTime})})}))}));var l=new PerformanceObserver((function(n){var e=n.getEntries(),t=e[e.length-1];c((function(n){return k(k({},n),{},{timing:k(k({},n.timing),{},{largestContentfulPaint:t.startTime})})}))}));l.observe({type:"largest-contentful-paint",buffered:!0});var s=new PerformanceObserver((function(n){var e=n.getEntries();c((function(n){return k(k({},n),{},{longTasks:[].concat((0,o.A)(n.longTasks),(0,o.A)(e)).slice(-10)})}))}));return s.observe({type:"longtask",buffered:!0}),function(){cancelAnimationFrame(r),l.disconnect(),s.disconnect()}}()}),[x]);var b=function(){v(!x)};if(!x)return a.createElement(u.m_,{title:"Show Performance Monitor"},a.createElement(u.$n,{type:"primary",shape:"circle",icon:a.createElement(E.zpd,null),onClick:b,style:{position:"fixed",bottom:"20px",right:"20px",zIndex:1e3}}));var y=function(n){return n>=55?"#52c41a":n>=30?"#faad14":"#f5222d"},w=function(){return l.memory.total?Math.round(l.memory.used/l.memory.total*100):0},A=function(){var n=w();return n<70?"#52c41a":n<90?"#faad14":"#f5222d"};return a.createElement("div",{className:"performance-monitor"},a.createElement(z,{expanded:m,title:a.createElement(P,null,a.createElement("span",null,a.createElement(E.zpd,null)," Performance Monitor"),a.createElement(T,null,a.createElement(u.$n,{type:"text",icon:a.createElement(E.KF4,null),onClick:function(){c({fps:0,memory:{used:0,total:0,limit:0},timing:{domComplete:0,domInteractive:0,loadEvent:0,firstContentfulPaint:0,largestContentfulPaint:0},resources:{count:0,totalSize:0},longTasks:[]}),performance.clearMarks(),performance.clearMeasures(),performance.clearResourceTimings()},style:{marginRight:"8px"},"aria-label":"Reset metrics"}),a.createElement(u.$n,{type:"text",icon:a.createElement(E.cd5,null),onClick:function(){f(!m)},style:{marginRight:"8px"},"aria-label":m?"Collapse view":"Expand view"}),a.createElement(u.$n,{type:"text",icon:a.createElement(E.r$3,null),onClick:b,"aria-label":"Close performance monitor"}))),bodyStyle:{padding:m?"16px":"8px"}},a.createElement(u.fI,{gutter:[16,16]},a.createElement(u.fv,{span:m?12:24},a.createElement(u.jL,{title:"FPS",value:l.fps,suffix:"fps",valueStyle:{color:y(l.fps)},prefix:a.createElement(E.L8Y,null)}),m&&a.createElement(u.ke,{percent:l.fps/60*100,strokeColor:y(l.fps),showInfo:!1,size:"small",style:{marginTop:"8px"}})),l.memory.used>0&&a.createElement(u.fv,{span:m?12:24},a.createElement(u.jL,{title:"Memory Usage",value:l.memory.used,suffix:"MB",valueStyle:{color:A()},prefix:a.createElement(E.cd5,null)}),m&&a.createElement(u.m_,{title:"".concat(l.memory.used,"MB / ").concat(l.memory.total,"MB")},a.createElement(u.ke,{percent:w(),strokeColor:A(),showInfo:!1,size:"small",style:{marginTop:"8px"}}))),m&&a.createElement(a.Fragment,null,a.createElement(u.fv,{span:24},a.createElement(u.cG,{orientation:"left"},"Page Load Timing")),a.createElement(u.fv,{span:12},a.createElement(u.jL,{title:"DOM Interactive",value:l.timing.domInteractive,suffix:"ms",valueStyle:{fontSize:"16px"}})),a.createElement(u.fv,{span:12},a.createElement(u.jL,{title:"DOM Complete",value:l.timing.domComplete,suffix:"ms",valueStyle:{fontSize:"16px"}})),a.createElement(u.fv,{span:12},a.createElement(u.jL,{title:"First Contentful Paint",value:Math.round(l.timing.firstContentfulPaint),suffix:"ms",valueStyle:{fontSize:"16px"}})),a.createElement(u.fv,{span:12},a.createElement(u.jL,{title:"Largest Contentful Paint",value:Math.round(l.timing.largestContentfulPaint),suffix:"ms",valueStyle:{fontSize:"16px"}})),a.createElement(u.fv,{span:24},a.createElement(u.cG,{orientation:"left"},"Resources")),a.createElement(u.fv,{span:12},a.createElement(u.jL,{title:"Resource Count",value:l.resources.count,valueStyle:{fontSize:"16px"}})),a.createElement(u.fv,{span:12},a.createElement(u.jL,{title:"Total Size",value:l.resources.totalSize.toFixed(2),suffix:"MB",valueStyle:{fontSize:"16px"}})),l.longTasks.length>0&&a.createElement(a.Fragment,null,a.createElement(u.fv,{span:24},a.createElement(u.cG,{orientation:"left"},"Long Tasks"),a.createElement(j,null,l.longTasks.map((function(n,e){return a.createElement(O,{key:e},a.createElement(S,{type:"danger"},a.createElement(E.v7y,null)," Task blocked for ",n.duration.toFixed(2),"ms"),a.createElement("div",null,a.createElement(M,{type:"secondary"},new Date(n.startTime).toLocaleTimeString())))}))))),a.createElement(u.fv,{span:24},a.createElement(u.cG,null),a.createElement(L,{type:"secondary"},a.createElement(E.hWy,null)," Performance monitoring active"))))))};t(81415)},26390:(n,e,t)=>{t.d(e,{$0:()=>a,e9:()=>i});var r="http://localhost:8000",o="ws://localhost:8000/ws";console.log("Environment Variables:"),console.log("API_URL:","http://localhost:8000"),console.log("WS_URL:","ws://localhost:8000/ws"),console.log("WS_HOST:","localhost:8000"),console.log("WS_ENDPOINT:","app_builder"),console.log("BACKEND_HOST:","localhost"),console.log("ENV:","development"),console.log("DEBUG:",!0);var a=function(n){var e,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.replace(/^\/+|\/+$/g,"");t.forceSecure||"undefined"!=typeof window&&window.location.protocol;var a=o.endsWith("/")?o:"".concat(o,"/");e="".concat(a).concat(r,"/"),console.log("Generated WebSocket URL:",e);var i=e.replace(/([^:]\/)\/+/g,"$1");return console.log("WebSocket URL for endpoint '".concat(r,"': ").concat(i)),i},i=function(n){var e=n.replace(/^\/+|\/+$/g,""),t=r.endsWith("/")?r.slice(0,-1):r;return"".concat(t,"/api/").concat(e,"/")}},38812:(n,e,t)=>{t.d(e,{O2:()=>c});var r,o=t(57528),a=t(96540),i=t(1807),l=(0,t(70572).Ay)(i.Zp)(r||(r=(0,o.A)(["\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n  margin-bottom: 16px;\n"]))),c=function(n){var e=n.cards,t=void 0===e?4:e;return a.createElement("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(300px, 1fr))",gap:24}},Array.from({length:t}).map((function(n,e){return a.createElement(l,{key:"dashboard-skeleton-card-".concat(e)},a.createElement(i.EA,{active:!0,paragraph:{rows:2}}))})))}},57683:(n,e,t)=>{t.d(e,{A:()=>C});var r,o,a,i,l,c,s,d,p=t(5544),m=t(57528),u=t(96540),f=t(16918),g=t(35346),h=t(70572),x=t(82569),v=(0,h.i7)(r||(r=(0,m.A)(["\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n"]))),b=(0,h.i7)(o||(o=(0,m.A)(["\n  from {\n    opacity: 0;\n    transform: scale(0.8);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n"]))),y=h.Ay.div(a||(a=(0,m.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"]))),w=(0,h.Ay)(f.$n)(i||(i=(0,m.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  border: 1px solid var(--color-border);\n  background-color: var(--color-surface);\n  color: var(--color-text);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n\n  /* Ensure WCAG AA contrast compliance */\n  &:hover, &:focus {\n    border-color: var(--color-primary);\n    color: var(--color-primary);\n    background-color: var(--color-background-secondary);\n    transform: scale(1.05);\n    box-shadow: var(--shadow-md);\n  }\n\n  &:focus-visible {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 2px;\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n\n  .anticon {\n    font-size: 18px;\n    animation: "," 0.3s ease;\n\n    /* Ensure icon has sufficient contrast */\n    filter: contrast(1.1);\n  }\n\n  &.rotating .anticon {\n    animation: "," 0.5s ease;\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border-width: 2px;\n    font-weight: 600;\n\n    &:hover, &:focus {\n      border-width: 3px;\n    }\n\n    .anticon {\n      filter: contrast(1.3);\n    }\n  }\n\n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n\n    &:hover {\n      transform: none;\n    }\n\n    &:active {\n      transform: none;\n    }\n\n    .anticon {\n      animation: none;\n    }\n\n    &.rotating .anticon {\n      animation: none;\n    }\n  }\n"])),b,v),E=h.Ay.div(l||(l=(0,m.A)(["\n  min-width: 180px;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: 8px;\n  box-shadow: var(--shadow-lg);\n  padding: 4px;\n"]))),A=h.Ay.div(c||(c=(0,m.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 12px 16px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border-radius: 6px;\n  margin: 2px 0;\n  color: var(--color-text);\n\n  &:hover {\n    background-color: var(--color-background-secondary);\n    transform: translateX(2px);\n  }\n\n  &:focus {\n    outline: 2px solid var(--color-primary);\n    outline-offset: 1px;\n  }\n\n  &.active {\n    background-color: var(--color-primary);\n    color: white;\n    box-shadow: var(--shadow-sm);\n  }\n\n  .option-content {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n  }\n\n  .anticon {\n    font-size: 16px;\n\n    /* Ensure icon contrast in active state */\n    filter: ",";\n  }\n\n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border: 1px solid var(--color-border);\n\n    &:hover {\n      border-color: var(--color-primary);\n    }\n\n    &.active {\n      border: 2px solid white;\n    }\n  }\n\n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: background-color 0.2s ease;\n\n    &:hover {\n      transform: none;\n    }\n  }\n"])),(function(n){var e;return null!==(e=n.className)&&void 0!==e&&e.includes("active")?"none":"contrast(1.1)"})),k=h.Ay.span(s||(s=(0,m.A)(["\n  font-size: 14px;\n  font-weight: 500;\n  line-height: 1.2;\n"]))),S=h.Ay.div(d||(d=(0,m.A)(["\n  font-size: 12px;\n  color: ",";\n  margin-top: 2px;\n  line-height: 1.2;\n\n  /* Ensure description text meets contrast requirements */\n  opacity: ",";\n"])),(function(n){return n.active?"rgba(255, 255, 255, 0.8)":"var(--color-text-secondary)"}),(function(n){return n.active?.9:.8}));const C=function(n){var e=n.showDropdown,t=void 0===e||e,r=n.size,o=void 0===r?"default":r,a=(0,x.ZV)(),i=a.isDarkMode,l=a.themeMode,c=a.toggleDarkMode,s=a.setThemeMode,d=a.systemPrefersDark,m=(0,u.useState)(!1),h=(0,p.A)(m,2),v=h[0],b=h[1],C=function(n){b(!0),s(n),setTimeout((function(){return b(!1)}),500)},z=function(){return"system"===l?u.createElement(g.zlw,null):i?u.createElement(g.IO1,null):u.createElement(g.qVE,null)},P=function(){switch(l){case"light":return"Light mode";case"dark":return"Dark mode";case"system":return"System mode (".concat(d?"dark":"light",")");default:return"Toggle theme"}},T={items:[{key:"light",icon:u.createElement(g.qVE,null),label:"Light",description:"Light theme"},{key:"dark",icon:u.createElement(g.IO1,null),label:"Dark",description:"Dark theme"},{key:"system",icon:u.createElement(g.zlw,null),label:"System",description:"Follow system preference"}].map((function(n){return{key:n.key,label:u.createElement(A,{className:l===n.key?"active":"",onClick:function(){return C(n.key)},role:"menuitem",tabIndex:0,"aria-selected":l===n.key,onKeyDown:function(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),C(n.key))}},u.createElement("div",{className:"option-content"},n.icon,u.createElement("div",null,u.createElement(k,null,n.label),u.createElement(S,{active:l===n.key},n.description))),l===n.key&&u.createElement(g.JIb,{"aria-label":"Selected"}))}}))};return t?u.createElement(y,null,u.createElement(f.ms,{menu:T,trigger:["click"],placement:"bottomRight",arrow:!0,dropdownRender:function(n){return u.createElement(E,{role:"menu","aria-label":"Theme selection menu"},n)},onOpenChange:function(n){n&&setTimeout((function(){var n=document.querySelector('[role="menuitem"]');n&&n.focus()}),100)}},u.createElement(f.m_,{title:P(),placement:"bottom"},u.createElement(w,{type:"text",size:o,className:v?"rotating":"","aria-label":"Theme options menu. Current theme: ".concat(P()),"aria-haspopup":"menu","aria-expanded":"false",role:"button"},z())))):u.createElement(y,null,u.createElement(f.m_,{title:P(),placement:"bottom"},u.createElement(w,{type:"text",size:o,className:v?"rotating":"",onClick:function(){b(!0),c(),setTimeout((function(){return b(!1)}),500)},"aria-label":"Switch to ".concat(i?"light":"dark"," mode. Current theme: ").concat(P()),"aria-pressed":i,role:"switch"},z())))}},66894:(n,e,t)=>{t.d(e,{JT:()=>z,LN:()=>w,Lv:()=>E,T6:()=>C,jn:()=>k,n5:()=>P,p1:()=>S,pK:()=>A});var r,o,a,i,l,c,s,d,p,m,u,f,g=t(57528),h=t(70572),x=t(1807),v=x.o5.Title,b=(x.o5.Text,x.o5.Paragraph),y=x.PE.Content,w=(0,h.Ay)(y)(r||(r=(0,g.A)(["\n  padding: 24px;\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n  \n  @media (max-width: 768px) {\n    padding: 16px;\n  }\n"]))),E=(h.Ay.div(o||(o=(0,g.A)(["\n  margin-bottom: 32px;\n  padding: ",";\n  background-color: ",";\n  border-radius: 8px;\n"])),(function(n){return n.padded?"24px":"0"}),(function(n){return n.background?n.background:"transparent"})),(0,h.Ay)(x.Zp)(a||(a=(0,g.A)(["\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);\n  }\n  \n  .ant-card-head {\n    border-bottom: 1px solid #f0f0f0;\n  }\n  \n  .ant-card-head-title {\n    font-weight: 600;\n  }\n"])))),A=(0,h.Ay)(E)(i||(i=(0,g.A)(["\n  height: 100%;\n  \n  .ant-card-body {\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n    height: calc(100% - 57px); // Adjust for card header\n  }\n"]))),k=(0,h.Ay)(x.$n)(l||(l=(0,g.A)(["\n  height: 40px;\n  font-weight: 500;\n  \n  &.ant-btn-primary {\n    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);\n    \n    &:hover {\n      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);\n    }\n  }\n"]))),S=((0,h.Ay)(x.$n)(c||(c=(0,g.A)(["\n  height: 40px;\n  font-weight: 500;\n"]))),(0,h.Ay)(v)(s||(s=(0,g.A)(["\n  margin-bottom: "," !important;\n  color: ",";\n"])),(function(n){return n.noMargin?"0":"24px"}),(function(n){return n.color||"inherit"}))),C=(0,h.Ay)(b)(d||(d=(0,g.A)(["\n  margin-bottom: "," !important;\n  font-size: 16px;\n  line-height: 1.6;\n  color: rgba(0, 0, 0, 0.65);\n"])),(function(n){return n.noMargin?"0":"16px"})),z=((0,h.Ay)(x.pd)(p||(p=(0,g.A)(["\n  height: 40px;\n"]))),(0,h.Ay)(x.XI)(m||(m=(0,g.A)(["\n  .ant-table {\n    border-radius: 8px;\n    overflow: hidden;\n  }\n  \n  .ant-table-thead > tr > th {\n    background-color: #fafafa;\n    font-weight: 600;\n  }\n"]))),h.Ay.div(u||(u=(0,g.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 24px;\n  margin-bottom: 32px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n"])))),P=h.Ay.div(f||(f=(0,g.A)(["\n  display: flex;\n  flex-direction: ",";\n  justify-content: ",";\n  align-items: ",";\n  flex-wrap: ",";\n  gap: ","px;\n"])),(function(n){return n.direction||"row"}),(function(n){return n.justify||"flex-start"}),(function(n){return n.align||"flex-start"}),(function(n){return n.wrap||"nowrap"}),(function(n){return n.gap||"0"}))},70405:(n,e,t)=>{t.d(e,{c:()=>u}),t(64467),t(5544);var r,o,a,i,l,c=t(57528),s=t(96540),d=t(1807),p=t(35346),m=t(70572);d.l6.Option;var u={mobile:{name:"Mobile",icon:s.createElement(p.jHj,null),variants:{"iphone-se":{name:"iPhone SE",width:375,height:667,scale:.8},"iphone-12":{name:"iPhone 12",width:390,height:844,scale:.7},"pixel-5":{name:"Pixel 5",width:393,height:851,scale:.7},"samsung-s21":{name:"Samsung S21",width:384,height:854,scale:.7}},defaultVariant:"iphone-12",frame:!0,category:"mobile"},tablet:{name:"Tablet",icon:s.createElement(p.pLH,null),variants:{ipad:{name:"iPad",width:768,height:1024,scale:.6},"ipad-pro":{name:"iPad Pro",width:1024,height:1366,scale:.5},surface:{name:"Surface Pro",width:912,height:1368,scale:.5},"galaxy-tab":{name:"Galaxy Tab",width:800,height:1280,scale:.6}},defaultVariant:"ipad",frame:!0,category:"tablet"},desktop:{name:"Desktop",icon:s.createElement(p.zlw,null),variants:{laptop:{name:"Laptop",width:1366,height:768,scale:.7},desktop:{name:"Desktop",width:1920,height:1080,scale:.5},ultrawide:{name:"Ultrawide",width:2560,height:1080,scale:.4},custom:{name:"Custom",width:1200,height:800,scale:.8}},defaultVariant:"laptop",frame:!1,category:"desktop"}};m.Ay.div(r||(r=(0,c.A)(["\n  position: relative;\n  margin: 20px auto;\n  transition: all 0.3s ease;\n  transform: ",";\n"])),(function(n){return n.orientation,"rotate(0deg)"})),m.Ay.div(o||(o=(0,c.A)(["\n  position: relative;\n  background: ",";\n  border-radius: ",";\n  padding: ",";\n  box-shadow: ",";\n  transition: all 0.3s ease;\n  \n  ","\n  \n  ","\n"])),(function(n){return"mobile"===n.category?"#333":"tablet"===n.category?"#444":"transparent"}),(function(n){return"mobile"===n.category?"25px":"tablet"===n.category?"15px":"8px"}),(function(n){return"mobile"===n.category?"20px 10px":"tablet"===n.category?"15px":"0"}),(function(n){return n.frame?"0 8px 32px rgba(0, 0, 0, 0.3)":"none"}),(function(n){return"mobile"===n.category&&"\n    &::before {\n      content: '';\n      position: absolute;\n      top: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 60px;\n      height: 4px;\n      background: #666;\n      border-radius: 2px;\n    }\n    \n    &::after {\n      content: '';\n      position: absolute;\n      bottom: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 40px;\n      height: 40px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  "}),(function(n){return"tablet"===n.category&&"\n    &::before {\n      content: '';\n      position: absolute;\n      bottom: 6px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 30px;\n      height: 30px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  "})),m.Ay.div(a||(a=(0,c.A)(["\n  width: ","px;\n  height: ","px;\n  max-width: 100%;\n  max-height: 100%;\n  background: white;\n  border-radius: ",";\n  overflow: auto;\n  position: relative;\n  transform: scale(",");\n  transform-origin: top center;\n  transition: all 0.3s ease;\n  \n  @media (max-width: 1200px) {\n    transform: scale(",");\n  }\n  \n  @media (max-width: 768px) {\n    transform: scale(",");\n  }\n"])),(function(n){return"landscape"===n.orientation?n.height:n.width}),(function(n){return"landscape"===n.orientation?n.width:n.height}),(function(n){return"mobile"===n.category?"8px":"tablet"===n.category?"6px":"4px"}),(function(n){return n.scale}),(function(n){return Math.min(n.scale,.8)}),(function(n){return Math.min(n.scale,.6)})),m.Ay.div(i||(i=(0,c.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 16px;\n  flex-wrap: wrap;\n"]))),m.Ay.div(l||(l=(0,c.A)(["\n  position: absolute;\n  top: -30px;\n  left: 0;\n  font-size: 12px;\n  color: #666;\n  background: rgba(255, 255, 255, 0.9);\n  padding: 4px 8px;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n"])))},76413:(n,e,t)=>{t.d(e,{A:()=>l});var r,o=t(57528),a=t(96540),i=t(70572).Ay.a(r||(r=(0,o.A)(["\n  position: absolute;\n  top: -40px;\n  left: 0;\n  background: ",";\n  color: white;\n  padding: 8px;\n  z-index: ",";\n  transition: top 0.3s;\n\n  &:focus {\n    top: 0;\n  }\n"])),(function(n){var e,t,r;return null!==(e=n.theme)&&void 0!==e&&null!==(e=e.colorPalette)&&void 0!==e&&e.primary?n.theme.colorPalette.primary:null!==(t=n.theme)&&void 0!==t&&null!==(t=t.colors)&&void 0!==t&&null!==(t=t.primary)&&void 0!==t&&t.main?n.theme.colors.primary.main:null!==(r=n.theme)&&void 0!==r&&r.primaryColor?n.theme.primaryColor:"#2563EB"}),(function(n){var e,t;return(null===(e=n.theme)||void 0===e||null===(e=e.zIndex)||void 0===e?void 0:e.tooltip)||(null===(t=n.theme)||void 0===t||null===(t=t.zIndex)||void 0===t?void 0:t.modal)||1e3}));const l=function(n){var e=n.targetId,t=void 0===e?"main-content":e;return a.createElement(i,{href:"#".concat(t)},"Skip to main content")}},88167:(n,e,t)=>{t.d(e,{A:()=>p});var r=t(23029),o=t(92901),a=t(56822),i=t(53954),l=t(85501),c=t(96540),s=t(1807);function d(){try{var n=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(n){}return(d=function(){return!!n})()}const p=function(n){function e(n){var t,o,l,c;return(0,r.A)(this,e),o=this,l=e,c=[n],l=(0,i.A)(l),(t=(0,a.A)(o,d()?Reflect.construct(l,c||[],(0,i.A)(o).constructor):l.apply(o,c))).state={hasError:!1,error:null},t}return(0,l.A)(e,n),(0,o.A)(e,[{key:"componentDidCatch",value:function(n,e){console.error("Component error caught by SafeComponentWrapper:",n,e)}},{key:"render",value:function(){var n,e=this;return this.state.hasError?c.createElement("div",{style:{padding:"20px"}},c.createElement(s.Fc,{message:"Component Loading Error",description:c.createElement("div",null,c.createElement("p",null,"This component failed to load properly. This might be due to missing dependencies or configuration issues."),c.createElement("p",null,c.createElement("strong",null,"Error:")," ",(null===(n=this.state.error)||void 0===n?void 0:n.message)||"Unknown error"),this.props.fallback&&c.createElement("div",{style:{marginTop:"16px"}},c.createElement("p",null,"Using fallback component:"),this.props.fallback)),type:"warning",showIcon:!0,action:c.createElement(s.$n,{size:"small",onClick:function(){return e.setState({hasError:!1,error:null})}},"Retry")})):this.props.children}}],[{key:"getDerivedStateFromError",value:function(n){return{hasError:!0,error:n}}}])}(c.Component)},99160:(n,e,t)=>{var r,o,a,i,l,c,s=t(57528),d=(t(96540),t(1807)),p=(t(35346),t(70572)),m=(t(82569),t(6827),d.PE.Content),u=d.PE.Footer;(0,p.Ay)(d.PE)(r||(r=(0,s.A)(["\n  min-height: 100vh;\n  background-color: var(--color-background);\n  transition: background-color 0.3s ease;\n"]))),(0,p.Ay)(m)(o||(o=(0,s.A)(["\n  padding: 24px;\n  background-color: var(--color-background);\n  min-height: calc(100vh - 64px - 70px);\n  transition: all 0.3s ease;\n\n  @media (max-width: 768px) {\n    padding: 16px;\n  }\n\n  @media (max-width: 480px) {\n    padding: 12px;\n  }\n"]))),(0,p.Ay)(u)(a||(a=(0,s.A)(["\n  text-align: center;\n  padding: 24px;\n  background-color: var(--color-surface);\n  border-top: 1px solid var(--color-border-light);\n  color: var(--color-text-secondary);\n  font-size: 14px;\n  transition: all 0.3s ease;\n\n  .footer-content {\n    max-width: 1200px;\n    margin: 0 auto;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    flex-wrap: wrap;\n    gap: 16px;\n\n    @media (max-width: 768px) {\n      flex-direction: column;\n      text-align: center;\n    }\n  }\n\n  .footer-links {\n    display: flex;\n    gap: 24px;\n    align-items: center;\n\n    @media (max-width: 768px) {\n      gap: 16px;\n    }\n\n    a {\n      color: var(--color-text-secondary);\n      text-decoration: none;\n      transition: color 0.3s ease;\n\n      &:hover {\n        color: var(--color-primary);\n      }\n    }\n  }\n\n  .footer-info {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: var(--color-text-tertiary);\n    font-size: 12px;\n  }\n"]))),(0,p.Ay)(d.XT)(i||(i=(0,s.A)(["\n  .ant-back-top-content {\n    background-color: var(--color-primary);\n    color: white;\n    border-radius: 50%;\n    width: 48px;\n    height: 48px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-shadow: var(--shadow-lg);\n    transition: all 0.3s ease;\n\n    &:hover {\n      background-color: var(--color-primary-hover);\n      transform: scale(1.1);\n    }\n\n    .anticon {\n      font-size: 16px;\n    }\n  }\n"]))),p.Ay.div(l||(l=(0,s.A)(["\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n"]))),p.Ay.div(c||(c=(0,s.A)(["\n  background-color: var(--color-background);\n  border-radius: var(--border-radius-lg);\n  overflow: hidden;\n  transition: all 0.3s ease;\n\n  @media (max-width: 768px) {\n    border-radius: var(--border-radius-md);\n  }\n"])))}}]);