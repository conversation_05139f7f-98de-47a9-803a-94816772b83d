"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[167],{6548:(e,t,r)=>{r.r(t),r.d(t,{default:()=>g});var n=r(467),a=r(5544),s=r(4756),l=r.n(s),i=r(6540),o=r(3016),c=r(9467),m=r(9740),u=r(677),d=r(7355),p=r(9249),y=r(5619),f=r(4976),A=o.A.Title,E=o.A.Text;const g=function(){var e=(0,i.useState)(!1),t=(0,a.A)(e,2),r=t[0],s=t[1],o=c.A.useForm(),g=(0,a.A)(o,1)[0],h=function(){var e=(0,n.A)(l().mark((function e(t){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,s(!0),e.next=4,new Promise((function(e){return setTimeout(e,1e3)}));case 4:m.Ay.success("Password reset link has been sent to your email"),g.resetFields(),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),m.Ay.error("Failed to send password reset link"),console.error("Error sending password reset link:",e.t0);case 12:return e.prev=12,s(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(t){return e.apply(this,arguments)}}();return i.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"100vh",padding:"20px"}},i.createElement(u.A,{style:{width:"100%",maxWidth:400}},i.createElement("div",{style:{textAlign:"center",marginBottom:24}},i.createElement(A,{level:3},"Forgot Password"),i.createElement(E,{type:"secondary"},"Enter your email address and we'll send you a link to reset your password")),i.createElement(c.A,{form:g,layout:"vertical",onFinish:h},i.createElement(c.A.Item,{name:"email",rules:[{required:!0,message:"Please enter your email"},{type:"email",message:"Please enter a valid email"}]},i.createElement(d.A,{prefix:i.createElement(y.A,null),placeholder:"Email",size:"large"})),i.createElement(c.A.Item,null,i.createElement(p.Ay,{type:"primary",htmlType:"submit",loading:r,block:!0,size:"large"},"Send Reset Link")),i.createElement("div",{style:{textAlign:"center"}},i.createElement(f.N_,{to:"/login"},"Back to Login")))))}}}]);