"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[7772],{87772:(e,t,r)=>{r.r(t),r.d(t,{default:()=>w});var n=r(60436),a=r(64467),c=r(10467),i=r(5544),o=r(54756),s=r.n(o),l=r(96540),u=r(1807),m=r(35346);function f(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return p(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var c,i=!0,o=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){o=!0,c=e},f:function(){try{i||null==r.return||r.return()}finally{if(o)throw c}}}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){(0,a.A)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var g=u.o5.Title,h=u.o5.Text,E=u.o5.Paragraph;const w=function(){var e=(0,l.useState)({supported:!1,registered:!1,active:!1,installing:!1,waiting:!1,error:null}),t=(0,i.A)(e,2),r=t[0],a=t[1],o=(0,l.useState)({available:!1,caches:[],totalSize:0}),p=(0,i.A)(o,2),d=p[0],w=p[1],y=(0,l.useState)({supported:!1,permission:"default"}),b=(0,i.A)(y,2),k=b[0],x=b[1],S=(0,l.useState)(navigator.onLine),A=(0,i.A)(S,2),O=A[0],C=A[1],N=(0,l.useState)([]),W=(0,i.A)(N,2),P=W[0],T=W[1];(0,l.useEffect)((function(){j(),B(),$();var e=function(){return C(!0)},t=function(){return C(!1)};return window.addEventListener("online",e),window.addEventListener("offline",t),function(){window.removeEventListener("online",e),window.removeEventListener("offline",t)}}),[]);var j=function(){var e=(0,c.A)(s().mark((function e(){var t,r;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!("serviceWorker"in navigator)){e.next=17;break}return e.prev=1,e.next=4,navigator.serviceWorker.getRegistrations();case 4:return t=e.sent,e.next=7,navigator.serviceWorker.getRegistration();case 7:r=e.sent,a({supported:!0,registered:t.length>0,active:r&&null!==r.active,installing:r&&null!==r.installing,waiting:r&&null!==r.waiting,error:null}),r&&r.addEventListener("updatefound",(function(){console.log("Service Worker update found"),j()})),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(1),a((function(t){return v(v({},t),{},{error:e.t0.message})}));case 15:e.next=18;break;case 17:a((function(e){return v(v({},e),{},{supported:!1})}));case 18:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(){return e.apply(this,arguments)}}(),B=function(){var e=(0,c.A)(s().mark((function e(){var t,r,n;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!("caches"in window)){e.next=15;break}return e.prev=1,e.next=4,caches.keys();case 4:return t=e.sent,r=0,e.next=8,Promise.all(t.map(function(){var e=(0,c.A)(s().mark((function e(t){var r,n;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,caches.open(t);case 2:return r=e.sent,e.next=5,r.keys();case 5:return n=e.sent,e.abrupt("return",{name:t,itemCount:n.length,items:n.slice(0,5).map((function(e){return e.url}))});case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()));case 8:n=e.sent,w({available:!0,caches:n,totalSize:r}),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(1),console.error("Error checking cache:",e.t0);case 15:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(){return e.apply(this,arguments)}}(),$=function(){"Notification"in window&&x({supported:!0,permission:Notification.permission})},R=function(){var e=(0,c.A)(s().mark((function e(){var t;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,navigator.serviceWorker.register("/service-worker.js");case 3:t=e.sent,console.log("Service Worker registered:",t),U("Service Worker Registration","success","Successfully registered service worker"),j(),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(0),console.error("Service Worker registration failed:",e.t0),U("Service Worker Registration","error","Registration failed: ".concat(e.t0.message));case 13:case"end":return e.stop()}}),e,null,[[0,9]])})));return function(){return e.apply(this,arguments)}}(),I=function(){var e=(0,c.A)(s().mark((function e(){var t,r,n,a;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,navigator.serviceWorker.getRegistrations();case 3:t=e.sent,r=f(t),e.prev=5,r.s();case 7:if((n=r.n()).done){e.next=13;break}return a=n.value,e.next=11,a.unregister();case 11:e.next=7;break;case 13:e.next=18;break;case 15:e.prev=15,e.t0=e.catch(5),r.e(e.t0);case 18:return e.prev=18,r.f(),e.finish(18);case 21:U("Service Worker Unregistration","success","All service workers unregistered"),j(),e.next=28;break;case 25:e.prev=25,e.t1=e.catch(0),U("Service Worker Unregistration","error","Unregistration failed: ".concat(e.t1.message));case 28:case"end":return e.stop()}}),e,null,[[0,25],[5,15,18,21]])})));return function(){return e.apply(this,arguments)}}(),Y=function(){var e=(0,c.A)(s().mark((function e(){var t;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,caches.keys();case 3:return t=e.sent,e.next=6,Promise.all(t.map((function(e){return caches.delete(e)})));case 6:U("Cache Clear","success","Cleared ".concat(t.length," caches")),B(),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),U("Cache Clear","error","Failed to clear caches: ".concat(e.t0.message));case 13:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(){return e.apply(this,arguments)}}(),D=function(){var e=(0,c.A)(s().mark((function e(){return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/",{cache:"only-if-cached",mode:"same-origin"});case 3:e.sent.ok?U("Offline Test","success","App is available offline"):U("Offline Test","warning","App may not work offline"),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),U("Offline Test","error","Offline capability test failed");case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}(),L=function(){var e=(0,c.A)(s().mark((function e(){var t;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Notification.requestPermission();case 3:t=e.sent,x((function(e){return v(v({},e),{},{permission:t})})),U("Notification Permission","success","Permission: ".concat(t)),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),U("Notification Permission","error","Failed: ".concat(e.t0.message));case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(){return e.apply(this,arguments)}}(),U=function(e,t,r){var a={id:Date.now(),test:e,status:t,message:r,timestamp:(new Date).toLocaleTimeString()};T((function(e){return[a].concat((0,n.A)(e.slice(0,9)))}))},F=function(e){switch(e){case"success":return l.createElement(m.hWy,{style:{color:"#52c41a"}});case"error":return l.createElement(m.bBN,{style:{color:"#ff4d4f"}});case"warning":return l.createElement(m.OmY,{style:{color:"#faad14"}});default:return l.createElement(m.OmY,null)}};return l.createElement("div",{style:{padding:"24px",maxWidth:"1200px",margin:"0 auto"}},l.createElement(g,{level:2},"Service Worker Testing Dashboard"),l.createElement(E,null,"Test and monitor the service worker functionality, caching, and offline capabilities."),l.createElement(u.Fc,{message:"Connection Status: ".concat(O?"Online":"Offline"),type:O?"success":"warning",icon:O?l.createElement(m._bA,null):l.createElement(m.gDm,null),style:{marginBottom:"24px"}}),l.createElement("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(400px, 1fr))",gap:"24px"}},l.createElement(u.Zp,{title:"Service Worker Status",extra:l.createElement(m.OmY,{onClick:j})},l.createElement(u.$x,{direction:"vertical",style:{width:"100%"}},l.createElement("div",null,l.createElement(h,{strong:!0},"Supported: "),r.supported?l.createElement(u.vw,{color:"green"},l.createElement(m.hWy,null)," Yes"):l.createElement(u.vw,{color:"red"},l.createElement(m.bBN,null)," No")),l.createElement("div",null,l.createElement(h,{strong:!0},"Registered: "),r.registered?l.createElement(u.vw,{color:"green"},l.createElement(m.hWy,null)," Yes"):l.createElement(u.vw,{color:"orange"},l.createElement(m.bBN,null)," No")),l.createElement("div",null,l.createElement(h,{strong:!0},"Active: "),r.active?l.createElement(u.vw,{color:"green"},l.createElement(m.hWy,null)," Yes"):l.createElement(u.vw,{color:"orange"},l.createElement(m.bBN,null)," No")),r.error&&l.createElement(u.Fc,{message:r.error,type:"error",size:"small"}),l.createElement(u.cG,null),l.createElement(u.$x,null,l.createElement(u.$n,{type:"primary",onClick:R,disabled:!r.supported||r.registered},"Register SW"),l.createElement(u.$n,{onClick:I,disabled:!r.supported||!r.registered},"Unregister SW")))),l.createElement(u.Zp,{title:"Cache Status",extra:l.createElement(m.KF4,{onClick:B})},l.createElement(u.$x,{direction:"vertical",style:{width:"100%"}},l.createElement("div",null,l.createElement(h,{strong:!0},"Cache API Available: "),d.available?l.createElement(u.vw,{color:"green"},l.createElement(m.hWy,null)," Yes"):l.createElement(u.vw,{color:"red"},l.createElement(m.bBN,null)," No")),l.createElement("div",null,l.createElement(h,{strong:!0},"Active Caches: "),l.createElement(u.vw,{color:"blue"},d.caches.length)),d.caches.length>0&&l.createElement(u.B8,{size:"small",dataSource:d.caches,renderItem:function(e){return l.createElement(u.B8.Item,null,l.createElement("div",{style:{width:"100%"}},l.createElement(h,{strong:!0},e.name),l.createElement("br",null),l.createElement(h,{type:"secondary"},e.itemCount," items")))}}),l.createElement(u.cG,null),l.createElement(u.$x,null,l.createElement(u.$n,{icon:l.createElement(m.SUY,null),onClick:Y,disabled:0===d.caches.length},"Clear All Caches"),l.createElement(u.$n,{onClick:D},"Test Offline")))),l.createElement(u.Zp,{title:"Notification Status"},l.createElement(u.$x,{direction:"vertical",style:{width:"100%"}},l.createElement("div",null,l.createElement(h,{strong:!0},"Supported: "),k.supported?l.createElement(u.vw,{color:"green"},l.createElement(m.hWy,null)," Yes"):l.createElement(u.vw,{color:"red"},l.createElement(m.bBN,null)," No")),l.createElement("div",null,l.createElement(h,{strong:!0},"Permission: "),l.createElement(u.vw,{color:"granted"===k.permission?"green":"orange"},k.permission)),l.createElement(u.cG,null),l.createElement(u.$x,null,l.createElement(u.$n,{icon:l.createElement(m.Kbk,null),onClick:L,disabled:!k.supported||"granted"===k.permission},"Request Permission"),l.createElement(u.$n,{onClick:function(){"granted"===Notification.permission?(new Notification("Test Notification",{body:"This is a test notification from the App Builder",icon:"/logo192.png",badge:"/favicon.ico"}),U("Push Notification","success","Test notification sent")):U("Push Notification","error","Notification permission not granted")},disabled:"granted"!==k.permission},"Test Notification")))),l.createElement(u.Zp,{title:"Test Results",style:{gridColumn:"span 2"}},0===P.length?l.createElement(h,{type:"secondary"},"No tests run yet. Click the buttons above to start testing."):l.createElement(u.B8,{dataSource:P,renderItem:function(e){return l.createElement(u.B8.Item,null,l.createElement(u.B8.Item.Meta,{avatar:F(e.status),title:e.test,description:"".concat(e.message," - ").concat(e.timestamp)}))}}))))}}}]);