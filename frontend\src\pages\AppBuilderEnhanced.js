import React, { useState, useEffect, useCallback, useMemo, lazy, Suspense } from 'react';
import { Card, Typography, Button, message, Tabs, Row, Col, Tooltip, Spin, Modal, FloatButton, Badge, Switch } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { setCurrentView } from '../redux/reducers/uiReducer';
import styled from 'styled-components';
import {
  AppstoreOutlined,
  LayoutOutlined,
  BgColorsOutlined,
  ApiOutlined,
  ProjectOutlined,
  CodeOutlined,
  DashboardOutlined,
  InfoCircleOutlined,
  QuestionCircleOutlined,
  EyeOutlined,
  SyncOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { useEnhancedTheme } from '../contexts/EnhancedThemeContext';
import EnhancedLayout from '../components/layout/EnhancedLayout';

// Import new preview functionality
import useRealTimePreview from '../hooks/useRealTimePreview';
import useResponsivePreview from '../hooks/useResponsivePreview';
import useCollaborativePreview from '../hooks/useCollaborativePreview';
import { PerformanceMonitor as FloatingPerformanceMonitor } from '../components/performance';

// Import safe wrapper
import SafeComponentWrapper from '../components/enhanced/SafeComponentWrapper';

// Import the actual components using lazy loading for better performance with error handling
const ComponentBuilder = lazy(() =>
  import('../components/enhanced/ComponentBuilder').catch(err => {
    console.warn('Failed to load ComponentBuilder:', err);
    return { default: () => <div>Component Builder not available</div> };
  })
);
const LayoutDesigner = lazy(() =>
  import('../components/enhanced/LayoutDesigner').catch(err => {
    console.warn('Failed to load LayoutDesigner:', err);
    return { default: () => <div>Layout Designer not available</div> };
  })
);
const ThemeManager = lazy(() =>
  import('../components/enhanced/ThemeManager').catch(err => {
    console.warn('Failed to load ThemeManager:', err);
    return { default: () => <div>Theme Manager not available</div> };
  })
);
const FixedWebSocketManager = lazy(() =>
  import('../components/enhanced/FixedWebSocketManager').catch(err => {
    console.warn('Failed to load FixedWebSocketManager:', err);
    return { default: () => <div>WebSocket Manager not available</div> };
  })
);
const ProjectManager = lazy(() =>
  import('../components/enhanced/ProjectManager').catch(err => {
    console.warn('Failed to load ProjectManager:', err);
    return { default: () => <div>Project Manager not available</div> };
  })
);
const CodeExporter = lazy(() =>
  import('../components/enhanced/CodeExporter').catch(err => {
    console.warn('Failed to load CodeExporter:', err);
    return { default: () => <div>Code Exporter not available</div> };
  })
);
const EnhancedCodeExporter = lazy(() =>
  import('../components/enhanced/EnhancedCodeExporter').catch(err => {
    console.warn('Failed to load EnhancedCodeExporter:', err);
    return { default: () => <div>Enhanced Code Exporter not available</div> };
  })
);
const PerformanceMonitor = lazy(() =>
  import('../components/enhanced/PerformanceMonitor').catch(err => {
    console.warn('Failed to load PerformanceMonitor:', err);
    return { default: () => <div>Performance Monitor not available</div> };
  })
);
const DataManagementDemo = lazy(() =>
  import('../components/enhanced/DataManagementDemo').catch(err => {
    console.warn('Failed to load DataManagementDemo:', err);
    return { default: () => <div>Data Management not available</div> };
  })
);
const TestingTools = lazy(() =>
  import('../components/enhanced/TestingTools').catch(err => {
    console.warn('Failed to load TestingTools:', err);
    return { default: () => <div>Testing Tools not available</div> };
  })
);
const TutorialAIPlugin = lazy(() =>
  import('../plugins/TutorialAIPlugin').catch(err => {
    console.warn('Failed to load TutorialAIPlugin:', err);
    return { default: () => <div>Tutorial Assistant not available</div> };
  })
);

const { Title, Paragraph } = Typography;

const AppBuilderContainer = styled.div`
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
  background-color: var(--color-background);
  min-height: 100vh;
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    padding: var(--spacing-md);
  }

  @media (max-width: 480px) {
    padding: var(--spacing-sm);
  }
`;

const AppHeader = styled.div`
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--shadow-md);
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);

    h2 {
      margin-bottom: var(--spacing-sm);
    }
  }
`;

const StyledTabs = styled(Tabs)`
  .ant-tabs-nav {
    margin-bottom: var(--spacing-xl);
    background-color: var(--color-surface);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-sm);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-border-light);
  }

  .ant-tabs-tab {
    padding: var(--spacing-md) var(--spacing-lg);
    transition: all 0.3s ease;
    border-radius: var(--border-radius-md);
    margin: 0 var(--spacing-xs);
    color: var(--color-text-secondary);
    font-weight: 500;

    &:hover {
      color: var(--color-primary);
      background-color: var(--color-background-secondary);
      transform: translateY(-2px);
    }

    .anticon {
      margin-right: var(--spacing-sm);
      font-size: 16px;
    }
  }

  .ant-tabs-tab-active {
    background-color: var(--color-primary);
    color: white;
    box-shadow: var(--shadow-md);

    &:hover {
      background-color: var(--color-primary-hover);
      color: white;
    }
  }

  .ant-tabs-content-holder {
    background-color: var(--color-surface);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-border-light);
    overflow: hidden;
  }

  .ant-tabs-tabpane {
    padding: var(--spacing-lg);
  }
`;

const ComponentCard = styled(Card)`
  height: 100%;
  transition: all 0.3s ease;
  cursor: pointer;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border-light);
  border-radius: var(--border-radius-lg);
  overflow: hidden;

  &:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
    border-color: var(--color-primary);
  }

  &:active {
    transform: translateY(-4px);
  }

  .ant-card-head {
    background-color: ${props => props.active ? 'var(--color-primary)' : 'var(--color-background-secondary)'};
    color: ${props => props.active ? 'white' : 'var(--color-text)'};
    border-bottom: 1px solid var(--color-border-light);
    transition: all 0.3s ease;
  }

  .ant-card-body {
    padding: var(--spacing-lg);
    text-align: center;
    background-color: var(--color-surface);
  }
`;

const ComponentIcon = styled.div`
  font-size: 32px;
  margin-bottom: var(--spacing-md);
  color: ${props => props.active ? 'var(--color-primary)' : 'var(--color-text-secondary)'};
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: ${props => props.active ? 'rgba(24, 144, 255, 0.1)' : 'var(--color-background-secondary)'};
  margin: 0 auto var(--spacing-md);
`;

const WelcomeCard = styled(Card)`
  margin-bottom: var(--spacing-xl);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  border: none;
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  position: relative;

  /* Add overlay for better text contrast */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
  }

  .ant-card-body {
    padding: var(--spacing-xxl);
    color: white;
    position: relative;
    z-index: 2;
  }

  h4, p {
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
  }

  .ant-btn-primary {
    background-color: white;
    border-color: white;
    color: var(--color-primary);
    font-weight: 600;
    position: relative;
    z-index: 2;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    &:hover {
      background-color: rgba(255, 255, 255, 0.95);
      border-color: rgba(255, 255, 255, 0.95);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    }
  }
`;

// Define the spin animation
const SpinAnimation = styled.div`
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

/**
 * Enhanced App Builder with improved accessibility, performance, and code organization
 */
const AppBuilderEnhanced = () => {
  const dispatch = useDispatch();
  const { isDarkMode, colors } = useEnhancedTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState({
    api: 'checking',
    websocket: 'checking'
  });
  // Initialize with the current view from Redux or default to 'components'
  const currentViewFromStore = useSelector(state => state.ui?.currentView || 'components');
  const [activeComponent, setActiveComponent] = useState(currentViewFromStore);
  const [showTutorialModal, setShowTutorialModal] = useState(false);

  // Enhanced preview state
  const [previewSettings, setPreviewSettings] = useState({
    realTimeEnabled: true,
    collaborationEnabled: false,
    performanceMonitoring: true,
    deviceSync: true
  });
  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`);
  const [userId] = useState(() => `user_${Math.random().toString(36).substring(2, 11)}`);

  // WebSocket and collaboration state
  const websocketConnected = useSelector(state => state.websocket?.connected || false);
  const components = useSelector(state => state.app?.components || []);

  // Initialize enhanced preview hooks
  const responsivePreview = useResponsivePreview({
    initialDevice: 'desktop',
    enableBreakpointDetection: true
  });

  const collaborativePreview = useCollaborativePreview({
    sessionId,
    userId,
    username: 'App Builder User',
    enableCollaboration: previewSettings.collaborationEnabled && websocketConnected,
    enableCursorTracking: true,
    enableDeviceSync: previewSettings.deviceSync
  });

  const realTimePreview = useRealTimePreview({
    components,
    websocketService: collaborativePreview.wsService,
    enableWebSocket: previewSettings.realTimeEnabled && websocketConnected,
    updateDelay: 300
  });

  // Handle preview settings changes
  const handlePreviewSettingChange = useCallback((setting, value) => {
    setPreviewSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  }, []);

  useEffect(() => {
    // Initialize the app
    const initApp = async () => {
      try {
        // Set initial view in Redux
        dispatch(setCurrentView(activeComponent));
        console.log('App Builder initialized successfully');

        // Check if we're in development mode
        const isDev = process.env.NODE_ENV === 'development';

        // In development mode, we can continue even if connections fail
        if (isDev) {
          console.log('Development mode: App will continue to load even if connections fail');
        }

        // Quick API connection check
        try {
          console.log('Checking API connection...');
          const apiResponse = await Promise.race([
            fetch('/api/status/'),
            new Promise((_, reject) => setTimeout(() => reject(new Error('API timeout')), 1000))
          ]);

          if (apiResponse.ok) {
            setConnectionStatus(prev => ({ ...prev, api: 'connected' }));
            console.log('API connection successful');
          } else {
            setConnectionStatus(prev => ({ ...prev, api: isDev ? 'warning' : 'error' }));
            console.warn('API connection failed');
          }
        } catch (apiError) {
          setConnectionStatus(prev => ({ ...prev, api: isDev ? 'warning' : 'error' }));
          console.warn('API connection error:', apiError.message);
          if (isDev) {
            console.log('Development mode: Continuing with mock API');
          }
        }

        // Quick WebSocket connection check
        try {
          console.log('Checking WebSocket connection...');
          // Skip WebSocket check in development mode to speed up loading
          if (isDev) {
            setConnectionStatus(prev => ({ ...prev, websocket: 'warning' }));
            console.log('Development mode: Skipping WebSocket check');
          } else {
            // Quick WebSocket test
            const ws = new WebSocket('ws://localhost:8000/ws/app_builder/');
            const timeoutId = setTimeout(() => {
              ws.close();
              setConnectionStatus(prev => ({ ...prev, websocket: 'error' }));
            }, 1000);

            ws.onopen = () => {
              clearTimeout(timeoutId);
              setConnectionStatus(prev => ({ ...prev, websocket: 'connected' }));
              console.log('WebSocket connection successful');
              ws.close();
            };

            ws.onerror = () => {
              clearTimeout(timeoutId);
              setConnectionStatus(prev => ({ ...prev, websocket: 'error' }));
              console.warn('WebSocket connection failed');
              ws.close();
            };
          }
        } catch (wsError) {
          setConnectionStatus(prev => ({ ...prev, websocket: isDev ? 'warning' : 'error' }));
          console.warn('WebSocket connection error:', wsError.message);
        }
      } catch (error) {
        console.error('Failed to initialize App Builder:', error);
        message.error('Failed to initialize App Builder. Please try refreshing the page.');
        setError('Failed to initialize App Builder. Please try refreshing the page.');
      } finally {
        // Always set loading to false after initialization
        setTimeout(() => {
          setLoading(false);
        }, 500);
      }
    };

    // Set a timeout to ensure loading state is not stuck
    const timer = setTimeout(() => {
      setLoading(false);
      console.log('Loading timeout triggered - forcing app to load');
    }, 3000);

    initApp();

    // Clean up the timer
    return () => clearTimeout(timer);
  }, [dispatch, activeComponent]);

  // Handle component selection - memoized for better performance
  const handleComponentSelect = useCallback((component) => {
    setActiveComponent(component);
    dispatch(setCurrentView(component));
  }, [dispatch]);

  // Define tab items - memoized for better performance
  // This must be defined before any conditional returns to follow Rules of Hooks
  const tabItems = useMemo(() => [
    {
      key: 'projects',
      label: (
        <span>
          <ProjectOutlined /> Projects
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Projects...</div></div>}>
          <ProjectManager />
        </Suspense>
      )
    },
    {
      key: 'components',
      label: (
        <span>
          <AppstoreOutlined /> Component Builder
        </span>
      ),
      children: (
        <SafeComponentWrapper fallback={<div>Using basic component builder...</div>}>
          <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Component Builder...</div></div>}>
            <ComponentBuilder />
          </Suspense>
        </SafeComponentWrapper>
      )
    },
    {
      key: 'layouts',
      label: (
        <span>
          <LayoutOutlined /> Layout Designer
        </span>
      ),
      children: (
        <SafeComponentWrapper fallback={<div>Using basic layout designer...</div>}>
          <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Layout Designer...</div></div>}>
            <LayoutDesigner />
          </Suspense>
        </SafeComponentWrapper>
      )
    },
    {
      key: 'themes',
      label: (
        <span>
          <BgColorsOutlined /> Theme Manager
        </span>
      ),
      children: (
        <SafeComponentWrapper fallback={<div>Using basic theme manager...</div>}>
          <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Theme Manager...</div></div>}>
            <ThemeManager />
          </Suspense>
        </SafeComponentWrapper>
      )
    },
    {
      key: 'export',
      label: (
        <span>
          <CodeOutlined /> Enhanced Export
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Enhanced Code Exporter...</div></div>}>
          <EnhancedCodeExporter />
        </Suspense>
      )
    },
    {
      key: 'performance',
      label: (
        <span>
          <DashboardOutlined /> Performance
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Performance Monitor...</div></div>}>
          <PerformanceMonitor />
        </Suspense>
      )
    },
    {
      key: 'websocket',
      label: (
        <span>
          <ApiOutlined /> WebSocket Manager
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading WebSocket Manager...</div></div>}>
          <FixedWebSocketManager />
        </Suspense>
      )
    },
    {
      key: 'data',
      label: (
        <span>
          <InfoCircleOutlined /> Data Management
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Data Management...</div></div>}>
          <DataManagementDemo />
        </Suspense>
      )
    },
    {
      key: 'testing',
      label: (
        <span>
          <InfoCircleOutlined /> Testing Tools
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Testing Tools...</div></div>}>
          <TestingTools />
        </Suspense>
      )
    },
    {
      key: 'tutorial',
      label: (
        <span>
          <QuestionCircleOutlined /> Tutorial Assistant
        </span>
      ),
      children: (
        <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Tutorial Assistant...</div></div>}>
          <div style={{ padding: '20px' }}>
            <Title level={3}>Tutorial Assistant</Title>
            <Paragraph>
              Get help and learn how to use App Builder with our AI-powered tutorial assistant.
            </Paragraph>
            <TutorialAIPlugin />
          </div>
        </Suspense>
      )
    }
  ], []);

  if (loading) {
    return (
      <SpinAnimation>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh', flexDirection: 'column' }}>
          <div style={{ width: '50px', height: '50px', border: '5px solid #f3f3f3', borderTop: '5px solid #3498db', borderRadius: '50%', animation: 'spin 1s linear infinite' }}></div>
          <Title level={3} style={{ marginTop: '20px' }}>Loading App Builder...</Title>
          <div style={{ marginTop: '20px', textAlign: 'center' }}>
            <div style={{
              color: 'var(--color-text)',
              backgroundColor: 'var(--color-background-secondary)',
              padding: '8px 16px',
              borderRadius: '8px',
              marginBottom: '8px',
              border: '1px solid var(--color-border-light)'
            }}>
              API Connection: {' '}
              <span style={{
                color: connectionStatus.api === 'connected' ? '#52c41a' :
                  connectionStatus.api === 'error' ? '#ff4d4f' :
                    connectionStatus.api === 'warning' ? '#faad14' : '#1890ff',
                fontWeight: '600'
              }}>
                {connectionStatus.api === 'connected' ? 'Connected' :
                  connectionStatus.api === 'error' ? 'Failed' :
                    connectionStatus.api === 'warning' ? 'Limited (Mock)' : 'Checking...'}
              </span>
            </div>
            <div style={{
              color: 'var(--color-text)',
              backgroundColor: 'var(--color-background-secondary)',
              padding: '8px 16px',
              borderRadius: '8px',
              border: '1px solid var(--color-border-light)'
            }}>
              WebSocket Connection: {' '}
              <span style={{
                color: connectionStatus.websocket === 'connected' ? '#52c41a' :
                  connectionStatus.websocket === 'error' ? '#ff4d4f' :
                    connectionStatus.websocket === 'warning' ? '#faad14' : '#1890ff',
                fontWeight: '600'
              }}>
                {connectionStatus.websocket === 'connected' ? 'Connected' :
                  connectionStatus.websocket === 'error' ? 'Failed' :
                    connectionStatus.websocket === 'warning' ? 'Limited (Mock)' : 'Checking...'}
              </span>
            </div>
            {(connectionStatus.api === 'error' || connectionStatus.websocket === 'error') && (
              <div style={{
                marginTop: '20px',
                color: '#ff4d4f',
                backgroundColor: 'var(--color-background-secondary)',
                padding: '12px 16px',
                borderRadius: '8px',
                border: '1px solid #ff4d4f',
                fontWeight: '500'
              }}>
                <p style={{ margin: '0 0 8px 0' }}>Some connections failed. The app will continue to load with limited functionality.</p>
                <p style={{ margin: 0 }}>Please ensure the backend server is running at http://localhost:8000</p>
              </div>
            )}
            {(connectionStatus.api === 'warning' || connectionStatus.websocket === 'warning') && (
              <div style={{
                marginTop: '20px',
                color: '#faad14',
                backgroundColor: 'var(--color-background-secondary)',
                padding: '12px 16px',
                borderRadius: '8px',
                border: '1px solid #faad14',
                fontWeight: '500'
              }}>
                <p style={{ margin: '0 0 8px 0' }}>Some connections are in limited mode. The app will use mock data.</p>
                <p style={{ margin: 0 }}>This is normal in development mode when the backend is not running.</p>
              </div>
            )}
          </div>
        </div>
      </SpinAnimation>
    );
  }

  // Show error state if there's an error
  if (error) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh', flexDirection: 'column' }}>
        <div style={{ color: 'red', fontSize: '48px', marginBottom: '20px' }}>
          <InfoCircleOutlined />
        </div>
        <Title level={3} style={{ color: 'red' }}>Error</Title>
        <Paragraph style={{ textAlign: 'center', maxWidth: '600px', marginTop: '20px' }}>
          {error}
        </Paragraph>
        <Button
          type="primary"
          style={{ marginTop: '20px' }}
          onClick={() => window.location.reload()}
        >
          Refresh Page
        </Button>
      </div>
    );
  }

  return (
    <AppBuilderContainer className="app-builder-enhanced">
      <AppHeader>
        <div>
          <Title level={2}>App Builder Enhanced</Title>
          <Paragraph>
            Create and manage your application components with ease
          </Paragraph>
          <div style={{ display: 'flex', gap: '10px', marginTop: '5px' }}>
            <Tooltip title={
              connectionStatus.api === 'connected' ? 'API Connected' :
                connectionStatus.api === 'warning' ? 'API in Limited Mode (Mock)' :
                  'API Connection Failed'
            }>
              <div style={{
                display: 'inline-flex',
                alignItems: 'center',
                fontSize: '12px',
                color: connectionStatus.api === 'connected' ? '#52c41a' :
                  connectionStatus.api === 'warning' ? '#faad14' : '#ff4d4f',
                backgroundColor: 'var(--color-background-secondary)',
                padding: '4px 8px',
                borderRadius: '12px',
                border: '1px solid var(--color-border-light)',
                fontWeight: '500'
              }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  backgroundColor: connectionStatus.api === 'connected' ? '#52c41a' :
                    connectionStatus.api === 'warning' ? '#faad14' : '#ff4d4f',
                  marginRight: '6px',
                  boxShadow: `0 0 4px ${connectionStatus.api === 'connected' ? '#52c41a' :
                    connectionStatus.api === 'warning' ? '#faad14' : '#ff4d4f'}`
                }}></div>
                API
              </div>
            </Tooltip>
            <Tooltip title={
              connectionStatus.websocket === 'connected' ? 'WebSocket Connected' :
                connectionStatus.websocket === 'warning' ? 'WebSocket in Limited Mode (Mock)' :
                  'WebSocket Connection Failed'
            }>
              <div style={{
                display: 'inline-flex',
                alignItems: 'center',
                fontSize: '12px',
                color: connectionStatus.websocket === 'connected' ? '#52c41a' :
                  connectionStatus.websocket === 'warning' ? '#faad14' : '#ff4d4f',
                backgroundColor: 'var(--color-background-secondary)',
                padding: '4px 8px',
                borderRadius: '12px',
                border: '1px solid var(--color-border-light)',
                fontWeight: '500'
              }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  backgroundColor: connectionStatus.websocket === 'connected' ? '#52c41a' :
                    connectionStatus.websocket === 'warning' ? '#faad14' : '#ff4d4f',
                  marginRight: '6px',
                  boxShadow: `0 0 4px ${connectionStatus.websocket === 'connected' ? '#52c41a' :
                    connectionStatus.websocket === 'warning' ? '#faad14' : '#ff4d4f'}`
                }}></div>
                WebSocket
              </div>
            </Tooltip>
          </div>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          {/* Enhanced Preview Controls */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 12px',
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}>
            <Tooltip title="Real-time Preview">
              <Switch
                size="small"
                checked={previewSettings.realTimeEnabled}
                onChange={(checked) => handlePreviewSettingChange('realTimeEnabled', checked)}
                checkedChildren={<EyeOutlined />}
                unCheckedChildren={<EyeOutlined />}
              />
            </Tooltip>

            <Tooltip title="Collaboration">
              <Switch
                size="small"
                checked={previewSettings.collaborationEnabled && websocketConnected}
                onChange={(checked) => handlePreviewSettingChange('collaborationEnabled', checked)}
                disabled={!websocketConnected}
                checkedChildren={<SyncOutlined />}
                unCheckedChildren={<SyncOutlined />}
              />
            </Tooltip>

            <Tooltip title="Performance Monitoring">
              <Switch
                size="small"
                checked={previewSettings.performanceMonitoring}
                onChange={(checked) => handlePreviewSettingChange('performanceMonitoring', checked)}
                checkedChildren={<ThunderboltOutlined />}
                unCheckedChildren={<ThunderboltOutlined />}
              />
            </Tooltip>

            {/* Collaboration Status */}
            {previewSettings.collaborationEnabled && collaborativePreview.isConnected && (
              <Badge
                count={collaborativePreview.collaborators.length}
                showZero={false}
                style={{ backgroundColor: '#52c41a' }}
              >
                <Tooltip title="Active collaborators">
                  <div style={{
                    width: '20px',
                    height: '20px',
                    borderRadius: '50%',
                    background: '#52c41a',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <SyncOutlined style={{ fontSize: '10px', color: 'white' }} />
                  </div>
                </Tooltip>
              </Badge>
            )}
          </div>

          <Tooltip title="Open Tutorial Assistant">
            <Button
              type="default"
              icon={<QuestionCircleOutlined />}
              style={{ marginRight: '10px' }}
              onClick={() => setShowTutorialModal(true)}
            >
              Help
            </Button>
          </Tooltip>
          <Tooltip title="Refresh connections">
            <Button
              type="default"
              onClick={() => window.location.reload()}
            >
              Refresh
            </Button>
          </Tooltip>
        </div>
      </AppHeader>

      <WelcomeCard>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={16}>
            <Title level={4}>Welcome to App Builder Enhanced</Title>
            <Paragraph>
              This tool helps you create and manage your application components with ease.
              Use the tabs below to navigate between different features.
            </Paragraph>
            <Button
              type="primary"
              size="large"
              onClick={() => handleComponentSelect('components')}
            >
              Start Building
            </Button>
          </Col>
          <Col xs={24} md={8}>
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <img
                src="/static/images/app-builder-logo.svg"
                alt="App Builder Logo"
                style={{ maxWidth: '100%', height: 'auto' }}
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.style.display = 'none';
                }}
              />
            </div>
          </Col>
        </Row>
      </WelcomeCard>

      <StyledTabs
        activeKey={activeComponent}
        onChange={handleComponentSelect}
        type="card"
        size="large"
        items={tabItems}
      />

      <Card title="Getting Started" style={{ marginTop: '24px' }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={6}>
            <ComponentCard
              title="Step 1"
              active={activeComponent === 'components'}
              onClick={() => handleComponentSelect('components')}
            >
              <ComponentIcon active={activeComponent === 'components'}>
                <AppstoreOutlined />
              </ComponentIcon>
              <Paragraph>
                Use the Component Builder to create UI components
              </Paragraph>
            </ComponentCard>
          </Col>
          <Col xs={24} md={6}>
            <ComponentCard
              title="Step 2"
              active={activeComponent === 'layouts'}
              onClick={() => handleComponentSelect('layouts')}
            >
              <ComponentIcon active={activeComponent === 'layouts'}>
                <LayoutOutlined />
              </ComponentIcon>
              <Paragraph>
                Design your layout with the Layout Designer
              </Paragraph>
            </ComponentCard>
          </Col>
          <Col xs={24} md={6}>
            <ComponentCard
              title="Step 3"
              active={activeComponent === 'themes'}
              onClick={() => handleComponentSelect('themes')}
            >
              <ComponentIcon active={activeComponent === 'themes'}>
                <BgColorsOutlined />
              </ComponentIcon>
              <Paragraph>
                Customize your theme with the Theme Manager
              </Paragraph>
            </ComponentCard>
          </Col>
          <Col xs={24} md={6}>
            <ComponentCard
              title="Step 4"
              active={activeComponent === 'websocket'}
              onClick={() => handleComponentSelect('websocket')}
            >
              <ComponentIcon active={activeComponent === 'websocket'}>
                <ApiOutlined />
              </ComponentIcon>
              <Paragraph>
                Set up real-time communication with WebSocket Manager
              </Paragraph>
            </ComponentCard>
          </Col>
        </Row>
      </Card>

      {/* Enhanced Preview Performance Monitor */}
      {previewSettings.performanceMonitoring && (
        <FloatingPerformanceMonitor
          renderTime={realTimePreview.isUpdating ? 16 : 8}
          frameRate={60}
          memoryUsage={performance.memory ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) : 0}
          componentCount={components.length}
          visibleComponents={components.length}
          cacheSize={0}
          updateFrequency={realTimePreview.hasPendingUpdates ? 30 : 0}
          floating={true}
          showAlerts={true}
          optimizationsEnabled={previewSettings.realTimeEnabled}
          onToggleOptimizations={(enabled) => handlePreviewSettingChange('realTimeEnabled', enabled)}
        />
      )}

      {/* Floating Help Button */}
      <FloatButton
        icon={<QuestionCircleOutlined />}
        type="primary"
        style={{
          right: previewSettings.performanceMonitoring ? 340 : 24,
          bottom: 24,
        }}
        onClick={() => setShowTutorialModal(true)}
        tooltip="Tutorial Assistant"
      />

      {/* Tutorial Modal */}
      <Modal
        title="Tutorial Assistant"
        open={showTutorialModal}
        onCancel={() => setShowTutorialModal(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
      >
        <div style={{ padding: '10px 0' }}>
          <Paragraph>
            Get help and learn how to use App Builder with our AI-powered tutorial assistant.
          </Paragraph>
          <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}><Spin size="large" /><div style={{ marginTop: '10px' }}>Loading Tutorial Assistant...</div></div>}>
            <TutorialAIPlugin />
          </Suspense>
        </div>
      </Modal>
    </AppBuilderContainer>
  );
};

export default AppBuilderEnhanced;
