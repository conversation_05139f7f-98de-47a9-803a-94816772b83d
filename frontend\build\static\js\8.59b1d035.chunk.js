"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[8],{5008:(e,n,t)=>{t.r(n),t.d(n,{default:()=>B});var o,c,a,l,r=t(5544),u=t(7528),i=t(6540),s=t(1468),d=t(3016),m=t(2395),p=t(4358),y=t(9740),f=t(6552),g=t(770),v=t(2702),h=t(1196),A=t(7197),E=t(9249),x=t(9248),C=t(7046),b=t(756),S=t(6191),k=t(6020),L=d.A.Title,w=d.A.Text,T=d.A.Paragraph,I=(m.A.TabPane,p.A.Option),N=S.styled.div(o||(o=(0,u.A)(["\n  padding: ",";\n"])),k.Ay.spacing[3]),R=S.styled.pre(c||(c=(0,u.A)(["\n  background-color: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  padding: ",";\n  overflow: auto;\n  max-height: 500px;\n  font-family: ",";\n  font-size: ",";\n  line-height: 1.5;\n"])),k.Ay.colors.neutral[100],k.Ay.colors.neutral[300],k.Ay.borderRadius.md,k.Ay.spacing[3],k.Ay.typography.fontFamily.code,k.Ay.typography.fontSize.sm),Y=S.styled.div(a||(a=(0,u.A)(["\n  margin-bottom: ",";\n"])),k.Ay.spacing[4]),j=S.styled.div(l||(l=(0,u.A)(["\n  margin-bottom: ",";\n"])),k.Ay.spacing[3]);const B=function(){var e=(0,s.d4)((function(e){var n;return(null===(n=e.app)||void 0===n?void 0:n.components)||[]})),n=(0,s.d4)((function(e){var n;return(null===(n=e.app)||void 0===n?void 0:n.layouts)||[]})),t=(0,s.d4)((function(e){var n;return(null===(n=e.themes)||void 0===n?void 0:n.activeTheme)||"default"})),o=(0,s.d4)((function(e){var n;return(null===(n=e.themes)||void 0===n?void 0:n.themes)||[]})),c=(0,i.useState)("react"),a=(0,r.A)(c,2),l=a[0],u=a[1],d=(0,i.useState)(!0),m=(0,r.A)(d,2),S=m[0],B=m[1],O=(0,i.useState)(!0),F=(0,r.A)(O,2),U=F[0],H=F[1],M=(0,i.useState)("components"),P=(0,r.A)(M,2),V=P[0],z=P[1],D=(0,i.useState)([]),G=(0,r.A)(D,2),J=G[0],q=G[1],K=(0,i.useState)([]),Q=(0,r.A)(K,2),W=Q[0],X=Q[1],Z=(0,i.useState)(""),$=(0,r.A)(Z,2),_=$[0],ee=$[1],ne=(0,i.useState)(!1),te=(0,r.A)(ne,2),oe=te[0],ce=te[1];(0,i.useEffect)((function(){e.length>0&&0===J.length&&q(e.map((function(e){return e.id}))),n.length>0&&0===W.length&&X(n.map((function(e){return e.id})))}),[e,n,J.length,W.length]),(0,i.useEffect)((function(){ae()}),[l,S,U,V,J,W]);var ae=function(){var c=e.filter((function(e){return J.includes(e.id)})),a=n.filter((function(e){return W.includes(e.id)})),r=o.find((function(e){return e.id===t}))||o[0],u="";switch(l){case"react":default:u=le(c,a,r);break;case"vue":u=re(c,a,r);break;case"angular":u=ue(c,a,r);break;case"html":u=ie(c,a,r)}ee(u)},le=function(e,n,t){var o="";return o+="import React from 'react';\n",S&&(o+="import styled from 'styled-components';\n"),o+="\n",U&&t&&(o+="// Theme definition\n",o+="const theme = ".concat(JSON.stringify(t,null,2),";\n\n")),"components"!==V&&"all"!==V||e.forEach((function(e){o+="// ".concat(e.name," component\n"),S&&e.props.customStyles&&(o+="const Styled".concat(e.name.replace(/\s/g,"")," = styled.div`\n"),o+="  ".concat(e.props.customStyles,"\n"),o+="`;\n\n"),o+="const ".concat(e.name.replace(/\s/g,"")," = (props) => {\n"),o+="  return (\n",S&&e.props.customStyles?(o+="    <Styled".concat(e.name.replace(/\s/g,""),">\n"),o+="      <div>".concat(e.name,"</div>\n"),o+="    </Styled".concat(e.name.replace(/\s/g,""),">\n")):(o+='    <div className="'.concat(e.name.toLowerCase().replace(/\s/g,"-"),'">\n'),o+="      <div>".concat(e.name,"</div>\n"),o+="    </div>\n"),o+="  );\n",o+="};\n\n"})),"layouts"!==V&&"all"!==V||n.forEach((function(n){var t,c,a,l;o+="// ".concat(n.name||"Layout"," layout\n"),S&&(o+="const ".concat((null===(c=n.name)||void 0===c?void 0:c.replace(/\s/g,""))||"Layout","Container = styled.div`\n"),o+="  display: grid;\n",o+="  grid-template-columns: repeat(12, 1fr);\n",o+="  gap: 16px;\n",o+="`;\n\n"),o+="const ".concat((null===(t=n.name)||void 0===t?void 0:t.replace(/\s/g,""))||"Layout"," = () => {\n"),o+="  return (\n",S?(o+="    <".concat((null===(a=n.name)||void 0===a?void 0:a.replace(/\s/g,""))||"Layout","Container>\n"),n.components&&n.components.length>0?n.components.forEach((function(n){var t=e.find((function(e){return e.id===n}));t&&(o+="      <".concat(t.name.replace(/\s/g,"")," />\n"))})):o+="      {/* Add components here */}\n",o+="    </".concat((null===(l=n.name)||void 0===l?void 0:l.replace(/\s/g,""))||"Layout","Container>\n")):(o+='    <div className="'.concat((n.name||"layout").toLowerCase().replace(/\s/g,"-"),'-container">\n'),n.components&&n.components.length>0?n.components.forEach((function(n){var t=e.find((function(e){return e.id===n}));t&&(o+="      <".concat(t.name.replace(/\s/g,"")," />\n"))})):o+="      {/* Add components here */}\n",o+="    </div>\n"),o+="  );\n",o+="};\n\n"})),o+="// Exports\n","components"!==V&&"all"!==V||e.forEach((function(e){o+="export { ".concat(e.name.replace(/\s/g,"")," };\n")})),"layouts"!==V&&"all"!==V||n.forEach((function(e){var n;o+="export { ".concat((null===(n=e.name)||void 0===n?void 0:n.replace(/\s/g,""))||"Layout"," };\n")})),o},re=function(e,n,t){return"// Vue.js code export is coming soon\n// Selected ".concat(e.length," components and ").concat(n.length," layouts\n// Export format: Vue.js\n// Include styles: ").concat(S?"Yes":"No","\n// Include theme: ").concat(U?"Yes":"No","\n// Bundle type: ").concat(V)},ue=function(e,n,t){return"// Angular code export is coming soon\n// Selected ".concat(e.length," components and ").concat(n.length," layouts\n// Export format: Angular\n// Include styles: ").concat(S?"Yes":"No","\n// Include theme: ").concat(U?"Yes":"No","\n// Bundle type: ").concat(V)},ie=function(e,n,t){return"\x3c!-- HTML code export is coming soon --\x3e\n\x3c!-- Selected ".concat(e.length," components and ").concat(n.length," layouts --\x3e\n\x3c!-- Export format: HTML --\x3e\n\x3c!-- Include styles: ").concat(S?"Yes":"No"," --\x3e\n\x3c!-- Include theme: ").concat(U?"Yes":"No"," --\x3e\n\x3c!-- Bundle type: ").concat(V," --\x3e")};return i.createElement(N,null,i.createElement(L,{level:4},"Export Code"),i.createElement(T,null,"Export your components and layouts as code in various formats."),i.createElement(f.A,null),i.createElement(Y,null,i.createElement(j,null,i.createElement(w,{strong:!0},"Export Format"),i.createElement(p.A,{value:l,onChange:u,style:{width:"100%",marginTop:k.Ay.spacing[1]}},i.createElement(I,{value:"react"},"React"),i.createElement(I,{value:"vue"},"Vue.js"),i.createElement(I,{value:"angular"},"Angular"),i.createElement(I,{value:"html"},"HTML"))),i.createElement(j,null,i.createElement(w,{strong:!0},"Bundle Type"),i.createElement(g.Ay.Group,{value:V,onChange:function(e){return z(e.target.value)},style:{display:"block",marginTop:k.Ay.spacing[1]}},i.createElement(g.Ay,{value:"components"},"Components Only"),i.createElement(g.Ay,{value:"layouts"},"Layouts Only"),i.createElement(g.Ay,{value:"all"},"Components & Layouts"))),i.createElement(j,null,i.createElement(v.A,{direction:"vertical"},i.createElement(h.A,{checked:S,onChange:function(e){return B(e.target.checked)}},"Include Styles"),i.createElement(h.A,{checked:U,onChange:function(e){return H(e.target.checked)}},"Include Theme")))),i.createElement(A.A,{message:"Export Preview",description:"This is a preview of the exported code. You can copy it to clipboard or download it as a file.",type:"info",showIcon:!0,icon:i.createElement(x.A,null),style:{marginBottom:k.Ay.spacing[3]}}),i.createElement(R,null,_),i.createElement(f.A,null),i.createElement(v.A,null,i.createElement(E.Ay,{type:"primary",icon:i.createElement(C.A,null),onClick:function(){navigator.clipboard.writeText(_).then((function(){ce(!0),y.Ay.success("Code copied to clipboard"),setTimeout((function(){ce(!1)}),2e3)})).catch((function(e){console.error("Failed to copy code:",e),y.Ay.error("Failed to copy code")}))}},oe?"Copied!":"Copy Code"),i.createElement(E.Ay,{icon:i.createElement(b.A,null),onClick:function(){var e="app-builder-export.".concat("html"===l?"html":"js"),n=new Blob([_],{type:"text/plain"}),t=URL.createObjectURL(n),o=document.createElement("a");o.href=t,o.download=e,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(t),y.Ay.success("Code downloaded as ".concat(e))}},"Download")))}}}]);