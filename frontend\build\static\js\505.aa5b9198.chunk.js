"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[505],{5505:(e,t,n)=>{n.r(t),n.d(t,{default:()=>De});var r=n(436),a=n(4467),l=n(5544),i=n(3986),o=n(7528),c=n(6540),u=n(1468),d=n(9748),s=n(3598),m=n(7852),p=n(3903),g=n(9237),y=n(1143),f=n(359),v=n(2454),E=n(7046),h=n(261),x=n(234),b=n(3016),A=n(4358),S=n(7197),C=n(7152),w=n(6370),L=n(677),k=n(2702),D=n(7355),O=n(9249),I=n(321),j=b.A.Title,z=b.A.Paragraph,R=A.A.Option;const T=function(){var e=(0,c.useState)([{id:"1",name:"Header Layout",type:"flex",description:"Simple header with navigation",items:["Header","Navigation","Content"]},{id:"2",name:"Sidebar Layout",type:"grid",description:"Layout with sidebar and main content",items:["Sidebar","Main Content","Footer"]},{id:"3",name:"Card Grid",type:"grid",description:"Responsive card grid layout",items:["Card 1","Card 2","Card 3","Card 4"]}]),t=(0,l.A)(e,2),n=t[0],a=t[1],i=(0,c.useState)(""),o=(0,l.A)(i,2),u=o[0],d=o[1],m=(0,c.useState)("grid"),y=(0,l.A)(m,2),f=y[0],v=y[1],E=(0,c.useState)(null),x=(0,l.A)(E,2),b=x[0],T=x[1],P=function(e){T(e)};return c.createElement("div",{style:{padding:"20px"}},c.createElement(j,{level:3},"Layout Designer"),c.createElement(z,null,"Create and manage layout templates for your application. This is a basic version of the layout designer."),c.createElement(S.A,{message:"Basic Layout Designer",description:"This is a simplified version of the layout designer. It provides basic layout management functionality.",type:"info",showIcon:!0,style:{marginBottom:"24px"}}),c.createElement(C.A,{gutter:[24,24]},c.createElement(w.A,{xs:24,lg:12},c.createElement(L.A,{title:"Create New Layout",style:{marginBottom:"24px"}},c.createElement(k.A,{direction:"vertical",style:{width:"100%"}},c.createElement(D.A,{placeholder:"Layout name",value:u,onChange:function(e){return d(e.target.value)},prefix:c.createElement(I.A,null)}),c.createElement(A.A,{value:f,onChange:v,style:{width:"100%"}},c.createElement(R,{value:"grid"},"Grid Layout"),c.createElement(R,{value:"flex"},"Flex Layout"),c.createElement(R,{value:"custom"},"Custom Layout")),c.createElement(O.Ay,{type:"primary",icon:c.createElement(g.A,null),onClick:function(){if(u.trim()){var e={id:Date.now().toString(),name:u.trim(),type:f,description:"Custom layout",items:["Item 1","Item 2"]};a([].concat((0,r.A)(n),[e])),d(""),v("grid")}},disabled:!u.trim(),block:!0},"Add Layout"))),c.createElement(L.A,{title:"Layout Library"},c.createElement(k.A,{direction:"vertical",style:{width:"100%"}},n.map((function(e){return c.createElement(L.A,{key:e.id,size:"small",style:{cursor:"pointer",border:(null==b?void 0:b.id)===e.id?"2px solid #1890ff":"1px solid #d9d9d9"},onClick:function(){return P(e)},actions:[c.createElement(h.A,{key:"edit",onClick:function(t){t.stopPropagation(),P(e)}}),c.createElement(s.A,{key:"delete",onClick:function(t){var r;t.stopPropagation(),r=e.id,a(n.filter((function(e){return e.id!==r}))),b&&b.id===r&&T(null)}})]},c.createElement(L.A.Meta,{title:e.name,description:c.createElement("div",null,c.createElement("div",null,"Type: ",e.type),c.createElement("div",null,e.description),c.createElement("div",null,"Items: ",e.items.join(", ")))}))}))))),c.createElement(w.A,{xs:24,lg:12},c.createElement(L.A,{title:"Layout Preview"},b?c.createElement("div",null,c.createElement(j,{level:4},b.name),c.createElement(z,null,b.description),c.createElement("div",{style:{border:"2px dashed #d9d9d9",borderRadius:"8px",padding:"20px",minHeight:"300px",background:"#fafafa"}},c.createElement("div",{style:{display:"flex"===b.type?"flex":"grid",gridTemplateColumns:"grid"===b.type?"repeat(auto-fit, minmax(150px, 1fr))":"none",gap:"16px",height:"100%"}},b.items.map((function(e,t){return c.createElement("div",{key:t,style:{background:"white",border:"1px solid #d9d9d9",borderRadius:"4px",padding:"16px",textAlign:"center",minHeight:"80px",display:"flex",alignItems:"center",justifyContent:"center"}},e)})))),c.createElement("div",{style:{marginTop:"16px"}},c.createElement(O.Ay,{type:"primary",icon:c.createElement(p.A,null)},"Save Layout"))):c.createElement("div",{style:{textAlign:"center",padding:"60px 20px",color:"#999"}},c.createElement(I.A,{style:{fontSize:"48px",marginBottom:"16px"}}),c.createElement("div",null,"Select a layout to preview"))))))};var P,M,W,H,B,F,N,Y,U,G,X,_,J,q,V,K,Q,Z,$,ee=["children"],te=["children"],ne=["children"],re=["children"],ae=["children"],le=["children"],ie=["children"],oe=["children"],ce=["children"],ue=["children"];function de(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function se(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?de(Object(n),!0).forEach((function(t){(0,a.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):de(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}try{var me=n(4816);X=me.addLayout,_=me.updateLayout,J=me.removeLayout}catch(e){console.warn("Redux actions not available, using fallback"),X=function(){return{type:"ADD_LAYOUT"}},_=function(){return{type:"UPDATE_LAYOUT"}},J=function(){return{type:"REMOVE_LAYOUT"}}}var pe,ge,ye,fe,ve,Ee,he,xe,be,Ae,Se=!0;try{var Ce=n(6191);q=Ce.styled,V=Ce.Button,K=Ce.Card,Q=Ce.Input,Z=Ce.Select,$=n(6020).Ay}catch(e){console.warn("Design system not available, using fallback"),Se=!1,$={spacing:["0","4px","8px","12px","16px","20px","24px","32px","48px"],colors:{primary:{main:"#1976d2",light:"#e3f2fd"},neutral:{100:"#f5f5f5",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e"},danger:{main:"#d32f2f"}},borderRadius:{md:"4px"},typography:{fontWeight:{medium:500,semibold:600},fontSize:{sm:"14px"}}}}Se&&q?(pe=q.div(P||(P=(0,o.A)(["\n    display: flex;\n    flex-direction: column;\n    gap: ",";\n  "])),$.spacing[4]),ge=q.div(M||(M=(0,o.A)(["\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n    gap: ",";\n  "])),$.spacing[4]),ye=q.div(W||(W=(0,o.A)(["\n    display: grid;\n    grid-template-columns: repeat(12, 1fr);\n    grid-template-rows: repeat(12, 40px);\n    gap: ",";\n    background-color: ",";\n    border: 1px dashed ",";\n    border-radius: ",";\n    padding: ",";\n    min-height: 500px;\n  "])),$.spacing[2],$.colors.neutral[100],$.colors.neutral[300],$.borderRadius.md,$.spacing[4]),fe=q.div(H||(H=(0,o.A)(["\n    grid-column: span ",";\n    grid-row: span ",";\n    background-color: ",";\n    border: 1px solid ",";\n    border-radius: ",";\n    padding: ",";\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n    cursor: ",";\n    opacity: ",";\n    box-shadow: ",";\n  "])),(function(e){return e.width||3}),(function(e){return e.height||2}),(function(e){return e.isPlaceholder?$.colors.primary.light:"white"}),(function(e){return e.isSelected?$.colors.primary.main:$.colors.neutral[300]}),$.borderRadius.md,$.spacing[2],(function(e){return e.isDragging?"grabbing":"grab"}),(function(e){return e.isDragging?.5:1}),(function(e){return e.isSelected?"0 0 0 2px ".concat($.colors.primary.main):"none"})),ve=q.div(B||(B=(0,o.A)(["\n    display: flex;\n    flex-wrap: wrap;\n    gap: ",";\n    margin-bottom: ",";\n  "])),$.spacing[2],$.spacing[4]),Ee=q.div(F||(F=(0,o.A)(["\n    padding: ",";\n    background-color: white;\n    border: 1px solid ",";\n    border-radius: ",";\n    cursor: grab;\n    display: flex;\n    align-items: center;\n    gap: ",";\n\n    &:hover {\n      background-color: ",";\n    }\n  "])),$.spacing[2],$.colors.neutral[300],$.borderRadius.md,$.spacing[2],$.colors.neutral[100]),he=q.div(N||(N=(0,o.A)(["\n    display: flex;\n    flex-direction: column;\n    gap: ",";\n  "])),$.spacing[3]),xe=q.div(Y||(Y=(0,o.A)(["\n    display: flex;\n    flex-direction: column;\n    gap: ",";\n  "])),$.spacing[2]),be=q.div(U||(U=(0,o.A)(["\n    display: flex;\n    gap: ",";\n    margin-bottom: ",";\n  "])),$.spacing[2],$.spacing[4]),Ae=q.div(G||(G=(0,o.A)(["\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding: ",";\n    background-color: ",";\n    border-radius: ",";\n    text-align: center;\n  "])),$.spacing[8],$.colors.neutral[100],$.borderRadius.md)):(pe=function(e){var t=e.children,n=(0,i.A)(e,ee);return c.createElement("div",n,t)},ge=function(e){var t=e.children,n=(0,i.A)(e,te);return c.createElement("div",n,t)},ye=function(e){var t=e.children,n=(0,i.A)(e,ne);return c.createElement("div",n,t)},fe=function(e){var t=e.children,n=(0,i.A)(e,re);return c.createElement("div",n,t)},ve=function(e){var t=e.children,n=(0,i.A)(e,ae);return c.createElement("div",n,t)},Ee=function(e){var t=e.children,n=(0,i.A)(e,le);return c.createElement("div",n,t)},he=function(e){var t=e.children,n=(0,i.A)(e,ie);return c.createElement("div",n,t)},xe=function(e){var t=e.children,n=(0,i.A)(e,oe);return c.createElement("div",n,t)},be=function(e){var t=e.children,n=(0,i.A)(e,ce);return c.createElement("div",n,t)},Ae=function(e){var t=e.children,n=(0,i.A)(e,ue);return c.createElement("div",n,t)});var we=function(e){var t=e.component,n=(e.index,e.onSelect),r=e.isSelected,a=e.onRemove,i=e.onDragStart,o=e.onDragEnd,u=(0,c.useState)(!1),m=(0,l.A)(u,2),p=m[0],g=m[1],y=(0,c.useState)({x:t.x||0,y:t.y||0}),f=(0,l.A)(y,2),v=f[0],E=f[1];return c.createElement(fe,{isDragging:p,isSelected:r,width:t.width,height:t.height,style:{gridColumn:"".concat(v.x+1," / span ").concat(t.width),gridRow:"".concat(v.y+1," / span ").concat(t.height),cursor:p?"grabbing":"grab",position:"relative",zIndex:p?10:1},onClick:function(e){e.stopPropagation(),n(t)},onMouseDown:function(e){g(!0),i&&i(t);var n=e.clientX,r=e.clientY,a=v.x,l=v.y,c=function(e){var t=e.clientX-n,i=e.clientY-r,o=Math.max(0,a+Math.round(t/50)),c=Math.max(0,l+Math.round(i/40));E({x:o,y:c})},u=function(){g(!1),o&&o(t,v),document.removeEventListener("mousemove",c),document.removeEventListener("mouseup",u)};document.addEventListener("mousemove",c),document.addEventListener("mouseup",u)}},c.createElement("div",null,c.createElement("div",{style:{fontWeight:$.typography.fontWeight.semibold}},t.name),c.createElement("div",{style:{fontSize:$.typography.fontSize.sm,color:$.colors.neutral[500]}},t.type)),c.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},c.createElement("div",{style:{fontSize:$.typography.fontSize.sm,color:$.colors.neutral[500]}},t.width,"x",t.height),c.createElement("div",{style:{display:"flex",gap:"4px"}},c.createElement(d.A,{style:{cursor:"grab"}}),c.createElement(s.A,{style:{cursor:"pointer",color:$.colors.danger.main},onClick:function(e){e.stopPropagation(),a&&a(t.id)}}))))},Le=function(e){var t=e.onDrop,n=e.children,r=(0,c.useState)(!1),a=(0,l.A)(r,2),i=a[0],o=a[1],u=c.useRef(null);return c.createElement("div",{ref:u,style:{position:"relative",height:"100%"},onDragOver:function(e){e.preventDefault(),o(!0)},onDragLeave:function(){o(!1)},onDrop:function(e){e.preventDefault(),o(!1);var n=u.current.getBoundingClientRect(),r=e.clientX-n.left,a=e.clientY-n.top,l=Math.floor(r/50),i=Math.floor(a/40);if(t)try{var c=JSON.parse(e.dataTransfer.getData("application/json"));t(c,{x:l,y:i})}catch(e){console.error("Error parsing drag data:",e)}},onClick:function(){}},n,i&&c.createElement("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(37, 99, 235, 0.1)",border:"2px dashed ".concat($.colors.primary.main),borderRadius:$.borderRadius.md,zIndex:10}}))},ke=function(){var e=(0,u.wA)(),t=(0,u.d4)((function(e){try{var t,n;return(null===(t=e.app)||void 0===t?void 0:t.components)||(null===(n=e.appData)||void 0===n?void 0:n.components)||[]}catch(e){return console.warn("Error accessing components from state:",e),[]}})),n=(0,u.d4)((function(e){try{var t,n;return(null===(t=e.app)||void 0===t?void 0:t.layouts)||(null===(n=e.appData)||void 0===n?void 0:n.layouts)||[]}catch(e){return console.warn("Error accessing layouts from state:",e),[]}})),a=(0,c.useState)(""),i=(0,l.A)(a,2),o=i[0],b=i[1],A=(0,c.useState)("grid"),S=(0,l.A)(A,2),C=S[0],w=S[1],L=(0,c.useState)([]),k=(0,l.A)(L,2),D=k[0],O=k[1],I=(0,c.useState)(null),j=(0,l.A)(I,2),z=j[0],R=j[1],T=(0,c.useState)(null),P=(0,l.A)(T,2),M=P[0],W=P[1],H=(0,c.useState)(!1),B=(0,l.A)(H,2),F=B[0],N=B[1],Y=(0,c.useState)({}),U=(0,l.A)(Y,2),G=U[0],q=U[1],ee=(0,c.useState)(3),te=(0,l.A)(ee,2),ne=te[0],re=te[1],ae=(0,c.useState)(2),le=(0,l.A)(ae,2),ie=le[0],oe=le[1],ce=function(){var e={};return o.trim()||(e.name="Layout name is required"),q(e),0===Object.keys(e).length},ue=function(e){R(e),b(e.name),w(e.type),O(e.items||[]),N(!0),q({})},de=function(e){O(D.filter((function(t){return t.id!==e}))),M&&M.id===e&&W(null)};return c.createElement(pe,null,c.createElement(K,null,c.createElement(K.Header,null,c.createElement(K.Title,null,F?"Edit Layout":"Create Layout"),F&&c.createElement(V,{variant:"text",size:"small",onClick:function(){b(""),w("grid"),O([]),R(null),N(!1),q({})},startIcon:c.createElement(m.A,null)},"Cancel")),c.createElement(K.Content,null,c.createElement(he,null,c.createElement(xe,null,c.createElement(Q,{label:"Layout Name",value:o,onChange:function(e){return b(e.target.value)},placeholder:"Enter layout name",fullWidth:!0,error:!!G.name,helperText:G.name})),c.createElement(xe,null,c.createElement(Z,{label:"Layout Type",value:C,onChange:function(e){return w(e.target.value)},options:[{value:"grid",label:"Grid Layout"},{value:"flex",label:"Flex Layout"},{value:"custom",label:"Custom Layout"}],fullWidth:!0})))),c.createElement(K.Footer,null,F?c.createElement(V,{variant:"primary",onClick:function(){if(z&&ce())try{var t=se(se({},z),{},{name:o.trim(),type:C,items:D,updatedAt:(new Date).toISOString()});e(_(t.id,t)),b(""),w("grid"),O([]),R(null),N(!1),q({}),console.log("Layout updated successfully:",t)}catch(e){console.error("Error updating layout:",e),q({submit:"Failed to update layout"})}},startIcon:c.createElement(p.A,null)},"Update Layout"):c.createElement(V,{variant:"primary",onClick:function(){if(ce())try{var t={id:Date.now().toString(),name:o.trim(),type:C,items:D,createdAt:(new Date).toISOString()};e(X(t)),b(""),w("grid"),O([]),q({}),console.log("Layout added successfully:",t)}catch(e){console.error("Error adding layout:",e),q({submit:"Failed to add layout"})}},startIcon:c.createElement(g.A,null)},"Add Layout"))),c.createElement(K,null,c.createElement(K.Header,null,c.createElement(K.Title,null,"Layout Designer")),c.createElement(K.Content,null,c.createElement(ve,null,c.createElement("div",{style:{marginRight:$.spacing[4],fontWeight:$.typography.fontWeight.medium}},"Component Palette:"),t.map((function(e){return c.createElement(Ee,{key:e.id,draggable:!0,onDragStart:function(t){t.dataTransfer.setData("application/json",JSON.stringify({id:e.id,name:e.name,type:e.type})),t.dataTransfer.effectAllowed="copy"}},c.createElement(d.A,null),c.createElement("span",null,e.name))})),0===t.length&&c.createElement("div",{style:{color:$.colors.neutral[500]}},"No components available. Create components first.")),c.createElement(be,null,c.createElement("div",{style:{display:"flex",alignItems:"center",gap:$.spacing[2]}},c.createElement(y.A,null),c.createElement(Q,{label:"Width",type:"number",min:1,max:12,value:ne,onChange:function(e){return re(parseInt(e.target.value,10))},style:{width:"80px"}})),c.createElement("div",{style:{display:"flex",alignItems:"center",gap:$.spacing[2]}},c.createElement(f.A,null),c.createElement(Q,{label:"Height",type:"number",min:1,max:12,value:ie,onChange:function(e){return oe(parseInt(e.target.value,10))},style:{width:"80px"}})),M&&c.createElement(V,{variant:"primary",size:"small",onClick:function(){if(M){var e=D.map((function(e){return e.id===M.id?se(se({},e),{},{width:ne,height:ie}):e}));O(e),W(null)}}},"Apply")),c.createElement(Le,{onDrop:function(e,n){var a=t.find((function(t){return t.id===e.id}));if(a){var l=Math.floor(10*Math.random())+1,i=Math.floor(10*Math.random())+1,o={id:Date.now().toString(),componentId:a.id,name:a.name,type:a.type,x:l,y:i,width:ne,height:ie};O([].concat((0,r.A)(D),[o]))}}},c.createElement(ye,null,D.map((function(e,t){return c.createElement(we,{key:e.id,component:e,index:t,onSelect:function(){return function(e){W(e),re(e.width),oe(e.height)}(e)},isSelected:M&&M.id===e.id,onRemove:de,onDragEnd:function(e,t){var n=D.map((function(n){return n.id===e.id?se(se({},n),{},{x:t.x,y:t.y}):n}));O(n)}})})))))),c.createElement(K,null,c.createElement(K.Header,null,c.createElement(K.Title,null,"Saved Layouts")),c.createElement(K.Content,null,0===n.length?c.createElement(Ae,null,c.createElement("div",{style:{fontSize:"48px",color:$.colors.neutral[400],marginBottom:$.spacing[4]}},c.createElement(v.A,null)),c.createElement("h3",null,"No Layouts Yet"),c.createElement("p",null,"Create your first layout to get started")):c.createElement(ge,null,n.map((function(t){var n;return c.createElement(K,{key:t.id,elevation:"sm"},c.createElement(K.Header,null,c.createElement("div",null,c.createElement("div",{style:{fontWeight:$.typography.fontWeight.semibold}},t.name),c.createElement("div",{style:{fontSize:$.typography.fontSize.sm,color:$.colors.neutral[500]}},t.type)),c.createElement("div",{style:{display:"flex",gap:$.spacing[1]}},c.createElement(V,{variant:"text",size:"small",onClick:function(){return function(t){var n=se(se({},t),{},{id:Date.now().toString(),name:"".concat(t.name," (Copy)"),createdAt:(new Date).toISOString()});e(X(n))}(t)}},c.createElement(E.A,null)),c.createElement(V,{variant:"text",size:"small",onClick:function(){return ue(t)}},c.createElement(h.A,null)),c.createElement(V,{variant:"text",size:"small",onClick:function(){return function(t){try{e(J(t)),z&&z.id===t&&(b(""),w("grid"),O([]),R(null),N(!1)),console.log("Layout removed successfully:",t)}catch(e){console.error("Error removing layout:",e)}}(t.id)}},c.createElement(s.A,null)))),c.createElement(K.Content,{onClick:function(){return ue(t)}},c.createElement("div",{style:{height:"150px",backgroundColor:$.colors.neutral[100],borderRadius:$.borderRadius.md,display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer"}},c.createElement(x.A,{style:{fontSize:"24px",color:$.colors.neutral[400]}}))),c.createElement(K.Footer,null,c.createElement("div",{style:{fontSize:$.typography.fontSize.sm,color:$.colors.neutral[500]}},(null===(n=t.items)||void 0===n?void 0:n.length)||0," components")))}))))))};const De=function(){return Se&&q&&V&&K&&Q&&Z&&$?c.createElement(ke,null):(console.log("Using basic layout designer due to missing dependencies"),c.createElement(T,null))}}}]);