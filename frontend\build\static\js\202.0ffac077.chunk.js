"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[202],{5202:(e,t,n)=>{n.r(t),n.d(t,{default:()=>ut});var a,r,o,l,c,i,p,s=n(436),u=n(2284),m=n(4467),d=n(467),f=n(5544),A=n(4756),y=n.n(A),g=n(6540),E=n(3016),v=n(4358),h=n(2395),b=n(7355),x=n(9467),w=n(9740),k=n(6914),_=n(2652),S=n(677),O=n(2702),C=n(7977),N=n(9249),L=n(8295),P=n(2070),T=n(5039),q=n(2120),j=n(9029),D=n(7308),I=n(7829),J=n(321),F=n(9091),Q=n(1372),B=n(234),z=n(5763),M=n(2786),R=n(7046),U=n(756),Y=n(261),G=n(3598),H=n(8937),K=n(7028),V=n(9237),W=n(7450),Z=n(2877),X=n(3108),$=n(2441),ee=n(1083),te=n(7528),ne=n(5448),ae=n(3308),re=n(7206),oe=n(6552),le=n(7767),ce=n(4976),ie=n(8572),pe=n(6555),se=n(5182),ue=n(1427),me=n(761),de=n(8602),fe=n(4336),Ae=n(4103),ye=n(1606),ge=n(1250),Ee=(E.A.Title,E.A.Text),ve=(0,ge.Ay)(O.A)(a||(a=(0,te.A)(["\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s;\n\n  &:hover {\n    background-color: rgba(0, 0, 0, 0.05);\n  }\n"])));(0,ge.Ay)(S.A)(r||(r=(0,te.A)(["\n  max-width: 500px;\n  margin: 0 auto;\n"]))),ge.Ay.div(o||(o=(0,te.A)(["\n  display: flex;\n  align-items: center;\n  gap: 16px;\n"]))),ge.Ay.div(l||(l=(0,te.A)(["\n  flex: 1;\n"]))),ge.Ay.div(c||(c=(0,te.A)(["\n  display: flex;\n  justify-content: space-around;\n  text-align: center;\n"]))),ge.Ay.div(i||(i=(0,te.A)(["\n  padding: 0 16px;\n"]))),ge.Ay.div(p||(p=(0,te.A)(["\n  display: flex;\n  justify-content: space-between;\n  gap: 8px;\n"])));const he=function(e){var t=e.onLogout,n=e.showAvatar,a=void 0===n||n,r=e.showName,o=void 0===r||r,l=e.showMenu,c=void 0===l||l,i=(0,g.useState)(ye.A.getUser()),p=(0,f.A)(i,2),s=p[0],u=p[1];if((0,g.useEffect)((function(){return ye.A.addListener((function(e){"login"===e||"register"===e?u(ye.A.getUser()):"logout"===e&&u(null)}))}),[]),!s)return null;var m=[{key:"profile",label:"Profile",icon:g.createElement(W.A,null),onClick:function(){return window.location.href="/profile"}},{key:"settings",label:"Settings",icon:g.createElement(de.A,null),onClick:function(){return window.location.href="/settings"}},{key:"divider",type:"divider"},{key:"logout",label:"Logout",icon:g.createElement(fe.A,null),onClick:function(){ye.A.logout(),t&&t()}}];return!a||o||c?a&&o&&!c?g.createElement(O.A,null,g.createElement(ue.A,{size:"default",src:s.avatar,icon:!s.avatar&&g.createElement(W.A,null),alt:s.name}),g.createElement(Ee,{strong:!0},s.name)):c?g.createElement(me.A,{overlay:g.createElement(re.A,{items:m}),trigger:["click"],placement:"bottomRight"},g.createElement(ve,null,g.createElement(ue.A,{size:"default",src:s.avatar,icon:!s.avatar&&g.createElement(W.A,null),alt:s.name}),o&&g.createElement(Ee,{strong:!0},s.name),g.createElement(Ae.A,null))):g.createElement(Ee,{strong:!0},s.name):g.createElement(ue.A,{size:"large",src:s.avatar,icon:!s.avatar&&g.createElement(W.A,null),alt:s.name})};var be,xe=n(414),we=(0,ge.Ay)(N.Ay)(be||(be=(0,te.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s;\n\n  &:hover {\n    transform: rotate(30deg);\n  }\n"])));const ke=function(e){var t=e.isDarkMode,n=e.toggleTheme;return g.createElement(C.A,{title:t?"Switch to Light Mode":"Switch to Dark Mode"},g.createElement(we,{type:"text",icon:t?g.createElement(H.A,null):g.createElement(xe.A,null),onClick:n,"aria-label":t?"Switch to Light Mode":"Switch to Dark Mode"}))};var _e,Se,Oe=n(9444),Ce=n(2510),Ne=n(385),Le=n(1617),Pe=n(3567),Te=n(378),qe=n(7053),je=ge.Ay.div(_e||(_e=(0,te.A)(["\n  display: inline-flex;\n  align-items: center;\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 4px;\n  transition: background-color 0.3s;\n\n  &:hover {\n    background-color: rgba(0, 0, 0, 0.05);\n  }\n"]))),De=ge.Ay.span(Se||(Se=(0,te.A)(["\n  margin-left: 8px;\n  font-size: 12px;\n  @media (max-width: 768px) {\n    display: none;\n  }\n"])));const Ie=function(){var e=(0,g.useState)("disconnected"),t=(0,f.A)(e,2),n=t[0],a=t[1],r=(0,g.useState)(null),o=(0,f.A)(r,2),l=o[0],c=o[1],i=(0,g.useState)(0),p=(0,f.A)(i,2),s=p[0],u=p[1],m=(0,g.useState)(!1),d=(0,f.A)(m,2),A=d[0],y=d[1],E=(0,g.useState)(navigator.onLine),v=(0,f.A)(E,2),h=v[0],b=v[1],x=(0,g.useState)(null),w=(0,f.A)(x,2),k=w[0],_=w[1];(0,g.useEffect)((function(){a(qe.A.isConnected?"connected":"disconnected"),b(navigator.onLine),u(0);try{if("function"==typeof qe.A.getOfflineQueueStatus){var e=qe.A.getOfflineQueueStatus();_(e)}}catch(e){console.error("Error getting offline queue status:",e)}var t=function(){a("connected"),u(0)},n=function(e){a("disconnected"),e&&e.code&&c({message:"Disconnected (code: ".concat(e.code,")"),timestamp:new Date})},r=function(){a("connecting")},o=function(e){a("error"),c(e)},l=function(e){a("connecting"),u(e.attempt)},i=function(e){b(e.online)},p=function(){try{if("function"==typeof qe.A.getOfflineQueueStatus){var e=qe.A.getOfflineQueueStatus();_(e)}}catch(e){console.error("Error getting updated offline queue status:",e)}};qe.A.on("connect",t),qe.A.on("disconnect",n),qe.A.on("connecting",r),qe.A.on("error",o),qe.A.on("reconnect_scheduled",l),qe.A.on("network_status",i),"function"==typeof qe.A.on&&"function"==typeof qe.A.getOfflineQueueStatus&&(qe.A.on("message_queued_offline",p),qe.A.on("offline_queue_loaded",p),qe.A.on("offline_queue_cleared",p));var s=function(){return b(!0)},m=function(){return b(!1)};return window.addEventListener("online",s),window.addEventListener("offline",m),function(){qe.A.off("connect",t),qe.A.off("disconnect",n),qe.A.off("connecting",r),qe.A.off("error",o),qe.A.off("reconnect_scheduled",l),qe.A.off("network_status",i),"function"==typeof qe.A.off&&"function"==typeof qe.A.getOfflineQueueStatus&&(qe.A.off("message_queued_offline",p),qe.A.off("offline_queue_loaded",p),qe.A.off("offline_queue_cleared",p)),window.removeEventListener("online",s),window.removeEventListener("offline",m)}}),[]);var S=function(){if(!h){var e=(null==k?void 0:k.count)||0;return{icon:g.createElement(Oe.A,{style:{color:"#faad14"}}),text:e>0?"Offline (".concat(e,")"):"Offline",status:"warning",tooltip:"Browser is offline. "+(e>0?"".concat(e," messages queued for delivery when online."):"No queued messages.")}}if(h&&(null==k?void 0:k.count)>0&&"connected"===n)return{icon:g.createElement(Ce.A,{style:{color:"#1890ff"}}),text:"Syncing (".concat(k.count,")"),status:"processing",tooltip:"Connected. Syncing ".concat(k.count," queued messages.")};switch(n){case"connected":return{icon:g.createElement(Ne.A,{style:{color:"#52c41a"}}),text:"Connected",status:"success",tooltip:"WebSocket connection established"};case"disconnected":return{icon:g.createElement(Le.A,{style:{color:"#bfbfbf"}}),text:"Disconnected",status:"default",tooltip:l?"Disconnected: ".concat(l.message):"WebSocket disconnected"};case"connecting":return{icon:g.createElement(Pe.A,{style:{color:"#1890ff"}}),text:s>0?"Reconnecting (".concat(s,")"):"Connecting",status:"processing",tooltip:"Attempting to establish WebSocket connection"};case"error":return{icon:g.createElement(Te.A,{style:{color:"#ff4d4f"}}),text:"Connection Error",status:"error",tooltip:l?"Error: ".concat(l.message):"WebSocket connection error"};default:return{icon:g.createElement(Le.A,{style:{color:"#bfbfbf"}}),text:"Unknown",status:"default",tooltip:"WebSocket status unknown"}}}();return g.createElement(C.A,{title:g.createElement("div",null,g.createElement("div",null,S.tooltip),l&&g.createElement("div",{style:{marginTop:4}},"Last error: ",l.message,l.timestamp&&g.createElement("div",{style:{fontSize:"0.8em",opacity:.8}},new Date(l.timestamp).toLocaleTimeString())),(null==k?void 0:k.count)>0&&g.createElement("div",{style:{marginTop:4,borderTop:"1px solid rgba(255,255,255,0.2)",paddingTop:4}},g.createElement("div",null,"Offline queue: ",k.count," message",1!==k.count?"s":""),g.createElement("div",{style:{marginTop:4}},g.createElement("button",{onClick:function(e){if(e.stopPropagation(),(null==k?void 0:k.count)>0&&"function"==typeof qe.A.clearOfflineQueue){var t=qe.A.clearOfflineQueue();alert("Cleared ".concat(t," messages from the offline queue."))}},style:{background:"rgba(255,255,255,0.2)",border:"none",padding:"2px 8px",borderRadius:"4px",cursor:"pointer",fontSize:"0.8em"}},"Clear Queue"))),!h&&g.createElement("div",{style:{marginTop:8,fontSize:"0.9em"}},"You are offline. Check your internet connection."),h&&"connected"!==n&&g.createElement("div",{style:{marginTop:8,fontSize:"0.9em"}},"Click to reconnect"),h&&"connected"===n&&(null==k?void 0:k.count)>0&&g.createElement("div",{style:{marginTop:8,fontSize:"0.9em"}},"Click to sync offline messages")),open:A,onOpenChange:y},g.createElement(je,{onClick:function(){h?"connected"!==n?qe.A.reconnect():(null==k?void 0:k.count)>0&&"function"==typeof qe.A.processOfflineQueue&&qe.A.processOfflineQueue():alert("You are currently offline. Please check your internet connection.")},"data-testid":"websocket-status"},g.createElement(q.A,{status:S.status}),g.createElement(O.A,{size:4},S.icon,g.createElement(De,null,S.text))))};var Je,Fe,Qe,Be,ze=ne.A.Header,Me=(0,ge.Ay)(ze)(Je||(Je=(0,te.A)(["\n  display: flex;\n  align-items: center;\n  background-color: #fff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  padding: 0 24px;\n  height: 64px;\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n"]))),Re=ge.Ay.div(Fe||(Fe=(0,te.A)(["\n  font-size: 20px;\n  font-weight: bold;\n  margin-right: 48px;\n\n  a {\n    color: #1890ff;\n    text-decoration: none;\n  }\n"]))),Ue=(0,ge.Ay)(re.A)(Qe||(Qe=(0,te.A)(["\n  flex: 1;\n  border-bottom: none;\n  background: transparent;\n"]))),Ye=ge.Ay.div(Be||(Be=(0,te.A)(["\n  display: flex;\n  align-items: center;\n"])));const Ge=function(){var e=(0,le.Zp)(),t=(0,le.zy)(),n=ye.A.isAuthenticated(),a=t.pathname;return g.createElement(Me,null,g.createElement(Re,null,g.createElement(ce.N_,{to:"/"},"App Builder")),g.createElement(Ue,{theme:"light",mode:"horizontal",selectedKeys:[a]},g.createElement(re.A.Item,{key:"/",icon:g.createElement(ie.A,null)},g.createElement(ce.N_,{to:"/"},"Home")),g.createElement(re.A.Item,{key:"/apps",icon:g.createElement(Q.A,null)},g.createElement(ce.N_,{to:"/apps"},"My Apps")),g.createElement(re.A.Item,{key:"/templates",icon:g.createElement(Q.A,null)},g.createElement(ce.N_,{to:"/templates"},"Templates"))),g.createElement(Ye,null,g.createElement(Ie,null),g.createElement(oe.A,{type:"vertical",style:{margin:"0 8px"}}),g.createElement(ke,null),n?g.createElement(he,{showAvatar:!0,showName:!0,showMenu:!0,onLogout:function(){ye.A.logout(),e("/")}}):g.createElement(O.A,{split:g.createElement(oe.A,{type:"vertical"})},g.createElement(N.Ay,{type:"link",icon:g.createElement(pe.A,null),onClick:function(){e("/auth?tab=login")}},"Login"),g.createElement(N.Ay,{type:"primary",icon:g.createElement(se.A,null),onClick:function(){e("/auth?tab=register")}},"Register"))))};n(3986);var He,Ke,Ve,We=function(e){var t=e.targetId,n=void 0===t?"main-content":t,a=e.children,r=void 0===a?"Skip to main content":a;return g.createElement("a",{href:"#".concat(n),className:"skip-link","aria-label":r},r,g.createElement("style",null,"\n        .skip-link {\n          position: absolute;\n          top: -40px;\n          left: 0;\n          background: #2563EB;\n          color: white;\n          padding: 8px;\n          z-index: 100;\n          transition: top 0.3s;\n        }\n\n        .skip-link:focus {\n          top: 0;\n        }\n      "))},Ze=ne.A.Content,Xe=ne.A.Footer,$e=(0,ge.Ay)(ne.A)(He||(He=(0,te.A)(["\n  min-height: 100vh;\n"]))),et=(0,ge.Ay)(Ze)(Ke||(Ke=(0,te.A)(["\n  padding: 24px;\n  background-color: #f5f5f5;\n  min-height: calc(100vh - 64px - 70px); /* Viewport height - header - footer */\n"]))),tt=(0,ge.Ay)(Xe)(Ve||(Ve=(0,te.A)(["\n  text-align: center;\n  padding: 24px;\n  background-color: #fff;\n"])));const nt=function(e){var t=e.children;return g.createElement($e,null,g.createElement(We,{targetId:"main-content"}),g.createElement(Ge,null),g.createElement(et,{id:"main-content"},t),g.createElement(tt,null,"App Builder ©",(new Date).getFullYear()," - Build with ease"),g.createElement(ae.A,null))};function at(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function rt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?at(Object(n),!0).forEach((function(t){(0,m.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):at(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ot=E.A.Title,lt=E.A.Text,ct=E.A.Paragraph,it=v.A.Option,pt=h.A.TabPane,st=b.A.TextArea;const ut=function(){var e=(0,g.useState)([]),t=(0,f.A)(e,2),n=t[0],a=t[1],r=(0,g.useState)([]),o=(0,f.A)(r,2),l=o[0],c=o[1],i=(0,g.useState)([]),p=(0,f.A)(i,2),m=p[0],A=p[1],E=(0,g.useState)(!0),te=(0,f.A)(E,2),ne=te[0],ae=te[1],re=(0,g.useState)(""),oe=(0,f.A)(re,2),le=oe[0],ce=oe[1],ie=(0,g.useState)(""),pe=(0,f.A)(ie,2),se=pe[0],ue=pe[1],me=(0,g.useState)(""),de=(0,f.A)(me,2),fe=de[0],Ae=de[1],ye=(0,g.useState)("all"),ge=(0,f.A)(ye,2),Ee=ge[0],ve=ge[1],he=(0,g.useState)(!1),be=(0,f.A)(he,2),xe=be[0],we=be[1],ke=(0,g.useState)(!1),_e=(0,f.A)(ke,2),Se=_e[0],Oe=_e[1],Ce=(0,g.useState)(!1),Ne=(0,f.A)(Ce,2),Le=Ne[0],Pe=Ne[1],Te=(0,g.useState)(null),qe=(0,f.A)(Te,2),je=qe[0],De=qe[1],Ie=x.A.useForm(),Je=(0,f.A)(Ie,1)[0],Fe=(0,g.useState)(!1),Qe=(0,f.A)(Fe,2),Be=Qe[0],ze=Qe[1],Me=(0,g.useState)("my"),Re=(0,f.A)(Me,2),Ue=Re[0],Ye=Re[1],Ge=(0,g.useState)("components"),He=(0,f.A)(Ge,2),Ke=He[0],Ve=He[1],We=["button","input","select","checkbox","radio","switch","slider","date-picker","time-picker","upload","form","table","list","card","tabs","modal","drawer","menu","layout","custom"],Ze=["grid","flex","sidebar","header-footer","dashboard","landing","blog","portfolio","ecommerce","admin","custom"],Xe=[{value:"business",label:"Business Apps"},{value:"ecommerce",label:"E-commerce"},{value:"portfolio",label:"Portfolio"},{value:"dashboard",label:"Dashboard"},{value:"landing",label:"Landing Page"},{value:"blog",label:"Blog"},{value:"social",label:"Social Media"},{value:"education",label:"Education"},{value:"healthcare",label:"Healthcare"},{value:"finance",label:"Finance"},{value:"other",label:"Other"}];(0,g.useEffect)((function(){$e()}),[Ue,Ke]);var $e=function(){var e=(0,d.A)(y().mark((function e(){return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ae(!0),e.prev=1,e.next=4,Promise.all([et(),tt(),at()]);case 4:e.next=10;break;case 6:e.prev=6,e.t0=e.catch(1),console.error("Error fetching templates:",e.t0),w.Ay.error("Failed to load templates");case 10:return e.prev=10,ae(!1),e.finish(10);case 13:case"end":return e.stop()}}),e,null,[[1,6,10,13]])})));return function(){return e.apply(this,arguments)}}(),et=function(){var e=(0,d.A)(y().mark((function e(){var t,n,r;return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t="/api/component-templates/",n=new URLSearchParams,"my"===Ue?n.append("user","current"):"public"===Ue&&n.append("is_public","true"),n.toString()&&(t+="?".concat(n.toString())),e.next=7,ee.A.get(t);case 7:r=e.sent,a(r.data.results||r.data),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("Error fetching component templates:",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(){return e.apply(this,arguments)}}(),tt=function(){var e=(0,d.A)(y().mark((function e(){var t,n,a;return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t="/api/layout-templates/",n=new URLSearchParams,"my"===Ue?n.append("user","current"):"public"===Ue&&n.append("is_public","true"),n.toString()&&(t+="?".concat(n.toString())),e.next=7,ee.A.get(t);case 7:a=e.sent,c(a.data.results||a.data),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("Error fetching layout templates:",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(){return e.apply(this,arguments)}}(),at=function(){var e=(0,d.A)(y().mark((function e(){var t,n,a;return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,t="/api/app-templates/",n=new URLSearchParams,"my"===Ue?n.append("user","current"):"public"===Ue&&n.append("is_public","true"),n.toString()&&(t+="?".concat(n.toString())),e.next=7,ee.A.get(t);case 7:a=e.sent,A(a.data.results||a.data),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.error("Error fetching app templates:",e.t0);case 14:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(){return e.apply(this,arguments)}}(),ut=function(){var e=(0,d.A)(y().mark((function e(t){var n,a;return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ze(!0),e.prev=1,"components"===Ke?(n="/api/component-templates/",a=rt(rt({},t),{},{default_props:JSON.stringify(t.default_props||{})})):"layouts"===Ke?(n="/api/layout-templates/",a=rt(rt({},t),{},{components:JSON.stringify(t.components||{}),default_props:JSON.stringify(t.default_props||{})})):"apps"===Ke&&(n="/api/app-templates/",a=rt(rt({},t),{},{components:JSON.stringify(t.components||{}),default_props:JSON.stringify(t.default_props||{}),required_components:JSON.stringify(t.required_components||[])})),e.next=5,ee.A.post(n,a);case 5:w.Ay.success("Template created successfully"),we(!1),Je.resetFields(),$e(),e.next=15;break;case 11:e.prev=11,e.t0=e.catch(1),console.error("Error creating template:",e.t0),w.Ay.error("Failed to create template");case 15:return e.prev=15,ze(!1),e.finish(15);case 18:case"end":return e.stop()}}),e,null,[[1,11,15,18]])})));return function(t){return e.apply(this,arguments)}}(),mt=function(){var e=(0,d.A)(y().mark((function e(t){var n,a;return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ze(!0),e.prev=1,"component"===je.template_type?(n="/api/component-templates/".concat(je.id,"/"),a=rt(rt({},t),{},{default_props:"object"===(0,u.A)(t.default_props)?JSON.stringify(t.default_props):t.default_props})):"layout"===je.template_type?(n="/api/layout-templates/".concat(je.id,"/"),a=rt(rt({},t),{},{components:"object"===(0,u.A)(t.components)?JSON.stringify(t.components):t.components,default_props:"object"===(0,u.A)(t.default_props)?JSON.stringify(t.default_props):t.default_props})):"app"===je.template_type&&(n="/api/app-templates/".concat(je.id,"/"),a=rt(rt({},t),{},{components:"object"===(0,u.A)(t.components)?JSON.stringify(t.components):t.components,default_props:"object"===(0,u.A)(t.default_props)?JSON.stringify(t.default_props):t.default_props,required_components:"object"===(0,u.A)(t.required_components)?JSON.stringify(t.required_components):t.required_components})),e.next=5,ee.A.patch(n,a);case 5:w.Ay.success("Template updated successfully"),Oe(!1),$e(),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(1),console.error("Error updating template:",e.t0),w.Ay.error("Failed to update template");case 14:return e.prev=14,ze(!1),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[1,10,14,17]])})));return function(t){return e.apply(this,arguments)}}(),dt=function(){var e=(0,d.A)(y().mark((function e(t){var n;return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,"component"===t.template_type?n="/api/component-templates/".concat(t.id,"/"):"layout"===t.template_type?n="/api/layout-templates/".concat(t.id,"/"):"app"===t.template_type&&(n="/api/app-templates/".concat(t.id,"/")),e.next=4,ee.A.delete(n);case 4:w.Ay.success("Template deleted successfully"),$e(),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("Error deleting template:",e.t0),w.Ay.error("Failed to delete template");case 12:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),ft=function(){switch(Ke){case"layouts":return l.map((function(e){return rt(rt({},e),{},{template_type:"layout"})}));case"apps":return m.map((function(e){return rt(rt({},e),{},{template_type:"app"})}));default:return n.map((function(e){return rt(rt({},e),{},{template_type:"component"})}))}},At=function(){switch(Ke){case"layouts":return"Layout Template";case"apps":return"App Template";default:return"Component Template"}},yt=ft().filter((function(e){var t=""===le||e.name.toLowerCase().includes(le.toLowerCase())||e.description.toLowerCase().includes(le.toLowerCase()),n=!0;"components"===Ke&&se?n=e.component_type===se:"layouts"===Ke&&se?n=e.layout_type===se:"apps"===Ke&&fe&&(n=e.app_category===fe);var a="all"===Ee||"public"===Ee&&e.is_public||"private"===Ee&&!e.is_public;return t&&n&&a})),gt=function(){var e=ft();switch(Ke){case"layouts":return(0,s.A)(new Set(e.map((function(e){return e.layout_type}))));case"apps":return(0,s.A)(new Set(e.map((function(e){return e.app_category}))));default:return(0,s.A)(new Set(e.map((function(e){return e.component_type}))))}},Et=function(e){return g.createElement(_.A.Item,null,g.createElement(S.A,{title:g.createElement(O.A,null,function(){switch(e.template_type){case"layout":return g.createElement(J.A,null);case"app":return g.createElement(F.A,null);default:return g.createElement(Q.A,null)}}(),g.createElement(lt,{strong:!0},e.name),e.is_public?g.createElement(k.A,{icon:g.createElement(B.A,null),color:"green"},"Public"):g.createElement(k.A,{icon:g.createElement(z.A,null),color:"default"},"Private"),"app"===e.template_type&&e.preview_image&&g.createElement(k.A,{icon:g.createElement(M.A,null),color:"gold"},"Featured")),extra:g.createElement(O.A,null,g.createElement(C.A,{title:"Use Template"},g.createElement(N.Ay,{icon:g.createElement(R.A,null)})),g.createElement(C.A,{title:"Export Template"},g.createElement(N.Ay,{icon:g.createElement(U.A,null),onClick:function(){return vt(e)}})),g.createElement(C.A,{title:"Edit Template"},g.createElement(N.Ay,{icon:g.createElement(Y.A,null),onClick:function(){return function(e){De(e);var t="string"==typeof e.default_props?JSON.parse(e.default_props):e.default_props,n=e.components&&"string"==typeof e.components?JSON.parse(e.components):e.components,a=e.required_components&&"string"==typeof e.required_components?JSON.parse(e.required_components):e.required_components,r={name:e.name,description:e.description,default_props:t,is_public:e.is_public};"component"===e.template_type?r.component_type=e.component_type:"layout"===e.template_type?(r.layout_type=e.layout_type,r.components=n):"app"===e.template_type&&(r.app_category=e.app_category,r.components=n,r.required_components=a,r.preview_image=e.preview_image),Je.setFieldsValue(r),Oe(!0)}(e)}})),g.createElement(C.A,{title:"Delete Template"},g.createElement(N.Ay,{danger:!0,icon:g.createElement(G.A,null),onClick:function(){return L.A.confirm({title:"Delete Template",content:'Are you sure you want to delete "'.concat(e.name,'"?'),okText:"Delete",okType:"danger",onOk:function(){return dt(e)}})}}))),style:{width:"100%"},cover:"app"===e.template_type&&e.preview_image?g.createElement(P.A,{alt:e.name,src:e.preview_image,height:120,style:{objectFit:"cover"},fallback:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"}):null},g.createElement(ct,{ellipsis:{rows:2}},e.description||"No description provided"),g.createElement(O.A,{wrap:!0},function(){switch(e.template_type){case"layout":return g.createElement(k.A,{color:"purple"},e.layout_type);case"app":return g.createElement(k.A,{color:"orange"},e.app_category);default:return g.createElement(k.A,{color:"blue"},e.component_type)}}(),"app"===e.template_type&&e.required_components&&e.required_components.length>0&&g.createElement(k.A,{color:"cyan"},g.createElement(H.A,null)," ",e.required_components.length," dependencies"),g.createElement(lt,{type:"secondary"},"Created by: ",e.user?e.user.username:"Anonymous"),g.createElement(lt,{type:"secondary"},"Created: ",new Date(e.created_at).toLocaleDateString()))))},vt=function(){var e=(0,d.A)(y().mark((function e(t){var n,a,r,o,l;return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,"component"===t.template_type?n="/api/component-templates/".concat(t.id,"/export_template/"):"layout"===t.template_type?n="/api/layout-templates/".concat(t.id,"/export_template/"):"app"===t.template_type&&(n="/api/app-templates/".concat(t.id,"/export_template/")),e.next=4,ee.A.get(n);case 4:a=e.sent,r=JSON.stringify(a.data,null,2),o=new Blob([r],{type:"application/json"}),(l=document.createElement("a")).href=URL.createObjectURL(o),l.download="".concat(t.name.replace(/\s+/g,"_"),"_template.json"),l.click(),w.Ay.success("Template exported successfully"),e.next=18;break;case 14:e.prev=14,e.t0=e.catch(0),console.error("Error exporting template:",e.t0),w.Ay.error("Failed to export template");case 18:case"end":return e.stop()}}),e,null,[[0,14]])})));return function(t){return e.apply(this,arguments)}}(),ht=function(){return g.createElement(x.A,{form:Je,layout:"vertical",onFinish:je?mt:ut},g.createElement(x.A.Item,{name:"name",label:"Template Name",rules:[{required:!0,message:"Please enter template name"}]},g.createElement(b.A,{placeholder:"Enter template name"})),g.createElement(x.A.Item,{name:"description",label:"Description"},g.createElement(st,{placeholder:"Enter template description",rows:4,maxLength:500,showCount:!0})),"components"===Ke&&g.createElement(x.A.Item,{name:"component_type",label:"Component Type",rules:[{required:!0,message:"Please select component type"}]},g.createElement(v.A,{placeholder:"Select component type"},We.map((function(e){return g.createElement(it,{key:e,value:e},e)})))),"layouts"===Ke&&g.createElement(x.A.Item,{name:"layout_type",label:"Layout Type",rules:[{required:!0,message:"Please select layout type"}]},g.createElement(v.A,{placeholder:"Select layout type"},Ze.map((function(e){return g.createElement(it,{key:e,value:e},e)})))),"apps"===Ke&&g.createElement(x.A.Item,{name:"app_category",label:"App Category",rules:[{required:!0,message:"Please select app category"}]},g.createElement(v.A,{placeholder:"Select app category"},Xe.map((function(e){return g.createElement(it,{key:e.value,value:e.value},e.label)})))),("layouts"===Ke||"apps"===Ke)&&g.createElement(x.A.Item,{name:"components",label:"Components Configuration",rules:[{required:!0,message:"Please enter components configuration"},{validator:function(e,t){try{return"string"==typeof t&&JSON.parse(t),Promise.resolve()}catch(e){return Promise.reject("Please enter valid JSON")}}}]},g.createElement(st,{placeholder:"Enter components configuration in JSON format",rows:8,style:{fontFamily:"monospace"}})),"apps"===Ke&&g.createElement(x.A.Item,{name:"required_components",label:"Required Components",rules:[{validator:function(e,t){try{if(t&&"string"==typeof t){var n=JSON.parse(t);if(!Array.isArray(n))return Promise.reject("Required components must be an array")}return Promise.resolve()}catch(e){return Promise.reject("Please enter valid JSON array")}}}]},g.createElement(st,{placeholder:'Enter required components as JSON array, e.g., ["button", "input"]',rows:3,style:{fontFamily:"monospace"}})),"apps"===Ke&&g.createElement(x.A.Item,{name:"preview_image",label:"Preview Image URL"},g.createElement(b.A,{placeholder:"Enter preview image URL (optional)"})),g.createElement(x.A.Item,{name:"default_props",label:"Default Properties",rules:[{validator:function(e,t){try{return t&&"string"==typeof t&&JSON.parse(t),Promise.resolve()}catch(e){return Promise.reject("Please enter valid JSON")}}}]},g.createElement(st,{placeholder:"Enter default properties in JSON format (optional)",rows:4,style:{fontFamily:"monospace"}})),g.createElement(x.A.Item,{name:"is_public",label:"Visibility",valuePropName:"checked"},g.createElement(T.A,{checkedChildren:g.createElement(B.A,null),unCheckedChildren:g.createElement(z.A,null)}),g.createElement(lt,{type:"secondary",style:{marginLeft:8}},"Make this template public")))},bt=function(){var e=(0,d.A)(y().mark((function e(t){var n;return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,"components"===Ke?n="/api/component-templates/import_template/":"layouts"===Ke?n="/api/layout-templates/import_template/":"apps"===Ke&&(n="/api/app-templates/import_template/"),e.next=4,ee.A.post(n,{template_data:t});case 4:w.Ay.success("Template imported successfully"),Pe(!1),$e(),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(0),console.error("Error importing template:",e.t0),w.Ay.error("Failed to import template");case 13:case"end":return e.stop()}}),e,null,[[0,9]])})));return function(t){return e.apply(this,arguments)}}();return g.createElement(nt,null,g.createElement("div",{className:"templates-page"},g.createElement("div",{className:"templates-header"},g.createElement(ot,{level:2},g.createElement(Q.A,null)," Template Library"),g.createElement(O.A,null,g.createElement(N.Ay,{icon:g.createElement(K.A,null),onClick:function(){return Pe(!0)}},"Import Template"),g.createElement(N.Ay,{type:"primary",icon:g.createElement(V.A,null),onClick:function(){Je.resetFields(),we(!0)}},"Create ",At()))),g.createElement("div",{className:"template-type-tabs"},g.createElement(h.A,{activeKey:Ke,onChange:Ve,type:"card",size:"large"},g.createElement(pt,{tab:g.createElement("span",null,g.createElement(Q.A,null),"Components",g.createElement(q.A,{count:n.length,style:{marginLeft:8}})),key:"components"}),g.createElement(pt,{tab:g.createElement("span",null,g.createElement(J.A,null),"Layouts",g.createElement(q.A,{count:l.length,style:{marginLeft:8}})),key:"layouts"}),g.createElement(pt,{tab:g.createElement("span",null,g.createElement(F.A,null),"App Starters",g.createElement(q.A,{count:m.length,style:{marginLeft:8}})),key:"apps"}))),g.createElement(h.A,{activeKey:Ue,onChange:Ye,className:"templates-tabs"},g.createElement(pt,{tab:g.createElement("span",null,g.createElement(W.A,null)," My Templates"),key:"my"},g.createElement("div",{className:"templates-filters"},g.createElement(b.A,{placeholder:"Search templates",prefix:g.createElement(Z.A,null),value:le,onChange:function(e){return ce(e.target.value)},style:{width:250}}),g.createElement(v.A,{placeholder:"Filter by ".concat("components"===Ke?"component type":"layouts"===Ke?"layout type":"category"),value:"apps"===Ke?fe:se,onChange:"apps"===Ke?Ae:ue,style:{width:200},allowClear:!0,suffixIcon:g.createElement(X.A,null)},"apps"===Ke?Xe.map((function(e){return g.createElement(it,{key:e.value,value:e.value},e.label)})):gt().map((function(e){return g.createElement(it,{key:e,value:e},e)}))),g.createElement(v.A,{placeholder:"Filter by visibility",value:Ee,onChange:ve,style:{width:150},suffixIcon:g.createElement(X.A,null)},g.createElement(it,{value:"all"},"All"),g.createElement(it,{value:"public"},"Public"),g.createElement(it,{value:"private"},"Private"))),g.createElement("div",{className:"templates-list"},g.createElement(j.A,{spinning:ne},yt.length>0?g.createElement(_.A,{grid:{gutter:16,xs:1,sm:1,md:2,lg:2,xl:3,xxl:3},dataSource:yt,renderItem:Et,pagination:{pageSize:9,hideOnSinglePage:!0}}):g.createElement(D.A,{description:g.createElement("span",null,ne?"Loading templates...":"No ".concat(Ke," templates found"))})))),g.createElement(pt,{tab:g.createElement("span",null,g.createElement($.A,null)," Public Templates"),key:"public"},g.createElement("div",{className:"templates-filters"},g.createElement(b.A,{placeholder:"Search templates",prefix:g.createElement(Z.A,null),value:le,onChange:function(e){return ce(e.target.value)},style:{width:250}}),g.createElement(v.A,{placeholder:"Filter by ".concat("components"===Ke?"component type":"layouts"===Ke?"layout type":"category"),value:"apps"===Ke?fe:se,onChange:"apps"===Ke?Ae:ue,style:{width:200},allowClear:!0,suffixIcon:g.createElement(X.A,null)},"apps"===Ke?Xe.map((function(e){return g.createElement(it,{key:e.value,value:e.value},e.label)})):gt().map((function(e){return g.createElement(it,{key:e,value:e},e)})))),g.createElement("div",{className:"templates-list"},g.createElement(j.A,{spinning:ne},yt.length>0?g.createElement(_.A,{grid:{gutter:16,xs:1,sm:1,md:2,lg:2,xl:3,xxl:3},dataSource:yt,renderItem:Et,pagination:{pageSize:9,hideOnSinglePage:!0}}):g.createElement(D.A,{description:g.createElement("span",null,ne?"Loading templates...":"No public ".concat(Ke," templates found"))}))))),g.createElement(L.A,{title:"Create ".concat(At()),open:xe,onCancel:function(){return we(!1)},onOk:function(){return Je.submit()},okText:"Create",confirmLoading:Be,width:800},ht()),g.createElement(L.A,{title:"Edit ".concat("component"===(null==je?void 0:je.template_type)?"Component":"layout"===(null==je?void 0:je.template_type)?"Layout":"App"," Template"),open:Se,onCancel:function(){return Oe(!1)},onOk:function(){return Je.submit()},okText:"Save Changes",confirmLoading:Be,width:800},ht()),g.createElement(L.A,{title:"Import Template",open:Le,onCancel:function(){return Pe(!1)},footer:null,width:600},g.createElement(I.A.Dragger,{name:"file",multiple:!1,accept:".json",beforeUpload:function(e){var t=new FileReader;return t.onload=function(){var e=(0,d.A)(y().mark((function e(t){var n;return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,n=JSON.parse(t.target.result),e.next=4,bt(n);case 4:e.next=9;break;case 6:e.prev=6,e.t0=e.catch(0),w.Ay.error("Invalid JSON file");case 9:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(t){return e.apply(this,arguments)}}(),t.readAsText(e),!1}},g.createElement("p",{className:"ant-upload-drag-icon"},g.createElement(K.A,null)),g.createElement("p",{className:"ant-upload-text"},"Click or drag file to this area to upload"),g.createElement("p",{className:"ant-upload-hint"},"Support for JSON template files only. The template will be imported to the current template type (",Ke,").")))),g.createElement("style",{jsx:"true"},"\n        .templates-page {\n          max-width: 1400px;\n          margin: 0 auto;\n          padding: 0 16px;\n        }\n\n        .templates-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 24px;\n        }\n\n        .template-type-tabs {\n          margin-bottom: 24px;\n        }\n\n        .template-type-tabs .ant-tabs-card > .ant-tabs-content {\n          margin-top: 0;\n        }\n\n        .template-type-tabs .ant-tabs-card > .ant-tabs-content > .ant-tabs-tabpane {\n          background: transparent;\n          border: none;\n        }\n\n        .templates-tabs {\n          background-color: #fff;\n          padding: 16px;\n          border-radius: 8px;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n        }\n\n        .templates-filters {\n          display: flex;\n          gap: 16px;\n          margin-bottom: 24px;\n          flex-wrap: wrap;\n        }\n\n        .templates-list {\n          min-height: 400px;\n        }\n\n        .ant-card-cover img {\n          border-radius: 8px 8px 0 0;\n        }\n\n        .ant-upload-drag {\n          border: 2px dashed #d9d9d9;\n          border-radius: 8px;\n          background: #fafafa;\n          text-align: center;\n          padding: 40px 20px;\n        }\n\n        .ant-upload-drag:hover {\n          border-color: #1890ff;\n        }\n\n        .ant-upload-drag-icon {\n          font-size: 48px;\n          color: #d9d9d9;\n          margin-bottom: 16px;\n        }\n\n        .ant-upload-text {\n          font-size: 16px;\n          color: #666;\n          margin-bottom: 8px;\n        }\n\n        .ant-upload-hint {\n          font-size: 14px;\n          color: #999;\n        }\n      "))}}}]);