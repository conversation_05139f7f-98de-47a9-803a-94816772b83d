"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[772],{7772:(e,t,r)=>{r.r(t),r.d(t,{default:()=>R});var n=r(436),a=r(4467),c=r(467),i=r(5544),o=r(4756),s=r.n(o),l=r(6540),u=r(3016),m=r(7197),f=r(677),p=r(2702),d=r(6914),v=r(6552),g=r(9249),A=r(2652),E=r(1295),h=r(9552),y=r(1005),k=r(8226),w=r(9444),b=r(581),x=r(3598),S=r(562);function C(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return O(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?O(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var c,i=!0,o=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){o=!0,c=e},f:function(){try{i||null==r.return||r.return()}finally{if(o)throw c}}}}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function P(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function N(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?P(Object(r),!0).forEach((function(t){(0,a.A)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var W=u.A.Title,T=u.A.Text,j=u.A.Paragraph;const R=function(){var e=(0,l.useState)({supported:!1,registered:!1,active:!1,installing:!1,waiting:!1,error:null}),t=(0,i.A)(e,2),r=t[0],a=t[1],o=(0,l.useState)({available:!1,caches:[],totalSize:0}),u=(0,i.A)(o,2),O=u[0],P=u[1],R=(0,l.useState)({supported:!1,permission:"default"}),I=(0,i.A)(R,2),D=I[0],L=I[1],U=(0,l.useState)(navigator.onLine),Y=(0,i.A)(U,2),z=Y[0],q=Y[1],B=(0,l.useState)([]),F=(0,i.A)(B,2),M=F[0],$=F[1];(0,l.useEffect)((function(){G(),H(),J();var e=function(){return q(!0)},t=function(){return q(!1)};return window.addEventListener("online",e),window.addEventListener("offline",t),function(){window.removeEventListener("online",e),window.removeEventListener("offline",t)}}),[]);var G=function(){var e=(0,c.A)(s().mark((function e(){var t,r;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!("serviceWorker"in navigator)){e.next=17;break}return e.prev=1,e.next=4,navigator.serviceWorker.getRegistrations();case 4:return t=e.sent,e.next=7,navigator.serviceWorker.getRegistration();case 7:r=e.sent,a({supported:!0,registered:t.length>0,active:r&&null!==r.active,installing:r&&null!==r.installing,waiting:r&&null!==r.waiting,error:null}),r&&r.addEventListener("updatefound",(function(){console.log("Service Worker update found"),G()})),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(1),a((function(t){return N(N({},t),{},{error:e.t0.message})}));case 15:e.next=18;break;case 17:a((function(e){return N(N({},e),{},{supported:!1})}));case 18:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(){return e.apply(this,arguments)}}(),H=function(){var e=(0,c.A)(s().mark((function e(){var t,r,n;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!("caches"in window)){e.next=15;break}return e.prev=1,e.next=4,caches.keys();case 4:return t=e.sent,r=0,e.next=8,Promise.all(t.map(function(){var e=(0,c.A)(s().mark((function e(t){var r,n;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,caches.open(t);case 2:return r=e.sent,e.next=5,r.keys();case 5:return n=e.sent,e.abrupt("return",{name:t,itemCount:n.length,items:n.slice(0,5).map((function(e){return e.url}))});case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()));case 8:n=e.sent,P({available:!0,caches:n,totalSize:r}),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(1),console.error("Error checking cache:",e.t0);case 15:case"end":return e.stop()}}),e,null,[[1,12]])})));return function(){return e.apply(this,arguments)}}(),J=function(){"Notification"in window&&L({supported:!0,permission:Notification.permission})},K=function(){var e=(0,c.A)(s().mark((function e(){var t;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,navigator.serviceWorker.register("/service-worker.js");case 3:t=e.sent,console.log("Service Worker registered:",t),_("Service Worker Registration","success","Successfully registered service worker"),G(),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(0),console.error("Service Worker registration failed:",e.t0),_("Service Worker Registration","error","Registration failed: ".concat(e.t0.message));case 13:case"end":return e.stop()}}),e,null,[[0,9]])})));return function(){return e.apply(this,arguments)}}(),Q=function(){var e=(0,c.A)(s().mark((function e(){var t,r,n,a;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,navigator.serviceWorker.getRegistrations();case 3:t=e.sent,r=C(t),e.prev=5,r.s();case 7:if((n=r.n()).done){e.next=13;break}return a=n.value,e.next=11,a.unregister();case 11:e.next=7;break;case 13:e.next=18;break;case 15:e.prev=15,e.t0=e.catch(5),r.e(e.t0);case 18:return e.prev=18,r.f(),e.finish(18);case 21:_("Service Worker Unregistration","success","All service workers unregistered"),G(),e.next=28;break;case 25:e.prev=25,e.t1=e.catch(0),_("Service Worker Unregistration","error","Unregistration failed: ".concat(e.t1.message));case 28:case"end":return e.stop()}}),e,null,[[0,25],[5,15,18,21]])})));return function(){return e.apply(this,arguments)}}(),V=function(){var e=(0,c.A)(s().mark((function e(){var t;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,caches.keys();case 3:return t=e.sent,e.next=6,Promise.all(t.map((function(e){return caches.delete(e)})));case 6:_("Cache Clear","success","Cleared ".concat(t.length," caches")),H(),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),_("Cache Clear","error","Failed to clear caches: ".concat(e.t0.message));case 13:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(){return e.apply(this,arguments)}}(),X=function(){var e=(0,c.A)(s().mark((function e(){return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/",{cache:"only-if-cached",mode:"same-origin"});case 3:e.sent.ok?_("Offline Test","success","App is available offline"):_("Offline Test","warning","App may not work offline"),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),_("Offline Test","error","Offline capability test failed");case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}(),Z=function(){var e=(0,c.A)(s().mark((function e(){var t;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Notification.requestPermission();case 3:t=e.sent,L((function(e){return N(N({},e),{},{permission:t})})),_("Notification Permission","success","Permission: ".concat(t)),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0),_("Notification Permission","error","Failed: ".concat(e.t0.message));case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(){return e.apply(this,arguments)}}(),_=function(e,t,r){var a={id:Date.now(),test:e,status:t,message:r,timestamp:(new Date).toLocaleTimeString()};$((function(e){return[a].concat((0,n.A)(e.slice(0,9)))}))},ee=function(e){switch(e){case"success":return l.createElement(E.A,{style:{color:"#52c41a"}});case"error":return l.createElement(h.A,{style:{color:"#ff4d4f"}});case"warning":return l.createElement(y.A,{style:{color:"#faad14"}});default:return l.createElement(y.A,null)}};return l.createElement("div",{style:{padding:"24px",maxWidth:"1200px",margin:"0 auto"}},l.createElement(W,{level:2},"Service Worker Testing Dashboard"),l.createElement(j,null,"Test and monitor the service worker functionality, caching, and offline capabilities."),l.createElement(m.A,{message:"Connection Status: ".concat(z?"Online":"Offline"),type:z?"success":"warning",icon:z?l.createElement(k.A,null):l.createElement(w.A,null),style:{marginBottom:"24px"}}),l.createElement("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(400px, 1fr))",gap:"24px"}},l.createElement(f.A,{title:"Service Worker Status",extra:l.createElement(y.A,{onClick:G})},l.createElement(p.A,{direction:"vertical",style:{width:"100%"}},l.createElement("div",null,l.createElement(T,{strong:!0},"Supported: "),r.supported?l.createElement(d.A,{color:"green"},l.createElement(E.A,null)," Yes"):l.createElement(d.A,{color:"red"},l.createElement(h.A,null)," No")),l.createElement("div",null,l.createElement(T,{strong:!0},"Registered: "),r.registered?l.createElement(d.A,{color:"green"},l.createElement(E.A,null)," Yes"):l.createElement(d.A,{color:"orange"},l.createElement(h.A,null)," No")),l.createElement("div",null,l.createElement(T,{strong:!0},"Active: "),r.active?l.createElement(d.A,{color:"green"},l.createElement(E.A,null)," Yes"):l.createElement(d.A,{color:"orange"},l.createElement(h.A,null)," No")),r.error&&l.createElement(m.A,{message:r.error,type:"error",size:"small"}),l.createElement(v.A,null),l.createElement(p.A,null,l.createElement(g.Ay,{type:"primary",onClick:K,disabled:!r.supported||r.registered},"Register SW"),l.createElement(g.Ay,{onClick:Q,disabled:!r.supported||!r.registered},"Unregister SW")))),l.createElement(f.A,{title:"Cache Status",extra:l.createElement(b.A,{onClick:H})},l.createElement(p.A,{direction:"vertical",style:{width:"100%"}},l.createElement("div",null,l.createElement(T,{strong:!0},"Cache API Available: "),O.available?l.createElement(d.A,{color:"green"},l.createElement(E.A,null)," Yes"):l.createElement(d.A,{color:"red"},l.createElement(h.A,null)," No")),l.createElement("div",null,l.createElement(T,{strong:!0},"Active Caches: "),l.createElement(d.A,{color:"blue"},O.caches.length)),O.caches.length>0&&l.createElement(A.A,{size:"small",dataSource:O.caches,renderItem:function(e){return l.createElement(A.A.Item,null,l.createElement("div",{style:{width:"100%"}},l.createElement(T,{strong:!0},e.name),l.createElement("br",null),l.createElement(T,{type:"secondary"},e.itemCount," items")))}}),l.createElement(v.A,null),l.createElement(p.A,null,l.createElement(g.Ay,{icon:l.createElement(x.A,null),onClick:V,disabled:0===O.caches.length},"Clear All Caches"),l.createElement(g.Ay,{onClick:X},"Test Offline")))),l.createElement(f.A,{title:"Notification Status"},l.createElement(p.A,{direction:"vertical",style:{width:"100%"}},l.createElement("div",null,l.createElement(T,{strong:!0},"Supported: "),D.supported?l.createElement(d.A,{color:"green"},l.createElement(E.A,null)," Yes"):l.createElement(d.A,{color:"red"},l.createElement(h.A,null)," No")),l.createElement("div",null,l.createElement(T,{strong:!0},"Permission: "),l.createElement(d.A,{color:"granted"===D.permission?"green":"orange"},D.permission)),l.createElement(v.A,null),l.createElement(p.A,null,l.createElement(g.Ay,{icon:l.createElement(S.A,null),onClick:Z,disabled:!D.supported||"granted"===D.permission},"Request Permission"),l.createElement(g.Ay,{onClick:function(){"granted"===Notification.permission?(new Notification("Test Notification",{body:"This is a test notification from the App Builder",icon:"/logo192.png",badge:"/favicon.ico"}),_("Push Notification","success","Test notification sent")):_("Push Notification","error","Notification permission not granted")},disabled:"granted"!==D.permission},"Test Notification")))),l.createElement(f.A,{title:"Test Results",style:{gridColumn:"span 2"}},0===M.length?l.createElement(T,{type:"secondary"},"No tests run yet. Click the buttons above to start testing."):l.createElement(A.A,{dataSource:M,renderItem:function(e){return l.createElement(A.A.Item,null,l.createElement(A.A.Item.Meta,{avatar:ee(e.status),title:e.test,description:"".concat(e.message," - ").concat(e.timestamp)}))}}))))}}}]);