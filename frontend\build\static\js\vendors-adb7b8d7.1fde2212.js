"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[2382],{60551:(e,t,n)=>{n.d(t,{A:()=>_});var r=n(58168),o=n(82284),i=n(89379),u=n(64467),c=n(5544),a=n(53986),l=n(46942),s=n.n(l),f=n(26076),v=n(81470),d=n(30981),h=n(96540),m=n(40961),g=h.forwardRef((function(e,t){var n=e.height,o=e.offsetY,c=e.offsetX,a=e.children,l=e.prefixCls,v=e.onInnerResize,d=e.innerProps,m=e.rtl,g=e.extra,p={},A={display:"flex",flexDirection:"column"};return void 0!==o&&(p={height:n,position:"relative",overflow:"hidden"},A=(0,i.A)((0,i.A)({},A),{},(0,u.A)((0,u.A)((0,u.A)((0,u.A)((0,u.A)({transform:"translateY(".concat(o,"px)")},m?"marginRight":"marginLeft",-c),"position","absolute"),"left",0),"right",0),"top",0))),h.createElement("div",{style:p},h.createElement(f.A,{onResize:function(e){e.offsetHeight&&v&&v()}},h.createElement("div",(0,r.A)({style:A,className:s()((0,u.A)({},"".concat(l,"-holder-inner"),l)),ref:t},d),a,g)))}));g.displayName="Filler";const p=g;function A(e){var t=e.children,n=e.setRef,r=h.useCallback((function(e){n(e)}),[]);return h.cloneElement(t,{ref:r})}var w=n(25371);const R="object"===("undefined"==typeof navigator?"undefined":(0,o.A)(navigator))&&/Firefox/i.test(navigator.userAgent),E=function(e,t,n,r){var o=(0,h.useRef)(!1),i=(0,h.useRef)(null),u=(0,h.useRef)({top:e,bottom:t,left:n,right:r});return u.current.top=e,u.current.bottom=t,u.current.left=n,u.current.right=r,function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=e?t<0&&u.current.left||t>0&&u.current.right:t<0&&u.current.top||t>0&&u.current.bottom;return n&&r?(clearTimeout(i.current),o.current=!1):r&&!o.current||(clearTimeout(i.current),o.current=!0,i.current=setTimeout((function(){o.current=!1}),50)),!o.current&&r}};var M=n(23029),S=n(92901);const b=function(){function e(){(0,M.A)(this,e),(0,u.A)(this,"maps",void 0),(0,u.A)(this,"id",0),(0,u.A)(this,"diffRecords",new Map),this.maps=Object.create(null)}return(0,S.A)(e,[{key:"set",value:function(e,t){this.diffRecords.set(e,this.maps[e]),this.maps[e]=t,this.id+=1}},{key:"get",value:function(e){return this.maps[e]}},{key:"resetRecord",value:function(){this.diffRecords.clear()}},{key:"getRecord",value:function(){return this.diffRecords}}]),e}();function y(e){var t=parseFloat(e);return isNaN(t)?0:t}var x=14/15;function L(e){return Math.floor(Math.pow(e,.5))}function H(e,t){return("touches"in e?e.touches[0]:e)[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}const T=h.forwardRef((function(e,t){var n=e.prefixCls,r=e.rtl,o=e.scrollOffset,a=e.scrollRange,l=e.onStartMove,f=e.onStopMove,v=e.onScroll,d=e.horizontal,m=e.spinSize,g=e.containerSize,p=e.style,A=e.thumbStyle,R=e.showScrollBar,E=h.useState(!1),M=(0,c.A)(E,2),S=M[0],b=M[1],y=h.useState(null),x=(0,c.A)(y,2),L=x[0],T=x[1],k=h.useState(null),z=(0,c.A)(k,2),D=z[0],C=z[1],N=!r,Y=h.useRef(),_=h.useRef(),B=h.useState(R),I=(0,c.A)(B,2),P=I[0],X=I[1],O=h.useRef(),j=function(){!0!==R&&!1!==R&&(clearTimeout(O.current),X(!0),O.current=setTimeout((function(){X(!1)}),3e3))},K=a-g||0,V=g-m||0,W=h.useMemo((function(){return 0===o||0===K?0:o/K*V}),[o,K,V]),F=h.useRef({top:W,dragging:S,pageY:L,startTop:D});F.current={top:W,dragging:S,pageY:L,startTop:D};var q=function(e){b(!0),T(H(e,d)),C(F.current.top),l(),e.stopPropagation(),e.preventDefault()};h.useEffect((function(){var e=function(e){e.preventDefault()},t=Y.current,n=_.current;return t.addEventListener("touchstart",e,{passive:!1}),n.addEventListener("touchstart",q,{passive:!1}),function(){t.removeEventListener("touchstart",e),n.removeEventListener("touchstart",q)}}),[]);var G=h.useRef();G.current=K;var J=h.useRef();J.current=V,h.useEffect((function(){if(S){var e,t=function(t){var n=F.current,r=n.dragging,o=n.pageY,i=n.startTop;w.A.cancel(e);var u=Y.current.getBoundingClientRect(),c=g/(d?u.width:u.height);if(r){var a=(H(t,d)-o)*c,l=i;!N&&d?l-=a:l+=a;var s=G.current,f=J.current,h=f?l/f:0,m=Math.ceil(h*s);m=Math.max(m,0),m=Math.min(m,s),e=(0,w.A)((function(){v(m,d)}))}},n=function(){b(!1),f()};return window.addEventListener("mousemove",t,{passive:!0}),window.addEventListener("touchmove",t,{passive:!0}),window.addEventListener("mouseup",n,{passive:!0}),window.addEventListener("touchend",n,{passive:!0}),function(){window.removeEventListener("mousemove",t),window.removeEventListener("touchmove",t),window.removeEventListener("mouseup",n),window.removeEventListener("touchend",n),w.A.cancel(e)}}}),[S]),h.useEffect((function(){return j(),function(){clearTimeout(O.current)}}),[o]),h.useImperativeHandle(t,(function(){return{delayHidden:j}}));var Q="".concat(n,"-scrollbar"),U={position:"absolute",visibility:P?null:"hidden"},Z={position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:99,cursor:"pointer",userSelect:"none"};return d?(U.height=8,U.left=0,U.right=0,U.bottom=0,Z.height="100%",Z.width=m,N?Z.left=W:Z.right=W):(U.width=8,U.top=0,U.bottom=0,N?U.right=0:U.left=0,Z.width="100%",Z.height=m,Z.top=W),h.createElement("div",{ref:Y,className:s()(Q,(0,u.A)((0,u.A)((0,u.A)({},"".concat(Q,"-horizontal"),d),"".concat(Q,"-vertical"),!d),"".concat(Q,"-visible"),P)),style:(0,i.A)((0,i.A)({},U),p),onMouseDown:function(e){e.stopPropagation(),e.preventDefault()},onMouseMove:j},h.createElement("div",{ref:_,className:s()("".concat(Q,"-thumb"),(0,u.A)({},"".concat(Q,"-thumb-moving"),S)),style:(0,i.A)((0,i.A)({},Z),A),onMouseDown:q}))}));function k(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=e/(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)*e;return isNaN(t)&&(t=0),t=Math.max(t,20),Math.floor(t)}var z=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],D=[],C={overflowY:"auto",overflowAnchor:"none"};function N(e,t){var n=e.prefixCls,l=void 0===n?"rc-virtual-list":n,g=e.className,M=e.height,S=e.itemHeight,N=e.fullHeight,Y=void 0===N||N,_=e.style,B=e.data,I=e.children,P=e.itemKey,X=e.virtual,O=e.direction,j=e.scrollWidth,K=e.component,V=void 0===K?"div":K,W=e.onScroll,F=e.onVirtualScroll,q=e.onVisibleChange,G=e.innerProps,J=e.extraRender,Q=e.styles,U=e.showScrollBar,Z=void 0===U?"optional":U,$=(0,a.A)(e,z),ee=h.useCallback((function(e){return"function"==typeof P?P(e):null==e?void 0:e[P]}),[P]),te=function(e){var t=h.useState(0),n=(0,c.A)(t,2),r=n[0],o=n[1],i=(0,h.useRef)(new Map),u=(0,h.useRef)(new b),a=(0,h.useRef)(0);function l(){a.current+=1}function s(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];l();var t=function(){var e=!1;i.current.forEach((function(t,n){if(t&&t.offsetParent){var r=t.offsetHeight,o=getComputedStyle(t),i=o.marginTop,c=o.marginBottom,a=r+y(i)+y(c);u.current.get(n)!==a&&(u.current.set(n,a),e=!0)}})),e&&o((function(e){return e+1}))};if(e)t();else{a.current+=1;var n=a.current;Promise.resolve().then((function(){n===a.current&&t()}))}}return(0,h.useEffect)((function(){return l}),[]),[function(t,n){var r=e(t);i.current.get(r);n?(i.current.set(r,n),s()):i.current.delete(r)},s,u.current,r]}(ee),ne=(0,c.A)(te,4),re=ne[0],oe=ne[1],ie=ne[2],ue=ne[3],ce=!(!1===X||!M||!S),ae=h.useMemo((function(){return Object.values(ie.maps).reduce((function(e,t){return e+t}),0)}),[ie.id,ie.maps]),le=ce&&B&&(Math.max(S*B.length,ae)>M||!!j),se="rtl"===O,fe=s()(l,(0,u.A)({},"".concat(l,"-rtl"),se),g),ve=B||D,de=(0,h.useRef)(),he=(0,h.useRef)(),me=(0,h.useRef)(),ge=(0,h.useState)(0),pe=(0,c.A)(ge,2),Ae=pe[0],we=pe[1],Re=(0,h.useState)(0),Ee=(0,c.A)(Re,2),Me=Ee[0],Se=Ee[1],be=(0,h.useState)(!1),ye=(0,c.A)(be,2),xe=ye[0],Le=ye[1],He=function(){Le(!0)},Te=function(){Le(!1)},ke={getKey:ee};function ze(e){we((function(t){var n=function(e){var t=e;return Number.isNaN(Qe.current)||(t=Math.min(t,Qe.current)),t=Math.max(t,0)}("function"==typeof e?e(t):e);return de.current.scrollTop=n,n}))}var De=(0,h.useRef)({start:0,end:ve.length}),Ce=(0,h.useRef)(),Ne=function(e,t,n){var r=h.useState(e),o=(0,c.A)(r,2),i=o[0],u=o[1],a=h.useState(null),l=(0,c.A)(a,2),s=l[0],f=l[1];return h.useEffect((function(){var r=function(e,t,n){var r,o,i=e.length,u=t.length;if(0===i&&0===u)return null;i<u?(r=e,o=t):(r=t,o=e);var c={__EMPTY_ITEM__:!0};function a(e){return void 0!==e?n(e):c}for(var l=null,s=1!==Math.abs(i-u),f=0;f<o.length;f+=1){var v=a(r[f]);if(v!==a(o[f])){l=f,s=s||v!==a(o[f+1]);break}}return null===l?null:{index:l,multiple:s}}(i||[],e||[],t);void 0!==(null==r?void 0:r.index)&&(null==n||n(r.index),f(e[r.index])),u(e)}),[e]),[s]}(ve,ee),Ye=(0,c.A)(Ne,1)[0];Ce.current=Ye;var _e=h.useMemo((function(){if(!ce)return{scrollHeight:void 0,start:0,end:ve.length-1,offset:void 0};var e;if(!le)return{scrollHeight:(null===(e=he.current)||void 0===e?void 0:e.offsetHeight)||0,start:0,end:ve.length-1,offset:void 0};for(var t,n,r,o=0,i=ve.length,u=0;u<i;u+=1){var c=ve[u],a=ee(c),l=ie.get(a),s=o+(void 0===l?S:l);s>=Ae&&void 0===t&&(t=u,n=o),s>Ae+M&&void 0===r&&(r=u),o=s}return void 0===t&&(t=0,n=0,r=Math.ceil(M/S)),void 0===r&&(r=ve.length-1),{scrollHeight:o,start:t,end:r=Math.min(r+1,ve.length-1),offset:n}}),[le,ce,Ae,ve,ue,M]),Be=_e.scrollHeight,Ie=_e.start,Pe=_e.end,Xe=_e.offset;De.current.start=Ie,De.current.end=Pe,h.useLayoutEffect((function(){var e=ie.getRecord();if(1===e.size){var t=Array.from(e.keys())[0],n=e.get(t),r=ve[Ie];if(r&&void 0===n&&ee(r)===t){var o=ie.get(t)-S;ze((function(e){return e+o}))}}ie.resetRecord()}),[Be]);var Oe=h.useState({width:0,height:M}),je=(0,c.A)(Oe,2),Ke=je[0],Ve=je[1],We=(0,h.useRef)(),Fe=(0,h.useRef)(),qe=h.useMemo((function(){return k(Ke.width,j)}),[Ke.width,j]),Ge=h.useMemo((function(){return k(Ke.height,Be)}),[Ke.height,Be]),Je=Be-M,Qe=(0,h.useRef)(Je);Qe.current=Je;var Ue=Ae<=0,Ze=Ae>=Je,$e=Me<=0,et=Me>=j,tt=E(Ue,Ze,$e,et),nt=function(){return{x:se?-Me:Me,y:Ae}},rt=(0,h.useRef)(nt()),ot=(0,v._q)((function(e){if(F){var t=(0,i.A)((0,i.A)({},nt()),e);rt.current.x===t.x&&rt.current.y===t.y||(F(t),rt.current=t)}}));function it(e,t){var n=e;t?((0,m.flushSync)((function(){Se(n)})),ot()):ze(n)}var ut=function(e){var t=e,n=j?j-Ke.width:0;return t=Math.max(t,0),Math.min(t,n)},ct=(0,v._q)((function(e,t){t?((0,m.flushSync)((function(){Se((function(t){return ut(t+(se?-e:e))}))})),ot()):ze((function(t){return t+e}))})),at=function(e,t,n,r,o,i,u){var c=(0,h.useRef)(0),a=(0,h.useRef)(null),l=(0,h.useRef)(null),s=(0,h.useRef)(!1),f=E(t,n,r,o),v=(0,h.useRef)(null),d=(0,h.useRef)(null);return[function(t){if(e){w.A.cancel(d.current),d.current=(0,w.A)((function(){v.current=null}),2);var n=t.deltaX,r=t.deltaY,o=t.shiftKey,h=n,m=r;("sx"===v.current||!v.current&&o&&r&&!n)&&(h=r,m=0,v.current="sx");var g=Math.abs(h),p=Math.abs(m);null===v.current&&(v.current=i&&g>p?"x":"y"),"y"===v.current?function(e,t){if(w.A.cancel(a.current),!f(!1,t)){var n=e;n._virtualHandled||(n._virtualHandled=!0,c.current+=t,l.current=t,R||n.preventDefault(),a.current=(0,w.A)((function(){var e=s.current?10:1;u(c.current*e,!1),c.current=0})))}}(t,m):function(e,t){u(t,!0),R||e.preventDefault()}(t,h)}},function(t){e&&(s.current=t.detail===l.current)}]}(ce,Ue,Ze,$e,et,!!j,ct),lt=(0,c.A)(at,2),st=lt[0],ft=lt[1];!function(e,t,n){var r,o=(0,h.useRef)(!1),i=(0,h.useRef)(0),u=(0,h.useRef)(0),c=(0,h.useRef)(null),a=(0,h.useRef)(null),l=function(e){if(o.current){var t=Math.ceil(e.touches[0].pageX),r=Math.ceil(e.touches[0].pageY),c=i.current-t,l=u.current-r,s=Math.abs(c)>Math.abs(l);s?i.current=t:u.current=r;var f=n(s,s?c:l,!1,e);f&&e.preventDefault(),clearInterval(a.current),f&&(a.current=setInterval((function(){s?c*=x:l*=x;var e=Math.floor(s?c:l);(!n(s,e,!0)||Math.abs(e)<=.1)&&clearInterval(a.current)}),16))}},s=function(){o.current=!1,r()},f=function(e){r(),1!==e.touches.length||o.current||(o.current=!0,i.current=Math.ceil(e.touches[0].pageX),u.current=Math.ceil(e.touches[0].pageY),c.current=e.target,c.current.addEventListener("touchmove",l,{passive:!1}),c.current.addEventListener("touchend",s,{passive:!0}))};r=function(){c.current&&(c.current.removeEventListener("touchmove",l),c.current.removeEventListener("touchend",s))},(0,d.A)((function(){return e&&t.current.addEventListener("touchstart",f,{passive:!0}),function(){var e;null===(e=t.current)||void 0===e||e.removeEventListener("touchstart",f),r(),clearInterval(a.current)}}),[e])}(ce,de,(function(e,t,n,r){var o=r;return!(tt(e,t,n)||o&&o._virtualHandled||(o&&(o._virtualHandled=!0),st({preventDefault:function(){},deltaX:e?t:0,deltaY:e?0:t}),0))})),function(e,t){h.useEffect((function(){var n=t.current;if(e&&n){var r,o,i=!1,u=function(){w.A.cancel(r)},c=function e(){u(),r=(0,w.A)((function(){var t;t=o,ze((function(e){return e+t})),e()}))},a=function(e){if(!e.target.draggable&&0===e.button){var t=e;t._virtualHandled||(t._virtualHandled=!0,i=!0)}},l=function(){i=!1,u()},s=function(e){if(i){var t=H(e,!1),r=n.getBoundingClientRect(),a=r.top,l=r.bottom;t<=a?(o=-L(a-t),c()):t>=l?(o=L(t-l),c()):u()}};return n.addEventListener("mousedown",a),n.ownerDocument.addEventListener("mouseup",l),n.ownerDocument.addEventListener("mousemove",s),function(){n.removeEventListener("mousedown",a),n.ownerDocument.removeEventListener("mouseup",l),n.ownerDocument.removeEventListener("mousemove",s),u()}}}),[e])}(le,de),(0,d.A)((function(){function e(e){var t=Ue&&e.detail<0,n=Ze&&e.detail>0;!ce||t||n||e.preventDefault()}var t=de.current;return t.addEventListener("wheel",st,{passive:!1}),t.addEventListener("DOMMouseScroll",ft,{passive:!0}),t.addEventListener("MozMousePixelScroll",e,{passive:!1}),function(){t.removeEventListener("wheel",st),t.removeEventListener("DOMMouseScroll",ft),t.removeEventListener("MozMousePixelScroll",e)}}),[ce,Ue,Ze]),(0,d.A)((function(){if(j){var e=ut(Me);Se(e),ot({x:e})}}),[Ke.width,j]);var vt=function(){var e,t;null===(e=We.current)||void 0===e||e.delayHidden(),null===(t=Fe.current)||void 0===t||t.delayHidden()},dt=function(e,t,n,r,u,a,l,s){var f=h.useRef(),v=h.useState(null),m=(0,c.A)(v,2),g=m[0],p=m[1];return(0,d.A)((function(){if(g&&g.times<10){if(!e.current)return void p((function(e){return(0,i.A)({},e)}));a();var o=g.targetAlign,c=g.originAlign,s=g.index,f=g.offset,v=e.current.clientHeight,d=!1,h=o,m=null;if(v){for(var A=o||c,w=0,R=0,E=0,M=Math.min(t.length-1,s),S=0;S<=M;S+=1){var b=u(t[S]);R=w;var y=n.get(b);w=E=R+(void 0===y?r:y)}for(var x="top"===A?f:v-f,L=M;L>=0;L-=1){var H=u(t[L]),T=n.get(H);if(void 0===T){d=!0;break}if((x-=T)<=0)break}switch(A){case"top":m=R-f;break;case"bottom":m=E-v+f;break;default:var k=e.current.scrollTop;R<k?h="top":E>k+v&&(h="bottom")}null!==m&&l(m),m!==g.lastTop&&(d=!0)}d&&p((0,i.A)((0,i.A)({},g),{},{times:g.times+1,targetAlign:h,lastTop:m}))}}),[g,e.current]),function(e){if(null!=e){if(w.A.cancel(f.current),"number"==typeof e)l(e);else if(e&&"object"===(0,o.A)(e)){var n,r=e.align;n="index"in e?e.index:t.findIndex((function(t){return u(t)===e.key}));var i=e.offset;p({times:0,index:n,offset:void 0===i?0:i,originAlign:r})}}else s()}}(de,ve,ie,S,ee,(function(){return oe(!0)}),ze,vt);h.useImperativeHandle(t,(function(){return{nativeElement:me.current,getScrollInfo:nt,scrollTo:function(e){var t;(t=e)&&"object"===(0,o.A)(t)&&("left"in t||"top"in t)?(void 0!==e.left&&Se(ut(e.left)),dt(e.top)):dt(e)}}})),(0,d.A)((function(){if(q){var e=ve.slice(Ie,Pe+1);q(e,ve)}}),[Ie,Pe,ve]);var ht=function(e,t,n,r){var o=h.useMemo((function(){return[new Map,[]]}),[e,n.id,r]),i=(0,c.A)(o,2),u=i[0],a=i[1];return function(o){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o,c=u.get(o),l=u.get(i);if(void 0===c||void 0===l)for(var s=e.length,f=a.length;f<s;f+=1){var v,d=e[f],h=t(d);u.set(h,f);var m=null!==(v=n.get(h))&&void 0!==v?v:r;if(a[f]=(a[f-1]||0)+m,h===o&&(c=f),h===i&&(l=f),void 0!==c&&void 0!==l)break}return{top:a[c-1]||0,bottom:a[l]}}}(ve,ee,ie,S),mt=null==J?void 0:J({start:Ie,end:Pe,virtual:le,offsetX:Me,offsetY:Xe,rtl:se,getSize:ht}),gt=function(e,t,n,r,o,i,u,c){var a=c.getKey;return e.slice(t,n+1).map((function(e,n){var c=u(e,t+n,{style:{width:r},offsetX:o}),l=a(e);return h.createElement(A,{key:l,setRef:function(t){return i(e,t)}},c)}))}(ve,Ie,Pe,j,Me,re,I,ke),pt=null;M&&(pt=(0,i.A)((0,u.A)({},Y?"height":"maxHeight",M),C),ce&&(pt.overflowY="hidden",j&&(pt.overflowX="hidden"),xe&&(pt.pointerEvents="none")));var At={};return se&&(At.dir="rtl"),h.createElement("div",(0,r.A)({ref:me,style:(0,i.A)((0,i.A)({},_),{},{position:"relative"}),className:fe},At,$),h.createElement(f.A,{onResize:function(e){Ve({width:e.offsetWidth,height:e.offsetHeight})}},h.createElement(V,{className:"".concat(l,"-holder"),style:pt,ref:de,onScroll:function(e){var t=e.currentTarget.scrollTop;t!==Ae&&ze(t),null==W||W(e),ot()},onMouseEnter:vt},h.createElement(p,{prefixCls:l,height:Be,offsetX:Me,offsetY:Xe,scrollWidth:j,onInnerResize:oe,ref:he,innerProps:G,rtl:se,extra:mt},gt))),le&&Be>M&&h.createElement(T,{ref:We,prefixCls:l,scrollOffset:Ae,scrollRange:Be,rtl:se,onScroll:it,onStartMove:He,onStopMove:Te,spinSize:Ge,containerSize:Ke.height,style:null==Q?void 0:Q.verticalScrollBar,thumbStyle:null==Q?void 0:Q.verticalScrollBarThumb,showScrollBar:Z}),le&&j>Ke.width&&h.createElement(T,{ref:Fe,prefixCls:l,scrollOffset:Me,scrollRange:j,rtl:se,onScroll:it,onStartMove:He,onStopMove:Te,spinSize:qe,containerSize:Ke.width,horizontal:!0,style:null==Q?void 0:Q.horizontalScrollBar,thumbStyle:null==Q?void 0:Q.horizontalScrollBarThumb,showScrollBar:Z}))}var Y=h.forwardRef(N);Y.displayName="List";const _=Y}}]);