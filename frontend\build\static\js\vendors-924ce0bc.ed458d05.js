"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[1437],{1397:(e,n,t)=>{t.d(n,{g3:()=>ee,JM:()=>te,c$:()=>re,Ay:()=>we,Vm:()=>y});var o=t(58168),r=t(60436),a=t(64467),l=t(89379),i=t(5544),u=t(53986),c=t(82284),s=t(12533),d=t(68210),f=t(96540),v=t(46942),p=t.n(v),m=t(30981),h=t(68430),g=t(8719);const A=function(e){var n=e.className,t=e.customizeIcon,o=e.customizeIconProps,r=e.children,a=e.onMouseDown,l=e.onClick,i="function"==typeof t?t(o):t;return f.createElement("span",{className:n,onMouseDown:function(e){e.preventDefault(),null==a||a(e)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:l,"aria-hidden":!0},void 0!==i?i:f.createElement("span",{className:p()(n.split(/\s+/).map((function(e){return"".concat(e,"-icon")})))},r))};var b=f.createContext(null);function y(){return f.useContext(b)}function C(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:250,n=f.useRef(null),t=f.useRef(null);return f.useEffect((function(){return function(){window.clearTimeout(t.current)}}),[]),[function(){return n.current},function(o){(o||null===n.current)&&(n.current=o),window.clearTimeout(t.current),t.current=window.setTimeout((function(){n.current=null}),e)}]}var E=t(16928),w=t(72065),x=t(99591),S=t(69998),k=["prefixCls","id","inputElement","autoFocus","autoComplete","editable","activeDescendantId","value","open","attrs"],N=function(e,n){var t=e.prefixCls,o=e.id,r=e.inputElement,a=e.autoFocus,i=e.autoComplete,c=e.editable,s=e.activeDescendantId,v=e.value,m=e.open,h=e.attrs,A=(0,u.A)(e,k),b=r||f.createElement("input",null),y=b,C=y.ref,E=y.props;return(0,d.$e)(!("maxLength"in b.props),"Passing 'maxLength' to input element directly may not work because input in BaseSelect is controlled."),f.cloneElement(b,(0,l.A)((0,l.A)((0,l.A)({type:"search"},(0,S.A)(A,E,!0)),{},{id:o,ref:(0,g.K4)(n,C),autoComplete:i||"off",autoFocus:a,className:p()("".concat(t,"-selection-search-input"),null==E?void 0:E.className),role:"combobox","aria-expanded":m||!1,"aria-haspopup":"listbox","aria-owns":"".concat(o,"_list"),"aria-autocomplete":"list","aria-controls":"".concat(o,"_list"),"aria-activedescendant":m?s:void 0},h),{},{value:c?v:"",readOnly:!c,unselectable:c?null:"on",style:(0,l.A)((0,l.A)({},E.style),{},{opacity:c?null:0})}))};const M=f.forwardRef(N);function I(e){return Array.isArray(e)?e:void 0!==e?[e]:[]}var R="undefined"!=typeof window&&window.document&&window.document.documentElement;function D(e){return["string","number"].includes((0,c.A)(e))}function P(e){var n=void 0;return e&&(D(e.title)?n=e.title.toString():D(e.label)&&(n=e.label.toString())),n}function T(e){var n;return null!==(n=e.key)&&void 0!==n?n:e.value}var V=function(e){e.preventDefault(),e.stopPropagation()};const O=function(e){var n,t,o=e.id,r=e.prefixCls,l=e.values,u=e.open,c=e.searchValue,s=e.autoClearSearchValue,d=e.inputRef,v=e.placeholder,m=e.disabled,h=e.mode,g=e.showSearch,b=e.autoFocus,y=e.autoComplete,C=e.activeDescendantId,E=e.tabIndex,S=e.removeIcon,k=e.maxTagCount,N=e.maxTagTextLength,I=e.maxTagPlaceholder,D=void 0===I?function(e){return"+ ".concat(e.length," ...")}:I,O=e.tagRender,F=e.onToggleOpen,L=e.onRemove,H=e.onInputChange,K=e.onInputPaste,W=e.onInputKeyDown,B=e.onInputMouseDown,z=e.onInputCompositionStart,_=e.onInputCompositionEnd,U=e.onInputBlur,j=f.useRef(null),Y=(0,f.useState)(0),X=(0,i.A)(Y,2),G=X[0],q=X[1],$=(0,f.useState)(!1),J=(0,i.A)($,2),Q=J[0],Z=J[1],ee="".concat(r,"-selection"),ne=u||"multiple"===h&&!1===s||"tags"===h?c:"",te="tags"===h||"multiple"===h&&!1===s||g&&(u||Q);n=function(){q(j.current.scrollWidth)},t=[ne],R?f.useLayoutEffect(n,t):f.useEffect(n,t);var oe=function(e,n,t,o,r){return f.createElement("span",{title:P(e),className:p()("".concat(ee,"-item"),(0,a.A)({},"".concat(ee,"-item-disabled"),t))},f.createElement("span",{className:"".concat(ee,"-item-content")},n),o&&f.createElement(A,{className:"".concat(ee,"-item-remove"),onMouseDown:V,onClick:r,customizeIcon:S},"×"))},re=function(e,n,t,o,r,a){return f.createElement("span",{onMouseDown:function(e){V(e),F(!u)}},O({label:n,value:e,disabled:t,closable:o,onClose:r,isMaxTag:!!a}))},ae=f.createElement("div",{className:"".concat(ee,"-search"),style:{width:G},onFocus:function(){Z(!0)},onBlur:function(){Z(!1)}},f.createElement(M,{ref:d,open:u,prefixCls:r,id:o,inputElement:null,disabled:m,autoFocus:b,autoComplete:y,editable:te,activeDescendantId:C,value:ne,onKeyDown:W,onMouseDown:B,onChange:H,onPaste:K,onCompositionStart:z,onCompositionEnd:_,onBlur:U,tabIndex:E,attrs:(0,w.A)(e,!0)}),f.createElement("span",{ref:j,className:"".concat(ee,"-search-mirror"),"aria-hidden":!0},ne," ")),le=f.createElement(x.A,{prefixCls:"".concat(ee,"-overflow"),data:l,renderItem:function(e){var n=e.disabled,t=e.label,o=e.value,r=!m&&!n,a=t;if("number"==typeof N&&("string"==typeof t||"number"==typeof t)){var l=String(a);l.length>N&&(a="".concat(l.slice(0,N),"..."))}var i=function(n){n&&n.stopPropagation(),L(e)};return"function"==typeof O?re(o,a,n,r,i):oe(e,a,n,r,i)},renderRest:function(e){if(!l.length)return null;var n="function"==typeof D?D(e):D;return"function"==typeof O?re(void 0,n,!1,!1,void 0,!0):oe({title:n},n,!1)},suffix:ae,itemKey:T,maxCount:k});return f.createElement("span",{className:"".concat(ee,"-wrap")},le,!l.length&&!ne&&f.createElement("span",{className:"".concat(ee,"-placeholder")},v))},F=function(e){var n=e.inputElement,t=e.prefixCls,o=e.id,r=e.inputRef,a=e.disabled,l=e.autoFocus,u=e.autoComplete,c=e.activeDescendantId,s=e.mode,d=e.open,v=e.values,p=e.placeholder,m=e.tabIndex,h=e.showSearch,g=e.searchValue,A=e.activeValue,b=e.maxLength,y=e.onInputKeyDown,C=e.onInputMouseDown,E=e.onInputChange,x=e.onInputPaste,S=e.onInputCompositionStart,k=e.onInputCompositionEnd,N=e.onInputBlur,I=e.title,R=f.useState(!1),D=(0,i.A)(R,2),T=D[0],V=D[1],O="combobox"===s,F=O||h,L=v[0],H=g||"";O&&A&&!T&&(H=A),f.useEffect((function(){O&&V(!1)}),[O,A]);var K=!("combobox"!==s&&!d&&!h||!H),W=void 0===I?P(L):I,B=f.useMemo((function(){return L?null:f.createElement("span",{className:"".concat(t,"-selection-placeholder"),style:K?{visibility:"hidden"}:void 0},p)}),[L,K,p,t]);return f.createElement("span",{className:"".concat(t,"-selection-wrap")},f.createElement("span",{className:"".concat(t,"-selection-search")},f.createElement(M,{ref:r,prefixCls:t,id:o,open:d,inputElement:n,disabled:a,autoFocus:l,autoComplete:u,editable:F,activeDescendantId:c,value:H,onKeyDown:y,onMouseDown:C,onChange:function(e){V(!0),E(e)},onPaste:x,onCompositionStart:S,onCompositionEnd:k,onBlur:N,tabIndex:m,attrs:(0,w.A)(e,!0),maxLength:O?b:void 0})),!O&&L?f.createElement("span",{className:"".concat(t,"-selection-item"),title:W,style:K?{visibility:"hidden"}:void 0},L.label):null,B)};var L=function(e,n){var t=(0,f.useRef)(null),r=(0,f.useRef)(!1),a=e.prefixCls,l=e.open,u=e.mode,c=e.showSearch,s=e.tokenWithEnter,d=e.disabled,v=e.prefix,p=e.autoClearSearchValue,m=e.onSearch,h=e.onSearchSubmit,g=e.onToggleOpen,A=e.onInputKeyDown,b=e.onInputBlur,y=e.domRef;f.useImperativeHandle(n,(function(){return{focus:function(e){t.current.focus(e)},blur:function(){t.current.blur()}}}));var w=C(0),x=(0,i.A)(w,2),S=x[0],k=x[1],N=(0,f.useRef)(null),M=function(e){!1!==m(e,!0,r.current)&&g(!0)},I={inputRef:t,onInputKeyDown:function(e){var n,o=e.which,a=t.current instanceof HTMLTextAreaElement;a||!l||o!==E.A.UP&&o!==E.A.DOWN||e.preventDefault(),A&&A(e),o!==E.A.ENTER||"tags"!==u||r.current||l||null==h||h(e.target.value),a&&!l&&~[E.A.UP,E.A.DOWN,E.A.LEFT,E.A.RIGHT].indexOf(o)||(n=o)&&![E.A.ESC,E.A.SHIFT,E.A.BACKSPACE,E.A.TAB,E.A.WIN_KEY,E.A.ALT,E.A.META,E.A.WIN_KEY_RIGHT,E.A.CTRL,E.A.SEMICOLON,E.A.EQUALS,E.A.CAPS_LOCK,E.A.CONTEXT_MENU,E.A.F1,E.A.F2,E.A.F3,E.A.F4,E.A.F5,E.A.F6,E.A.F7,E.A.F8,E.A.F9,E.A.F10,E.A.F11,E.A.F12].includes(n)&&g(!0)},onInputMouseDown:function(){k(!0)},onInputChange:function(e){var n=e.target.value;if(s&&N.current&&/[\r\n]/.test(N.current)){var t=N.current.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");n=n.replace(t,N.current)}N.current=null,M(n)},onInputPaste:function(e){var n=e.clipboardData,t=null==n?void 0:n.getData("text");N.current=t||""},onInputCompositionStart:function(){r.current=!0},onInputCompositionEnd:function(e){r.current=!1,"combobox"!==u&&M(e.target.value)},onInputBlur:b},R="multiple"===u||"tags"===u?f.createElement(O,(0,o.A)({},e,I)):f.createElement(F,(0,o.A)({},e,I));return f.createElement("div",{ref:y,className:"".concat(a,"-selector"),onClick:function(e){e.target!==t.current&&(void 0!==document.body.style.msTouchAction?setTimeout((function(){t.current.focus()})):t.current.focus())},onMouseDown:function(e){var n=S();e.target===t.current||n||"combobox"===u&&d||e.preventDefault(),("combobox"===u||c&&n)&&l||(l&&!1!==p&&m("",!0,!1),g())}},v&&f.createElement("div",{className:"".concat(a,"-prefix")},v),R)};const H=f.forwardRef(L);var K=t(62427),W=["prefixCls","disabled","visible","children","popupElement","animation","transitionName","dropdownStyle","dropdownClassName","direction","placement","builtinPlacements","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","getPopupContainer","empty","getTriggerDOMNode","onPopupVisibleChange","onPopupMouseEnter"],B=function(e,n){var t=e.prefixCls,r=(e.disabled,e.visible),i=e.children,c=e.popupElement,s=e.animation,d=e.transitionName,v=e.dropdownStyle,m=e.dropdownClassName,h=e.direction,g=void 0===h?"ltr":h,A=e.placement,b=e.builtinPlacements,y=e.dropdownMatchSelectWidth,C=e.dropdownRender,E=e.dropdownAlign,w=e.getPopupContainer,x=e.empty,S=e.getTriggerDOMNode,k=e.onPopupVisibleChange,N=e.onPopupMouseEnter,M=(0,u.A)(e,W),I="".concat(t,"-dropdown"),R=c;C&&(R=C(c));var D=f.useMemo((function(){return b||function(e){var n=!0===e?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"}}}(y)}),[b,y]),P=s?"".concat(I,"-").concat(s):d,T="number"==typeof y,V=f.useMemo((function(){return T?null:!1===y?"minWidth":"width"}),[y,T]),O=v;T&&(O=(0,l.A)((0,l.A)({},O),{},{width:y}));var F=f.useRef(null);return f.useImperativeHandle(n,(function(){return{getPopupElement:function(){var e;return null===(e=F.current)||void 0===e?void 0:e.popupElement}}})),f.createElement(K.A,(0,o.A)({},M,{showAction:k?["click"]:[],hideAction:k?["click"]:[],popupPlacement:A||("rtl"===g?"bottomRight":"bottomLeft"),builtinPlacements:D,prefixCls:I,popupTransitionName:P,popup:f.createElement("div",{onMouseEnter:N},R),ref:F,stretch:V,popupAlign:E,popupVisible:r,getPopupContainer:w,popupClassName:p()(m,(0,a.A)({},"".concat(I,"-empty"),x)),popupStyle:O,getTriggerDOMNode:S,onPopupVisibleChange:k}),i)};const z=f.forwardRef(B);var _=t(87695);function U(e,n){var t,o=e.key;return"value"in e&&(t=e.value),null!=o?o:void 0!==t?t:"rc-index-key-".concat(n)}function j(e){return void 0!==e&&!Number.isNaN(e)}function Y(e,n){var t=e||{},o=t.label||(n?"children":"label");return{label:o,value:t.value||"value",options:t.options||"options",groupLabel:t.groupLabel||o}}function X(e){var n=(0,l.A)({},e);return"props"in n||Object.defineProperty(n,"props",{get:function(){return(0,d.Ay)(!1,"Return type is option instead of Option instance. Please read value directly instead of reading from `props`."),n}}),n}const G=f.createContext(null);function q(e){var n=e.visible,t=e.values;return n?f.createElement("span",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"".concat(t.slice(0,50).map((function(e){var n=e.label,t=e.value;return["number","string"].includes((0,c.A)(n))?n:t})).join(", ")),t.length>50?", ...":null):null}var $=["id","prefixCls","className","showSearch","tagRender","direction","omitDomProps","displayValues","onDisplayValuesChange","emptyOptions","notFoundContent","onClear","mode","disabled","loading","getInputElement","getRawInputElement","open","defaultOpen","onDropdownVisibleChange","activeValue","onActiveValueChange","activeDescendantId","searchValue","autoClearSearchValue","onSearch","onSearchSplit","tokenSeparators","allowClear","prefix","suffixIcon","clearIcon","OptionList","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","placement","builtinPlacements","getPopupContainer","showAction","onFocus","onBlur","onKeyUp","onKeyDown","onMouseDown"],J=["value","onChange","removeIcon","placeholder","autoFocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabIndex"],Q=function(e){return"tags"===e||"multiple"===e},Z=f.forwardRef((function(e,n){var t,d=e.id,v=e.prefixCls,y=e.className,E=e.showSearch,w=e.tagRender,x=e.direction,S=e.omitDomProps,k=e.displayValues,N=e.onDisplayValuesChange,M=e.emptyOptions,I=e.notFoundContent,R=void 0===I?"Not Found":I,D=e.onClear,P=e.mode,T=e.disabled,V=e.loading,O=e.getInputElement,F=e.getRawInputElement,L=e.open,K=e.defaultOpen,W=e.onDropdownVisibleChange,B=e.activeValue,U=e.onActiveValueChange,Y=e.activeDescendantId,X=e.searchValue,Z=e.autoClearSearchValue,ee=e.onSearch,ne=e.onSearchSplit,te=e.tokenSeparators,oe=e.allowClear,re=e.prefix,ae=e.suffixIcon,le=e.clearIcon,ie=e.OptionList,ue=e.animation,ce=e.transitionName,se=e.dropdownStyle,de=e.dropdownClassName,fe=e.dropdownMatchSelectWidth,ve=e.dropdownRender,pe=e.dropdownAlign,me=e.placement,he=e.builtinPlacements,ge=e.getPopupContainer,Ae=e.showAction,be=void 0===Ae?[]:Ae,ye=e.onFocus,Ce=e.onBlur,Ee=e.onKeyUp,we=e.onKeyDown,xe=e.onMouseDown,Se=(0,u.A)(e,$),ke=Q(P),Ne=(void 0!==E?E:ke)||"combobox"===P,Me=(0,l.A)({},Se);J.forEach((function(e){delete Me[e]})),null==S||S.forEach((function(e){delete Me[e]}));var Ie=f.useState(!1),Re=(0,i.A)(Ie,2),De=Re[0],Pe=Re[1];f.useEffect((function(){Pe((0,h.A)())}),[]);var Te=f.useRef(null),Ve=f.useRef(null),Oe=f.useRef(null),Fe=f.useRef(null),Le=f.useRef(null),He=f.useRef(!1),Ke=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,n=f.useState(!1),t=(0,i.A)(n,2),o=t[0],r=t[1],a=f.useRef(null),l=function(){window.clearTimeout(a.current)};return f.useEffect((function(){return l}),[]),[o,function(n,t){l(),a.current=window.setTimeout((function(){r(n),t&&t()}),e)},l]}(),We=(0,i.A)(Ke,3),Be=We[0],ze=We[1],_e=We[2];f.useImperativeHandle(n,(function(){var e,n;return{focus:null===(e=Fe.current)||void 0===e?void 0:e.focus,blur:null===(n=Fe.current)||void 0===n?void 0:n.blur,scrollTo:function(e){var n;return null===(n=Le.current)||void 0===n?void 0:n.scrollTo(e)},nativeElement:Te.current||Ve.current}}));var Ue=f.useMemo((function(){var e;if("combobox"!==P)return X;var n=null===(e=k[0])||void 0===e?void 0:e.value;return"string"==typeof n||"number"==typeof n?String(n):""}),[X,P,k]),je="combobox"===P&&"function"==typeof O&&O()||null,Ye="function"==typeof F&&F(),Xe=(0,g.xK)(Ve,null==Ye||null===(t=Ye.props)||void 0===t?void 0:t.ref),Ge=f.useState(!1),qe=(0,i.A)(Ge,2),$e=qe[0],Je=qe[1];(0,m.A)((function(){Je(!0)}),[]);var Qe=(0,s.A)(!1,{defaultValue:K,value:L}),Ze=(0,i.A)(Qe,2),en=Ze[0],nn=Ze[1],tn=!!$e&&en,on=!R&&M;(T||on&&tn&&"combobox"===P)&&(tn=!1);var rn=!on&&tn,an=f.useCallback((function(e){var n=void 0!==e?e:!tn;T||(nn(n),tn!==n&&(null==W||W(n)))}),[T,tn,nn,W]),ln=f.useMemo((function(){return(te||[]).some((function(e){return["\n","\r\n"].includes(e)}))}),[te]),un=f.useContext(G)||{},cn=un.maxCount,sn=un.rawValues,dn=function(e,n,t){if(!(ke&&j(cn)&&(null==sn?void 0:sn.size)>=cn)){var o=!0,a=e;null==U||U(null);var l=function(e,n,t){if(!n||!n.length)return null;var o=!1,a=function e(n,t){var a=(0,_.A)(t),l=a[0],i=a.slice(1);if(!l)return[n];var u=n.split(l);return o=o||u.length>1,u.reduce((function(n,t){return[].concat((0,r.A)(n),(0,r.A)(e(t,i)))}),[]).filter(Boolean)}(e,n);return o?void 0!==t?a.slice(0,t):a:null}(e,te,j(cn)?cn-sn.size:void 0),i=t?null:l;return"combobox"!==P&&i&&(a="",null==ne||ne(i),an(!1),o=!1),ee&&Ue!==a&&ee(a,{source:n?"typing":"effect"}),o}};f.useEffect((function(){tn||ke||"combobox"===P||dn("",!1,!1)}),[tn]),f.useEffect((function(){en&&T&&nn(!1),T&&!He.current&&ze(!1)}),[T]);var fn=C(),vn=(0,i.A)(fn,2),pn=vn[0],mn=vn[1],hn=f.useRef(!1),gn=f.useRef(!1),An=[];f.useEffect((function(){return function(){An.forEach((function(e){return clearTimeout(e)})),An.splice(0,An.length)}}),[]);var bn,yn=f.useState({}),Cn=(0,i.A)(yn,2)[1];Ye&&(bn=function(e){an(e)}),function(e,n,t,o){var r=f.useRef(null);r.current={open:n,triggerOpen:t,customizedTrigger:o},f.useEffect((function(){function e(e){var n,t;if(null===(n=r.current)||void 0===n||!n.customizedTrigger){var o=e.target;o.shadowRoot&&e.composed&&(o=e.composedPath()[0]||o),r.current.open&&[Te.current,null===(t=Oe.current)||void 0===t?void 0:t.getPopupElement()].filter((function(e){return e})).every((function(e){return!e.contains(o)&&e!==o}))&&r.current.triggerOpen(!1)}}return window.addEventListener("mousedown",e),function(){return window.removeEventListener("mousedown",e)}}),[])}(0,rn,an,!!Ye);var En,wn=f.useMemo((function(){return(0,l.A)((0,l.A)({},e),{},{notFoundContent:R,open:tn,triggerOpen:rn,id:d,showSearch:Ne,multiple:ke,toggleOpen:an})}),[e,R,rn,tn,d,Ne,ke,an]),xn=!!ae||V;xn&&(En=f.createElement(A,{className:p()("".concat(v,"-arrow"),(0,a.A)({},"".concat(v,"-arrow-loading"),V)),customizeIcon:ae,customizeIconProps:{loading:V,searchValue:Ue,open:tn,focused:Be,showSearch:Ne}}));var Sn,kn=function(e,n,t,o,r){var a=arguments.length>5&&void 0!==arguments[5]&&arguments[5],l=arguments.length>6?arguments[6]:void 0,i=arguments.length>7?arguments[7]:void 0,u=f.useMemo((function(){return"object"===(0,c.A)(o)?o.clearIcon:r||void 0}),[o,r]);return{allowClear:f.useMemo((function(){return!(a||!o||!t.length&&!l||"combobox"===i&&""===l)}),[o,a,t.length,l,i]),clearIcon:f.createElement(A,{className:"".concat(e,"-clear"),onMouseDown:n,customizeIcon:u},"×")}}(v,(function(){var e;null==D||D(),null===(e=Fe.current)||void 0===e||e.focus(),N([],{type:"clear",values:k}),dn("",!1,!1)}),k,oe,le,T,Ue,P),Nn=kn.allowClear,Mn=kn.clearIcon,In=f.createElement(ie,{ref:Le}),Rn=p()(v,y,(0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)((0,a.A)({},"".concat(v,"-focused"),Be),"".concat(v,"-multiple"),ke),"".concat(v,"-single"),!ke),"".concat(v,"-allow-clear"),oe),"".concat(v,"-show-arrow"),xn),"".concat(v,"-disabled"),T),"".concat(v,"-loading"),V),"".concat(v,"-open"),tn),"".concat(v,"-customize-input"),je),"".concat(v,"-show-search"),Ne)),Dn=f.createElement(z,{ref:Oe,disabled:T,prefixCls:v,visible:rn,popupElement:In,animation:ue,transitionName:ce,dropdownStyle:se,dropdownClassName:de,direction:x,dropdownMatchSelectWidth:fe,dropdownRender:ve,dropdownAlign:pe,placement:me,builtinPlacements:he,getPopupContainer:ge,empty:M,getTriggerDOMNode:function(e){return Ve.current||e},onPopupVisibleChange:bn,onPopupMouseEnter:function(){Cn({})}},Ye?f.cloneElement(Ye,{ref:Xe}):f.createElement(H,(0,o.A)({},e,{domRef:Ve,prefixCls:v,inputElement:je,ref:Fe,id:d,prefix:re,showSearch:Ne,autoClearSearchValue:Z,mode:P,activeDescendantId:Y,tagRender:w,values:k,open:tn,onToggleOpen:an,activeValue:B,searchValue:Ue,onSearch:dn,onSearchSubmit:function(e){e&&e.trim()&&ee(e,{source:"submit"})},onRemove:function(e){var n=k.filter((function(n){return n!==e}));N(n,{type:"remove",values:[e]})},tokenWithEnter:ln,onInputBlur:function(){hn.current=!1}})));return Sn=Ye?Dn:f.createElement("div",(0,o.A)({className:Rn},Me,{ref:Te,onMouseDown:function(e){var n,t=e.target,o=null===(n=Oe.current)||void 0===n?void 0:n.getPopupElement();if(o&&o.contains(t)){var r=setTimeout((function(){var e,n=An.indexOf(r);-1!==n&&An.splice(n,1),_e(),De||o.contains(document.activeElement)||null===(e=Fe.current)||void 0===e||e.focus()}));An.push(r)}for(var a=arguments.length,l=new Array(a>1?a-1:0),i=1;i<a;i++)l[i-1]=arguments[i];null==xe||xe.apply(void 0,[e].concat(l))},onKeyDown:function(e){var n,t=pn(),o=e.key,a="Enter"===o;if(a&&("combobox"!==P&&e.preventDefault(),tn||an(!0)),mn(!!Ue),"Backspace"===o&&!t&&ke&&!Ue&&k.length){for(var l=(0,r.A)(k),i=null,u=l.length-1;u>=0;u-=1){var c=l[u];if(!c.disabled){l.splice(u,1),i=c;break}}i&&N(l,{type:"remove",values:[i]})}for(var s=arguments.length,d=new Array(s>1?s-1:0),f=1;f<s;f++)d[f-1]=arguments[f];!tn||a&&hn.current||(a&&(hn.current=!0),null===(n=Le.current)||void 0===n||n.onKeyDown.apply(n,[e].concat(d))),null==we||we.apply(void 0,[e].concat(d))},onKeyUp:function(e){for(var n=arguments.length,t=new Array(n>1?n-1:0),o=1;o<n;o++)t[o-1]=arguments[o];var r;tn&&(null===(r=Le.current)||void 0===r||r.onKeyUp.apply(r,[e].concat(t))),"Enter"===e.key&&(hn.current=!1),null==Ee||Ee.apply(void 0,[e].concat(t))},onFocus:function(){ze(!0),T||(ye&&!gn.current&&ye.apply(void 0,arguments),be.includes("focus")&&an(!0)),gn.current=!0},onBlur:function(){He.current=!0,ze(!1,(function(){gn.current=!1,He.current=!1,an(!1)})),T||(Ue&&("tags"===P?ee(Ue,{source:"submit"}):"multiple"===P&&ee("",{source:"blur"})),Ce&&Ce.apply(void 0,arguments))}}),f.createElement(q,{visible:Be&&!tn,values:k}),Dn,En,Nn&&Mn),f.createElement(b.Provider,{value:wn},Sn)}));const ee=Z;var ne=function(){return null};ne.isSelectOptGroup=!0;const te=ne;var oe=function(){return null};oe.isSelectOption=!0;const re=oe;var ae=t(28104),le=t(19853),ie=t(60551),ue=["disabled","title","children","style","className"];function ce(e){return"string"==typeof e||"number"==typeof e}var se=function(e,n){var t=y(),l=t.prefixCls,c=t.id,s=t.open,d=t.multiple,v=t.mode,m=t.searchValue,h=t.toggleOpen,g=t.notFoundContent,b=t.onPopupScroll,C=f.useContext(G),x=C.maxCount,S=C.flattenOptions,k=C.onActiveValue,N=C.defaultActiveFirstOption,M=C.onSelect,I=C.menuItemSelectedIcon,R=C.rawValues,D=C.fieldNames,P=C.virtual,T=C.direction,V=C.listHeight,O=C.listItemHeight,F=C.optionRender,L="".concat(l,"-item"),H=(0,ae.A)((function(){return S}),[s,S],(function(e,n){return n[0]&&e[1]!==n[1]})),K=f.useRef(null),W=f.useMemo((function(){return d&&j(x)&&(null==R?void 0:R.size)>=x}),[d,x,null==R?void 0:R.size]),B=function(e){e.preventDefault()},z=function(e){var n;null===(n=K.current)||void 0===n||n.scrollTo("number"==typeof e?{index:e}:e)},_=f.useCallback((function(e){return"combobox"!==v&&R.has(e)}),[v,(0,r.A)(R).toString(),R.size]),U=function(e){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,t=H.length,o=0;o<t;o+=1){var r=(e+o*n+t)%t,a=H[r]||{},l=a.group,i=a.data;if(!l&&(null==i||!i.disabled)&&(_(i.value)||!W))return r}return-1},Y=f.useState((function(){return U(0)})),X=(0,i.A)(Y,2),q=X[0],$=X[1],J=function(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];$(e);var t={source:n?"keyboard":"mouse"},o=H[e];o?k(o.value,e,t):k(null,-1,t)};(0,f.useEffect)((function(){J(!1!==N?U(0):-1)}),[H.length,m]);var Q=f.useCallback((function(e){return"combobox"===v?String(e).toLowerCase()===m.toLowerCase():R.has(e)}),[v,m,(0,r.A)(R).toString(),R.size]);(0,f.useEffect)((function(){var e,n=setTimeout((function(){if(!d&&s&&1===R.size){var e=Array.from(R)[0],n=H.findIndex((function(n){var t=n.data;return m?String(t.value).startsWith(m):t.value===e}));-1!==n&&(J(n),z(n))}}));return s&&(null===(e=K.current)||void 0===e||e.scrollTo(void 0)),function(){return clearTimeout(n)}}),[s,m]);var Z=function(e){void 0!==e&&M(e,{selected:!R.has(e)}),d||h(!1)};if(f.useImperativeHandle(n,(function(){return{onKeyDown:function(e){var n=e.which,t=e.ctrlKey;switch(n){case E.A.N:case E.A.P:case E.A.UP:case E.A.DOWN:var o=0;if(n===E.A.UP?o=-1:n===E.A.DOWN?o=1:/(mac\sos|macintosh)/i.test(navigator.appVersion)&&t&&(n===E.A.N?o=1:n===E.A.P&&(o=-1)),0!==o){var r=U(q+o,o);z(r),J(r,!0)}break;case E.A.TAB:case E.A.ENTER:var a,l=H[q];!l||null!=l&&null!==(a=l.data)&&void 0!==a&&a.disabled||W?Z(void 0):Z(l.value),s&&e.preventDefault();break;case E.A.ESC:h(!1),s&&e.stopPropagation()}},onKeyUp:function(){},scrollTo:function(e){z(e)}}})),0===H.length)return f.createElement("div",{role:"listbox",id:"".concat(c,"_list"),className:"".concat(L,"-empty"),onMouseDown:B},g);var ee=Object.keys(D).map((function(e){return D[e]})),ne=function(e){return e.label};function te(e,n){return{role:e.group?"presentation":"option",id:"".concat(c,"_list_").concat(n)}}var oe=function(e){var n=H[e];if(!n)return null;var t=n.data||{},r=t.value,a=n.group,l=(0,w.A)(t,!0),i=ne(n);return n?f.createElement("div",(0,o.A)({"aria-label":"string"!=typeof i||a?null:i},l,{key:e},te(n,e),{"aria-selected":Q(r)}),r):null},re={role:"listbox",id:"".concat(c,"_list")};return f.createElement(f.Fragment,null,P&&f.createElement("div",(0,o.A)({},re,{style:{height:0,width:0,overflow:"hidden"}}),oe(q-1),oe(q),oe(q+1)),f.createElement(ie.A,{itemKey:"key",ref:K,data:H,height:V,itemHeight:O,fullHeight:!1,onMouseDown:B,onScroll:b,virtual:P,direction:T,innerProps:P?null:re},(function(e,n){var t=e.group,r=e.groupOption,l=e.data,i=e.label,c=e.value,s=l.key;if(t){var d,v=null!==(d=l.title)&&void 0!==d?d:ce(i)?i.toString():void 0;return f.createElement("div",{className:p()(L,"".concat(L,"-group"),l.className),title:v},void 0!==i?i:s)}var m=l.disabled,h=l.title,g=(l.children,l.style),b=l.className,y=(0,u.A)(l,ue),C=(0,le.A)(y,ee),E=_(c),x=m||!E&&W,S="".concat(L,"-option"),k=p()(L,S,b,(0,a.A)((0,a.A)((0,a.A)((0,a.A)({},"".concat(S,"-grouped"),r),"".concat(S,"-active"),q===n&&!x),"".concat(S,"-disabled"),x),"".concat(S,"-selected"),E)),N=ne(e),M=!I||"function"==typeof I||E,R="number"==typeof N?N:N||c,D=ce(R)?R.toString():void 0;return void 0!==h&&(D=h),f.createElement("div",(0,o.A)({},(0,w.A)(C),P?{}:te(e,n),{"aria-selected":Q(c),className:k,title:D,onMouseMove:function(){q===n||x||J(n)},onClick:function(){x||Z(c)},style:g}),f.createElement("div",{className:"".concat(S,"-content")},"function"==typeof F?F(e,{index:n}):R),f.isValidElement(I)||E,M&&f.createElement(A,{className:"".concat(L,"-option-state"),customizeIcon:I,customizeIconProps:{value:c,disabled:x,isSelected:E}},E?"✓":null))})))};const de=f.forwardRef(se);function fe(e,n){return I(e).join("").toUpperCase().includes(n)}var ve=t(3979),pe=t(82546),me=["children","value"],he=["children"];function ge(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return(0,pe.A)(e).map((function(e,t){if(!f.isValidElement(e)||!e.type)return null;var o=e,r=o.type.isSelectOptGroup,a=o.key,i=o.props,c=i.children,s=(0,u.A)(i,he);return n||!r?function(e){var n=e,t=n.key,o=n.props,r=o.children,a=o.value,i=(0,u.A)(o,me);return(0,l.A)({key:t,value:void 0!==a?a:t,children:r},i)}(e):(0,l.A)((0,l.A)({key:"__RC_SELECT_GRP__".concat(null===a?t:a,"__"),label:a},s),{},{options:ge(c)})})).filter((function(e){return e}))}const Ae=function(e,n,t,o,r){return f.useMemo((function(){var a=e;!e&&(a=ge(n));var l=new Map,i=new Map,u=function(e,n,t){t&&"string"==typeof t&&e.set(n[t],n)};return function e(n){for(var a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],c=0;c<n.length;c+=1){var s=n[c];!s[t.options]||a?(l.set(s[t.value],s),u(i,s,t.label),u(i,s,o),u(i,s,r)):e(s[t.options],!0)}}(a),{options:a,valueOptions:l,labelOptions:i}}),[e,n,t,o,r])};function be(e){var n=f.useRef();n.current=e;var t=f.useCallback((function(){return n.current.apply(n,arguments)}),[]);return t}var ye=["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","optionRender","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","direction","listHeight","listItemHeight","labelRender","value","defaultValue","labelInValue","onChange","maxCount"],Ce=["inputValue"],Ee=f.forwardRef((function(e,n){var t=e.id,d=e.mode,v=e.prefixCls,p=void 0===v?"rc-select":v,m=e.backfill,h=e.fieldNames,g=e.inputValue,A=e.searchValue,b=e.onSearch,y=e.autoClearSearchValue,C=void 0===y||y,E=e.onSelect,w=e.onDeselect,x=e.dropdownMatchSelectWidth,S=void 0===x||x,k=e.filterOption,N=e.filterSort,M=e.optionFilterProp,R=e.optionLabelProp,D=e.options,P=e.optionRender,T=e.children,V=e.defaultActiveFirstOption,O=e.menuItemSelectedIcon,F=e.virtual,L=e.direction,H=e.listHeight,K=void 0===H?200:H,W=e.listItemHeight,B=void 0===W?20:W,z=e.labelRender,_=e.value,j=e.defaultValue,q=e.labelInValue,$=e.onChange,J=e.maxCount,Z=(0,u.A)(e,ye),ne=(0,ve.Ay)(t),te=Q(d),oe=!(D||!T),re=f.useMemo((function(){return(void 0!==k||"combobox"!==d)&&k}),[k,d]),ae=f.useMemo((function(){return Y(h,oe)}),[JSON.stringify(h),oe]),le=(0,s.A)("",{value:void 0!==A?A:g,postState:function(e){return e||""}}),ie=(0,i.A)(le,2),ue=ie[0],ce=ie[1],se=Ae(D,T,ae,M,R),pe=se.valueOptions,me=se.labelOptions,he=se.options,ge=f.useCallback((function(e){return I(e).map((function(e){var n,t,o,r,a,l;!function(e){return!e||"object"!==(0,c.A)(e)}(e)?(o=e.key,t=e.label,n=null!==(l=e.value)&&void 0!==l?l:o):n=e;var i,u=pe.get(n);return u&&(void 0===t&&(t=null==u?void 0:u[R||ae.label]),void 0===o&&(o=null!==(i=null==u?void 0:u.key)&&void 0!==i?i:n),r=null==u?void 0:u.disabled,a=null==u?void 0:u.title),{label:t,value:n,key:o,disabled:r,title:a}}))}),[ae,R,pe]),Ee=(0,s.A)(j,{value:_}),we=(0,i.A)(Ee,2),xe=we[0],Se=we[1],ke=f.useMemo((function(){var e,n=ge(te&&null===xe?[]:xe);return"combobox"===d&&function(e){return!e&&0!==e}(null===(e=n[0])||void 0===e?void 0:e.value)?[]:n}),[xe,ge,d,te]),Ne=function(e,n){var t=f.useRef({values:new Map,options:new Map});return[f.useMemo((function(){var o=t.current,r=o.values,a=o.options,i=e.map((function(e){var n;return void 0===e.label?(0,l.A)((0,l.A)({},e),{},{label:null===(n=r.get(e.value))||void 0===n?void 0:n.label}):e})),u=new Map,c=new Map;return i.forEach((function(e){u.set(e.value,e),c.set(e.value,n.get(e.value)||a.get(e.value))})),t.current.values=u,t.current.options=c,i}),[e,n]),f.useCallback((function(e){return n.get(e)||t.current.options.get(e)}),[n])]}(ke,pe),Me=(0,i.A)(Ne,2),Ie=Me[0],Re=Me[1],De=f.useMemo((function(){if(!d&&1===Ie.length){var e=Ie[0];if(null===e.value&&(null===e.label||void 0===e.label))return[]}return Ie.map((function(e){var n;return(0,l.A)((0,l.A)({},e),{},{label:null!==(n="function"==typeof z?z(e):e.label)&&void 0!==n?n:e.value})}))}),[d,Ie,z]),Pe=f.useMemo((function(){return new Set(Ie.map((function(e){return e.value})))}),[Ie]);f.useEffect((function(){if("combobox"===d){var e,n=null===(e=Ie[0])||void 0===e?void 0:e.value;ce(function(e){return null!=e}(n)?String(n):"")}}),[Ie]);var Te=be((function(e,n){var t=null!=n?n:e;return(0,a.A)((0,a.A)({},ae.value,e),ae.label,t)})),Ve=function(e,n,t,o,r){return f.useMemo((function(){if(!t||!1===o)return e;var i=n.options,u=n.label,c=n.value,s=[],d="function"==typeof o,f=t.toUpperCase(),v=d?o:function(e,n){return r?fe(n[r],f):n[i]?fe(n["children"!==u?u:"label"],f):fe(n[c],f)},p=d?function(e){return X(e)}:function(e){return e};return e.forEach((function(e){if(e[i])if(v(t,p(e)))s.push(e);else{var n=e[i].filter((function(e){return v(t,p(e))}));n.length&&s.push((0,l.A)((0,l.A)({},e),{},(0,a.A)({},i,n)))}else v(t,p(e))&&s.push(e)})),s}),[e,o,r,t,n])}(f.useMemo((function(){if("tags"!==d)return he;var e=(0,r.A)(he);return(0,r.A)(Ie).sort((function(e,n){return e.value<n.value?-1:1})).forEach((function(n){var t=n.value;(function(e){return pe.has(e)})(t)||e.push(Te(t,n.label))})),e}),[Te,he,pe,Ie,d]),ae,ue,re,M),Oe=f.useMemo((function(){return"tags"!==d||!ue||Ve.some((function(e){return e[M||"value"]===ue}))||Ve.some((function(e){return e[ae.value]===ue}))?Ve:[Te(ue)].concat((0,r.A)(Ve))}),[Te,M,d,Ve,ue,ae]),Fe=function e(n){return(0,r.A)(n).sort((function(e,n){return N(e,n,{searchValue:ue})})).map((function(n){return Array.isArray(n.options)?(0,l.A)((0,l.A)({},n),{},{options:n.options.length>0?e(n.options):n.options}):n}))},Le=f.useMemo((function(){return N?Fe(Oe):Oe}),[Oe,N,ue]),He=f.useMemo((function(){return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=n.fieldNames,o=n.childrenAsData,r=[],a=Y(t,!1),l=a.label,i=a.value,u=a.options,c=a.groupLabel;return function e(n,t){Array.isArray(n)&&n.forEach((function(n){if(t||!(u in n)){var a=n[i];r.push({key:U(n,r.length),groupOption:t,data:n,label:n[l],value:a})}else{var s=n[c];void 0===s&&o&&(s=n.label),r.push({key:U(n,r.length),group:!0,data:n,label:s}),e(n[u],!0)}}))}(e,!1),r}(Le,{fieldNames:ae,childrenAsData:oe})}),[Le,ae,oe]),Ke=function(e){var n=ge(e);if(Se(n),$&&(n.length!==Ie.length||n.some((function(e,n){var t;return(null===(t=Ie[n])||void 0===t?void 0:t.value)!==(null==e?void 0:e.value)})))){var t=q?n:n.map((function(e){return e.value})),o=n.map((function(e){return X(Re(e.value))}));$(te?t:t[0],te?o:o[0])}},We=f.useState(null),Be=(0,i.A)(We,2),ze=Be[0],_e=Be[1],Ue=f.useState(0),je=(0,i.A)(Ue,2),Ye=je[0],Xe=je[1],Ge=void 0!==V?V:"combobox"!==d,qe=f.useCallback((function(e,n){var t=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).source,o=void 0===t?"keyboard":t;Xe(n),m&&"combobox"===d&&null!==e&&"keyboard"===o&&_e(String(e))}),[m,d]),$e=function(e,n,t){var o=function(){var n,t=Re(e);return[q?{label:null==t?void 0:t[ae.label],value:e,key:null!==(n=null==t?void 0:t.key)&&void 0!==n?n:e}:e,X(t)]};if(n&&E){var r=o(),a=(0,i.A)(r,2),l=a[0],u=a[1];E(l,u)}else if(!n&&w&&"clear"!==t){var c=o(),s=(0,i.A)(c,2),d=s[0],f=s[1];w(d,f)}},Je=be((function(e,n){var t,o=!te||n.selected;t=o?te?[].concat((0,r.A)(Ie),[e]):[e]:Ie.filter((function(n){return n.value!==e})),Ke(t),$e(e,o),"combobox"===d?_e(""):Q&&!C||(ce(""),_e(""))})),Qe=f.useMemo((function(){var e=!1!==F&&!1!==S;return(0,l.A)((0,l.A)({},se),{},{flattenOptions:He,onActiveValue:qe,defaultActiveFirstOption:Ge,onSelect:Je,menuItemSelectedIcon:O,rawValues:Pe,fieldNames:ae,virtual:e,direction:L,listHeight:K,listItemHeight:B,childrenAsData:oe,maxCount:J,optionRender:P})}),[J,se,He,qe,Ge,Je,O,Pe,ae,F,S,L,K,B,oe,P]);return f.createElement(G.Provider,{value:Qe},f.createElement(ee,(0,o.A)({},Z,{id:ne,prefixCls:p,ref:n,omitDomProps:Ce,mode:d,displayValues:De,onDisplayValuesChange:function(e,n){Ke(e);var t=n.type,o=n.values;"remove"!==t&&"clear"!==t||o.forEach((function(e){$e(e.value,!1,t)}))},direction:L,searchValue:ue,onSearch:function(e,n){if(ce(e),_e(null),"submit"!==n.source)"blur"!==n.source&&("combobox"===d&&Ke(e),null==b||b(e));else{var t=(e||"").trim();if(t){var o=Array.from(new Set([].concat((0,r.A)(Pe),[t])));Ke(o),$e(t,!0),ce("")}}},autoClearSearchValue:C,onSearchSplit:function(e){var n=e;"tags"!==d&&(n=e.map((function(e){var n=me.get(e);return null==n?void 0:n.value})).filter((function(e){return void 0!==e})));var t=Array.from(new Set([].concat((0,r.A)(Pe),(0,r.A)(n))));Ke(t),t.forEach((function(e){$e(e,!0)}))},dropdownMatchSelectWidth:S,OptionList:de,emptyOptions:!He.length,activeValue:ze,activeDescendantId:"".concat(ne,"_list_").concat(Ye)})))}));Ee.Option=re,Ee.OptGroup=te;const we=Ee},3979:(e,n,t)=>{t.d(n,{Ay:()=>u});var o=t(5544),r=t(96540),a=t(20998),l=0,i=(0,a.A)();function u(e){var n=r.useState(),t=(0,o.A)(n,2),a=t[0],u=t[1];return r.useEffect((function(){var e;u("rc_select_".concat((i?(e=l,l+=1):e="TEST_OR_SSR",e)))}),[]),e||a}},26076:(e,n,t)=>{t.d(n,{A:()=>w});var o=t(58168),r=t(96540),a=t(82546),l=(t(68210),t(89379)),i=t(82284),u=t(66588),c=t(8719),s=r.createContext(null),d=t(43591),f=new Map,v=new d.A((function(e){e.forEach((function(e){var n,t=e.target;null===(n=f.get(t))||void 0===n||n.forEach((function(e){return e(t)}))}))})),p=t(23029),m=t(92901),h=t(85501),g=t(29426),A=function(e){(0,h.A)(t,e);var n=(0,g.A)(t);function t(){return(0,p.A)(this,t),n.apply(this,arguments)}return(0,m.A)(t,[{key:"render",value:function(){return this.props.children}}]),t}(r.Component);function b(e,n){var t=e.children,o=e.disabled,a=r.useRef(null),d=r.useRef(null),p=r.useContext(s),m="function"==typeof t,h=m?t(a):t,g=r.useRef({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),b=!m&&r.isValidElement(h)&&(0,c.f3)(h),y=b?(0,c.A9)(h):null,C=(0,c.xK)(y,a),E=function(){var e;return(0,u.Ay)(a.current)||(a.current&&"object"===(0,i.A)(a.current)?(0,u.Ay)(null===(e=a.current)||void 0===e?void 0:e.nativeElement):null)||(0,u.Ay)(d.current)};r.useImperativeHandle(n,(function(){return E()}));var w=r.useRef(e);w.current=e;var x=r.useCallback((function(e){var n=w.current,t=n.onResize,o=n.data,r=e.getBoundingClientRect(),a=r.width,i=r.height,u=e.offsetWidth,c=e.offsetHeight,s=Math.floor(a),d=Math.floor(i);if(g.current.width!==s||g.current.height!==d||g.current.offsetWidth!==u||g.current.offsetHeight!==c){var f={width:s,height:d,offsetWidth:u,offsetHeight:c};g.current=f;var v=u===Math.round(a)?a:u,m=c===Math.round(i)?i:c,h=(0,l.A)((0,l.A)({},f),{},{offsetWidth:v,offsetHeight:m});null==p||p(h,e,o),t&&Promise.resolve().then((function(){t(h,e)}))}}),[]);return r.useEffect((function(){var e,n,t=E();return t&&!o&&(e=t,n=x,f.has(e)||(f.set(e,new Set),v.observe(e)),f.get(e).add(n)),function(){return function(e,n){f.has(e)&&(f.get(e).delete(n),f.get(e).size||(v.unobserve(e),f.delete(e)))}(t,x)}}),[a.current,o]),r.createElement(A,{ref:d},b?r.cloneElement(h,{ref:C}):h)}const y=r.forwardRef(b);function C(e,n){var t=e.children;return("function"==typeof t?[t]:(0,a.A)(t)).map((function(t,a){var l=(null==t?void 0:t.key)||"".concat("rc-observer-key","-").concat(a);return r.createElement(y,(0,o.A)({},e,{key:l,ref:0===a?n:void 0}),t)}))}var E=r.forwardRef(C);E.Collection=function(e){var n=e.children,t=e.onBatchResize,o=r.useRef(0),a=r.useRef([]),l=r.useContext(s),i=r.useCallback((function(e,n,r){o.current+=1;var i=o.current;a.current.push({size:e,element:n,data:r}),Promise.resolve().then((function(){i===o.current&&(null==t||t(a.current),a.current=[])})),null==l||l(e,n,r)}),[t,l]);return r.createElement(s.Provider,{value:i},n)};const w=E},55168:(e,n,t)=>{t.d(n,{Q:()=>w,A:()=>F});var o=t(89379),r=t(64467),a=t(60436),l=t(82284),i=t(5544),u=t(46942),c=t.n(u),s=t(26956),d=t(12533),f=t(43210),v=t(68210),p=t(96540),m=t(58168),h=t(53986),g=t(40961);function A(e,n,t){return(e-n)/(t-n)}function b(e,n,t,o){var r=A(n,t,o),a={};switch(e){case"rtl":a.right="".concat(100*r,"%"),a.transform="translateX(50%)";break;case"btt":a.bottom="".concat(100*r,"%"),a.transform="translateY(50%)";break;case"ttb":a.top="".concat(100*r,"%"),a.transform="translateY(-50%)";break;default:a.left="".concat(100*r,"%"),a.transform="translateX(-50%)"}return a}function y(e,n){return Array.isArray(e)?e[n]:e}var C=t(16928);const E=p.createContext({min:0,max:0,direction:"ltr",step:1,includedStart:0,includedEnd:0,tabIndex:0,keyboard:!0,styles:{},classNames:{}});var w=p.createContext({}),x=["prefixCls","value","valueIndex","onStartMove","onDelete","style","render","dragging","draggingDelete","onOffsetChange","onChangeComplete","onFocus","onMouseEnter"];const S=p.forwardRef((function(e,n){var t,a=e.prefixCls,l=e.value,i=e.valueIndex,u=e.onStartMove,s=e.onDelete,d=e.style,f=e.render,v=e.dragging,g=e.draggingDelete,A=e.onOffsetChange,w=e.onChangeComplete,S=e.onFocus,k=e.onMouseEnter,N=(0,h.A)(e,x),M=p.useContext(E),I=M.min,R=M.max,D=M.direction,P=M.disabled,T=M.keyboard,V=M.range,O=M.tabIndex,F=M.ariaLabelForHandle,L=M.ariaLabelledByForHandle,H=M.ariaRequired,K=M.ariaValueTextFormatterForHandle,W=M.styles,B=M.classNames,z="".concat(a,"-handle"),_=function(e){P||u(e,i)},U=b(D,l,I,R),j={};null!==i&&(j={tabIndex:P?null:y(O,i),role:"slider","aria-valuemin":I,"aria-valuemax":R,"aria-valuenow":l,"aria-disabled":P,"aria-label":y(F,i),"aria-labelledby":y(L,i),"aria-required":y(H,i),"aria-valuetext":null===(t=y(K,i))||void 0===t?void 0:t(l),"aria-orientation":"ltr"===D||"rtl"===D?"horizontal":"vertical",onMouseDown:_,onTouchStart:_,onFocus:function(e){null==S||S(e,i)},onMouseEnter:function(e){k(e,i)},onKeyDown:function(e){if(!P&&T){var n=null;switch(e.which||e.keyCode){case C.A.LEFT:n="ltr"===D||"btt"===D?-1:1;break;case C.A.RIGHT:n="ltr"===D||"btt"===D?1:-1;break;case C.A.UP:n="ttb"!==D?1:-1;break;case C.A.DOWN:n="ttb"!==D?-1:1;break;case C.A.HOME:n="min";break;case C.A.END:n="max";break;case C.A.PAGE_UP:n=2;break;case C.A.PAGE_DOWN:n=-2;break;case C.A.BACKSPACE:case C.A.DELETE:s(i)}null!==n&&(e.preventDefault(),A(n,i))}},onKeyUp:function(e){switch(e.which||e.keyCode){case C.A.LEFT:case C.A.RIGHT:case C.A.UP:case C.A.DOWN:case C.A.HOME:case C.A.END:case C.A.PAGE_UP:case C.A.PAGE_DOWN:null==w||w()}}});var Y=p.createElement("div",(0,m.A)({ref:n,className:c()(z,(0,r.A)((0,r.A)((0,r.A)({},"".concat(z,"-").concat(i+1),null!==i&&V),"".concat(z,"-dragging"),v),"".concat(z,"-dragging-delete"),g),B.handle),style:(0,o.A)((0,o.A)((0,o.A)({},U),d),W.handle)},j,N));return f&&(Y=f(Y,{index:i,prefixCls:a,value:l,dragging:v,draggingDelete:g})),Y}));var k=["prefixCls","style","onStartMove","onOffsetChange","values","handleRender","activeHandleRender","draggingIndex","draggingDelete","onFocus"];const N=p.forwardRef((function(e,n){var t=e.prefixCls,r=e.style,a=e.onStartMove,l=e.onOffsetChange,u=e.values,c=e.handleRender,s=e.activeHandleRender,d=e.draggingIndex,f=e.draggingDelete,v=e.onFocus,A=(0,h.A)(e,k),b=p.useRef({}),C=p.useState(!1),E=(0,i.A)(C,2),w=E[0],x=E[1],N=p.useState(-1),M=(0,i.A)(N,2),I=M[0],R=M[1],D=function(e){R(e),x(!0)};p.useImperativeHandle(n,(function(){return{focus:function(e){var n;null===(n=b.current[e])||void 0===n||n.focus()},hideHelp:function(){(0,g.flushSync)((function(){x(!1)}))}}}));var P=(0,o.A)({prefixCls:t,onStartMove:a,onOffsetChange:l,render:c,onFocus:function(e,n){D(n),null==v||v(e)},onMouseEnter:function(e,n){D(n)}},A);return p.createElement(p.Fragment,null,u.map((function(e,n){var t=d===n;return p.createElement(S,(0,m.A)({ref:function(e){e?b.current[n]=e:delete b.current[n]},dragging:t,draggingDelete:t&&f,style:y(r,n),key:n,value:e,valueIndex:n},P))})),s&&w&&p.createElement(S,(0,m.A)({key:"a11y"},P,{value:u[I],valueIndex:null,dragging:-1!==d,draggingDelete:f,render:s,style:{pointerEvents:"none"},tabIndex:null,"aria-hidden":!0})))})),M=function(e){var n=e.prefixCls,t=e.style,a=e.children,l=e.value,i=e.onClick,u=p.useContext(E),s=u.min,d=u.max,f=u.direction,v=u.includedStart,m=u.includedEnd,h=u.included,g="".concat(n,"-text"),A=b(f,l,s,d);return p.createElement("span",{className:c()(g,(0,r.A)({},"".concat(g,"-active"),h&&v<=l&&l<=m)),style:(0,o.A)((0,o.A)({},A),t),onMouseDown:function(e){e.stopPropagation()},onClick:function(){i(l)}},a)},I=function(e){var n=e.prefixCls,t=e.marks,o=e.onClick,r="".concat(n,"-mark");return t.length?p.createElement("div",{className:r},t.map((function(e){var n=e.value,t=e.style,a=e.label;return p.createElement(M,{key:n,prefixCls:r,style:t,value:n,onClick:o},a)}))):null},R=function(e){var n=e.prefixCls,t=e.value,a=e.style,l=e.activeStyle,i=p.useContext(E),u=i.min,s=i.max,d=i.direction,f=i.included,v=i.includedStart,m=i.includedEnd,h="".concat(n,"-dot"),g=f&&v<=t&&t<=m,A=(0,o.A)((0,o.A)({},b(d,t,u,s)),"function"==typeof a?a(t):a);return g&&(A=(0,o.A)((0,o.A)({},A),"function"==typeof l?l(t):l)),p.createElement("span",{className:c()(h,(0,r.A)({},"".concat(h,"-active"),g)),style:A})},D=function(e){var n=e.prefixCls,t=e.marks,o=e.dots,r=e.style,a=e.activeStyle,l=p.useContext(E),i=l.min,u=l.max,c=l.step,s=p.useMemo((function(){var e=new Set;if(t.forEach((function(n){e.add(n.value)})),o&&null!==c)for(var n=i;n<=u;)e.add(n),n+=c;return Array.from(e)}),[i,u,c,o,t]);return p.createElement("div",{className:"".concat(n,"-step")},s.map((function(e){return p.createElement(R,{prefixCls:n,key:e,value:e,style:r,activeStyle:a})})))},P=function(e){var n=e.prefixCls,t=e.style,a=e.start,l=e.end,i=e.index,u=e.onStartMove,s=e.replaceCls,d=p.useContext(E),f=d.direction,v=d.min,m=d.max,h=d.disabled,g=d.range,b=d.classNames,y="".concat(n,"-track"),C=A(a,v,m),w=A(l,v,m),x=function(e){!h&&u&&u(e,-1)},S={};switch(f){case"rtl":S.right="".concat(100*C,"%"),S.width="".concat(100*w-100*C,"%");break;case"btt":S.bottom="".concat(100*C,"%"),S.height="".concat(100*w-100*C,"%");break;case"ttb":S.top="".concat(100*C,"%"),S.height="".concat(100*w-100*C,"%");break;default:S.left="".concat(100*C,"%"),S.width="".concat(100*w-100*C,"%")}var k=s||c()(y,(0,r.A)((0,r.A)({},"".concat(y,"-").concat(i+1),null!==i&&g),"".concat(n,"-track-draggable"),u),b.track);return p.createElement("div",{className:k,style:(0,o.A)((0,o.A)({},S),t),onMouseDown:x,onTouchStart:x})},T=function(e){var n=e.prefixCls,t=e.style,r=e.values,a=e.startPoint,l=e.onStartMove,i=p.useContext(E),u=i.included,s=i.range,d=i.min,f=i.styles,v=i.classNames,m=p.useMemo((function(){if(!s){if(0===r.length)return[];var e=null!=a?a:d,n=r[0];return[{start:Math.min(e,n),end:Math.max(e,n)}]}for(var t=[],o=0;o<r.length-1;o+=1)t.push({start:r[o],end:r[o+1]});return t}),[r,s,a,d]);if(!u)return null;var h=null!=m&&m.length&&(v.tracks||f.tracks)?p.createElement(P,{index:null,prefixCls:n,start:m[0].start,end:m[m.length-1].end,replaceCls:c()(v.tracks,"".concat(n,"-tracks")),style:f.tracks}):null;return p.createElement(p.Fragment,null,h,m.map((function(e,r){var a=e.start,i=e.end;return p.createElement(P,{index:r,prefixCls:n,style:(0,o.A)((0,o.A)({},y(t,r)),f.track),start:a,end:i,key:r,onStartMove:l})})))};var V=t(30981);function O(e){var n="targetTouches"in e?e.targetTouches[0]:e;return{pageX:n.pageX,pageY:n.pageY}}const F=p.forwardRef((function(e,n){var t=e.prefixCls,u=void 0===t?"rc-slider":t,m=e.className,h=e.style,g=e.classNames,A=e.styles,b=e.id,y=e.disabled,C=void 0!==y&&y,x=e.keyboard,S=void 0===x||x,k=e.autoFocus,M=e.onFocus,R=e.onBlur,P=e.min,F=void 0===P?0:P,L=e.max,H=void 0===L?100:L,K=e.step,W=void 0===K?1:K,B=e.value,z=e.defaultValue,_=e.range,U=e.count,j=e.onChange,Y=e.onBeforeChange,X=e.onAfterChange,G=e.onChangeComplete,q=e.allowCross,$=void 0===q||q,J=e.pushable,Q=void 0!==J&&J,Z=e.reverse,ee=e.vertical,ne=e.included,te=void 0===ne||ne,oe=e.startPoint,re=e.trackStyle,ae=e.handleStyle,le=e.railStyle,ie=e.dotStyle,ue=e.activeDotStyle,ce=e.marks,se=e.dots,de=e.handleRender,fe=e.activeHandleRender,ve=e.track,pe=e.tabIndex,me=void 0===pe?0:pe,he=e.ariaLabelForHandle,ge=e.ariaLabelledByForHandle,Ae=e.ariaRequired,be=e.ariaValueTextFormatterForHandle,ye=p.useRef(null),Ce=p.useRef(null),Ee=p.useMemo((function(){return ee?Z?"ttb":"btt":Z?"rtl":"ltr"}),[Z,ee]),we=function(e){return(0,p.useMemo)((function(){if(!0===e||!e)return[!!e,!1,!1,0];var n=e.editable,t=e.draggableTrack;return[!0,n,!n&&t,e.minCount||0,e.maxCount]}),[e])}(_),xe=(0,i.A)(we,5),Se=xe[0],ke=xe[1],Ne=xe[2],Me=xe[3],Ie=xe[4],Re=p.useMemo((function(){return isFinite(F)?F:0}),[F]),De=p.useMemo((function(){return isFinite(H)?H:100}),[H]),Pe=p.useMemo((function(){return null!==W&&W<=0?1:W}),[W]),Te=p.useMemo((function(){return"boolean"==typeof Q?!!Q&&Pe:Q>=0&&Q}),[Q,Pe]),Ve=p.useMemo((function(){return Object.keys(ce||{}).map((function(e){var n=ce[e],t={value:Number(e)};return n&&"object"===(0,l.A)(n)&&!p.isValidElement(n)&&("label"in n||"style"in n)?(t.style=n.style,t.label=n.label):t.label=n,t})).filter((function(e){var n=e.label;return n||"number"==typeof n})).sort((function(e,n){return e.value-n.value}))}),[ce]),Oe=function(e,n,t,o,r,l){var i=p.useCallback((function(t){return Math.max(e,Math.min(n,t))}),[e,n]),u=p.useCallback((function(o){if(null!==t){var r=e+Math.round((i(o)-e)/t)*t,a=function(e){return(String(e).split(".")[1]||"").length},l=Math.max(a(t),a(n),a(e)),u=Number(r.toFixed(l));return e<=u&&u<=n?u:null}return null}),[t,e,n,i]),c=p.useCallback((function(r){var a=i(r),l=o.map((function(e){return e.value}));null!==t&&l.push(u(r)),l.push(e,n);var c=l[0],s=n-e;return l.forEach((function(e){var n=Math.abs(a-e);n<=s&&(c=e,s=n)})),c}),[e,n,o,t,i,u]),s=function r(l,i,c){var s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit";if("number"==typeof i){var d,f=l[c],v=f+i,p=[];o.forEach((function(e){p.push(e.value)})),p.push(e,n),p.push(u(f));var m=i>0?1:-1;"unit"===s?p.push(u(f+m*t)):p.push(u(v)),p=p.filter((function(e){return null!==e})).filter((function(e){return i<0?e<=f:e>=f})),"unit"===s&&(p=p.filter((function(e){return e!==f})));var h="unit"===s?f:v;d=p[0];var g=Math.abs(d-h);if(p.forEach((function(e){var n=Math.abs(e-h);n<g&&(d=e,g=n)})),void 0===d)return i<0?e:n;if("dist"===s)return d;if(Math.abs(i)>1){var A=(0,a.A)(l);return A[c]=d,r(A,i-m,c,s)}return d}return"min"===i?e:"max"===i?n:void 0},d=function(e,n,t){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",r=e[t],a=s(e,n,t,o);return{value:a,changed:a!==r}},f=function(e){return null===l&&0===e||"number"==typeof l&&e<l};return[c,function(e,n,t){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",a=e.map(c),i=a[t],u=s(a,n,t,o);if(a[t]=u,!1===r){var v=l||0;t>0&&a[t-1]!==i&&(a[t]=Math.max(a[t],a[t-1]+v)),t<a.length-1&&a[t+1]!==i&&(a[t]=Math.min(a[t],a[t+1]-v))}else if("number"==typeof l||null===l){for(var p=t+1;p<a.length;p+=1)for(var m=!0;f(a[p]-a[p-1])&&m;){var h=d(a,1,p);a[p]=h.value,m=h.changed}for(var g=t;g>0;g-=1)for(var A=!0;f(a[g]-a[g-1])&&A;){var b=d(a,-1,g-1);a[g-1]=b.value,A=b.changed}for(var y=a.length-1;y>0;y-=1)for(var C=!0;f(a[y]-a[y-1])&&C;){var E=d(a,-1,y-1);a[y-1]=E.value,C=E.changed}for(var w=0;w<a.length-1;w+=1)for(var x=!0;f(a[w+1]-a[w])&&x;){var S=d(a,1,w+1);a[w+1]=S.value,x=S.changed}}return{value:a[t],values:a}}]}(Re,De,Pe,Ve,$,Te),Fe=(0,i.A)(Oe,2),Le=Fe[0],He=Fe[1],Ke=(0,d.A)(z,{value:B}),We=(0,i.A)(Ke,2),Be=We[0],ze=We[1],_e=p.useMemo((function(){var e=null==Be?[]:Array.isArray(Be)?Be:[Be],n=(0,i.A)(e,1)[0],t=null===Be?[]:[void 0===n?Re:n];if(Se){if(t=(0,a.A)(e),U||void 0===Be){var o=U>=0?U+1:2;for(t=t.slice(0,o);t.length<o;){var r;t.push(null!==(r=t[t.length-1])&&void 0!==r?r:Re)}}t.sort((function(e,n){return e-n}))}return t.forEach((function(e,n){t[n]=Le(e)})),t}),[Be,Se,Re,U,Le]),Ue=function(e){return Se?e:e[0]},je=(0,s.A)((function(e){var n=(0,a.A)(e).sort((function(e,n){return e-n}));j&&!(0,f.A)(n,_e,!0)&&j(Ue(n)),ze(n)})),Ye=(0,s.A)((function(e){e&&ye.current.hideHelp();var n=Ue(_e);null==X||X(n),(0,v.Ay)(!X,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null==G||G(n)})),Xe=function(e,n,t,o,r,l,u,c,d,f,v){var m=p.useState(null),h=(0,i.A)(m,2),g=h[0],A=h[1],b=p.useState(-1),y=(0,i.A)(b,2),C=y[0],E=y[1],x=p.useState(!1),S=(0,i.A)(x,2),k=S[0],N=S[1],M=p.useState(t),I=(0,i.A)(M,2),R=I[0],D=I[1],P=p.useState(t),T=(0,i.A)(P,2),F=T[0],L=T[1],H=p.useRef(null),K=p.useRef(null),W=p.useRef(null),B=p.useContext(w),z=B.onDragStart,_=B.onDragChange;(0,V.A)((function(){-1===C&&D(t)}),[t,C]),p.useEffect((function(){return function(){document.removeEventListener("mousemove",H.current),document.removeEventListener("mouseup",K.current),W.current&&(W.current.removeEventListener("touchmove",H.current),W.current.removeEventListener("touchend",K.current))}}),[]);var U=function(e,n,t){void 0!==n&&A(n),D(e);var o=e;t&&(o=e.filter((function(e,n){return n!==C}))),u(o),_&&_({rawValues:e,deleteIndex:t?C:-1,draggingIndex:C,draggingValue:n})},j=(0,s.A)((function(e,n,t){if(-1===e){var i=F[0],u=F[F.length-1],c=o-i,s=r-u,f=n*(r-o);f=Math.max(f,c),f=Math.min(f,s);var v=l(i+f);f=v-i;var p=F.map((function(e){return e+f}));U(p)}else{var m=(r-o)*n,h=(0,a.A)(R);h[e]=F[e];var g=d(h,m,e,"dist");U(g.values,g.value,t)}})),Y=p.useMemo((function(){var e=(0,a.A)(t).sort((function(e,n){return e-n})),n=(0,a.A)(R).sort((function(e,n){return e-n})),o={};n.forEach((function(e){o[e]=(o[e]||0)+1})),e.forEach((function(e){o[e]=(o[e]||0)-1}));var r=f?1:0;return Object.values(o).reduce((function(e,n){return e+Math.abs(n)}),0)<=r?R:t}),[t,R,f]);return[C,g,k,Y,function(o,r,a){o.stopPropagation();var l=a||t,i=l[r];E(r),A(i),L(l),D(l),N(!1);var u=O(o),s=u.pageX,d=u.pageY,p=!1;z&&z({rawValues:l,draggingIndex:r,draggingValue:i});var m=function(t){t.preventDefault();var o,a,l=O(t),i=l.pageX,u=l.pageY,c=i-s,m=u-d,h=e.current.getBoundingClientRect(),g=h.width,A=h.height;switch(n){case"btt":o=-m/A,a=c;break;case"ttb":o=m/A,a=c;break;case"rtl":o=-c/g,a=m;break;default:o=c/g,a=m}p=!!f&&Math.abs(a)>130&&v<R.length,N(p),j(r,o,p)},h=function e(n){n.preventDefault(),document.removeEventListener("mouseup",e),document.removeEventListener("mousemove",m),W.current&&(W.current.removeEventListener("touchmove",H.current),W.current.removeEventListener("touchend",K.current)),H.current=null,K.current=null,W.current=null,c(p),E(-1),N(!1)};document.addEventListener("mouseup",h),document.addEventListener("mousemove",m),o.currentTarget.addEventListener("touchend",h),o.currentTarget.addEventListener("touchmove",m),H.current=m,K.current=h,W.current=o.currentTarget}]}(Ce,Ee,_e,Re,De,Le,je,Ye,He,ke,Me),Ge=(0,i.A)(Xe,5),qe=Ge[0],$e=Ge[1],Je=Ge[2],Qe=Ge[3],Ze=Ge[4],en=function(e,n){if(!C){var t=(0,a.A)(_e),o=0,r=0,l=De-Re;_e.forEach((function(n,t){var a=Math.abs(e-n);a<=l&&(l=a,o=t),n<e&&(r=t)}));var i=o;ke&&0!==l&&(!Ie||_e.length<Ie)?(t.splice(r+1,0,e),i=r+1):t[o]=e,Se&&!_e.length&&void 0===U&&t.push(e);var u,c,s=Ue(t);null==Y||Y(s),je(t),n?(null===(u=document.activeElement)||void 0===u||null===(c=u.blur)||void 0===c||c.call(u),ye.current.focus(i),Ze(n,i,t)):(null==X||X(s),(0,v.Ay)(!X,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null==G||G(s))}},nn=p.useState(null),tn=(0,i.A)(nn,2),on=tn[0],rn=tn[1];p.useEffect((function(){if(null!==on){var e=_e.indexOf(on);e>=0&&ye.current.focus(e)}rn(null)}),[on]);var an=p.useMemo((function(){return(!Ne||null!==Pe)&&Ne}),[Ne,Pe]),ln=(0,s.A)((function(e,n){Ze(e,n),null==Y||Y(Ue(_e))})),un=-1!==qe;p.useEffect((function(){if(!un){var e=_e.lastIndexOf($e);ye.current.focus(e)}}),[un]);var cn=p.useMemo((function(){return(0,a.A)(Qe).sort((function(e,n){return e-n}))}),[Qe]),sn=p.useMemo((function(){return Se?[cn[0],cn[cn.length-1]]:[Re,cn[0]]}),[cn,Se,Re]),dn=(0,i.A)(sn,2),fn=dn[0],vn=dn[1];p.useImperativeHandle(n,(function(){return{focus:function(){ye.current.focus(0)},blur:function(){var e,n=document.activeElement;null!==(e=Ce.current)&&void 0!==e&&e.contains(n)&&(null==n||n.blur())}}})),p.useEffect((function(){k&&ye.current.focus(0)}),[]);var pn=p.useMemo((function(){return{min:Re,max:De,direction:Ee,disabled:C,keyboard:S,step:Pe,included:te,includedStart:fn,includedEnd:vn,range:Se,tabIndex:me,ariaLabelForHandle:he,ariaLabelledByForHandle:ge,ariaRequired:Ae,ariaValueTextFormatterForHandle:be,styles:A||{},classNames:g||{}}}),[Re,De,Ee,C,S,Pe,te,fn,vn,Se,me,he,ge,Ae,be,A,g]);return p.createElement(E.Provider,{value:pn},p.createElement("div",{ref:Ce,className:c()(u,m,(0,r.A)((0,r.A)((0,r.A)((0,r.A)({},"".concat(u,"-disabled"),C),"".concat(u,"-vertical"),ee),"".concat(u,"-horizontal"),!ee),"".concat(u,"-with-marks"),Ve.length)),style:h,onMouseDown:function(e){e.preventDefault();var n,t=Ce.current.getBoundingClientRect(),o=t.width,r=t.height,a=t.left,l=t.top,i=t.bottom,u=t.right,c=e.clientX,s=e.clientY;switch(Ee){case"btt":n=(i-s)/r;break;case"ttb":n=(s-l)/r;break;case"rtl":n=(u-c)/o;break;default:n=(c-a)/o}en(Le(Re+n*(De-Re)),e)},id:b},p.createElement("div",{className:c()("".concat(u,"-rail"),null==g?void 0:g.rail),style:(0,o.A)((0,o.A)({},le),null==A?void 0:A.rail)}),!1!==ve&&p.createElement(T,{prefixCls:u,style:re,values:_e,startPoint:oe,onStartMove:an?ln:void 0}),p.createElement(D,{prefixCls:u,marks:Ve,dots:se,style:ie,activeStyle:ue}),p.createElement(N,{ref:ye,prefixCls:u,style:ae,values:Qe,draggingIndex:qe,draggingDelete:Je,onStartMove:ln,onOffsetChange:function(e,n){if(!C){var t=He(_e,e,n);null==Y||Y(Ue(_e)),je(t.values),rn(t.value)}},onFocus:M,onBlur:R,handleRender:de,activeHandleRender:fe,onChangeComplete:Ye,onDelete:ke?function(e){if(!(C||!ke||_e.length<=Me)){var n=(0,a.A)(_e);n.splice(e,1),null==Y||Y(Ue(n)),je(n);var t=Math.max(0,e-1);ye.current.hideHelp(),ye.current.focus(t)}}:void 0}),p.createElement(I,{prefixCls:u,marks:Ve,onClick:en})))}))},81102:(e,n,t)=>{t.d(n,{A:()=>p});var o=t(58168),r=t(64467),a=t(5544),l=t(53986),i=t(96540),u=t(46942),c=t.n(u),s=t(12533),d=t(16928),f=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],v=i.forwardRef((function(e,n){var t,u=e.prefixCls,v=void 0===u?"rc-switch":u,p=e.className,m=e.checked,h=e.defaultChecked,g=e.disabled,A=e.loadingIcon,b=e.checkedChildren,y=e.unCheckedChildren,C=e.onClick,E=e.onChange,w=e.onKeyDown,x=(0,l.A)(e,f),S=(0,s.A)(!1,{value:m,defaultValue:h}),k=(0,a.A)(S,2),N=k[0],M=k[1];function I(e,n){var t=N;return g||(M(t=e),null==E||E(t,n)),t}var R=c()(v,p,(t={},(0,r.A)(t,"".concat(v,"-checked"),N),(0,r.A)(t,"".concat(v,"-disabled"),g),t));return i.createElement("button",(0,o.A)({},x,{type:"button",role:"switch","aria-checked":N,disabled:g,className:R,ref:n,onKeyDown:function(e){e.which===d.A.LEFT?I(!1,e):e.which===d.A.RIGHT&&I(!0,e),null==w||w(e)},onClick:function(e){var n=I(!N,e);null==C||C(n,e)}}),A,i.createElement("span",{className:"".concat(v,"-inner")},i.createElement("span",{className:"".concat(v,"-inner-checked")},b),i.createElement("span",{className:"".concat(v,"-inner-unchecked")},y)))}));v.displayName="Switch";const p=v},92387:(e,n,t)=>{t.d(n,{A:()=>h});var o=t(58168),r=t(89379),a=t(64467),l=t(53986),i=t(46942),u=t.n(i),c=t(96540),s=t(16928),d=["className","prefixCls","style","active","status","iconPrefix","icon","wrapperStyle","stepNumber","disabled","description","title","subTitle","progressDot","stepIcon","tailContent","icons","stepIndex","onStepClick","onClick","render"];function f(e){return"string"==typeof e}const v=function(e){var n,t=e.className,i=e.prefixCls,v=e.style,p=e.active,m=e.status,h=e.iconPrefix,g=e.icon,A=(e.wrapperStyle,e.stepNumber),b=e.disabled,y=e.description,C=e.title,E=e.subTitle,w=e.progressDot,x=e.stepIcon,S=e.tailContent,k=e.icons,N=e.stepIndex,M=e.onStepClick,I=e.onClick,R=e.render,D=(0,l.A)(e,d),P={};M&&!b&&(P.role="button",P.tabIndex=0,P.onClick=function(e){null==I||I(e),M(N)},P.onKeyDown=function(e){var n=e.which;n!==s.A.ENTER&&n!==s.A.SPACE||M(N)});var T,V,O,F,L=m||"wait",H=u()("".concat(i,"-item"),"".concat(i,"-item-").concat(L),t,(n={},(0,a.A)(n,"".concat(i,"-item-custom"),g),(0,a.A)(n,"".concat(i,"-item-active"),p),(0,a.A)(n,"".concat(i,"-item-disabled"),!0===b),n)),K=(0,r.A)({},v),W=c.createElement("div",(0,o.A)({},D,{className:H,style:K}),c.createElement("div",(0,o.A)({onClick:I},P,{className:"".concat(i,"-item-container")}),c.createElement("div",{className:"".concat(i,"-item-tail")},S),c.createElement("div",{className:"".concat(i,"-item-icon")},(O=u()("".concat(i,"-icon"),"".concat(h,"icon"),(T={},(0,a.A)(T,"".concat(h,"icon-").concat(g),g&&f(g)),(0,a.A)(T,"".concat(h,"icon-check"),!g&&"finish"===m&&(k&&!k.finish||!k)),(0,a.A)(T,"".concat(h,"icon-cross"),!g&&"error"===m&&(k&&!k.error||!k)),T)),F=c.createElement("span",{className:"".concat(i,"-icon-dot")}),V=w?"function"==typeof w?c.createElement("span",{className:"".concat(i,"-icon")},w(F,{index:A-1,status:m,title:C,description:y})):c.createElement("span",{className:"".concat(i,"-icon")},F):g&&!f(g)?c.createElement("span",{className:"".concat(i,"-icon")},g):k&&k.finish&&"finish"===m?c.createElement("span",{className:"".concat(i,"-icon")},k.finish):k&&k.error&&"error"===m?c.createElement("span",{className:"".concat(i,"-icon")},k.error):g||"finish"===m||"error"===m?c.createElement("span",{className:O}):c.createElement("span",{className:"".concat(i,"-icon")},A),x&&(V=x({index:A-1,status:m,title:C,description:y,node:V})),V)),c.createElement("div",{className:"".concat(i,"-item-content")},c.createElement("div",{className:"".concat(i,"-item-title")},C,E&&c.createElement("div",{title:"string"==typeof E?E:void 0,className:"".concat(i,"-item-subtitle")},E)),y&&c.createElement("div",{className:"".concat(i,"-item-description")},y))));return R&&(W=R(W)||null),W};var p=["prefixCls","style","className","children","direction","type","labelPlacement","iconPrefix","status","size","current","progressDot","stepIcon","initial","icons","onChange","itemRender","items"];function m(e){var n,t=e.prefixCls,i=void 0===t?"rc-steps":t,s=e.style,d=void 0===s?{}:s,f=e.className,m=(e.children,e.direction),h=void 0===m?"horizontal":m,g=e.type,A=void 0===g?"default":g,b=e.labelPlacement,y=void 0===b?"horizontal":b,C=e.iconPrefix,E=void 0===C?"rc":C,w=e.status,x=void 0===w?"process":w,S=e.size,k=e.current,N=void 0===k?0:k,M=e.progressDot,I=void 0!==M&&M,R=e.stepIcon,D=e.initial,P=void 0===D?0:D,T=e.icons,V=e.onChange,O=e.itemRender,F=e.items,L=void 0===F?[]:F,H=(0,l.A)(e,p),K="navigation"===A,W="inline"===A,B=W||I,z=W?"horizontal":h,_=W?void 0:S,U=B?"vertical":y,j=u()(i,"".concat(i,"-").concat(z),f,(n={},(0,a.A)(n,"".concat(i,"-").concat(_),_),(0,a.A)(n,"".concat(i,"-label-").concat(U),"horizontal"===z),(0,a.A)(n,"".concat(i,"-dot"),!!B),(0,a.A)(n,"".concat(i,"-navigation"),K),(0,a.A)(n,"".concat(i,"-inline"),W),n)),Y=function(e){V&&N!==e&&V(e)};return c.createElement("div",(0,o.A)({className:j,style:d},H),L.filter((function(e){return e})).map((function(e,n){var t=(0,r.A)({},e),a=P+n;return"error"===x&&n===N-1&&(t.className="".concat(i,"-next-error")),t.status||(t.status=a===N?x:a<N?"finish":"wait"),W&&(t.icon=void 0,t.subTitle=void 0),!t.render&&O&&(t.render=function(e){return O(t,e)}),c.createElement(v,(0,o.A)({},t,{active:a===N,stepNumber:a+1,stepIndex:a,key:a,prefixCls:i,iconPrefix:E,wrapperStyle:d,progressDot:B,stepIcon:R,icons:T,onStepClick:V&&Y}))})))}m.Step=v;const h=m},92849:(e,n,t)=>{t.d(n,{A:()=>E});var o=t(58168),r=t(5544),a=t(53986),l=t(64467),i=t(89379),u=t(82284),c=t(46942),s=t.n(c),d=t(12533),f=t(19853),v=t(8719),p=t(96540),m=t(57557),h=t(30981),g=function(e,n){if(!e)return null;var t={left:e.offsetLeft,right:e.parentElement.clientWidth-e.clientWidth-e.offsetLeft,width:e.clientWidth,top:e.offsetTop,bottom:e.parentElement.clientHeight-e.clientHeight-e.offsetTop,height:e.clientHeight};return n?{left:0,right:0,width:0,top:t.top,bottom:t.bottom,height:t.height}:{left:t.left,right:t.right,width:t.width,top:0,bottom:0,height:0}},A=function(e){return void 0!==e?"".concat(e,"px"):void 0};function b(e){var n=e.prefixCls,t=e.containerRef,o=e.value,a=e.getValueIndex,l=e.motionName,u=e.onMotionStart,c=e.onMotionEnd,d=e.direction,f=e.vertical,b=void 0!==f&&f,y=p.useRef(null),C=p.useState(o),E=(0,r.A)(C,2),w=E[0],x=E[1],S=function(e){var o,r=a(e),l=null===(o=t.current)||void 0===o?void 0:o.querySelectorAll(".".concat(n,"-item"))[r];return(null==l?void 0:l.offsetParent)&&l},k=p.useState(null),N=(0,r.A)(k,2),M=N[0],I=N[1],R=p.useState(null),D=(0,r.A)(R,2),P=D[0],T=D[1];(0,h.A)((function(){if(w!==o){var e=S(w),n=S(o),t=g(e,b),r=g(n,b);x(o),I(t),T(r),e&&n?u():c()}}),[o]);var V=p.useMemo((function(){var e;return A(b?null!==(e=null==M?void 0:M.top)&&void 0!==e?e:0:"rtl"===d?-(null==M?void 0:M.right):null==M?void 0:M.left)}),[b,d,M]),O=p.useMemo((function(){var e;return A(b?null!==(e=null==P?void 0:P.top)&&void 0!==e?e:0:"rtl"===d?-(null==P?void 0:P.right):null==P?void 0:P.left)}),[b,d,P]);return M&&P?p.createElement(m.Ay,{visible:!0,motionName:l,motionAppear:!0,onAppearStart:function(){return b?{transform:"translateY(var(--thumb-start-top))",height:"var(--thumb-start-height)"}:{transform:"translateX(var(--thumb-start-left))",width:"var(--thumb-start-width)"}},onAppearActive:function(){return b?{transform:"translateY(var(--thumb-active-top))",height:"var(--thumb-active-height)"}:{transform:"translateX(var(--thumb-active-left))",width:"var(--thumb-active-width)"}},onVisibleChanged:function(){I(null),T(null),c()}},(function(e,t){var o=e.className,r=e.style,a=(0,i.A)((0,i.A)({},r),{},{"--thumb-start-left":V,"--thumb-start-width":A(null==M?void 0:M.width),"--thumb-active-left":O,"--thumb-active-width":A(null==P?void 0:P.width),"--thumb-start-top":V,"--thumb-start-height":A(null==M?void 0:M.height),"--thumb-active-top":O,"--thumb-active-height":A(null==P?void 0:P.height)}),l={ref:(0,v.K4)(y,t),style:a,className:s()("".concat(n,"-thumb"),o)};return p.createElement("div",l)})):null}var y=["prefixCls","direction","vertical","options","disabled","defaultValue","value","name","onChange","className","motionName"];var C=function(e){var n=e.prefixCls,t=e.className,o=e.disabled,r=e.checked,a=e.label,i=e.title,u=e.value,c=e.name,d=e.onChange,f=e.onFocus,v=e.onBlur,m=e.onKeyDown,h=e.onKeyUp,g=e.onMouseDown;return p.createElement("label",{className:s()(t,(0,l.A)({},"".concat(n,"-item-disabled"),o)),onMouseDown:g},p.createElement("input",{name:c,className:"".concat(n,"-item-input"),type:"radio",disabled:o,checked:r,onChange:function(e){o||d(e,u)},onFocus:f,onBlur:v,onKeyDown:m,onKeyUp:h}),p.createElement("div",{className:"".concat(n,"-item-label"),title:i,"aria-selected":r},a))};const E=p.forwardRef((function(e,n){var t,c,m=e.prefixCls,h=void 0===m?"rc-segmented":m,g=e.direction,A=e.vertical,E=e.options,w=void 0===E?[]:E,x=e.disabled,S=e.defaultValue,k=e.value,N=e.name,M=e.onChange,I=e.className,R=void 0===I?"":I,D=e.motionName,P=void 0===D?"thumb-motion":D,T=(0,a.A)(e,y),V=p.useRef(null),O=p.useMemo((function(){return(0,v.K4)(V,n)}),[V,n]),F=p.useMemo((function(){return function(e){return e.map((function(e){if("object"===(0,u.A)(e)&&null!==e){var n=function(e){return void 0!==e.title?e.title:"object"!==(0,u.A)(e.label)?null===(n=e.label)||void 0===n?void 0:n.toString():void 0;var n}(e);return(0,i.A)((0,i.A)({},e),{},{title:n})}return{label:null==e?void 0:e.toString(),title:null==e?void 0:e.toString(),value:e}}))}(w)}),[w]),L=(0,d.A)(null===(t=F[0])||void 0===t?void 0:t.value,{value:k,defaultValue:S}),H=(0,r.A)(L,2),K=H[0],W=H[1],B=p.useState(!1),z=(0,r.A)(B,2),_=z[0],U=z[1],j=function(e,n){W(n),null==M||M(n)},Y=(0,f.A)(T,["children"]),X=p.useState(!1),G=(0,r.A)(X,2),q=G[0],$=G[1],J=p.useState(!1),Q=(0,r.A)(J,2),Z=Q[0],ee=Q[1],ne=function(){ee(!0)},te=function(){ee(!1)},oe=function(){$(!1)},re=function(e){"Tab"===e.key&&$(!0)},ae=function(e){var n=F.findIndex((function(e){return e.value===K})),t=F.length,o=F[(n+e+t)%t];o&&(W(o.value),null==M||M(o.value))},le=function(e){switch(e.key){case"ArrowLeft":case"ArrowUp":ae(-1);break;case"ArrowRight":case"ArrowDown":ae(1)}};return p.createElement("div",(0,o.A)({role:"radiogroup","aria-label":"segmented control",tabIndex:x?void 0:0},Y,{className:s()(h,(c={},(0,l.A)(c,"".concat(h,"-rtl"),"rtl"===g),(0,l.A)(c,"".concat(h,"-disabled"),x),(0,l.A)(c,"".concat(h,"-vertical"),A),c),R),ref:O}),p.createElement("div",{className:"".concat(h,"-group")},p.createElement(b,{vertical:A,prefixCls:h,value:K,containerRef:V,motionName:"".concat(h,"-").concat(P),direction:g,getValueIndex:function(e){return F.findIndex((function(n){return n.value===e}))},onMotionStart:function(){U(!0)},onMotionEnd:function(){U(!1)}}),F.map((function(e){var n;return p.createElement(C,(0,o.A)({},e,{name:N,key:e.value,prefixCls:h,className:s()(e.className,"".concat(h,"-item"),(n={},(0,l.A)(n,"".concat(h,"-item-selected"),e.value===K&&!_),(0,l.A)(n,"".concat(h,"-item-focused"),Z&&q&&e.value===K),n)),checked:e.value===K,onChange:j,onFocus:ne,onBlur:te,onKeyDown:le,onKeyUp:re,onMouseDown:oe,disabled:!!x||!!e.disabled}))}))))}))}}]);