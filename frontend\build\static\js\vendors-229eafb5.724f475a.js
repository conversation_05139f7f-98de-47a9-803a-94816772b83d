/*! For license information please see vendors-229eafb5.724f475a.js.LICENSE.txt */
(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[689],{2833:e=>{e.exports=function(e,n,o,s){var r=o?o.call(s,e,n):void 0;if(void 0!==r)return!!r;if(e===n)return!0;if("object"!=typeof e||!e||"object"!=typeof n||!n)return!1;var a=Object.keys(e),i=Object.keys(n);if(a.length!==i.length)return!1;for(var t=Object.prototype.hasOwnProperty.bind(n),d=0;d<a.length;d++){var p=a[d];if(!t(p))return!1;var l=e[p],c=n[p];if(!1===(r=o?o.call(s,l,c,p):void 0)||void 0===r&&l!==c)return!1}return!0}},16426:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var n=document.activeElement,o=[],s=0;s<e.rangeCount;s++)o.push(e.getRangeAt(s));switch(n.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":n.blur();break;default:n=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||o.forEach((function(n){e.addRange(n)})),n&&n.focus()}}},18467:(e,n,o)=>{"use strict";o.d(n,{wE:()=>G,lK:()=>V,As:()=>K});var s="comm",r="rule",a="decl",i="@import",t="@namespace",d="@keyframes",p="@layer",l=Math.abs,c=String.fromCharCode;function m(e){return e.trim()}function _(e,n,o){return e.replace(n,o)}function C(e,n,o){return e.indexOf(n,o)}function P(e,n){return 0|e.charCodeAt(n)}function u(e,n,o){return e.slice(n,o)}function S(e){return e.length}function g(e,n){return n.push(e),e}Object.assign;var E=1,A=1,f=0,D=0,O=0,R="";function h(e,n,o,s,r,a,i,t){return{value:e,root:n,parent:o,type:s,props:r,children:a,line:E,column:A,length:i,return:"",siblings:t}}function I(){return O=D>0?P(R,--D):0,A--,10===O&&(A=1,E--),O}function U(){return O=D<f?P(R,D++):0,A++,10===O&&(A=1,E++),O}function T(){return P(R,D)}function N(){return D}function b(e,n){return u(R,e,n)}function y(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function L(e){return m(b(D-1,F(91===e?e+2:40===e?e+1:e)))}function v(e){for(;(O=T())&&O<33;)U();return y(e)>2||y(O)>3?"":" "}function W(e,n){for(;--n&&U()&&!(O<48||O>102||O>57&&O<65||O>70&&O<97););return b(e,N()+(n<6&&32==T()&&32==U()))}function F(e){for(;U();)switch(O){case e:return D;case 34:case 39:34!==e&&39!==e&&F(O);break;case 40:41===e&&F(e);break;case 92:U()}return D}function w(e,n){for(;U()&&e+O!==57&&(e+O!==84||47!==T()););return"/*"+b(n,D-1)+"*"+c(47===e?e:U())}function M(e){for(;!y(T());)U();return b(e,D)}function G(e){return function(e){return R="",e}(x("",null,null,null,[""],e=function(e){return E=A=1,f=S(R=e),D=0,[]}(e),0,[0],e))}function x(e,n,o,s,r,a,i,t,d){for(var p=0,m=0,E=i,A=0,f=0,D=0,O=1,R=1,h=1,b=0,F="",G=r,V=a,K=s,B=F;R;)switch(D=b,b=U()){case 40:if(108!=D&&58==P(B,E-1)){-1!=C(B+=_(L(b),"&","&\f"),"&\f",l(p?t[p-1]:0))&&(h=-1);break}case 34:case 39:case 91:B+=L(b);break;case 9:case 10:case 13:case 32:B+=v(D);break;case 92:B+=W(N()-1,7);continue;case 47:switch(T()){case 42:case 47:g(H(w(U(),N()),n,o,d),d),5!=y(D||1)&&5!=y(T()||1)||!S(B)||" "===u(B,-1,void 0)||(B+=" ");break;default:B+="/"}break;case 123*O:t[p++]=S(B)*h;case 125*O:case 59:case 0:switch(b){case 0:case 125:R=0;case 59+m:-1==h&&(B=_(B,/\f/g,"")),f>0&&(S(B)-E||0===O&&47===D)&&g(f>32?k(B+";",s,o,E-1,d):k(_(B," ","")+";",s,o,E-2,d),d);break;case 59:B+=";";default:if(g(K=j(B,n,o,p,m,r,t,F,G=[],V=[],E,a),a),123===b)if(0===m)x(B,n,K,K,G,a,E,t,V);else{switch(A){case 99:if(110===P(B,3))break;case 108:if(97===P(B,2))break;default:m=0;case 100:case 109:case 115:}m?x(e,K,K,s&&g(j(e,K,K,0,0,r,t,F,r,G=[],E,V),V),r,V,E,t,s?G:V):x(B,K,K,K,[""],V,0,t,V)}}p=m=f=0,O=h=1,F=B="",E=i;break;case 58:E=1+S(B),f=D;default:if(O<1)if(123==b)--O;else if(125==b&&0==O++&&125==I())continue;switch(B+=c(b),b*O){case 38:h=m>0?1:(B+="\f",-1);break;case 44:t[p++]=(S(B)-1)*h,h=1;break;case 64:45===T()&&(B+=L(U())),A=T(),m=E=S(F=B+=M(N())),b++;break;case 45:45===D&&2==S(B)&&(O=0)}}return a}function j(e,n,o,s,a,i,t,d,p,c,C,P){for(var S=a-1,g=0===a?i:[""],E=function(e){return e.length}(g),A=0,f=0,D=0;A<s;++A)for(var O=0,R=u(e,S+1,S=l(f=t[A])),I=e;O<E;++O)(I=m(f>0?g[O]+" "+R:_(R,/&\f/g,g[O])))&&(p[D++]=I);return h(e,n,o,0===a?r:d,p,c,C,P)}function H(e,n,o,r){return h(e,n,o,s,c(O),u(e,2,-2),0,r)}function k(e,n,o,s,r){return h(e,n,o,a,u(e,0,s),u(e,s+1,-1),s,r)}function V(e,n){for(var o="",s=0;s<e.length;s++)o+=n(e[s],s,e,n)||"";return o}function K(e,n,o,l){switch(e.type){case p:if(e.children.length)break;case i:case t:case a:return e.return=e.return||e.value;case s:return"";case d:return e.return=e.value+"{"+V(e.children,l)+"}";case r:if(!S(e.value=e.props.join(",")))return""}return S(o=V(e.children,l))?e.return=e.value+"{"+o+"}":""}},28028:e=>{e.exports=function(e){return e.replace(/[A-Z]/g,(function(e){return"-"+e.toLowerCase()})).toLowerCase()}},34915:e=>{e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},68499:(e,n,o)=>{"use strict";o.d(n,{A:()=>a});var s=o(69081);const r=e=>!1===e?{block:"end",inline:"nearest"}:(e=>e===Object(e)&&0!==Object.keys(e).length)(e)?e:{block:"start",inline:"nearest"};function a(e,n){if(!e.isConnected||!(e=>{let n=e;for(;n&&n.parentNode;){if(n.parentNode===document)return!0;n=n.parentNode instanceof ShadowRoot?n.parentNode.host:n.parentNode}return!1})(e))return;const o=(e=>{const n=window.getComputedStyle(e);return{top:parseFloat(n.scrollMarginTop)||0,right:parseFloat(n.scrollMarginRight)||0,bottom:parseFloat(n.scrollMarginBottom)||0,left:parseFloat(n.scrollMarginLeft)||0}})(e);if("object"==typeof(a=n)&&"function"==typeof a.behavior)return n.behavior((0,s.O)(e,n));var a;const i="boolean"==typeof n||null==n?void 0:n.behavior;for(const{el:a,top:t,left:d}of(0,s.O)(e,r(n))){const e=t-o.top+o.bottom,n=d-o.left+o.right;a.scroll({top:e,left:n,behavior:i})}}},70572:(e,n,o)=>{"use strict";o.d(n,{NP:()=>xn,DU:()=>Yn,AH:()=>Kn,Ay:()=>Jn,i7:()=>zn});var s=function(){return s=Object.assign||function(e){for(var n,o=1,s=arguments.length;o<s;o++)for(var r in n=arguments[o])Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r]);return e},s.apply(this,arguments)};function r(e,n,o){if(o||2===arguments.length)for(var s,r=0,a=n.length;r<a;r++)!s&&r in n||(s||(s=Array.prototype.slice.call(n,0,r)),s[r]=n[r]);return e.concat(s||Array.prototype.slice.call(n))}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError,o(39316);var a=o(96540),i=o(2833),t=o.n(i),d="-ms-",p="-moz-",l="-webkit-",c="comm",m="rule",_="decl",C="@keyframes",P=Math.abs,u=String.fromCharCode,S=Object.assign;function g(e){return e.trim()}function E(e,n){return(e=n.exec(e))?e[0]:e}function A(e,n,o){return e.replace(n,o)}function f(e,n,o){return e.indexOf(n,o)}function D(e,n){return 0|e.charCodeAt(n)}function O(e,n,o){return e.slice(n,o)}function R(e){return e.length}function h(e){return e.length}function I(e,n){return n.push(e),e}function U(e,n){return e.filter((function(e){return!E(e,n)}))}var T=1,N=1,b=0,y=0,L=0,v="";function W(e,n,o,s,r,a,i,t){return{value:e,root:n,parent:o,type:s,props:r,children:a,line:T,column:N,length:i,return:"",siblings:t}}function F(e,n){return S(W("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},n)}function w(e){for(;e.root;)e=F(e.root,{children:[e]});I(e,e.siblings)}function M(){return L=y>0?D(v,--y):0,N--,10===L&&(N=1,T--),L}function G(){return L=y<b?D(v,y++):0,N++,10===L&&(N=1,T++),L}function x(){return D(v,y)}function j(){return y}function H(e,n){return O(v,e,n)}function k(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function V(e){return g(H(y-1,X(91===e?e+2:40===e?e+1:e)))}function K(e){for(;(L=x())&&L<33;)G();return k(e)>2||k(L)>3?"":" "}function B(e,n){for(;--n&&G()&&!(L<48||L>102||L>57&&L<65||L>70&&L<97););return H(e,j()+(n<6&&32==x()&&32==G()))}function X(e){for(;G();)switch(L){case e:return y;case 34:case 39:34!==e&&39!==e&&X(L);break;case 40:41===e&&X(e);break;case 92:G()}return y}function J(e,n){for(;G()&&e+L!==57&&(e+L!==84||47!==x()););return"/*"+H(n,y-1)+"*"+u(47===e?e:G())}function $(e){for(;!k(x());)G();return H(e,y)}function Y(e){return function(e){return v="",e}(z("",null,null,null,[""],e=function(e){return T=N=1,b=R(v=e),y=0,[]}(e),0,[0],e))}function z(e,n,o,s,r,a,i,t,d){for(var p=0,l=0,c=i,m=0,_=0,C=0,S=1,g=1,E=1,O=0,h="",U=r,T=a,N=s,b=h;g;)switch(C=O,O=G()){case 40:if(108!=C&&58==D(b,c-1)){-1!=f(b+=A(V(O),"&","&\f"),"&\f",P(p?t[p-1]:0))&&(E=-1);break}case 34:case 39:case 91:b+=V(O);break;case 9:case 10:case 13:case 32:b+=K(C);break;case 92:b+=B(j()-1,7);continue;case 47:switch(x()){case 42:case 47:I(q(J(G(),j()),n,o,d),d);break;default:b+="/"}break;case 123*S:t[p++]=R(b)*E;case 125*S:case 59:case 0:switch(O){case 0:case 125:g=0;case 59+l:-1==E&&(b=A(b,/\f/g,"")),_>0&&R(b)-c&&I(_>32?Q(b+";",s,o,c-1,d):Q(A(b," ","")+";",s,o,c-2,d),d);break;case 59:b+=";";default:if(I(N=Z(b,n,o,p,l,r,t,h,U=[],T=[],c,a),a),123===O)if(0===l)z(b,n,N,N,U,a,c,t,T);else switch(99===m&&110===D(b,3)?100:m){case 100:case 108:case 109:case 115:z(e,N,N,s&&I(Z(e,N,N,0,0,r,t,h,r,U=[],c,T),T),r,T,c,t,s?U:T);break;default:z(b,N,N,N,[""],T,0,t,T)}}p=l=_=0,S=E=1,h=b="",c=i;break;case 58:c=1+R(b),_=C;default:if(S<1)if(123==O)--S;else if(125==O&&0==S++&&125==M())continue;switch(b+=u(O),O*S){case 38:E=l>0?1:(b+="\f",-1);break;case 44:t[p++]=(R(b)-1)*E,E=1;break;case 64:45===x()&&(b+=V(G())),m=x(),l=c=R(h=b+=$(j())),O++;break;case 45:45===C&&2==R(b)&&(S=0)}}return a}function Z(e,n,o,s,r,a,i,t,d,p,l,c){for(var _=r-1,C=0===r?a:[""],u=h(C),S=0,E=0,f=0;S<s;++S)for(var D=0,R=O(e,_+1,_=P(E=i[S])),I=e;D<u;++D)(I=g(E>0?C[D]+" "+R:A(R,/&\f/g,C[D])))&&(d[f++]=I);return W(e,n,o,0===r?m:t,d,p,l,c)}function q(e,n,o,s){return W(e,n,o,c,u(L),O(e,2,-2),0,s)}function Q(e,n,o,s,r){return W(e,n,o,_,O(e,0,s),O(e,s+1,-1),s,r)}function ee(e,n,o){switch(function(e,n){return 45^D(e,0)?(((n<<2^D(e,0))<<2^D(e,1))<<2^D(e,2))<<2^D(e,3):0}(e,n)){case 5103:return l+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return l+e+e;case 4789:return p+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return l+e+p+e+d+e+e;case 5936:switch(D(e,n+11)){case 114:return l+e+d+A(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return l+e+d+A(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return l+e+d+A(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return l+e+d+e+e;case 6165:return l+e+d+"flex-"+e+e;case 5187:return l+e+A(e,/(\w+).+(:[^]+)/,l+"box-$1$2"+d+"flex-$1$2")+e;case 5443:return l+e+d+"flex-item-"+A(e,/flex-|-self/g,"")+(E(e,/flex-|baseline/)?"":d+"grid-row-"+A(e,/flex-|-self/g,""))+e;case 4675:return l+e+d+"flex-line-pack"+A(e,/align-content|flex-|-self/g,"")+e;case 5548:return l+e+d+A(e,"shrink","negative")+e;case 5292:return l+e+d+A(e,"basis","preferred-size")+e;case 6060:return l+"box-"+A(e,"-grow","")+l+e+d+A(e,"grow","positive")+e;case 4554:return l+A(e,/([^-])(transform)/g,"$1"+l+"$2")+e;case 6187:return A(A(A(e,/(zoom-|grab)/,l+"$1"),/(image-set)/,l+"$1"),e,"")+e;case 5495:case 3959:return A(e,/(image-set\([^]*)/,l+"$1$`$1");case 4968:return A(A(e,/(.+:)(flex-)?(.*)/,l+"box-pack:$3"+d+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+l+e+e;case 4200:if(!E(e,/flex-|baseline/))return d+"grid-column-align"+O(e,n)+e;break;case 2592:case 3360:return d+A(e,"template-","")+e;case 4384:case 3616:return o&&o.some((function(e,o){return n=o,E(e.props,/grid-\w+-end/)}))?~f(e+(o=o[n].value),"span",0)?e:d+A(e,"-start","")+e+d+"grid-row-span:"+(~f(o,"span",0)?E(o,/\d+/):+E(o,/\d+/)-+E(e,/\d+/))+";":d+A(e,"-start","")+e;case 4896:case 4128:return o&&o.some((function(e){return E(e.props,/grid-\w+-start/)}))?e:d+A(A(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return A(e,/(.+)-inline(.+)/,l+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(R(e)-1-n>6)switch(D(e,n+1)){case 109:if(45!==D(e,n+4))break;case 102:return A(e,/(.+:)(.+)-([^]+)/,"$1"+l+"$2-$3$1"+p+(108==D(e,n+3)?"$3":"$2-$3"))+e;case 115:return~f(e,"stretch",0)?ee(A(e,"stretch","fill-available"),n,o)+e:e}break;case 5152:case 5920:return A(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,(function(n,o,s,r,a,i,t){return d+o+":"+s+t+(r?d+o+"-span:"+(a?i:+i-+s)+t:"")+e}));case 4949:if(121===D(e,n+6))return A(e,":",":"+l)+e;break;case 6444:switch(D(e,45===D(e,14)?18:11)){case 120:return A(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+l+(45===D(e,14)?"inline-":"")+"box$3$1"+l+"$2$3$1"+d+"$2box$3")+e;case 100:return A(e,":",":"+d)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return A(e,"scroll-","scroll-snap-")+e}return e}function ne(e,n){for(var o="",s=0;s<e.length;s++)o+=n(e[s],s,e,n)||"";return o}function oe(e,n,o,s){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case _:return e.return=e.return||e.value;case c:return"";case C:return e.return=e.value+"{"+ne(e.children,s)+"}";case m:if(!R(e.value=e.props.join(",")))return""}return R(o=ne(e.children,s))?e.return=e.value+"{"+o+"}":""}function se(e,n,o,s){if(e.length>-1&&!e.return)switch(e.type){case _:return void(e.return=ee(e.value,e.length,o));case C:return ne([F(e,{value:A(e.value,"@","@"+l)})],s);case m:if(e.length)return function(e,n){return e.map(n).join("")}(o=e.props,(function(n){switch(E(n,s=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":w(F(e,{props:[A(n,/:(read-\w+)/,":-moz-$1")]})),w(F(e,{props:[n]})),S(e,{props:U(o,s)});break;case"::placeholder":w(F(e,{props:[A(n,/:(plac\w+)/,":"+l+"input-$1")]})),w(F(e,{props:[A(n,/:(plac\w+)/,":-moz-$1")]})),w(F(e,{props:[A(n,/:(plac\w+)/,d+"input-$1")]})),w(F(e,{props:[n]})),S(e,{props:U(o,s)})}return""}))}}var re={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},ae=o(65606),ie=void 0!==ae&&void 0!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}&&({ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.REACT_APP_SC_ATTR||{ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.SC_ATTR)||"data-styled",te="active",de="data-styled-version",pe="6.1.18",le="/*!sc*/\n",ce="undefined"!=typeof window&&"undefined"!=typeof document,me=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:void 0!==ae&&void 0!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}&&void 0!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.REACT_APP_SC_DISABLE_SPEEDY&&""!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.REACT_APP_SC_DISABLE_SPEEDY?"false"!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.REACT_APP_SC_DISABLE_SPEEDY&&{ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.REACT_APP_SC_DISABLE_SPEEDY:void 0!==ae&&void 0!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}&&void 0!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.SC_DISABLE_SPEEDY&&""!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.SC_DISABLE_SPEEDY&&"false"!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.SC_DISABLE_SPEEDY&&{ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.SC_DISABLE_SPEEDY),_e={},Ce=(new Set,Object.freeze([])),Pe=Object.freeze({});function ue(e,n,o){return void 0===o&&(o=Pe),e.theme!==o.theme&&e.theme||n||o.theme}var Se=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),ge=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,Ee=/(^-|-$)/g;function Ae(e){return e.replace(ge,"-").replace(Ee,"")}var fe=/(a)(d)/gi,De=52,Oe=function(e){return String.fromCharCode(e+(e>25?39:97))};function Re(e){var n,o="";for(n=Math.abs(e);n>De;n=n/De|0)o=Oe(n%De)+o;return(Oe(n%De)+o).replace(fe,"$1-$2")}var he,Ie=function(e,n){for(var o=n.length;o;)e=33*e^n.charCodeAt(--o);return e},Ue=function(e){return Ie(5381,e)};function Te(e){return Re(Ue(e)>>>0)}function Ne(e){return"string"==typeof e&&!0}var be="function"==typeof Symbol&&Symbol.for,ye=be?Symbol.for("react.memo"):60115,Le=be?Symbol.for("react.forward_ref"):60112,ve={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},We={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Fe={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},we=((he={})[Le]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},he[ye]=Fe,he);function Me(e){return("type"in(n=e)&&n.type.$$typeof)===ye?Fe:"$$typeof"in e?we[e.$$typeof]:ve;var n}var Ge=Object.defineProperty,xe=Object.getOwnPropertyNames,je=Object.getOwnPropertySymbols,He=Object.getOwnPropertyDescriptor,ke=Object.getPrototypeOf,Ve=Object.prototype;function Ke(e,n,o){if("string"!=typeof n){if(Ve){var s=ke(n);s&&s!==Ve&&Ke(e,s,o)}var r=xe(n);je&&(r=r.concat(je(n)));for(var a=Me(e),i=Me(n),t=0;t<r.length;++t){var d=r[t];if(!(d in We||o&&o[d]||i&&d in i||a&&d in a)){var p=He(n,d);try{Ge(e,d,p)}catch(e){}}}}return e}function Be(e){return"function"==typeof e}function Xe(e){return"object"==typeof e&&"styledComponentId"in e}function Je(e,n){return e&&n?"".concat(e," ").concat(n):e||n||""}function $e(e,n){if(0===e.length)return"";for(var o=e[0],s=1;s<e.length;s++)o+=n?n+e[s]:e[s];return o}function Ye(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function ze(e,n,o){if(void 0===o&&(o=!1),!o&&!Ye(e)&&!Array.isArray(e))return n;if(Array.isArray(n))for(var s=0;s<n.length;s++)e[s]=ze(e[s],n[s]);else if(Ye(n))for(var s in n)e[s]=ze(e[s],n[s]);return e}function Ze(e,n){Object.defineProperty(e,"toString",{value:n})}function qe(e){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(n.length>0?" Args: ".concat(n.join(", ")):""))}var Qe=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var n=0,o=0;o<e;o++)n+=this.groupSizes[o];return n},e.prototype.insertRules=function(e,n){if(e>=this.groupSizes.length){for(var o=this.groupSizes,s=o.length,r=s;e>=r;)if((r<<=1)<0)throw qe(16,"".concat(e));this.groupSizes=new Uint32Array(r),this.groupSizes.set(o),this.length=r;for(var a=s;a<r;a++)this.groupSizes[a]=0}for(var i=this.indexOfGroup(e+1),t=(a=0,n.length);a<t;a++)this.tag.insertRule(i,n[a])&&(this.groupSizes[e]++,i++)},e.prototype.clearGroup=function(e){if(e<this.length){var n=this.groupSizes[e],o=this.indexOfGroup(e),s=o+n;this.groupSizes[e]=0;for(var r=o;r<s;r++)this.tag.deleteRule(o)}},e.prototype.getGroup=function(e){var n="";if(e>=this.length||0===this.groupSizes[e])return n;for(var o=this.groupSizes[e],s=this.indexOfGroup(e),r=s+o,a=s;a<r;a++)n+="".concat(this.tag.getRule(a)).concat(le);return n},e}(),en=new Map,nn=new Map,on=1,sn=function(e){if(en.has(e))return en.get(e);for(;nn.has(on);)on++;var n=on++;return en.set(e,n),nn.set(n,e),n},rn=function(e,n){on=n+1,en.set(e,n),nn.set(n,e)},an="style[".concat(ie,"][").concat(de,'="').concat(pe,'"]'),tn=new RegExp("^".concat(ie,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),dn=function(e,n,o){for(var s,r=o.split(","),a=0,i=r.length;a<i;a++)(s=r[a])&&e.registerName(n,s)},pn=function(e,n){for(var o,s=(null!==(o=n.textContent)&&void 0!==o?o:"").split(le),r=[],a=0,i=s.length;a<i;a++){var t=s[a].trim();if(t){var d=t.match(tn);if(d){var p=0|parseInt(d[1],10),l=d[2];0!==p&&(rn(l,p),dn(e,l,d[3]),e.getTag().insertRules(p,r)),r.length=0}else r.push(t)}}},ln=function(e){for(var n=document.querySelectorAll(an),o=0,s=n.length;o<s;o++){var r=n[o];r&&r.getAttribute(ie)!==te&&(pn(e,r),r.parentNode&&r.parentNode.removeChild(r))}};function cn(){return o.nc}var mn=function(e){var n=document.head,o=e||n,s=document.createElement("style"),r=function(e){var n=Array.from(e.querySelectorAll("style[".concat(ie,"]")));return n[n.length-1]}(o),a=void 0!==r?r.nextSibling:null;s.setAttribute(ie,te),s.setAttribute(de,pe);var i=cn();return i&&s.setAttribute("nonce",i),o.insertBefore(s,a),s},_n=function(){function e(e){this.element=mn(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var n=document.styleSheets,o=0,s=n.length;o<s;o++){var r=n[o];if(r.ownerNode===e)return r}throw qe(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,n){try{return this.sheet.insertRule(n,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var n=this.sheet.cssRules[e];return n&&n.cssText?n.cssText:""},e}(),Cn=function(){function e(e){this.element=mn(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,n){if(e<=this.length&&e>=0){var o=document.createTextNode(n);return this.element.insertBefore(o,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),Pn=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,n){return e<=this.length&&(this.rules.splice(e,0,n),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),un=ce,Sn={isServer:!ce,useCSSOMInjection:!me},gn=function(){function e(e,n,o){void 0===e&&(e=Pe),void 0===n&&(n={});var r=this;this.options=s(s({},Sn),e),this.gs=n,this.names=new Map(o),this.server=!!e.isServer,!this.server&&ce&&un&&(un=!1,ln(this)),Ze(this,(function(){return function(e){for(var n=e.getTag(),o=n.length,s="",r=function(o){var r=function(e){return nn.get(e)}(o);if(void 0===r)return"continue";var a=e.names.get(r),i=n.getGroup(o);if(void 0===a||!a.size||0===i.length)return"continue";var t="".concat(ie,".g").concat(o,'[id="').concat(r,'"]'),d="";void 0!==a&&a.forEach((function(e){e.length>0&&(d+="".concat(e,","))})),s+="".concat(i).concat(t,'{content:"').concat(d,'"}').concat(le)},a=0;a<o;a++)r(a);return s}(r)}))}return e.registerId=function(e){return sn(e)},e.prototype.rehydrate=function(){!this.server&&ce&&ln(this)},e.prototype.reconstructWithOptions=function(n,o){return void 0===o&&(o=!0),new e(s(s({},this.options),n),this.gs,o&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var n=e.useCSSOMInjection,o=e.target;return e.isServer?new Pn(o):n?new _n(o):new Cn(o)}(this.options),new Qe(e)));var e},e.prototype.hasNameForId=function(e,n){return this.names.has(e)&&this.names.get(e).has(n)},e.prototype.registerName=function(e,n){if(sn(e),this.names.has(e))this.names.get(e).add(n);else{var o=new Set;o.add(n),this.names.set(e,o)}},e.prototype.insertRules=function(e,n,o){this.registerName(e,n),this.getTag().insertRules(sn(e),o)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(sn(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),En=/&/g,An=/^\s*\/\/.*$/gm;function fn(e,n){return e.map((function(e){return"rule"===e.type&&(e.value="".concat(n," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(n," ")),e.props=e.props.map((function(e){return"".concat(n," ").concat(e)}))),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=fn(e.children,n)),e}))}function Dn(e){var n,o,s,r=void 0===e?Pe:e,a=r.options,i=void 0===a?Pe:a,t=r.plugins,d=void 0===t?Ce:t,p=function(e,s,r){return r.startsWith(o)&&r.endsWith(o)&&r.replaceAll(o,"").length>0?".".concat(n):e},l=d.slice();l.push((function(e){e.type===m&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(En,o).replace(s,p))})),i.prefix&&l.push(se),l.push(oe);var c=function(e,r,a,t){void 0===r&&(r=""),void 0===a&&(a=""),void 0===t&&(t="&"),n=t,o=r,s=new RegExp("\\".concat(o,"\\b"),"g");var d=e.replace(An,""),p=Y(a||r?"".concat(a," ").concat(r," { ").concat(d," }"):d);i.namespace&&(p=fn(p,i.namespace));var c,m,_,C=[];return ne(p,(c=l.concat((_=function(e){return C.push(e)},function(e){e.root||(e=e.return)&&_(e)})),m=h(c),function(e,n,o,s){for(var r="",a=0;a<m;a++)r+=c[a](e,n,o,s)||"";return r})),C};return c.hash=d.length?d.reduce((function(e,n){return n.name||qe(15),Ie(e,n.name)}),5381).toString():"",c}var On=new gn,Rn=Dn(),hn=a.createContext({shouldForwardProp:void 0,styleSheet:On,stylis:Rn}),In=(hn.Consumer,a.createContext(void 0));function Un(){return(0,a.useContext)(hn)}function Tn(e){var n=(0,a.useState)(e.stylisPlugins),o=n[0],s=n[1],r=Un().styleSheet,i=(0,a.useMemo)((function(){var n=r;return e.sheet?n=e.sheet:e.target&&(n=n.reconstructWithOptions({target:e.target},!1)),e.disableCSSOMInjection&&(n=n.reconstructWithOptions({useCSSOMInjection:!1})),n}),[e.disableCSSOMInjection,e.sheet,e.target,r]),d=(0,a.useMemo)((function(){return Dn({options:{namespace:e.namespace,prefix:e.enableVendorPrefixes},plugins:o})}),[e.enableVendorPrefixes,e.namespace,o]);(0,a.useEffect)((function(){t()(o,e.stylisPlugins)||s(e.stylisPlugins)}),[e.stylisPlugins]);var p=(0,a.useMemo)((function(){return{shouldForwardProp:e.shouldForwardProp,styleSheet:i,stylis:d}}),[e.shouldForwardProp,i,d]);return a.createElement(hn.Provider,{value:p},a.createElement(In.Provider,{value:d},e.children))}var Nn=function(){function e(e,n){var o=this;this.inject=function(e,n){void 0===n&&(n=Rn);var s=o.name+n.hash;e.hasNameForId(o.id,s)||e.insertRules(o.id,s,n(o.rules,s,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=n,Ze(this,(function(){throw qe(12,String(o.name))}))}return e.prototype.getName=function(e){return void 0===e&&(e=Rn),this.name+e.hash},e}(),bn=function(e){return e>="A"&&e<="Z"};function yn(e){for(var n="",o=0;o<e.length;o++){var s=e[o];if(1===o&&"-"===s&&"-"===e[0])return e;bn(s)?n+="-"+s.toLowerCase():n+=s}return n.startsWith("ms-")?"-"+n:n}var Ln=function(e){return null==e||!1===e||""===e},vn=function(e){var n,o,s=[];for(var a in e){var i=e[a];e.hasOwnProperty(a)&&!Ln(i)&&(Array.isArray(i)&&i.isCss||Be(i)?s.push("".concat(yn(a),":"),i,";"):Ye(i)?s.push.apply(s,r(r(["".concat(a," {")],vn(i),!1),["}"],!1)):s.push("".concat(yn(a),": ").concat((n=a,null==(o=i)||"boolean"==typeof o||""===o?"":"number"!=typeof o||0===o||n in re||n.startsWith("--")?String(o).trim():"".concat(o,"px")),";")))}return s};function Wn(e,n,o,s){return Ln(e)?[]:Xe(e)?[".".concat(e.styledComponentId)]:Be(e)?!Be(r=e)||r.prototype&&r.prototype.isReactComponent||!n?[e]:Wn(e(n),n,o,s):e instanceof Nn?o?(e.inject(o,s),[e.getName(s)]):[e]:Ye(e)?vn(e):Array.isArray(e)?Array.prototype.concat.apply(Ce,e.map((function(e){return Wn(e,n,o,s)}))):[e.toString()];var r}function Fn(e){for(var n=0;n<e.length;n+=1){var o=e[n];if(Be(o)&&!Xe(o))return!1}return!0}var wn=Ue(pe),Mn=function(){function e(e,n,o){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===o||o.isStatic)&&Fn(e),this.componentId=n,this.baseHash=Ie(wn,n),this.baseStyle=o,gn.registerId(n)}return e.prototype.generateAndInjectStyles=function(e,n,o){var s=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,n,o):"";if(this.isStatic&&!o.hash)if(this.staticRulesId&&n.hasNameForId(this.componentId,this.staticRulesId))s=Je(s,this.staticRulesId);else{var r=$e(Wn(this.rules,e,n,o)),a=Re(Ie(this.baseHash,r)>>>0);if(!n.hasNameForId(this.componentId,a)){var i=o(r,".".concat(a),void 0,this.componentId);n.insertRules(this.componentId,a,i)}s=Je(s,a),this.staticRulesId=a}else{for(var t=Ie(this.baseHash,o.hash),d="",p=0;p<this.rules.length;p++){var l=this.rules[p];if("string"==typeof l)d+=l;else if(l){var c=$e(Wn(l,e,n,o));t=Ie(t,c+p),d+=c}}if(d){var m=Re(t>>>0);n.hasNameForId(this.componentId,m)||n.insertRules(this.componentId,m,o(d,".".concat(m),void 0,this.componentId)),s=Je(s,m)}}return s},e}(),Gn=a.createContext(void 0);function xn(e){var n=a.useContext(Gn),o=(0,a.useMemo)((function(){return function(e,n){if(!e)throw qe(14);if(Be(e))return e(n);if(Array.isArray(e)||"object"!=typeof e)throw qe(8);return n?s(s({},n),e):e}(e.theme,n)}),[e.theme,n]);return e.children?a.createElement(Gn.Provider,{value:o},e.children):null}Gn.Consumer;var jn={};function Hn(e,n,o){var r=Xe(e),i=e,t=!Ne(e),d=n.attrs,p=void 0===d?Ce:d,l=n.componentId,c=void 0===l?function(e,n){var o="string"!=typeof e?"sc":Ae(e);jn[o]=(jn[o]||0)+1;var s="".concat(o,"-").concat(Te(pe+o+jn[o]));return n?"".concat(n,"-").concat(s):s}(n.displayName,n.parentComponentId):l,m=n.displayName,_=void 0===m?function(e){return Ne(e)?"styled.".concat(e):"Styled(".concat(function(e){return e.displayName||e.name||"Component"}(e),")")}(e):m,C=n.displayName&&n.componentId?"".concat(Ae(n.displayName),"-").concat(n.componentId):n.componentId||c,P=r&&i.attrs?i.attrs.concat(p).filter(Boolean):p,u=n.shouldForwardProp;if(r&&i.shouldForwardProp){var S=i.shouldForwardProp;if(n.shouldForwardProp){var g=n.shouldForwardProp;u=function(e,n){return S(e,n)&&g(e,n)}}else u=S}var E=new Mn(o,C,r?i.componentStyle:void 0);function A(e,n){return function(e,n,o){var r=e.attrs,i=e.componentStyle,t=e.defaultProps,d=e.foldedComponentIds,p=e.styledComponentId,l=e.target,c=a.useContext(Gn),m=Un(),_=e.shouldForwardProp||m.shouldForwardProp,C=ue(n,c,t)||Pe,P=function(e,n,o){for(var r,a=s(s({},n),{className:void 0,theme:o}),i=0;i<e.length;i+=1){var t=Be(r=e[i])?r(a):r;for(var d in t)a[d]="className"===d?Je(a[d],t[d]):"style"===d?s(s({},a[d]),t[d]):t[d]}return n.className&&(a.className=Je(a.className,n.className)),a}(r,n,C),u=P.as||l,S={};for(var g in P)void 0===P[g]||"$"===g[0]||"as"===g||"theme"===g&&P.theme===C||("forwardedAs"===g?S.as=P.forwardedAs:_&&!_(g,u)||(S[g]=P[g]));var E=function(e,n){var o=Un();return e.generateAndInjectStyles(n,o.styleSheet,o.stylis)}(i,P),A=Je(d,p);return E&&(A+=" "+E),P.className&&(A+=" "+P.className),S[Ne(u)&&!Se.has(u)?"class":"className"]=A,o&&(S.ref=o),(0,a.createElement)(u,S)}(f,e,n)}A.displayName=_;var f=a.forwardRef(A);return f.attrs=P,f.componentStyle=E,f.displayName=_,f.shouldForwardProp=u,f.foldedComponentIds=r?Je(i.foldedComponentIds,i.styledComponentId):"",f.styledComponentId=C,f.target=r?i.target:e,Object.defineProperty(f,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=r?function(e){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];for(var s=0,r=n;s<r.length;s++)ze(e,r[s],!0);return e}({},i.defaultProps,e):e}}),Ze(f,(function(){return".".concat(f.styledComponentId)})),t&&Ke(f,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),f}function kn(e,n){for(var o=[e[0]],s=0,r=n.length;s<r;s+=1)o.push(n[s],e[s+1]);return o}new Set;var Vn=function(e){return Object.assign(e,{isCss:!0})};function Kn(e){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];if(Be(e)||Ye(e))return Vn(Wn(kn(Ce,r([e],n,!0))));var s=e;return 0===n.length&&1===s.length&&"string"==typeof s[0]?Wn(s):Vn(Wn(kn(s,n)))}function Bn(e,n,o){if(void 0===o&&(o=Pe),!n)throw qe(1,n);var a=function(s){for(var a=[],i=1;i<arguments.length;i++)a[i-1]=arguments[i];return e(n,o,Kn.apply(void 0,r([s],a,!1)))};return a.attrs=function(r){return Bn(e,n,s(s({},o),{attrs:Array.prototype.concat(o.attrs,r).filter(Boolean)}))},a.withConfig=function(r){return Bn(e,n,s(s({},o),r))},a}var Xn=function(e){return Bn(Hn,e)},Jn=Xn;Se.forEach((function(e){Jn[e]=Xn(e)}));var $n=function(){function e(e,n){this.rules=e,this.componentId=n,this.isStatic=Fn(e),gn.registerId(this.componentId+1)}return e.prototype.createStyles=function(e,n,o,s){var r=s($e(Wn(this.rules,n,o,s)),""),a=this.componentId+e;o.insertRules(a,a,r)},e.prototype.removeStyles=function(e,n){n.clearRules(this.componentId+e)},e.prototype.renderStyles=function(e,n,o,s){e>2&&gn.registerId(this.componentId+e),this.removeStyles(e,o),this.createStyles(e,n,o,s)},e}();function Yn(e){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];var i=Kn.apply(void 0,r([e],n,!1)),t="sc-global-".concat(Te(JSON.stringify(i))),d=new $n(i,t),p=function(e){var n=Un(),o=a.useContext(Gn),s=a.useRef(n.styleSheet.allocateGSInstance(t)).current;return n.styleSheet.server&&l(s,e,n.styleSheet,o,n.stylis),a.useLayoutEffect((function(){if(!n.styleSheet.server)return l(s,e,n.styleSheet,o,n.stylis),function(){return d.removeStyles(s,n.styleSheet)}}),[s,e,n.styleSheet,o,n.stylis]),null};function l(e,n,o,r,a){if(d.isStatic)d.renderStyles(e,_e,o,a);else{var i=s(s({},n),{theme:ue(n,r,p.defaultProps)});d.renderStyles(e,i,o,a)}}return a.memo(p)}function zn(e){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];var s=$e(Kn.apply(void 0,r([e],n,!1))),a=Te(s);return new Nn(a,s)}(function(){function e(){var e=this;this._emitSheetCSS=function(){var n=e.instance.toString();if(!n)return"";var o=cn(),s=$e([o&&'nonce="'.concat(o,'"'),"".concat(ie,'="true"'),"".concat(de,'="').concat(pe,'"')].filter(Boolean)," ");return"<style ".concat(s,">").concat(n,"</style>")},this.getStyleTags=function(){if(e.sealed)throw qe(2);return e._emitSheetCSS()},this.getStyleElement=function(){var n;if(e.sealed)throw qe(2);var o=e.instance.toString();if(!o)return[];var r=((n={})[ie]="",n[de]=pe,n.dangerouslySetInnerHTML={__html:o},n),i=cn();return i&&(r.nonce=i),[a.createElement("style",s({},r,{key:"sc-0-0"}))]},this.seal=function(){e.sealed=!0},this.instance=new gn({isServer:!0}),this.sealed=!1}e.prototype.collectStyles=function(e){if(this.sealed)throw qe(2);return a.createElement(Tn,{sheet:this.instance},e)},e.prototype.interleaveWithNodeStream=function(e){throw qe(3)}})(),"__sc-".concat(ie,"__")},73700:(e,n,o)=>{"use strict";function s(e,n,o){var s=(o||{}).atBegin;return function(e,n,o){var s,r=o||{},a=r.noTrailing,i=void 0!==a&&a,t=r.noLeading,d=void 0!==t&&t,p=r.debounceMode,l=void 0===p?void 0:p,c=!1,m=0;function _(){s&&clearTimeout(s)}function C(){for(var o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];var t=this,p=Date.now()-m;function C(){m=Date.now(),n.apply(t,r)}function P(){s=void 0}c||(d||!l||s||C(),_(),void 0===l&&p>e?d?(m=Date.now(),i||(s=setTimeout(l?P:C,e))):C():!0!==i&&(s=setTimeout(l?P:C,void 0===l?e-p:e)))}return C.cancel=function(e){var n=(e||{}).upcomingOnly,o=void 0!==n&&n;_(),c=!o},C}(e,n,{debounceMode:!1!==(void 0!==s&&s)})}o.d(n,{s:()=>s})},78418:(e,n,o)=>{"use strict";e.exports=o(85160)},79730:(e,n,o)=>{"use strict";o.d(n,{JK:()=>u});try{self["workbox:window:7.2.0"]&&_()}catch(s){}function s(e,n){return new Promise((function(o){var s=new MessageChannel;s.port1.onmessage=function(e){o(e.data)},e.postMessage(n,[s.port2])}))}function r(e){var n=function(e){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,"string");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof n?n:n+""}function a(e,n){for(var o=0;o<n.length;o++){var s=n[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,r(s.key),s)}}function i(e,n){return i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,n){return e.__proto__=n,e},i(e,n)}function t(e,n){(null==n||n>e.length)&&(n=e.length);for(var o=0,s=new Array(n);o<n;o++)s[o]=e[o];return s}try{self["workbox:core:7.2.0"]&&_()}catch(s){}var d=function(){var e=this;this.promise=new Promise((function(n,o){e.resolve=n,e.reject=o}))};function p(e,n){var o=location.href;return new URL(e,o).href===new URL(n,o).href}var l=function(e,n){this.type=e,Object.assign(this,n)};function c(e,n,o){return o?n?n(e):e:(e&&e.then||(e=Promise.resolve(e)),n?e.then(n):e)}function m(){}var C={type:"SKIP_WAITING"};function P(e,n){if(!n)return e&&e.then?e.then(m):Promise.resolve()}var u=function(e){function n(n,o){var s,r;return void 0===o&&(o={}),(s=e.call(this)||this).nn={},s.tn=0,s.rn=new d,s.en=new d,s.on=new d,s.un=0,s.an=new Set,s.cn=function(){var e=s.fn,n=e.installing;s.tn>0||!p(n.scriptURL,s.sn.toString())||performance.now()>s.un+6e4?(s.vn=n,e.removeEventListener("updatefound",s.cn)):(s.hn=n,s.an.add(n),s.rn.resolve(n)),++s.tn,n.addEventListener("statechange",s.ln)},s.ln=function(e){var n=s.fn,o=e.target,r=o.state,a=o===s.vn,i={sw:o,isExternal:a,originalEvent:e};!a&&s.mn&&(i.isUpdate=!0),s.dispatchEvent(new l(r,i)),"installed"===r?s.wn=self.setTimeout((function(){"installed"===r&&n.waiting===o&&s.dispatchEvent(new l("waiting",i))}),200):"activating"===r&&(clearTimeout(s.wn),a||s.en.resolve(o))},s.yn=function(e){var n=s.hn,o=n!==navigator.serviceWorker.controller;s.dispatchEvent(new l("controlling",{isExternal:o,originalEvent:e,sw:n,isUpdate:s.mn})),o||s.on.resolve(n)},s.gn=(r=function(e){var n=e.data,o=e.ports,r=e.source;return c(s.getSW(),(function(){s.an.has(r)&&s.dispatchEvent(new l("message",{data:n,originalEvent:e,ports:o,sw:r}))}))},function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(r.apply(this,e))}catch(e){return Promise.reject(e)}}),s.sn=n,s.nn=o,navigator.serviceWorker.addEventListener("message",s.gn),s}var o,r;r=e,(o=n).prototype=Object.create(r.prototype),o.prototype.constructor=o,i(o,r);var t,m,_=n.prototype;return _.register=function(e){var n=(void 0===e?{}:e).immediate,o=void 0!==n&&n;try{var s=this;return c(function(e,n){var o=e();return o&&o.then?o.then(n):n()}((function(){if(!o&&"complete"!==document.readyState)return P(new Promise((function(e){return window.addEventListener("load",e)})))}),(function(){return s.mn=Boolean(navigator.serviceWorker.controller),s.dn=s.pn(),c(s.bn(),(function(e){s.fn=e,s.dn&&(s.hn=s.dn,s.en.resolve(s.dn),s.on.resolve(s.dn),s.dn.addEventListener("statechange",s.ln,{once:!0}));var n=s.fn.waiting;return n&&p(n.scriptURL,s.sn.toString())&&(s.hn=n,Promise.resolve().then((function(){s.dispatchEvent(new l("waiting",{sw:n,wasWaitingBeforeRegister:!0}))})).then((function(){}))),s.hn&&(s.rn.resolve(s.hn),s.an.add(s.hn)),s.fn.addEventListener("updatefound",s.cn),navigator.serviceWorker.addEventListener("controllerchange",s.yn),s.fn}))})))}catch(e){return Promise.reject(e)}},_.update=function(){try{return this.fn?c(P(this.fn.update())):c()}catch(e){return Promise.reject(e)}},_.getSW=function(){return void 0!==this.hn?Promise.resolve(this.hn):this.rn.promise},_.messageSW=function(e){try{return c(this.getSW(),(function(n){return s(n,e)}))}catch(e){return Promise.reject(e)}},_.messageSkipWaiting=function(){this.fn&&this.fn.waiting&&s(this.fn.waiting,C)},_.pn=function(){var e=navigator.serviceWorker.controller;return e&&p(e.scriptURL,this.sn.toString())?e:void 0},_.bn=function(){try{var e=this;return c(function(e,n){try{var o=e()}catch(e){return n(e)}return o&&o.then?o.then(void 0,n):o}((function(){return c(navigator.serviceWorker.register(e.sn,e.nn),(function(n){return e.un=performance.now(),n}))}),(function(e){throw e})))}catch(e){return Promise.reject(e)}},t=n,(m=[{key:"active",get:function(){return this.en.promise}},{key:"controlling",get:function(){return this.on.promise}}])&&a(t.prototype,m),Object.defineProperty(t,"prototype",{writable:!1}),t}(function(){function e(){this.Pn=new Map}var n=e.prototype;return n.addEventListener=function(e,n){this.jn(e).add(n)},n.removeEventListener=function(e,n){this.jn(e).delete(n)},n.dispatchEvent=function(e){e.target=this;for(var n,o=function(e){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,n){if(e){if("string"==typeof e)return t(e,n);var o=Object.prototype.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?t(e,n):void 0}}(e))){n&&(e=n);var o=0;return function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(this.jn(e.type));!(n=o()).done;)(0,n.value)(e)},n.jn=function(e){return this.Pn.has(e)||this.Pn.set(e,new Set),this.Pn.get(e)},e}())},85160:(e,n,o)=>{"use strict";var s=o(96540),r="function"==typeof Object.is?Object.is:function(e,n){return e===n&&(0!==e||1/e==1/n)||e!=e&&n!=n},a=s.useSyncExternalStore,i=s.useRef,t=s.useEffect,d=s.useMemo,p=s.useDebugValue;n.useSyncExternalStoreWithSelector=function(e,n,o,s,l){var c=i(null);if(null===c.current){var m={hasValue:!1,value:null};c.current=m}else m=c.current;c=d((function(){function e(e){if(!t){if(t=!0,a=e,e=s(e),void 0!==l&&m.hasValue){var n=m.value;if(l(n,e))return i=n}return i=e}if(n=i,r(a,e))return n;var o=s(e);return void 0!==l&&l(n,o)?(a=e,n):(a=e,i=o)}var a,i,t=!1,d=void 0===o?null:o;return[function(){return e(n())},null===d?void 0:function(){return e(d())}]}),[n,o,s,l]);var _=a(e,c[0],c[1]);return t((function(){m.hasValue=!0,m.value=_}),[_]),p(_),_}}}]);