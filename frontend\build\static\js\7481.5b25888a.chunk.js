"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[7481],{97481:(e,t,a)=>{a.r(t),a.d(t,{default:()=>F});var r=a(10467),n=a(5544),l=a(57528),c=a(54756),o=a.n(c),i=a(96540),s=a(1807),u=a(35346),p=a(70572),m=a(71468),d=(a(48035),{temporary:new Map,persistent:new Map,expiring:new Map,timestamps:new Map});const h=function(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{type:"temporary",expiresIn:36e5},r=a.type,n=a.expiresIn;switch(r){case"persistent":d.persistent.set(e,t);try{localStorage.setItem("cache_".concat(e),JSON.stringify(t))}catch(e){console.warn("Failed to save to localStorage:",e)}break;case"expiring":d.expiring.set(e,t),d.timestamps.set(e,Date.now()+n);break;default:d.temporary.set(e,t)}},g=function(e){switch(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"temporary"){case"persistent":if(d.persistent.has(e))return d.persistent.get(e);try{var t=localStorage.getItem("cache_".concat(e));if(t){var a=JSON.parse(t);return d.persistent.set(e,a),a}}catch(e){console.warn("Failed to retrieve from localStorage:",e)}return null;case"expiring":if(d.expiring.has(e)){var r=d.timestamps.get(e);if(r&&r>Date.now())return d.expiring.get(e);d.expiring.delete(e),d.timestamps.delete(e)}return null;default:return d.temporary.get(e)||null}},v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"temporary";if(e)switch(t){case"persistent":d.persistent.delete(e);try{localStorage.removeItem("cache_".concat(e))}catch(e){console.warn("Failed to remove from localStorage:",e)}break;case"expiring":d.expiring.delete(e),d.timestamps.delete(e);break;case"all":d.temporary.delete(e),d.persistent.delete(e),d.expiring.delete(e),d.timestamps.delete(e);try{localStorage.removeItem("cache_".concat(e))}catch(e){console.warn("Failed to remove from localStorage:",e)}break;default:d.temporary.delete(e)}else switch(t){case"persistent":d.persistent.clear();try{Object.keys(localStorage).forEach((function(e){e.startsWith("cache_")&&localStorage.removeItem(e)}))}catch(e){console.warn("Failed to clear localStorage:",e)}break;case"expiring":d.expiring.clear(),d.timestamps.clear();break;case"all":d.temporary.clear(),d.persistent.clear(),d.expiring.clear(),d.timestamps.clear();try{Object.keys(localStorage).forEach((function(e){e.startsWith("cache_")&&localStorage.removeItem(e)}))}catch(e){console.warn("Failed to clear localStorage:",e)}break;default:d.temporary.clear()}};var f,y,E,x,w,b=a(34816),C=s.o5.Title,k=s.o5.Text,S=s.o5.Paragraph,A=s.l6.Option,I=p.Ay.div(f||(f=(0,l.A)(["\n  padding: 20px;\n"]))),T=(0,p.Ay)(s.Zp)(y||(y=(0,l.A)(["\n  margin-bottom: 20px;\n"]))),D=p.Ay.div(E||(E=(0,l.A)(["\n  margin-bottom: 16px;\n  \n  .label {\n    display: block;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n"]))),_=p.Ay.div(x||(x=(0,l.A)(["\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n"]))),N=(0,p.Ay)(s.Fc)(w||(w=(0,l.A)(["\n  margin-bottom: 16px;\n"])));const F=function(){var e=(0,i.useState)(""),t=(0,n.A)(e,2),a=t[0],l=t[1],c=(0,i.useState)("temporary"),p=(0,n.A)(c,2),d=p[0],f=p[1],y=(0,i.useState)(!0),E=(0,n.A)(y,2),x=E[0],w=E[1],F=function(e){var t=e.selector,a=e.action,l=e.cacheKey,c=e.cacheType,s=void 0===c?"temporary":c,u=e.expiresIn,p=void 0===u?36e5:u,d=e.useCache,f=void 0===d||d,y=e.defaultValue,E=void 0===y?null:y,x=(0,m.wA)(),w=(0,i.useState)(!1),b=(0,n.A)(w,2),C=b[0],k=b[1],S=(0,i.useState)(null),A=(0,n.A)(S,2),I=A[0],T=A[1],D=t?(0,m.d4)(t):void 0,_=(0,i.useCallback)((function(){return f&&l?g(l,s):null}),[f,l,s]),N=(0,i.useCallback)((function(e){f&&l&&h(l,e,{type:s,expiresIn:p})}),[f,l,s,p]),F=(0,i.useCallback)((function(){l&&v(l,s)}),[l,s]),M=(0,i.useCallback)(function(){var e=(0,r.A)(o().mark((function e(t){var r;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a){e.next=2;break}return e.abrupt("return");case 2:return k(!0),T(null),e.prev=4,e.next=7,x(a(t));case 7:return r=e.sent,f&&l&&N((null==r?void 0:r.payload)||r),k(!1),e.abrupt("return",r);case 13:throw e.prev=13,e.t0=e.catch(4),T(e.t0),k(!1),e.t0;case 18:case"end":return e.stop()}}),e,null,[[4,13]])})));return function(t){return e.apply(this,arguments)}}(),[a,x,f,l,N]),V=(0,i.useCallback)((function(){if(void 0!==D)return D;var e=_();return null!==e?e:E}),[D,_,E]),O=(0,i.useCallback)((0,r.A)(o().mark((function e(){var t;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a){e.next=2;break}return e.abrupt("return");case 2:return k(!0),T(null),e.prev=4,e.next=7,x(a());case 7:return t=e.sent,f&&l&&N((null==t?void 0:t.payload)||t),k(!1),e.abrupt("return",t);case 13:throw e.prev=13,e.t0=e.catch(4),T(e.t0),k(!1),e.t0;case 18:case"end":return e.stop()}}),e,null,[[4,13]])}))),[a,x,f,l,N]);return(0,i.useEffect)((function(){f&&l&&void 0!==D&&!_()&&N(D)}),[f,l,D,_,N]),{data:V(),loading:C,error:I,updateData:M,refreshData:O,clearCache:F}}({selector:function(e){var t;return null===(t=e.ui)||void 0===t?void 0:t.currentView},action:b.setCurrentView,cacheKey:"current_view",cacheType:d,useCache:x,defaultValue:"components"}),M=F.data,V=F.loading,O=F.error,$=F.updateData,R=F.refreshData,K=F.clearCache,P=function(){var e=(0,r.A)(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a.trim()){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,$(a);case 5:l(""),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(2),console.error("Error saving data:",e.t0);case 11:case"end":return e.stop()}}),e,null,[[2,8]])})));return function(){return e.apply(this,arguments)}}(),j=function(){var e=(0,r.A)(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,R();case 3:e.next=8;break;case 5:e.prev=5,e.t0=e.catch(0),console.error("Error refreshing data:",e.t0);case 8:case"end":return e.stop()}}),e,null,[[0,5]])})));return function(){return e.apply(this,arguments)}}();return i.createElement(I,null,i.createElement(C,{level:3},"Data Management Demo"),i.createElement(S,null,"This demo shows how to use the data management utilities to optimize Redux usage and handle data operations."),i.createElement(T,{title:"Current View Data"},i.createElement(N,{type:"info",message:"Redux + Cache Integration",description:"This example shows how to use the useDataManager hook to manage data with Redux and caching.",icon:i.createElement(u.rUN,null)}),V?i.createElement("div",{style:{textAlign:"center",padding:"20px"}},i.createElement(s.tK,{size:"large"}),i.createElement("div",{style:{marginTop:"10px"}},"Loading data...")):i.createElement(i.Fragment,null,O&&i.createElement(s.Fc,{type:"error",message:"Error",description:O.message||"An error occurred while managing data",style:{marginBottom:"16px"}}),i.createElement(D,null,i.createElement("div",{className:"label"},"Current View:"),i.createElement(k,{strong:!0},M)),i.createElement(D,null,i.createElement("div",{className:"label"},"New View:"),i.createElement(s.pd,{value:a,onChange:function(e){return l(e.target.value)},placeholder:"Enter a new view name"})),i.createElement(D,null,i.createElement("div",{className:"label"},"Cache Type:"),i.createElement(s.l6,{value:d,onChange:f,style:{width:"100%"}},i.createElement(A,{value:"temporary"},"Temporary (Memory only)"),i.createElement(A,{value:"persistent"},"Persistent (LocalStorage)"),i.createElement(A,{value:"expiring"},"Expiring (Time-based)"))),i.createElement(D,null,i.createElement("div",{className:"label"},"Use Cache:"),i.createElement(s.dO,{checked:x,onChange:w})),i.createElement(_,null,i.createElement(s.$n,{type:"primary",icon:i.createElement(u.ylI,null),onClick:P,disabled:!a.trim()},"Save to Redux"),i.createElement(s.$n,{icon:i.createElement(u.KF4,null),onClick:j},"Refresh Data"),i.createElement(s.$n,{danger:!0,icon:i.createElement(u.ohj,null),onClick:function(){K()}},"Clear Cache")))),i.createElement(T,{title:"Direct Cache Operations"},i.createElement(N,{type:"info",message:"Direct Cache API",description:"This example shows how to use the dataManager utility directly for caching operations.",icon:i.createElement(u.rUN,null)}),i.createElement(D,null,i.createElement("div",{className:"label"},"Cache Value:"),i.createElement(s.pd,{value:a,onChange:function(e){return l(e.target.value)},placeholder:"Enter a value to cache"})),i.createElement(D,null,i.createElement("div",{className:"label"},"Cache Type:"),i.createElement(s.l6,{value:d,onChange:f,style:{width:"100%"}},i.createElement(A,{value:"temporary"},"Temporary (Memory only)"),i.createElement(A,{value:"persistent"},"Persistent (LocalStorage)"),i.createElement(A,{value:"expiring"},"Expiring (Time-based)"))),i.createElement(_,null,i.createElement(s.$n,{type:"primary",onClick:function(){a.trim()&&(h("direct_cache_demo",a,{type:d,expiresIn:36e5}),l(""))},disabled:!a.trim()},"Set Cache"),i.createElement(s.$n,{onClick:function(){var e=g("direct_cache_demo",d);l(e||"")}},"Get Cache"),i.createElement(s.$n,{danger:!0,onClick:function(){return v("direct_cache_demo",d)}},"Clear Cache"))))}}}]);