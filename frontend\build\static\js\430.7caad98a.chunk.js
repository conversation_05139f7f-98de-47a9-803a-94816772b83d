"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[430],{3430:(e,t,n)=>{n.r(t),n.d(t,{default:()=>J});var l=n(4467),a=n(5544),r=n(6540),o=n(1468),c=n(3016),i=n(4358),s=n(2395),u=n(9740),m=n(677),p=n(6914),d=n(2702),y=n(7977),A=n(9249),E=n(7152),f=n(6370),v=n(7355),g=n(7197),b=n(2652),h=n(6044),w=n(8602),S=n(234),C=n(3903),k=n(756),P=n(9237),x=n(6008),O=n(261),I=n(3598),L=n(5132),T=n(1616);function j(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function D(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?j(Object(n),!0).forEach((function(t){(0,l.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}n(7053);var z=c.A.Title,B=c.A.Text,N=i.A.Option,M=s.A.TabPane;const J=function(){var e=(0,o.wA)(),t=(0,o.d4)((function(e){var t;return(null===(t=e.app)||void 0===t?void 0:t.components)||[]})),n=(0,o.d4)((function(e){var t;return(null===(t=e.app)||void 0===t?void 0:t.layouts)||[]})),l=(0,o.d4)((function(e){var t;return null===(t=e.websocket)||void 0===t?void 0:t.connected})),c=(0,r.useState)(""),j=(0,a.A)(c,2),J=j[0],R=j[1],F=(0,r.useState)("Button"),G=(0,a.A)(F,2),Y=G[0],H=G[1],V=(0,r.useState)({}),q=(0,a.A)(V,2),K=q[0],U=q[1],X=(0,r.useState)("Grid"),Z=(0,a.A)(X,2),$=Z[0],Q=Z[1],W=(0,r.useState)("builder"),_=(0,a.A)(W,2),ee=_[0],te=_[1],ne=(0,r.useState)(!1),le=(0,a.A)(ne,2),ae=le[0],re=le[1],oe=(0,r.useState)(null),ce=(0,a.A)(oe,2),ie=(ce[0],ce[1]),se=(0,r.useState)(!1),ue=(0,a.A)(se,2),me=(ue[0],ue[1]),pe=(0,r.useState)(!1),de=(0,a.A)(pe,2),ye=(de[0],de[1]),Ae=(0,r.useState)("My App"),Ee=(0,a.A)(Ae,2),fe=Ee[0],ve=(Ee[1],(0,r.useState)("Built with App Builder MVP")),ge=(0,a.A)(ve,2),be=ge[0],he=(ge[1],[{value:"Button",label:"Button",icon:"🔘",defaultProps:{text:"Click me",type:"primary",size:"medium"}},{value:"Text",label:"Text",icon:"📝",defaultProps:{content:"Sample text",size:"medium",color:"#000000"}},{value:"Input",label:"Input Field",icon:"📝",defaultProps:{placeholder:"Enter text...",type:"text",required:!1}},{value:"Image",label:"Image",icon:"🖼️",defaultProps:{src:"https://via.placeholder.com/150",alt:"Image",width:150}},{value:"Card",label:"Card",icon:"🃏",defaultProps:{title:"Card Title",content:"Card content goes here"}},{value:"List",label:"List",icon:"📋",defaultProps:{items:["Item 1","Item 2","Item 3"],type:"unordered"}}]),we=[{value:"Grid",label:"Grid Layout",icon:"⚏",defaultProps:{columns:3,gap:"16px",responsive:!0}},{value:"Flex",label:"Flex Layout",icon:"↔️",defaultProps:{direction:"row",justify:"center",align:"center"}},{value:"Stack",label:"Stack Layout",icon:"📚",defaultProps:{spacing:"16px",align:"center",direction:"vertical"}}];(0,r.useEffect)((function(){var e=he.find((function(e){return e.value===Y}));e&&U(e.defaultProps)}),[Y]),(0,r.useEffect)((function(){l&&u.Ay.success("Real-time connection established")}),[l]);var Se=function(){if(J.trim()){var t=he.find((function(e){return e.value===Y})),n={id:"".concat(Y.toLowerCase(),"-").concat(Date.now()),type:Y,name:J,props:D(D(D({},t.defaultProps),K),{},{name:J}),created:(new Date).toISOString()};e(T.X8(n)),R(""),u.Ay.success("Added ".concat(Y," component: ").concat(J))}else u.Ay.error("Please enter a component name")};return r.createElement("div",{className:"app-builder-mvp"},r.createElement(m.A,{title:r.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},r.createElement("div",null,r.createElement(z,{level:3,style:{margin:0}},fe," - App Builder MVP"),r.createElement(B,{type:"secondary"},be)),r.createElement("div",{className:"connection-status"},l?r.createElement(p.A,{color:"green",icon:"●"},"Real-time connected"):r.createElement(p.A,{color:"orange",icon:"●"},"HTTP fallback"))),bordered:!1,extra:r.createElement(d.A,null,r.createElement(y.A,{title:"App Settings"},r.createElement(A.Ay,{icon:r.createElement(w.A,null),onClick:function(){return ye(!0)}})),r.createElement(y.A,{title:ae?"Exit Preview":"Preview App"},r.createElement(A.Ay,{icon:r.createElement(S.A,null),type:ae?"primary":"default",onClick:function(){re(!ae),u.Ay.info(ae?"Exited preview mode":"Entered preview mode")}})),r.createElement(y.A,{title:"Save App"},r.createElement(A.Ay,{icon:r.createElement(C.A,null),type:"primary",onClick:function(){var l={name:fe,description:be,components:t,layouts:n,styles:{},data:{},metadata:{version:"1.0.0",created:(new Date).toISOString(),componentCount:t.length,layoutCount:n.length}};e(T.ZL(l)),u.Ay.success("App data saved successfully")}})),r.createElement(y.A,{title:"Export App"},r.createElement(A.Ay,{icon:r.createElement(k.A,null),onClick:function(){var e={name:fe,description:be,components:t,layouts:n,exportedAt:(new Date).toISOString()},l=JSON.stringify(e,null,2),a="data:application/json;charset=utf-8,"+encodeURIComponent(l),r="".concat(fe.replace(/\s+/g,"-").toLowerCase(),"-app.json"),o=document.createElement("a");o.setAttribute("href",a),o.setAttribute("download",r),o.click(),u.Ay.success("App exported successfully")}})))},r.createElement(s.A,{activeKey:ee,onChange:te},r.createElement(M,{tab:r.createElement("span",null,r.createElement(P.A,null),"Builder"),key:"builder"},r.createElement(m.A,{title:"Add Components",size:"small",style:{marginBottom:16}},r.createElement(E.A,{gutter:[16,16]},r.createElement(f.A,{span:8},r.createElement(v.A,{placeholder:"Component Name",value:J,onChange:function(e){return R(e.target.value)},onPressEnter:Se})),r.createElement(f.A,{span:8},r.createElement(i.A,{value:Y,onChange:H,style:{width:"100%"},placeholder:"Select component type"},he.map((function(e){return r.createElement(N,{key:e.value,value:e.value},e.icon," ",e.label)})))),r.createElement(f.A,{span:8},r.createElement(A.Ay,{type:"primary",icon:r.createElement(P.A,null),onClick:Se,block:!0},"Add Component")))),r.createElement(m.A,{title:"Add Layouts",size:"small",style:{marginBottom:16}},r.createElement(E.A,{gutter:[16,16]},r.createElement(f.A,{span:16},r.createElement(i.A,{value:$,onChange:Q,style:{width:"100%"},placeholder:"Select layout type"},we.map((function(e){return r.createElement(N,{key:e.value,value:e.value},e.icon," ",e.label)})))),r.createElement(f.A,{span:8},r.createElement(A.Ay,{type:"primary",icon:r.createElement(P.A,null),onClick:function(){var t=we.find((function(e){return e.value===$})),n={id:"".concat($.toLowerCase(),"-").concat(Date.now()),type:$,components:[],props:t.defaultProps,created:(new Date).toISOString()};e(T.S7(n.type,n.components,n.props)),u.Ay.success("Added ".concat($," layout"))},block:!0},"Add Layout"))))),r.createElement(M,{tab:r.createElement("span",null,r.createElement(x.A,null),"Components"),key:"components"},r.createElement(m.A,{title:"Components (".concat(t.length,")"),size:"small"},0===t.length?r.createElement(g.A,{message:"No components added yet",description:"Start building your app by adding components in the Builder tab.",type:"info",showIcon:!0}):r.createElement(b.A,{dataSource:t,renderItem:function(t,n){var l;return r.createElement(b.A.Item,{key:t.id||"component-".concat(n),actions:[r.createElement(y.A,{title:"Edit"},r.createElement(A.Ay,{icon:r.createElement(O.A,null),size:"small",onClick:function(){return function(e){ie(e),me(!0)}(t)}})),r.createElement(h.A,{title:"Delete this component?",onConfirm:function(){return n=t.id,e(T.$5(n)),void u.Ay.success("Component deleted");var n},okText:"Yes",cancelText:"No"},r.createElement(y.A,{title:"Delete"},r.createElement(A.Ay,{icon:r.createElement(I.A,null),size:"small",danger:!0})))]},r.createElement(b.A.Item.Meta,{title:r.createElement(d.A,null,r.createElement(p.A,{color:"blue"},t.type),r.createElement(B,{strong:!0},t.name||(null===(l=t.props)||void 0===l?void 0:l.name))),description:r.createElement(B,{type:"secondary"},JSON.stringify(t.props,null,2).substring(0,100),JSON.stringify(t.props).length>100?"...":"")}))}}))),r.createElement(M,{tab:r.createElement("span",null,r.createElement(w.A,null),"Layouts"),key:"layouts"},r.createElement(m.A,{title:"Layouts (".concat(n.length,")"),size:"small"},0===n.length?r.createElement(g.A,{message:"No layouts added yet",description:"Add layouts to organize your components in the Builder tab.",type:"info",showIcon:!0}):r.createElement(b.A,{dataSource:n,renderItem:function(e,t){return r.createElement(b.A.Item,{key:e.id||"layout-".concat(t),actions:[r.createElement(y.A,{title:"Edit"},r.createElement(A.Ay,{icon:r.createElement(O.A,null),size:"small"})),r.createElement(h.A,{title:"Delete this layout?",onConfirm:function(){u.Ay.success("Layout deleted")},okText:"Yes",cancelText:"No"},r.createElement(y.A,{title:"Delete"},r.createElement(A.Ay,{icon:r.createElement(I.A,null),size:"small",danger:!0})))]},r.createElement(b.A.Item.Meta,{title:r.createElement(d.A,null,r.createElement(p.A,{color:"green"},e.type),r.createElement(B,{strong:!0},"Layout ",t+1)),description:r.createElement(B,{type:"secondary"},JSON.stringify(e.styles||e.props,null,2).substring(0,100),JSON.stringify(e.styles||e.props).length>100?"...":"")}))}}))),r.createElement(M,{tab:r.createElement("span",null,r.createElement(L.A,null),"Preview"),key:"preview"},r.createElement(m.A,{title:"App Preview",size:"small"},r.createElement(g.A,{message:"Preview Mode",description:"This is a simplified preview of your app. In a full implementation, this would render your actual components and layouts.",type:"info",showIcon:!0,style:{marginBottom:16}}),r.createElement("div",{style:{border:"2px dashed #d9d9d9",borderRadius:"6px",padding:"24px",textAlign:"center",minHeight:"300px",backgroundColor:"#fafafa"}},r.createElement(z,{level:4},"Your App Preview"),r.createElement(B,{type:"secondary"},"Components: ",t.length," | Layouts: ",n.length),t.length>0&&r.createElement("div",{style:{marginTop:16}},r.createElement(B,{strong:!0},"Components in your app:"),r.createElement("div",{style:{marginTop:8}},t.map((function(e,t){var n;return r.createElement(p.A,{key:t,style:{margin:4}},e.type,": ",e.name||(null===(n=e.props)||void 0===n?void 0:n.name))})))),n.length>0&&r.createElement("div",{style:{marginTop:16}},r.createElement(B,{strong:!0},"Layouts in your app:"),r.createElement("div",{style:{marginTop:8}},n.map((function(e,t){return r.createElement(p.A,{key:t,color:"green",style:{margin:4}},e.type," Layout")}))))))))))}}}]);