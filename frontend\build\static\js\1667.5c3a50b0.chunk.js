"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[1667],{71667:(e,t,n)=>{n.r(t),n.d(t,{default:()=>z});var r,a,o,l,i,c,s,d,u,m,p=n(64467),y=n(5544),g=n(57528),f=n(96540),E=n(71468),h=n(1807),v=n(35346),b=n(81616),F=n(4318),C=n(79146),A=n(86020);function x(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?x(Object(n),!0).forEach((function(t){(0,p.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):x(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var w=C.styled.div(r||(r=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),A.Ay.spacing[4]),T=C.styled.div(a||(a=(0,g.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: ",";\n"])),A.Ay.spacing[4]),B=C.styled.div(o||(o=(0,g.A)(["\n  display: flex;\n  align-items: center;\n  gap: ",";\n\n  .color-preview {\n    width: 36px;\n    height: 36px;\n    border-radius: ",";\n    border: 1px solid ",';\n    overflow: hidden;\n    position: relative;\n\n    input[type="color"] {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      border: none;\n      padding: 0;\n      margin: 0;\n      cursor: pointer;\n    }\n  }\n\n  .color-input {\n    flex: 1;\n  }\n'])),A.Ay.spacing[2],A.Ay.borderRadius.md,A.Ay.colors.neutral[300]),S=C.styled.div(l||(l=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n\n  .font-preview {\n    padding: ",";\n    border: 1px solid ",";\n    border-radius: ",";\n    min-height: 60px;\n  }\n"])),A.Ay.spacing[2],A.Ay.spacing[2],A.Ay.colors.neutral[300],A.Ay.borderRadius.md),D=C.styled.div(i||(i=(0,g.A)(["\n  padding: ",";\n  border-radius: ",";\n  background-color: ",";\n  color: ",";\n  font-family: ",";\n  transition: all 0.3s ease;\n\n  h3 {\n    margin-top: 0;\n    margin-bottom: ",";\n    color: ",";\n  }\n\n  p {\n    margin-bottom: ",";\n  }\n\n  .buttons {\n    display: flex;\n    gap: ",";\n  }\n\n  .primary-button {\n    padding: "," ",";\n    background-color: ",";\n    color: white;\n    border: none;\n    border-radius: ",";\n    cursor: pointer;\n    transition: all 0.2s ease;\n\n    &:hover {\n      opacity: 0.9;\n      transform: translateY(-2px);\n    }\n  }\n\n  .secondary-button {\n    padding: "," ",";\n    background-color: ",";\n    color: white;\n    border: none;\n    border-radius: ",";\n    cursor: pointer;\n    transition: all 0.2s ease;\n\n    &:hover {\n      opacity: 0.9;\n      transform: translateY(-2px);\n    }\n  }\n\n  .card-example {\n    margin-top: ",";\n    padding: ",";\n    border-radius: ",";\n    background-color: ",";\n    border: 1px solid ",";\n  }\n\n  .input-example {\n    margin-top: ",";\n    padding: ",";\n    border-radius: ",";\n    border: 1px solid ",";\n    background-color: ",";\n    color: ",";\n    width: 100%;\n    font-family: ",";\n  }\n"])),A.Ay.spacing[4],A.Ay.borderRadius.md,(function(e){return e.backgroundColor||"white"}),(function(e){return e.textColor||"black"}),(function(e){return e.fontFamily||"inherit"}),A.Ay.spacing[3],(function(e){return e.textColor||"black"}),A.Ay.spacing[3],A.Ay.spacing[2],A.Ay.spacing[2],A.Ay.spacing[3],(function(e){return e.primaryColor||A.Ay.colors.primary.main}),A.Ay.borderRadius.md,A.Ay.spacing[2],A.Ay.spacing[3],(function(e){return e.secondaryColor||A.Ay.colors.secondary.main}),A.Ay.borderRadius.md,A.Ay.spacing[3],A.Ay.spacing[3],A.Ay.borderRadius.md,(function(e){return"#FFFFFF"===e.backgroundColor?"#F9FAFB":"rgba(255, 255, 255, 0.1)"}),(function(e){return"#FFFFFF"===e.backgroundColor?"#E5E7EB":"rgba(255, 255, 255, 0.2)"}),A.Ay.spacing[3],A.Ay.spacing[2],A.Ay.borderRadius.sm,(function(e){return"#FFFFFF"===e.backgroundColor?"#D1D5DB":"rgba(255, 255, 255, 0.2)"}),(function(e){return"#FFFFFF"===e.backgroundColor?"white":"rgba(255, 255, 255, 0.05)"}),(function(e){return e.textColor}),(function(e){return e.fontFamily})),I=(C.styled.div(c||(c=(0,g.A)(["\n  display: flex;\n  gap: ",";\n  margin-top: ",";\n\n  .color-swatch {\n    width: 24px;\n    height: 24px;\n    border-radius: 50%;\n    border: 1px solid ",";\n    cursor: pointer;\n  }\n"])),A.Ay.spacing[2],A.Ay.spacing[2],A.Ay.colors.neutral[300]),C.styled.div(s||(s=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),A.Ay.spacing[3])),O=C.styled.div(d||(d=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),A.Ay.spacing[2]),P=C.styled.div(u||(u=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ",";\n  background-color: ",";\n  border-radius: ",";\n  text-align: center;\n"])),A.Ay.spacing[8],A.Ay.colors.neutral[100],A.Ay.borderRadius.md),W=(0,C.styled)(C.Card)(m||(m=(0,g.A)(["\n  border: ",";\n  transition: ",";\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ",";\n  }\n"])),(function(e){return e.isActive?"2px solid ".concat(A.Ay.colors.primary.main):"none"}),A.Ay.transitions.default,A.Ay.shadows.md),R=["Inter, sans-serif","Arial, sans-serif","Helvetica, sans-serif","Georgia, serif","Times New Roman, serif","Courier New, monospace","Verdana, sans-serif","Roboto, sans-serif","Open Sans, sans-serif","Lato, sans-serif"],N=[{name:"Blue",primary:"#2563EB",secondary:"#10B981",background:"#FFFFFF",text:"#111827"},{name:"Purple",primary:"#8B5CF6",secondary:"#EC4899",background:"#FFFFFF",text:"#111827"},{name:"Green",primary:"#10B981",secondary:"#3B82F6",background:"#FFFFFF",text:"#111827"},{name:"Red",primary:"#EF4444",secondary:"#F59E0B",background:"#FFFFFF",text:"#111827"},{name:"Dark",primary:"#3B82F6",secondary:"#10B981",background:"#111827",text:"#F9FAFB"},{name:"Monochrome",primary:"#000000",secondary:"#666666",background:"#FFFFFF",text:"#333333"},{name:"Sunset",primary:"#FF5733",secondary:"#FFC300",background:"#FFFAF0",text:"#333333"},{name:"Ocean",primary:"#1A5276",secondary:"#2E86C1",background:"#EBF5FB",text:"#17202A"},{name:"Forest",primary:"#1E8449",secondary:"#F1C40F",background:"#F4F6F6",text:"#145A32"},{name:"Night Mode",primary:"#BB86FC",secondary:"#03DAC5",background:"#121212",text:"#E1E1E1"}];const z=function(){var e,t,n=(0,E.wA)(),r=(0,E.d4)((function(e){return e&&e.themes?Array.isArray(e.themes.themes)?e.themes.themes:[]:(console.warn("Redux state or themes slice not found, using fallback values"),[])})),a=(0,E.d4)((function(e){return e&&e.themes&&e.themes.activeTheme||"default"})),o=(0,E.d4)((function(e){return e&&e.themes&&e.themes.userPreferences?e.themes.userPreferences:{savedTheme:null,autoApplyTheme:!0}})),l=(0,f.useState)(""),i=(0,y.A)(l,2),c=i[0],s=i[1],d=(0,f.useState)("#2563EB"),u=(0,y.A)(d,2),m=u[0],p=u[1],g=(0,f.useState)("#10B981"),x=(0,y.A)(g,2),z=x[0],j=x[1],H=(0,f.useState)("#FFFFFF"),M=(0,y.A)(H,2),U=M[0],_=M[1],L=(0,f.useState)("#111827"),J=(0,y.A)(L,2),Y=J[0],q=J[1],G=(0,f.useState)("Inter, sans-serif"),V=(0,y.A)(G,2),Q=V[0],$=V[1],K=(0,f.useState)(null),X=(0,y.A)(K,2),Z=X[0],ee=X[1],te=(0,f.useState)(!1),ne=(0,y.A)(te,2),re=ne[0],ae=ne[1],oe=(0,f.useState)({}),le=(0,y.A)(oe,2),ie=le[0],ce=le[1];(0,f.useEffect)((function(){if(!Array.isArray(r)||0===r.length)try{n((0,b.zp)({id:"default",name:"Default Theme",primaryColor:"#2563EB",secondaryColor:"#10B981",backgroundColor:"#FFFFFF",textColor:"#111827",fontFamily:"Inter, sans-serif"}))}catch(e){console.error("Error dispatching addTheme action:",e)}if(!a)try{n((0,b.Ic)("default"))}catch(e){console.error("Error dispatching setActiveTheme action:",e)}}),[n]),(0,f.useEffect)((function(){var e=function(e){e.data&&"THEME_CACHE_UPDATED"===e.data.type&&console.log("Theme cache updated at:",e.data.timestamp)};return"serviceWorker"in navigator&&navigator.serviceWorker.addEventListener("message",e),function(){"serviceWorker"in navigator&&navigator.serviceWorker.removeEventListener("message",e)}}),[]);var se=function(){var e={};return c.trim()||(e.name="Theme name is required"),ce(e),0===Object.keys(e).length},de=function(e){ee(e),s(e.name),p(e.primaryColor),j(e.secondaryColor),_(e.backgroundColor),q(e.textColor),$(e.fontFamily),ae(!0),ce({})};return f.createElement(w,null,f.createElement(C.Card,null,f.createElement(C.Card.Header,null,f.createElement(C.Card.Title,null,re?"Edit Theme":"Create Theme"),re&&f.createElement(C.Button,{variant:"text",size:"small",onClick:function(){s(""),p("#2563EB"),j("#10B981"),_("#FFFFFF"),q("#111827"),$("Inter, sans-serif"),ee(null),ae(!1),ce({})},startIcon:f.createElement(v.r$3,null)},"Cancel")),f.createElement(C.Card.Content,null,f.createElement(I,null,f.createElement(O,null,f.createElement(C.Input,{label:"Theme Name",value:c,onChange:function(e){return s(e.target.value)},placeholder:"Enter theme name",fullWidth:!0,error:!!ie.name,helperText:ie.name})),f.createElement("div",{style:{marginBottom:A.Ay.spacing[2]}},f.createElement("div",{style:{fontWeight:A.Ay.typography.fontWeight.medium,marginBottom:A.Ay.spacing[2]}},"Color Palettes"),f.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:A.Ay.spacing[2]}},N.map((function(e,t){return f.createElement(C.Button,{key:t,variant:"outline",size:"small",onClick:function(){return function(e){p(e.primary),j(e.secondary),_(e.background),q(e.text)}(e)}},e.name)})))),f.createElement(O,null,f.createElement("label",null,"Primary Color"),f.createElement(B,null,f.createElement("div",{className:"color-preview"},f.createElement("input",{type:"color",value:m,onChange:function(e){return p(e.target.value)}})),f.createElement(C.Input,{className:"color-input",value:m,onChange:function(e){return p(e.target.value)},fullWidth:!0}))),f.createElement(O,null,f.createElement("label",null,"Secondary Color"),f.createElement(B,null,f.createElement("div",{className:"color-preview"},f.createElement("input",{type:"color",value:z,onChange:function(e){return j(e.target.value)}})),f.createElement(C.Input,{className:"color-input",value:z,onChange:function(e){return j(e.target.value)},fullWidth:!0}))),f.createElement(O,null,f.createElement("label",null,"Background Color"),f.createElement(B,null,f.createElement("div",{className:"color-preview"},f.createElement("input",{type:"color",value:U,onChange:function(e){return _(e.target.value)}})),f.createElement(C.Input,{className:"color-input",value:U,onChange:function(e){return _(e.target.value)},fullWidth:!0}))),f.createElement(O,null,f.createElement("label",null,"Text Color"),f.createElement(B,null,f.createElement("div",{className:"color-preview"},f.createElement("input",{type:"color",value:Y,onChange:function(e){return q(e.target.value)}})),f.createElement(C.Input,{className:"color-input",value:Y,onChange:function(e){return q(e.target.value)},fullWidth:!0}))),f.createElement(O,null,f.createElement("label",null,"Font Family"),f.createElement("select",{value:Q,onChange:function(e){return $(e.target.value)},style:{width:"100%",padding:A.Ay.spacing[2],borderRadius:A.Ay.borderRadius.md,border:"1px solid ".concat(A.Ay.colors.neutral[300])}},R.map((function(e){return f.createElement("option",{key:e,value:e},e)}))),f.createElement(S,null,f.createElement("div",{className:"font-preview",style:{fontFamily:Q}},"The quick brown fox jumps over the lazy dog."))))),f.createElement(C.Card.Footer,null,re?f.createElement(C.Button,{variant:"primary",onClick:function(){if(Z&&se()){var e=k(k({},Z),{},{name:c.trim(),primaryColor:m,secondaryColor:z,backgroundColor:U,textColor:Y,fontFamily:Q,updatedAt:(new Date).toISOString()});n((0,b.V_)(e)),"serviceWorker"in navigator&&navigator.serviceWorker.controller&&(navigator.serviceWorker.controller.postMessage({type:"THEME_UPDATED",theme:e}),navigator.serviceWorker.controller.postMessage({type:"UPDATE_THEME_CACHE"})),s(""),p("#2563EB"),j("#10B981"),_("#FFFFFF"),q("#111827"),$("Inter, sans-serif"),ee(null),ae(!1),ce({})}},startIcon:f.createElement(v.ylI,null),disabled:"default"===(null==Z?void 0:Z.id)},"Update Theme"):f.createElement(C.Button,{variant:"primary",onClick:function(){if(se()){var e={id:Date.now().toString(),name:c.trim(),primaryColor:m,secondaryColor:z,backgroundColor:U,textColor:Y,fontFamily:Q,createdAt:(new Date).toISOString()};n((0,b.zp)(e)),"serviceWorker"in navigator&&navigator.serviceWorker.controller&&(navigator.serviceWorker.controller.postMessage({type:"THEME_UPDATED",theme:e}),navigator.serviceWorker.controller.postMessage({type:"UPDATE_THEME_CACHE"})),s(""),p("#2563EB"),j("#10B981"),_("#FFFFFF"),q("#111827"),$("Inter, sans-serif"),ce({})}},startIcon:f.createElement(v.bW0,null)},"Add Theme"))),f.createElement(C.Card,null,f.createElement(C.Card.Header,null,f.createElement(C.Card.Title,null,"Theme Preview")),f.createElement(C.Card.Content,null,f.createElement(D,{primaryColor:m,secondaryColor:z,backgroundColor:U,textColor:Y,fontFamily:Q},f.createElement("h3",null,"Theme Preview"),f.createElement("p",null,"This is a preview of how your theme will look. The text color, background color, and font family are applied to this preview."),f.createElement("div",{className:"buttons"},f.createElement("button",{className:"primary-button"},"Primary Button"),f.createElement("button",{className:"secondary-button"},"Secondary Button")),f.createElement("div",{className:"card-example"},f.createElement("h4",{style:{margin:"0 0 8px 0",color:Y}},"Card Example"),f.createElement("p",{style:{margin:"0 0 8px 0",fontSize:"14px"}},"This shows how cards will appear with your theme.")),f.createElement("label",{style:{display:"block",marginTop:"16px",marginBottom:"8px"}},"Input Example:"),f.createElement("input",{type:"text",className:"input-example",placeholder:"Enter text here..."}),f.createElement("div",{style:{marginTop:"16px",display:"flex",justifyContent:"space-between"}},f.createElement("div",{style:{width:"24px",height:"24px",borderRadius:"50%",backgroundColor:m,border:"1px solid rgba(0,0,0,0.1)"}}),f.createElement("div",{style:{width:"24px",height:"24px",borderRadius:"50%",backgroundColor:z,border:"1px solid rgba(0,0,0,0.1)"}}),f.createElement("div",{style:{width:"24px",height:"24px",borderRadius:"50%",backgroundColor:U,border:"1px solid rgba(0,0,0,0.1)"}}),f.createElement("div",{style:{width:"24px",height:"24px",borderRadius:"50%",backgroundColor:Y,border:"1px solid rgba(0,0,0,0.1)"}}))))),f.createElement(C.Card,null,f.createElement(C.Card.Header,null,f.createElement(C.Card.Title,null,"Theme Preferences"),f.createElement(v.JO7,{style:{fontSize:"18px",color:A.Ay.colors.neutral[500]}})),f.createElement(C.Card.Content,null,f.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:A.Ay.spacing[2]}},f.createElement("div",null,f.createElement("h4",{style:{margin:0,marginBottom:A.Ay.spacing[1]}},"Auto-apply Theme"),f.createElement("p",{style:{margin:0,color:A.Ay.colors.neutral[500],fontSize:A.Ay.typography.fontSize.sm}},"Automatically save your theme selection as a preference")),f.createElement(h.dO,{checked:o.autoApplyTheme,onChange:function(){n({type:F._E}),n((function(e,t){try{var n=t().themes.userPreferences;localStorage.setItem("themePreferences",JSON.stringify(n))}catch(e){console.error("Error saving theme preferences:",e)}}));var e=!o.autoApplyTheme;h.iU.success("Auto-apply theme ".concat(e?"enabled":"disabled"))},style:{backgroundColor:o.autoApplyTheme?A.Ay.colors.primary.main:void 0}})),f.createElement("div",{style:{marginTop:A.Ay.spacing[3],padding:A.Ay.spacing[3],backgroundColor:A.Ay.colors.neutral[100],borderRadius:A.Ay.borderRadius.md}},f.createElement("h4",{style:{margin:0,marginBottom:A.Ay.spacing[2]}},"Current Preferences"),f.createElement("div",{style:{display:"flex",gap:A.Ay.spacing[2],alignItems:"center"}},f.createElement("div",{style:{width:"20px",height:"20px",borderRadius:"50%",backgroundColor:o.savedTheme&&(null===(e=r.find((function(e){return e.id===o.savedTheme})))||void 0===e?void 0:e.primaryColor)||"#2563EB",border:"1px solid #e5e7eb"}}),f.createElement("span",null,o.savedTheme?(null===(t=r.find((function(e){return e.id===o.savedTheme})))||void 0===t?void 0:t.name)||"Default Theme":"No saved preference"))))),f.createElement(C.Card,null,f.createElement(C.Card.Header,null,f.createElement(C.Card.Title,null,"Available Themes"),f.createElement("div",{style:{display:"flex",gap:A.Ay.spacing[2]}},f.createElement("input",{type:"file",id:"theme-import",accept:".json",style:{display:"none"},onChange:function(e){var t=e.target.files[0];if(t){var r=new FileReader;r.onload=function(t){try{var r=JSON.parse(t.target.result);if(!(r.name&&r.primaryColor&&r.secondaryColor&&r.backgroundColor&&r.textColor&&r.fontFamily))throw new Error("Invalid theme format");var a=k(k({},r),{},{id:Date.now().toString(),name:"".concat(r.name," (Imported)"),createdAt:(new Date).toISOString()});n((0,b.zp)(a)),"serviceWorker"in navigator&&navigator.serviceWorker.controller&&(navigator.serviceWorker.controller.postMessage({type:"THEME_UPDATED",theme:a}),navigator.serviceWorker.controller.postMessage({type:"UPDATE_THEME_CACHE"})),e.target.value=""}catch(t){console.error("Error importing theme:",t),e.target.value=""}},r.readAsText(t)}}}),f.createElement(C.Button,{variant:"outline",size:"small",onClick:function(){return document.getElementById("theme-import").click()}},"Import Theme"))),f.createElement(C.Card.Content,null,0===r.length?f.createElement(P,null,f.createElement("div",{style:{fontSize:"48px",color:A.Ay.colors.neutral[400],marginBottom:A.Ay.spacing[4]}},f.createElement(v.Ebl,null)),f.createElement("h3",null,"No Themes Yet"),f.createElement("p",null,"Create your first theme to get started")):f.createElement(T,null,(Array.isArray(r)?r:[]).map((function(e){return f.createElement(W,{key:e.id,elevation:"sm",isActive:a===e.id},f.createElement(C.Card.Header,null,f.createElement("div",null,f.createElement("div",{style:{fontWeight:e.typography.fontWeight.semibold}},e.name),f.createElement("div",{style:{fontSize:e.typography.fontSize.sm,color:e.colors.neutral[500]}},e.fontFamily.split(",")[0])),f.createElement("div",{style:{display:"flex",gap:e.spacing[1]}},"default"!==e.id&&f.createElement(f.Fragment,null,f.createElement(C.Button,{variant:"text",size:"small",onClick:function(){return function(e){var t=e||Z;if(t){var n=JSON.stringify(t,null,2),r=new Blob([n],{type:"application/json"}),a=URL.createObjectURL(r),o=document.createElement("a");o.href=a,o.download="".concat(t.name.replace(/\s+/g,"-").toLowerCase(),"-theme.json"),document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(a)}}(e)},title:"Export Theme"},f.createElement(v.Om2,null)),f.createElement(C.Button,{variant:"text",size:"small",onClick:function(){return function(e){var t=k(k({},e),{},{id:Date.now().toString(),name:"".concat(e.name," (Copy)"),createdAt:(new Date).toISOString()});n((0,b.zp)(t))}(e)},title:"Duplicate Theme"},f.createElement(v.wq3,null)),f.createElement(C.Button,{variant:"text",size:"small",onClick:function(){return de(e)},title:"Edit Theme"},f.createElement(v.xjh,null)),f.createElement(C.Button,{variant:"text",size:"small",onClick:function(){var t;"default"!==(t=e.id)&&(n((0,b.Qo)(t)),Z&&Z.id===t&&(s(""),p("#2563EB"),j("#10B981"),_("#FFFFFF"),q("#111827"),$("Inter, sans-serif"),ee(null),ae(!1)),a===t&&n((0,b.Ic)("default")))},title:"Delete Theme"},f.createElement(v.SUY,null))))),f.createElement(C.Card.Content,{onClick:function(){return de(e)}},f.createElement(D,{primaryColor:e.primaryColor,secondaryColor:e.secondaryColor,backgroundColor:e.backgroundColor,textColor:e.textColor,fontFamily:e.fontFamily,style:{height:"120px",overflow:"hidden"}},f.createElement("h3",{style:{fontSize:"16px"}},"Preview"),f.createElement("p",{style:{fontSize:"14px"}},"Sample text with this theme applied."),f.createElement("div",{className:"buttons"},f.createElement("button",{className:"primary-button",style:{padding:"4px 8px",fontSize:"12px"}},"Button"),f.createElement("button",{className:"secondary-button",style:{padding:"4px 8px",fontSize:"12px"}},"Button")))),f.createElement(C.Card.Footer,null,f.createElement("div",{style:{display:"flex",gap:e.spacing[2]}},f.createElement("div",{style:{width:"20px",height:"20px",backgroundColor:e.primaryColor,borderRadius:"50%",border:"1px solid #e5e7eb"}}),f.createElement("div",{style:{width:"20px",height:"20px",backgroundColor:e.secondaryColor,borderRadius:"50%",border:"1px solid #e5e7eb"}})),a===e.id?f.createElement(C.Button,{variant:"text",size:"small",startIcon:f.createElement(v.JIb,null),style:{color:e.colors.success.main}},"Active"):f.createElement(C.Button,{variant:"outline",size:"small",onClick:function(){return function(e){try{n((0,b.Ic)(e)),h.iU.success("Theme activated successfully");var t=(Array.isArray(r)?r:[]).concat([{id:"default",name:"Default Theme",primaryColor:"#2563EB",secondaryColor:"#10B981",backgroundColor:"#FFFFFF",textColor:"#111827",fontFamily:"Inter, sans-serif"}]).find((function(t){return t.id===e}));"serviceWorker"in navigator&&navigator.serviceWorker.controller&&t&&navigator.serviceWorker.controller.postMessage({type:"THEME_UPDATED",theme:t})}catch(e){console.error("Error setting active theme:",e),h.iU.error("Failed to activate theme. Please try again.")}}(e.id)}},"Activate")))}))))))}}}]);