"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[435],{7435:(e,r,t)=>{t.r(r),t.d(r,{default:()=>K});var a,l,n,s,o,i=t(467),m=t(5544),c=t(7528),p=t(4756),u=t.n(p),f=t(6540),d=t(3016),A=t(2395),E=t(1427),h=t(9467),g=t(9740),w=t(9249),y=t(7197),v=t(7355),x=t(7829),P=t(7450),b=t(3903),I=t(261),k=t(5619),q=t(5542),N=t(5763),T=t(7028),S=t(9391),F=t(2389),R=t(1250),C=t(7749),B=d.A.Title,z=d.A.Text,$=(d.<PERSON>.Paragraph,<PERSON><PERSON><PERSON><PERSON>),U=R.Ay.div(a||(a=(0,c.A)(["\n  padding: 24px;\n  max-width: 800px;\n  margin: 0 auto;\n"]))),Z=R.Ay.div(l||(l=(0,c.A)(["\n  display: flex;\n  align-items: center;\n  margin-bottom: 24px;\n  \n  @media (max-width: 576px) {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n"]))),L=(0,R.Ay)(E.A)(n||(n=(0,c.A)(["\n  margin-right: 24px;\n  background-color: ",";\n  \n  @media (max-width: 576px) {\n    margin-right: 0;\n    margin-bottom: 16px;\n  }\n"])),(function(e){return e.theme.colorPalette.primary})),V=R.Ay.div(s||(s=(0,c.A)(["\n  flex: 1;\n"]))),j=(0,R.Ay)(h.A)(o||(o=(0,c.A)(["\n  .ant-form-item-label {\n    text-align: left;\n  }\n"])));const K=function(){var e=(0,S.As)().user,r=(0,F.Bd)().t,t=(0,f.useState)(!1),a=(0,m.A)(t,2),l=a[0],n=a[1],s=(0,f.useState)(null),o=(0,m.A)(s,2),c=o[0],p=o[1],d=(0,f.useState)(null),E=(0,m.A)(d,2),R=E[0],K=E[1],M=(0,f.useState)("1"),_=(0,m.A)(M,2),D=_[0],G=_[1],H=(0,f.useState)(!1),J=(0,m.A)(H,2),O=J[0],Q=J[1],W=function(){var e=(0,i.A)(u().mark((function e(t){return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n(!0),p(null),K(null),e.prev=3,e.next=6,new Promise((function(e){return setTimeout(e,1e3)}));case 6:K(r("profile.updateSuccess")),Q(!1),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(3),p(e.t0.message||r("profile.updateError"));case 13:return e.prev=13,n(!1),e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[3,10,13,16]])})));return function(r){return e.apply(this,arguments)}}(),X=function(){var e=(0,i.A)(u().mark((function e(t){return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n(!0),p(null),K(null),e.prev=3,e.next=6,new Promise((function(e){return setTimeout(e,1e3)}));case 6:K(r("profile.passwordChangeSuccess")),te.resetFields(),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(3),p(e.t0.message||r("profile.passwordChangeError"));case 13:return e.prev=13,n(!1),e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[3,10,13,16]])})));return function(r){return e.apply(this,arguments)}}(),Y=h.A.useForm(),ee=(0,m.A)(Y,1)[0],re=h.A.useForm(),te=(0,m.A)(re,1)[0];return f.useEffect((function(){e&&ee.setFieldsValue({username:e.username,email:e.email,firstName:e.firstName,lastName:e.lastName,phone:e.phone})}),[e,ee]),e?f.createElement(U,null,f.createElement(C.sT,{level:2},r("profile.title")),f.createElement(Z,null,f.createElement(L,{size:100,icon:f.createElement(P.A,null),src:e.avatar}),f.createElement(V,null,f.createElement(B,{level:3},e.firstName," ",e.lastName),f.createElement(z,{type:"secondary"},e.email),e.roles&&e.roles.length>0&&f.createElement("div",{style:{marginTop:"8px"}},e.roles.map((function(e){return f.createElement(C.ih,{key:e,color:"blue"},e)}))))),f.createElement(A.A,{activeKey:D,onChange:G},f.createElement($,{tab:f.createElement("span",null,f.createElement(P.A,null),r("profile.tabs.profile")),key:"1"},f.createElement(C.ee,{title:r("profile.profileInfo"),extra:f.createElement(w.Ay,{type:O?"primary":"default",icon:O?f.createElement(b.A,null):f.createElement(I.A,null),onClick:function(){O?ee.submit():Q(!0)}},r(O?"profile.save":"profile.edit"))},c&&f.createElement(y.A,{message:r("profile.error"),description:c,type:"error",showIcon:!0,style:{marginBottom:"24px"}}),R&&f.createElement(y.A,{message:r("profile.success"),description:R,type:"success",showIcon:!0,style:{marginBottom:"24px"}}),f.createElement(j,{form:ee,layout:"vertical",onFinish:W,disabled:!O},f.createElement(h.A.Item,{label:r("profile.username"),name:"username",rules:[{required:!0,message:r("profile.usernameRequired")},{min:3,message:r("profile.usernameTooShort")},{max:20,message:r("profile.usernameTooLong")},{pattern:/^[a-zA-Z0-9_]+$/,message:r("profile.usernameInvalid")}]},f.createElement(v.A,{prefix:f.createElement(P.A,null),placeholder:r("profile.usernamePlaceholder")})),f.createElement(h.A.Item,{label:r("profile.email"),name:"email",rules:[{required:!0,message:r("profile.emailRequired")},{type:"email",message:r("profile.emailInvalid")}]},f.createElement(v.A,{prefix:f.createElement(k.A,null),placeholder:r("profile.emailPlaceholder")})),f.createElement(h.A.Item,{label:r("profile.firstName"),name:"firstName",rules:[{required:!0,message:r("profile.firstNameRequired")}]},f.createElement(v.A,{placeholder:r("profile.firstNamePlaceholder")})),f.createElement(h.A.Item,{label:r("profile.lastName"),name:"lastName",rules:[{required:!0,message:r("profile.lastNameRequired")}]},f.createElement(v.A,{placeholder:r("profile.lastNamePlaceholder")})),f.createElement(h.A.Item,{label:r("profile.phone"),name:"phone",rules:[{required:!0,message:r("profile.phoneRequired")},{pattern:/^\+?[0-9]{10,15}$/,message:r("profile.phoneInvalid")}]},f.createElement(v.A,{prefix:f.createElement(q.A,null),placeholder:r("profile.phonePlaceholder")}))))),f.createElement($,{tab:f.createElement("span",null,f.createElement(N.A,null),r("profile.tabs.security")),key:"2"},f.createElement(C.ee,{title:r("profile.changePassword")},c&&f.createElement(y.A,{message:r("profile.error"),description:c,type:"error",showIcon:!0,style:{marginBottom:"24px"}}),R&&f.createElement(y.A,{message:r("profile.success"),description:R,type:"success",showIcon:!0,style:{marginBottom:"24px"}}),f.createElement(j,{form:te,layout:"vertical",onFinish:X},f.createElement(h.A.Item,{label:r("profile.currentPassword"),name:"currentPassword",rules:[{required:!0,message:r("profile.currentPasswordRequired")}]},f.createElement(v.A.Password,{prefix:f.createElement(N.A,null),placeholder:r("profile.currentPasswordPlaceholder")})),f.createElement(h.A.Item,{label:r("profile.newPassword"),name:"newPassword",rules:[{required:!0,message:r("profile.newPasswordRequired")},{min:8,message:r("profile.passwordTooShort")},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/,message:r("profile.passwordInvalid")}],hasFeedback:!0},f.createElement(v.A.Password,{prefix:f.createElement(N.A,null),placeholder:r("profile.newPasswordPlaceholder")})),f.createElement(h.A.Item,{label:r("profile.confirmPassword"),name:"confirmPassword",dependencies:["newPassword"],hasFeedback:!0,rules:[{required:!0,message:r("profile.confirmPasswordRequired")},function(e){var t=e.getFieldValue;return{validator:function(e,a){return a&&t("newPassword")!==a?Promise.reject(new Error(r("profile.passwordsMismatch"))):Promise.resolve()}}}]},f.createElement(v.A.Password,{prefix:f.createElement(N.A,null),placeholder:r("profile.confirmPasswordPlaceholder")})),f.createElement(h.A.Item,null,f.createElement(w.Ay,{type:"primary",htmlType:"submit",loading:l},r("profile.changePassword")))))),f.createElement($,{tab:f.createElement("span",null,f.createElement(T.A,null),r("profile.tabs.avatar")),key:"3"},f.createElement(C.ee,{title:r("profile.changeAvatar")},f.createElement("div",{style:{textAlign:"center",marginBottom:"24px"}},f.createElement(L,{size:150,icon:f.createElement(P.A,null),src:e.avatar})),f.createElement(x.A,{name:"avatar",listType:"picture",showUploadList:!1,action:"/api/upload",onChange:function(e){"uploading"!==e.file.status&&("done"===e.file.status?g.Ay.success(r("profile.avatarUploadSuccess")):"error"===e.file.status&&g.Ay.error(r("profile.avatarUploadError")))}},f.createElement(w.Ay,{icon:f.createElement(T.A,null)},r("profile.uploadAvatar"))))))):null}}}]);