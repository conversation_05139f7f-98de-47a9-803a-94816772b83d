"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[5897],{47119:(e,t,n)=>{n.d(t,{$l:()=>o}),n(60436);var r=n(5544),a=n(96540),o=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.onDrop,n=e.onDragStart,o=e.onDragEnd,u=e.onDragOver,c=e.onDragLeave,i=e.snapToGrid,s=void 0!==i&&i,l=e.gridSize,f=void 0===l?20:l,d=(e.showDropZones,e.acceptedTypes),p=void 0===d?["application/json"]:d,g=(0,a.useState)(!1),v=(0,r.A)(g,2),b=v[0],m=v[1],y=(0,a.useState)(!1),h=(0,r.A)(y,2),C=h[0],S=h[1],k=(0,a.useState)(null),O=(0,r.A)(k,2),w=O[0],A=O[1],E=(0,a.useState)({x:0,y:0}),j=(0,r.A)(E,2),x=j[0],D=j[1],P=(0,a.useState)(!0),R=(0,r.A)(P,2),M=R[0],L=R[1],T=(0,a.useState)(null),I=(0,r.A)(T,2),z=(I[0],I[1],(0,a.useRef)(null)),F=(0,a.useRef)(null),H=(0,a.useCallback)((function(e,t){if(m(!0),A(t),t&&e.dataTransfer.setData("application/json",JSON.stringify(t)),e.dataTransfer.effectAllowed="copy",F.current){var r=F.current.cloneNode(!0);r.style.position="absolute",r.style.top="-1000px",r.style.left="-1000px",r.style.opacity="0.8",r.style.transform="rotate(5deg) scale(0.9)",r.style.pointerEvents="none",r.style.zIndex="9999",document.body.appendChild(r),e.dataTransfer.setDragImage(r,50,25),setTimeout((function(){document.body.contains(r)&&document.body.removeChild(r)}),0)}n&&n(e,t)}),[n]),K=(0,a.useCallback)((function(e){m(!1),A(null),D({x:0,y:0}),L(!0),o&&o(e)}),[o]),_=(0,a.useCallback)((function(e){e.preventDefault(),S(!0)}),[]),J=(0,a.useCallback)((function(e){if(e.preventDefault(),z.current){var t=z.current.getBoundingClientRect(),n=e.clientX-t.left,r=e.clientY-t.top;s&&(n=Math.round(n/f)*f,r=Math.round(r/f)*f),D({x:n,y:r})}var a=e.dataTransfer.types[0],o=p.includes(a)||0===p.length;L(o),e.dataTransfer.dropEffect=o?"copy":"none",u&&u(e)}),[s,f,p,u]),N=(0,a.useCallback)((function(e){e.preventDefault(),e.currentTarget.contains(e.relatedTarget)||(S(!1),L(!0),c&&c(e))}),[c]),U=(0,a.useCallback)((function(e){e.preventDefault(),S(!1),L(!0);try{var n=e.dataTransfer.getData("application/json"),r=null;n&&(r=JSON.parse(n));var a=x;s&&(a={x:Math.round(x.x/f)*f,y:Math.round(x.y/f)*f}),t&&t(e,r,a)}catch(e){console.error("Error handling drop:",e)}}),[x,s,f,t]);return(0,a.useEffect)((function(){var e=z.current;if(e)return e.addEventListener("dragenter",_),e.addEventListener("dragover",J),e.addEventListener("dragleave",N),e.addEventListener("drop",U),function(){e.removeEventListener("dragenter",_),e.removeEventListener("dragover",J),e.removeEventListener("dragleave",N),e.removeEventListener("drop",U)}}),[_,J,N,U]),{isDragging:b,isOver:C,dragData:w,dropPosition:x,validDropZone:M,dropZoneRef:z,dragPreviewRef:F,handleDragStart:H,handleDragEnd:K,reset:function(){m(!1),S(!1),A(null),D({x:0,y:0}),L(!0)}}}},48860:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(64467),a=n(5544),o=n(96540),u=n(2543);function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}const i=function(e){var t=e.components,n=void 0===t?[]:t,i=e.containerHeight,s=void 0===i?600:i,l=e.itemHeight,f=void 0===l?100:l,d=e.overscan,p=void 0===d?5:d,g=e.enableVirtualization,v=void 0===g||g,b=e.enablePerformanceMonitoring,m=void 0===b||b,y=(0,o.useState)(0),h=(0,a.A)(y,2),C=h[0],S=h[1],k=(0,o.useState)(null),O=(0,a.A)(k,2),w=O[0],A=O[1],E=(0,o.useState)({start:0,end:0}),j=(0,a.A)(E,2),x=j[0],D=j[1],P=(0,o.useState)(0),R=(0,a.A)(P,2),M=R[0],L=R[1],T=(0,o.useState)(60),I=(0,a.A)(T,2),z=I[0],F=I[1],H=(0,o.useState)(0),K=(0,a.A)(H,2),_=K[0],J=K[1],N=(0,o.useRef)(0),U=(0,o.useRef)(0),X=(0,o.useRef)(performance.now()),Z=(0,o.useRef)(new Map),q=(0,o.useRef)(null),W=(0,o.useCallback)((function(){if(!v||!w||0===n.length)return{start:0,end:n.length};var e=Math.floor(C/f),t=Math.min(e+Math.ceil(s/f)+p,n.length);return{start:Math.max(0,e-p),end:t}}),[C,f,s,p,n.length,v,w]);(0,o.useEffect)((function(){var e=W();D(e)}),[W]);var Y=(0,o.useCallback)((0,u.throttle)((function(e){e.target&&S(e.target.scrollTop)}),16),[]),B=(0,o.useMemo)((function(){return v?n.slice(x.start,x.end).map((function(e,t){return{component:e,index:x.start+t}})):n.map((function(e,t){return{component:e,index:t}}))}),[n,x,v]),G=(0,o.useCallback)((function(e,t){var r="".concat(e,"_").concat(JSON.stringify(n.find((function(t){return t.id===e}))));if(Z.current.has(r))return Z.current.get(r);var a=t();if(Z.current.set(r,a),Z.current.size>100){var o=Z.current.keys().next().value;Z.current.delete(o)}return a}),[n]),V=(0,o.useCallback)((function(){m&&(N.current=performance.now())}),[m]),$=(0,o.useCallback)((function(){if(m&&N.current>0){var e=performance.now()-N.current;L(e),N.current=0}}),[m]);(0,o.useEffect)((function(){if(m){var e,t=function(){var n=performance.now(),r=n-X.current;if(r>=1e3){var a=Math.round(1e3*U.current/r);F(a),U.current=0,X.current=n}else U.current++;e=requestAnimationFrame(t)};return e=requestAnimationFrame(t),function(){e&&cancelAnimationFrame(e)}}}),[m]),(0,o.useEffect)((function(){if(m&&performance.memory){var e=function(){var e=performance.memory,t=Math.round(e.usedJSHeapSize/1024/1024);J(t)},t=setInterval(e,5e3);return e(),function(){return clearInterval(t)}}}),[m]),(0,o.useEffect)((function(){if(v)return q.current=new IntersectionObserver((function(e){e.forEach((function(e){e.isIntersecting&&e.target.dataset.componentId}))}),{root:w,rootMargin:"".concat(p*f,"px"),threshold:.1}),function(){q.current&&q.current.disconnect()}}),[w,p,f,v]),(0,o.useEffect)((function(){var e=new Set(n.map((function(e){return e.id})));new Set(Array.from(Z.current.keys()).map((function(e){return e.split("_")[0]}))).forEach((function(t){e.has(t)||Array.from(Z.current.keys()).filter((function(e){return e.startsWith(t)})).forEach((function(e){return Z.current.delete(e)}))}))}),[n]);var Q=(0,o.useCallback)((function(){return v?{ref:A,onScroll:Y,style:{height:s,overflow:"auto",position:"relative"}}:{}}),[v,s,Y]),ee=(0,o.useCallback)((function(){if(!v)return{before:{},after:{}};var e=n.length*f;return{before:{style:{height:x.start*f,width:"100%"}},after:{style:{height:e-x.end*f,width:"100%"}}}}),[v,n.length,f,x]);return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({visibleComponents:B,visibleRange:x,getContainerProps:Q,getSpacerProps:ee,renderTime:M,frameRate:z,memoryUsage:_,startRenderMeasurement:V,endRenderMeasurement:$,getCachedComponent:G},{clearCache:function(){return Z.current.clear()},getCacheSize:function(){return Z.current.size},getPerformanceMetrics:function(){return{renderTime:M,frameRate:z,memoryUsage:_,cacheSize:Z.current.size,visibleComponents:B.length,totalComponents:n.length}},shouldRender:function(e){if(!v)return!0;var t=n.findIndex((function(t){return t.id===e}));return t>=x.start&&t<x.end}})}},87169:(e,t,n)=>{n.d(t,{A:()=>g});var r=n(64467),a=n(10467),o=n(5544),u=n(54756),c=n.n(u),i=n(96540),s=n(71468),l=n(86329),f=n(81616);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const g=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.autoRefresh,n=void 0===t||t,r=e.refreshInterval,u=void 0===r?3e4:r,d=e.enableCache,g=void 0===d||d,v=e.context,b=void 0===v?{}:v,m=(0,s.wA)(),y=(0,s.d4)((function(e){var t;return(null===(t=e.app)||void 0===t?void 0:t.components)||e.components||[]})),h=(0,s.d4)((function(e){var t;return(null===(t=e.app)||void 0===t?void 0:t.layouts)||e.layouts||[]})),C=(0,s.d4)((function(e){var t;return(null===(t=e.ui)||void 0===t?void 0:t.selectedComponent)||null})),S=(0,i.useState)({layout:[],combinations:[],analysis:null}),k=(0,o.A)(S,2),O=k[0],w=k[1],A=(0,i.useState)({layout:!1,combinations:!1,analysis:!1}),E=(0,o.A)(A,2),j=E[0],x=E[1],D=(0,i.useState)(null),P=(0,o.A)(D,2),R=P[0],M=P[1],L=(0,i.useState)(null),T=(0,o.A)(L,2),I=T[0],z=T[1],F=(0,i.useRef)(null),H=(0,i.useRef)(null),K=(0,i.useCallback)((0,a.A)(c().mark((function e(){var t,n,r,a,u,i,s,f,d,p=arguments;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=p.length>0&&void 0!==p[0]&&p[0],y&&0!==y.length){e.next=4;break}return w({layout:[],combinations:[],analysis:null}),e.abrupt("return");case 4:return H.current&&H.current.abort(),H.current=new AbortController,M(null),x({layout:!0,combinations:!0,analysis:!0}),e.prev=8,t&&g&&l.A.clearCache(),e.next=12,Promise.allSettled([l.A.generateLayoutSuggestions(y,h,b),l.A.generateComponentCombinations(y,C,b),l.A.analyzeAppStructure(y,h)]);case 12:n=e.sent,r=(0,o.A)(n,3),a=r[0],u=r[1],i=r[2],s="fulfilled"===a.status&&a.value.suggestions||[],f="fulfilled"===u.status&&u.value.suggestions||[],d="fulfilled"===i.status&&i.value.analysis||null,w({layout:s,combinations:f,analysis:d}),z(new Date),[a,u,i].forEach((function(e,t){"rejected"===e.status&&console.warn("Failed to load ".concat(["layout","combinations","analysis"][t]," suggestions:"),e.reason)})),e.next=28;break;case 25:e.prev=25,e.t0=e.catch(8),"AbortError"!==e.t0.name&&M("Failed to load suggestions: ".concat(e.t0.message));case 28:return e.prev=28,x({layout:!1,combinations:!1,analysis:!1}),e.finish(28);case 31:case"end":return e.stop()}}),e,null,[[8,25,28,31]])}))),[y,h,C,b,g]),_=(0,i.useCallback)((0,a.A)(c().mark((function e(){var t;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(y&&0!==y.length){e.next=2;break}return e.abrupt("return");case 2:return x((function(e){return p(p({},e),{},{layout:!0})})),M(null),e.prev=4,e.next=7,l.A.generateLayoutSuggestions(y,h,b);case 7:t=e.sent,w((function(e){return p(p({},e),{},{layout:t.suggestions||[]})})),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(4),M("Failed to load layout suggestions: ".concat(e.t0.message));case 14:return e.prev=14,x((function(e){return p(p({},e),{},{layout:!1})})),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[4,11,14,17]])}))),[y,h,b]),J=(0,i.useCallback)((0,a.A)(c().mark((function e(){var t;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(y&&0!==y.length){e.next=2;break}return e.abrupt("return");case 2:return x((function(e){return p(p({},e),{},{combinations:!0})})),M(null),e.prev=4,e.next=7,l.A.generateComponentCombinations(y,C,b);case 7:t=e.sent,w((function(e){return p(p({},e),{},{combinations:t.suggestions||[]})})),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(4),M("Failed to load combination suggestions: ".concat(e.t0.message));case 14:return e.prev=14,x((function(e){return p(p({},e),{},{combinations:!1})})),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[4,11,14,17]])}))),[y,C,b]),N=(0,i.useCallback)((function(e){try{return console.log("Applying layout suggestion:",e),!0}catch(e){return M("Failed to apply layout suggestion: ".concat(e.message)),!1}}),[]),U=(0,i.useCallback)((function(e){try{return e.missing_components&&e.missing_components.length>0&&e.missing_components.forEach((function(e){var t={type:e,props:{},id:"".concat(e,"-").concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9))};m((0,f.X8)(t.type,t.props))})),console.log("Applied component combination:",e),!0}catch(e){return M("Failed to apply component combination: ".concat(e.message)),!1}}),[m]),X=(0,i.useCallback)((function(){K(!0)}),[K]),Z=(0,i.useCallback)((function(){M(null)}),[]);return(0,i.useEffect)((function(){if(n&&u>0)return F.current=setInterval((function(){K()}),u),function(){F.current&&clearInterval(F.current)}}),[n,u,K]),(0,i.useEffect)((function(){K()}),[K]),(0,i.useEffect)((function(){return function(){F.current&&clearInterval(F.current),H.current&&H.current.abort()}}),[]),{suggestions:O,loading:j,error:R,lastRefresh:I,loadSuggestions:K,loadLayoutSuggestions:_,loadCombinationSuggestions:J,applyLayoutSuggestion:N,applyComponentCombination:U,refresh:X,clearError:Z,hasLayoutSuggestions:O.layout.length>0,hasCombinationSuggestions:O.combinations.length>0,hasAnalysis:null!==O.analysis,isLoading:j.layout||j.combinations||j.analysis,componentCount:y.length,layoutCount:h.length,selectedComponentType:(null==C?void 0:C.type)||null}}},94588:(e,t,n)=>{n.d(t,{Cd:()=>d,EF:()=>l,KW:()=>s,R2:()=>f,aD:()=>i,iD:()=>p});var r=n(64467),a=n(5544),o=n(96540);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var i=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,n=(0,o.useState)([e]),r=(0,a.A)(n,2),u=r[0],c=r[1],i=(0,o.useState)(0),s=(0,a.A)(i,2),l=s[0],f=s[1],d=(0,o.useRef)(!1),p=u[l],g=(0,o.useCallback)((function(e){d.current?d.current=!1:(c((function(n){var r=n.slice(0,l+1);return r.push(e),r.length>t?(r.shift(),r):r})),f((function(e){return Math.min(e+1,t-1)})))}),[l,t]),v=(0,o.useCallback)((function(){return l>0?(d.current=!0,f((function(e){return e-1})),u[l-1]):p}),[l,u,p]),b=(0,o.useCallback)((function(){return l<u.length-1?(d.current=!0,f((function(e){return e+1})),u[l+1]):p}),[l,u,p]),m=l>0,y=l<u.length-1,h=(0,o.useCallback)((function(){c([p]),f(0)}),[p]),C=(0,o.useCallback)((function(){return{totalStates:u.length,currentIndex:l,canUndo:m,canRedo:y}}),[u.length,l,m,y]);return{state:p,pushState:g,undo:v,redo:b,canUndo:m,canRedo:y,clearHistory:h,getHistoryInfo:C}},s=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];(0,o.useEffect)((function(){var t=function(t){var n=t.ctrlKey,r=t.metaKey,a=t.shiftKey,o=t.altKey,u=t.key,c=[];(n||r)&&c.push("ctrl"),a&&c.push("shift"),o&&c.push("alt");var i=[].concat(c,[u.toLowerCase()]).join("+");e[i]&&(t.preventDefault(),e[i](t))};return document.addEventListener("keydown",t),function(){document.removeEventListener("keydown",t)}}),t)},l=function(){var e=(0,o.useState)({visible:!1,x:0,y:0,items:[]}),t=(0,a.A)(e,2),n=t[0],r=t[1],u=(0,o.useCallback)((function(e,t){e.preventDefault(),r({visible:!0,x:e.clientX,y:e.clientY,items:t||[]})}),[]),i=(0,o.useCallback)((function(){r((function(e){return c(c({},e),{},{visible:!1})}))}),[]);return(0,o.useEffect)((function(){var e=function(){n.visible&&i()};return document.addEventListener("click",e),function(){document.removeEventListener("click",e)}}),[n.visible,i]),{contextMenu:n,showContextMenu:u,hideContextMenu:i}},f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:200,t=(0,o.useState)(!1),n=(0,a.A)(t,2),r=n[0],u=n[1],c=(0,o.useState)(""),i=(0,a.A)(c,2),s=i[0],l=i[1],f=(0,o.useRef)(null),d=(0,o.useCallback)((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";f.current&&clearTimeout(f.current),f.current=setTimeout((function(){u(!0),l(t)}),e)}),[e]),p=(0,o.useCallback)((function(){f.current&&(clearTimeout(f.current),f.current=null),u(!1),l("")}),[]);return(0,o.useEffect)((function(){return function(){f.current&&clearTimeout(f.current)}}),[]),{isLoading:r,loadingMessage:s,startLoading:d,stopLoading:p}},d=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=(0,o.useState)(new Set),n=(0,a.A)(t,2),r=n[0],u=n[1],c=(0,o.useState)(-1),i=(0,a.A)(c,2),s=i[0],l=i[1],f=(0,o.useCallback)((function(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=e.findIndex((function(e){return e.id===t.id}));u((function(e){var r=new Set(n?e:[]);return r.has(t.id)?r.delete(t.id):r.add(t.id),r})),l(r)}),[e]),d=(0,o.useCallback)((function(t){var n=e.findIndex((function(e){return e.id===t.id}));if(-1!==s){var r=Math.min(s,n),a=Math.max(s,n);u((function(t){for(var n=new Set(t),o=r;o<=a;o++)e[o]&&n.add(e[o].id);return n}))}else f(t)}),[e,s,f]),p=(0,o.useCallback)((function(){u(new Set(e.map((function(e){return e.id}))))}),[e]),g=(0,o.useCallback)((function(){u(new Set),l(-1)}),[]),v=(0,o.useCallback)((function(e){return r.has(e)}),[r]),b=(0,o.useCallback)((function(){return e.filter((function(e){return r.has(e.id)}))}),[e,r]);return{selectedItems:Array.from(r),selectItem:f,selectRange:d,selectAll:p,clearSelection:g,isSelected:v,getSelectedItems:b,selectedCount:r.size}},p=function(){var e=(0,o.useState)(null),t=(0,a.A)(e,2),n=t[0],r=t[1];return{copy:(0,o.useCallback)((function(e){r(e),navigator.clipboard&&"string"==typeof e&&navigator.clipboard.writeText(e).catch(console.error)}),[]),paste:(0,o.useCallback)((function(){return n}),[n]),clear:(0,o.useCallback)((function(){r(null)}),[]),hasData:null!==n,data:n}}}}]);