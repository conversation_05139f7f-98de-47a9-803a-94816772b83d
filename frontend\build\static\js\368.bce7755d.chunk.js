"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[368],{90368:(e,t,n)=>{n.r(t),n.d(t,{default:()=>h});var a,u=n(57528),r=n(96540),i=n(1807),l=n(11080),c=n(63710),o=n(70572).Ay.div(a||(a=(0,u.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: calc(100vh - 200px);\n  padding: 24px;\n"])));const h=function(){var e=(0,c.Bd)().t;return r.createElement(o,null,r.createElement(i.Q7,{status:"403",title:e("auth.unauthorized.title"),subTitle:e("auth.unauthorized.subtitle"),extra:[r.createElement(i.$n,{type:"primary",key:"home"},r.createElement(l.N_,{to:"/"},e("auth.unauthorized.backHome"))),r.createElement(i.$n,{key:"login"},r.createElement(l.N_,{to:"/login"},e("auth.unauthorized.login")))]}))}}}]);