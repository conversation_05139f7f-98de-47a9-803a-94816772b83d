"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[4309],{4309:(e,t,n)=>{n.r(t),n.d(t,{default:()=>Jn});var l,a,r,o,i=n(64467),c=n(10467),s=n(5544),u=n(57528),p=n(54756),d=n.n(p),m=n(96540),f=n(71468),y=n(35346),v=n(34816),g=n(79146),h=n(86020),b=n(58168),x=n(53986),E=n(1807),w=["value","onChange","min","max","step","unit","units","showSlider","showUnit","placeholder","tooltip","precision"],A=E.o5.Text,C=g.styled.div(l||(l=(0,u.A)(["\n  width: 100%;\n"]))),S=g.styled.div(a||(a=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"]))),O=g.styled.div(r||(r=(0,u.A)(["\n  flex: 1;\n  margin-left: 8px;\n"]))),k=g.styled.select(o||(o=(0,u.A)(["\n  padding: 4px 8px;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  background: white;\n  font-size: 12px;\n  min-width: 50px;\n"])));const z=function(e){var t=e.value,n=e.onChange,l=e.min,a=void 0===l?0:l,r=e.max,o=void 0===r?100:r,i=e.step,c=void 0===i?1:i,u=e.unit,p=void 0===u?"px":u,d=e.units,f=void 0===d?["px","%","rem","em","vh","vw"]:d,v=e.showSlider,g=void 0!==v&&v,h=e.showUnit,z=void 0===h||h,j=e.placeholder,P=void 0===j?"Enter value":j,T=e.tooltip,F=e.precision,R=void 0===F?0:F,B=(0,x.A)(e,w),N=(0,m.useState)(0),L=(0,s.A)(N,2),I=L[0],M=L[1],$=(0,m.useState)(p),H=(0,s.A)($,2),D=H[0],W=H[1];(0,m.useEffect)((function(){if(t){var e=U(t);M(e.number),W(e.unit)}}),[t]);var U=function(e){if("number"==typeof e)return{number:e,unit:D};if("string"==typeof e){var t=e.match(/^(-?\d*\.?\d+)(.*)$/);if(t)return{number:parseFloat(t[1]),unit:t[2]||D}}return{number:0,unit:D}},Y=function(e,t){return z&&t?"".concat(e).concat(t):e},J=function(e){if(null!=e){M(e);var t=Y(e,D);null==n||n(t)}};return m.createElement(C,null,m.createElement(S,null,m.createElement(E.YI,(0,b.A)({value:I,onChange:J,min:a,max:o,step:c,precision:R,placeholder:P,style:{flex:1}},B)),z&&m.createElement(k,{value:D,onChange:function(e){var t=e.target.value;W(t);var l=Y(I,t);null==n||n(l)}},f.map((function(e){return m.createElement("option",{key:e,value:e},e)}))),T&&m.createElement(E.m_,{title:T},m.createElement(y.rUN,{style:{color:"#8c8c8c"}}))),g&&m.createElement(O,null,m.createElement(E.Ap,{value:I,onChange:J,min:a,max:o,step:c,tooltip:{formatter:function(e){return"".concat(e).concat(D)}}})),(void 0!==a||void 0!==o)&&m.createElement(A,{type:"secondary",style:{fontSize:"12px"}},"Range: ",a," - ",o))};var j,P,T,F,R,B,N=n(60436),L=["value","onChange","showPresets","showModeToggle","presets","placeholder"],I=E.o5.Text,M=g.styled.div(j||(j=(0,u.A)(["\n  width: 100%;\n"]))),$=g.styled.div(P||(P=(0,u.A)(["\n  width: 32px;\n  height: 32px;\n  border-radius: 4px;\n  border: 1px solid #d9d9d9;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: ",";\n  position: relative;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(45deg, #ccc 25%, transparent 25%), \n                linear-gradient(-45deg, #ccc 25%, transparent 25%), \n                linear-gradient(45deg, transparent 75%, #ccc 75%), \n                linear-gradient(-45deg, transparent 75%, #ccc 75%);\n    background-size: 8px 8px;\n    background-position: 0 0, 0 4px, 4px -4px, -4px 0px;\n    z-index: -1;\n  }\n"])),(function(e){return e.color||"#ffffff"})),H=g.styled.div(T||(T=(0,u.A)(["\n  display: flex;\n  gap: 4px;\n  margin-bottom: 8px;\n"]))),D=(0,g.styled)(E.$n)(F||(F=(0,u.A)(["\n  font-size: 12px;\n  height: 24px;\n  padding: 0 8px;\n"]))),W=g.styled.div(R||(R=(0,u.A)(["\n  display: grid;\n  grid-template-columns: repeat(8, 1fr);\n  gap: 4px;\n  margin-top: 8px;\n"]))),U=g.styled.div(B||(B=(0,u.A)(["\n  width: 24px;\n  height: 24px;\n  border-radius: 2px;\n  border: 1px solid #d9d9d9;\n  cursor: pointer;\n  background: ",";\n  \n  &:hover {\n    border-color: #1890ff;\n    transform: scale(1.1);\n  }\n"])),(function(e){return e.color}));const Y=function(e){var t=e.value,n=e.onChange,l=e.showPresets,a=void 0===l||l,r=e.showModeToggle,o=void 0===r||r,i=e.presets,c=void 0===i?[]:i,u=e.placeholder,p=void 0===u?"Enter color":u,d=(0,x.A)(e,L),f=(0,m.useState)("hex"),v=(0,s.A)(f,2),g=v[0],w=v[1],A=(0,m.useState)(t||"#ffffff"),C=(0,s.A)(A,2),S=C[0],O=C[1],k=(0,m.useState)(""),z=(0,s.A)(k,2),j=z[0],P=z[1],T=["#ffffff","#f5f5f5","#d9d9d9","#bfbfbf","#8c8c8c","#595959","#262626","#000000","#ff4d4f","#ff7a45","#ffa940","#ffec3d","#bae637","#73d13d","#40a9ff","#597ef7","#9254de","#f759ab","#ff85c0","#ffc069"].concat((0,N.A)(h.Ay.colors.primary?[h.Ay.colors.primary[500]]:[]),(0,N.A)(h.Ay.colors.secondary?[h.Ay.colors.secondary[500]]:[]),(0,N.A)(c));(0,m.useEffect)((function(){t&&(O(t),P(F(t,g)))}),[t,g]);var F=function(e,t){if(!e)return"";try{switch(t){case"hex":return e.startsWith("#")?e:"#".concat(e);case"rgb":return R(e);case"hsl":return B(e);default:return e}}catch(t){return e}},R=function(e){if(!e.startsWith("#"))return e;var t=parseInt(e.slice(1,3),16),n=parseInt(e.slice(3,5),16),l=parseInt(e.slice(5,7),16);return"rgb(".concat(t,", ").concat(n,", ").concat(l,")")},B=function(e){return e.startsWith("#")?"hsl(0, 0%, 50%)":e},Y=m.createElement("div",{style:{width:280}},o&&m.createElement(m.Fragment,null,m.createElement(H,null,m.createElement(D,{type:"hex"===g?"primary":"default",size:"small",onClick:function(){return w("hex")}},"HEX"),m.createElement(D,{type:"rgb"===g?"primary":"default",size:"small",onClick:function(){return w("rgb")}},"RGB"),m.createElement(D,{type:"hsl"===g?"primary":"default",size:"small",onClick:function(){return w("hsl")}},"HSL")),m.createElement(E.cG,{style:{margin:"8px 0"}})),m.createElement(E.sk,(0,b.A)({value:S,onChange:function(e){var t=e.toHexString();O(t),P(F(t,g)),null==n||n(t)},showText:!0,size:"large"},d)),a&&m.createElement(m.Fragment,null,m.createElement(E.cG,{style:{margin:"8px 0"}}),m.createElement(I,{strong:!0,style:{fontSize:"12px"}},"Color Presets"),m.createElement(W,null,T.slice(0,24).map((function(e,t){return m.createElement(U,{key:t,color:e,onClick:function(){return O(t=e),P(F(t,g)),void(null==n||n(t));var t},title:e})})))));return m.createElement(M,null,m.createElement(E.$x.Compact,{style:{width:"100%"}},m.createElement(E.AM,{content:Y,trigger:"click",placement:"bottomLeft"},m.createElement($,{color:S},m.createElement(y.Ebl,{style:{color:"rgba(0,0,0,0.3)"}}))),m.createElement(E.pd,{value:j,onChange:function(e){var t,l=e.target.value;P(l),(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(t=l)||/^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/.test(t)||/^hsl\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)$/.test(t))&&(O(l),null==n||n(l))},placeholder:p,style:{flex:1}})))};var J,V,G,X,q,Z,_,K,Q,ee,te,ne=n(82284),le=["value","onChange","type","showVisual","showPresets","unit"];function ae(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}var re=E.o5.Text,oe=g.styled.div(J||(J=(0,u.A)(["\n  width: 100%;\n"]))),ie=g.styled.div(V||(V=(0,u.A)(["\n  position: relative;\n  width: 120px;\n  height: 120px;\n  margin: 16px auto;\n  background: #f5f5f5;\n  border: 2px dashed #d9d9d9;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n"]))),ce=g.styled.div(G||(G=(0,u.A)(["\n  width: 60px;\n  height: 60px;\n  background: #1890ff;\n  opacity: 0.3;\n  border-radius: 4px;\n  position: relative;\n"]))),se=g.styled.div(X||(X=(0,u.A)(["\n  position: absolute;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n"]))),ue=(0,g.styled)(se)(q||(q=(0,u.A)(["\n  top: -30px;\n  left: 50%;\n  transform: translateX(-50%);\n"]))),pe=(0,g.styled)(se)(Z||(Z=(0,u.A)(["\n  right: -60px;\n  top: 50%;\n  transform: translateY(-50%);\n"]))),de=(0,g.styled)(se)(_||(_=(0,u.A)(["\n  bottom: -30px;\n  left: 50%;\n  transform: translateX(-50%);\n"]))),me=(0,g.styled)(se)(K||(K=(0,u.A)(["\n  left: -60px;\n  top: 50%;\n  transform: translateY(-50%);\n"]))),fe=g.styled.div(Q||(Q=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"]))),ye=g.styled.div(ee||(ee=(0,u.A)(["\n  display: flex;\n  gap: 4px;\n  margin-top: 8px;\n"]))),ve=(0,g.styled)(E.$n)(te||(te=(0,u.A)(["\n  font-size: 12px;\n  height: 24px;\n  padding: 0 8px;\n"])));const ge=function(e){var t=e.value,n=e.onChange,l=e.type,a=void 0===l?"margin":l,r=e.showVisual,o=void 0===r||r,c=e.showPresets,u=void 0===c||c,p=e.unit,d=void 0===p?"px":p,f=((0,x.A)(e,le),(0,m.useState)({top:0,right:0,bottom:0,left:0})),v=(0,s.A)(f,2),g=v[0],h=v[1],b=(0,m.useState)(!1),w=(0,s.A)(b,2),A=w[0],C=w[1];(0,m.useEffect)((function(){if(t){var e=S(t);h(e)}}),[t]);var S=function(e){if(!e)return{top:0,right:0,bottom:0,left:0};if("object"===(0,ne.A)(e))return{top:parseFloat(e.top)||0,right:parseFloat(e.right)||0,bottom:parseFloat(e.bottom)||0,left:parseFloat(e.left)||0};var t=e.toString().split(/\s+/).map((function(e){return parseFloat(e.replace(/[^\d.-]/g,""))||0}));switch(t.length){case 1:return{top:t[0],right:t[0],bottom:t[0],left:t[0]};case 2:return{top:t[0],right:t[1],bottom:t[0],left:t[1]};case 3:return{top:t[0],right:t[1],bottom:t[2],left:t[1]};case 4:return{top:t[0],right:t[1],bottom:t[2],left:t[3]};default:return{top:0,right:0,bottom:0,left:0}}},O=function(e){var t=e.top,n=e.right,l=e.bottom,a=e.left;return t===n&&n===l&&l===a?"".concat(t).concat(d):t===l&&a===n?"".concat(t).concat(d," ").concat(n).concat(d):"".concat(t).concat(d," ").concat(n).concat(d," ").concat(l).concat(d," ").concat(a).concat(d)},k=function(e,t){var l=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ae(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ae(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},g);A?(l.top=t,l.right=t,l.bottom=t,l.left=t):l[e]=t,h(l),null==n||n(O(l))};return m.createElement(oe,null,m.createElement(fe,null,m.createElement(re,{strong:!0,style:{fontSize:"12px"}},"margin"===a?"Margin":"Padding"),m.createElement(E.$n,{type:A?"primary":"default",size:"small",icon:A?m.createElement(y.t7c,null):m.createElement(y.Rrh,null),onClick:function(){C(!A)},title:A?"Unlink sides":"Link all sides"})),o&&m.createElement(ie,null,m.createElement(ue,null,m.createElement(E.YI,{size:"small",value:g.top,onChange:function(e){return k("top",e||0)},style:{width:50},min:0})),m.createElement(pe,null,m.createElement(E.YI,{size:"small",value:g.right,onChange:function(e){return k("right",e||0)},style:{width:50},min:0})),m.createElement(de,null,m.createElement(E.YI,{size:"small",value:g.bottom,onChange:function(e){return k("bottom",e||0)},style:{width:50},min:0})),m.createElement(me,null,m.createElement(E.YI,{size:"small",value:g.left,onChange:function(e){return k("left",e||0)},style:{width:50},min:0})),m.createElement(ce,null)),m.createElement(E.$x,{direction:"vertical",style:{width:"100%"}},m.createElement(fe,null,m.createElement(re,{style:{fontSize:"12px",minWidth:"30px"}},"Top:"),m.createElement(E.YI,{value:g.top,onChange:function(e){return k("top",e||0)},style:{flex:1},min:0,addonAfter:d})),m.createElement(fe,null,m.createElement(re,{style:{fontSize:"12px",minWidth:"30px"}},"Right:"),m.createElement(E.YI,{value:g.right,onChange:function(e){return k("right",e||0)},style:{flex:1},min:0,addonAfter:d})),m.createElement(fe,null,m.createElement(re,{style:{fontSize:"12px",minWidth:"30px"}},"Bottom:"),m.createElement(E.YI,{value:g.bottom,onChange:function(e){return k("bottom",e||0)},style:{flex:1},min:0,addonAfter:d})),m.createElement(fe,null,m.createElement(re,{style:{fontSize:"12px",minWidth:"30px"}},"Left:"),m.createElement(E.YI,{value:g.left,onChange:function(e){return k("left",e||0)},style:{flex:1},min:0,addonAfter:d}))),u&&m.createElement(ye,null,m.createElement(re,{style:{fontSize:"12px",marginRight:"8px"}},"Presets:"),["0px","4px","8px","12px","16px","24px","32px","8px 16px","16px 24px"].map((function(e,t){return m.createElement(ve,{key:t,onClick:function(){return function(e){var t=S(e);h(t),null==n||n(O(t))}(e)},title:"Apply ".concat(e)},e)}))))};var he,be,xe,Ee,we=["value","onChange","showPreview"],Ae=E.o5.Text,Ce=E.l6.Option,Se=g.styled.div(he||(he=(0,u.A)(["\n  width: 100%;\n"]))),Oe=g.styled.div(be||(be=(0,u.A)(["\n  width: 100%;\n  height: 60px;\n  margin: 12px 0;\n  background: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 4px;\n  border: ",";\n"])),(function(e){return e.borderStyle||"1px solid #d9d9d9"})),ke=g.styled.div(xe||(xe=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"]))),ze=(0,g.styled)(Ae)(Ee||(Ee=(0,u.A)(["\n  min-width: 60px;\n  font-size: 12px;\n  font-weight: 500;\n"])));const je=function(e){var t=e.value,n=e.onChange,l=e.showPreview,a=void 0===l||l,r=((0,x.A)(e,we),(0,m.useState)("solid")),o=(0,s.A)(r,2),i=o[0],c=o[1],u=(0,m.useState)("1px"),p=(0,s.A)(u,2),d=p[0],f=p[1],v=(0,m.useState)("#d9d9d9"),g=(0,s.A)(v,2),h=g[0],b=g[1];(0,m.useEffect)((function(){if(t){var e=w(t);c(e.style),f(e.width),b(e.color)}}),[t]);var w=function(e){if(!e)return{style:"solid",width:"1px",color:"#d9d9d9"};if("object"===(0,ne.A)(e))return{style:e.style||"solid",width:e.width||"1px",color:e.color||"#d9d9d9"};if("string"==typeof e){var t=e.split(/\s+/),n="1px",l="solid",a="#d9d9d9";return t.forEach((function(e){e.match(/^\d+(\.\d+)?(px|em|rem|%)$/)?n=e:["none","solid","dashed","dotted","double","groove","ridge","inset","outset"].includes(e)?l=e:(e.startsWith("#")||e.startsWith("rgb")||e.startsWith("hsl")||A(e))&&(a=e)})),{style:l,width:n,color:a}}return{style:"solid",width:"1px",color:"#d9d9d9"}},A=function(e){return["black","white","red","green","blue","yellow","orange","purple","pink","brown","gray","grey","transparent"].includes(e.toLowerCase())},C=function(e,t,n){return"none"===e?"none":"".concat(t," ").concat(e," ").concat(n)},S=C(i,d,h);return m.createElement(Se,null,m.createElement(E.$x,{direction:"vertical",style:{width:"100%"}},m.createElement(ke,null,m.createElement(ze,null,"Style:"),m.createElement(E.l6,{value:i,onChange:function(e){c(e);var t=C(e,d,h);null==n||n(t)},style:{flex:1},size:"small"},[{value:"none",label:"None"},{value:"solid",label:"Solid"},{value:"dashed",label:"Dashed"},{value:"dotted",label:"Dotted"},{value:"double",label:"Double"},{value:"groove",label:"Groove"},{value:"ridge",label:"Ridge"},{value:"inset",label:"Inset"},{value:"outset",label:"Outset"}].map((function(e){return m.createElement(Ce,{key:e.value,value:e.value},e.label)})))),"none"!==i&&m.createElement(m.Fragment,null,m.createElement(ke,null,m.createElement(ze,null,"Width:"),m.createElement("div",{style:{flex:1}},m.createElement(z,{value:d,onChange:function(e){f(e);var t=C(i,e,h);null==n||n(t)},min:0,max:20,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement(ke,null,m.createElement(ze,null,"Color:"),m.createElement("div",{style:{flex:1}},m.createElement(Y,{value:h,onChange:function(e){b(e);var t=C(i,d,e);null==n||n(t)},placeholder:"Border color"})))),a&&m.createElement(m.Fragment,null,m.createElement(E.cG,{style:{margin:"8px 0"}}),m.createElement(Ae,{style:{fontSize:"12px",marginBottom:"4px"}},"Preview:"),m.createElement(Oe,{borderStyle:S},m.createElement(y.bnM,{style:{fontSize:"24px",color:"#8c8c8c"}})),m.createElement(Ae,{type:"secondary",style:{fontSize:"11px",textAlign:"center"}},S))))};var Pe,Te,Fe,Re,Be,Ne=["value","onChange","showPreview"],Le=E.o5.Text,Ie=g.styled.div(Pe||(Pe=(0,u.A)(["\n  width: 100%;\n"]))),Me=g.styled.div(Te||(Te=(0,u.A)(["\n  width: 100%;\n  height: 80px;\n  margin: 12px 0;\n  background: #ffffff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 8px;\n  box-shadow: ",";\n  border: 1px solid #f0f0f0;\n"])),(function(e){return e.shadowStyle||"none"})),$e=g.styled.div(Fe||(Fe=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"]))),He=(0,g.styled)(Le)(Re||(Re=(0,u.A)(["\n  min-width: 80px;\n  font-size: 12px;\n  font-weight: 500;\n"]))),De=g.styled.div(Be||(Be=(0,u.A)(["\n  width: 60px;\n  height: 40px;\n  background: #1890ff;\n  border-radius: 4px;\n  opacity: 0.8;\n"])));const We=function(e){var t=e.value,n=e.onChange,l=e.showPreview,a=void 0===l||l,r=((0,x.A)(e,Ne),(0,m.useState)("0px")),o=(0,s.A)(r,2),i=o[0],c=o[1],u=(0,m.useState)("2px"),p=(0,s.A)(u,2),d=p[0],f=p[1],y=(0,m.useState)("4px"),v=(0,s.A)(y,2),g=v[0],h=v[1],b=(0,m.useState)("0px"),w=(0,s.A)(b,2),A=w[0],C=w[1],S=(0,m.useState)("rgba(0, 0, 0, 0.1)"),O=(0,s.A)(S,2),k=O[0],j=O[1],P=(0,m.useState)(!1),T=(0,s.A)(P,2),F=T[0],R=T[1];(0,m.useEffect)((function(){if(t){var e=B(t);c(e.offsetX),f(e.offsetY),h(e.blurRadius),C(e.spreadRadius),j(e.color),R(e.inset)}}),[t]);var B=function(e){if(!e||"none"===e)return{offsetX:"0px",offsetY:"2px",blurRadius:"4px",spreadRadius:"0px",color:"rgba(0, 0, 0, 0.1)",inset:!1};if("object"===(0,ne.A)(e))return{offsetX:e.offsetX||"0px",offsetY:e.offsetY||"2px",blurRadius:e.blurRadius||"4px",spreadRadius:e.spreadRadius||"0px",color:e.color||"rgba(0, 0, 0, 0.1)",inset:e.inset||!1};if("string"==typeof e){var t=e.trim(),n=!1;t.startsWith("inset ")&&(n=!0,t=t.replace("inset ",""));var l="rgba(0, 0, 0, 0.1)",a=t.match(/(rgba?\([^)]+\)|hsla?\([^)]+\)|#[a-fA-F0-9]{3,8}|\b\w+\b)$/);a&&(l=a[1],t=t.replace(a[1],"").trim());var r=t.split(/\s+/).filter((function(e){return e}));return{offsetX:r[0]||"0px",offsetY:r[1]||"2px",blurRadius:r[2]||"4px",spreadRadius:r[3]||"0px",color:l,inset:n}}return{offsetX:"0px",offsetY:"2px",blurRadius:"4px",spreadRadius:"0px",color:"rgba(0, 0, 0, 0.1)",inset:!1}},N=function(e,t,n,l,a,r){var o=[e,t,n,l,a].join(" ");return r?"inset ".concat(o):o},L=function(e,t){var l=i,a=d,r=g,o=A,s=k,u=F;switch(e){case"offsetX":l=t,c(t);break;case"offsetY":a=t,f(t);break;case"blurRadius":r=t,h(t);break;case"spreadRadius":o=t,C(t);break;case"color":s=t,j(t);break;case"inset":u=t,R(t)}var p=N(l,a,r,o,s,u);null==n||n(p)},I=N(i,d,g,A,k,F);return m.createElement(Ie,null,m.createElement(E.$x,{direction:"vertical",style:{width:"100%"}},m.createElement($e,null,m.createElement(He,null,"Inset:"),m.createElement(E.dO,{checked:F,onChange:function(e){return L("inset",e)},size:"small"})),m.createElement($e,null,m.createElement(He,null,"Offset X:"),m.createElement("div",{style:{flex:1}},m.createElement(z,{value:i,onChange:function(e){return L("offsetX",e)},min:-50,max:50,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement($e,null,m.createElement(He,null,"Offset Y:"),m.createElement("div",{style:{flex:1}},m.createElement(z,{value:d,onChange:function(e){return L("offsetY",e)},min:-50,max:50,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement($e,null,m.createElement(He,null,"Blur:"),m.createElement("div",{style:{flex:1}},m.createElement(z,{value:g,onChange:function(e){return L("blurRadius",e)},min:0,max:100,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement($e,null,m.createElement(He,null,"Spread:"),m.createElement("div",{style:{flex:1}},m.createElement(z,{value:A,onChange:function(e){return L("spreadRadius",e)},min:-50,max:50,step:1,unit:"px",units:["px","em","rem"],size:"small"}))),m.createElement($e,null,m.createElement(He,null,"Color:"),m.createElement("div",{style:{flex:1}},m.createElement(Y,{value:k,onChange:function(e){return L("color",e)},placeholder:"Shadow color"}))),a&&m.createElement(m.Fragment,null,m.createElement(E.cG,{style:{margin:"8px 0"}}),m.createElement(Le,{style:{fontSize:"12px",marginBottom:"4px"}},"Preview:"),m.createElement(Me,{shadowStyle:I},m.createElement(De,null)),m.createElement(Le,{type:"secondary",style:{fontSize:"11px",textAlign:"center"}},I))))};var Ue,Ye,Je,Ve,Ge=["value","onChange","showPreview"],Xe=E.o5.Text,qe=E.l6.Option,Ze=g.styled.div(Ue||(Ue=(0,u.A)(["\n  width: 100%;\n"]))),_e=g.styled.div(Ye||(Ye=(0,u.A)(["\n  width: 100%;\n  padding: 16px;\n  margin: 12px 0;\n  background: #f5f5f5;\n  border-radius: 4px;\n  border: 1px solid #d9d9d9;\n  font-family: ",";\n  font-size: ",";\n  font-weight: ",";\n  line-height: ",";\n  text-align: center;\n"])),(function(e){return e.fontFamily||"inherit"}),(function(e){return e.fontSize||"16px"}),(function(e){return e.fontWeight||"normal"}),(function(e){return e.lineHeight||"1.5"})),Ke=g.styled.div(Je||(Je=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n"]))),Qe=(0,g.styled)(Xe)(Ve||(Ve=(0,u.A)(["\n  min-width: 80px;\n  font-size: 12px;\n  font-weight: 500;\n"])));const et=function(e){var t=e.value,n=e.onChange,l=e.showPreview,a=void 0===l||l,r=((0,x.A)(e,Ge),(0,m.useState)("inherit")),o=(0,s.A)(r,2),i=o[0],c=o[1],u=(0,m.useState)("16px"),p=(0,s.A)(u,2),d=p[0],f=p[1],v=(0,m.useState)("normal"),g=(0,s.A)(v,2),h=g[0],b=g[1],w=(0,m.useState)("1.5"),A=(0,s.A)(w,2),C=A[0],S=A[1];(0,m.useEffect)((function(){if(t){var e=O(t);c(e.family),f(e.size),b(e.weight),S(e.lineHeight)}}),[t]);var O=function(e){return e?"object"===(0,ne.A)(e)?{family:e.fontFamily||e.family||"inherit",size:e.fontSize||e.size||"16px",weight:e.fontWeight||e.weight||"normal",lineHeight:e.lineHeight||"1.5"}:{family:e,size:d,weight:h,lineHeight:C}:{family:"inherit",size:"16px",weight:"normal",lineHeight:"1.5"}},k=function(e,t){var l=i,a=d,r=h,o=C;switch(e){case"family":l=t,c(t);break;case"size":a=t,f(t);break;case"weight":r=t,b(t);break;case"lineHeight":o=t,S(t)}null==n||n({fontFamily:l,fontSize:a,fontWeight:r,lineHeight:o})};return m.createElement(Ze,null,m.createElement(E.$x,{direction:"vertical",style:{width:"100%"}},m.createElement(Ke,null,m.createElement(Qe,null,"Family:"),m.createElement(E.l6,{value:i,onChange:function(e){return k("family",e)},style:{flex:1},size:"small",showSearch:!0,placeholder:"Select font family"},[{value:"inherit",label:"Inherit"},{value:"Arial, sans-serif",label:"Arial"},{value:"Helvetica, Arial, sans-serif",label:"Helvetica"},{value:'"Times New Roman", Times, serif',label:"Times New Roman"},{value:"Georgia, serif",label:"Georgia"},{value:'"Courier New", Courier, monospace',label:"Courier New"},{value:"Verdana, Geneva, sans-serif",label:"Verdana"},{value:'"Trebuchet MS", Helvetica, sans-serif',label:"Trebuchet MS"},{value:'"Lucida Sans Unicode", "Lucida Grande", sans-serif',label:"Lucida Sans"},{value:"Impact, Charcoal, sans-serif",label:"Impact"},{value:'"Comic Sans MS", cursive',label:"Comic Sans MS"},{value:'"Palatino Linotype", "Book Antiqua", Palatino, serif',label:"Palatino"},{value:'"Inter", -apple-system, BlinkMacSystemFont, sans-serif',label:"Inter"},{value:'"Roboto", sans-serif',label:"Roboto"},{value:'"Open Sans", sans-serif',label:"Open Sans"},{value:'"Lato", sans-serif',label:"Lato"},{value:'"Montserrat", sans-serif',label:"Montserrat"},{value:'"Source Sans Pro", sans-serif',label:"Source Sans Pro"}].map((function(e){return m.createElement(qe,{key:e.value,value:e.value},m.createElement("span",{style:{fontFamily:e.value}},e.label))})))),m.createElement(Ke,null,m.createElement(Qe,null,"Size:"),m.createElement("div",{style:{flex:1}},m.createElement(z,{value:d,onChange:function(e){return k("size",e)},min:8,max:72,step:1,unit:"px",units:["px","em","rem","%"],size:"small"}))),m.createElement(Ke,null,m.createElement(Qe,null,"Weight:"),m.createElement(E.l6,{value:h,onChange:function(e){return k("weight",e)},style:{flex:1},size:"small"},[{value:"100",label:"Thin (100)"},{value:"200",label:"Extra Light (200)"},{value:"300",label:"Light (300)"},{value:"normal",label:"Normal (400)"},{value:"500",label:"Medium (500)"},{value:"600",label:"Semi Bold (600)"},{value:"bold",label:"Bold (700)"},{value:"800",label:"Extra Bold (800)"},{value:"900",label:"Black (900)"}].map((function(e){return m.createElement(qe,{key:e.value,value:e.value},e.label)})))),m.createElement(Ke,null,m.createElement(Qe,null,"Line Height:"),m.createElement("div",{style:{flex:1}},m.createElement(z,{value:C,onChange:function(e){return k("lineHeight",e)},min:.5,max:3,step:.1,precision:1,showUnit:!1,size:"small",tooltip:"Line height as a multiplier (e.g., 1.5 = 150%)"}))),a&&m.createElement(m.Fragment,null,m.createElement(E.cG,{style:{margin:"8px 0"}}),m.createElement(Xe,{style:{fontSize:"12px",marginBottom:"4px"}},"Preview:"),m.createElement(_e,{fontFamily:i,fontSize:d,fontWeight:h,lineHeight:C},m.createElement(y.ld1,{style:{marginRight:"8px"}}),"The quick brown fox jumps over the lazy dog"),m.createElement(Xe,{type:"secondary",style:{fontSize:"11px",textAlign:"center"}},i," • ",d," • ",h," • ",C))))};var tt="text",nt="number",lt="boolean",at="color",rt="select",ot="spacing",it="border",ct="shadow",st="font",ut="array",pt="object",dt={button:{text:{type:tt,label:"Button Text",placeholder:"Enter button text"},variant:{type:rt,label:"Variant",options:[{value:"primary",label:"Primary"},{value:"secondary",label:"Secondary"},{value:"text",label:"Text"},{value:"link",label:"Link"},{value:"ghost",label:"Ghost"},{value:"dashed",label:"Dashed"}]},size:{type:rt,label:"Size",options:[{value:"small",label:"Small"},{value:"medium",label:"Medium"},{value:"large",label:"Large"}]},disabled:{type:lt,label:"Disabled"},block:{type:lt,label:"Full Width"},onClick:{type:tt,label:"onClick Handler",placeholder:"Enter function name"}},text:{content:{type:tt,label:"Text Content",multiline:!0,placeholder:"Enter text content"},variant:{type:rt,label:"Variant",options:[{value:"h1",label:"Heading 1"},{value:"h2",label:"Heading 2"},{value:"h3",label:"Heading 3"},{value:"h4",label:"Heading 4"},{value:"h5",label:"Heading 5"},{value:"h6",label:"Heading 6"},{value:"p",label:"Paragraph"},{value:"span",label:"Span"}]},color:{type:at,label:"Text Color"},align:{type:rt,label:"Text Alignment",options:[{value:"left",label:"Left"},{value:"center",label:"Center"},{value:"right",label:"Right"},{value:"justify",label:"Justify"}]}},input:{label:{type:tt,label:"Input Label",placeholder:"Enter input label"},placeholder:{type:tt,label:"Placeholder",placeholder:"Enter placeholder text"},type:{type:rt,label:"Input Type",options:[{value:"text",label:"Text"},{value:"password",label:"Password"},{value:"email",label:"Email"},{value:"number",label:"Number"},{value:"tel",label:"Telephone"},{value:"url",label:"URL"}]},required:{type:lt,label:"Required"},disabled:{type:lt,label:"Disabled"},validation:{type:rt,label:"Validation",options:[{value:"none",label:"None"},{value:"email",label:"Email"},{value:"url",label:"URL"},{value:"phone",label:"Phone"},{value:"custom",label:"Custom"}]}},card:{title:{type:tt,label:"Card Title",placeholder:"Enter card title"},description:{type:tt,label:"Description",multiline:!0,placeholder:"Enter card description"},image:{type:tt,label:"Image URL",placeholder:"Enter image URL"},elevation:{type:rt,label:"Elevation",options:[{value:"none",label:"None"},{value:"sm",label:"Small"},{value:"md",label:"Medium"},{value:"lg",label:"Large"}]},bordered:{type:lt,label:"Bordered"}}},mt={width:{type:tt,label:"Width",placeholder:"e.g., 100%, 200px",group:"dimensions"},height:{type:tt,label:"Height",placeholder:"e.g., 100%, 200px",group:"dimensions"},minWidth:{type:tt,label:"Min Width",placeholder:"e.g., 100px",group:"dimensions"},maxWidth:{type:tt,label:"Max Width",placeholder:"e.g., 500px",group:"dimensions"},minHeight:{type:tt,label:"Min Height",placeholder:"e.g., 100px",group:"dimensions"},maxHeight:{type:tt,label:"Max Height",placeholder:"e.g., 500px",group:"dimensions"},margin:{type:ot,label:"Margin",group:"spacing"},padding:{type:ot,label:"Padding",group:"spacing"},fontSize:{type:nt,label:"Font Size",min:8,max:72,unit:"px",units:["px","em","rem","%"],group:"typography"},fontWeight:{type:rt,label:"Font Weight",options:[{value:"normal",label:"Normal"},{value:"bold",label:"Bold"},{value:"lighter",label:"Lighter"},{value:"bolder",label:"Bolder"},{value:"100",label:"100"},{value:"200",label:"200"},{value:"300",label:"300"},{value:"400",label:"400"},{value:"500",label:"500"},{value:"600",label:"600"},{value:"700",label:"700"},{value:"800",label:"800"},{value:"900",label:"900"}],group:"typography"},lineHeight:{type:nt,label:"Line Height",min:.5,max:3,step:.1,precision:1,showUnit:!1,group:"typography"},fontFamily:{type:st,label:"Font Family",group:"typography"},color:{type:at,label:"Text Color",group:"colors"},backgroundColor:{type:at,label:"Background Color",group:"colors"},border:{type:it,label:"Border",group:"border"},borderTop:{type:it,label:"Border Top",group:"border"},borderRight:{type:it,label:"Border Right",group:"border"},borderBottom:{type:it,label:"Border Bottom",group:"border"},borderLeft:{type:it,label:"Border Left",group:"border"},borderRadius:{type:nt,label:"Border Radius",min:0,max:50,unit:"px",units:["px","em","rem","%"],group:"border"},boxShadow:{type:ct,label:"Box Shadow",group:"shadow"},textShadow:{type:ct,label:"Text Shadow",group:"shadow"},display:{type:rt,label:"Display",options:[{value:"block",label:"Block"},{value:"inline",label:"Inline"},{value:"inline-block",label:"Inline Block"},{value:"flex",label:"Flex"},{value:"grid",label:"Grid"},{value:"none",label:"None"}],group:"layout"},position:{type:rt,label:"Position",options:[{value:"static",label:"Static"},{value:"relative",label:"Relative"},{value:"absolute",label:"Absolute"},{value:"fixed",label:"Fixed"},{value:"sticky",label:"Sticky"}],group:"layout"}},ft=function(e){return e.replace(/([A-Z])/g," $1").replace(/^./,(function(e){return e.toUpperCase()})).trim()},yt=["propertyName","value","onChange","componentType","schema","showValidation"],vt=E.pd.TextArea,gt=E.l6.Option,ht=E.o5.Text;const bt=function(e){var t,n=e.propertyName,l=e.value,a=e.onChange,r=e.componentType,o=e.schema,i=e.showValidation,c=void 0===i||i,s=(0,x.A)(e,yt),u=o||function(e,t,n){return n&&dt[n]&&dt[n][e]?dt[n][e]:mt[e]?mt[e]:function(e,t){var n=e.toLowerCase();return n.includes("color")||n.includes("background")?{type:at,label:ft(e)}:"number"==typeof t||"string"==typeof t&&/^\d+(\.\d+)?(px|em|rem|%)?$/.test(t)?{type:nt,label:ft(e)}:"boolean"==typeof t?{type:lt,label:ft(e)}:n.includes("margin")||n.includes("padding")?{type:ot,label:ft(e)}:n.includes("border")&&!n.includes("radius")?{type:it,label:ft(e)}:n.includes("shadow")?{type:ct,label:ft(e)}:!n.includes("font")||n.includes("size")||n.includes("weight")?Array.isArray(t)?{type:ut,label:ft(e)}:"object"===(0,ne.A)(t)&&null!==t?{type:pt,label:ft(e)}:{type:tt,label:ft(e)}:{type:st,label:ft(e)}}(e,t)}(n,l,r),p=c?function(e,t){if(!t)return{valid:!0};switch(t.type){case nt:var n=parseFloat(e);if(isNaN(n))return{valid:!1,error:"Must be a valid number"};if(void 0!==t.min&&n<t.min)return{valid:!1,error:"Must be at least ".concat(t.min)};if(void 0!==t.max&&n>t.max)return{valid:!1,error:"Must be at most ".concat(t.max)};break;case at:if(e&&!/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/.test(e))return{valid:!1,error:"Must be a valid color value"};break;case rt:if(t.options&&e&&!t.options.some((function(t){return t.value===e})))return{valid:!1,error:"Must be one of the available options"}}return{valid:!0}}(l,u):{valid:!0},d=function(e){a&&a(e,n,u)},f=function(){return!c||p.valid?null:m.createElement(ht,{type:"danger",style:{fontSize:"12px",display:"block",marginTop:"4px"}},p.error)},v=function(){return u.description||u.tooltip?m.createElement(E.m_,{title:u.description||u.tooltip},m.createElement(y.rUN,{style:{color:"#8c8c8c",marginLeft:"4px"}})):null};switch(u.type){case tt:return u.multiline?m.createElement("div",null,m.createElement(vt,(0,b.A)({value:l,onChange:function(e){return d(e.target.value)},placeholder:u.placeholder,rows:u.rows||3,status:p.valid?"":"error"},s)),v(),f()):m.createElement("div",null,m.createElement(E.pd,(0,b.A)({value:l,onChange:function(e){return d(e.target.value)},placeholder:u.placeholder,status:p.valid?"":"error"},s)),v(),f());case nt:return m.createElement("div",null,m.createElement(z,(0,b.A)({value:l,onChange:d,min:u.min,max:u.max,step:u.step,precision:u.precision,unit:u.unit,units:u.units,showSlider:u.showSlider,showUnit:u.showUnit,placeholder:u.placeholder,tooltip:u.tooltip},s)),v(),f());case lt:return m.createElement("div",{style:{display:"flex",alignItems:"center",gap:"8px"}},m.createElement(E.dO,(0,b.A)({checked:l,onChange:d},s)),v(),f());case at:return m.createElement("div",null,m.createElement(Y,(0,b.A)({value:l,onChange:d,showPresets:u.showPresets,showModeToggle:u.showModeToggle,presets:u.presets,placeholder:u.placeholder},s)),v(),f());case rt:return m.createElement("div",null,m.createElement(E.l6,(0,b.A)({value:l,onChange:d,placeholder:u.placeholder,style:{width:"100%"},status:p.valid?"":"error"},s),null===(t=u.options)||void 0===t?void 0:t.map((function(e){return m.createElement(gt,{key:e.value,value:e.value},e.label)}))),v(),f());case ot:return m.createElement("div",null,m.createElement(ge,(0,b.A)({value:l,onChange:d,type:n.includes("margin")?"margin":"padding",showVisual:u.showVisual,showPresets:u.showPresets,unit:u.unit},s)),v(),f());case it:return m.createElement("div",null,m.createElement(je,(0,b.A)({value:l,onChange:d,showPreview:u.showPreview},s)),v(),f());case ct:return m.createElement("div",null,m.createElement(We,(0,b.A)({value:l,onChange:d,showPreview:u.showPreview},s)),v(),f());case st:return m.createElement("div",null,m.createElement(et,(0,b.A)({value:l,onChange:d,showPreview:u.showPreview},s)),v(),f());case ut:return m.createElement("div",null,m.createElement(vt,(0,b.A)({value:Array.isArray(l)?JSON.stringify(l,null,2):l,onChange:function(e){try{var t=JSON.parse(e.target.value);d(t)}catch(t){d(e.target.value)}},placeholder:u.placeholder||"Enter array as JSON",rows:4,status:p.valid?"":"error"},s)),v(),f());case pt:return m.createElement("div",null,m.createElement(vt,(0,b.A)({value:"object"===(0,ne.A)(l)?JSON.stringify(l,null,2):l,onChange:function(e){try{var t=JSON.parse(e.target.value);d(t)}catch(t){d(e.target.value)}},placeholder:u.placeholder||"Enter object as JSON",rows:6,status:p.valid?"":"error"},s)),v(),f());case"json":return m.createElement("div",null,m.createElement(vt,(0,b.A)({value:"string"==typeof l?l:JSON.stringify(l,null,2),onChange:function(e){return d(e.target.value)},placeholder:u.placeholder||"Enter JSON",rows:8,status:p.valid?"":"error",style:{fontFamily:"monospace"}},s)),v(),f());default:return m.createElement("div",null,m.createElement(E.pd,(0,b.A)({value:l,onChange:function(e){return d(e.target.value)},placeholder:u.placeholder||"Enter value",status:p.valid?"":"error"},s)),v(),f())}};var xt,Et,wt,At=["properties","onFilter","showGroupFilter","showTypeFilter","placeholder"],Ct=E.o5.Text,St=E.l6.Option,Ot=g.styled.div(xt||(xt=(0,u.A)(["\n  padding: 12px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n"]))),kt=g.styled.div(Et||(Et=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n"]))),zt=g.styled.div(wt||(wt=(0,u.A)(["\n  display: flex;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-top: 8px;\n"])));const jt=function(e){var t=e.properties,n=void 0===t?{}:t,l=e.onFilter,a=e.showGroupFilter,r=void 0===a||a,o=e.showTypeFilter,i=void 0===o||o,c=e.placeholder,u=void 0===c?"Search properties...":c,p=((0,x.A)(e,At),(0,m.useState)("")),d=(0,s.A)(p,2),f=d[0],v=d[1],g=(0,m.useState)("all"),h=(0,s.A)(g,2),b=h[0],w=h[1],A=(0,m.useState)("all"),C=(0,s.A)(A,2),S=C[0],O=C[1],k=(0,m.useMemo)((function(){var e=new Set,t=new Set;return Object.values(n).forEach((function(n){n.group&&e.add(n.group),n.type&&t.add(n.type)})),{groups:Array.from(e).sort(),types:Array.from(t).sort()}}),[n]),z=k.groups,j=k.types,P=(0,m.useMemo)((function(){var e={};return Object.entries(n).forEach((function(t){var n=(0,s.A)(t,2),l=n[0],a=n[1],r=!f||l.toLowerCase().includes(f.toLowerCase())||a.label&&a.label.toLowerCase().includes(f.toLowerCase())||a.description&&a.description.toLowerCase().includes(f.toLowerCase()),o="all"===b||a.group===b,i="all"===S||a.type===S;r&&o&&i&&(e[l]=a)})),e}),[n,f,b,S]),T=[f&&"search","all"!==b&&"group","all"!==S&&"type"].filter(Boolean),F=function(e){return e.charAt(0).toUpperCase()+e.slice(1).replace(/([A-Z])/g," $1")};return m.createElement(Ot,null,m.createElement(kt,null,m.createElement(E.pd,{prefix:m.createElement(y.VrN,null),placeholder:u,value:f,onChange:function(e){var t=e.target.value;v(t),null==l||l(P,{searchTerm:t,group:b,type:S})},allowClear:!0,style:{flex:1}}),r&&z.length>0&&m.createElement(E.l6,{value:b,onChange:function(e){w(e),null==l||l(P,{searchTerm:f,group:e,type:S})},style:{minWidth:120},size:"small"},m.createElement(St,{value:"all"},"All Groups"),z.map((function(e){return m.createElement(St,{key:e,value:e},F(e))}))),i&&j.length>0&&m.createElement(E.l6,{value:S,onChange:function(e){O(e),null==l||l(P,{searchTerm:f,group:b,type:e})},style:{minWidth:100},size:"small"},m.createElement(St,{value:"all"},"All Types"),j.map((function(e){return m.createElement(St,{key:e,value:e},F(e))}))),T.length>0&&m.createElement(y.ohj,{onClick:function(){v(""),w("all"),O("all"),null==l||l(n,{searchTerm:"",group:"all",type:"all"})},style:{cursor:"pointer",color:"#8c8c8c"},title:"Clear all filters"})),T.length>0&&m.createElement("div",null,m.createElement(E.$x,{size:4,style:{marginBottom:4}},m.createElement(y.Lxx,{style:{fontSize:"12px",color:"#8c8c8c"}}),m.createElement(Ct,{type:"secondary",style:{fontSize:"12px"}},"Active filters:")),m.createElement(zt,null,f&&m.createElement(E.vw,{closable:!0,onClose:function(){v(""),null==l||l(P,{searchTerm:"",group:b,type:S})},size:"small"},'Search: "',f,'"'),"all"!==b&&m.createElement(E.vw,{closable:!0,onClose:function(){w("all"),null==l||l(P,{searchTerm:f,group:"all",type:S})},size:"small"},"Group: ",F(b)),"all"!==S&&m.createElement(E.vw,{closable:!0,onClose:function(){O("all"),null==l||l(P,{searchTerm:f,group:b,type:"all"})},size:"small"},"Type: ",F(S)))),m.createElement(E.$x,{style:{marginTop:8,fontSize:"12px"}},m.createElement(Ct,{type:"secondary"},"Showing ",Object.keys(P).length," of ",Object.keys(n).length," properties")))};var Pt,Tt,Ft,Rt,Bt,Nt,Lt,It,Mt=["groupName","properties","values","onChange","componentType","collapsible","defaultExpanded","showResetAll","showPropertyCount"],$t=(E.SD.Panel,E.o5.Text),Ht=g.styled.div(Pt||(Pt=(0,u.A)(["\n  margin-bottom: 16px;\n"]))),Dt=g.styled.div(Tt||(Tt=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 12px;\n  background: #fafafa;\n  border: 1px solid #f0f0f0;\n  border-radius: 4px;\n  cursor: pointer;\n  user-select: none;\n  \n  &:hover {\n    background: #f5f5f5;\n  }\n"]))),Wt=g.styled.div(Ft||(Ft=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"]))),Ut=g.styled.div(Rt||(Rt=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  gap: 4px;\n"]))),Yt=g.styled.div(Bt||(Bt=(0,u.A)(["\n  padding: 12px;\n  border-bottom: 1px solid #f0f0f0;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n"]))),Jt=g.styled.div(Nt||(Nt=(0,u.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 8px;\n"]))),Vt=(0,g.styled)($t)(Lt||(Lt=(0,u.A)(["\n  font-weight: 500;\n  font-size: 13px;\n"]))),Gt=(0,g.styled)($t)(It||(It=(0,u.A)(["\n  font-size: 12px;\n  color: #8c8c8c;\n  display: block;\n  margin-top: 2px;\n"])));const Xt=function(e){var t,n=e.groupName,l=e.properties,a=void 0===l?{}:l,r=e.values,o=void 0===r?{}:r,i=e.onChange,c=e.componentType,u=e.collapsible,p=void 0===u||u,d=e.defaultExpanded,f=void 0===d||d,v=e.showResetAll,g=void 0===v||v,h=e.showPropertyCount,w=void 0===h||h,A=(0,x.A)(e,Mt),C=(0,m.useState)(f),S=(0,s.A)(C,2),O=S[0],k=S[1],z=function(e,t,n){i&&i(t,e,n)},j=Object.keys(a).filter((function(e){var t=o[e];return t!==(a[e].defaultValue||"")&&""!==t&&null!=t})).length,P=Object.keys(a).length;return 0===P?null:m.createElement(Ht,null,m.createElement(Dt,{onClick:function(){p&&k(!O)}},m.createElement(Wt,null,p&&(O?m.createElement(y.lHd,{style:{fontSize:"12px"}}):m.createElement(y.Xq1,{style:{fontSize:"12px"}})),(t=n,{basic:m.createElement(y.JO7,null),dimensions:m.createElement(y.x18,null),spacing:m.createElement(y.hy2,null),typography:m.createElement(y.ld1,null),colors:m.createElement(y.Ebl,null),border:m.createElement(y.bnM,null),shadow:m.createElement(y.NSj,null),layout:m.createElement(y.hy2,null)}[t.toLowerCase()]||m.createElement(y.JO7,null)),m.createElement($t,{strong:!0,style:{fontSize:"14px"}},function(e){return e.charAt(0).toUpperCase()+e.slice(1).replace(/([A-Z])/g," $1")}(n)),w&&m.createElement(E.$x,{size:4},m.createElement(E.Ex,{count:P,size:"small",color:"#f0f0f0",style:{color:"#8c8c8c"}}),j>0&&m.createElement(E.Ex,{count:j,size:"small",color:"#1890ff"}))),m.createElement(Ut,null,g&&j>0&&m.createElement(E.m_,{title:"Reset all properties in this group"},m.createElement(E.$n,{type:"text",size:"small",icon:m.createElement(y.Xrf,null),onClick:function(e){e.stopPropagation(),Object.keys(a).forEach((function(e){var t=a[e],n=t.defaultValue||"";z(n,e,t)}))},style:{fontSize:"12px"}})))),O&&m.createElement("div",{style:{border:"1px solid #f0f0f0",borderTop:"none",borderRadius:"0 0 4px 4px"}},Object.entries(a).map((function(e){var t=(0,s.A)(e,2),n=t[0],l=t[1],r=o[n],i=void 0!==r&&""!==r&&null!==r&&r!==(l.defaultValue||"");return m.createElement(Yt,{key:n},m.createElement(Jt,null,m.createElement("div",null,m.createElement(Vt,null,l.label||n,l.required&&m.createElement($t,{type:"danger"}," *")),l.description&&m.createElement(Gt,null,l.description)),i&&m.createElement(E.m_,{title:"Reset to default"},m.createElement(E.$n,{type:"text",size:"small",icon:m.createElement(y.Xrf,null),onClick:function(e){return function(e,t){t.stopPropagation();var n=a[e],l=n.defaultValue||"";z(l,e,n)}(n,e)},style:{fontSize:"12px"}}))),m.createElement(bt,(0,b.A)({propertyName:n,value:r,onChange:z,componentType:c,schema:l,size:"small"},A)))}))))};var qt,Zt,_t,Kt,Qt,en=["component","properties","values","showPreview","showCode","showValidation","onReset"];function tn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function nn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?tn(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):tn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ln=E.o5.Text,an=(E.o5.Title,g.styled.div(qt||(qt=(0,u.A)(["\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  background: white;\n  border-bottom: 1px solid #f0f0f0;\n"])))),rn=g.styled.div(Zt||(Zt=(0,u.A)(["\n  min-height: 120px;\n  padding: 16px;\n  background: ",";\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  overflow: hidden;\n"])),(function(e){return e.background||"#f5f5f5"})),on=g.styled.div(_t||(_t=(0,u.A)(["\n  transition: all 0.2s ease;\n  ","\n"])),(function(e){return e.styles||""})),cn=g.styled.pre(Kt||(Kt=(0,u.A)(["\n  background: #f6f8fa;\n  border: 1px solid #e1e4e8;\n  border-radius: 4px;\n  padding: 12px;\n  font-size: 12px;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  overflow-x: auto;\n  max-height: 200px;\n  margin: 0;\n"]))),sn=g.styled.div(Qt||(Qt=(0,u.A)(["\n  margin-top: 8px;\n"])));const un=function(e){var t=e.component,n=e.properties,l=void 0===n?{}:n,a=e.values,r=void 0===a?{}:a,o=e.showPreview,i=void 0===o||o,c=e.showCode,u=void 0!==c&&c,p=e.showValidation,d=void 0===p||p,f=e.onReset,v=((0,x.A)(e,en),(0,m.useState)(i)),g=(0,s.A)(v,2),h=g[0],w=g[1],A=(0,m.useState)(u),C=(0,s.A)(A,2),S=C[0],O=C[1],k=(0,m.useState)({}),z=(0,s.A)(k,2),j=z[0],P=z[1],T=(0,m.useMemo)((function(){var e={};return Object.entries(r).forEach((function(t){var n=(0,s.A)(t,2),l=n[0],a=n[1];if(null!=a&&""!==a){var r=l.replace(/([A-Z])/g,"-$1").toLowerCase();"object"===(0,ne.A)(a)&&null!==a?"font"===l||"fontFamily"===l?Object.entries(a).forEach((function(t){var n=(0,s.A)(t,2),l=n[0],a=n[1],r=l.replace(/([A-Z])/g,"-$1").toLowerCase();e[r]=a})):e[r]=JSON.stringify(a):e[r]=a}})),e}),[r]),F=(0,m.useMemo)((function(){var e=Object.entries(T).map((function(e){var t=(0,s.A)(e,2),n=t[0],l=t[1];return"  ".concat(n,": ").concat(l,";")}));return".component {\n".concat(e.join("\n"),"\n}")}),[T]),R=(0,m.useMemo)((function(){var e={};return Object.entries(T).forEach((function(t){var n=(0,s.A)(t,2),l=n[0],a=n[1],r=l.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}));e[r]=a})),e}),[T]);(0,m.useEffect)((function(){var e={};Object.entries(r).forEach((function(t){var n=(0,s.A)(t,2),a=n[0],r=n[1],o=l[a];if(o&&null!=r&&""!==r)if("number"===o.type){var i=parseFloat(r);isNaN(i)?e[a]="Must be a valid number":void 0!==o.min&&i<o.min?e[a]="Must be at least ".concat(o.min):void 0!==o.max&&i>o.max&&(e[a]="Must be at most ".concat(o.max))}else"color"===o.type&&(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/.test(r)||(e[a]="Must be a valid color value"))})),P(e)}),[r,l]);var B=Object.keys(j).length>0,N=Object.keys(r).some((function(e){return void 0!==r[e]&&""!==r[e]&&null!==r[e]}));return m.createElement(an,null,m.createElement(E.Zp,{size:"small",title:"Property Preview"},m.createElement(E.$x,{direction:"vertical",style:{width:"100%"}},m.createElement(E.$x,{style:{width:"100%",justifyContent:"space-between"}},m.createElement(E.$x,null,m.createElement(E.dO,{checked:h,onChange:w,checkedChildren:m.createElement(y.Om2,null),unCheckedChildren:m.createElement(y.LCF,null),size:"small"}),m.createElement(ln,{style:{fontSize:"12px"}},"Live Preview")),m.createElement(E.$x,null,m.createElement(E.$n,{type:"text",size:"small",icon:m.createElement(y.C$o,null),onClick:function(){return O(!S)},style:{fontSize:"12px"}},S?"Hide":"Show"," CSS"),N&&m.createElement(E.$n,{type:"text",size:"small",icon:m.createElement(y.Xrf,null),onClick:f,style:{fontSize:"12px"}},"Reset All"))),h&&m.createElement(rn,null,m.createElement(on,{styles:Object.entries(R).map((function(e){var t=(0,s.A)(e,2),n=t[0],l=t[1];return"".concat(n,": ").concat(l,";")})).join(" ")},function(){var e={style:R,className:"preview-element"};switch(null==t?void 0:t.type){case"button":return m.createElement("button",e,r.text||"Button");case"text":var n=r.variant||"p";return m.createElement(n,e,r.content||"Sample text");case"input":return m.createElement("input",(0,b.A)({},e,{type:r.type||"text",placeholder:r.placeholder||"Enter text",disabled:r.disabled}));case"card":return m.createElement("div",(0,b.A)({},e,{style:nn(nn({},R),{},{border:"1px solid #d9d9d9",borderRadius:"4px",padding:"16px",minWidth:"200px"})}),r.title&&m.createElement("h4",{style:{margin:"0 0 8px 0"}},r.title),r.description&&m.createElement("p",{style:{margin:0,color:"#666"}},r.description));default:return m.createElement("div",(0,b.A)({},e,{style:nn(nn({},R),{},{padding:"16px",background:"#fff",border:"1px solid #d9d9d9",borderRadius:"4px"})}),(null==t?void 0:t.name)||"Component"," Preview")}}())),S&&m.createElement(m.Fragment,null,m.createElement(E.cG,{style:{margin:"8px 0"}}),m.createElement("div",null,m.createElement(ln,{strong:!0,style:{fontSize:"12px"}},"Generated CSS:"),m.createElement(cn,null,F))),d&&B&&m.createElement(sn,null,m.createElement(E.Fc,{message:"Validation Errors",description:m.createElement("ul",{style:{margin:0,paddingLeft:"16px"}},Object.entries(j).map((function(e){var t=(0,s.A)(e,2),n=t[0],l=t[1];return m.createElement("li",{key:n,style:{fontSize:"12px"}},m.createElement("strong",null,n,":")," ",l)}))),type:"error",size:"small",showIcon:!0})),d&&!B&&N&&m.createElement(sn,null,m.createElement(E.Fc,{message:"All properties are valid",type:"success",size:"small",showIcon:!0})))))};var pn,dn,mn,fn,yn=n(81616),vn=n(2543),gn=["name"];function hn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function bn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?hn(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):hn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}E.o5.Title;var xn=E.o5.Text,En=E.tU.TabPane,wn=g.styled.div(pn||(pn=(0,u.A)(["\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n"]))),An=g.styled.div(dn||(dn=(0,u.A)(["\n  flex: 1;\n  overflow-y: auto;\n  padding: 0;\n"]))),Cn=g.styled.div(mn||(mn=(0,u.A)(["\n  padding: 16px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n"]))),Sn=g.styled.div(fn||(fn=(0,u.A)(["\n  padding: 0;\n"])));const On=function(e){var t=e.component,n=e.onUpdate,l=e.onRealTimeUpdate,a=e.enableRealTimePreview,r=void 0===a||a,o=e.enableCollaboration,c=void 0!==o&&o,u=e.collaborativeSession,p=void 0===u?null:u,d=(0,f.wA)(),v=(0,m.useState)("basic"),g=(0,s.A)(v,2),h=g[0],b=g[1],w=(0,m.useState)({}),A=(0,s.A)(w,2),C=A[0],S=A[1],O=(0,m.useState)({}),k=(0,s.A)(O,2),z=(k[0],k[1]),j=(0,m.useState)(!1),P=(0,s.A)(j,2),T=P[0],F=P[1],R=(0,m.useState)(r),B=(0,s.A)(R,2),N=B[0],L=B[1],I=(0,m.useState)(!1),M=(0,s.A)(I,2),$=M[0],H=M[1],D=(0,m.useState)(null),W=(0,s.A)(D,2),U=W[0],Y=W[1],J=(0,m.useRef)(null),V=(0,m.useRef)({});(0,m.useEffect)((function(){if(t){var e=bn(bn({name:t.name},t.props),t.style);S(e),F(!1)}}),[t]);var G=(0,m.useMemo)((function(){return null!=t&&t.type?(e=t.type,dt[e]||{}):{};var e}),[null==t?void 0:t.type]),X=(0,m.useMemo)((function(){return e={},Object.entries(mt).forEach((function(t){var n=(0,s.A)(t,2),l=n[0],a=n[1],r=a.group||"other";e[r]||(e[r]={}),e[r][l]=a})),e;var e}),[]),q=(0,m.useMemo)((function(){return(0,vn.debounce)((function(e){N&&l&&(H(!0),l(e),Y(new Date),setTimeout((function(){return H(!1)}),300))}),300)}),[N,l]),Z=(0,m.useCallback)((function(e){n&&n(e)}),[n]),_=(0,m.useCallback)((function(e,n,l){var a=bn(bn({},C),{},(0,i.A)({},e,n));S(a),F(!0);var r=bn(bn({},t),{},{name:"name"===e?n:a.name,props:bn(bn({},t.props),"name"===e||mt[e]?{}:(0,i.A)({},e,n)),style:bn(bn({},t.style),mt[e]?(0,i.A)({},e,n):{})});Z(r),N&&q(r),V.current=a}),[C,t,N,Z,q]),K=function(){if(t){var e=bn(bn({name:t.name},t.props),t.style);S(e),F(!1)}},Q=function(e,t){z(e)};(0,m.useEffect)((function(){return function(){J.current&&clearTimeout(J.current),q.cancel()}}),[q]);var ee=(0,m.useCallback)((function(e){if(L(e),e&&T){var n=bn(bn({},t),{},{name:C.name||t.name,props:bn({},t.props),style:bn({},t.style)});Object.entries(C).forEach((function(e){var t=(0,s.A)(e,2),l=t[0],a=t[1];"name"!==l&&(mt[l]?n.style[l]=a:n.props[l]=a)})),q(n)}}),[T,t,C,q]);return t?m.createElement(wn,null,m.createElement("div",{style:{padding:"12px 16px",borderBottom:"1px solid #f0f0f0",background:"#fafafa",display:"flex",alignItems:"center",justifyContent:"space-between"}},m.createElement(E.$x,null,m.createElement(xn,{strong:!0,style:{fontSize:"14px"}},t.name||t.type," Properties"),$&&m.createElement(y.OmY,{spin:!0,style:{color:"#1890ff"}})),m.createElement(E.$x,null,r&&m.createElement(E.m_,{title:"Toggle real-time preview updates"},m.createElement(E.dO,{size:"small",checked:N,onChange:ee,checkedChildren:m.createElement(y.Om2,null),unCheckedChildren:m.createElement(y.Om2,null)})),U&&N&&m.createElement(E.Ex,{status:"success",text:"Updated ".concat(U.toLocaleTimeString()),style:{fontSize:"11px"}}),c&&p&&m.createElement(E.Ex,{count:p.collaboratorCount||0,showZero:!1,style:{backgroundColor:"#52c41a"}},m.createElement(E.m_,{title:"Active collaborators"},m.createElement(E.$n,{size:"small",type:"text",icon:m.createElement(y.OmY,null)}))))),m.createElement(un,{component:t,properties:bn(bn({},G),mt),values:C,onReset:K,showPreview:!0,showCode:!1,showValidation:!0,realTimeEnabled:N}),m.createElement(An,null,m.createElement(E.tU,{activeKey:h,onChange:b,size:"small"},m.createElement(En,{tab:"Properties",key:"basic"},m.createElement(Sn,null,m.createElement(jt,{properties:G,onFilter:Q,placeholder:"Search component properties..."}),m.createElement(Xt,{groupName:"basic",properties:{name:{type:"text",label:"Component Name",required:!0,placeholder:"Enter component name"}},values:C,onChange:_,componentType:t.type,defaultExpanded:!0}),Object.keys(G).length>0&&m.createElement(Xt,{groupName:"component",properties:G,values:C,onChange:_,componentType:t.type,defaultExpanded:!0}))),m.createElement(En,{tab:"Styling",key:"style"},m.createElement(Sn,null,m.createElement(jt,{properties:mt,onFilter:Q,placeholder:"Search style properties..."}),Object.entries(X).map((function(e){var n=(0,s.A)(e,2),l=n[0],a=n[1];return m.createElement(Xt,{key:l,groupName:l,properties:a,values:C,onChange:_,componentType:t.type,defaultExpanded:"dimensions"===l||"colors"===l})})))),m.createElement(En,{tab:"Advanced",key:"advanced"},m.createElement(Sn,null,m.createElement(Xt,{groupName:"advanced",properties:{customProps:{type:"json",label:"Custom Properties",placeholder:"Enter custom properties as JSON",description:"Additional properties not covered by the standard options"},customStyles:{type:"json",label:"Custom Styles",placeholder:"Enter custom styles as JSON",description:"Additional CSS styles not covered by the standard options"}},values:C,onChange:_,componentType:t.type,defaultExpanded:!0}))))),m.createElement(Cn,null,m.createElement(E.$x,{style:{width:"100%",justifyContent:"space-between"}},m.createElement(E.$x,null,T&&m.createElement(xn,{type:"warning",style:{fontSize:"12px"}},"Unsaved changes")),m.createElement(E.$x,null,m.createElement(E.$n,{size:"small",icon:m.createElement(y.Xrf,null),onClick:K,disabled:!T},"Reset"),m.createElement(E.$n,{type:"primary",size:"small",icon:m.createElement(y.ylI,null),onClick:function(){if(t){var e=C.name,l=(0,x.A)(C,gn),a={},r={};Object.entries(l).forEach((function(e){var t=(0,s.A)(e,2),n=t[0],l=t[1];mt[n]?r[n]=l:a[n]=l}));var o=bn(bn({},t),{},{name:e||t.name,props:bn(bn({},t.props),a),style:bn(bn({},t.style),r)});d((0,yn.ZP)(o)),F(!1),n&&n(o)}},disabled:!T},"Apply Changes"))))):m.createElement(wn,null,m.createElement("div",{style:{padding:"24px",textAlign:"center"}},m.createElement(xn,{type:"secondary"},"Select a component to edit its properties")))};var kn,zn,jn,Pn,Tn,Fn,Rn,Bn;function Nn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function Ln(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Nn(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Nn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}try{Bn=n(54043).A}catch(e){console.warn("Enhanced component builder not available, using fallback"),Bn=null}var In=g.styled.div(kn||(kn=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),h.Ay.spacing[4]),Mn=g.styled.div(zn||(zn=(0,u.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: ",";\n"])),h.Ay.spacing[4]),$n=g.styled.div(jn||(jn=(0,u.A)(["\n  padding: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  background-color: white;\n"])),h.Ay.spacing[4],h.Ay.colors.neutral[200],h.Ay.borderRadius.md),Hn=g.styled.div(Pn||(Pn=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),h.Ay.spacing[3]),Dn=g.styled.div(Tn||(Tn=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),h.Ay.spacing[2]),Wn=g.styled.div(Fn||(Fn=(0,u.A)(["\n  cursor: pointer;\n  transition: ",";\n  border: 2px solid ",";\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ",";\n  }\n"])),h.Ay.transitions.default,(function(e){return e.isSelected?h.Ay.colors.primary.main:"transparent"}),h.Ay.shadows.md),Un=g.styled.div(Rn||(Rn=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ",";\n  background-color: ",";\n  border-radius: ",";\n  text-align: center;\n"])),h.Ay.spacing[8],h.Ay.colors.neutral[100],h.Ay.borderRadius.md),Yn=[{value:"container",label:"Container"},{value:"text",label:"Text"},{value:"button",label:"Button"},{value:"input",label:"Input Field"},{value:"image",label:"Image"},{value:"card",label:"Card"},{value:"list",label:"List"},{value:"custom",label:"Custom"}];const Jn=function(){var e=(0,m.useState)(null!==Bn);if((0,s.A)(e,1)[0]&&Bn)return m.createElement(Bn,null);var t=(0,f.wA)(),n=(0,f.d4)((function(e){var t,n;return(null===(t=e.app)||void 0===t?void 0:t.components)||(null===(n=e.appData)||void 0===n?void 0:n.components)||[]})),l=(0,m.useState)(!0),a=(0,s.A)(l,2),r=a[0],o=a[1],i=(0,m.useState)(""),u=(0,s.A)(i,2),p=u[0],b=u[1],x=(0,m.useState)("container"),E=(0,s.A)(x,2),w=E[0],A=E[1],C=(0,m.useState)("{}"),S=(0,s.A)(C,2),O=S[0],k=S[1],z=(0,m.useState)(null),j=(0,s.A)(z,2),P=j[0],T=j[1],F=(0,m.useState)(!1),R=(0,s.A)(F,2),B=R[0],N=R[1],L=(0,m.useState)({}),I=(0,s.A)(L,2),M=I[0],$=I[1];if((0,m.useEffect)((function(){var e=function(){var e=(0,c.A)(d().mark((function e(){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{o(!0),0===n.length&&[{id:"button-1",name:"Primary Button",type:"button",props:{text:"Click Me",variant:"primary",size:"medium",onClick:"handleButtonClick"},createdAt:(new Date).toISOString()},{id:"text-1",name:"Header Text",type:"text",props:{content:"Welcome to App Builder",variant:"h1",color:"#2563EB",align:"center"},createdAt:(new Date).toISOString()},{id:"input-1",name:"Email Input",type:"input",props:{label:"Email Address",placeholder:"Enter your email",type:"email",required:!0,validation:"email"},createdAt:(new Date).toISOString()},{id:"card-1",name:"Feature Card",type:"card",props:{title:"Easy to Use",description:"Build applications with a simple drag-and-drop interface",image:"https://via.placeholder.com/150",elevation:"md"},createdAt:(new Date).toISOString()}].forEach((function(e){t((0,v.addComponent)(e))})),o(!1)}catch(e){console.error("Failed to initialize ComponentBuilder:",e),o(!1)}case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[n.length,t]),(0,m.useEffect)((function(){return console.log("ComponentBuilder mounting..."),function(){console.log("ComponentBuilder unmounting...")}}),[]),(0,m.useEffect)((function(){console.log("Components updated:",n)}),[n]),(0,m.useEffect)((function(){Object.keys(M).length>0&&console.error("ComponentBuilder errors:",M)}),[M]),r)return m.createElement("div",null,"Loading ComponentBuilder...");var H=function(){var e={};p.trim()||(e.name="Component name is required");try{O&&JSON.parse(O)}catch(t){e.props="Invalid JSON format"}return $(e),0===Object.keys(e).length},D=function(e){T(e),b(e.name),A(e.type),k(JSON.stringify(e.props,null,2)),N(!0),$({})};return m.createElement(In,null,m.createElement(g.Card,null,m.createElement(g.Card.Header,null,m.createElement(g.Card.Title,null,B?"Edit Component":"Create Component"),B&&m.createElement(g.Button,{variant:"text",size:"small",onClick:function(){b(""),A("container"),k("{}"),T(null),N(!1),$({})},startIcon:m.createElement(y.r$3,null)},"Cancel")),m.createElement(g.Card.Content,null,m.createElement(Hn,null,m.createElement(Dn,null,m.createElement(g.Input,{label:"Component Name",value:p,onChange:function(e){return b(e.target.value)},placeholder:"Enter component name",fullWidth:!0,error:!!M.name,helperText:M.name})),m.createElement(Dn,null,m.createElement(g.Select,{label:"Component Type",value:w,onChange:function(e){return A(e.target.value)},options:Yn,fullWidth:!0})),m.createElement(Dn,null,m.createElement(g.Input,{label:"Component Props (JSON)",value:O,onChange:function(e){return k(e.target.value)},placeholder:'{"text": "Hello", "color": "blue"}',fullWidth:!0,error:!!M.props,helperText:M.props,as:"textarea",rows:5,style:{fontFamily:h.Ay.typography.fontFamily.code}})))),m.createElement(g.Card.Footer,null,B?m.createElement(g.Button,{variant:"primary",onClick:function(){if(P&&H())try{var e=O?JSON.parse(O):{},n=Ln(Ln({},P),{},{name:p.trim(),type:w,props:e,updatedAt:(new Date).toISOString()});t((0,v.updateComponent)(n)),b(""),A("container"),k("{}"),T(null),N(!1),$({})}catch(e){$(Ln(Ln({},M),{},{props:e.message}))}},startIcon:m.createElement(y.ylI,null)},"Update Component"):m.createElement(g.Button,{variant:"primary",onClick:function(){if(H())try{var e=O?JSON.parse(O):{},n={id:Date.now().toString(),name:p.trim(),type:w,props:e,createdAt:(new Date).toISOString()};t((0,v.addComponent)(n)).then((function(){b(""),A("container"),k("{}"),$({})})).catch((function(e){console.error("Failed to add component:",e),$({submit:"Failed to add component"})}))}catch(e){$(Ln(Ln({},M),{},{props:e.message}))}},startIcon:m.createElement(y.bW0,null)},"Add Component"))),m.createElement(g.Card,null,m.createElement(g.Card.Header,null,m.createElement(g.Card.Title,null,"Component Library")),m.createElement(g.Card.Content,null,0===n.length?m.createElement(Un,null,m.createElement("div",{style:{fontSize:"48px",color:h.Ay.colors.neutral[400],marginBottom:h.Ay.spacing[4]}},m.createElement(y.rS9,null)),m.createElement("h3",null,"No Components Yet"),m.createElement("p",null,"Create your first component to get started")):m.createElement(Mn,null,n.map((function(e){return m.createElement(Wn,{key:e.id,isSelected:P&&P.id===e.id},m.createElement(g.Card,{elevation:"sm"},m.createElement(g.Card.Header,null,m.createElement("div",null,m.createElement("div",{style:{fontWeight:h.Ay.typography.fontWeight.semibold}},e.name),m.createElement("div",{style:{fontSize:h.Ay.typography.fontSize.sm,color:h.Ay.colors.neutral[500]}},e.type)),m.createElement("div",{style:{display:"flex",gap:h.Ay.spacing[1]}},m.createElement(g.Button,{variant:"text",size:"small",onClick:function(n){n.stopPropagation(),function(e){var n=Ln(Ln({},e),{},{id:Date.now().toString(),name:"".concat(e.name," (Copy)"),createdAt:(new Date).toISOString()});t((0,v.addComponent)(n))}(e)}},m.createElement(y.wq3,null)),m.createElement(g.Button,{variant:"text",size:"small",onClick:function(t){t.stopPropagation(),D(e)}},m.createElement(y.xjh,null)),m.createElement(g.Button,{variant:"text",size:"small",onClick:function(n){var l;n.stopPropagation(),l=e.id,t((0,v.removeComponent)(l)),P&&P.id===l&&(b(""),A("container"),k("{}"),T(null),N(!1))}},m.createElement(y.SUY,null)))),m.createElement(g.Card.Content,{onClick:function(){return D(e)}},m.createElement($n,null,m.createElement("pre",{style:{margin:0,overflow:"auto",maxHeight:"100px"}},JSON.stringify(e.props,null,2))))))}))))),P&&m.createElement(g.Card,null,m.createElement(g.Card.Header,null,m.createElement(g.Card.Title,null,"Component Properties")),m.createElement(g.Card.Content,null,m.createElement(On,{component:P,onUpdate:function(e){t((0,v.updateComponent)(e))}}))))}}}]);