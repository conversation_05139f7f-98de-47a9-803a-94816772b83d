/* App Builder MVP Styles */
.app-builder-mvp {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.app-builder-mvp .ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.app-builder-mvp .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.app-builder-mvp .ant-card-head-title {
  color: white;
}

.app-builder-mvp .ant-card-extra .ant-btn {
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.app-builder-mvp .ant-card-extra .ant-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-connected {
  color: #52c41a;
  font-weight: 500;
}

.status-disconnected {
  color: #f5222d;
  font-weight: 500;
}

/* Enhanced component and layout sections */
.component-select,
.layout-select {
  width: 100%;
  height: 32px;
  padding: 4px 11px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.component-select:hover,
.layout-select:hover {
  border-color: #40a9ff;
}

.component-select:focus,
.layout-select:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}

.component-list,
.layout-list {
  margin-top: 10px;
  margin-bottom: 20px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  background-color: #fafafa;
}

.component-item,
.layout-item {
  padding: 12px;
  margin-bottom: 8px;
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.component-item:hover,
.layout-item:hover {
  border-color: #40a9ff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.component-item:last-child,
.layout-item:last-child {
  margin-bottom: 0;
}

/* Tab customizations */
.app-builder-mvp .ant-tabs-tab {
  font-weight: 500;
}

.app-builder-mvp .ant-tabs-tab-active {
  color: #1890ff;
}

/* List item customizations */
.app-builder-mvp .ant-list-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px;
  transition: background-color 0.3s ease;
}

.app-builder-mvp .ant-list-item:hover {
  background-color: #f9f9f9;
}

.app-builder-mvp .ant-list-item-meta-title {
  margin-bottom: 4px;
}

.app-builder-mvp .ant-list-item-action {
  margin-left: 16px;
}

/* Preview section styles */
.app-preview-container {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  min-height: 300px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;
  overflow: hidden;
}

.app-preview-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: repeating-linear-gradient(45deg,
      transparent,
      transparent 10px,
      rgba(255, 255, 255, 0.1) 10px,
      rgba(255, 255, 255, 0.1) 20px);
  animation: shimmer 20s linear infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }

  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Button enhancements */
.app-builder-mvp .ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.app-builder-mvp .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.app-builder-mvp .ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.app-builder-mvp .ant-btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Card enhancements */
.app-builder-mvp .ant-card-small .ant-card-head {
  min-height: 48px;
  padding: 0 16px;
}

.app-builder-mvp .ant-card-small .ant-card-body {
  padding: 16px;
}

/* Drawer customizations */
.app-builder-mvp .ant-drawer-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.app-builder-mvp .ant-drawer-title {
  color: white;
}

.app-builder-mvp .ant-drawer-close {
  color: white;
}

/* Modal customizations */
.app-builder-mvp .ant-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
}

.app-builder-mvp .ant-modal-title {
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .app-builder-mvp {
    padding: 10px;
  }

  .app-builder-mvp .ant-card-head-title {
    font-size: 16px;
  }

  .app-builder-mvp .ant-tabs-tab {
    padding: 8px 12px;
    font-size: 14px;
  }
}

/* Loading and animation effects */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Success/Error states */
.success-state {
  border-color: #52c41a;
  background-color: #f6ffed;
}

.error-state {
  border-color: #ff4d4f;
  background-color: #fff2f0;
}
