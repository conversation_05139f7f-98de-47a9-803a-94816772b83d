"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[667],{1667:(e,t,n)=>{n.r(t),n.d(t,{default:()=>G});var r,a,o,l,i,c,s,d,u,m,p=n(4467),y=n(5544),g=n(7528),f=n(6540),E=n(1468),h=n(9740),v=n(5039),b=n(7852),F=n(3903),A=n(9237),C=n(8602),x=n(778),k=n(234),w=n(7046),T=n(261),B=n(3598),S=n(6067),D=n(1616),I=n(4318),P=n(6191),O=n(6020);function W(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function R(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?W(Object(n),!0).forEach((function(t){(0,p.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):W(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var N=P.styled.div(r||(r=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),O.Ay.spacing[4]),z=P.styled.div(a||(a=(0,g.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: ",";\n"])),O.Ay.spacing[4]),j=P.styled.div(o||(o=(0,g.A)(["\n  display: flex;\n  align-items: center;\n  gap: ",";\n\n  .color-preview {\n    width: 36px;\n    height: 36px;\n    border-radius: ",";\n    border: 1px solid ",';\n    overflow: hidden;\n    position: relative;\n\n    input[type="color"] {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      border: none;\n      padding: 0;\n      margin: 0;\n      cursor: pointer;\n    }\n  }\n\n  .color-input {\n    flex: 1;\n  }\n'])),O.Ay.spacing[2],O.Ay.borderRadius.md,O.Ay.colors.neutral[300]),H=P.styled.div(l||(l=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n\n  .font-preview {\n    padding: ",";\n    border: 1px solid ",";\n    border-radius: ",";\n    min-height: 60px;\n  }\n"])),O.Ay.spacing[2],O.Ay.spacing[2],O.Ay.colors.neutral[300],O.Ay.borderRadius.md),M=P.styled.div(i||(i=(0,g.A)(["\n  padding: ",";\n  border-radius: ",";\n  background-color: ",";\n  color: ",";\n  font-family: ",";\n  transition: all 0.3s ease;\n\n  h3 {\n    margin-top: 0;\n    margin-bottom: ",";\n    color: ",";\n  }\n\n  p {\n    margin-bottom: ",";\n  }\n\n  .buttons {\n    display: flex;\n    gap: ",";\n  }\n\n  .primary-button {\n    padding: "," ",";\n    background-color: ",";\n    color: white;\n    border: none;\n    border-radius: ",";\n    cursor: pointer;\n    transition: all 0.2s ease;\n\n    &:hover {\n      opacity: 0.9;\n      transform: translateY(-2px);\n    }\n  }\n\n  .secondary-button {\n    padding: "," ",";\n    background-color: ",";\n    color: white;\n    border: none;\n    border-radius: ",";\n    cursor: pointer;\n    transition: all 0.2s ease;\n\n    &:hover {\n      opacity: 0.9;\n      transform: translateY(-2px);\n    }\n  }\n\n  .card-example {\n    margin-top: ",";\n    padding: ",";\n    border-radius: ",";\n    background-color: ",";\n    border: 1px solid ",";\n  }\n\n  .input-example {\n    margin-top: ",";\n    padding: ",";\n    border-radius: ",";\n    border: 1px solid ",";\n    background-color: ",";\n    color: ",";\n    width: 100%;\n    font-family: ",";\n  }\n"])),O.Ay.spacing[4],O.Ay.borderRadius.md,(function(e){return e.backgroundColor||"white"}),(function(e){return e.textColor||"black"}),(function(e){return e.fontFamily||"inherit"}),O.Ay.spacing[3],(function(e){return e.textColor||"black"}),O.Ay.spacing[3],O.Ay.spacing[2],O.Ay.spacing[2],O.Ay.spacing[3],(function(e){return e.primaryColor||O.Ay.colors.primary.main}),O.Ay.borderRadius.md,O.Ay.spacing[2],O.Ay.spacing[3],(function(e){return e.secondaryColor||O.Ay.colors.secondary.main}),O.Ay.borderRadius.md,O.Ay.spacing[3],O.Ay.spacing[3],O.Ay.borderRadius.md,(function(e){return"#FFFFFF"===e.backgroundColor?"#F9FAFB":"rgba(255, 255, 255, 0.1)"}),(function(e){return"#FFFFFF"===e.backgroundColor?"#E5E7EB":"rgba(255, 255, 255, 0.2)"}),O.Ay.spacing[3],O.Ay.spacing[2],O.Ay.borderRadius.sm,(function(e){return"#FFFFFF"===e.backgroundColor?"#D1D5DB":"rgba(255, 255, 255, 0.2)"}),(function(e){return"#FFFFFF"===e.backgroundColor?"white":"rgba(255, 255, 255, 0.05)"}),(function(e){return e.textColor}),(function(e){return e.fontFamily})),_=(P.styled.div(c||(c=(0,g.A)(["\n  display: flex;\n  gap: ",";\n  margin-top: ",";\n\n  .color-swatch {\n    width: 24px;\n    height: 24px;\n    border-radius: 50%;\n    border: 1px solid ",";\n    cursor: pointer;\n  }\n"])),O.Ay.spacing[2],O.Ay.spacing[2],O.Ay.colors.neutral[300]),P.styled.div(s||(s=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),O.Ay.spacing[3])),U=P.styled.div(d||(d=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  gap: ",";\n"])),O.Ay.spacing[2]),L=P.styled.div(u||(u=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ",";\n  background-color: ",";\n  border-radius: ",";\n  text-align: center;\n"])),O.Ay.spacing[8],O.Ay.colors.neutral[100],O.Ay.borderRadius.md),Y=(0,P.styled)(P.Card)(m||(m=(0,g.A)(["\n  border: ",";\n  transition: ",";\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ",";\n  }\n"])),(function(e){return e.isActive?"2px solid ".concat(O.Ay.colors.primary.main):"none"}),O.Ay.transitions.default,O.Ay.shadows.md),J=["Inter, sans-serif","Arial, sans-serif","Helvetica, sans-serif","Georgia, serif","Times New Roman, serif","Courier New, monospace","Verdana, sans-serif","Roboto, sans-serif","Open Sans, sans-serif","Lato, sans-serif"],q=[{name:"Blue",primary:"#2563EB",secondary:"#10B981",background:"#FFFFFF",text:"#111827"},{name:"Purple",primary:"#8B5CF6",secondary:"#EC4899",background:"#FFFFFF",text:"#111827"},{name:"Green",primary:"#10B981",secondary:"#3B82F6",background:"#FFFFFF",text:"#111827"},{name:"Red",primary:"#EF4444",secondary:"#F59E0B",background:"#FFFFFF",text:"#111827"},{name:"Dark",primary:"#3B82F6",secondary:"#10B981",background:"#111827",text:"#F9FAFB"},{name:"Monochrome",primary:"#000000",secondary:"#666666",background:"#FFFFFF",text:"#333333"},{name:"Sunset",primary:"#FF5733",secondary:"#FFC300",background:"#FFFAF0",text:"#333333"},{name:"Ocean",primary:"#1A5276",secondary:"#2E86C1",background:"#EBF5FB",text:"#17202A"},{name:"Forest",primary:"#1E8449",secondary:"#F1C40F",background:"#F4F6F6",text:"#145A32"},{name:"Night Mode",primary:"#BB86FC",secondary:"#03DAC5",background:"#121212",text:"#E1E1E1"}];const G=function(){var e,t,n=(0,E.wA)(),r=(0,E.d4)((function(e){return e&&e.themes?Array.isArray(e.themes.themes)?e.themes.themes:[]:(console.warn("Redux state or themes slice not found, using fallback values"),[])})),a=(0,E.d4)((function(e){return e&&e.themes&&e.themes.activeTheme||"default"})),o=(0,E.d4)((function(e){return e&&e.themes&&e.themes.userPreferences?e.themes.userPreferences:{savedTheme:null,autoApplyTheme:!0}})),l=(0,f.useState)(""),i=(0,y.A)(l,2),c=i[0],s=i[1],d=(0,f.useState)("#2563EB"),u=(0,y.A)(d,2),m=u[0],p=u[1],g=(0,f.useState)("#10B981"),W=(0,y.A)(g,2),G=W[0],V=W[1],Q=(0,f.useState)("#FFFFFF"),K=(0,y.A)(Q,2),X=K[0],Z=K[1],$=(0,f.useState)("#111827"),ee=(0,y.A)($,2),te=ee[0],ne=ee[1],re=(0,f.useState)("Inter, sans-serif"),ae=(0,y.A)(re,2),oe=ae[0],le=ae[1],ie=(0,f.useState)(null),ce=(0,y.A)(ie,2),se=ce[0],de=ce[1],ue=(0,f.useState)(!1),me=(0,y.A)(ue,2),pe=me[0],ye=me[1],ge=(0,f.useState)({}),fe=(0,y.A)(ge,2),Ee=fe[0],he=fe[1];(0,f.useEffect)((function(){if(!Array.isArray(r)||0===r.length)try{n((0,D.zp)({id:"default",name:"Default Theme",primaryColor:"#2563EB",secondaryColor:"#10B981",backgroundColor:"#FFFFFF",textColor:"#111827",fontFamily:"Inter, sans-serif"}))}catch(e){console.error("Error dispatching addTheme action:",e)}if(!a)try{n((0,D.Ic)("default"))}catch(e){console.error("Error dispatching setActiveTheme action:",e)}}),[n]),(0,f.useEffect)((function(){var e=function(e){e.data&&"THEME_CACHE_UPDATED"===e.data.type&&console.log("Theme cache updated at:",e.data.timestamp)};return"serviceWorker"in navigator&&navigator.serviceWorker.addEventListener("message",e),function(){"serviceWorker"in navigator&&navigator.serviceWorker.removeEventListener("message",e)}}),[]);var ve=function(){var e={};return c.trim()||(e.name="Theme name is required"),he(e),0===Object.keys(e).length},be=function(e){de(e),s(e.name),p(e.primaryColor),V(e.secondaryColor),Z(e.backgroundColor),ne(e.textColor),le(e.fontFamily),ye(!0),he({})};return f.createElement(N,null,f.createElement(P.Card,null,f.createElement(P.Card.Header,null,f.createElement(P.Card.Title,null,pe?"Edit Theme":"Create Theme"),pe&&f.createElement(P.Button,{variant:"text",size:"small",onClick:function(){s(""),p("#2563EB"),V("#10B981"),Z("#FFFFFF"),ne("#111827"),le("Inter, sans-serif"),de(null),ye(!1),he({})},startIcon:f.createElement(b.A,null)},"Cancel")),f.createElement(P.Card.Content,null,f.createElement(_,null,f.createElement(U,null,f.createElement(P.Input,{label:"Theme Name",value:c,onChange:function(e){return s(e.target.value)},placeholder:"Enter theme name",fullWidth:!0,error:!!Ee.name,helperText:Ee.name})),f.createElement("div",{style:{marginBottom:O.Ay.spacing[2]}},f.createElement("div",{style:{fontWeight:O.Ay.typography.fontWeight.medium,marginBottom:O.Ay.spacing[2]}},"Color Palettes"),f.createElement("div",{style:{display:"flex",flexWrap:"wrap",gap:O.Ay.spacing[2]}},q.map((function(e,t){return f.createElement(P.Button,{key:t,variant:"outline",size:"small",onClick:function(){return function(e){p(e.primary),V(e.secondary),Z(e.background),ne(e.text)}(e)}},e.name)})))),f.createElement(U,null,f.createElement("label",null,"Primary Color"),f.createElement(j,null,f.createElement("div",{className:"color-preview"},f.createElement("input",{type:"color",value:m,onChange:function(e){return p(e.target.value)}})),f.createElement(P.Input,{className:"color-input",value:m,onChange:function(e){return p(e.target.value)},fullWidth:!0}))),f.createElement(U,null,f.createElement("label",null,"Secondary Color"),f.createElement(j,null,f.createElement("div",{className:"color-preview"},f.createElement("input",{type:"color",value:G,onChange:function(e){return V(e.target.value)}})),f.createElement(P.Input,{className:"color-input",value:G,onChange:function(e){return V(e.target.value)},fullWidth:!0}))),f.createElement(U,null,f.createElement("label",null,"Background Color"),f.createElement(j,null,f.createElement("div",{className:"color-preview"},f.createElement("input",{type:"color",value:X,onChange:function(e){return Z(e.target.value)}})),f.createElement(P.Input,{className:"color-input",value:X,onChange:function(e){return Z(e.target.value)},fullWidth:!0}))),f.createElement(U,null,f.createElement("label",null,"Text Color"),f.createElement(j,null,f.createElement("div",{className:"color-preview"},f.createElement("input",{type:"color",value:te,onChange:function(e){return ne(e.target.value)}})),f.createElement(P.Input,{className:"color-input",value:te,onChange:function(e){return ne(e.target.value)},fullWidth:!0}))),f.createElement(U,null,f.createElement("label",null,"Font Family"),f.createElement("select",{value:oe,onChange:function(e){return le(e.target.value)},style:{width:"100%",padding:O.Ay.spacing[2],borderRadius:O.Ay.borderRadius.md,border:"1px solid ".concat(O.Ay.colors.neutral[300])}},J.map((function(e){return f.createElement("option",{key:e,value:e},e)}))),f.createElement(H,null,f.createElement("div",{className:"font-preview",style:{fontFamily:oe}},"The quick brown fox jumps over the lazy dog."))))),f.createElement(P.Card.Footer,null,pe?f.createElement(P.Button,{variant:"primary",onClick:function(){if(se&&ve()){var e=R(R({},se),{},{name:c.trim(),primaryColor:m,secondaryColor:G,backgroundColor:X,textColor:te,fontFamily:oe,updatedAt:(new Date).toISOString()});n((0,D.V_)(e)),"serviceWorker"in navigator&&navigator.serviceWorker.controller&&(navigator.serviceWorker.controller.postMessage({type:"THEME_UPDATED",theme:e}),navigator.serviceWorker.controller.postMessage({type:"UPDATE_THEME_CACHE"})),s(""),p("#2563EB"),V("#10B981"),Z("#FFFFFF"),ne("#111827"),le("Inter, sans-serif"),de(null),ye(!1),he({})}},startIcon:f.createElement(F.A,null),disabled:"default"===(null==se?void 0:se.id)},"Update Theme"):f.createElement(P.Button,{variant:"primary",onClick:function(){if(ve()){var e={id:Date.now().toString(),name:c.trim(),primaryColor:m,secondaryColor:G,backgroundColor:X,textColor:te,fontFamily:oe,createdAt:(new Date).toISOString()};n((0,D.zp)(e)),"serviceWorker"in navigator&&navigator.serviceWorker.controller&&(navigator.serviceWorker.controller.postMessage({type:"THEME_UPDATED",theme:e}),navigator.serviceWorker.controller.postMessage({type:"UPDATE_THEME_CACHE"})),s(""),p("#2563EB"),V("#10B981"),Z("#FFFFFF"),ne("#111827"),le("Inter, sans-serif"),he({})}},startIcon:f.createElement(A.A,null)},"Add Theme"))),f.createElement(P.Card,null,f.createElement(P.Card.Header,null,f.createElement(P.Card.Title,null,"Theme Preview")),f.createElement(P.Card.Content,null,f.createElement(M,{primaryColor:m,secondaryColor:G,backgroundColor:X,textColor:te,fontFamily:oe},f.createElement("h3",null,"Theme Preview"),f.createElement("p",null,"This is a preview of how your theme will look. The text color, background color, and font family are applied to this preview."),f.createElement("div",{className:"buttons"},f.createElement("button",{className:"primary-button"},"Primary Button"),f.createElement("button",{className:"secondary-button"},"Secondary Button")),f.createElement("div",{className:"card-example"},f.createElement("h4",{style:{margin:"0 0 8px 0",color:te}},"Card Example"),f.createElement("p",{style:{margin:"0 0 8px 0",fontSize:"14px"}},"This shows how cards will appear with your theme.")),f.createElement("label",{style:{display:"block",marginTop:"16px",marginBottom:"8px"}},"Input Example:"),f.createElement("input",{type:"text",className:"input-example",placeholder:"Enter text here..."}),f.createElement("div",{style:{marginTop:"16px",display:"flex",justifyContent:"space-between"}},f.createElement("div",{style:{width:"24px",height:"24px",borderRadius:"50%",backgroundColor:m,border:"1px solid rgba(0,0,0,0.1)"}}),f.createElement("div",{style:{width:"24px",height:"24px",borderRadius:"50%",backgroundColor:G,border:"1px solid rgba(0,0,0,0.1)"}}),f.createElement("div",{style:{width:"24px",height:"24px",borderRadius:"50%",backgroundColor:X,border:"1px solid rgba(0,0,0,0.1)"}}),f.createElement("div",{style:{width:"24px",height:"24px",borderRadius:"50%",backgroundColor:te,border:"1px solid rgba(0,0,0,0.1)"}}))))),f.createElement(P.Card,null,f.createElement(P.Card.Header,null,f.createElement(P.Card.Title,null,"Theme Preferences"),f.createElement(C.A,{style:{fontSize:"18px",color:O.Ay.colors.neutral[500]}})),f.createElement(P.Card.Content,null,f.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:O.Ay.spacing[2]}},f.createElement("div",null,f.createElement("h4",{style:{margin:0,marginBottom:O.Ay.spacing[1]}},"Auto-apply Theme"),f.createElement("p",{style:{margin:0,color:O.Ay.colors.neutral[500],fontSize:O.Ay.typography.fontSize.sm}},"Automatically save your theme selection as a preference")),f.createElement(v.A,{checked:o.autoApplyTheme,onChange:function(){n({type:I._E}),n((function(e,t){try{var n=t().themes.userPreferences;localStorage.setItem("themePreferences",JSON.stringify(n))}catch(e){console.error("Error saving theme preferences:",e)}}));var e=!o.autoApplyTheme;h.Ay.success("Auto-apply theme ".concat(e?"enabled":"disabled"))},style:{backgroundColor:o.autoApplyTheme?O.Ay.colors.primary.main:void 0}})),f.createElement("div",{style:{marginTop:O.Ay.spacing[3],padding:O.Ay.spacing[3],backgroundColor:O.Ay.colors.neutral[100],borderRadius:O.Ay.borderRadius.md}},f.createElement("h4",{style:{margin:0,marginBottom:O.Ay.spacing[2]}},"Current Preferences"),f.createElement("div",{style:{display:"flex",gap:O.Ay.spacing[2],alignItems:"center"}},f.createElement("div",{style:{width:"20px",height:"20px",borderRadius:"50%",backgroundColor:o.savedTheme&&(null===(e=r.find((function(e){return e.id===o.savedTheme})))||void 0===e?void 0:e.primaryColor)||"#2563EB",border:"1px solid #e5e7eb"}}),f.createElement("span",null,o.savedTheme?(null===(t=r.find((function(e){return e.id===o.savedTheme})))||void 0===t?void 0:t.name)||"Default Theme":"No saved preference"))))),f.createElement(P.Card,null,f.createElement(P.Card.Header,null,f.createElement(P.Card.Title,null,"Available Themes"),f.createElement("div",{style:{display:"flex",gap:O.Ay.spacing[2]}},f.createElement("input",{type:"file",id:"theme-import",accept:".json",style:{display:"none"},onChange:function(e){var t=e.target.files[0];if(t){var r=new FileReader;r.onload=function(t){try{var r=JSON.parse(t.target.result);if(!(r.name&&r.primaryColor&&r.secondaryColor&&r.backgroundColor&&r.textColor&&r.fontFamily))throw new Error("Invalid theme format");var a=R(R({},r),{},{id:Date.now().toString(),name:"".concat(r.name," (Imported)"),createdAt:(new Date).toISOString()});n((0,D.zp)(a)),"serviceWorker"in navigator&&navigator.serviceWorker.controller&&(navigator.serviceWorker.controller.postMessage({type:"THEME_UPDATED",theme:a}),navigator.serviceWorker.controller.postMessage({type:"UPDATE_THEME_CACHE"})),e.target.value=""}catch(t){console.error("Error importing theme:",t),e.target.value=""}},r.readAsText(t)}}}),f.createElement(P.Button,{variant:"outline",size:"small",onClick:function(){return document.getElementById("theme-import").click()}},"Import Theme"))),f.createElement(P.Card.Content,null,0===r.length?f.createElement(L,null,f.createElement("div",{style:{fontSize:"48px",color:O.Ay.colors.neutral[400],marginBottom:O.Ay.spacing[4]}},f.createElement(x.A,null)),f.createElement("h3",null,"No Themes Yet"),f.createElement("p",null,"Create your first theme to get started")):f.createElement(z,null,(Array.isArray(r)?r:[]).map((function(e){return f.createElement(Y,{key:e.id,elevation:"sm",isActive:a===e.id},f.createElement(P.Card.Header,null,f.createElement("div",null,f.createElement("div",{style:{fontWeight:e.typography.fontWeight.semibold}},e.name),f.createElement("div",{style:{fontSize:e.typography.fontSize.sm,color:e.colors.neutral[500]}},e.fontFamily.split(",")[0])),f.createElement("div",{style:{display:"flex",gap:e.spacing[1]}},"default"!==e.id&&f.createElement(f.Fragment,null,f.createElement(P.Button,{variant:"text",size:"small",onClick:function(){return function(e){var t=e||se;if(t){var n=JSON.stringify(t,null,2),r=new Blob([n],{type:"application/json"}),a=URL.createObjectURL(r),o=document.createElement("a");o.href=a,o.download="".concat(t.name.replace(/\s+/g,"-").toLowerCase(),"-theme.json"),document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(a)}}(e)},title:"Export Theme"},f.createElement(k.A,null)),f.createElement(P.Button,{variant:"text",size:"small",onClick:function(){return function(e){var t=R(R({},e),{},{id:Date.now().toString(),name:"".concat(e.name," (Copy)"),createdAt:(new Date).toISOString()});n((0,D.zp)(t))}(e)},title:"Duplicate Theme"},f.createElement(w.A,null)),f.createElement(P.Button,{variant:"text",size:"small",onClick:function(){return be(e)},title:"Edit Theme"},f.createElement(T.A,null)),f.createElement(P.Button,{variant:"text",size:"small",onClick:function(){var t;"default"!==(t=e.id)&&(n((0,D.Qo)(t)),se&&se.id===t&&(s(""),p("#2563EB"),V("#10B981"),Z("#FFFFFF"),ne("#111827"),le("Inter, sans-serif"),de(null),ye(!1)),a===t&&n((0,D.Ic)("default")))},title:"Delete Theme"},f.createElement(B.A,null))))),f.createElement(P.Card.Content,{onClick:function(){return be(e)}},f.createElement(M,{primaryColor:e.primaryColor,secondaryColor:e.secondaryColor,backgroundColor:e.backgroundColor,textColor:e.textColor,fontFamily:e.fontFamily,style:{height:"120px",overflow:"hidden"}},f.createElement("h3",{style:{fontSize:"16px"}},"Preview"),f.createElement("p",{style:{fontSize:"14px"}},"Sample text with this theme applied."),f.createElement("div",{className:"buttons"},f.createElement("button",{className:"primary-button",style:{padding:"4px 8px",fontSize:"12px"}},"Button"),f.createElement("button",{className:"secondary-button",style:{padding:"4px 8px",fontSize:"12px"}},"Button")))),f.createElement(P.Card.Footer,null,f.createElement("div",{style:{display:"flex",gap:e.spacing[2]}},f.createElement("div",{style:{width:"20px",height:"20px",backgroundColor:e.primaryColor,borderRadius:"50%",border:"1px solid #e5e7eb"}}),f.createElement("div",{style:{width:"20px",height:"20px",backgroundColor:e.secondaryColor,borderRadius:"50%",border:"1px solid #e5e7eb"}})),a===e.id?f.createElement(P.Button,{variant:"text",size:"small",startIcon:f.createElement(S.A,null),style:{color:e.colors.success.main}},"Active"):f.createElement(P.Button,{variant:"outline",size:"small",onClick:function(){return function(e){try{n((0,D.Ic)(e)),h.Ay.success("Theme activated successfully");var t=(Array.isArray(r)?r:[]).concat([{id:"default",name:"Default Theme",primaryColor:"#2563EB",secondaryColor:"#10B981",backgroundColor:"#FFFFFF",textColor:"#111827",fontFamily:"Inter, sans-serif"}]).find((function(t){return t.id===e}));"serviceWorker"in navigator&&navigator.serviceWorker.controller&&t&&navigator.serviceWorker.controller.postMessage({type:"THEME_UPDATED",theme:t})}catch(e){console.error("Error setting active theme:",e),h.Ay.error("Failed to activate theme. Please try again.")}}(e.id)}},"Activate")))}))))))}}}]);