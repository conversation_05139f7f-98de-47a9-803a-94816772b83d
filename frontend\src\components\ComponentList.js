import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { addComponent, removeComponent } from '../redux/minimal-store';

const ComponentList = () => {
  const [name, setName] = useState('');
  const components = useSelector(state => state.app?.components || []);
  const dispatch = useDispatch();

  const handleAddComponent = () => {
    if (name.trim()) {
      dispatch(addComponent({
        id: Date.now().toString(),
        name: name.trim(),
        type: 'custom'
      }));
      setName('');
    }
  };

  const handleRemoveComponent = (id) => {
    dispatch(removeComponent(id));
  };

  return (
    <div style={{ padding: '1rem' }}>
      <h2>Components</h2>
      <div style={{ marginBottom: '1rem' }}>
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Component name"
          style={{ padding: '0.5rem', marginRight: '0.5rem' }}
        />
        <button onClick={handleAddComponent}>Add Component</button>
      </div>
      {components.length === 0 ? (
        <p>No components yet. Add one to get started.</p>
      ) : (
        <ul>
          {components.map(component => (
            <li key={component.id} style={{ marginBottom: '0.5rem' }}>
              {component.name} ({component.type})
              <button
                onClick={() => handleRemoveComponent(component.id)}
                style={{ marginLeft: '0.5rem', padding: '0.25rem 0.5rem', backgroundColor: '#EF4444' }}
              >
                Remove
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default ComponentList;
