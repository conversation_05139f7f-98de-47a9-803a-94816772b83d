"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[4043],{54043:(e,n,t)=>{t.d(n,{A:()=>Rn});var o=t(64467),r=t(5544),i=t(57528),a=t(96540),l=t(1807),c=t(35346),s=t(71468),d=t(70572),u=t(60436),p=t(10467),m=t(54756),f=t.n(m),g=t(87169),b=t(5556),y=t.n(b),x=l.o5.Text,v=l.o5.Paragraph,h=function(e){var n,t,o,i=e.suggestion,s=e.onApply,d=e.onPreview,m=e.applied,g=void 0!==m&&m,b=e.showPreview,y=void 0===b||b,h=e.showScore,E=void 0===h||h,w=e.compact,k=void 0!==w&&w,C=(0,a.useState)(!1),A=(0,r.A)(C,2),S=A[0],z=A[1],D=(0,a.useState)(!1),O=(0,r.A)(D,2),P=O[0],T=O[1],I=function(){var e=(0,p.A)(f().mark((function e(){return f().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!g&&!P){e.next=2;break}return e.abrupt("return");case 2:if(T(!0),e.prev=3,!s){e.next=7;break}return e.next=7,s(i);case 7:e.next=12;break;case 9:e.prev=9,e.t0=e.catch(3),console.error("Error applying component combination:",e.t0);case 12:return e.prev=12,T(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[3,9,12,15]])})));return function(){return e.apply(this,arguments)}}(),j=function(){d?d(i):z(!0)},M=function(e){return e>=80?"#52c41a":e>=60?"#1890ff":e>=40?"#faad14":"#ff4d4f"},R=function(e){return{button:"🔘",form:"📝",input:"📝",text:"📄",image:"🖼️",card:"🃏",header:"📋",nav:"🧭",list:"📋",divider:"➖",section:"📦",modal:"🪟",table:"📊",chart:"📈"}[e]||"🔧"},U=function(){var e=i.components,n=void 0===e?[]:e,t=i.missing_components,o=void 0===t?[]:t,r=(0,u.A)(n);return a.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",padding:"16px",background:"#fafafa",borderRadius:"6px",border:"1px solid #d9d9d9",minHeight:"80px"}},a.createElement(l.$x,{size:"small",wrap:!0},r.map((function(e,n){return a.createElement("div",{key:n,style:{textAlign:"center"}},a.createElement(l.eu,{size:"small",style:{backgroundColor:o.includes(e)?"#ff4d4f":"#1890ff",color:"white",fontSize:"12px"}},R(e)),a.createElement("div",{style:{fontSize:"10px",marginTop:"2px",color:o.includes(e)?"#ff4d4f":"#666"}},e))})),o.length>0&&a.createElement(a.Fragment,null,a.createElement(c.bW0,{style:{color:"#999",margin:"0 4px"}}),o.map((function(e,n){return a.createElement("div",{key:"missing-".concat(n),style:{textAlign:"center"}},a.createElement(l.eu,{size:"small",style:{backgroundColor:"#52c41a",color:"white",fontSize:"12px",border:"2px dashed #52c41a"}},R(e)),a.createElement("div",{style:{fontSize:"10px",marginTop:"2px",color:"#52c41a"}},"+",e))})))))},$=function(){return a.createElement(l.aF,{title:a.createElement(l.$x,null,a.createElement(c.rS9,null),i.name,E&&a.createElement(l.Ex,{count:i.score,style:{backgroundColor:M(i.score)}})),open:S,onCancel:function(){return z(!1)},footer:[a.createElement(l.$n,{key:"cancel",onClick:function(){return z(!1)}},"Close"),a.createElement(l.$n,{key:"apply",type:"primary",onClick:function(){z(!1),I()},disabled:g,loading:P},g?"Applied":"Add Components")],width:600},a.createElement(l.$x,{direction:"vertical",style:{width:"100%"}},a.createElement("div",{style:{marginBottom:"16px"}},U()),a.createElement(v,null,i.description),a.createElement("div",null,a.createElement(x,{strong:!0},"Why this combination?"),a.createElement(v,{type:"secondary"},i.explanation)),i.missing_components&&i.missing_components.length>0&&a.createElement("div",null,a.createElement(x,{strong:!0},"Components to add:"),a.createElement("div",{style:{marginTop:"8px"}},i.missing_components.map((function(e,n){return a.createElement(l.vw,{key:n,color:"green",style:{marginBottom:"4px"}},a.createElement(c.bW0,{style:{marginRight:"4px"}}),e)})))),i.use_cases&&a.createElement("div",null,a.createElement(x,{strong:!0},"Best for:"),a.createElement("div",{style:{marginTop:"8px"}},i.use_cases.map((function(e,n){return a.createElement(l.vw,{key:n,color:"blue",style:{marginBottom:"4px"}},e.replace("_"," "))}))))))};return k?a.createElement(a.Fragment,null,a.createElement("div",{style:{display:"flex",alignItems:"center",padding:"8px",border:"1px solid #d9d9d9",borderRadius:"6px",marginBottom:"8px",background:g?"#f6ffed":"white"}},a.createElement("div",{style:{width:"60px",marginRight:"12px"}},a.createElement(l.$x,{size:"small"},null===(n=i.components)||void 0===n?void 0:n.slice(0,2).map((function(e,n){return a.createElement(l.eu,{key:n,size:"small",style:{backgroundColor:"#1890ff",fontSize:"10px"}},R(e))})),(null===(t=i.missing_components)||void 0===t?void 0:t.length)>0&&a.createElement(l.eu,{size:"small",style:{backgroundColor:"#52c41a",fontSize:"10px"}},"+"))),a.createElement("div",{style:{flex:1}},a.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"2px"}},a.createElement(x,{strong:!0,style:{fontSize:"12px"}},i.name),E&&a.createElement(l.Ex,{count:i.score,style:{backgroundColor:M(i.score),marginLeft:"8px"}})),a.createElement(x,{type:"secondary",style:{fontSize:"11px"}},(null===(o=i.missing_components)||void 0===o?void 0:o.length)>0?"Add ".concat(i.missing_components.join(", ")):i.explanation)),a.createElement(l.$x,null,y&&a.createElement(l.m_,{title:"Preview"},a.createElement(l.$n,{type:"text",size:"small",icon:a.createElement(c.Om2,null),onClick:j})),a.createElement(l.$n,{type:g?"default":"primary",size:"small",icon:g?a.createElement(c.JIb,null):a.createElement(c.bW0,null),onClick:I,disabled:g,loading:P},g?"Added":"Add"))),$()):a.createElement(a.Fragment,null,a.createElement(l.Zp,{size:"small",style:{marginBottom:"12px",border:g?"2px solid #52c41a":"1px solid #d9d9d9"},title:a.createElement("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"}},a.createElement(l.$x,null,a.createElement(x,{strong:!0},i.name),E&&a.createElement(l.Ex,{count:i.score,style:{backgroundColor:M(i.score)}})),g&&a.createElement(c.JIb,{style:{color:"#52c41a"}})),extra:a.createElement(l.$x,null,y&&a.createElement(l.m_,{title:"Preview combination"},a.createElement(l.$n,{type:"text",size:"small",icon:a.createElement(c.Om2,null),onClick:j})),a.createElement(l.$n,{type:g?"default":"primary",size:"small",icon:g?a.createElement(c.JIb,null):a.createElement(c.bW0,null),onClick:I,disabled:g,loading:P},g?"Added":"Add Components"))},a.createElement("div",{style:{marginBottom:"12px"}},U()),a.createElement(v,{style:{margin:"0 0 8px 0",fontSize:"12px",color:"#666"}},i.description),i.missing_components&&i.missing_components.length>0&&a.createElement("div",{style:{marginBottom:"8px"}},a.createElement(x,{strong:!0,style:{fontSize:"11px"}},"Missing components: "),a.createElement(l.$x,{size:"small",wrap:!0},i.missing_components.map((function(e,n){return a.createElement(l.vw,{key:n,color:"green",size:"small"},e)})))),a.createElement("div",{style:{display:"flex",alignItems:"center"}},a.createElement(c.rUN,{style:{marginRight:"4px",color:"#1890ff"}}),a.createElement(x,{style:{fontSize:"11px",fontStyle:"italic"}},i.explanation))),$())};h.propTypes={suggestion:y().shape({id:y().string.isRequired,name:y().string.isRequired,description:y().string.isRequired,score:y().number.isRequired,explanation:y().string.isRequired,components:y().array,missing_components:y().array,use_cases:y().array}).isRequired,onApply:y().func,onPreview:y().func,applied:y().bool,showPreview:y().bool,showScore:y().bool,compact:y().bool};var E,w,k,C,A,S,z,D,O,P,T=function(e){var n=e.suggestions,t=void 0===n?[]:n,o=e.onApply,r=e.onPreview,i=e.appliedSuggestions,s=void 0===i?new Set:i,d=e.loading,u=void 0!==d&&d,p=e.compact,m=void 0!==p&&p,f=e.showScore,g=void 0===f||f,b=e.showPreview,y=void 0===b||b,x=e.emptyMessage,v=void 0===x?"No component combinations available":x;return u?a.createElement("div",{style:{textAlign:"center",padding:"20px"}},a.createElement(l.tK,{tip:"Finding component combinations..."})):0===t.length?a.createElement("div",{style:{textAlign:"center",padding:"20px",color:"#999"}},a.createElement(c.rS9,{style:{fontSize:"48px",marginBottom:"16px"}}),a.createElement("div",null,v)):a.createElement("div",{style:{maxHeight:"400px",overflowY:"auto"}},t.map((function(e){return a.createElement(h,{key:e.id,suggestion:e,onApply:o,onPreview:r,applied:s.has(e.id),compact:m,showScore:g,showPreview:y})})))};function I(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);n&&(o=o.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,o)}return t}function j(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?I(Object(t),!0).forEach((function(n){(0,o.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):I(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}T.propTypes={suggestions:y().array,onApply:y().func,onPreview:y().func,appliedSuggestions:y().instanceOf(Set),loading:y().bool,compact:y().bool,showScore:y().bool,showPreview:y().bool,emptyMessage:y().string};var M=l.pd.Search,R=l.o5.Text,U=l.o5.Title,$=(l.SD.Panel,d.Ay.div(E||(E=(0,i.A)(["\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n"])))),_=d.Ay.div(w||(w=(0,i.A)(["\n  padding: 16px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  \n  .ant-typography {\n    color: white !important;\n    margin-bottom: 8px;\n  }\n"]))),B=d.Ay.div(k||(k=(0,i.A)(["\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n"]))),L=(0,d.Ay)(l.Zp)(C||(C=(0,i.A)(["\n  margin: 4px;\n  cursor: grab;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: ",";\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n    border-color: #1890ff;\n  }\n  \n  &:active {\n    cursor: grabbing;\n    transform: scale(0.98);\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    text-align: center;\n  }\n"])),(function(e){return e.isDragging?"#e6f7ff":"#fff"})),F=d.Ay.div(A||(A=(0,i.A)(["\n  font-size: 24px;\n  color: #1890ff;\n  margin-bottom: 8px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: rgba(24, 144, 255, 0.1);\n  margin: 0 auto 8px;\n  transition: all 0.3s ease;\n  \n  ",":hover & {\n    background: rgba(24, 144, 255, 0.2);\n    transform: scale(1.1);\n  }\n"])),L),V=d.Ay.div(S||(S=(0,i.A)(["\n  font-size: 12px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n"]))),H=d.Ay.div(z||(z=(0,i.A)(["\n  font-size: 10px;\n  color: #666;\n  line-height: 1.2;\n"]))),X=d.Ay.div(D||(D=(0,i.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 16px;\n  background: #f0f2f5;\n  border-bottom: 1px solid #d9d9d9;\n  font-weight: 600;\n  color: #333;\n"]))),W=d.Ay.div(O||(O=(0,i.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\n  gap: 8px;\n  padding: 16px;\n"]))),J=d.Ay.div(P||(P=(0,i.A)(["\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  color: #bbb;\n  font-size: 12px;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  \n  ",":hover & {\n    opacity: 1;\n  }\n"])),L);const N=function(e){var n=e.onAddComponent,t=e.onDragStart,o=e.onDragEnd,i=e.components,s=void 0===i?[]:i,d=e.selectedComponent,m=void 0===d?null:d,b=e.showAISuggestions,y=void 0===b||b,x=(0,a.useState)(""),v=(0,r.A)(x,2),h=v[0],E=v[1],w=(0,a.useState)(["AI Suggestions","Layout","Basic Components"]),k=(0,r.A)(w,2),C=k[0],A=k[1],S=(0,a.useState)(!0),z=(0,r.A)(S,2),D=z[0],O=z[1],P=(0,a.useState)(null),I=(0,r.A)(P,2),N=I[0],G=I[1],Y=(0,a.useRef)(null),q=(0,g.A)({autoRefresh:!0,context:{selectedComponent:m}}),Z=q.suggestions,K=q.loading,Q=q.applyComponentCombination,ee=(q.hasLayoutSuggestions,q.hasCombinationSuggestions),ne=function(){var e=(0,p.A)(f().mark((function e(t){return f().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!Q(t)){e.next=6;break}return t.missing_components&&t.missing_components.forEach((function(e){n(e)})),l.iU.success("Applied AI suggestion: ".concat(t.name)),e.abrupt("return",!0);case 6:e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("Error applying AI combination:",e.t0),l.iU.error("Failed to apply AI suggestion");case 12:return e.abrupt("return",!1);case 13:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(n){return e.apply(this,arguments)}}(),te=function(){if(!m)return[];var e=m.type,n=s.map((function(e){return e.type})),t=[];return"button"!==e||n.includes("form")||t.push({type:"form",reason:"Buttons often work with forms",priority:"high",icon:a.createElement(c.XDk,null),label:"Form"}),"form"!==e||n.includes("input")||t.push({type:"input",reason:"Forms need input fields",priority:"high",icon:a.createElement(c.XDk,null),label:"Input"}),"text"!==e||n.includes("image")||t.push({type:"image",reason:"Text and images work well together",priority:"medium",icon:a.createElement(c.JZT,null),label:"Image"}),"card"!==e||n.includes("button")||t.push({type:"button",reason:"Cards often need action buttons",priority:"medium",icon:a.createElement(c.rS9,null),label:"Button"}),t}(),oe=[].concat((0,u.A)(y&&(ee||te.length>0)?[{title:"AI Suggestions",description:"Smart component recommendations based on your current app",color:"#1890ff",isAI:!0,components:(0,u.A)(te.map((function(e){return{type:e.type,icon:e.icon,label:e.label,description:e.reason,usage:"Recommended for your ".concat(null==m?void 0:m.type),tags:["ai","smart","contextual"],priority:e.priority,isAISuggestion:!0}})))}]:[]),[{title:"Layout",description:"Structural components for organizing content",color:"#52c41a",components:[{type:"header",icon:a.createElement(c.ld1,null),label:"Header",description:"Page or section header with title and navigation",usage:"Use for page titles, navigation bars, or section headers",tags:["layout","navigation","title"]},{type:"section",icon:a.createElement(c.hy2,null),label:"Section",description:"Container for grouping related content",usage:"Organize content into logical sections",tags:["layout","container","organization"]},{type:"card",icon:a.createElement(c.wN,null),label:"Card",description:"Flexible content container with optional header and footer",usage:"Display content in a clean, contained format",tags:["layout","container","content"]},{type:"tabs",icon:a.createElement(c.DX6,null),label:"Tabs",description:"Tabbed interface for organizing content",usage:"Switch between different views or content sections",tags:["layout","navigation","organization"]},{type:"divider",icon:a.createElement(c.DX6,null),label:"Divider",description:"Visual separator between content sections",usage:"Separate content sections visually",tags:["layout","separator","visual"]}]},{title:"Basic Components",description:"Essential UI elements for content and interaction",color:"#1890ff",components:[{type:"text",icon:a.createElement(c.y9H,null),label:"Text",description:"Formatted text content with typography options",usage:"Display paragraphs, headings, and formatted text",tags:["content","text","typography"]},{type:"button",icon:a.createElement(c.rS9,null),label:"Button",description:"Interactive button for user actions",usage:"Trigger actions, submit forms, or navigate",tags:["interaction","action","click"]},{type:"image",icon:a.createElement(c.JZT,null),label:"Image",description:"Display images with responsive sizing",usage:"Show photos, illustrations, or graphics",tags:["media","visual","content"]},{type:"list",icon:a.createElement(c.CsJ,null),label:"List",description:"Ordered or unordered list of items",usage:"Display collections of related items",tags:["content","organization","items"]},{type:"tag",icon:a.createElement(c.owP,null),label:"Tag",description:"Small label for categorization or status",usage:"Label content, show status, or categorize",tags:["label","status","category"]}]},{title:"Form Components",description:"Interactive elements for user input and data collection",color:"#722ed1",components:[{type:"form",icon:a.createElement(c.XDk,null),label:"Form",description:"Container for form fields with validation",usage:"Collect user input with validation and submission",tags:["input","validation","data"]},{type:"input",icon:a.createElement(c.XDk,null),label:"Input",description:"Text input field for user data entry",usage:"Collect text, numbers, or other typed input",tags:["input","text","data"]},{type:"select",icon:a.createElement(c.XDk,null),label:"Select",description:"Dropdown selection from predefined options",usage:"Choose from a list of predefined options",tags:["input","selection","dropdown"]},{type:"checkbox",icon:a.createElement(c.C50,null),label:"Checkbox",description:"Boolean input for yes/no or multiple selections",usage:"Select multiple options or toggle settings",tags:["input","boolean","selection"]},{type:"datepicker",icon:a.createElement(c.hOh,null),label:"Date Picker",description:"Calendar interface for date selection",usage:"Select dates, date ranges, or schedule events",tags:["input","date","calendar"]},{type:"slider",icon:a.createElement(c.hcX,null),label:"Slider",description:"Range input with visual slider interface",usage:"Select numeric values within a range",tags:["input","range","numeric"]}]},{title:"Data Components",description:"Components for displaying and visualizing data",color:"#fa8c16",components:[{type:"table",icon:a.createElement(c.zDD,null),label:"Table",description:"Structured data display with sorting and filtering",usage:"Display tabular data with advanced features",tags:["data","table","structured"]},{type:"chart",icon:a.createElement(c.cd5,null),label:"Chart",description:"Visual data representation with multiple chart types",usage:"Visualize data trends and comparisons",tags:["data","visualization","analytics"]},{type:"statistic",icon:a.createElement(c.cd5,null),label:"Statistic",description:"Highlighted numeric data with formatting",usage:"Display key metrics and KPIs prominently",tags:["data","metrics","numbers"]}]}]),re=(0,a.useMemo)((function(){return h?oe.map((function(e){return j(j({},e),{},{components:e.components.filter((function(e){return e.label.toLowerCase().includes(h.toLowerCase())||e.description.toLowerCase().includes(h.toLowerCase())||e.tags.some((function(e){return e.toLowerCase().includes(h.toLowerCase())}))}))})})).filter((function(e){return e.components.length>0})):oe}),[h]),ie=function(e){G(null),o&&o()};return a.createElement($,null,a.createElement(_,null,a.createElement(U,{level:5,style:{margin:0,color:"white"}},"Component Palette"),a.createElement(R,{style:{color:"rgba(255, 255, 255, 0.8)"}},"Drag components to the canvas or click to add")),a.createElement(B,null,a.createElement(M,{placeholder:"Search components...",value:h,onChange:function(e){return E(e.target.value)},prefix:a.createElement(c.VrN,null),allowClear:!0,style:{marginBottom:8}}),a.createElement(l.$x,null,a.createElement(R,{style:{fontSize:12}},"Show descriptions:"),a.createElement(l.dO,{size:"small",checked:D,onChange:O}))),re.map((function(e){return a.createElement("div",{key:e.title},a.createElement(X,{onClick:function(){return n=e.title,void A((function(e){return e.includes(n)?e.filter((function(e){return e!==n})):[].concat((0,u.A)(e),[n])}));var n}},a.createElement(l.$x,null,a.createElement("div",{style:{width:12,height:12,borderRadius:"50%",backgroundColor:e.color}}),e.isAI&&a.createElement(c.J_h,{style:{color:e.color}}),a.createElement("span",null,e.title),a.createElement(l.Ex,{count:e.components.length,size:"small"}),e.isAI&&K.combinations&&a.createElement(l.Ex,{status:"processing"})),a.createElement(c.lHd,{style:{transform:C.includes(e.title)?"rotate(180deg)":"rotate(0deg)",transition:"transform 0.3s ease"}})),C.includes(e.title)&&a.createElement(a.Fragment,null,e.isAI&&ee&&a.createElement("div",{style:{padding:"16px",borderBottom:"1px solid #f0f0f0"}},a.createElement(R,{type:"secondary",style:{fontSize:"12px",display:"block",marginBottom:"8px"}},a.createElement(c.o3f,null)," AI-powered component combinations:"),a.createElement(T,{suggestions:Z.combinations,onApply:ne,loading:K.combinations,compact:!0,showScore:!1,showPreview:!1,emptyMessage:"No AI combinations available"})),a.createElement(W,null,e.components.map((function(e){return a.createElement(L,{key:e.type,size:"small",hoverable:!0,isDragging:(null==N?void 0:N.type)===e.type,draggable:!0,onDragStart:function(n){return function(e,n){if(G(n),e.dataTransfer.setData("application/json",JSON.stringify({type:n.type,label:n.label,source:"palette"})),e.dataTransfer.effectAllowed="copy",Y.current){var o=Y.current.cloneNode(!0);o.style.transform="rotate(5deg)",o.style.opacity="0.8",e.dataTransfer.setDragImage(o,60,30)}t&&t(n)}(n,e)},onDragEnd:ie,onClick:function(){return n(e.type)},ref:(null==N?void 0:N.type)===e.type?Y:null,style:{border:e.isAISuggestion?"2px solid #52c41a":void 0,background:e.isAISuggestion?"#f6ffed":void 0}},a.createElement(J,null,a.createElement(c.duJ,null)),e.isAISuggestion&&a.createElement("div",{style:{position:"absolute",top:4,right:4,background:"high"===e.priority?"#52c41a":"#1890ff",color:"white",borderRadius:"50%",width:16,height:16,display:"flex",alignItems:"center",justifyContent:"center",fontSize:10}},a.createElement(c.CwG,null)),a.createElement(F,{style:{background:e.isAISuggestion?"high"===e.priority?"rgba(82, 196, 26, 0.1)":"rgba(24, 144, 255, 0.1)":void 0}},e.icon),a.createElement(V,null,e.label),D&&a.createElement(H,null,e.description),a.createElement(l.m_,{title:a.createElement("div",null,a.createElement("div",{style:{fontWeight:"bold",marginBottom:4}},e.label,e.isAISuggestion&&a.createElement(l.Ex,{count:"AI",style:{backgroundColor:"#52c41a",marginLeft:8}})),a.createElement("div",{style:{marginBottom:8}},e.description),a.createElement("div",{style:{fontSize:11,color:"#ccc"}},a.createElement("strong",null,"Usage:")," ",e.usage),a.createElement("div",{style:{fontSize:11,color:"#ccc",marginTop:4}},a.createElement("strong",null,"Tags:")," ",e.tags.join(", "))),placement:"right"},a.createElement(c.rUN,{style:{position:"absolute",top:4,left:4,fontSize:10,color:e.isAISuggestion?"#52c41a":"#bbb",opacity:.7}})))})))))})),0===re.length&&a.createElement("div",{style:{padding:32,textAlign:"center",color:"#999"}},a.createElement(c.VrN,{style:{fontSize:24,marginBottom:8}}),a.createElement("div",null,'No components found matching "',h,'"'),a.createElement(l.$n,{type:"link",size:"small",onClick:function(){return E("")},style:{padding:0,marginTop:8}},"Clear search")))};var G,Y,q,Z,K,Q,ee,ne,te,oe,re,ie,ae,le=t(58168),ce=(t(2543),t(79459)),se=t(48860);function de(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);n&&(o=o.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,o)}return t}function ue(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?de(Object(t),!0).forEach((function(n){(0,o.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):de(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var pe=l.o5.Title,me=l.o5.Text,fe=(l.o5.Paragraph,l.l6.Option,{mobile:{name:"Mobile",icon:a.createElement(c.jHj,null),width:375,height:667,scale:.8,frame:!0},tablet:{name:"Tablet",icon:a.createElement(c.pLH,null),width:768,height:1024,scale:.7,frame:!0},desktop:{name:"Desktop",icon:a.createElement(c.zlw,null),width:1200,height:800,scale:1,frame:!1}}),ge=d.Ay.div(G||(G=(0,i.A)(["\n  position: relative;\n  height: 100%;\n  background: #f5f5f5;\n  border-radius: 8px;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n"]))),be=d.Ay.div(Y||(Y=(0,i.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 16px;\n  background: white;\n  border-bottom: 1px solid #e8e8e8;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  z-index: 10;\n  flex-wrap: wrap;\n  gap: 8px;\n\n  @media (max-width: 768px) {\n    padding: 6px 12px;\n    gap: 6px;\n  }\n"]))),ye=d.Ay.div(q||(q=(0,i.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 4px;\n  background: #f5f5f5;\n  border-radius: 6px;\n"]))),xe=(0,d.Ay)(l.$n)(Z||(Z=(0,i.A)(["\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  border: none;\n  background: ",";\n  color: ",";\n  box-shadow: none;\n\n  &:hover {\n    background: ",";\n    color: ",";\n  }\n"])),(function(e){return e.active?"#1890ff":"transparent"}),(function(e){return e.active?"white":"#666"}),(function(e){return e.active?"#40a9ff":"#e6f7ff"}),(function(e){return e.active?"white":"#1890ff"})),ve=d.Ay.div(K||(K=(0,i.A)(["\n  position: relative;\n  margin: 20px auto;\n  background: ",";\n  border-radius: ",";\n  padding: ",";\n  box-shadow: ",";\n  transition: all 0.3s ease;\n\n  ","\n\n  ","\n"])),(function(e){return"mobile"===e.deviceType?"#333":"tablet"===e.deviceType?"#444":"transparent"}),(function(e){return"mobile"===e.deviceType?"25px":"tablet"===e.deviceType?"15px":"0"}),(function(e){return"mobile"===e.deviceType?"20px 10px":"tablet"===e.deviceType?"15px":"0"}),(function(e){return e.frame?"0 8px 32px rgba(0, 0, 0, 0.3)":"none"}),(function(e){return"mobile"===e.deviceType&&"\n    &::before {\n      content: '';\n      position: absolute;\n      top: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 60px;\n      height: 4px;\n      background: #666;\n      border-radius: 2px;\n    }\n\n    &::after {\n      content: '';\n      position: absolute;\n      bottom: 8px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 40px;\n      height: 40px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  "}),(function(e){return"tablet"===e.deviceType&&"\n    &::before {\n      content: '';\n      position: absolute;\n      bottom: 6px;\n      left: 50%;\n      transform: translateX(-50%);\n      width: 30px;\n      height: 30px;\n      border: 2px solid #666;\n      border-radius: 50%;\n    }\n  "})),he=d.Ay.div(Q||(Q=(0,i.A)(["\n  width: ","px;\n  height: ","px;\n  max-width: 100%;\n  max-height: 100%;\n  background: white;\n  border-radius: ",";\n  overflow: auto;\n  position: relative;\n  transform: scale(",");\n  transform-origin: top center;\n  transition: all 0.3s ease;\n\n  @media (max-width: 1200px) {\n    transform: scale(",");\n  }\n\n  @media (max-width: 768px) {\n    transform: scale(",");\n  }\n"])),(function(e){return e.deviceWidth}),(function(e){return e.deviceHeight}),(function(e){return"mobile"===e.deviceType?"8px":"tablet"===e.deviceType?"6px":"0"}),(function(e){return e.scale}),(function(e){return Math.min(e.scale,.8)}),(function(e){return Math.min(e.scale,.6)})),Ee=d.Ay.div(ee||(ee=(0,i.A)(["\n  flex: 1;\n  position: relative;\n  overflow: auto;\n  background: ",";\n  background-size: ","px ","px;\n  background-position: ","px ","px;\n"])),(function(e){return e.showGrid?"radial-gradient(circle, #ddd 1px, transparent 1px)":"#f5f5f5"}),(function(e){return e.gridSize||20}),(function(e){return e.gridSize||20}),(function(e){var n;return(null===(n=e.gridOffset)||void 0===n?void 0:n.x)||0}),(function(e){var n;return(null===(n=e.gridOffset)||void 0===n?void 0:n.y)||0})),we=(d.Ay.div(ne||(ne=(0,i.A)(["\n  min-height: 100%;\n  min-width: 100%;\n  position: relative;\n  transform: scale(",");\n  transform-origin: top left;\n  transition: transform 0.2s ease;\n  padding: ",";\n"])),(function(e){return e.zoom||1}),(function(e){return e.previewMode?"0":"32px"})),d.Ay.div(te||(te=(0,i.A)(["\n  position: relative;\n  margin: 8px 0;\n  border: ",";\n  border-radius: 4px;\n  background: ",";\n  transition: all 0.3s ease;\n  cursor: ",";\n  \n  &:hover {\n    border-color: ",";\n    box-shadow: ",";\n    transform: ",";\n  }\n  \n  ","\n"])),(function(e){return e.isSelected?"2px solid #1890ff":"1px dashed transparent"}),(function(e){return e.isSelected?"rgba(24, 144, 255, 0.05)":"white"}),(function(e){return e.previewMode?"default":"pointer"}),(function(e){return e.previewMode?"transparent":"#1890ff"}),(function(e){return e.previewMode?"none":"0 2px 8px rgba(24, 144, 255, 0.2)"}),(function(e){return e.previewMode?"none":"translateY(-1px)"}),(function(e){return e.isDragOver&&"\n    border-color: #52c41a !important;\n    background: rgba(82, 196, 26, 0.1) !important;\n    transform: scale(1.02);\n  "}))),ke=d.Ay.div(oe||(oe=(0,i.A)(["\n  position: absolute;\n  top: -2px;\n  right: -2px;\n  display: flex;\n  gap: 4px;\n  background: rgba(255, 255, 255, 0.95);\n  padding: 4px;\n  border-radius: 4px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n  opacity: ",";\n  transform: translateY(",");\n  transition: all 0.3s ease;\n  z-index: 5;\n"])),(function(e){return e.visible?1:0}),(function(e){return e.visible?"0":"-10px"})),Ce=(0,d.Ay)(l.$n)(re||(re=(0,i.A)(["\n  width: 24px;\n  height: 24px;\n  padding: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n  box-shadow: none;\n  \n  &:hover {\n    background: #f0f0f0;\n    transform: scale(1.1);\n  }\n"]))),Ae=d.Ay.div(ie||(ie=(0,i.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed #d9d9d9;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.8);\n  opacity: ",";\n  pointer-events: ",";\n  transition: all 0.3s ease;\n  z-index: 1;\n  \n  ","\n"])),(function(e){return e.visible?1:0}),(function(e){return e.visible?"auto":"none"}),(function(e){return e.isActive&&"\n    border-color: #52c41a;\n    background: rgba(82, 196, 26, 0.1);\n    \n    &::before {\n      content: 'Drop component here';\n      color: #52c41a;\n      font-weight: 600;\n      font-size: 16px;\n    }\n  "})),Se=d.Ay.div(ae||(ae=(0,i.A)(["\n  position: absolute;\n  background: rgba(255, 255, 255, 0.9);\n  border: 1px solid #e8e8e8;\n  font-size: 10px;\n  color: #666;\n  z-index: 5;\n  \n  ","\n  \n  ","\n"])),(function(e){return"horizontal"===e.orientation&&"\n    top: 0;\n    left: 32px;\n    right: 0;\n    height: 20px;\n    border-bottom: 1px solid #e8e8e8;\n    background-image: repeating-linear-gradient(\n      to right,\n      transparent,\n      transparent 9px,\n      #e8e8e8 9px,\n      #e8e8e8 10px\n    );\n  "}),(function(e){return"vertical"===e.orientation&&"\n    top: 20px;\n    left: 0;\n    bottom: 0;\n    width: 32px;\n    border-right: 1px solid #e8e8e8;\n    background-image: repeating-linear-gradient(\n      to bottom,\n      transparent,\n      transparent 9px,\n      #e8e8e8 9px,\n      #e8e8e8 10px\n    );\n  "}));const ze=function(e){var n=e.components,t=void 0===n?[]:n,o=e.onSelectComponent,i=e.onDeleteComponent,d=e.onUpdateComponent,u=e.onMoveComponent,p=e.previewMode,m=void 0!==p&&p,f=e.selectedComponentId,g=e.onDrop,b=e.onDragOver,y=e.onDragLeave,x=e.realTimeUpdates,v=void 0===x||x,h=e.websocketConnected,E=void 0!==h&&h,w=(0,a.useState)("desktop"),k=(0,r.A)(w,2),C=k[0],A=k[1],S=(0,a.useState)(1),z=(0,r.A)(S,2),D=z[0],O=z[1],P=(0,a.useState)(!0),T=(0,r.A)(P,2),I=T[0],j=T[1],M=(0,a.useState)(20),R=(0,r.A)(M,2),U=R[0],$=(R[1],(0,a.useState)(!1)),_=(0,r.A)($,2),B=_[0],L=_[1],F=(0,a.useState)(!0),V=(0,r.A)(F,2),H=V[0],X=V[1],W=(0,a.useState)(!1),J=(0,r.A)(W,2),N=J[0],G=J[1],Y=(0,a.useState)(null),q=(0,r.A)(Y,2),Z=q[0],K=q[1],Q=(0,a.useState)(null),ee=(0,r.A)(Q,2),ne=ee[0],te=ee[1],oe=(0,a.useState)(!1),re=(0,r.A)(oe,2),ie=re[0],ae=re[1],de=(0,a.useState)(null),ze=(0,r.A)(de,2),De=ze[0],Oe=ze[1],Pe=(0,a.useRef)(null),Te=(0,a.useRef)(null),Ie=((0,s.wA)(),(0,s.d4)((function(e){return e.websocket||{}})),(0,s.d4)((function(e){var n;return null===(n=e.websocket)||void 0===n?void 0:n.service}))),je=fe[C],Me=(0,a.useMemo)((function(){return{width:je.width,height:je.height,scale:je.scale}}),[je]),Re=(0,ce.A)({components:t,onUpdateComponent:d,onAddComponent:function(e){d&&d(e)},onDeleteComponent:i,websocketService:Ie,enableWebSocket:v&&E}),Ue=Re.isUpdating,$e=Re.lastUpdateTime,_e=Re.websocketConnected,Be=Re.updateComponent,Le=(Re.addComponent,Re.deleteComponent,Re.getAllComponents),Fe=(Re.forceUpdate,(0,se.A)({components:Le(),containerHeight:Me.height,itemHeight:"mobile"===C?60:"tablet"===C?80:100,enableVirtualization:t.length>20,enablePerformanceMonitoring:!0})),Ve=Fe.visibleComponents,He=Fe.getContainerProps,Xe=Fe.getSpacerProps,We=(Fe.renderTime,Fe.frameRate,Fe.memoryUsage,Fe.startRenderMeasurement),Je=Fe.endRenderMeasurement,Ne=Fe.getCachedComponent,Ge=(0,a.useCallback)((function(e){A(e);var n=fe[e];n.scale!==D&&O(n.scale)}),[D]),Ye=(0,a.useCallback)((function(e,n){v&&(Be(e,n,!(arguments.length>2&&void 0!==arguments[2])||arguments[2]),ae(!0),Oe(new Date),setTimeout((function(){return ae(!1)}),500))}),[v,Be]);(0,a.useEffect)((function(){Ue!==ie&&ae(Ue),$e&&$e!==De&&Oe($e)}),[Ue,$e,ie,De]),(0,a.useEffect)((function(){return function(){Te.current&&clearTimeout(Te.current)}}),[]);var qe=(0,a.useCallback)((function(e){e.preventDefault(),G(!0)}),[]),Ze=(0,a.useCallback)((function(e){e.preventDefault(),e.dataTransfer.dropEffect="copy",b&&b(e)}),[b]),Ke=(0,a.useCallback)((function(e){e.preventDefault(),e.currentTarget.contains(e.relatedTarget)||(G(!1),K(null),y&&y(e))}),[y]),Qe=(0,a.useCallback)((function(e){if(e.preventDefault(),G(!1),K(null),g){var n,t=null===(n=Pe.current)||void 0===n?void 0:n.getBoundingClientRect();if(t){var o=(e.clientX-t.left)/D,r=(e.clientY-t.top)/D,i=H?Math.round(o/U)*U:o,a=H?Math.round(r/U)*U:r;g(e,{x:i,y:a})}}}),[g,D,H,U]),en=(0,a.useCallback)((function(e,n){e.preventDefault(),e.stopPropagation(),K(n)}),[]),nn=(0,a.useCallback)((function(e,n){e.preventDefault(),e.stopPropagation(),e.currentTarget.contains(e.relatedTarget)||K(null)}),[]),tn=(0,a.useCallback)((function(e,n){var t=e.component||e;return void 0!==e.index&&e.index,Ne(t.id,(function(){We();var e=t.id===f,n=ne===t.id,r=Z===t.id,s=function(){var e,n,o,r,i,c,s,d,u,p,f,g={fontSize:"mobile"===C?"14px":"tablet"===C?"15px":"16px",padding:"mobile"===C?"8px":"tablet"===C?"12px":"16px"};switch(t.type){case"text":return a.createElement(me,{style:g},(null===(e=t.props)||void 0===e?void 0:e.content)||"Sample text");case"button":return a.createElement(l.$n,{type:(null===(n=t.props)||void 0===n?void 0:n.type)||"default",size:"mobile"===C?"small":"middle",style:{fontSize:g.fontSize}},(null===(o=t.props)||void 0===o?void 0:o.text)||"Button");case"header":return a.createElement(pe,{level:(null===(r=t.props)||void 0===r?void 0:r.level)||("mobile"===C?4:2),style:g},(null===(i=t.props)||void 0===i?void 0:i.text)||"Header");case"card":return a.createElement(l.Zp,{title:(null===(c=t.props)||void 0===c?void 0:c.title)||"Card Title",size:"mobile"===C?"small":"default",style:{fontSize:g.fontSize}},(null===(s=t.props)||void 0===s?void 0:s.content)||"Card content");case"image":return a.createElement("img",{src:(null===(d=t.props)||void 0===d?void 0:d.src)||"https://via.placeholder.com/150",alt:(null===(u=t.props)||void 0===u?void 0:u.alt)||"Image",style:{maxWidth:"100%",height:"auto",borderRadius:"mobile"===C?"4px":"6px"}});case"divider":return a.createElement(l.cG,{style:g},null===(p=t.props)||void 0===p?void 0:p.text);case"input":return a.createElement(l.pd,{placeholder:(null===(f=t.props)||void 0===f?void 0:f.placeholder)||"Enter text",disabled:!m,size:"mobile"===C?"small":"middle",style:g});case"form":return a.createElement(l.lV,{layout:"vertical",size:"mobile"===C?"small":"middle"},a.createElement(l.lV.Item,{label:"Sample Field"},a.createElement(l.pd,{placeholder:"Sample input",disabled:!m,style:g})));case"table":return a.createElement(l.XI,{columns:[{title:"Name",dataIndex:"name",key:"name"},{title:"Age",dataIndex:"age",key:"age"}],dataSource:[{key:"1",name:"John",age:32},{key:"2",name:"Jane",age:28}],size:"mobile"===C?"small":"middle",scroll:"mobile"===C?{x:!0}:void 0});default:return a.createElement("div",{style:{padding:g.padding,border:"1px dashed #ccc",textAlign:"center",fontSize:g.fontSize,borderRadius:"mobile"===C?"4px":"6px"}},t.type," Component")}};return a.createElement(we,{key:t.id,isSelected:e,previewMode:m,isDragOver:r,onClick:function(e){e.stopPropagation(),!m&&o&&o(t)},onMouseEnter:function(){return te(t.id)},onMouseLeave:function(){return te(null)},onDragOver:function(e){return en(e,t.id)},onDragLeave:function(e){return nn(e,t.id)},style:{padding:"mobile"===C?"8px":"tablet"===C?"12px":"16px",position:"relative",margin:"mobile"===C?"4px 0":"8px 0"}},a.createElement(s,null),!m&&(e||n)&&a.createElement(ke,{visible:e||n},a.createElement(l.m_,{title:"Edit"},a.createElement(Ce,{icon:a.createElement(c.xjh,null),size:"small",onClick:function(e){e.stopPropagation(),v&&Ye(t.id,{editing:!0})}})),a.createElement(l.m_,{title:"Copy"},a.createElement(Ce,{icon:a.createElement(c.wq3,null),size:"small",onClick:function(e){if(e.stopPropagation(),v){var n=ue(ue({},t),{},{id:Date.now().toString()});Ye(n.id,n)}}})),"desktop"===C&&a.createElement(a.Fragment,null,a.createElement(l.m_,{title:"Move Up"},a.createElement(Ce,{icon:a.createElement(c.lu9,null),size:"small",onClick:function(e){e.stopPropagation(),u&&u(t.id,"up"),v&&Ye(t.id,{moved:"up"})}})),a.createElement(l.m_,{title:"Move Down"},a.createElement(Ce,{icon:a.createElement(c.Axk,null),size:"small",onClick:function(e){e.stopPropagation(),u&&u(t.id,"down"),v&&Ye(t.id,{moved:"down"})}}))),a.createElement(l.m_,{title:"Delete"},a.createElement(Ce,{icon:a.createElement(c.SUY,null),size:"small",danger:!0,onClick:function(e){e.stopPropagation(),i&&i(t.id),v&&Ye(t.id,{deleted:!0})}}))))}))}),[f,ne,Z,C,m,v,Ne,We,Je,Ye,u,i]);return a.createElement(ge,null,a.createElement(be,null,a.createElement(l.$x,null,a.createElement(me,{strong:!0},"Preview"),a.createElement(l.cG,{type:"vertical"}),a.createElement(ye,null,Object.entries(fe).map((function(e){var n=(0,r.A)(e,2),t=n[0],o=n[1];return a.createElement(xe,{key:t,size:"small",active:C===t,onClick:function(){return Ge(t)},icon:o.icon},!m&&o.name)})))),a.createElement(l.$x,null,v&&a.createElement(a.Fragment,null,a.createElement(l.Ex,{status:_e?"success":"error",text:_e?"Live":"Offline"}),(ie||Ue)&&a.createElement(c.OmY,{spin:!0}),!1,a.createElement(l.cG,{type:"vertical"})),!m&&a.createElement(a.Fragment,null,a.createElement(l.m_,{title:"Zoom Out"},a.createElement(l.$n,{icon:a.createElement(c.uC4,null),size:"small",onClick:function(){return O((function(e){return Math.max(e-.1,.5)}))},disabled:D<=.5})),a.createElement(me,{style:{minWidth:40,textAlign:"center"}},Math.round(100*D),"%"),a.createElement(l.m_,{title:"Zoom In"},a.createElement(l.$n,{icon:a.createElement(c.$gz,null),size:"small",onClick:function(){return O((function(e){return Math.min(e+.1,2)}))},disabled:D>=2})),a.createElement(l.$n,{size:"small",onClick:function(){return O(je.scale)}},"Reset"),a.createElement(l.cG,{type:"vertical"}))),!m&&a.createElement(l.$x,null,a.createElement(l.m_,{title:"Toggle Grid"},a.createElement(l.dO,{checked:I,onChange:j,checkedChildren:a.createElement(c.bnM,null),unCheckedChildren:a.createElement(c.bnM,null),size:"small"})),a.createElement(l.m_,{title:"Toggle Rulers"},a.createElement(l.dO,{checked:B,onChange:L,size:"small"})),a.createElement(l.m_,{title:"Snap to Grid"},a.createElement(l.dO,{checked:H,onChange:X,size:"small"}))),v&&De&&a.createElement(l.$x,null,a.createElement(me,{type:"secondary",style:{fontSize:"12px"}},"Updated: ",De.toLocaleTimeString()))),a.createElement(Ee,{showGrid:I&&!m&&"desktop"===C,gridSize:U,onDragEnter:qe,onDragOver:Ze,onDragLeave:Ke,onDrop:Qe},B&&!m&&"desktop"===C&&a.createElement(a.Fragment,null,a.createElement(Se,{orientation:"horizontal"}),a.createElement(Se,{orientation:"vertical"})),a.createElement(ve,{deviceType:C,frame:je.frame},a.createElement(he,(0,le.A)({},He(),{ref:Pe,deviceWidth:Me.width,deviceHeight:Me.height,deviceType:C,scale:D,onClick:function(){return o&&o(null)}}),!1,a.createElement("div",Xe().before),Ve.length>0?Ve.map((function(e,n){return tn(e,n)})):0===t.length?a.createElement(l.Sv,{description:a.createElement("span",null,"No components added yet.",a.createElement("br",null),m?"Add components to see them here.":"Drag components from the palette to get started."),style:{margin:"mobile"===C?"50px 20px":"100px 0",fontSize:"mobile"===C?"14px":"16px"}}):null,a.createElement("div",Xe().after))),a.createElement(Ae,{visible:N&&!m,isActive:N})))};var De,Oe,Pe,Te,Ie,je,Me,Re,Ue,$e,_e,Be,Le,Fe,Ve=(0,d.i7)(De||(De=(0,i.A)(["\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.7;\n    transform: scale(1.05);\n  }\n"]))),He=(0,d.i7)(Oe||(Oe=(0,i.A)(["\n  0%, 100% { transform: translateX(0); }\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }\n  20%, 40%, 60%, 80% { transform: translateX(3px); }\n"]))),Xe=(0,d.i7)(Pe||(Pe=(0,i.A)(["\n  0%, 20%, 53%, 80%, 100% {\n    transform: translate3d(0, 0, 0);\n  }\n  40%, 43% {\n    transform: translate3d(0, -8px, 0);\n  }\n  70% {\n    transform: translate3d(0, -4px, 0);\n  }\n  90% {\n    transform: translate3d(0, -2px, 0);\n  }\n"]))),We=(0,d.i7)(Te||(Te=(0,i.A)(["\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n"]))),Je=(0,d.i7)(Ie||(Ie=(0,i.A)(["\n  0%, 100% {\n    box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);\n  }\n  50% {\n    box-shadow: 0 0 20px rgba(24, 144, 255, 0.8);\n  }\n"]))),Ne=d.Ay.div(je||(je=(0,i.A)(["\n  position: absolute;\n  width: 100%;\n  height: 4px;\n  background: ",";\n  border-radius: 2px;\n  z-index: 1000;\n  animation: "," 1.5s ease-in-out infinite;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: -6px;\n    top: -2px;\n    width: 8px;\n    height: 8px;\n    background: ",";\n    border-radius: 50%;\n    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    right: -6px;\n    top: -2px;\n    width: 8px;\n    height: 8px;\n    background: ",";\n    border-radius: 50%;\n    box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);\n  }\n"])),(function(e){return e.isValid?"#52c41a":"#ff4d4f"}),Ve,(function(e){return e.isValid?"#52c41a":"#ff4d4f"}),(function(e){return e.isValid?"#52c41a":"#ff4d4f"})),Ge=d.Ay.div(Me||(Me=(0,i.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed ",";\n  border-radius: 8px;\n  background: ",";\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n  animation: "," 0.3s ease-out;\n  \n  ","\n  \n  ","\n"])),(function(e){return e.isValid?"#52c41a":"#ff4d4f"}),(function(e){return e.isValid?"rgba(82, 196, 26, 0.1)":"rgba(255, 77, 79, 0.1)"}),We,(function(e){return e.isValid&&(0,d.AH)(Re||(Re=(0,i.A)(["\n    animation: "," 1.5s ease-in-out infinite;\n  "])),Ve)}),(function(e){return!e.isValid&&(0,d.AH)(Ue||(Ue=(0,i.A)(["\n    animation: "," 0.5s ease-in-out;\n  "])),He)})),Ye=d.Ay.div($e||($e=(0,i.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 16px;\n  background: white;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  font-weight: 600;\n  color: ",";\n  animation: "," 0.3s ease-out 0.1s both;\n"])),(function(e){return e.isValid?"#52c41a":"#ff4d4f"}),We),qe=d.Ay.div(_e||(_e=(0,i.A)(["\n  position: fixed;\n  pointer-events: none;\n  z-index: 9999;\n  opacity: 0.7;\n  transform: rotate(5deg) scale(0.9);\n  filter: blur(1px);\n  transition: all 0.1s ease-out;\n  border: 2px solid #1890ff;\n  border-radius: 4px;\n  background: rgba(255, 255, 255, 0.9);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n"]))),Ze=d.Ay.div(Be||(Be=(0,i.A)(["\n  padding: 8px 12px;\n  background: white;\n  border: 2px solid #1890ff;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n  color: #1890ff;\n  animation: "," 2s ease-in-out infinite;\n"])),Je),Ke=d.Ay.div(Le||(Le=(0,i.A)(["\n  position: absolute;\n  top: -2px;\n  left: -2px;\n  right: -2px;\n  bottom: -2px;\n  border: 2px solid #1890ff;\n  border-radius: 6px;\n  background: rgba(24, 144, 255, 0.05);\n  pointer-events: none;\n  z-index: 10;\n  animation: "," 0.2s ease-out;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: -4px;\n    left: -4px;\n    right: -4px;\n    bottom: -4px;\n    border: 1px solid rgba(24, 144, 255, 0.3);\n    border-radius: 8px;\n    animation: "," 2s ease-in-out infinite;\n  }\n"])),We,Ve),Qe=d.Ay.div(Fe||(Fe=(0,i.A)(["\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: #52c41a;\n  color: white;\n  padding: 8px 12px;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-weight: 600;\n  z-index: 1001;\n  animation: "," 0.6s ease-out;\n  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);\n"])),Xe),en=function(e){var n=e.position,t=e.isValid,o=void 0===t||t,r=e.visible;return void 0!==r&&r&&n?a.createElement(Ne,{isValid:o,style:{top:n.y,left:n.x,width:n.width||"100%"}}):null},nn=function(e){var n=e.isValid,t=void 0===n||n,o=e.visible,r=void 0!==o&&o,i=e.message;return r?a.createElement(Ge,{isValid:t},a.createElement(Ye,{isValid:t},t?a.createElement(c.hWy,null):a.createElement(c.bBN,null),i||(t?"Drop here to add component":"Invalid drop target"))):null},tn=function(e){var n=e.visible,t=void 0!==n&&n,o=e.position,r=e.children,i=e.componentType,l=(0,a.useRef)(null);return(0,a.useEffect)((function(){l.current&&t&&o&&(l.current.style.left="".concat(o.x,"px"),l.current.style.top="".concat(o.y,"px"))}),[t,o]),t?a.createElement(qe,{ref:l},r||a.createElement(Ze,null,a.createElement(c.duJ,null),i||"Component")):null},on=function(e){var n=e.visible,t=void 0!==n&&n,o=e.targetRef,r=(0,a.useRef)(null);return(0,a.useEffect)((function(){if(r.current&&null!=o&&o.current&&t){var e=o.current.getBoundingClientRect(),n=r.current;n.style.position="fixed",n.style.top="".concat(e.top,"px"),n.style.left="".concat(e.left,"px"),n.style.width="".concat(e.width,"px"),n.style.height="".concat(e.height,"px")}}),[t,o]),t?a.createElement(Ke,{ref:r}):null},rn=function(e){var n=e.visible,t=void 0!==n&&n,o=e.message,r=void 0===o?"Component added!":o;return t?a.createElement(Qe,null,a.createElement(c.hWy,null),r):null};const an=function(e){var n=e.isDragging,t=void 0!==n&&n,o=e.isOver,r=void 0!==o&&o,i=e.isValid,l=void 0===i||i,c=e.dropPosition,s=e.ghostPosition,d=e.hoveredElement,u=e.draggedComponent,p=e.showSuccess,m=void 0!==p&&p,f=e.successMessage,g=e.dropMessage,b=e.children;return a.createElement(a.Fragment,null,a.createElement(en,{position:c,isValid:l,visible:t&&r&&c}),a.createElement(nn,{isValid:l,visible:t&&r,message:g}),a.createElement(tn,{visible:t,position:s,componentType:null==u?void 0:u.type}),a.createElement(on,{visible:!t&&d,targetRef:d}),a.createElement(rn,{visible:m,message:f}),b)};var ln,cn,sn,dn,un=d.Ay.div(ln||(ln=(0,i.A)(["\n  position: fixed;\n  z-index: 10000;\n  background: white;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  border: 1px solid #e8e8e8;\n  min-width: 180px;\n  overflow: hidden;\n  \n  .ant-menu {\n    border: none;\n    box-shadow: none;\n  }\n  \n  .ant-menu-item {\n    margin: 0;\n    padding: 8px 16px;\n    height: auto;\n    line-height: 1.4;\n    \n    &:hover {\n      background: #f0f2f5;\n    }\n    \n    &.ant-menu-item-disabled {\n      color: #bfbfbf;\n      cursor: not-allowed;\n      \n      &:hover {\n        background: transparent;\n      }\n    }\n  }\n  \n  .ant-menu-item-icon {\n    margin-right: 8px;\n    font-size: 14px;\n  }\n"]))),pn=d.Ay.div(cn||(cn=(0,i.A)(["\n  padding: 4px 0;\n  \n  &:not(:last-child) {\n    border-bottom: 1px solid #f0f0f0;\n  }\n"]))),mn=d.Ay.div(sn||(sn=(0,i.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n"]))),fn=d.Ay.span(dn||(dn=(0,i.A)(["\n  font-size: 11px;\n  color: #999;\n  margin-left: 16px;\n"])));const gn=function(e){var n=e.visible,t=e.x,o=e.y,r=e.onClose,i=e.selectedComponent,s=e.selectedComponents,d=void 0===s?[]:s,u=e.onCopy,p=e.onPaste,m=e.onDelete,f=e.onEdit,g=e.onDuplicate,b=e.onMoveUp,y=e.onMoveDown,x=e.onToggleVisibility,v=e.onToggleLock,h=e.onGroup,E=e.onUngroup,w=e.onCopyStyle,k=e.onPasteStyle,C=e.onProperties,A=e.clipboardHasData,S=void 0!==A&&A,z=e.canMoveUp,D=void 0===z||z,O=e.canMoveDown,P=void 0===O||O,T=e.canGroup,I=void 0!==T&&T,j=e.canUngroup,M=void 0!==j&&j,R=(0,a.useRef)(null);if((0,a.useEffect)((function(){if(n&&R.current){var e=R.current,r=e.getBoundingClientRect(),i=window.innerWidth,a=window.innerHeight,l=t,c=o;t+r.width>i&&(l=i-r.width-10),o+r.height>a&&(c=a-r.height-10),e.style.left="".concat(Math.max(10,l),"px"),e.style.top="".concat(Math.max(10,c),"px")}}),[n,t,o]),(0,a.useEffect)((function(){var e=function(e){"Escape"===e.key&&n&&r()};return document.addEventListener("keydown",e),function(){return document.removeEventListener("keydown",e)}}),[n,r]),!n)return null;var U=d.length>1,$=i||d.length>0,_=[{section:"edit",items:[{key:"edit",icon:a.createElement(c.xjh,null),label:"Edit Properties",shortcut:"Enter",disabled:!$||U,onClick:function(){null==f||f(i),r()}},{key:"copy",icon:a.createElement(c.wq3,null),label:U?"Copy ".concat(d.length," Components"):"Copy",shortcut:"Ctrl+C",disabled:!$,onClick:function(){null==u||u(U?d:i),r()}},{key:"paste",icon:a.createElement(c.wq3,{style:{transform:"scaleX(-1)"}}),label:"Paste",shortcut:"Ctrl+V",disabled:!S,onClick:function(){null==p||p(),r()}},{key:"duplicate",icon:a.createElement(c.wq3,null),label:U?"Duplicate Selection":"Duplicate",shortcut:"Ctrl+D",disabled:!$,onClick:function(){null==g||g(U?d:i),r()}}]},{section:"arrange",items:[{key:"move-up",icon:a.createElement(c.lu9,null),label:"Move Up",shortcut:"Ctrl+↑",disabled:!$||!D,onClick:function(){U?d.forEach((function(e){return null==b?void 0:b(e)})):null==b||b(i),r()}},{key:"move-down",icon:a.createElement(c.Axk,null),label:"Move Down",shortcut:"Ctrl+↓",disabled:!$||!P,onClick:function(){U?d.forEach((function(e){return null==y?void 0:y(e)})):null==y||y(i),r()}}]},{section:"visibility",items:[{key:"toggle-visibility",icon:!1!==(null==i?void 0:i.visible)?a.createElement(c.LCF,null):a.createElement(c.Om2,null),label:!1!==(null==i?void 0:i.visible)?"Hide":"Show",shortcut:"Ctrl+H",disabled:!$,onClick:function(){U?d.forEach((function(e){return null==x?void 0:x(e)})):null==x||x(i),r()}},{key:"toggle-lock",icon:null!=i&&i.locked?a.createElement(c.Rrh,null):a.createElement(c.sXv,null),label:null!=i&&i.locked?"Unlock":"Lock",shortcut:"Ctrl+L",disabled:!$,onClick:function(){U?d.forEach((function(e){return null==v?void 0:v(e)})):null==v||v(i),r()}}]},{section:"group",items:[{key:"group",icon:a.createElement(c.WT5,null),label:"Group",shortcut:"Ctrl+G",disabled:!I||d.length<2,onClick:function(){null==h||h(d),r()}},{key:"ungroup",icon:a.createElement(c.T6x,null),label:"Ungroup",shortcut:"Ctrl+Shift+G",disabled:!M,onClick:function(){null==E||E(i),r()}}]},{section:"style",items:[{key:"copy-style",icon:a.createElement(c.tBh,null),label:"Copy Style",disabled:!$||U,onClick:function(){null==w||w(i),r()}},{key:"paste-style",icon:a.createElement(c.tBh,{style:{transform:"scaleX(-1)"}}),label:"Paste Style",disabled:!$,onClick:function(){U?d.forEach((function(e){return null==k?void 0:k(e)})):null==k||k(i),r()}}]},{section:"actions",items:[{key:"properties",icon:a.createElement(c.JO7,null),label:"Properties",shortcut:"F4",disabled:!$||U,onClick:function(){null==C||C(i),r()}},{key:"delete",icon:a.createElement(c.SUY,null),label:U?"Delete ".concat(d.length," Components"):"Delete",shortcut:"Delete",disabled:!$,danger:!0,onClick:function(){U?d.forEach((function(e){return null==m?void 0:m(e)})):null==m||m(i),r()}}]}];return a.createElement(un,{ref:R,style:{left:t,top:o},onClick:function(e){return e.stopPropagation()}},a.createElement(l.W1,{mode:"vertical",selectable:!1},_.map((function(e,n){return a.createElement(pn,{key:e.section},e.items.map((function(e){return a.createElement(l.W1.Item,{key:e.key,icon:e.icon,disabled:e.disabled,danger:e.danger,onClick:e.onClick},a.createElement(mn,null,a.createElement("span",null,e.label),e.shortcut&&a.createElement(fn,null,e.shortcut)))})))}))))};var bn,yn,xn,vn,hn,En,wn=t(94588),kn=t(47119),Cn=t(34816);function An(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);n&&(o=o.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,o)}return t}function Sn(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?An(Object(t),!0).forEach((function(n){(0,o.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):An(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var zn=l.PE.Sider,Dn=l.PE.Content,On=(0,d.Ay)(l.PE)(bn||(bn=(0,i.A)(["\n  height: 100vh;\n  background: #f5f5f5;\n"]))),Pn=d.Ay.div(yn||(yn=(0,i.A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 8px 16px;\n  background: white;\n  border-bottom: 1px solid #e8e8e8;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  z-index: 100;\n"]))),Tn=d.Ay.div(xn||(xn=(0,i.A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"]))),In=(0,d.Ay)(zn)(vn||(vn=(0,i.A)(["\n  background: white;\n  border-right: 1px solid #e8e8e8;\n  overflow: auto;\n  \n  .ant-layout-sider-children {\n    padding: 16px;\n  }\n"]))),jn=(0,d.Ay)(Dn)(hn||(hn=(0,i.A)(["\n  display: flex;\n  flex-direction: column;\n  background: #f5f5f5;\n  position: relative;\n"]))),Mn=d.Ay.div(En||(En=(0,i.A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  backdrop-filter: blur(2px);\n"])));const Rn=function(){var e=(0,s.wA)(),n=(0,s.d4)((function(e){var n;return(null===(n=e.app)||void 0===n?void 0:n.components)||[]})),t=(0,a.useState)(!1),o=(0,r.A)(t,2),i=o[0],d=o[1],u=(0,a.useState)(!1),p=(0,r.A)(u,2),m=p[0],f=p[1],g=(0,a.useState)(null),b=(0,r.A)(g,2),y=b[0],x=b[1],v=(0,a.useState)(!1),h=(0,r.A)(v,2),E=h[0],w=h[1],k=(0,wn.aD)(n),C=k.state,A=k.pushState,S=k.undo,z=k.redo,D=k.canUndo,O=k.canRedo,P=(0,wn.EF)(),T=P.contextMenu,I=P.showContextMenu,j=P.hideContextMenu,M=(0,wn.R2)(),R=M.isLoading,U=M.loadingMessage,$=M.startLoading,_=M.stopLoading,B=(0,wn.Cd)(n),L=B.selectedItems,F=B.selectItem,V=B.clearSelection,H=(B.isSelected,(0,wn.iD)()),X=H.copy,W=H.paste,J=H.hasData,G=(0,a.useRef)(null),Y=(0,a.useCallback)((function(n,t,o){if(t){$("Adding component...");try{var r={id:"".concat(t.type,"-").concat(Date.now()),type:t.type,props:Sn({x:o.x,y:o.y},re(t.type)),createdAt:(new Date).toISOString()};e((0,Cn.addComponent)(r)),w(!0),setTimeout((function(){return w(!1)}),2e3),l.iU.success("".concat(t.label||t.type," component added"))}catch(e){l.iU.error("Failed to add component"),console.error("Error adding component:",e)}finally{_()}}}),[e,$,_]),q=(0,kn.$l)({onDrop:Y,snapToGrid:!0,gridSize:20}),Z=q.isDragging,K=q.isOver,Q=q.validDropZone,ee=q.dropPosition,ne=q.dropZoneRef,te=q.handleDragStart,oe=q.handleDragEnd;(0,a.useEffect)((function(){JSON.stringify(n)!==JSON.stringify(C)&&A(n)}),[n,C,A]);var re=function(e){return{text:{content:"Sample text",fontSize:14},button:{text:"Button",type:"default"},header:{text:"Header",level:2},card:{title:"Card Title",content:"Card content"},image:{src:"https://via.placeholder.com/150",alt:"Image"},input:{placeholder:"Enter text"},form:{layout:"vertical"}}[e]||{}},ie=(0,a.useCallback)((function(e){e?F(e):V()}),[F,V]),ae=(0,a.useCallback)((function(n){$("Deleting component...");try{e((0,Cn.removeComponent)(n)),V(),l.iU.success("Component deleted")}catch(e){l.iU.error("Failed to delete component")}finally{_()}}),[e,V,$,_]),le=(0,a.useCallback)((function(n,t){$("Updating component...");try{e((0,Cn.updateComponent)(n,t)),l.iU.success("Component updated")}catch(e){l.iU.error("Failed to update component")}finally{_()}}),[e,$,_]),ce=(0,a.useCallback)((function(){S()&&l.iU.success("Undone")}),[S]),se=(0,a.useCallback)((function(){z()&&l.iU.success("Redone")}),[z]),de=(0,a.useCallback)((function(e){X(e),l.iU.success("Component copied")}),[X]),ue=(0,a.useCallback)((function(){var n=W();if(n){var t=Sn(Sn({},n),{},{id:"".concat(n.type,"-").concat(Date.now()),props:Sn(Sn({},n.props),{},{x:(n.props.x||0)+20,y:(n.props.y||0)+20})});e((0,Cn.addComponent)(t)),l.iU.success("Component pasted")}}),[W,e]);return(0,a.useCallback)((function(e,n){e.preventDefault(),I(e,[{key:"edit",label:"Edit",icon:"edit"},{key:"copy",label:"Copy",icon:"copy"},{key:"delete",label:"Delete",icon:"delete",danger:!0}])}),[I]),(0,wn.KW)({"ctrl+z":ce,"ctrl+y":se,"ctrl+c":function(){L.length>0&&de(n.find((function(e){return e.id===L[0]})))},"ctrl+v":ue,delete:function(){L.length>0&&L.forEach((function(e){return ae(e)}))},escape:function(){V(),j()},f11:function(e){e.preventDefault(),f(!m)}},[L,n,ce,se,de,ue,ae,V,j,m]),a.createElement(On,{ref:G,className:m?"fullscreen":""},a.createElement(Pn,null,a.createElement(Tn,null,a.createElement(l.m_,{title:"Undo (Ctrl+Z)"},a.createElement(l.$n,{icon:a.createElement(c.Xrf,null),disabled:!D,onClick:ce})),a.createElement(l.m_,{title:"Redo (Ctrl+Y)"},a.createElement(l.$n,{icon:a.createElement(c.zYO,null),disabled:!O,onClick:se})),a.createElement(l.$n,{icon:a.createElement(c.ylI,null)},"Save")),a.createElement(Tn,null,a.createElement("span",{style:{fontSize:16,fontWeight:600}},"Enhanced Component Builder")),a.createElement(Tn,null,a.createElement(l.m_,{title:"Toggle Preview Mode"},a.createElement(l.$n,{icon:a.createElement(c.Om2,null),type:i?"primary":"default",onClick:function(){return d(!i)}},"Preview")),a.createElement(l.m_,{title:"Settings"},a.createElement(l.$n,{icon:a.createElement(c.JO7,null)})),a.createElement(l.m_,{title:"Fullscreen (F11)"},a.createElement(l.$n,{icon:m?a.createElement(c.J5k,null):a.createElement(c.KrH,null),onClick:function(){return f(!m)}})))),a.createElement(l.PE,null,!i&&a.createElement(In,{width:300,theme:"light"},a.createElement(N,{onAddComponent:function(n){var t={id:"".concat(n,"-").concat(Date.now()),type:n,props:re(n),createdAt:(new Date).toISOString()};e((0,Cn.addComponent)(t)),l.iU.success("".concat(n," component added"))},onDragStart:function(e){x(e),te(null,e)},onDragEnd:function(){x(null),oe()}})),a.createElement(jn,{ref:ne},a.createElement(ze,{components:n,onSelectComponent:ie,onDeleteComponent:ae,onUpdateComponent:le,previewMode:i,selectedComponentId:L[0],onDrop:Y}),R&&a.createElement(Mn,null,a.createElement(l.$x,{direction:"vertical",align:"center"},a.createElement(l.tK,{size:"large"}),a.createElement("span",null,U))))),a.createElement(an,{isDragging:Z,isOver:K,isValid:Q,dropPosition:ee,draggedComponent:y,showSuccess:E,successMessage:"Component added successfully!"}),a.createElement(gn,{visible:T.visible,x:T.x,y:T.y,onClose:j,selectedComponent:n.find((function(e){return e.id===L[0]})),selectedComponents:n.filter((function(e){return L.includes(e.id)})),onCopy:de,onPaste:ue,onDelete:ae,clipboardHasData:J}))}}}]);