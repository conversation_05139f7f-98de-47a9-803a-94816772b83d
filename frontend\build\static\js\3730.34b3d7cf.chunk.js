"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[3730],{43730:(e,t,r)=>{r.r(t),r.d(t,{default:()=>H});var n=r(64467),a=r(60436),c=r(10467),o=r(5544),l=r(57528),i=r(54756),u=r.n(i),s=r(96540),m=r(1807),p=r(35346),d=r(11080),f=r(70572);function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach((function(t){(0,n.A)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var g={marks:{},measures:{},resources:[],errors:[]},w=function(){if(window.PerformanceObserver)try{new PerformanceObserver((function(e){var t=e.getEntries();g.resources=[].concat((0,a.A)(g.resources),(0,a.A)(t))})).observe({entryTypes:["resource"]}),new PerformanceObserver((function(e){e.getEntries().forEach((function(e){console.warn("Long task detected:",e),g.errors.push({type:"long-task",message:"Long task detected: ".concat(e.name," (").concat(e.duration,"ms)"),timestamp:(new Date).toISOString(),details:e})}))})).observe({entryTypes:["longtask"]}),new PerformanceObserver((function(e){e.getEntries().forEach((function(e){h("paint-".concat(e.name),e.startTime)}))})).observe({entryTypes:["paint"]}),new PerformanceObserver((function(e){e.getEntries().forEach((function(e){h("fid-".concat(e.name),e.startTime),e.duration>100&&g.errors.push({type:"fid",message:"High First Input Delay: ".concat(e.duration,"ms"),timestamp:(new Date).toISOString(),details:e})}))})).observe({entryTypes:["first-input"]}),new PerformanceObserver((function(e){e.getEntries().forEach((function(e){e.value>.1&&g.errors.push({type:"layout-shift",message:"High Layout Shift: ".concat(e.value),timestamp:(new Date).toISOString(),details:e})}))})).observe({entryTypes:["layout-shift"]})}catch(e){console.warn("Error setting up PerformanceObserver:",e)}},h=function(e,t){try{void 0!==t?window.performance.mark(e,{startTime:t}):window.performance.mark(e);var r=window.performance.getEntriesByName(e,"mark");r.length>0&&(g.marks[e]=r[r.length-1])}catch(t){console.warn('Error creating mark "'.concat(e,'":'),t)}},b=function(e,t,r){try{window.performance.measure(e,t,r);var n=window.performance.getEntriesByName(e,"measure");n.length>0&&(g.measures[e]=n[n.length-1])}catch(t){console.warn('Error creating measure "'.concat(e,'":'),t)}},y=function(){return v({},g.marks)},k=function(){return v({},g.measures)},j=function(){return(0,a.A)(g.resources)},A=function(){return(0,a.A)(g.errors)},S=function(){try{window.performance.clearMarks(),g.marks={}}catch(e){console.warn("Error clearing marks:",e)}},O=function(){try{window.performance.clearMeasures(),g.measures={}}catch(e){console.warn("Error clearing measures:",e)}},P=function(){try{window.performance.clearResourceTimings(),g.resources=[]}catch(e){console.warn("Error clearing resource timings:",e)}},T=function(){g.errors=[]},C=function(){return{marks:y(),measures:k(),resources:j(),errors:A(),navigation:window.performance.timing?{navigationStart:window.performance.timing.navigationStart,loadEventEnd:window.performance.timing.loadEventEnd,domComplete:window.performance.timing.domComplete,domInteractive:window.performance.timing.domInteractive,domContentLoadedEventEnd:window.performance.timing.domContentLoadedEventEnd}:null,memory:window.performance.memory?{jsHeapSizeLimit:window.performance.memory.jsHeapSizeLimit,totalJSHeapSize:window.performance.memory.totalJSHeapSize,usedJSHeapSize:window.performance.memory.usedJSHeapSize}:null}};const x=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.enabled,r=void 0!==t&&t,n=e.autoMarkRenders,a=void 0===n||n,c=e.autoMarkEffects,l=void 0===c||c,i=e.autoMarkEvents,u=void 0===i||i,m=e.reportInterval,p=void 0===m?1e4:m,d=e.onReport,f=void 0===d?null:d,E=(0,s.useState)(r),v=(0,o.A)(E,2),x=v[0],D=v[1],I=(0,s.useState)(!1),L=(0,o.A)(I,2),R=L[0],U=L[1],M=(0,s.useRef)(null),z=(0,s.useRef)("Component-".concat(Math.random().toString(36).substr(2,9))),J=(0,s.useRef)(0),N=(0,s.useRef)(0),Z=(0,s.useRef)(0),F=(0,s.useRef)(null);(0,s.useEffect)((function(){return x&&!R&&(M.current=window.performance?(g.marks={},g.measures={},g.resources=[],g.errors=[],window.performance.setResourceTimingBufferSize&&window.performance.setResourceTimingBufferSize(500),w(),{mark:h,measure:b,getMarks:y,getMeasures:k,getResourceTimings:j,getErrors:A,clearMarks:S,clearMeasures:O,clearResourceTimings:P,clearErrors:T,getPerformanceReport:C}):(console.warn("Performance API is not available in this browser."),null),U(!0),M.current&&M.current.mark("".concat(z.current,"-mount"))),function(){M.current&&(M.current.mark("".concat(z.current,"-unmount")),M.current.measure("".concat(z.current,"-lifetime"),"".concat(z.current,"-mount"),"".concat(z.current,"-unmount"))),F.current&&clearInterval(F.current)}}),[x,R]),(0,s.useEffect)((function(){if(x&&R&&f&&p>0)return F.current=setInterval((function(){if(M.current){var e=M.current.getPerformanceReport();f(e)}}),p),function(){clearInterval(F.current)}}),[x,R,f,p]),(0,s.useEffect)((function(){x&&R&&a&&M.current&&(J.current+=1,M.current.mark("".concat(z.current,"-render-").concat(J.current)),J.current>1&&M.current.measure("".concat(z.current,"-render-time-").concat(J.current),"".concat(z.current,"-render-").concat(J.current-1),"".concat(z.current,"-render-").concat(J.current)))}));var B=(0,s.useCallback)((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"effect";return x&&R&&l&&M.current?function(){N.current+=1;var r="".concat(z.current,"-").concat(t,"-").concat(N.current);M.current.mark("".concat(r,"-start"));try{var n=e.apply(void 0,arguments);return n&&"function"==typeof n.then?n.then((function(e){return M.current.mark("".concat(r,"-end")),M.current.measure("".concat(r,"-time"),"".concat(r,"-start"),"".concat(r,"-end")),e})).catch((function(e){throw M.current.mark("".concat(r,"-error")),M.current.measure("".concat(r,"-error-time"),"".concat(r,"-start"),"".concat(r,"-error")),e})):(M.current.mark("".concat(r,"-end")),M.current.measure("".concat(r,"-time"),"".concat(r,"-start"),"".concat(r,"-end")),n)}catch(e){throw M.current.mark("".concat(r,"-error")),M.current.measure("".concat(r,"-error-time"),"".concat(r,"-start"),"".concat(r,"-error")),e}}:e}),[x,R,l]),H=(0,s.useCallback)((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"event";return x&&R&&u&&M.current?function(){Z.current+=1;var r="".concat(z.current,"-").concat(t,"-").concat(Z.current);M.current.mark("".concat(r,"-start"));try{var n=e.apply(void 0,arguments);return n&&"function"==typeof n.then?n.then((function(e){return M.current.mark("".concat(r,"-end")),M.current.measure("".concat(r,"-time"),"".concat(r,"-start"),"".concat(r,"-end")),e})).catch((function(e){throw M.current.mark("".concat(r,"-error")),M.current.measure("".concat(r,"-error-time"),"".concat(r,"-start"),"".concat(r,"-error")),e})):(M.current.mark("".concat(r,"-end")),M.current.measure("".concat(r,"-time"),"".concat(r,"-start"),"".concat(r,"-end")),n)}catch(e){throw M.current.mark("".concat(r,"-error")),M.current.measure("".concat(r,"-error-time"),"".concat(r,"-start"),"".concat(r,"-error")),e}}:e}),[x,R,u]),V=(0,s.useCallback)((function(e){z.current=e}),[]),$=(0,s.useCallback)((function(e){D(e)}),[]),_=(0,s.useCallback)((function(){return x&&R&&M.current?M.current.getPerformanceReport():null}),[x,R]),G=(0,s.useCallback)((function(){x&&R&&M.current&&(M.current.clearMarks(),M.current.clearMeasures(),M.current.clearResourceTimings(),M.current.clearErrors())}),[x,R]);return{isEnabled:x,isInitialized:R,setEnabled:$,setComponentName:V,wrapEffect:B,wrapEvent:H,getReport:_,clearData:G,mark:(0,s.useCallback)((function(e){x&&R&&M.current&&M.current.mark("".concat(z.current,"-").concat(e))}),[x,R]),measure:(0,s.useCallback)((function(e,t,r){x&&R&&M.current&&M.current.measure("".concat(z.current,"-").concat(e),"".concat(z.current,"-").concat(t),"".concat(z.current,"-").concat(r))}),[x,R])}};var D,I,L;function R(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function U(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?R(Object(r),!0).forEach((function(t){(0,n.A)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var M=m.o5.Title,z=m.o5.Text,J=m.l6.Option,N=m.tU.TabPane,Z=f.Ay.div(D||(D=(0,l.A)(["\n  padding: 24px;\n"]))),F=f.Ay.div(I||(I=(0,l.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  flex-wrap: wrap;\n  gap: 16px;\n"]))),B=f.Ay.div(L||(L=(0,l.A)(["\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n"])));const H=function(){var e=(0,s.useState)([]),t=(0,o.A)(e,2),r=t[0],n=t[1],l=(0,s.useState)(!0),i=(0,o.A)(l,2),f=i[0],E=i[1],v=(0,s.useState)(""),g=(0,o.A)(v,2),w=g[0],h=g[1],b=(0,s.useState)(!1),y=(0,o.A)(b,2),k=y[0],j=y[1],A=(0,s.useState)(!1),S=(0,o.A)(A,2),O=S[0],P=S[1],T=(0,s.useState)(null),C=(0,o.A)(T,2),D=C[0],I=C[1],L=m.lV.useForm(),R=(0,o.A)(L,1)[0],H=(0,d.Zp)(),V=x({enabled:!1});V.setComponentName("ProjectsPage"),(0,s.useEffect)((function(){V.mark("fetch-projects-start");var e=function(){var e=(0,c.A)(u().mark((function e(){return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,new Promise((function(e){return setTimeout(e,1e3)}));case 3:n([{id:"1",name:"E-commerce Dashboard",description:"A dashboard for an e-commerce website",status:"active",createdAt:"2023-05-15T10:30:00Z",updatedAt:"2023-05-20T14:45:00Z",owner:"John Doe",team:["John Doe","Jane Smith"],tags:["dashboard","e-commerce"],template:"dashboard"},{id:"2",name:"Blog Template",description:"A template for a blog website",status:"completed",createdAt:"2023-04-10T09:15:00Z",updatedAt:"2023-04-25T16:20:00Z",owner:"Jane Smith",team:["Jane Smith","Bob Johnson"],tags:["blog","template"],template:"blog"},{id:"3",name:"Mobile App UI",description:"UI design for a mobile app",status:"archived",createdAt:"2023-03-05T11:45:00Z",updatedAt:"2023-03-15T13:10:00Z",owner:"Bob Johnson",team:["Bob Johnson"],tags:["mobile","ui"],template:"mobile"},{id:"4",name:"Landing Page",description:"A landing page for a product",status:"active",createdAt:"2023-05-01T08:30:00Z",updatedAt:"2023-05-10T15:45:00Z",owner:"John Doe",team:["John Doe","Alice Williams"],tags:["landing","marketing"],template:"landing"},{id:"5",name:"Admin Panel",description:"An admin panel for a web application",status:"active",createdAt:"2023-04-20T13:15:00Z",updatedAt:"2023-05-05T10:30:00Z",owner:"Alice Williams",team:["Alice Williams","John Doe"],tags:["admin","dashboard"],template:"admin"}]),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),console.error("Error fetching projects:",e.t0),m.iU.error("Failed to load projects");case 11:return e.prev=11,E(!1),V.mark("fetch-projects-end"),V.measure("fetch-projects","fetch-projects-start","fetch-projects-end"),e.finish(11);case 16:case"end":return e.stop()}}),e,null,[[0,7,11,16]])})));return function(){return e.apply(this,arguments)}}();e()}),[V]);var $=function(){var e=(0,c.A)(u().mark((function e(t){var c;return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return V.mark("create-project-start"),e.prev=1,e.next=4,new Promise((function(e){return setTimeout(e,1e3)}));case 4:c={id:String(r.length+1),name:t.name,description:t.description,status:"active",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),owner:"Current User",team:["Current User"],tags:t.tags||[],template:t.template},n([].concat((0,a.A)(r),[c])),j(!1),R.resetFields(),m.iU.success("Project created successfully"),H("/app-builder?project=".concat(c.id)),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(1),console.error("Error creating project:",e.t0),m.iU.error("Failed to create project");case 16:return e.prev=16,V.mark("create-project-end"),V.measure("create-project","create-project-start","create-project-end"),e.finish(16);case 20:case"end":return e.stop()}}),e,null,[[1,12,16,20]])})));return function(t){return e.apply(this,arguments)}}(),_=function(){var e=(0,c.A)(u().mark((function e(t){return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return V.mark("delete-project-start"),e.prev=1,e.next=4,new Promise((function(e){return setTimeout(e,1e3)}));case 4:n(r.filter((function(e){return e.id!==t}))),m.iU.success("Project deleted successfully"),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("Error deleting project:",e.t0),m.iU.error("Failed to delete project");case 12:return e.prev=12,V.mark("delete-project-end"),V.measure("delete-project","delete-project-start","delete-project-end"),e.finish(12);case 16:case"end":return e.stop()}}),e,null,[[1,8,12,16]])})));return function(t){return e.apply(this,arguments)}}(),G=function(){var e=(0,c.A)(u().mark((function e(t){var c;return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return V.mark("duplicate-project-start"),e.prev=1,e.next=4,new Promise((function(e){return setTimeout(e,1e3)}));case 4:c=U(U({},t),{},{id:String(r.length+1),name:"".concat(t.name," (Copy)"),createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()}),n([].concat((0,a.A)(r),[c])),m.iU.success("Project duplicated successfully"),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),console.error("Error duplicating project:",e.t0),m.iU.error("Failed to duplicate project");case 13:return e.prev=13,V.mark("duplicate-project-end"),V.measure("duplicate-project","duplicate-project-start","duplicate-project-end"),e.finish(13);case 17:case"end":return e.stop()}}),e,null,[[1,9,13,17]])})));return function(t){return e.apply(this,arguments)}}(),W=r.filter((function(e){var t=w.toLowerCase();return e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)||e.owner.toLowerCase().includes(t)||e.tags.some((function(e){return e.toLowerCase().includes(t)}))})),q=[{title:"Name",dataIndex:"name",key:"name",sorter:function(e,t){return e.name.localeCompare(t.name)}},{title:"Description",dataIndex:"description",key:"description",ellipsis:!0},{title:"Status",dataIndex:"status",key:"status",render:function(e){return s.createElement(m.vw,{color:"active"===e?"green":"completed"===e?"blue":"default"},e.toUpperCase())},filters:[{text:"Active",value:"active"},{text:"Completed",value:"completed"},{text:"Archived",value:"archived"}],onFilter:function(e,t){return t.status===e}},{title:"Created",dataIndex:"createdAt",key:"createdAt",render:function(e){return new Date(e).toLocaleDateString()},sorter:function(e,t){return new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime()}},{title:"Owner",dataIndex:"owner",key:"owner"},{title:"Tags",dataIndex:"tags",key:"tags",render:function(e){return s.createElement(s.Fragment,null,e.map((function(e){return s.createElement(m.vw,{key:e},e)})))}},{title:"Actions",key:"actions",render:function(e,t){return s.createElement(m.$x,{size:"small"},s.createElement(m.m_,{title:"View"},s.createElement(m.$n,{icon:s.createElement(p.Om2,null),size:"small",onClick:function(){return I(t),void P(!0)}})),s.createElement(m.m_,{title:"Edit"},s.createElement(m.$n,{icon:s.createElement(p.xjh,null),size:"small",onClick:function(){H("/app-builder?project=".concat(t.id))}})),s.createElement(m.m_,{title:"Export"},s.createElement(m.$n,{icon:s.createElement(p.PZg,null),size:"small",onClick:function(){return e=t,void m.iU.info("Exporting project: ".concat(e.name));var e}})),s.createElement(m.m_,{title:"Duplicate"},s.createElement(m.$n,{icon:s.createElement(p.wq3,null),size:"small",onClick:function(){return G(t)}})),s.createElement(m.m_,{title:"Delete"},s.createElement(m.iS,{title:"Are you sure you want to delete this project?",onConfirm:function(){return _(t.id)},okText:"Yes",cancelText:"No"},s.createElement(m.$n,{icon:s.createElement(p.SUY,null),size:"small",danger:!0}))))}}];return s.createElement(Z,null,s.createElement(F,null,s.createElement(M,{level:2},"Projects"),s.createElement(B,null,s.createElement(m.pd,{placeholder:"Search projects",prefix:s.createElement(p.VrN,null),value:w,onChange:function(e){return h(e.target.value)},style:{width:250}}),s.createElement(m.$n,{type:"primary",icon:s.createElement(p.bW0,null),onClick:function(){return j(!0)}},"New Project"))),s.createElement(m.Zp,null,f?s.createElement(m.EA,{active:!0,paragraph:{rows:10}}):s.createElement(m.XI,{dataSource:W,columns:q,rowKey:"id",pagination:{pageSize:10},locale:{emptyText:s.createElement(m.Sv,{description:"No projects found",image:m.Sv.PRESENTED_IMAGE_SIMPLE})}})),s.createElement(m.aF,{title:"Create New Project",visible:k,onCancel:function(){return j(!1)},footer:null},s.createElement(m.lV,{form:R,layout:"vertical",onFinish:$},s.createElement(m.lV.Item,{name:"name",label:"Project Name",rules:[{required:!0,message:"Please enter a project name"}]},s.createElement(m.pd,{placeholder:"Enter project name"})),s.createElement(m.lV.Item,{name:"description",label:"Description"},s.createElement(m.pd.TextArea,{placeholder:"Enter project description",rows:4})),s.createElement(m.lV.Item,{name:"template",label:"Template",rules:[{required:!0,message:"Please select a template"}]},s.createElement(m.l6,{placeholder:"Select a template"},s.createElement(J,{value:"blank"},"Blank"),s.createElement(J,{value:"dashboard"},"Dashboard"),s.createElement(J,{value:"blog"},"Blog"),s.createElement(J,{value:"ecommerce"},"E-commerce"),s.createElement(J,{value:"landing"},"Landing Page"),s.createElement(J,{value:"admin"},"Admin Panel"),s.createElement(J,{value:"mobile"},"Mobile App"))),s.createElement(m.lV.Item,{name:"tags",label:"Tags"},s.createElement(m.l6,{mode:"tags",placeholder:"Add tags",style:{width:"100%"}})),s.createElement(m.lV.Item,null,s.createElement(m.$n,{type:"primary",htmlType:"submit"},"Create Project")))),s.createElement(m._s,{title:null==D?void 0:D.name,width:600,placement:"right",onClose:function(){return P(!1)},visible:O},D&&s.createElement(m.tU,{defaultActiveKey:"details"},s.createElement(N,{tab:s.createElement("span",null,s.createElement(p.Om2,null)," Details"),key:"details"},s.createElement("div",null,s.createElement(M,{level:4},"Description"),s.createElement(z,null,D.description),s.createElement(m.cG,null),s.createElement(M,{level:4},"Status"),s.createElement(m.vw,{color:"active"===D.status?"green":"completed"===D.status?"blue":"default"},D.status.toUpperCase()),s.createElement(m.cG,null),s.createElement(M,{level:4},"Created"),s.createElement(z,null,new Date(D.createdAt).toLocaleString()),s.createElement(m.cG,null),s.createElement(M,{level:4},"Last Updated"),s.createElement(z,null,new Date(D.updatedAt).toLocaleString()),s.createElement(m.cG,null),s.createElement(M,{level:4},"Tags"),D.tags.map((function(e){return s.createElement(m.vw,{key:e},e)})),s.createElement(m.cG,null),s.createElement(M,{level:4},"Template"),s.createElement(z,null,D.template))),s.createElement(N,{tab:s.createElement("span",null,s.createElement(p.QM0,null)," Team"),key:"team"},s.createElement("div",null,s.createElement(M,{level:4},"Owner"),s.createElement(z,null,D.owner),s.createElement(m.cG,null),s.createElement(M,{level:4},"Team Members"),s.createElement("ul",null,D.team.map((function(e){return s.createElement("li",{key:e},e)}))))),s.createElement(N,{tab:s.createElement("span",null,s.createElement(p.dUu,null)," History"),key:"history"},s.createElement(m.Sv,{description:"No history available"})),s.createElement(N,{tab:s.createElement("span",null,s.createElement(p.JO7,null)," Settings"),key:"settings"},s.createElement(m.Sv,{description:"No settings available"})))))}}}]);