"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[1375],{91375:(e,n,a)=>{a.r(n),a.d(n,{default:()=>v});var t=a(5544),r=a(96540),o=a(49391),s=a(93385),i=a(64467),c=a(10467),l=a(54756),m=a.n(l),d=a(69477);function u(e,n){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),a.push.apply(a,t)}return a}function p(e){for(var n=1;n<arguments.length;n++){var a=null!=arguments[n]?arguments[n]:{};n%2?u(Object(a),!0).forEach((function(n){(0,i.A)(e,n,a[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(a,n))}))}return e}const g=function(){var e=(0,r.useState)({username:"",email:"",name:"",bio:"",avatar:""}),n=(0,t.A)(e,2),a=n[0],o=n[1],s=(0,r.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),l=(0,t.A)(s,2),u=l[0],g=l[1],v=(0,r.useState)(!1),b=(0,t.A)(v,2),f=b[0],E=b[1],h=(0,r.useState)(null),w=(0,t.A)(h,2),N=w[0],y=w[1],x=(0,r.useState)(null),k=(0,t.A)(x,2),P=k[0],S=k[1],A=(0,r.useState)(!1),C=(0,t.A)(A,2),z=C[0],j=C[1],O=(0,r.useState)(!1),F=(0,t.A)(O,2),D=F[0],U=F[1],M=(0,r.useState)("profile"),R=(0,t.A)(M,2),q=R[0],I=R[1];(0,r.useEffect)((function(){var e=function(){var e=(0,c.A)(m().mark((function e(){var n;return m().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,E(!0),e.next=4,(0,d.VM)();case 4:(n=e.sent)&&o({username:n.username||"",email:n.email||"",name:n.name||"",bio:n.bio||"",avatar:n.avatar||""}),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),console.error("Error fetching profile:",e.t0),y("Failed to load profile data");case 12:return e.prev=12,E(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(){return e.apply(this,arguments)}}();e()}),[]);var G=function(e){var n=e.target,t=n.name,r=n.value;o(p(p({},a),{},(0,i.A)({},t,r))),j(!1)},L=function(e){var n=e.target,a=n.name,t=n.value;g(p(p({},u),{},(0,i.A)({},a,t))),U(!1)},B=function(){var e=(0,c.A)(m().mark((function e(n){var t;return m().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.preventDefault(),e.prev=1,E(!0),y(null),j(!1),e.next=7,(0,d.eg)(a);case 7:(t=e.sent).success?j(!0):y(t.error||"Failed to update profile"),e.next=15;break;case 11:e.prev=11,e.t0=e.catch(1),console.error("Profile update error:",e.t0),y("An unexpected error occurred. Please try again.");case 15:return e.prev=15,E(!1),e.finish(15);case 18:case"end":return e.stop()}}),e,null,[[1,11,15,18]])})));return function(n){return e.apply(this,arguments)}}(),T=function(){var e=(0,c.A)(m().mark((function e(n){var a;return m().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n.preventDefault(),u.newPassword===u.confirmPassword){e.next=4;break}return S("Passwords do not match"),e.abrupt("return");case 4:if(!(u.newPassword.length<8)){e.next=7;break}return S("Password must be at least 8 characters long"),e.abrupt("return");case 7:return e.prev=7,E(!0),S(null),U(!1),e.next=13,(0,d.ec)(u.currentPassword,u.newPassword);case 13:(a=e.sent).success?(U(!0),g({currentPassword:"",newPassword:"",confirmPassword:""})):S(a.error||"Failed to change password"),e.next=21;break;case 17:e.prev=17,e.t0=e.catch(7),console.error("Password change error:",e.t0),S("An unexpected error occurred. Please try again.");case 21:return e.prev=21,E(!1),e.finish(21);case 24:case"end":return e.stop()}}),e,null,[[7,17,21,24]])})));return function(n){return e.apply(this,arguments)}}();return r.createElement("div",{className:"profile-container"},r.createElement("div",{className:"profile-header"},r.createElement("h2",null,"User Profile")),r.createElement("div",{className:"profile-tabs"},r.createElement("button",{className:"tab-button ".concat("profile"===q?"active":""),onClick:function(){return I("profile")}},"Profile"),r.createElement("button",{className:"tab-button ".concat("password"===q?"active":""),onClick:function(){return I("password")}},"Change Password")),r.createElement("div",{className:"profile-content"},"profile"===q&&r.createElement("div",{className:"profile-tab"},N&&r.createElement("div",{className:"error-message"},N),z&&r.createElement("div",{className:"success-message"},"Profile updated successfully"),r.createElement("form",{onSubmit:B,className:"profile-form"},r.createElement("div",{className:"form-group"},r.createElement("label",{htmlFor:"username"},"Username"),r.createElement("input",{type:"text",id:"username",name:"username",value:a.username,onChange:G,disabled:f,readOnly:!0}),r.createElement("div",{className:"field-hint"},"Username cannot be changed")),r.createElement("div",{className:"form-group"},r.createElement("label",{htmlFor:"email"},"Email"),r.createElement("input",{type:"email",id:"email",name:"email",value:a.email,onChange:G,disabled:f,readOnly:!0}),r.createElement("div",{className:"field-hint"},"Email cannot be changed")),r.createElement("div",{className:"form-group"},r.createElement("label",{htmlFor:"name"},"Full Name"),r.createElement("input",{type:"text",id:"name",name:"name",value:a.name,onChange:G,placeholder:"Enter your full name",disabled:f})),r.createElement("div",{className:"form-group"},r.createElement("label",{htmlFor:"bio"},"Bio"),r.createElement("textarea",{id:"bio",name:"bio",value:a.bio,onChange:G,placeholder:"Tell us about yourself",disabled:f,rows:4})),r.createElement("div",{className:"form-group"},r.createElement("label",{htmlFor:"avatar"},"Avatar URL"),r.createElement("input",{type:"text",id:"avatar",name:"avatar",value:a.avatar,onChange:G,placeholder:"Enter avatar URL",disabled:f})),r.createElement("button",{type:"submit",className:"save-button",disabled:f},f?"Saving...":"Save Profile"))),"password"===q&&r.createElement("div",{className:"password-tab"},P&&r.createElement("div",{className:"error-message"},P),D&&r.createElement("div",{className:"success-message"},"Password changed successfully"),r.createElement("form",{onSubmit:T,className:"password-form"},r.createElement("div",{className:"form-group"},r.createElement("label",{htmlFor:"currentPassword"},"Current Password"),r.createElement("input",{type:"password",id:"currentPassword",name:"currentPassword",value:u.currentPassword,onChange:L,placeholder:"Enter current password",disabled:f,required:!0})),r.createElement("div",{className:"form-group"},r.createElement("label",{htmlFor:"newPassword"},"New Password"),r.createElement("input",{type:"password",id:"newPassword",name:"newPassword",value:u.newPassword,onChange:L,placeholder:"Enter new password",disabled:f,required:!0}),r.createElement("div",{className:"password-requirements"},"Password must be at least 8 characters long")),r.createElement("div",{className:"form-group"},r.createElement("label",{htmlFor:"confirmPassword"},"Confirm Password"),r.createElement("input",{type:"password",id:"confirmPassword",name:"confirmPassword",value:u.confirmPassword,onChange:L,placeholder:"Confirm new password",disabled:f,required:!0})),r.createElement("button",{type:"submit",className:"save-button",disabled:f},f?"Changing Password...":"Change Password")))),r.createElement("style",{jsx:!0},"\n        .profile-container {\n          max-width: 800px;\n          margin: 0 auto;\n          padding: var(--spacing-lg);\n        }\n        \n        .profile-header {\n          margin-bottom: var(--spacing-lg);\n          text-align: center;\n        }\n        \n        .profile-tabs {\n          display: flex;\n          border-bottom: 1px solid var(--color-border);\n          margin-bottom: var(--spacing-lg);\n        }\n        \n        .tab-button {\n          padding: var(--spacing-sm) var(--spacing-md);\n          background: none;\n          border: none;\n          border-bottom: 2px solid transparent;\n          color: var(--color-textSecondary);\n          cursor: pointer;\n          font-size: var(--font-size-md);\n          transition: all var(--transition-fast) var(--transition-timing-function);\n        }\n        \n        .tab-button:hover {\n          color: var(--color-text);\n        }\n        \n        .tab-button.active {\n          color: var(--color-primary);\n          border-bottom-color: var(--color-primary);\n        }\n        \n        .profile-content {\n          background-color: var(--color-surface);\n          border-radius: var(--border-radius-lg);\n          box-shadow: var(--shadow-sm);\n          padding: var(--spacing-lg);\n        }\n        \n        .error-message {\n          padding: var(--spacing-sm);\n          margin-bottom: var(--spacing-md);\n          background-color: rgba(var(--color-error-rgb), 0.1);\n          border: 1px solid var(--color-error);\n          border-radius: var(--border-radius-md);\n          color: var(--color-error);\n          font-size: var(--font-size-sm);\n        }\n        \n        .success-message {\n          padding: var(--spacing-sm);\n          margin-bottom: var(--spacing-md);\n          background-color: rgba(var(--color-success-rgb), 0.1);\n          border: 1px solid var(--color-success);\n          border-radius: var(--border-radius-md);\n          color: var(--color-success);\n          font-size: var(--font-size-sm);\n        }\n        \n        .profile-form, .password-form {\n          display: flex;\n          flex-direction: column;\n          gap: var(--spacing-md);\n        }\n        \n        .form-group {\n          display: flex;\n          flex-direction: column;\n          gap: var(--spacing-xs);\n        }\n        \n        .form-group label {\n          font-size: var(--font-size-sm);\n          color: var(--color-textSecondary);\n        }\n        \n        .form-group input, .form-group textarea {\n          padding: var(--spacing-sm) var(--spacing-md);\n          border: 1px solid var(--color-border);\n          border-radius: var(--border-radius-md);\n          background-color: var(--color-background);\n          color: var(--color-text);\n          font-size: var(--font-size-md);\n          transition: border-color var(--transition-fast) var(--transition-timing-function);\n        }\n        \n        .form-group input:focus, .form-group textarea:focus {\n          outline: none;\n          border-color: var(--color-primary);\n        }\n        \n        .form-group input[readonly] {\n          background-color: var(--color-surface);\n          color: var(--color-textSecondary);\n          cursor: not-allowed;\n        }\n        \n        .field-hint, .password-requirements {\n          font-size: var(--font-size-xs);\n          color: var(--color-textSecondary);\n          margin-top: var(--spacing-xs);\n        }\n        \n        .save-button {\n          padding: var(--spacing-sm) var(--spacing-md);\n          background-color: var(--color-primary);\n          color: white;\n          border: none;\n          border-radius: var(--border-radius-md);\n          font-size: var(--font-size-md);\n          font-weight: var(--font-weight-medium);\n          cursor: pointer;\n          transition: background-color var(--transition-fast) var(--transition-timing-function);\n          align-self: flex-start;\n        }\n        \n        .save-button:hover {\n          background-color: color-mix(in srgb, var(--color-primary) 80%, black);\n        }\n        \n        .save-button:disabled {\n          background-color: var(--color-border);\n          color: var(--color-textSecondary);\n          cursor: not-allowed;\n        }\n      "))},v=function(){var e=(0,o.As)().user,n=(0,r.useState)("profile"),a=(0,t.A)(n,2),i=a[0],c=a[1];return r.createElement("div",{className:"settings-container"},r.createElement("div",{className:"settings-header"},r.createElement("h1",null,"Settings"),r.createElement("p",{className:"settings-subtitle"},"Manage your account and application preferences")),r.createElement("div",{className:"settings-content"},r.createElement("div",{className:"settings-sidebar"},r.createElement("div",{className:"user-info"},r.createElement("div",{className:"user-avatar"},null!=e&&e.avatar?r.createElement("img",{src:e.avatar,alt:e.name||e.username}):r.createElement("div",{className:"avatar-placeholder"},((null==e?void 0:e.name)||(null==e?void 0:e.username)||"User").charAt(0).toUpperCase())),r.createElement("div",{className:"user-details"},r.createElement("div",{className:"user-name"},(null==e?void 0:e.name)||(null==e?void 0:e.username)||"User"),r.createElement("div",{className:"user-email"},(null==e?void 0:e.email)||"No email"))),r.createElement("nav",{className:"settings-nav"},r.createElement("ul",{className:"nav-list"},[{id:"profile",label:"Profile",icon:"👤"},{id:"appearance",label:"Appearance",icon:"🎨"},{id:"notifications",label:"Notifications",icon:"🔔"},{id:"security",label:"Security",icon:"🔒"},{id:"integrations",label:"Integrations",icon:"🔌"}].map((function(e){return r.createElement("li",{key:e.id,className:"nav-item"},r.createElement("button",{className:"nav-button ".concat(i===e.id?"active":""),onClick:function(){return c(e.id)}},r.createElement("span",{className:"nav-icon"},e.icon),r.createElement("span",{className:"nav-label"},e.label)))}))))),r.createElement("div",{className:"settings-main"},"profile"===i&&r.createElement("div",{className:"settings-tab"},r.createElement("h2",null,"Profile Settings"),r.createElement(g,null)),"appearance"===i&&r.createElement("div",{className:"settings-tab"},r.createElement("h2",null,"Appearance Settings"),r.createElement("div",{className:"appearance-settings"},r.createElement(s.rV,null))),"notifications"===i&&r.createElement("div",{className:"settings-tab"},r.createElement("h2",null,"Notification Settings"),r.createElement("div",{className:"notification-settings"},r.createElement("div",{className:"settings-section"},r.createElement("h3",null,"Email Notifications"),r.createElement("div",{className:"settings-options"},r.createElement("div",{className:"settings-option"},r.createElement("label",{className:"toggle-switch"},r.createElement("input",{type:"checkbox",defaultChecked:!0}),r.createElement("span",{className:"toggle-slider"})),r.createElement("div",{className:"option-details"},r.createElement("div",{className:"option-label"},"Project updates"),r.createElement("div",{className:"option-description"},"Receive emails about updates to your projects"))),r.createElement("div",{className:"settings-option"},r.createElement("label",{className:"toggle-switch"},r.createElement("input",{type:"checkbox",defaultChecked:!0}),r.createElement("span",{className:"toggle-slider"})),r.createElement("div",{className:"option-details"},r.createElement("div",{className:"option-label"},"Security alerts"),r.createElement("div",{className:"option-description"},"Receive emails about security issues"))),r.createElement("div",{className:"settings-option"},r.createElement("label",{className:"toggle-switch"},r.createElement("input",{type:"checkbox"}),r.createElement("span",{className:"toggle-slider"})),r.createElement("div",{className:"option-details"},r.createElement("div",{className:"option-label"},"Marketing emails"),r.createElement("div",{className:"option-description"},"Receive emails about new features and offers"))))),r.createElement("div",{className:"settings-section"},r.createElement("h3",null,"In-App Notifications"),r.createElement("div",{className:"settings-options"},r.createElement("div",{className:"settings-option"},r.createElement("label",{className:"toggle-switch"},r.createElement("input",{type:"checkbox",defaultChecked:!0}),r.createElement("span",{className:"toggle-slider"})),r.createElement("div",{className:"option-details"},r.createElement("div",{className:"option-label"},"Project comments"),r.createElement("div",{className:"option-description"},"Receive notifications when someone comments on your projects"))),r.createElement("div",{className:"settings-option"},r.createElement("label",{className:"toggle-switch"},r.createElement("input",{type:"checkbox",defaultChecked:!0}),r.createElement("span",{className:"toggle-slider"})),r.createElement("div",{className:"option-details"},r.createElement("div",{className:"option-label"},"Project shares"),r.createElement("div",{className:"option-description"},"Receive notifications when someone shares a project with you"))))))),"security"===i&&r.createElement("div",{className:"settings-tab"},r.createElement("h2",null,"Security Settings"),r.createElement("div",{className:"security-settings"},r.createElement("div",{className:"settings-section"},r.createElement("h3",null,"Two-Factor Authentication"),r.createElement("p",{className:"section-description"},"Add an extra layer of security to your account by requiring a verification code in addition to your password."),r.createElement("button",{className:"primary-button"},"Enable 2FA")),r.createElement("div",{className:"settings-section"},r.createElement("h3",null,"Session Management"),r.createElement("p",{className:"section-description"},"Manage your active sessions and sign out from other devices."),r.createElement("div",{className:"session-list"},r.createElement("div",{className:"session-item current"},r.createElement("div",{className:"session-details"},r.createElement("div",{className:"session-device"},"Current Browser (Chrome)"),r.createElement("div",{className:"session-info"},"Windows • Last active: Just now")),r.createElement("div",{className:"session-actions"},r.createElement("span",{className:"current-label"},"Current"))),r.createElement("div",{className:"session-item"},r.createElement("div",{className:"session-details"},r.createElement("div",{className:"session-device"},"Mobile App"),r.createElement("div",{className:"session-info"},"Android • Last active: 2 hours ago")),r.createElement("div",{className:"session-actions"},r.createElement("button",{className:"text-button"},"Sign Out")))),r.createElement("button",{className:"secondary-button"},"Sign Out All Other Devices")))),"integrations"===i&&r.createElement("div",{className:"settings-tab"},r.createElement("h2",null,"Integrations"),r.createElement("div",{className:"integrations-settings"},r.createElement("div",{className:"settings-section"},r.createElement("h3",null,"Connected Services"),r.createElement("p",{className:"section-description"},"Connect your account to other services to import or export data."),r.createElement("div",{className:"integration-list"},r.createElement("div",{className:"integration-item"},r.createElement("div",{className:"integration-icon"},r.createElement("span",{className:"github-icon"},"G")),r.createElement("div",{className:"integration-details"},r.createElement("div",{className:"integration-name"},"GitHub"),r.createElement("div",{className:"integration-status"},"Not connected")),r.createElement("div",{className:"integration-actions"},r.createElement("button",{className:"primary-button"},"Connect"))),r.createElement("div",{className:"integration-item"},r.createElement("div",{className:"integration-icon"},r.createElement("span",{className:"google-icon"},"G")),r.createElement("div",{className:"integration-details"},r.createElement("div",{className:"integration-name"},"Google Drive"),r.createElement("div",{className:"integration-status"},"Not connected")),r.createElement("div",{className:"integration-actions"},r.createElement("button",{className:"primary-button"},"Connect"))))),r.createElement("div",{className:"settings-section"},r.createElement("h3",null,"API Access"),r.createElement("p",{className:"section-description"},"Manage your API keys and access tokens."),r.createElement("button",{className:"primary-button"},"Generate API Key")))))),r.createElement("style",{jsx:!0},'\n        .settings-container {\n          padding: var(--spacing-lg);\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n        \n        .settings-header {\n          margin-bottom: var(--spacing-lg);\n        }\n        \n        .settings-subtitle {\n          color: var(--color-textSecondary);\n          margin-top: var(--spacing-xs);\n        }\n        \n        .settings-content {\n          display: grid;\n          grid-template-columns: 250px 1fr;\n          gap: var(--spacing-lg);\n        }\n        \n        .settings-sidebar {\n          background-color: var(--color-surface);\n          border-radius: var(--border-radius-md);\n          box-shadow: var(--shadow-sm);\n          overflow: hidden;\n        }\n        \n        .user-info {\n          display: flex;\n          align-items: center;\n          padding: var(--spacing-md);\n          border-bottom: 1px solid var(--color-border);\n        }\n        \n        .user-avatar {\n          width: 50px;\n          height: 50px;\n          border-radius: 50%;\n          overflow: hidden;\n          margin-right: var(--spacing-md);\n        }\n        \n        .user-avatar img {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n        }\n        \n        .avatar-placeholder {\n          width: 100%;\n          height: 100%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          background-color: var(--color-primary);\n          color: white;\n          font-size: var(--font-size-lg);\n          font-weight: var(--font-weight-bold);\n        }\n        \n        .user-details {\n          overflow: hidden;\n        }\n        \n        .user-name {\n          font-weight: var(--font-weight-medium);\n          margin-bottom: var(--spacing-xs);\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n        \n        .user-email {\n          font-size: var(--font-size-sm);\n          color: var(--color-textSecondary);\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n        \n        .settings-nav {\n          padding: var(--spacing-md);\n        }\n        \n        .nav-list {\n          list-style: none;\n          padding: 0;\n          margin: 0;\n        }\n        \n        .nav-item {\n          margin-bottom: var(--spacing-xs);\n        }\n        \n        .nav-button {\n          display: flex;\n          align-items: center;\n          width: 100%;\n          padding: var(--spacing-sm) var(--spacing-md);\n          background: none;\n          border: none;\n          border-radius: var(--border-radius-md);\n          color: var(--color-text);\n          cursor: pointer;\n          text-align: left;\n          transition: background-color var(--transition-fast) var(--transition-timing-function);\n        }\n        \n        .nav-button:hover {\n          background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);\n        }\n        \n        .nav-button.active {\n          background-color: color-mix(in srgb, var(--color-primary) 15%, transparent);\n          color: var(--color-primary);\n          font-weight: var(--font-weight-medium);\n        }\n        \n        .nav-icon {\n          margin-right: var(--spacing-sm);\n          font-size: var(--font-size-lg);\n        }\n        \n        .settings-main {\n          background-color: var(--color-surface);\n          border-radius: var(--border-radius-md);\n          box-shadow: var(--shadow-sm);\n          padding: var(--spacing-lg);\n        }\n        \n        .settings-tab h2 {\n          margin-top: 0;\n          margin-bottom: var(--spacing-lg);\n          padding-bottom: var(--spacing-sm);\n          border-bottom: 1px solid var(--color-border);\n        }\n        \n        .settings-section {\n          margin-bottom: var(--spacing-lg);\n        }\n        \n        .settings-section h3 {\n          margin-bottom: var(--spacing-sm);\n        }\n        \n        .section-description {\n          color: var(--color-textSecondary);\n          margin-bottom: var(--spacing-md);\n        }\n        \n        .settings-options {\n          display: flex;\n          flex-direction: column;\n          gap: var(--spacing-md);\n        }\n        \n        .settings-option {\n          display: flex;\n          align-items: flex-start;\n          gap: var(--spacing-md);\n        }\n        \n        .option-details {\n          flex: 1;\n        }\n        \n        .option-label {\n          font-weight: var(--font-weight-medium);\n          margin-bottom: var(--spacing-xs);\n        }\n        \n        .option-description {\n          font-size: var(--font-size-sm);\n          color: var(--color-textSecondary);\n        }\n        \n        .toggle-switch {\n          position: relative;\n          display: inline-block;\n          width: 40px;\n          height: 24px;\n        }\n        \n        .toggle-switch input {\n          opacity: 0;\n          width: 0;\n          height: 0;\n        }\n        \n        .toggle-slider {\n          position: absolute;\n          cursor: pointer;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background-color: var(--color-border);\n          transition: var(--transition-fast);\n          border-radius: 34px;\n        }\n        \n        .toggle-slider:before {\n          position: absolute;\n          content: "";\n          height: 16px;\n          width: 16px;\n          left: 4px;\n          bottom: 4px;\n          background-color: white;\n          transition: var(--transition-fast);\n          border-radius: 50%;\n        }\n        \n        input:checked + .toggle-slider {\n          background-color: var(--color-primary);\n        }\n        \n        input:checked + .toggle-slider:before {\n          transform: translateX(16px);\n        }\n        \n        .primary-button {\n          padding: var(--spacing-sm) var(--spacing-md);\n          background-color: var(--color-primary);\n          color: white;\n          border: none;\n          border-radius: var(--border-radius-md);\n          cursor: pointer;\n          transition: background-color var(--transition-fast) var(--transition-timing-function);\n        }\n        \n        .primary-button:hover {\n          background-color: color-mix(in srgb, var(--color-primary) 80%, black);\n        }\n        \n        .secondary-button {\n          padding: var(--spacing-sm) var(--spacing-md);\n          background-color: transparent;\n          color: var(--color-primary);\n          border: 1px solid var(--color-primary);\n          border-radius: var(--border-radius-md);\n          cursor: pointer;\n          transition: all var(--transition-fast) var(--transition-timing-function);\n        }\n        \n        .secondary-button:hover {\n          background-color: color-mix(in srgb, var(--color-primary) 10%, transparent);\n        }\n        \n        .text-button {\n          background: none;\n          border: none;\n          color: var(--color-primary);\n          cursor: pointer;\n          padding: 0;\n        }\n        \n        .text-button:hover {\n          text-decoration: underline;\n        }\n        \n        .session-list, .integration-list {\n          display: flex;\n          flex-direction: column;\n          gap: var(--spacing-md);\n          margin-bottom: var(--spacing-md);\n        }\n        \n        .session-item, .integration-item {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: var(--spacing-md);\n          border: 1px solid var(--color-border);\n          border-radius: var(--border-radius-md);\n        }\n        \n        .session-item.current {\n          border-color: var(--color-primary);\n          background-color: color-mix(in srgb, var(--color-primary) 5%, transparent);\n        }\n        \n        .session-device, .integration-name {\n          font-weight: var(--font-weight-medium);\n          margin-bottom: var(--spacing-xs);\n        }\n        \n        .session-info, .integration-status {\n          font-size: var(--font-size-sm);\n          color: var(--color-textSecondary);\n        }\n        \n        .current-label {\n          font-size: var(--font-size-xs);\n          color: var(--color-primary);\n          border: 1px solid var(--color-primary);\n          padding: 2px 8px;\n          border-radius: var(--border-radius-sm);\n        }\n        \n        .integration-icon {\n          width: 40px;\n          height: 40px;\n          border-radius: var(--border-radius-md);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          background-color: var(--color-border);\n          margin-right: var(--spacing-md);\n        }\n        \n        .github-icon, .google-icon {\n          font-weight: var(--font-weight-bold);\n        }\n        \n        @media (max-width: 768px) {\n          .settings-content {\n            grid-template-columns: 1fr;\n          }\n          \n          .settings-sidebar {\n            margin-bottom: var(--spacing-md);\n          }\n        }\n      '))}}}]);