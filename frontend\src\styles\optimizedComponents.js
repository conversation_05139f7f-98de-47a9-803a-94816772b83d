import React, { memo } from 'react';
import styled from 'styled-components';
import { shouldForwardProp } from 'styled-components';
// Optimized Ant Design imports for better tree-shaking
import { Button, Card, Input, Typography, Alert, Spin, Tag } from '../utils/optimizedAntdImports';
import * as S from './components';

/**
 * Optimized styled components for the application
 * These components are memoized and use shouldForwardProp to prevent
 * unnecessary re-renders and prop forwarding
 */

const { Title, Text, Paragraph } = Typography;

// Helper to filter out custom props
const customProps = ['flex', 'flexMd', 'flexLg', 'status', 'messageType'];
const shouldForwardCustomProp = prop => !customProps.includes(prop);

// Optimized Typography components
export const PageTitle = memo(styled(S.PageTitle).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const SectionTitle = memo(styled(S.SectionTitle).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const SubTitle = memo(styled(S.SubTitle).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

// Optimized Layout components
export const Container = memo(styled(S.Container).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const Section = memo(styled(S.Section).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const Row = memo(styled(S.Row).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const Column = memo(styled(S.Column).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const Grid = memo(styled(S.Grid).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

// Optimized Card components
export const StyledCard = memo(styled(S.StyledCard).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const FeatureCard = memo(styled(S.FeatureCard).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const DashboardCard = memo(styled(S.DashboardCard).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

// Optimized Button components
export const PrimaryButton = memo(styled(S.PrimaryButton).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const SecondaryButton = memo(styled(S.SecondaryButton).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const IconButton = memo(styled(S.IconButton).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

// Optimized Form components
export const FormGroup = memo(styled(S.FormGroup).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const StyledInput = memo(styled(S.StyledInput).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const StyledTextArea = memo(styled(S.StyledTextArea).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

// Optimized Alert components
export const StyledAlert = memo(styled(S.StyledAlert).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

// Optimized Loading components
export const LoadingContainer = memo(styled(S.LoadingContainer).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const StyledSpin = memo(styled(S.StyledSpin).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

// Optimized Tag components
export const StyledTag = memo(styled(S.StyledTag).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

export const StatusTag = memo(styled(S.StatusTag).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

// Optimized Divider
export const Divider = memo(styled(S.Divider).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

// Optimized Badge
export const Badge = memo(styled(S.Badge).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

// Optimized Avatar
export const Avatar = memo(styled(S.Avatar).withConfig({
  shouldForwardProp: shouldForwardCustomProp,
})`
  /* Additional styles can be added here */
`);

// Export all components
export default {
  PageTitle,
  SectionTitle,
  SubTitle,
  Container,
  Section,
  Row,
  Column,
  Grid,
  StyledCard,
  FeatureCard,
  DashboardCard,
  PrimaryButton,
  SecondaryButton,
  IconButton,
  FormGroup,
  StyledInput,
  StyledTextArea,
  StyledAlert,
  LoadingContainer,
  StyledSpin,
  StyledTag,
  StatusTag,
  Divider,
  Badge,
  Avatar,
};
