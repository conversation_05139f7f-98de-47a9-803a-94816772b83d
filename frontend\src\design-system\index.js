/**
 * Enhanced Design System Index
 *
 * This file exports all components, utilities, and theme tokens from the design system.
 * It provides a centralized access point for all design system resources.
 */

// Enhanced Theme
export { default as theme } from './theme';
export {
    colors,
    typography,
    spacing,
    shadows,
    borderRadius,
    transitions,
    zIndex,
    breakpoints,
    mediaQueries,
    accessibility,
    animations,
    components as componentTokens,
} from './theme';

// Visual Hierarchy
export {
    typographyHierarchy as visualHierarchy,
    spacingHierarchy,
    visualGrouping,
    interactionPatterns,
    informationArchitecture,
    hierarchyUtils,
} from './visual-hierarchy';

// Design System Utilities
export { default as designUtils } from './utils';
export {
    colorUtils,
    spacingUtils,
    typographyUtils,
    a11yUtils,
    animationUtils,
    componentUtils,
    responsiveUtils,
} from './utils';

// Base Components
export { default as Button } from './Button';
export { default as Card } from './Card';
export { default as Input } from './Input';
export { default as Select } from './Select';

// Text Components
export { default as Text } from './components/Text';

// Re-export styled-components for convenience
export {
    default as styled,
    css,
    keyframes,
    ThemeProvider,
    createGlobalStyle
} from './styled-components';

// Global styles (import this in your app)
export const globalStyles = './global.css';

// Design system version
export const VERSION = '2.0.0';

// Design system configuration
export const config = {
    prefix: 'ds-', // CSS class prefix for design system components
    enableRTL: false, // Right-to-left language support
    enableDarkMode: true, // Dark mode support
    enableHighContrast: true, // High contrast mode support
    enableReducedMotion: true, // Reduced motion support
};
