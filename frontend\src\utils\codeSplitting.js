import React, { lazy, Suspense } from 'react';
import ErrorBoundary from '../components/ErrorBoundary';

// This component is exported for use in other parts of the application
export const DefaultLoading = ({ text = 'Loading...' }) => (
  <div className="default-loading">
    <div className="loading-spinner"></div>
    <p>{text}</p>
  </div>
);

// Enhanced lazy loading with custom fallback and error boundary
export function lazyWithFallback(importFn, options = {}) {
  const {
    fallbackText = 'Loading...',
    fallbackDescription = '',
    size = 'medium',
    fullPage = false,
    timeout = 10000
  } = options;

  const LazyComponent = lazy(() => {
    return Promise.race([
      importFn(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Loading timeout')), timeout)
      )
    ]);
  });

  return (props) => (
    <ErrorBoundary>
      <Suspense fallback={
        <div className={`loading-fallback ${fullPage ? 'full-page' : ''} size-${size}`}>
          <div className="loading-spinner"></div>
          <h3>{fallbackText}</h3>
          {fallbackDescription && <p>{fallbackDescription}</p>}
        </div>
      }>
        <LazyComponent {...props} />
      </Suspense>
    </ErrorBoundary>
  );
}

// Lazy load with delay (prevents flashing for fast connections)
export function lazyWithDelay(importFn, minDelay = 300) {
  return lazy(() => {
    const start = Date.now();
    return importFn().then(module => {
      const delta = Date.now() - start;
      if (delta < minDelay) {
        return new Promise(resolve => {
          setTimeout(() => resolve(module), minDelay - delta);
        });
      }
      return module;
    });
  });
}

// Lazy load on interaction (for non-critical components)
export function lazyOnInteraction(importFn, options = {}) {
  const {
    placeholder = <div className="interaction-placeholder"><p>Click to load component</p></div>,
    loadOnHover = true,
    loadOnFocus = true,
    loadOnClick = true
  } = options;

  let Component = null;
  let importPromise = null;

  const LazyComponent = props => {
    const [loaded, setLoaded] = React.useState(false);
    const [isLoading, setIsLoading] = React.useState(false);

    React.useEffect(() => {
      if (loaded && !Component && !importPromise) {
        setIsLoading(true);
        importPromise = importFn().then(module => {
          Component = module.default;
          setIsLoading(false);
          setLoaded(true);
        }).catch(error => {
          console.error('Failed to load component:', error);
          setIsLoading(false);
        });
      }
    }, [loaded]);

    const handleInteraction = () => {
      if (!loaded && !isLoading) {
        setLoaded(true);
      }
    };

    if (Component) {
      return (
        <ErrorBoundary>
          <Component {...props} />
        </ErrorBoundary>
      );
    }

    if (isLoading) {
      return <DefaultLoading text="Loading component..." />;
    }

    return (
      <div
        className="interaction-placeholder"
        onClick={loadOnClick ? handleInteraction : undefined}
        onMouseEnter={loadOnHover ? handleInteraction : undefined}
        onFocus={loadOnFocus ? handleInteraction : undefined}
        tabIndex={loadOnFocus ? 0 : undefined}
        role="button"
        aria-label="Load component"
      >
        {placeholder}
      </div>
    );
  };

  return LazyComponent;
}

