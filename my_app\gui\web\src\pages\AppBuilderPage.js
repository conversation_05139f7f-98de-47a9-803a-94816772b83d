import React, { useCallback, Suspense } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Row, Col, Button, Input, Form } from 'antd';
import * as actions from '../redux/actions';

const AppBuilderPage = () => {
    const dispatch = useDispatch();
    const components = useSelector(state => state.app?.components || []);
    const layouts = useSelector(state => state.app?.layouts || []);
    const styles = useSelector(state => state.app?.styles || {});
    const data = useSelector(state => state.app?.data || {});

    const handleAddComponent = useCallback((type, props = {}) => {
        dispatch(actions.addComponent(type, props));
    }, [dispatch]);

    const handleAddLayout = useCallback((type, components = [], styles = {}) => {
        dispatch(actions.addLayout(type, components, styles));
    }, [dispatch]);

    const handleAddStyle = useCallback((selector, style) => {
        dispatch(actions.addStyle(selector, style));
    }, [dispatch]);

    const handleAddData = useCallback((key, value) => {
        dispatch(actions.addData(key, value));
    }, [dispatch]);

    return (
        <div>
            <Row gutter={[16, 16]}>
                <Col span={6}>
                    <h2>Components</h2>
                    <Suspense fallback={<div>Loading...</div>}>
                        {components.map((component) => (
                            <div
                                key={component.id || `component-${component.type}-${JSON.stringify(component.props).slice(0, 20)}`}
                                onClick={() => handleComponentClick(component)}
                            >
                                {component.type}
                                {Object.keys(component.props).length > 0 && (
                                    <span> - {JSON.stringify(component.props)}</span>
                                )}
                            </div>
                        ))}
                    </Suspense>
                    <Button type="primary" onClick={() => handleAddComponent('Button')}>Add Button</Button>
                    <Button type="primary" onClick={() => handleAddComponent('Input')}>Add Input</Button>
                    <Button type="primary" onClick={() => handleAddComponent('Image')}>Add Image</Button>
                    <Button type="primary" onClick={() => handleAddComponent('Text')}>Add Text</Button>
                </Col>
                <Col span={18}>
                    <h2>Layouts</h2>
                    <Suspense fallback={<div>Loading...</div>}>
                        {layouts.map((layout) => (
                            <div key={layout.id || `layout-${layout.type}-${JSON.stringify(layout.styles).slice(0, 20)}`}>
                                {layout.type}
                                {layout.components.length > 0 && (
                                    <span> - Components: {layout.components.join(', ')}</span>
                                )}
                                {Object.keys(layout.styles).length > 0 && (
                                    <span> - Styles: {JSON.stringify(layout.styles)}</span>
                                )}
                            </div>
                        ))}
                    </Suspense>
                    <Button type="primary" onClick={() => handleAddLayout('Grid')}>Add Grid Layout</Button>
                    <Button type="primary" onClick={() => handleAddLayout('Flex')}>Add Flex Layout</Button>
                    <Button type="primary" onClick={() => handleAddLayout('Stack')}>Add Stack Layout</Button>
                    <br />
                    <br />
                    <h2>Styles</h2>
                    {Object.entries(styles).map(([selector, style]) => (
                        <div key={selector}>
                            {selector}: {JSON.stringify(style)}
                        </div>
                    ))}
                    <Button type="primary" onClick={() => handleAddStyle('.container', { display: 'flex' })}>Add Container Style</Button>
                    <br />
                    <br />
                    <h2>Data</h2>
                    {Object.entries(data).map(([key, value]) => (
                        <div key={key}>
                            {key}: {value}
                        </div>
                    ))}
                    <Form layout="inline" onFinish={(values) => handleAddData(values.key, values.value)}>
                        <Form.Item label="Key" name="key" rules={[{ required: true, message: 'Please enter a key' }]}>
                            <Input />
                        </Form.Item>
                        <Form.Item label="Value" name="value" rules={[{ required: true, message: 'Please enter a value' }]}>
                            <Input />
                        </Form.Item>
                        <Form.Item>
                            <Button type="primary" htmlType="submit">Add Data</Button>
                        </Form.Item>
                    </Form>
                </Col>
            </Row>
            <Button type="primary" onClick={handleSave}>Save</Button>
            <Button type="primary" onClick={() => setIsImageModalVisible(true)}>
                Generate Image
            </Button>
            {/* Modal for Image Generation */}
            <Modal
                title="Generate Image"
                visible={isImageModalVisible}
                onCancel={handleImageModalClose}
                footer={null}
            >
                <Input.TextArea
                    placeholder="Enter a description for the image you want to generate"
                    value={imagePrompt}
                    onChange={(e) => setImagePrompt(e.target.value)}
                />
                <Button type="primary" onClick={handleGenerateImage}>
                    Generate
                </Button>
                {generatedImage && (
                    <img src={generatedImage} alt="Generated Image" style={{ maxWidth: '100%', marginTop: 16 }} />
                )}
            </Modal>
        </div>
    );
};

export default AppBuilderPage;
