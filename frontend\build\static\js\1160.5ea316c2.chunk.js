"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[1160],{61160:(e,r,t)=>{t.r(r),t.d(r,{default:()=>w});var n,a,l,o,c,s=t(57528),d=t(96540),i=t(1807),m=t(82569),u=t(57683),g=t(70572),E=t(35346),p=i.o5.Title,b=i.o5.Paragraph,v=i.o5.Text,y=g.Ay.div(n||(n=(0,s.A)(["\n  padding: var(--spacing-lg);\n  background-color: var(--color-background);\n  min-height: 100vh;\n  transition: all 0.3s ease;\n"]))),h=(0,g.Ay)(i.Zp)(a||(a=(0,s.A)(["\n  margin-bottom: var(--spacing-lg);\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  .ant-card-head {\n    background-color: var(--color-background-secondary);\n    border-bottom: 1px solid var(--color-border-light);\n  }\n\n  .ant-card-head-title {\n    color: var(--color-text);\n  }\n"]))),x=g.Ay.div(l||(l=(0,s.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: var(--spacing-md);\n  margin: var(--spacing-md) 0;\n"]))),k=g.Ay.div(o||(o=(0,s.A)(["\n  padding: var(--spacing-md);\n  border-radius: var(--border-radius-md);\n  border: 1px solid var(--color-border);\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-md);\n  }\n"]))),f=g.Ay.div(c||(c=(0,s.A)(["\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--border-radius-sm);\n  font-size: 12px;\n  font-weight: 500;\n  margin: var(--spacing-xs);\n"])));const w=function(){var e=(0,m.ZV)(),r=e.isDarkMode,t=e.colors,n=e.themeMode;return d.createElement(y,null,d.createElement(h,{title:"Text Contrast & Visibility Test"},d.createElement("div",{style:{marginBottom:"24px",textAlign:"center"}},d.createElement(p,{level:3},"Current Theme: ",n," mode"),d.createElement(u.A,null)),d.createElement(i.cG,null,"Typography Hierarchy"),d.createElement(i.$x,{direction:"vertical",size:"large",style:{width:"100%"}},d.createElement("div",null,d.createElement(p,{level:1,style:{color:"var(--color-text)"}},"Heading Level 1"),d.createElement(p,{level:2,style:{color:"var(--color-text)"}},"Heading Level 2"),d.createElement(p,{level:3,style:{color:"var(--color-text)"}},"Heading Level 3"),d.createElement(p,{level:4,style:{color:"var(--color-text)"}},"Heading Level 4"),d.createElement(p,{level:5,style:{color:"var(--color-text)"}},"Heading Level 5")),d.createElement("div",null,d.createElement(b,{style:{color:"var(--color-text)"}},"This is a regular paragraph with normal text color. It should be easily readable against the current background in both light and dark modes."),d.createElement(v,{type:"secondary"},"This is secondary text that should have good contrast."),d.createElement("br",null),d.createElement(v,{type:"success"},"Success text should be visible and accessible."),d.createElement("br",null),d.createElement(v,{type:"warning"},"Warning text should stand out appropriately."),d.createElement("br",null),d.createElement(v,{type:"danger"},"Error text should be clearly visible."),d.createElement("br",null),d.createElement(v,{disabled:!0},"Disabled text should be distinguishable but subdued."))),d.createElement(i.cG,null,"Status Indicators"),d.createElement("div",null,[{type:"success",color:"#52c41a",bg:"rgba(82, 196, 26, 0.1)",text:"Success Status"},{type:"warning",color:"#faad14",bg:"rgba(250, 173, 20, 0.1)",text:"Warning Status"},{type:"error",color:"#ff4d4f",bg:"rgba(255, 77, 79, 0.1)",text:"Error Status"},{type:"info",color:"#1890ff",bg:"rgba(24, 144, 255, 0.1)",text:"Info Status"}].map((function(e,r){return d.createElement(f,{key:r,style:{color:e.color,backgroundColor:e.bg,border:"1px solid ".concat(e.color)}},"success"===e.type&&d.createElement(E.hWy,null),"warning"===e.type&&d.createElement(E.G2i,null),"error"===e.type&&d.createElement(E.bBN,null),"info"===e.type&&d.createElement(E.rUN,null),e.text)}))),d.createElement(i.cG,null,"Interactive Elements"),d.createElement(i.$x,{wrap:!0},d.createElement(i.$n,{type:"primary"},"Primary Button"),d.createElement(i.$n,{type:"default"},"Default Button"),d.createElement(i.$n,{type:"dashed"},"Dashed Button"),d.createElement(i.$n,{type:"text"},"Text Button"),d.createElement(i.$n,{type:"link"},"Link Button"),d.createElement(i.$n,{danger:!0},"Danger Button")),d.createElement(i.cG,null,"Tags and Badges"),d.createElement(i.$x,{wrap:!0},d.createElement(i.vw,{color:"blue"},"Blue Tag"),d.createElement(i.vw,{color:"green"},"Green Tag"),d.createElement(i.vw,{color:"orange"},"Orange Tag"),d.createElement(i.vw,{color:"red"},"Red Tag"),d.createElement(i.vw,{color:"purple"},"Purple Tag"),d.createElement(i.Ex,{count:5,style:{backgroundColor:"#52c41a"}},d.createElement("div",{style:{width:40,height:40,backgroundColor:"var(--color-background-secondary)",border:"1px solid var(--color-border)",borderRadius:"4px"}}))),d.createElement(i.cG,null,"Alerts"),d.createElement(i.$x,{direction:"vertical",style:{width:"100%"}},d.createElement(i.Fc,{message:"Success Alert",type:"success",showIcon:!0}),d.createElement(i.Fc,{message:"Info Alert",type:"info",showIcon:!0}),d.createElement(i.Fc,{message:"Warning Alert",type:"warning",showIcon:!0}),d.createElement(i.Fc,{message:"Error Alert",type:"error",showIcon:!0})),d.createElement(i.cG,null,"Progress Indicators"),d.createElement(i.$x,{direction:"vertical",style:{width:"100%"}},d.createElement(i.ke,{percent:30,status:"active"}),d.createElement(i.ke,{percent:50,status:"normal"}),d.createElement(i.ke,{percent:70,status:"exception"}),d.createElement(i.ke,{percent:100})),d.createElement(i.cG,null,"Background Variations"),d.createElement(x,null,d.createElement(k,{style:{backgroundColor:"var(--color-surface)"}},d.createElement(v,{style:{color:"var(--color-text)"}},"Surface Background")),d.createElement(k,{style:{backgroundColor:"var(--color-background-secondary)"}},d.createElement(v,{style:{color:"var(--color-text)"}},"Secondary Background")),d.createElement(k,{style:{backgroundColor:"var(--color-background-tertiary)"}},d.createElement(v,{style:{color:"var(--color-text)"}},"Tertiary Background")),d.createElement(k,{style:{backgroundColor:"var(--color-primary)",color:"white"}},d.createElement(v,{style:{color:"white"}},"Primary Background"))),d.createElement(i.cG,null,"Theme Information"),d.createElement("div",{style:{backgroundColor:"var(--color-background-secondary)",padding:"16px",borderRadius:"8px",border:"1px solid var(--color-border-light)"}},d.createElement(v,{style:{color:"var(--color-text)"}},d.createElement("strong",null,"Current Theme:")," ",r?"Dark":"Light"," Mode",d.createElement("br",null),d.createElement("strong",null,"Theme Mode Setting:")," ",n,d.createElement("br",null),d.createElement("strong",null,"Primary Color:")," ",t.primary,d.createElement("br",null),d.createElement("strong",null,"Background Color:")," ",t.background,d.createElement("br",null),d.createElement("strong",null,"Text Color:")," ",t.text))))}}}]);