"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[6548],{36548:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var n=r(10467),a=r(5544),l=r(54756),s=r.n(l),i=r(96540),o=r(1807),c=r(35346),m=r(11080),u=o.o5.Title,d=o.o5.Text;const p=function(){var e=(0,i.useState)(!1),t=(0,a.A)(e,2),r=t[0],l=t[1],p=o.lV.useForm(),y=(0,a.A)(p,1)[0],f=function(){var e=(0,n.A)(s().mark((function e(t){return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,l(!0),e.next=4,new Promise((function(e){return setTimeout(e,1e3)}));case 4:o.iU.success("Password reset link has been sent to your email"),y.resetFields(),e.next=12;break;case 8:e.prev=8,e.t0=e.catch(0),o.iU.error("Failed to send password reset link"),console.error("Error sending password reset link:",e.t0);case 12:return e.prev=12,l(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,8,12,15]])})));return function(t){return e.apply(this,arguments)}}();return i.createElement("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"100vh",padding:"20px"}},i.createElement(o.Zp,{style:{width:"100%",maxWidth:400}},i.createElement("div",{style:{textAlign:"center",marginBottom:24}},i.createElement(u,{level:3},"Forgot Password"),i.createElement(d,{type:"secondary"},"Enter your email address and we'll send you a link to reset your password")),i.createElement(o.lV,{form:y,layout:"vertical",onFinish:f},i.createElement(o.lV.Item,{name:"email",rules:[{required:!0,message:"Please enter your email"},{type:"email",message:"Please enter a valid email"}]},i.createElement(o.pd,{prefix:i.createElement(c._Wu,null),placeholder:"Email",size:"large"})),i.createElement(o.lV.Item,null,i.createElement(o.$n,{type:"primary",htmlType:"submit",loading:r,block:!0,size:"large"},"Send Reset Link")),i.createElement("div",{style:{textAlign:"center"}},i.createElement(m.N_,{to:"/login"},"Back to Login")))))}}}]);