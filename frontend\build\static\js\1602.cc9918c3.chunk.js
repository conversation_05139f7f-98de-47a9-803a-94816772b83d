"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[1602],{71602:(e,t,n)=>{n.r(t),n.d(t,{default:()=>U});var r,a,o=n(64467),c=n(60436),i=n(5544),l=n(57528),s=n(96540),m=n(71468),u=n(85331),d=n(1807),p=n(35346),g=n(70572),v=n(57749),f=(g.Ay.div(r||(r=(0,l.A)(["\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n"]))),s.createContext({announce:function(){},announcePolite:function(){},announceAssertive:function(){}}));(0,g.DU)(a||(a=(0,l.A)(["\n  /* Base focus styles */\n  :focus {\n    outline: none;\n  }\n\n  /* Focus visible styles for keyboard navigation */\n  :focus-visible {\n    outline: 2px solid ",";\n    outline-offset: 2px;\n    box-shadow: 0 0 0 4px ",";\n  }\n\n  /* Specific focus styles for different elements */\n  a:focus-visible,\n  button:focus-visible,\n  input:focus-visible,\n  textarea:focus-visible,\n  select:focus-visible,\n  [tabindex]:focus-visible {\n    outline: 2px solid ",";\n    outline-offset: 2px;\n    box-shadow: 0 0 0 4px ",";\n  }\n\n  /* High contrast mode focus styles */\n  @media (forced-colors: active) {\n    :focus-visible {\n      outline: 3px solid CanvasText;\n      outline-offset: 3px;\n    }\n  }\n"])),(function(e){var t,n,r;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.primary?e.theme.colorPalette.primary:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.primary)&&void 0!==n&&n.main?e.theme.colors.primary.main:null!==(r=e.theme)&&void 0!==r&&r.primaryColor?e.theme.primaryColor:"#2563EB"}),(function(e){var t,n;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.primaryLight?e.theme.colorPalette.primaryLight:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.primary)&&void 0!==n&&n.light?e.theme.colors.primary.light:"rgba(37, 99, 235, 0.2)"}),(function(e){var t,n,r;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.primary?e.theme.colorPalette.primary:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.primary)&&void 0!==n&&n.main?e.theme.colors.primary.main:null!==(r=e.theme)&&void 0!==r&&r.primaryColor?e.theme.primaryColor:"#2563EB"}),(function(e){var t,n;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.primaryLight?e.theme.colorPalette.primaryLight:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.primary)&&void 0!==n&&n.light?e.theme.colors.primary.light:"rgba(37, 99, 235, 0.2)"}));var y,E=n(58168),h=n(53986),b=["children","as"],S=g.Ay.span(y||(y=(0,l.A)(["\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n"])));const x=function(e){var t=e.children,n=e.as,r=void 0===n?"span":n,a=(0,h.A)(e,b);return s.createElement(S,(0,E.A)({as:r},a),t)};n(76413);var C,w,O,A,I,k,N=n(26390),D=n(17177);function P(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function R(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?P(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):P(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var T=d.o5.Paragraph,W=d.o5.Text,L=d.tU.TabPane,M=g.Ay.div(C||(C=(0,l.A)(["\n  height: 300px;\n  overflow-y: auto;\n  margin-bottom: 16px;\n  border: 1px solid ",";\n  padding: 8px;\n  border-radius: ",";\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.border)||"#D1D5DB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.borderRadius)||void 0===t?void 0:t.md)||"4px"})),j=(0,g.Ay)(d.B8.Item)(w||(w=(0,l.A)(["\n  padding: 8px;\n  background-color: ",";\n  margin-bottom: 8px;\n  border-radius: ",";\n"])),(function(e){var t,n,r,a;switch(e.messageType){case"sent":return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.infoLight)||"#DBEAFE";case"received":return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.successLight)||"#D1FAE5";case"error":return(null===(r=e.theme)||void 0===r||null===(r=r.colorPalette)||void 0===r?void 0:r.errorLight)||"#FEE2E2";default:return(null===(a=e.theme)||void 0===a||null===(a=a.colorPalette)||void 0===a?void 0:a.warningLight)||"#FEF3C7"}}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.borderRadius)||void 0===t?void 0:t.md)||"4px"})),B=g.Ay.div(O||(O=(0,l.A)(["\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 4px;\n"]))),F=g.Ay.pre(A||(A=(0,l.A)(["\n  margin: 0;\n  white-space: pre-wrap;\n  word-break: break-word;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n"]))),G=g.Ay.div(I||(I=(0,l.A)(["\n  text-align: center;\n  color: ",";\n  margin-top: 120px;\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.textSecondary)||"#4B5563"})),z=g.Ay.div(k||(k=(0,l.A)(["\n  margin-bottom: 16px;\n"])));const U=function(){var e=(0,m.wA)(),t=s.useContext(f).announce,n=(0,s.useState)((0,N.$0)("test")),r=(0,i.A)(n,2),a=r[0],o=r[1],l=(0,s.useState)(!1),g=(0,i.A)(l,2),y=(g[0],g[1]),E=(0,s.useState)(D.ConnectionState.CLOSED),h=(0,i.A)(E,2),b=h[0],S=h[1],C=(0,s.useState)([]),w=(0,i.A)(C,2),O=w[0],A=w[1],I=(0,s.useState)(""),k=(0,i.A)(I,2),P=k[0],U=k[1],J=(0,s.useState)("1"),Y=(0,i.A)(J,2),K=Y[0],$=Y[1],_=(0,s.useState)(!1),H=(0,i.A)(_,2),Q=H[0],q=H[1],V=(0,s.useRef)(null),X=(0,s.useState)({autoReconnect:!0,debug:!0,batchInterval:50,maxBatchSize:100,heartbeatInterval:3e4,enableCompression:!0,persistOfflineMessages:!0,reconnectInterval:3e3,maxReconnectAttempts:10}),Z=(0,i.A)(X,2),ee=Z[0],te=Z[1];(0,s.useEffect)((function(){return e((0,u.tI)("websocket")),function(){V.current&&(V.current.destroy(),V.current=null)}}),[e]);var ne=function(){if(V.current&&V.current.connectionState===D.ConnectionState.OPEN&&P.trim())try{var e;try{e=JSON.parse(P)}catch(t){e=P}V.current.send(e),A((function(t){return[].concat((0,c.A)(t),[{type:"sent",text:"string"==typeof e?e:JSON.stringify(e,null,2),timestamp:new Date}])})),U(""),t("Message sent")}catch(e){t("Failed to send message: ".concat(e.message),"assertive"),console.error("Error sending message:",e)}else t("Cannot send message: WebSocket is not connected","assertive")};return s.createElement(v.mc,null,s.createElement(v.sT,{level:2},s.createElement(p._bA,{style:{marginRight:"8px"}}),"WebSocket Manager"),s.createElement(d.tU,{activeKey:K,onChange:$},s.createElement(L,{tab:"Messages",key:"1"},s.createElement(v.ee,{title:s.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},s.createElement("span",null,"Enhanced WebSocket Connection"),s.createElement(d.Ex,{status:b===D.ConnectionState.OPEN?"success":b===D.ConnectionState.CONNECTING?"processing":b===D.ConnectionState.RECONNECTING||b===D.ConnectionState.CLOSING?"warning":"error",text:b===D.ConnectionState.OPEN?"Connected":b===D.ConnectionState.CONNECTING?"Connecting":b===D.ConnectionState.RECONNECTING?"Reconnecting":b===D.ConnectionState.CLOSING?"Closing":"Disconnected"}),s.createElement(x,null,b===D.ConnectionState.OPEN?"WebSocket is connected":b===D.ConnectionState.CONNECTING?"WebSocket is connecting":b===D.ConnectionState.RECONNECTING?"WebSocket is reconnecting":b===D.ConnectionState.CLOSING?"WebSocket is closing":"WebSocket is disconnected")),extra:s.createElement(d.$x,null,s.createElement(v.jn,{type:"primary",onClick:function(){V.current&&V.current.destroy(),A((function(e){return[].concat((0,c.A)(e),[{type:"system",text:"Attempting to connect to ".concat(a),timestamp:new Date}])})),t("Attempting to connect to ".concat(a)),q(!0);try{V.current=new D.default({url:a,autoConnect:!1,debug:ee.debug,autoReconnect:ee.autoReconnect,reconnectInterval:ee.reconnectInterval,maxReconnectAttempts:ee.maxReconnectAttempts,batchInterval:ee.batchInterval,maxBatchSize:ee.maxBatchSize,heartbeatInterval:ee.heartbeatInterval,enableCompression:ee.enableCompression,persistOfflineMessages:ee.persistOfflineMessages}),V.current.addEventListener("open",(function(e){y(!0),S(D.ConnectionState.OPEN),q(!1);var n="Connected to WebSocket server at ".concat(a);A((function(e){return[].concat((0,c.A)(e),[{type:"system",text:n,timestamp:new Date}])})),t("WebSocket connection established"),setTimeout((function(){if(V.current&&V.current.connectionState===D.ConnectionState.OPEN)try{var e={type:"ping",timestamp:(new Date).toISOString(),client:"EnhancedWebSocketPage"};V.current.send(e,!0,{urgent:!0}),A((function(t){return[].concat((0,c.A)(t),[{type:"sent",text:JSON.stringify(e,null,2),timestamp:new Date,isTest:!0}])})),t("Sent automatic test message")}catch(e){console.error("Failed to send test message:",e)}}),1e3)})),V.current.addEventListener("message",(function(e){try{var n,r=e.data;n="string"==typeof r?r:JSON.stringify(r,null,2),A((function(t){var r;return[].concat((0,c.A)(t),[{type:"received",text:n,timestamp:new Date,raw:null===(r=e.originalEvent)||void 0===r?void 0:r.data}])})),t("Message received from server")}catch(n){A((function(t){return[].concat((0,c.A)(t),[{type:"received",text:String(e.data),timestamp:new Date,parseError:n.message}])})),t("Message received but could not be processed")}})),V.current.addEventListener("close",(function(e){y(!1),S(D.ConnectionState.CLOSED),q(!1);var n="Disconnected from server: ".concat(e.reason||"Connection closed"," (code: ").concat(e.code,")");A((function(t){return[].concat((0,c.A)(t),[{type:"system",text:n,timestamp:new Date,wasClean:e.wasClean,code:e.code}])})),t("WebSocket connection closed")})),V.current.addEventListener("error",(function(e){q(!1);var n="WebSocket error: ".concat(e.message||"Unknown error");A((function(t){return[].concat((0,c.A)(t),[{type:"error",text:n,timestamp:new Date,error:e}])})),t("WebSocket error occurred","assertive"),console.error("WebSocket connection error:",{url:a,error:e,connectionState:e.connectionState,timestamp:(new Date).toISOString()})})),V.current.addEventListener("reconnect_attempt",(function(e){S(D.ConnectionState.RECONNECTING);var n="Reconnecting to WebSocket server (attempt ".concat(e.attempt,")...");A((function(e){return[].concat((0,c.A)(e),[{type:"system",text:n,timestamp:new Date}])})),t(n)})),V.current.open(),S(D.ConnectionState.CONNECTING)}catch(e){q(!1),A((function(t){return[].concat((0,c.A)(t),[{type:"error",text:"Failed to connect: ".concat(e.message),timestamp:new Date,stack:e.stack}])})),t("Connection error: ".concat(e.message)),console.error("Failed to create WebSocket:",e)}},disabled:b!==D.ConnectionState.CLOSED,loading:Q,icon:s.createElement(p.KF4,null),"aria-label":"Connect to WebSocket"},"Connect"),s.createElement(v.tA,{danger:!0,onClick:function(){V.current&&(V.current.close(),S(D.ConnectionState.CLOSING),t("Disconnecting from WebSocket server"))},disabled:b===D.ConnectionState.CLOSED||b===D.ConnectionState.CLOSING,"aria-label":"Disconnect from WebSocket"},"Disconnect"),s.createElement(v.K0,{onClick:function(){A([]),t("Messages cleared")},icon:s.createElement(p.ohj,null),"aria-label":"Clear all messages"},"Clear"))},s.createElement(v.gE,null,s.createElement(v.sQ,{value:a,onChange:function(e){return o(e.target.value)},placeholder:"WebSocket URL",addonBefore:"URL"})),s.createElement(M,null,0===O.length?s.createElement(G,null,s.createElement(W,{type:"secondary"},"No messages yet")):s.createElement(d.B8,{dataSource:O,"aria-label":"WebSocket messages",renderItem:function(e){return s.createElement(j,{messageType:e.type},s.createElement("div",{style:{width:"100%"}},s.createElement(B,null,s.createElement(W,{strong:!0},"sent"===e.type?"Sent":"received"===e.type?"Received":"error"===e.type?"Error":"System"),s.createElement(W,{type:"secondary"},(t=e.timestamp,new Date(t).toLocaleTimeString()))),s.createElement(F,null,e.text)));var t}})),s.createElement(v.gE,null,s.createElement(v.aQ,{value:P,onChange:function(e){return U(e.target.value)},placeholder:"Enter message to send (JSON or plain text)",rows:4,disabled:b!==D.ConnectionState.OPEN,onPressEnter:function(e){e.shiftKey||(e.preventDefault(),ne())}})),s.createElement(d.$x,null,s.createElement(v.jn,{type:"primary",onClick:ne,disabled:b!==D.ConnectionState.OPEN||!P.trim(),icon:s.createElement(p.jnF,null),"aria-label":"Send message"},"Send Message"),s.createElement(d.m_,{title:"Send a test ping message"},s.createElement(v.tA,{onClick:function(){if(V.current&&V.current.connectionState===D.ConnectionState.OPEN)try{var e={type:"ping",timestamp:(new Date).toISOString(),client:"EnhancedWebSocketPage"};V.current.send(e,!0,{urgent:!0}),A((function(t){return[].concat((0,c.A)(t),[{type:"sent",text:JSON.stringify(e,null,2),timestamp:new Date,isTest:!0}])})),t("Sent test ping message")}catch(e){console.error("Failed to send test message:",e)}},disabled:b!==D.ConnectionState.OPEN,icon:s.createElement(p.rUN,null),"aria-label":"Send test ping message"},"Send Ping"))))),s.createElement(L,{tab:"Settings",key:"2"},s.createElement(v.ee,{title:"Enhanced WebSocket Settings"},s.createElement(z,null,s.createElement(W,{strong:!0},"Auto Reconnect"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(d.dO,{checked:ee.autoReconnect,onChange:function(e){return te(R(R({},ee),{},{autoReconnect:e}))},style:{marginRight:"8px"}}),s.createElement(W,{type:"secondary"},ee.autoReconnect?"Enabled - Will automatically try to reconnect when disconnected":"Disabled - Will not automatically reconnect"))),s.createElement(z,null,s.createElement(W,{strong:!0},"Debug Mode"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(d.dO,{checked:ee.debug,onChange:function(e){return te(R(R({},ee),{},{debug:e}))},style:{marginRight:"8px"}}),s.createElement(W,{type:"secondary"},ee.debug?"Enabled - Detailed logs will be shown in the console":"Disabled - Minimal logging"))),s.createElement(z,null,s.createElement(W,{strong:!0},"Persist Offline Messages"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(d.dO,{checked:ee.persistOfflineMessages,onChange:function(e){return te(R(R({},ee),{},{persistOfflineMessages:e}))},style:{marginRight:"8px"}}),s.createElement(W,{type:"secondary"},ee.persistOfflineMessages?"Enabled - Messages will be saved when offline":"Disabled - Messages may be lost when offline"))),s.createElement(z,null,s.createElement(W,{strong:!0},"Enable Compression"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(d.dO,{checked:ee.enableCompression,onChange:function(e){return te(R(R({},ee),{},{enableCompression:e}))},style:{marginRight:"8px"}}),s.createElement(W,{type:"secondary"},ee.enableCompression?"Enabled - Large messages will be compressed":"Disabled - No compression"))),s.createElement(z,null,s.createElement(W,{strong:!0},"Reconnect Interval (ms)"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(d.YI,{min:1e3,max:1e4,step:500,value:ee.reconnectInterval,onChange:function(e){return te(R(R({},ee),{},{reconnectInterval:e}))},style:{marginRight:"8px"}}),s.createElement(W,{type:"secondary"},"Time to wait before attempting to reconnect"))),s.createElement(z,null,s.createElement(W,{strong:!0},"Max Reconnect Attempts"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(d.YI,{min:1,max:50,value:ee.maxReconnectAttempts,onChange:function(e){return te(R(R({},ee),{},{maxReconnectAttempts:e}))},style:{marginRight:"8px"}}),s.createElement(W,{type:"secondary"},"Maximum number of reconnection attempts"))),s.createElement(z,null,s.createElement(W,{strong:!0},"Batch Interval (ms)"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(d.YI,{min:0,max:500,value:ee.batchInterval,onChange:function(e){return te(R(R({},ee),{},{batchInterval:e}))},style:{marginRight:"8px"}}),s.createElement(W,{type:"secondary"},"Time to wait before sending batched messages (0 to disable batching)"))),s.createElement(z,null,s.createElement(W,{strong:!0},"Max Batch Size"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(d.YI,{min:1,max:1e3,value:ee.maxBatchSize,onChange:function(e){return te(R(R({},ee),{},{maxBatchSize:e}))},style:{marginRight:"8px"}}),s.createElement(W,{type:"secondary"},"Maximum number of messages in a batch"))),s.createElement(z,null,s.createElement(W,{strong:!0},"Heartbeat Interval (ms)"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(d.YI,{min:5e3,max:6e4,step:5e3,value:ee.heartbeatInterval,onChange:function(e){return te(R(R({},ee),{},{heartbeatInterval:e}))},style:{marginRight:"8px"}}),s.createElement(W,{type:"secondary"},"Time between heartbeat messages to keep the connection alive"))),s.createElement(T,null,s.createElement(W,{type:"secondary"},"These settings control the behavior of the Enhanced WebSocket client. Changes will take effect on the next connection."))))))}}}]);