"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[4605],{54605:(e,t,r)=>{r.r(t),r.d(t,{default:()=>J});var n=r(64467),a=r(10467),c=r(5544),o=r(57528),s=r(54756),l=r.n(s),i=r(96540),u=r(1807),m=r(35346),f=r(70572);function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach((function(t){(0,n.A)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var v,E,g,y,b,x,w,h=function(){var e=(0,a.A)(l().mark((function e(t){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e){try{var r=performance.now(),n=new WebSocket(t),a=setTimeout((function(){n.readyState!==WebSocket.OPEN&&(n.close(),e({success:!1,error:"Connection timeout",time:performance.now()-r}))}),5e3);n.onopen=function(){clearTimeout(a);var t=performance.now()-r;n.send(JSON.stringify({type:"ping"}));var c=setTimeout((function(){n.close(),e({success:!0,error:null,connectionTime:t,responseTime:null,message:"Connected but no response to ping"})}),2e3);n.onmessage=function(a){clearTimeout(c);var o=performance.now()-r;n.close(),e({success:!0,error:null,connectionTime:t,responseTime:o,message:"Connection successful"})},n.onerror=function(r){clearTimeout(c),n.close(),e({success:!1,error:"Error after connection: "+r.message,connectionTime:t,responseTime:null})}},n.onerror=function(t){clearTimeout(a),n.close(),e({success:!1,error:"Connection error: "+t.message,time:performance.now()-r})},n.onclose=function(t){clearTimeout(a),1e3!==t.code&&1001!==t.code&&e({success:!1,error:"Connection closed with code ".concat(t.code,": ").concat(t.reason),time:performance.now()-r})}}catch(t){e({success:!1,error:"Exception: "+t.message,time:0})}})));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),T=function(){var e=(0,a.A)(l().mark((function e(t){var r,n,a,c;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,r=performance.now(),e.next=4,fetch(t,{method:"GET",headers:{Accept:"application/json","Content-Type":"application/json"},timeout:5e3});case 4:if(n=e.sent,a=performance.now()-r,!n.ok){e.next=21;break}return e.prev=7,e.next=10,n.json();case 10:c=e.sent,e.next=18;break;case 13:return e.prev=13,e.t0=e.catch(7),e.next=17,n.text();case 17:c=e.sent;case 18:return e.abrupt("return",{success:!0,status:n.status,statusText:n.statusText,time:a,data:c});case 21:return e.abrupt("return",{success:!1,status:n.status,statusText:n.statusText,time:a,error:"HTTP error: ".concat(n.status," ").concat(n.statusText)});case 22:e.next=27;break;case 24:return e.prev=24,e.t1=e.catch(0),e.abrupt("return",{success:!1,error:"Exception: "+e.t1.message,time:0});case 27:case"end":return e.stop()}}),e,null,[[0,24],[7,13]])})));return function(t){return e.apply(this,arguments)}}(),S=function(){try{var e={};if(window.performance&&window.performance.timing){var t=window.performance.timing;e.pageLoad=t.loadEventEnd-t.navigationStart,e.domReady=t.domComplete-t.domLoading,e.networkLatency=t.responseEnd-t.fetchStart,e.processingTime=t.domComplete-t.responseEnd,e.backendTime=t.responseStart-t.navigationStart,e.frontendTime=t.loadEventEnd-t.responseStart}if(window.performance&&window.performance.memory&&(e.memory={jsHeapSizeLimit:window.performance.memory.jsHeapSizeLimit,totalJSHeapSize:window.performance.memory.totalJSHeapSize,usedJSHeapSize:window.performance.memory.usedJSHeapSize}),window.requestAnimationFrame){e.frameRate={current:0,average:0};var r=0,n=performance.now(),a=function(t){r++;var c=t-n;c>=1e3&&(e.frameRate.current=Math.round(1e3*r/c),e.frameRate.average?e.frameRate.average=Math.round((e.frameRate.average+e.frameRate.current)/2):e.frameRate.average=e.frameRate.current,r=0,n=t),window.requestAnimationFrame(a)};window.requestAnimationFrame(a)}return{success:!0,metrics:e}}catch(e){return{success:!1,error:"Exception: "+e.message}}},k=function(){var e=(0,a.A)(l().mark((function e(){var t,r,n=arguments;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=n.length>0&&void 0!==n[0]?n[0]:{},r={websocket:null,api:null,performance:null},e.prev=2,!t.websocketUrl){e.next=9;break}return u.iU.info("Testing WebSocket connection..."),e.next=7,h(t.websocketUrl);case 7:r.websocket=e.sent,r.websocket.success?u.iU.success("WebSocket connection successful (".concat(Math.round(r.websocket.connectionTime),"ms)")):u.iU.error("WebSocket connection failed: ".concat(r.websocket.error));case 9:if(!t.apiUrl){e.next=15;break}return u.iU.info("Testing API connection..."),e.next=13,T(t.apiUrl);case 13:r.api=e.sent,r.api.success?u.iU.success("API connection successful (".concat(Math.round(r.api.time),"ms)")):u.iU.error("API connection failed: ".concat(r.api.error));case 15:return t.testPerformance&&(u.iU.info("Testing browser performance..."),r.performance=S(),r.performance.success?u.iU.success("Performance test completed"):u.iU.error("Performance test failed: ".concat(r.performance.error))),e.abrupt("return",r);case 19:return e.prev=19,e.t0=e.catch(2),u.iU.error("Test failed: ".concat(e.t0.message)),e.abrupt("return",d(d({},r),{},{error:e.t0.message}));case 23:case"end":return e.stop()}}),e,null,[[2,19]])})));return function(){return e.apply(this,arguments)}}(),j=r(26390);function A(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function P(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?A(Object(r),!0).forEach((function(t){(0,n.A)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var L=u.o5.Title,O=u.o5.Text,R=u.o5.Paragraph,U=u.SD.Panel,C=f.Ay.div(v||(v=(0,o.A)(["\n  padding: 20px;\n"]))),z=(0,f.Ay)(u.Zp)(E||(E=(0,o.A)(["\n  margin-bottom: 20px;\n"]))),M=f.Ay.div(g||(g=(0,o.A)(["\n  margin-bottom: 16px;\n\n  .label {\n    display: block;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n"]))),H=f.Ay.div(y||(y=(0,o.A)(["\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n"]))),F=(0,f.Ay)(u.Fc)(b||(b=(0,o.A)(["\n  margin-bottom: 16px;\n"]))),I=(0,f.Ay)(u.Zp)(x||(x=(0,o.A)(["\n  margin-top: 16px;\n\n  .ant-card-head {\n    background-color: ",";\n  }\n"])),(function(e){return"success"===e.status?"rgba(82, 196, 26, 0.1)":"error"===e.status?"rgba(245, 34, 45, 0.1)":"warning"===e.status?"rgba(250, 173, 20, 0.1)":"transparent"})),N=(0,f.Ay)(u.Zp)(w||(w=(0,o.A)(["\n  height: 100%;\n\n  .ant-statistic-title {\n    font-size: 14px;\n  }\n\n  .ant-statistic-content {\n    font-size: 24px;\n  }\n"]))),D=function(e){var t=e.status;return"success"===t?i.createElement(m.hWy,{style:{color:"#52c41a"}}):"error"===t?i.createElement(m.bBN,{style:{color:"#f5222d"}}):"warning"===t?i.createElement(m.v7y,{style:{color:"#faad14"}}):"loading"===t?i.createElement(m.NKq,{style:{color:"#1890ff"}}):i.createElement(m.rUN,{style:{color:"#1890ff"}})};const J=function(){var e=(0,i.useState)((0,j.$0)("app_builder")),t=(0,c.A)(e,2),r=t[0],n=t[1],o=(0,i.useState)((0,j.e9)("health")),s=(0,c.A)(o,2),f=s[0],p=s[1],d=(0,i.useState)(!0),v=(0,c.A)(d,2),E=v[0],g=v[1],y=(0,i.useState)(!1),b=(0,c.A)(y,2),x=b[0],w=b[1],A=(0,i.useState)(null),J=(0,c.A)(A,2),W=J[0],$=J[1],B=function(){var e=(0,a.A)(l().mark((function e(){var t;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return w(!0),$(null),e.prev=2,e.next=5,k({websocketUrl:r,apiUrl:f,testPerformance:E});case 5:t=e.sent,$(t),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(2),console.error("Test error:",e.t0);case 12:return e.prev=12,w(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[2,9,12,15]])})));return function(){return e.apply(this,arguments)}}(),q=function(){var e=(0,a.A)(l().mark((function e(){var t;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return w(!0),e.prev=1,e.next=4,h(r);case 4:t=e.sent,$((function(e){return P(P({},e),{},{websocket:t})})),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("WebSocket test error:",e.t0);case 11:return e.prev=11,w(!1),e.finish(11);case 14:case"end":return e.stop()}}),e,null,[[1,8,11,14]])})));return function(){return e.apply(this,arguments)}}(),G=function(){var e=(0,a.A)(l().mark((function e(){var t;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return w(!0),e.prev=1,e.next=4,T(f);case 4:t=e.sent,$((function(e){return P(P({},e),{},{api:t})})),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("API test error:",e.t0);case 11:return e.prev=11,w(!1),e.finish(11);case 14:case"end":return e.stop()}}),e,null,[[1,8,11,14]])})));return function(){return e.apply(this,arguments)}}();return i.createElement(C,null,i.createElement(L,{level:3},"Testing Tools"),i.createElement(R,null,"Use these tools to test various aspects of the application."),i.createElement(z,{title:"Test Configuration"},i.createElement(F,{type:"info",message:"Configure Test Parameters",description:"Set up the parameters for the tests you want to run.",icon:i.createElement(m.rUN,null)}),i.createElement(M,null,i.createElement("div",{className:"label"},"WebSocket URL:"),i.createElement(u.pd,{value:r,onChange:function(e){return n(e.target.value)},placeholder:"Enter WebSocket URL",prefix:i.createElement(m.t7c,null),disabled:x})),i.createElement(M,null,i.createElement("div",{className:"label"},"API URL:"),i.createElement(u.pd,{value:f,onChange:function(e){return p(e.target.value)},placeholder:"Enter API URL",prefix:i.createElement(m.bfv,null),disabled:x})),i.createElement(M,null,i.createElement("div",{className:"label"},"Include Performance Test:"),i.createElement(u.dO,{checked:E,onChange:g,disabled:x})),i.createElement(H,null,i.createElement(u.$n,{type:"primary",icon:i.createElement(m.VgC,null),onClick:B,loading:x,disabled:x},"Run All Tests"),i.createElement(u.$n,{icon:i.createElement(m.bfv,null),onClick:q,disabled:x||!r},"Test WebSocket"),i.createElement(u.$n,{icon:i.createElement(m.t7c,null),onClick:G,disabled:x||!f},"Test API"),i.createElement(u.$n,{icon:i.createElement(m.zpd,null),onClick:function(){w(!0);try{var e=S();$((function(t){return P(P({},t),{},{performance:e})}))}catch(e){console.error("Performance test error:",e)}finally{w(!1)}},disabled:x||!E},"Test Performance"))),x&&i.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},i.createElement(u.tK,{size:"large"}),i.createElement("div",{style:{marginTop:"16px"}},"Running tests...")),W&&i.createElement(i.Fragment,null,function(){if(!W||!W.websocket)return null;var e=W.websocket,t=e.success?"success":"error";return i.createElement(I,{title:i.createElement(u.$x,null,i.createElement(D,{status:t}),i.createElement("span",null,"WebSocket Test Results")),status:t},e.success?i.createElement(i.Fragment,null,i.createElement(u.fI,{gutter:[16,16]},i.createElement(u.fv,{xs:24,sm:12},i.createElement(u.jL,{title:"Connection Time",value:e.connectionTime?Math.round(e.connectionTime):"N/A",suffix:"ms"})),i.createElement(u.fv,{xs:24,sm:12},i.createElement(u.jL,{title:"Response Time",value:e.responseTime?Math.round(e.responseTime-e.connectionTime):"N/A",suffix:"ms"}))),i.createElement(u.cG,null),i.createElement(R,null,i.createElement(O,{strong:!0},"Status:")," ",e.message||"Connection successful")):i.createElement(u.Fc,{message:"Connection Failed",description:e.error||"Unknown error",type:"error",showIcon:!0}))}(),function(){if(!W||!W.api)return null;var e=W.api,t=e.success?"success":"error";return i.createElement(I,{title:i.createElement(u.$x,null,i.createElement(D,{status:t}),i.createElement("span",null,"API Test Results")),status:t},e.success?i.createElement(i.Fragment,null,i.createElement(u.fI,{gutter:[16,16]},i.createElement(u.fv,{xs:24,sm:8},i.createElement(u.jL,{title:"Response Time",value:Math.round(e.time),suffix:"ms"})),i.createElement(u.fv,{xs:24,sm:8},i.createElement(u.jL,{title:"Status Code",value:e.status,suffix:e.statusText})),i.createElement(u.fv,{xs:24,sm:8},i.createElement(u.jL,{title:"Response Size",value:e.data?JSON.stringify(e.data).length:0,suffix:"bytes"}))),i.createElement(u.cG,null),i.createElement(u.SD,null,i.createElement(U,{header:"Response Data",key:"1"},i.createElement("pre",{style:{maxHeight:"200px",overflow:"auto"}},JSON.stringify(e.data,null,2))))):i.createElement(u.Fc,{message:"HTTP ".concat(e.status||"Error"),description:e.error||"Unknown error",type:"error",showIcon:!0}))}(),function(){if(!W||!W.performance)return null;var e=W.performance;if(e.success,!e.success)return i.createElement(I,{title:i.createElement(u.$x,null,i.createElement(D,{status:"error"}),i.createElement("span",null,"Performance Test Results")),status:"error"},i.createElement(u.Fc,{message:"Test Failed",description:e.error||"Unknown error",type:"error",showIcon:!0}));var t=e.metrics,r=0,n=0;if(t.pageLoad&&(r+=Math.max(0,100-t.pageLoad/30),n++),t.frameRate&&t.frameRate.average&&(r+=Math.min(100,t.frameRate.average/60*100),n++),t.memory&&t.memory.usedJSHeapSize&&t.memory.jsHeapSizeLimit){var a=t.memory.usedJSHeapSize/t.memory.jsHeapSizeLimit*100;r+=Math.max(0,100-a),n++}var c=n>0?Math.round(r/n):0,o="success";return c<50?o="error":c<70&&(o="warning"),i.createElement(I,{title:i.createElement(u.$x,null,i.createElement(D,{status:o}),i.createElement("span",null,"Performance Test Results")),status:o},i.createElement(u.fI,{gutter:[16,16],style:{marginBottom:"16px"}},i.createElement(u.fv,{xs:24,md:8},i.createElement(N,null,i.createElement(u.jL,{title:"Performance Score",value:c,suffix:"/100",valueStyle:{color:c>=70?"#52c41a":c>=50?"#faad14":"#f5222d"}}),i.createElement(u.ke,{percent:c,status:c>=70?"success":c>=50?"normal":"exception",showInfo:!1,style:{marginTop:"8px"}}))),t.pageLoad&&i.createElement(u.fv,{xs:24,md:8},i.createElement(N,null,i.createElement(u.jL,{title:i.createElement(u.m_,{title:"Time to fully load the page"},i.createElement("span",null,"Page Load Time ",i.createElement(m.rUN,null))),value:Math.round(t.pageLoad),suffix:"ms",valueStyle:{color:t.pageLoad<1e3?"#52c41a":t.pageLoad<3e3?"#faad14":"#f5222d"}}))),t.frameRate&&t.frameRate.average&&i.createElement(u.fv,{xs:24,md:8},i.createElement(N,null,i.createElement(u.jL,{title:i.createElement(u.m_,{title:"Average frames per second"},i.createElement("span",null,"Frame Rate ",i.createElement(m.rUN,null))),value:t.frameRate.average,suffix:"fps",valueStyle:{color:t.frameRate.average>=50?"#52c41a":t.frameRate.average>=30?"#faad14":"#f5222d"}})))),i.createElement(u.SD,null,i.createElement(U,{header:"Detailed Metrics",key:"1"},i.createElement(u.fI,{gutter:[16,16]},t.domReady&&i.createElement(u.fv,{xs:24,md:8},i.createElement(u.jL,{title:"DOM Ready Time",value:Math.round(t.domReady),suffix:"ms"})),t.networkLatency&&i.createElement(u.fv,{xs:24,md:8},i.createElement(u.jL,{title:"Network Latency",value:Math.round(t.networkLatency),suffix:"ms"})),t.processingTime&&i.createElement(u.fv,{xs:24,md:8},i.createElement(u.jL,{title:"Processing Time",value:Math.round(t.processingTime),suffix:"ms"})),t.backendTime&&i.createElement(u.fv,{xs:24,md:8},i.createElement(u.jL,{title:"Backend Time",value:Math.round(t.backendTime),suffix:"ms"})),t.frontendTime&&i.createElement(u.fv,{xs:24,md:8},i.createElement(u.jL,{title:"Frontend Time",value:Math.round(t.frontendTime),suffix:"ms"})),t.memory&&t.memory.usedJSHeapSize&&i.createElement(u.fv,{xs:24,md:8},i.createElement(u.jL,{title:"Memory Usage",value:(t.memory.usedJSHeapSize/1048576).toFixed(1),suffix:"MB"})),t.memory&&t.memory.totalJSHeapSize&&i.createElement(u.fv,{xs:24,md:8},i.createElement(u.jL,{title:"Total Heap Size",value:(t.memory.totalJSHeapSize/1048576).toFixed(1),suffix:"MB"})),t.memory&&t.memory.jsHeapSizeLimit&&i.createElement(u.fv,{xs:24,md:8},i.createElement(u.jL,{title:"Heap Size Limit",value:(t.memory.jsHeapSizeLimit/1048576).toFixed(1),suffix:"MB"}))))))}()))}}}]);