import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { addComponent, updateComponent, removeComponent } from '../redux/minimal-store';

const ComponentBuilder = () => {
  const [componentName, setComponentName] = useState('');
  const [componentType, setComponentType] = useState('container');
  const [componentProps, setComponentProps] = useState('');
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [editMode, setEditMode] = useState(false);

  const components = useSelector(state => state.app?.components || []);
  const dispatch = useDispatch();

  const componentTypes = [
    { value: 'container', label: 'Container' },
    { value: 'text', label: 'Text' },
    { value: 'button', label: 'Button' },
    { value: 'input', label: 'Input Field' },
    { value: 'image', label: 'Image' },
    { value: 'card', label: 'Card' },
    { value: 'list', label: 'List' },
    { value: 'custom', label: 'Custom' }
  ];

  const handleAddComponent = () => {
    if (!componentName.trim()) return;

    try {
      const propsObject = componentProps ? JSON.parse(componentProps) : {};

      const newComponent = {
        id: Date.now().toString(),
        name: componentName.trim(),
        type: componentType,
        props: propsObject,
        createdAt: new Date().toISOString()
      };

      dispatch(addComponent(newComponent));

      // Reset form
      setComponentName('');
      setComponentType('container');
      setComponentProps('');
    } catch (error) {
      alert(`Error parsing props: ${error.message}`);
    }
  };

  const handleUpdateComponent = () => {
    if (!selectedComponent || !componentName.trim()) return;

    try {
      const propsObject = componentProps ? JSON.parse(componentProps) : {};

      const updatedComponent = {
        ...selectedComponent,
        name: componentName.trim(),
        type: componentType,
        props: propsObject,
        updatedAt: new Date().toISOString()
      };

      dispatch(updateComponent(updatedComponent));

      // Reset form and exit edit mode
      setComponentName('');
      setComponentType('container');
      setComponentProps('');
      setSelectedComponent(null);
      setEditMode(false);
    } catch (error) {
      alert(`Error parsing props: ${error.message}`);
    }
  };

  const handleRemoveComponent = (id) => {
    dispatch(removeComponent(id));

    // If the removed component was selected, reset the form
    if (selectedComponent && selectedComponent.id === id) {
      setComponentName('');
      setComponentType('container');
      setComponentProps('');
      setSelectedComponent(null);
      setEditMode(false);
    }
  };

  const handleSelectComponent = (component) => {
    setSelectedComponent(component);
    setComponentName(component.name);
    setComponentType(component.type);
    setComponentProps(JSON.stringify(component.props, null, 2));
    setEditMode(true);
  };

  const handleCancelEdit = () => {
    setComponentName('');
    setComponentType('container');
    setComponentProps('');
    setSelectedComponent(null);
    setEditMode(false);
  };

  return (
    <div style={{ padding: '1rem', border: '1px solid #e5e7eb', borderRadius: '0.5rem', marginTop: '1rem' }}>
      <h2>Component Builder</h2>

      <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
        <div style={{ flex: 1 }}>
          <h3>{editMode ? 'Edit Component' : 'Create Component'}</h3>

          <div style={{ marginBottom: '1rem' }}>
            <label style={{ display: 'block', marginBottom: '0.25rem' }}>Component Name:</label>
            <input
              type="text"
              value={componentName}
              onChange={(e) => setComponentName(e.target.value)}
              style={{ width: '100%', padding: '0.5rem', borderRadius: '0.25rem', border: '1px solid #e5e7eb' }}
            />
          </div>

          <div style={{ marginBottom: '1rem' }}>
            <label style={{ display: 'block', marginBottom: '0.25rem' }}>Component Type:</label>
            <select
              value={componentType}
              onChange={(e) => setComponentType(e.target.value)}
              style={{ width: '100%', padding: '0.5rem', borderRadius: '0.25rem', border: '1px solid #e5e7eb' }}
            >
              {componentTypes.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>

          <div style={{ marginBottom: '1rem' }}>
            <label style={{ display: 'block', marginBottom: '0.25rem' }}>
              Component Props (JSON):
              <span style={{ fontSize: '0.8rem', color: '#6B7280', marginLeft: '0.5rem' }}>
                e.g. {`{"text": "Hello", "color": "blue"}`}
              </span>
            </label>
            <textarea
              value={componentProps}
              onChange={(e) => setComponentProps(e.target.value)}
              rows={5}
              style={{ width: '100%', padding: '0.5rem', borderRadius: '0.25rem', border: '1px solid #e5e7eb', fontFamily: 'monospace' }}
            />
          </div>

          <div style={{ display: 'flex', gap: '0.5rem' }}>
            {editMode ? (
              <>
                <button
                  onClick={handleUpdateComponent}
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#2563EB',
                    color: 'white',
                    border: 'none',
                    borderRadius: '0.25rem',
                    cursor: 'pointer'
                  }}
                >
                  Update Component
                </button>
                <button
                  onClick={handleCancelEdit}
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#6B7280',
                    color: 'white',
                    border: 'none',
                    borderRadius: '0.25rem',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
              </>
            ) : (
              <button
                onClick={handleAddComponent}
                style={{
                  padding: '0.5rem 1rem',
                  backgroundColor: '#2563EB',
                  color: 'white',
                  border: 'none',
                  borderRadius: '0.25rem',
                  cursor: 'pointer'
                }}
              >
                Add Component
              </button>
            )}
          </div>
        </div>

        <div style={{ flex: 1 }}>
          <h3>Component Library</h3>

          {components.length === 0 ? (
            <p style={{ color: '#6B7280', fontStyle: 'italic' }}>No components yet. Create one to get started.</p>
          ) : (
            <div style={{
              border: '1px solid #e5e7eb',
              borderRadius: '0.25rem',
              maxHeight: '300px',
              overflowY: 'auto'
            }}>
              {components.map(component => (
                <div
                  key={component.id}
                  style={{
                    padding: '0.75rem',
                    borderBottom: '1px solid #e5e7eb',
                    backgroundColor: selectedComponent && selectedComponent.id === component.id ? '#EFF6FF' : 'transparent',
                    cursor: 'pointer'
                  }}
                  onClick={() => handleSelectComponent(component)}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <strong>{component.name}</strong>
                      <div style={{ fontSize: '0.8rem', color: '#6B7280' }}>
                        Type: {component.type}
                      </div>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveComponent(component.id);
                      }}
                      style={{
                        padding: '0.25rem 0.5rem',
                        backgroundColor: '#EF4444',
                        color: 'white',
                        border: 'none',
                        borderRadius: '0.25rem',
                        cursor: 'pointer',
                        fontSize: '0.8rem'
                      }}
                    >
                      Remove
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {components.length > 0 && (
        <div style={{ marginTop: '1rem' }}>
          <h3>Component Preview</h3>
          <div style={{
            border: '1px solid #e5e7eb',
            borderRadius: '0.25rem',
            padding: '1rem',
            backgroundColor: '#f9fafb'
          }}>
            {selectedComponent ? (
              <div>
                <h4>{selectedComponent.name}</h4>
                <div style={{ fontFamily: 'monospace', fontSize: '0.9rem' }}>
                  <pre>{JSON.stringify(selectedComponent, null, 2)}</pre>
                </div>
              </div>
            ) : (
              <p style={{ color: '#6B7280', fontStyle: 'italic' }}>Select a component to preview</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ComponentBuilder;
