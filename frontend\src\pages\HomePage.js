import React, { useEffect, useState } from 'react';
// Optimized Ant Design imports for better tree-shaking
import { Card, Button, Statistic, List, Tag, Divider, Alert } from '../utils/optimizedAntdImports';
import {
  DashboardOutlined,
  ApiOutlined,
  SettingOutlined,
  RightOutlined,
  CheckCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { setCurrentView } from '../redux/reducers/uiReducer';
import WebSocketService from '../services/WebSocketService';
import {
  PageContainer,
  EnhancedTitle,
  EnhancedParagraph,
  GridLayout,
  EnhancedCard,
  PrimaryButton,
  FlexContainer
} from '../components/common/StyledComponents';
import { DashboardSkeleton } from '../components/common/SkeletonLoaders';

const HomePage = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const [connectionState, setConnectionState] = useState(WebSocketService.getConnectionState());
  const [systemStatus, setSystemStatus] = useState({
    websocket: connectionState.connected,
    api: true,
    database: true,
    storage: Math.random() > 0.2 // Randomly simulate storage issues
  });

  // Set current view in Redux store
  useEffect(() => {
    dispatch(setCurrentView('home'));

    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [dispatch]);

  // Listen for WebSocket events
  useEffect(() => {
    const handleConnect = () => {
      const state = WebSocketService.getConnectionState();
      setConnectionState(state);
      setSystemStatus(prev => ({ ...prev, websocket: true }));
    };

    const handleDisconnect = () => {
      const state = WebSocketService.getConnectionState();
      setConnectionState(state);
      setSystemStatus(prev => ({ ...prev, websocket: false }));
    };

    // Register event listeners
    WebSocketService.addEventListener('connect', handleConnect);
    WebSocketService.addEventListener('disconnect', handleDisconnect);

    // Initial state
    setConnectionState(WebSocketService.getConnectionState());
    setSystemStatus(prev => ({ ...prev, websocket: connectionState.connected }));

    // Cleanup event listeners on unmount
    return () => {
      WebSocketService.removeEventListener('connect', handleConnect);
      WebSocketService.removeEventListener('disconnect', handleDisconnect);
    };
  }, []);

  // Calculate overall system status
  const getOverallStatus = () => {
    const statusValues = Object.values(systemStatus);
    if (statusValues.every(status => status === true)) {
      return { status: 'success', text: 'All Systems Operational' };
    } else if (statusValues.filter(status => status === false).length > 1) {
      return { status: 'error', text: 'Multiple Systems Down' };
    } else {
      return { status: 'warning', text: 'Partial System Outage' };
    }
  };

  const overallStatus = getOverallStatus();

  // Render loading state
  if (loading) {
    return (
      <PageContainer>
        <EnhancedTitle level={2}>Welcome to App Builder</EnhancedTitle>
        <EnhancedParagraph>
          Build, test, and deploy your applications with ease.
        </EnhancedParagraph>
        <DashboardSkeleton cards={4} />
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <EnhancedTitle level={2}>Welcome to App Builder</EnhancedTitle>
      <EnhancedParagraph>
        Build, test, and deploy your applications with ease. Monitor your WebSocket connections,
        diagnose network issues, and optimize performance.
      </EnhancedParagraph>

      <Alert
        type={overallStatus.status}
        message={overallStatus.text}
        showIcon
        style={{ marginBottom: 24 }}
      />

      <GridLayout columns={2} columnsSm={1}>
        <EnhancedCard>
          <EnhancedTitle level={4}>System Status</EnhancedTitle>
          <List
            size="large"
            dataSource={[
              {
                name: 'WebSocket Service',
                status: systemStatus.websocket,
                icon: <ApiOutlined />,
                route: '/websocket-diagnostics'
              },
              {
                name: 'API Service',
                status: systemStatus.api,
                icon: <ApiOutlined />,
                route: '/dashboard'
              },
              {
                name: 'Database',
                status: systemStatus.database,
                icon: <SettingOutlined />,
                route: '/dashboard'
              },
              {
                name: 'Storage Service',
                status: systemStatus.storage,
                icon: <SettingOutlined />,
                route: '/dashboard'
              }
            ]}
            renderItem={item => (
              <List.Item
                actions={[
                  <Link to={item.route}>
                    <Button type="link" icon={<RightOutlined />}>
                      Details
                    </Button>
                  </Link>
                ]}
              >
                <List.Item.Meta
                  avatar={item.icon}
                  title={
                    <FlexContainer justify="space-between">
                      <span>{item.name}</span>
                      {item.status ? (
                        <Tag color="success" icon={<CheckCircleOutlined />}>Operational</Tag>
                      ) : (
                        <Tag color="error" icon={<WarningOutlined />}>Down</Tag>
                      )}
                    </FlexContainer>
                  }
                />
              </List.Item>
            )}
          />
        </EnhancedCard>

        <EnhancedCard>
          <EnhancedTitle level={4}>Quick Actions</EnhancedTitle>
          <FlexContainer direction="column" gap={16}>
            <Link to="/">
              <PrimaryButton icon={<DashboardOutlined />} block>
                App Builder
              </PrimaryButton>
            </Link>
            <Link to="/app-builder">
              <PrimaryButton icon={<DashboardOutlined />} block>
                App Builder (Alternative)
              </PrimaryButton>
            </Link>
            <Link to="/websocket">
              <PrimaryButton icon={<ApiOutlined />} block>
                WebSocket Manager
              </PrimaryButton>
            </Link>
            <Link to="/websocket-diagnostics">
              <PrimaryButton icon={<ApiOutlined />} block>
                WebSocket Diagnostics
              </PrimaryButton>
            </Link>
            <Link to="/network-diagnostic">
              <PrimaryButton icon={<ApiOutlined />} block>
                Network Diagnostics
              </PrimaryButton>
            </Link>
          </FlexContainer>
        </EnhancedCard>
      </GridLayout>

      <Divider style={{ margin: '32px 0 24px' }} />

      <GridLayout columns={3} columnsMd={2} columnsSm={1}>
        <EnhancedCard>
          <Statistic
            title="WebSocket Status"
            value={connectionState.connected ? "Connected" : "Disconnected"}
            valueStyle={{ color: connectionState.connected ? '#3f8600' : '#cf1322' }}
          />
          <div style={{ marginTop: 16 }}>
            <Link to="/websocket-diagnostics">
              <Button type="primary" size="small">
                View Details
              </Button>
            </Link>
          </div>
        </EnhancedCard>

        <EnhancedCard>
          <Statistic
            title="Connection Uptime"
            value={connectionState.connected ? "Active" : "Inactive"}
            suffix={connectionState.connected ? "now" : ""}
            valueStyle={{ color: connectionState.connected ? '#3f8600' : '#cf1322' }}
          />
          <div style={{ marginTop: 16 }}>
            <Link to="/websocket-diagnostics">
              <Button type="primary" size="small">
                View Details
              </Button>
            </Link>
          </div>
        </EnhancedCard>

        <EnhancedCard>
          <Statistic
            title="Reconnect Attempts"
            value={connectionState.reconnectAttempts || 0}
            valueStyle={{ color: connectionState.reconnectAttempts > 0 ? '#faad14' : '#3f8600' }}
          />
          <div style={{ marginTop: 16 }}>
            <Button
              type="primary"
              size="small"
              onClick={() => WebSocketService.reconnect()}
              disabled={connectionState.connected}
            >
              Reconnect
            </Button>
          </div>
        </EnhancedCard>
      </GridLayout>

      {connectionState.lastError && (
        <Alert
          type="error"
          message="WebSocket Error"
          description={
            <div>
              <div>{connectionState.lastError.message}</div>
              {connectionState.lastError.timestamp && (
                <div style={{ color: 'rgba(0, 0, 0, 0.45)', marginTop: 8 }}>
                  {new Date(connectionState.lastError.timestamp).toLocaleString()}
                </div>
              )}
            </div>
          }
          showIcon
          style={{ marginTop: 24 }}
        />
      )}
    </PageContainer>
  );
};

export default HomePage;
