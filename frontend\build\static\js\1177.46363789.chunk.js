"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[1177],{11177:(e,t,r)=>{r.r(t),r.d(t,{default:()=>k});var n,l,a,o,c=r(57528),d=r(96540),m=r(1807),s=r(82569),i=r(57683),u=r(70572),E=m.o5.Title,p=m.o5.Paragraph,g=m.o5.Text,v=u.Ay.div(n||(n=(0,c.A)(["\n  padding: var(--spacing-lg);\n  background-color: var(--color-background);\n  min-height: 100vh;\n  transition: all 0.3s ease;\n"]))),h=(0,u.Ay)(m.Zp)(l||(l=(0,c.A)(["\n  margin-bottom: var(--spacing-lg);\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-md);\n    transform: translateY(-2px);\n  }\n"]))),y=u.Ay.div(a||(a=(0,c.A)(["\n  display: inline-block;\n  width: 40px;\n  height: 40px;\n  border-radius: var(--border-radius-md);\n  margin-right: var(--spacing-sm);\n  border: 1px solid var(--color-border);\n  vertical-align: middle;\n"]))),b=u.Ay.div(o||(o=(0,c.A)(["\n  background-color: var(--color-background-secondary);\n  padding: var(--spacing-md);\n  border-radius: var(--border-radius-md);\n  border: 1px solid var(--color-border-light);\n  margin: var(--spacing-md) 0;\n"])));const k=function(){var e=(0,s.ZV)(),t=e.isDarkMode,r=e.themeMode,n=e.colors,l=e.systemPrefersDark;return d.createElement(v,null,d.createElement(h,null,d.createElement(E,{level:2},"Dark Mode Test"),d.createElement(p,null,"This component tests the dark mode functionality and theme switching."),d.createElement(m.$x,{size:"large",wrap:!0},d.createElement(i.A,{showDropdown:!0}),d.createElement(i.A,{showDropdown:!1})),d.createElement(m.cG,null),d.createElement(b,null,d.createElement(E,{level:4},"Current Theme Information"),d.createElement(m.$x,{direction:"vertical",size:"small"},d.createElement(g,null,d.createElement("strong",null,"Theme Mode:")," ",r),d.createElement(g,null,d.createElement("strong",null,"Is Dark Mode:")," ",t?"Yes":"No"),d.createElement(g,null,d.createElement("strong",null,"System Prefers Dark:")," ",l?"Yes":"No"))),d.createElement(m.cG,null),d.createElement(E,{level:4},"Color Palette"),d.createElement(m.$x,{direction:"vertical",size:"small"},d.createElement("div",null,d.createElement(y,{style:{backgroundColor:n.primary}}),d.createElement(g,null,"Primary: ",n.primary)),d.createElement("div",null,d.createElement(y,{style:{backgroundColor:n.secondary}}),d.createElement(g,null,"Secondary: ",n.secondary)),d.createElement("div",null,d.createElement(y,{style:{backgroundColor:n.background}}),d.createElement(g,null,"Background: ",n.background)),d.createElement("div",null,d.createElement(y,{style:{backgroundColor:n.surface}}),d.createElement(g,null,"Surface: ",n.surface)),d.createElement("div",null,d.createElement(y,{style:{backgroundColor:n.text}}),d.createElement(g,null,"Text: ",n.text))),d.createElement(m.cG,null),d.createElement(E,{level:4},"Interactive Elements"),d.createElement(m.$x,{wrap:!0},d.createElement(m.$n,{type:"primary"},"Primary Button"),d.createElement(m.$n,{type:"default"},"Default Button"),d.createElement(m.$n,{type:"dashed"},"Dashed Button"),d.createElement(m.$n,{type:"text"},"Text Button"),d.createElement(m.$n,{type:"link"},"Link Button")),d.createElement(m.cG,null),d.createElement(E,{level:4},"Text Variations"),d.createElement(m.$x,{direction:"vertical"},d.createElement(g,null,"Default text color"),d.createElement(g,{type:"secondary"},"Secondary text color"),d.createElement(g,{type:"success"},"Success text color"),d.createElement(g,{type:"warning"},"Warning text color"),d.createElement(g,{type:"danger"},"Danger text color"),d.createElement(g,{disabled:!0},"Disabled text color"))),d.createElement(h,null,d.createElement(E,{level:3},"Nested Card Example"),d.createElement(p,null,"This card demonstrates how nested components look in the current theme."),d.createElement(m.Zp,{type:"inner",title:"Inner Card"},d.createElement(p,null,"This is an inner card that should adapt to the theme as well."),d.createElement(m.$n,{type:"primary",size:"small"},"Inner Button"))))}}}]);