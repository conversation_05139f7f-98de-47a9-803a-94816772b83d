"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[3913],{56294:(e,t,n)=>{n.r(t),n.d(t,{default:()=>b});var c=n(64467),r=n(5544),a=n(96540),l=n(16918),o=n(35346),s=n(11080),i=n(71468),m=n(85331),u=n(17053),E=n(66894),p=n(38812);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(e);t&&(c=c.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,c)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){(0,c.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const b=function(){var e=(0,i.wA)(),t=(0,a.useState)(!0),n=(0,r.A)(t,2),c=n[0],d=n[1],b=(0,a.useState)(u.A.getConnectionState()),f=(0,r.A)(b,2),y=f[0],g=f[1],w=(0,a.useState)({websocket:y.connected,api:!0,database:!0,storage:Math.random()>.2}),k=(0,r.A)(w,2),S=k[0],A=k[1];(0,a.useEffect)((function(){e((0,m.tI)("home"));var t=setTimeout((function(){d(!1)}),1e3);return function(){return clearTimeout(t)}}),[e]),(0,a.useEffect)((function(){var e=function(){var e=u.A.getConnectionState();g(e),A((function(e){return v(v({},e),{},{websocket:!0})}))},t=function(){var e=u.A.getConnectionState();g(e),A((function(e){return v(v({},e),{},{websocket:!1})}))};return u.A.addEventListener("connect",e),u.A.addEventListener("disconnect",t),g(u.A.getConnectionState()),A((function(e){return v(v({},e),{},{websocket:y.connected})})),function(){u.A.removeEventListener("connect",e),u.A.removeEventListener("disconnect",t)}}),[]);var O,j=(O=Object.values(S)).every((function(e){return!0===e}))?{status:"success",text:"All Systems Operational"}:O.filter((function(e){return!1===e})).length>1?{status:"error",text:"Multiple Systems Down"}:{status:"warning",text:"Partial System Outage"};return c?a.createElement(E.LN,null,a.createElement(E.p1,{level:2},"Welcome to App Builder"),a.createElement(E.T6,null,"Build, test, and deploy your applications with ease."),a.createElement(p.O2,{cards:4})):a.createElement(E.LN,null,a.createElement(E.p1,{level:2},"Welcome to App Builder"),a.createElement(E.T6,null,"Build, test, and deploy your applications with ease. Monitor your WebSocket connections, diagnose network issues, and optimize performance."),a.createElement(l.Fc,{type:j.status,message:j.text,showIcon:!0,style:{marginBottom:24}}),a.createElement(E.JT,{columns:2,columnsSm:1},a.createElement(E.Lv,null,a.createElement(E.p1,{level:4},"System Status"),a.createElement(l.B8,{size:"large",dataSource:[{name:"WebSocket Service",status:S.websocket,icon:a.createElement(o.bfv,null),route:"/websocket-diagnostics"},{name:"API Service",status:S.api,icon:a.createElement(o.bfv,null),route:"/dashboard"},{name:"Database",status:S.database,icon:a.createElement(o.JO7,null),route:"/dashboard"},{name:"Storage Service",status:S.storage,icon:a.createElement(o.JO7,null),route:"/dashboard"}],renderItem:function(e){return a.createElement(l.B8.Item,{actions:[a.createElement(s.N_,{to:e.route},a.createElement(l.$n,{type:"link",icon:a.createElement(o.Xq1,null)},"Details"))]},a.createElement(l.B8.Item.Meta,{avatar:e.icon,title:a.createElement(E.n5,{justify:"space-between"},a.createElement("span",null,e.name),e.status?a.createElement(l.vw,{color:"success",icon:a.createElement(o.hWy,null)},"Operational"):a.createElement(l.vw,{color:"error",icon:a.createElement(o.v7y,null)},"Down"))}))}})),a.createElement(E.Lv,null,a.createElement(E.p1,{level:4},"Quick Actions"),a.createElement(E.n5,{direction:"column",gap:16},a.createElement(s.N_,{to:"/"},a.createElement(E.jn,{icon:a.createElement(o.zpd,null),block:!0},"App Builder")),a.createElement(s.N_,{to:"/app-builder"},a.createElement(E.jn,{icon:a.createElement(o.zpd,null),block:!0},"App Builder (Alternative)")),a.createElement(s.N_,{to:"/websocket"},a.createElement(E.jn,{icon:a.createElement(o.bfv,null),block:!0},"WebSocket Manager")),a.createElement(s.N_,{to:"/websocket-diagnostics"},a.createElement(E.jn,{icon:a.createElement(o.bfv,null),block:!0},"WebSocket Diagnostics")),a.createElement(s.N_,{to:"/network-diagnostic"},a.createElement(E.jn,{icon:a.createElement(o.bfv,null),block:!0},"Network Diagnostics"))))),a.createElement(l.cG,{style:{margin:"32px 0 24px"}}),a.createElement(E.JT,{columns:3,columnsMd:2,columnsSm:1},a.createElement(E.Lv,null,a.createElement(l.jL,{title:"WebSocket Status",value:y.connected?"Connected":"Disconnected",valueStyle:{color:y.connected?"#3f8600":"#cf1322"}}),a.createElement("div",{style:{marginTop:16}},a.createElement(s.N_,{to:"/websocket-diagnostics"},a.createElement(l.$n,{type:"primary",size:"small"},"View Details")))),a.createElement(E.Lv,null,a.createElement(l.jL,{title:"Connection Uptime",value:y.connected?"Active":"Inactive",suffix:y.connected?"now":"",valueStyle:{color:y.connected?"#3f8600":"#cf1322"}}),a.createElement("div",{style:{marginTop:16}},a.createElement(s.N_,{to:"/websocket-diagnostics"},a.createElement(l.$n,{type:"primary",size:"small"},"View Details")))),a.createElement(E.Lv,null,a.createElement(l.jL,{title:"Reconnect Attempts",value:y.reconnectAttempts||0,valueStyle:{color:y.reconnectAttempts>0?"#faad14":"#3f8600"}}),a.createElement("div",{style:{marginTop:16}},a.createElement(l.$n,{type:"primary",size:"small",onClick:function(){return u.A.reconnect()},disabled:y.connected},"Reconnect")))),y.lastError&&a.createElement(l.Fc,{type:"error",message:"WebSocket Error",description:a.createElement("div",null,a.createElement("div",null,y.lastError.message),y.lastError.timestamp&&a.createElement("div",{style:{color:"rgba(0, 0, 0, 0.45)",marginTop:8}},new Date(y.lastError.timestamp).toLocaleString())),showIcon:!0,style:{marginTop:24}}))}}}]);