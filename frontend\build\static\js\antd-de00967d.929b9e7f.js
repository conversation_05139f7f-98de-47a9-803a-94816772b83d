"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[526],{5903:(e,t,n)=>{n.d(t,{Ay:()=>f,Mb:()=>$});var o=n(96540),i=n(24768),r=n(4732),l=n(29729),a=n(65010),s=n(36962),c=n(46942),d=n.n(c),m=n(22370),u=n(38674),p=n(20934),g=n(98889);const b={info:o.createElement(a.A,null),success:o.createElement(i.A,null),error:o.createElement(r.A,null),warning:o.createElement(l.A,null),loading:o.createElement(s.A,null)},$=({prefixCls:e,type:t,icon:n,children:i})=>o.createElement("div",{className:d()(`${e}-custom-content`,`${e}-${t}`)},n||b[t],o.createElement("span",null,i)),f=e=>{const{prefixCls:t,className:n,type:i,icon:r,content:l}=e,a=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(o=Object.getOwnPropertySymbols(e);i<o.length;i++)t.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(n[o[i]]=e[o[i]])}return n}(e,["prefixCls","className","type","icon","content"]),{getPrefixCls:s}=o.useContext(u.QO),c=t||s("message"),b=(0,p.A)(c),[f,h,v]=(0,g.A)(c,b);return f(o.createElement(m.$T,Object.assign({},a,{prefixCls:c,className:d()(n,h,`${c}-notice-pure-panel`,v,b),eventKey:"pure",duration:null,content:o.createElement($,{prefixCls:c,type:i,icon:r},l)})))}},8182:(e,t,n)=>{n.d(t,{A:()=>s});var o=n(96069),i=n(24685),r=n(61340),l=n(65341);const a="${label} is not a valid ${type}",s={locale:"en",Pagination:o.A,DatePicker:r.A,TimePicker:l.A,Calendar:i.A,global:{placeholder:"Please select",close:"Close"},Table:{filterTitle:"Filter menu",filterConfirm:"OK",filterReset:"Reset",filterEmptyText:"No filters",filterCheckAll:"Select all items",filterSearchPlaceholder:"Search in filters",emptyText:"No data",selectAll:"Select current page",selectInvert:"Invert current page",selectNone:"Clear all data",selectionAll:"Select all data",sortTitle:"Sort",expand:"Expand row",collapse:"Collapse row",triggerDesc:"Click to sort descending",triggerAsc:"Click to sort ascending",cancelSort:"Click to cancel sorting"},Tour:{Next:"Next",Previous:"Previous",Finish:"Finish"},Modal:{okText:"OK",cancelText:"Cancel",justOkText:"OK"},Popconfirm:{okText:"OK",cancelText:"Cancel"},Transfer:{titles:["",""],searchPlaceholder:"Search here",itemUnit:"item",itemsUnit:"items",remove:"Remove",selectCurrent:"Select current page",removeCurrent:"Remove current page",selectAll:"Select all data",deselectAll:"Deselect all data",removeAll:"Remove all data",selectInvert:"Invert current page"},Upload:{uploading:"Uploading...",removeFile:"Remove file",uploadError:"Upload error",previewFile:"Preview file",downloadFile:"Download file"},Empty:{description:"No data"},Icon:{icon:"icon"},Text:{edit:"Edit",copy:"Copy",copied:"Copied",expand:"Expand",collapse:"Collapse"},Form:{optional:"(optional)",defaultValidateMessages:{default:"Field validation error for ${label}",required:"Please enter ${label}",enum:"${label} must be one of [${enum}]",whitespace:"${label} cannot be a blank character",date:{format:"${label} date format is invalid",parse:"${label} cannot be converted to a date",invalid:"${label} is an invalid date"},types:{string:a,method:a,array:a,object:a,number:a,date:a,boolean:a,integer:a,float:a,regexp:a,email:a,url:a,hex:a},string:{len:"${label} must be ${len} characters",min:"${label} must be at least ${min} characters",max:"${label} must be up to ${max} characters",range:"${label} must be between ${min}-${max} characters"},number:{len:"${label} must be equal to ${len}",min:"${label} must be minimum ${min}",max:"${label} must be maximum ${max}",range:"${label} must be between ${min}-${max}"},array:{len:"Must be ${len} ${label}",min:"At least ${min} ${label}",max:"At most ${max} ${label}",range:"The amount of ${label} must be between ${min}-${max}"},pattern:{mismatch:"${label} does not match the pattern ${pattern}"}}},Image:{preview:"Preview"},QRCode:{expired:"QR code expired",refresh:"Refresh",scanned:"Scanned"},ColorPicker:{presetEmpty:"Empty",transparent:"Transparent",singleColor:"Single",gradientColor:"Gradient"}}},19155:(e,t,n)=>{n.d(t,{A:()=>l});var o=n(96540),i=n(60685),r=n(8182);const l=(e,t)=>{const n=o.useContext(i.A);return[o.useMemo((()=>{var o;const i=t||r.A[e],l=null!==(o=null==n?void 0:n[e])&&void 0!==o?o:{};return Object.assign(Object.assign({},"function"==typeof i?i():i),l||{})}),[e,t,n]),o.useMemo((()=>{const e=null==n?void 0:n.locale;return(null==n?void 0:n.exist)&&!e?r.A.locale:e}),[n])]}},21282:(e,t,n)=>{n.d(t,{Ay:()=>s,M2:()=>a,Ym:()=>l.A});var o=n(96540),i=(n(18877),n(21815)),r=n(60685),l=n(19155);const a="internalMark",s=e=>{const{locale:t={},children:n,_ANT_MARK__:l}=e;o.useEffect((()=>(0,i.L)(null==t?void 0:t.Modal)),[t]);const a=o.useMemo((()=>Object.assign(Object.assign({},t),{exist:!0})),[t]);return o.createElement(r.A.Provider,{value:a},n)}},25783:(e,t,n)=>{function o(e,t){return{motionName:null!=t?t:`${e}-move-up`}}function i(e){let t;const n=new Promise((n=>{t=e((()=>{n(!0)}))})),o=()=>{null==t||t()};return o.then=(e,t)=>n.then(e,t),o.promise=n,o}n.d(t,{E:()=>i,V:()=>o})},42652:(e,t,n)=>{n.d(t,{A:()=>E});var o=n(60436),i=n(96540),r=n(46942),l=n.n(r),a=n(51679),s=n(24945),c=n(38674),d=n(62279),m=n(35128),u=n(829),p=n(36768),g=n(78551),b=n(44485),$=n(29029);const f=i.createContext({});f.Consumer;var h=n(40682),v=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(o=Object.getOwnPropertySymbols(e);i<o.length;i++)t.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(n[o[i]]=e[o[i]])}return n};const x=i.forwardRef(((e,t)=>{const{prefixCls:n,children:o,actions:r,extra:a,styles:s,className:d,classNames:m,colStyle:u}=e,g=v(e,["prefixCls","children","actions","extra","styles","className","classNames","colStyle"]),{grid:b,itemLayout:$}=(0,i.useContext)(f),{getPrefixCls:x,list:y}=(0,i.useContext)(c.QO),C=e=>{var t,n;return l()(null===(n=null===(t=null==y?void 0:y.item)||void 0===t?void 0:t.classNames)||void 0===n?void 0:n[e],null==m?void 0:m[e])},S=e=>{var t,n;return Object.assign(Object.assign({},null===(n=null===(t=null==y?void 0:y.item)||void 0===t?void 0:t.styles)||void 0===n?void 0:n[e]),null==s?void 0:s[e])},I=x("list",n),O=r&&r.length>0&&i.createElement("ul",{className:l()(`${I}-item-action`,C("actions")),key:"actions",style:S("actions")},r.map(((e,t)=>i.createElement("li",{key:`${I}-item-action-${t}`},e,t!==r.length-1&&i.createElement("em",{className:`${I}-item-action-split`}))))),A=b?"div":"li",B=i.createElement(A,Object.assign({},g,b?{}:{ref:t},{className:l()(`${I}-item`,{[`${I}-item-no-flex`]:!("vertical"===$?a:!(()=>{let e=!1;return i.Children.forEach(o,(t=>{"string"==typeof t&&(e=!0)})),e&&i.Children.count(o)>1})())},d)}),"vertical"===$&&a?[i.createElement("div",{className:`${I}-item-main`,key:"content"},o,O),i.createElement("div",{className:l()(`${I}-item-extra`,C("extra")),key:"extra",style:S("extra")},a)]:[o,O,(0,h.Ob)(a,{key:"extra"})]);return b?i.createElement(p.fv,{ref:t,flex:1,style:u},B):B})),y=x;y.Meta=e=>{var{prefixCls:t,className:n,avatar:o,title:r,description:a}=e,s=v(e,["prefixCls","className","avatar","title","description"]);const{getPrefixCls:d}=(0,i.useContext)(c.QO),m=d("list",t),u=l()(`${m}-item-meta`,n),p=i.createElement("div",{className:`${m}-item-meta-content`},r&&i.createElement("h4",{className:`${m}-item-meta-title`},r),a&&i.createElement("div",{className:`${m}-item-meta-description`},a));return i.createElement("div",Object.assign({},s,{className:u}),o&&i.createElement("div",{className:`${m}-item-meta-avatar`},o),(r||a)&&p)};const C=y;var S=n(36891),I=n(25905),O=n(51113);const A=e=>{const{listBorderedCls:t,componentCls:n,paddingLG:o,margin:i,itemPaddingSM:r,itemPaddingLG:l,marginLG:a,borderRadiusLG:s}=e;return{[t]:{border:`${(0,S.zA)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:s,[`${n}-header,${n}-footer,${n}-item`]:{paddingInline:o},[`${n}-pagination`]:{margin:`${(0,S.zA)(i)} ${(0,S.zA)(a)}`}},[`${t}${n}-sm`]:{[`${n}-item,${n}-header,${n}-footer`]:{padding:r}},[`${t}${n}-lg`]:{[`${n}-item,${n}-header,${n}-footer`]:{padding:l}}}},B=e=>{const{componentCls:t,screenSM:n,screenMD:o,marginLG:i,marginSM:r,margin:l}=e;return{[`@media screen and (max-width:${o}px)`]:{[t]:{[`${t}-item`]:{[`${t}-item-action`]:{marginInlineStart:i}}},[`${t}-vertical`]:{[`${t}-item`]:{[`${t}-item-extra`]:{marginInlineStart:i}}}},[`@media screen and (max-width: ${n}px)`]:{[t]:{[`${t}-item`]:{flexWrap:"wrap",[`${t}-action`]:{marginInlineStart:r}}},[`${t}-vertical`]:{[`${t}-item`]:{flexWrap:"wrap-reverse",[`${t}-item-main`]:{minWidth:e.contentWidth},[`${t}-item-extra`]:{margin:`auto auto ${(0,S.zA)(l)}`}}}}}},w=e=>{const{componentCls:t,antCls:n,controlHeight:o,minHeight:i,paddingSM:r,marginLG:l,padding:a,itemPadding:s,colorPrimary:c,itemPaddingSM:d,itemPaddingLG:m,paddingXS:u,margin:p,colorText:g,colorTextDescription:b,motionDurationSlow:$,lineWidth:f,headerBg:h,footerBg:v,emptyTextPadding:x,metaMarginBottom:y,avatarMarginRight:C,titleMarginBottom:O,descriptionFontSize:A}=e;return{[t]:Object.assign(Object.assign({},(0,I.dF)(e)),{position:"relative","*":{outline:"none"},[`${t}-header`]:{background:h},[`${t}-footer`]:{background:v},[`${t}-header, ${t}-footer`]:{paddingBlock:r},[`${t}-pagination`]:{marginBlockStart:l,[`${n}-pagination-options`]:{textAlign:"start"}},[`${t}-spin`]:{minHeight:i,textAlign:"center"},[`${t}-items`]:{margin:0,padding:0,listStyle:"none"},[`${t}-item`]:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:s,color:g,[`${t}-item-meta`]:{display:"flex",flex:1,alignItems:"flex-start",maxWidth:"100%",[`${t}-item-meta-avatar`]:{marginInlineEnd:C},[`${t}-item-meta-content`]:{flex:"1 0",width:0,color:g},[`${t}-item-meta-title`]:{margin:`0 0 ${(0,S.zA)(e.marginXXS)} 0`,color:g,fontSize:e.fontSize,lineHeight:e.lineHeight,"> a":{color:g,transition:`all ${$}`,"&:hover":{color:c}}},[`${t}-item-meta-description`]:{color:b,fontSize:A,lineHeight:e.lineHeight}},[`${t}-item-action`]:{flex:"0 0 auto",marginInlineStart:e.marginXXL,padding:0,fontSize:0,listStyle:"none","& > li":{position:"relative",display:"inline-block",padding:`0 ${(0,S.zA)(u)}`,color:b,fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"center","&:first-child":{paddingInlineStart:0}},[`${t}-item-action-split`]:{position:"absolute",insetBlockStart:"50%",insetInlineEnd:0,width:f,height:e.calc(e.fontHeight).sub(e.calc(e.marginXXS).mul(2)).equal(),transform:"translateY(-50%)",backgroundColor:e.colorSplit}}},[`${t}-empty`]:{padding:`${(0,S.zA)(a)} 0`,color:b,fontSize:e.fontSizeSM,textAlign:"center"},[`${t}-empty-text`]:{padding:x,color:e.colorTextDisabled,fontSize:e.fontSize,textAlign:"center"},[`${t}-item-no-flex`]:{display:"block"}}),[`${t}-grid ${n}-col > ${t}-item`]:{display:"block",maxWidth:"100%",marginBlockEnd:p,paddingBlock:0,borderBlockEnd:"none"},[`${t}-vertical ${t}-item`]:{alignItems:"initial",[`${t}-item-main`]:{display:"block",flex:1},[`${t}-item-extra`]:{marginInlineStart:l},[`${t}-item-meta`]:{marginBlockEnd:y,[`${t}-item-meta-title`]:{marginBlockStart:0,marginBlockEnd:O,color:g,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}},[`${t}-item-action`]:{marginBlockStart:a,marginInlineStart:"auto","> li":{padding:`0 ${(0,S.zA)(a)}`,"&:first-child":{paddingInlineStart:0}}}},[`${t}-split ${t}-item`]:{borderBlockEnd:`${(0,S.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderBlockEnd:"none"}},[`${t}-split ${t}-header`]:{borderBlockEnd:`${(0,S.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-split${t}-empty ${t}-footer`]:{borderTop:`${(0,S.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-loading ${t}-spin-nested-loading`]:{minHeight:o},[`${t}-split${t}-something-after-last-item ${n}-spin-container > ${t}-items > ${t}-item:last-child`]:{borderBlockEnd:`${(0,S.zA)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-lg ${t}-item`]:{padding:m},[`${t}-sm ${t}-item`]:{padding:d},[`${t}:not(${t}-vertical)`]:{[`${t}-item-no-flex`]:{[`${t}-item-action`]:{float:"right"}}}}},k=(0,O.OF)("List",(e=>{const t=(0,O.oX)(e,{listBorderedCls:`${e.componentCls}-bordered`,minHeight:e.controlHeightLG});return[w(t),A(t),B(t)]}),(e=>({contentWidth:220,itemPadding:`${(0,S.zA)(e.paddingContentVertical)} 0`,itemPaddingSM:`${(0,S.zA)(e.paddingContentVerticalSM)} ${(0,S.zA)(e.paddingContentHorizontal)}`,itemPaddingLG:`${(0,S.zA)(e.paddingContentVerticalLG)} ${(0,S.zA)(e.paddingContentHorizontalLG)}`,headerBg:"transparent",footerBg:"transparent",emptyTextPadding:e.padding,metaMarginBottom:e.padding,avatarMarginRight:e.padding,titleMarginBottom:e.paddingSM,descriptionFontSize:e.fontSize})));function z(e,t){const{pagination:n=!1,prefixCls:r,bordered:h=!1,split:v=!0,className:x,rootClassName:y,style:C,children:S,itemLayout:I,loadMore:O,grid:A,dataSource:B=[],size:w,header:z,footer:j,loading:E=!1,rowKey:H,renderItem:P,locale:N}=e,T=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(o=Object.getOwnPropertySymbols(e);i<o.length;i++)t.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(n[o[i]]=e[o[i]])}return n}(e,["pagination","prefixCls","bordered","split","className","rootClassName","style","children","itemLayout","loadMore","grid","dataSource","size","header","footer","loading","rowKey","renderItem","locale"]),M=n&&"object"==typeof n?n:{},[R,D]=i.useState(M.defaultCurrent||1),[W,L]=i.useState(M.defaultPageSize||10),{getPrefixCls:F,direction:G,className:X,style:q}=(0,d.TP)("list"),{renderEmpty:Y}=i.useContext(c.QO),V=e=>(t,o)=>{var i;D(t),L(o),n&&(null===(i=null==n?void 0:n[e])||void 0===i||i.call(n,t,o))},_=V("onChange"),Q=V("onShowSizeChange"),K=(e,t)=>{if(!P)return null;let n;return n="function"==typeof H?H(e):H?e[H]:e.key,n||(n=`list-item-${t}`),i.createElement(i.Fragment,{key:n},P(e,t))},U=!!(O||n||j),J=F("list",r),[Z,ee,te]=k(J);let ne=E;"boolean"==typeof ne&&(ne={spinning:ne});const oe=!!(null==ne?void 0:ne.spinning);let ie="";switch((0,u.A)(w)){case"large":ie="lg";break;case"small":ie="sm"}const re=l()(J,{[`${J}-vertical`]:"vertical"===I,[`${J}-${ie}`]:ie,[`${J}-split`]:v,[`${J}-bordered`]:h,[`${J}-loading`]:oe,[`${J}-grid`]:!!A,[`${J}-something-after-last-item`]:U,[`${J}-rtl`]:"rtl"===G},X,x,y,ee,te),le=(0,a.A)({current:1,total:0,position:"bottom"},{total:B.length,current:R,pageSize:W},n||{}),ae=Math.ceil(le.total/le.pageSize);le.current=Math.min(le.current,ae);const se=n&&i.createElement("div",{className:l()(`${J}-pagination`)},i.createElement(b.A,Object.assign({align:"end"},le,{onChange:_,onShowSizeChange:Q})));let ce=(0,o.A)(B);n&&B.length>(le.current-1)*le.pageSize&&(ce=(0,o.A)(B).splice((le.current-1)*le.pageSize,le.pageSize));const de=Object.keys(A||{}).some((e=>["xs","sm","md","lg","xl","xxl"].includes(e))),me=(0,g.A)(de),ue=i.useMemo((()=>{for(let e=0;e<s.ye.length;e+=1){const t=s.ye[e];if(me[t])return t}}),[me]),pe=i.useMemo((()=>{if(!A)return;const e=ue&&A[ue]?A[ue]:A.column;return e?{width:100/e+"%",maxWidth:100/e+"%"}:void 0}),[JSON.stringify(A),ue]);let ge=oe&&i.createElement("div",{style:{minHeight:53}});if(ce.length>0){const e=ce.map(K);ge=A?i.createElement(p.fI,{gutter:A.gutter},i.Children.map(e,(e=>i.createElement("div",{key:null==e?void 0:e.key,style:pe},e)))):i.createElement("ul",{className:`${J}-items`},e)}else S||oe||(ge=i.createElement("div",{className:`${J}-empty-text`},(null==N?void 0:N.emptyText)||(null==Y?void 0:Y("List"))||i.createElement(m.A,{componentName:"List"})));const be=le.position,$e=i.useMemo((()=>({grid:A,itemLayout:I})),[JSON.stringify(A),I]);return Z(i.createElement(f.Provider,{value:$e},i.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},q),C),className:re},T),("top"===be||"both"===be)&&se,z&&i.createElement("div",{className:`${J}-header`},z),i.createElement($.A,Object.assign({},ne),ge,S),j&&i.createElement("div",{className:`${J}-footer`},j),O||("bottom"===be||"both"===be)&&se)))}const j=i.forwardRef(z);j.Item=C;const E=j},60685:(e,t,n)=>{n.d(t,{A:()=>o});const o=(0,n(96540).createContext)(void 0)},61794:(e,t,n)=>{var o=n(96540),i=n(46942),r=n.n(i),l=n(95666),a=n(8719),s=n(96311),c=n(53425),d=n(58182),m=n(81168),u=(n(18877),n(38674)),p=n(35128),g=n(20934),b=n(94241),$=n(90124),f=n(29029),h=n(36891),v=n(81594),x=n(89222),y=n(25905),C=n(51113);const S=e=>{const{componentCls:t,colorTextDisabled:n,controlItemBgHover:o,controlPaddingHorizontal:i,colorText:r,motionDurationSlow:l,lineHeight:a,controlHeight:s,paddingInline:c,paddingBlock:d,fontSize:m,fontSizeIcon:u,colorIcon:p,colorTextQuaternary:g,colorBgElevated:b,paddingXXS:$,paddingLG:f,borderRadius:C,borderRadiusLG:S,boxShadowSecondary:I,itemPaddingVertical:O,calc:A}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,y.dF)(e)),(0,v.wj)(e)),{position:"relative",display:"inline-block",height:"auto",padding:0,overflow:"hidden",lineHeight:a,whiteSpace:"pre-wrap",verticalAlign:"bottom"}),(0,x.Eb)(e)),(0,x.sA)(e)),(0,x.lB)(e)),{"&-affix-wrapper":Object.assign(Object.assign({},(0,v.wj)(e)),{display:"inline-flex",padding:0,"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[`${t}-suffix`]:{position:"absolute",top:0,insetInlineEnd:c,bottom:0,zIndex:1,display:"inline-flex",alignItems:"center",margin:"auto"},[`&:has(${t}-suffix) > ${t} > textarea`]:{paddingInlineEnd:f},[`${t}-clear-icon`]:{position:"absolute",insetInlineEnd:0,insetBlockStart:A(m).mul(a).mul(.5).add(d).equal(),transform:"translateY(-50%)",margin:0,padding:0,color:g,fontSize:u,verticalAlign:-1,cursor:"pointer",transition:`color ${l}`,border:"none",outline:"none",backgroundColor:"transparent","&:hover":{color:p},"&:active":{color:r},"&-hidden":{visibility:"hidden"}}})}),(0,x.aP)(e)),{"&-disabled":{"> textarea":Object.assign({},(0,x.eT)(e))},[`&, &-affix-wrapper > ${t}`]:{[`> textarea, ${t}-measure`]:{color:r,boxSizing:"border-box",minHeight:e.calc(s).sub(2).equal(),margin:0,padding:`${(0,h.zA)(d)} ${(0,h.zA)(c)}`,overflow:"inherit",overflowX:"hidden",overflowY:"auto",fontWeight:"inherit",fontSize:"inherit",fontFamily:"inherit",fontStyle:"inherit",fontVariant:"inherit",fontSizeAdjust:"inherit",fontStretch:"inherit",lineHeight:"inherit",direction:"inherit",letterSpacing:"inherit",whiteSpace:"inherit",textAlign:"inherit",verticalAlign:"top",wordWrap:"break-word",wordBreak:"inherit",tabSize:"inherit"},"> textarea":Object.assign({width:"100%",border:"none",outline:"none",resize:"none",backgroundColor:"transparent"},(0,v.j_)(e.colorTextPlaceholder)),[`${t}-measure`]:{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:-1,color:"transparent",pointerEvents:"none","> span":{display:"inline-block",minHeight:"1em"}}},"&-dropdown":Object.assign(Object.assign({},(0,y.dF)(e)),{position:"absolute",top:-9999,insetInlineStart:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",fontSize:m,fontVariant:"initial",padding:$,backgroundColor:b,borderRadius:S,outline:"none",boxShadow:I,"&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.dropdownHeight,margin:0,paddingInlineStart:0,overflow:"auto",listStyle:"none",outline:"none","&-item":Object.assign(Object.assign({},y.L9),{position:"relative",display:"block",minWidth:e.controlItemWidth,padding:`${(0,h.zA)(O)} ${(0,h.zA)(i)}`,color:r,borderRadius:C,fontWeight:"normal",lineHeight:a,cursor:"pointer",transition:`background ${l} ease`,"&:hover":{backgroundColor:o},"&-disabled":{color:n,cursor:"not-allowed","&:hover":{color:n,backgroundColor:o,cursor:"not-allowed"}},"&-selected":{color:r,fontWeight:e.fontWeightStrong,backgroundColor:o},"&-active":{backgroundColor:o}})}})})}},I=(0,C.OF)("Mentions",(e=>{const t=(0,C.oX)(e,(0,v.C5)(e));return[S(t)]}),(e=>Object.assign(Object.assign({},(0,v.bi)(e)),{dropdownHeight:250,controlItemWidth:100,zIndexPopup:e.zIndexPopupBase+50,itemPaddingVertical:(e.controlHeight-e.fontHeight)/2})));const{Option:O}=l.A;function A(){return!0}const B=o.forwardRef(((e,t)=>{const{prefixCls:n,className:i,rootClassName:c,disabled:m,loading:h,filterOption:v,children:x,notFoundContent:y,options:C,status:S,allowClear:B=!1,popupClassName:w,style:k,variant:z}=e,j=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(o=Object.getOwnPropertySymbols(e);i<o.length;i++)t.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(n[o[i]]=e[o[i]])}return n}(e,["prefixCls","className","rootClassName","disabled","loading","filterOption","children","notFoundContent","options","status","allowClear","popupClassName","style","variant"]),[E,H]=o.useState(!1),P=o.useRef(null),N=(0,a.K4)(t,P),{getPrefixCls:T,renderEmpty:M,direction:R,mentions:D}=o.useContext(u.QO),{status:W,hasFeedback:L,feedbackIcon:F}=o.useContext(b.$W),G=(0,d.v)(W,S),X=o.useMemo((()=>void 0!==y?y:(null==M?void 0:M("Select"))||o.createElement(p.A,{componentName:"Select"})),[y,M]),q=o.useMemo((()=>h?o.createElement(O,{value:"ANTD_SEARCHING",disabled:!0},o.createElement(f.A,{size:"small"})):x),[h,x]),Y=h?[{value:"ANTD_SEARCHING",disabled:!0,label:o.createElement(f.A,{size:"small"})}]:C,V=h?A:v,_=T("mentions",n),Q=(0,s.A)(B),K=(0,g.A)(_),[U,J,Z]=I(_,K),[ee,te]=(0,$.A)("mentions",z),ne=L&&o.createElement(o.Fragment,null,F),oe=r()(null==D?void 0:D.className,i,c,Z,K);return U(o.createElement(l.A,Object.assign({silent:h,prefixCls:_,notFoundContent:X,className:oe,disabled:m,allowClear:Q,direction:R,style:Object.assign(Object.assign({},null==D?void 0:D.style),k)},j,{filterOption:V,onFocus:(...e)=>{j.onFocus&&j.onFocus.apply(j,e),H(!0)},onBlur:(...e)=>{j.onBlur&&j.onBlur.apply(j,e),H(!1)},dropdownClassName:r()(w,c,J,Z,K),ref:N,options:Y,suffix:ne,classNames:{mentions:r()({[`${_}-disabled`]:m,[`${_}-focused`]:E,[`${_}-rtl`]:"rtl"===R},J),variant:r()({[`${_}-${ee}`]:te},(0,d.L)(_,G)),affixWrapper:J}}),q))})),w=B;w.Option=O;const k=(0,c.A)(w,void 0,void 0,"mentions");w._InternalPanelDoNotUseOrYouWillBeFired=k,w.getMentions=(e="",t={})=>{const{prefix:n="@",split:o=" "}=t,i=(0,m.A)(n);return e.split(o).map(((e="")=>{let t=null;return i.some((n=>e.slice(0,n.length)===n&&(t=n,!0))),null!==t?{prefix:t,value:e.slice(t.length)}:null})).filter((e=>!!e&&!!e.value))}},87206:(e,t,n)=>{n.d(t,{A:()=>X});var o=n(96540),i=n(95391),r=n(71045),l=n(85539),a=n(46942),s=n.n(a),c=n(26956),d=n(19853),m=n(23723),u=n(40682),p=(n(18877),n(38674)),g=n(20934);const b=(0,o.createContext)({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});const $=e=>{const{prefixCls:t,className:n,dashed:r}=e,l=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(o=Object.getOwnPropertySymbols(e);i<o.length;i++)t.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(n[o[i]]=e[o[i]])}return n}(e,["prefixCls","className","dashed"]),{getPrefixCls:a}=o.useContext(p.QO),c=a("menu",t),d=s()({[`${c}-item-divider-dashed`]:!!r},n);return o.createElement(i.cG,Object.assign({className:d},l))};var f=n(82546),h=n(37977);const v=e=>{var t;const{className:n,children:l,icon:a,title:c,danger:m,extra:p}=e,{prefixCls:g,firstLevel:$,direction:v,disableMenuItemTitleTooltip:x,inlineCollapsed:y}=o.useContext(b),{siderCollapsed:C}=o.useContext(r.P);let S=c;void 0===c?S=$?l:"":!1===c&&(S="");const I={title:S};C||y||(I.title=null,I.open=!1);const O=(0,f.A)(l).length;let A=o.createElement(i.q7,Object.assign({},(0,d.A)(e,["title","icon","danger"]),{className:s()({[`${g}-item-danger`]:m,[`${g}-item-only-child`]:1===(a?O+1:O)},n),title:"string"==typeof c?c:void 0}),(0,u.Ob)(a,{className:s()(o.isValidElement(a)?null===(t=a.props)||void 0===t?void 0:t.className:"",`${g}-item-icon`)}),(e=>{const t=null==l?void 0:l[0],n=o.createElement("span",{className:s()(`${g}-title-content`,{[`${g}-title-content-with-extra`]:!!p||0===p})},l);return(!a||o.isValidElement(l)&&"span"===l.type)&&l&&e&&$&&"string"==typeof t?o.createElement("div",{className:`${g}-inline-collapsed-noicon`},t.charAt(0)):n})(y));return x||(A=o.createElement(h.A,Object.assign({},I,{placement:"rtl"===v?"left":"right",classNames:{root:`${g}-inline-collapsed-tooltip`}}),A)),A};var x=n(96476),y=n(36891),C=n(77020),S=n(25905),I=n(38328),O=n(51113);const A=e=>{const{componentCls:t,motionDurationSlow:n,horizontalLineHeight:o,colorSplit:i,lineWidth:r,lineType:l,itemPaddingInline:a}=e;return{[`${t}-horizontal`]:{lineHeight:o,border:0,borderBottom:`${(0,y.zA)(r)} ${l} ${i}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:a},[`> ${t}-item:hover,\n        > ${t}-item-active,\n        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:[`border-color ${n}`,`background ${n}`].join(",")},[`${t}-submenu-arrow`]:{display:"none"}}}},B=({componentCls:e,menuArrowOffset:t,calc:n})=>({[`${e}-rtl`]:{direction:"rtl"},[`${e}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${e}-rtl${e}-vertical,\n    ${e}-submenu-rtl ${e}-vertical`]:{[`${e}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${(0,y.zA)(n(t).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${(0,y.zA)(t)})`}}}}),w=e=>Object.assign({},(0,S.jk)(e)),k=(e,t)=>{const{componentCls:n,itemColor:o,itemSelectedColor:i,subMenuItemSelectedColor:r,groupTitleColor:l,itemBg:a,subMenuItemBg:s,itemSelectedBg:c,activeBarHeight:d,activeBarWidth:m,activeBarBorderWidth:u,motionDurationSlow:p,motionEaseInOut:g,motionEaseOut:b,itemPaddingInline:$,motionDurationMid:f,itemHoverColor:h,lineType:v,colorSplit:x,itemDisabledColor:C,dangerItemColor:S,dangerItemHoverColor:I,dangerItemSelectedColor:O,dangerItemActiveBg:A,dangerItemSelectedBg:B,popupBg:k,itemHoverBg:z,itemActiveBg:j,menuSubMenuBg:E,horizontalItemSelectedColor:H,horizontalItemSelectedBg:P,horizontalItemBorderRadius:N,horizontalItemHoverBg:T}=e;return{[`${n}-${t}, ${n}-${t} > ${n}`]:{color:o,background:a,[`&${n}-root:focus-visible`]:Object.assign({},w(e)),[`${n}-item`]:{"&-group-title, &-extra":{color:l}},[`${n}-submenu-selected > ${n}-submenu-title`]:{color:r},[`${n}-item, ${n}-submenu-title`]:{color:o,[`&:not(${n}-item-disabled):focus-visible`]:Object.assign({},w(e))},[`${n}-item-disabled, ${n}-submenu-disabled`]:{color:`${C} !important`},[`${n}-item:not(${n}-item-selected):not(${n}-submenu-selected)`]:{[`&:hover, > ${n}-submenu-title:hover`]:{color:h}},[`&:not(${n}-horizontal)`]:{[`${n}-item:not(${n}-item-selected)`]:{"&:hover":{backgroundColor:z},"&:active":{backgroundColor:j}},[`${n}-submenu-title`]:{"&:hover":{backgroundColor:z},"&:active":{backgroundColor:j}}},[`${n}-item-danger`]:{color:S,[`&${n}-item:hover`]:{[`&:not(${n}-item-selected):not(${n}-submenu-selected)`]:{color:I}},[`&${n}-item:active`]:{background:A}},[`${n}-item a`]:{"&, &:hover":{color:"inherit"}},[`${n}-item-selected`]:{color:i,[`&${n}-item-danger`]:{color:O},"a, a:hover":{color:"inherit"}},[`& ${n}-item-selected`]:{backgroundColor:c,[`&${n}-item-danger`]:{backgroundColor:B}},[`&${n}-submenu > ${n}`]:{backgroundColor:E},[`&${n}-popup > ${n}`]:{backgroundColor:k},[`&${n}-submenu-popup > ${n}`]:{backgroundColor:k},[`&${n}-horizontal`]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{[`> ${n}-item, > ${n}-submenu`]:{top:u,marginTop:e.calc(u).mul(-1).equal(),marginBottom:0,borderRadius:N,"&::after":{position:"absolute",insetInline:$,bottom:0,borderBottom:`${(0,y.zA)(d)} solid transparent`,transition:`border-color ${p} ${g}`,content:'""'},"&:hover, &-active, &-open":{background:T,"&::after":{borderBottomWidth:d,borderBottomColor:H}},"&-selected":{color:H,backgroundColor:P,"&:hover":{backgroundColor:P},"&::after":{borderBottomWidth:d,borderBottomColor:H}}}}),[`&${n}-root`]:{[`&${n}-inline, &${n}-vertical`]:{borderInlineEnd:`${(0,y.zA)(u)} ${v} ${x}`}},[`&${n}-inline`]:{[`${n}-sub${n}-inline`]:{background:s},[`${n}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${(0,y.zA)(m)} solid ${i}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${f} ${b}`,`opacity ${f} ${b}`].join(","),content:'""'},[`&${n}-item-danger`]:{"&::after":{borderInlineEndColor:O}}},[`${n}-selected, ${n}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${f} ${g}`,`opacity ${f} ${g}`].join(",")}}}}}},z=e=>{const{componentCls:t,itemHeight:n,itemMarginInline:o,padding:i,menuArrowSize:r,marginXS:l,itemMarginBlock:a,itemWidth:s,itemPaddingInline:c}=e,d=e.calc(r).add(i).add(l).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:n,lineHeight:(0,y.zA)(n),paddingInline:c,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:a,width:s},[`> ${t}-item,\n            > ${t}-submenu > ${t}-submenu-title`]:{height:n,lineHeight:(0,y.zA)(n)},[`${t}-item-group-list ${t}-submenu-title,\n            ${t}-submenu-title`]:{paddingInlineEnd:d}}},j=e=>{const{componentCls:t,iconCls:n,itemHeight:o,colorTextLightSolid:i,dropdownWidth:r,controlHeightLG:l,motionEaseOut:a,paddingXL:s,itemMarginInline:c,fontSizeLG:d,motionDurationFast:m,motionDurationSlow:u,paddingXS:p,boxShadowSecondary:g,collapsedWidth:b,collapsedIconSize:$}=e,f={height:o,lineHeight:(0,y.zA)(o),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},z(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},z(e)),{boxShadow:g})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:r,maxHeight:`calc(100vh - ${(0,y.zA)(e.calc(l).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${u}`,`background ${u}`,`padding ${m} ${a}`].join(","),[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:f,[`& ${t}-item-group-title`]:{paddingInlineStart:s}},[`${t}-item`]:f}},{[`${t}-inline-collapsed`]:{width:b,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:d,textAlign:"center"}}},[`> ${t}-item,\n          > ${t}-item-group > ${t}-item-group-list > ${t}-item,\n          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,\n          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${(0,y.zA)(e.calc($).div(2).equal())} - ${(0,y.zA)(c)})`,textOverflow:"clip",[`\n            ${t}-submenu-arrow,\n            ${t}-submenu-expand-icon\n          `]:{opacity:0},[`${t}-item-icon, ${n}`]:{margin:0,fontSize:$,lineHeight:(0,y.zA)(o),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${n}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${n}`]:{display:"none"},"a, a:hover":{color:i}},[`${t}-item-group-title`]:Object.assign(Object.assign({},S.L9),{paddingInline:p})}}]},E=e=>{const{componentCls:t,motionDurationSlow:n,motionDurationMid:o,motionEaseInOut:i,motionEaseOut:r,iconCls:l,iconSize:a,iconMarginInlineEnd:s}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${n}`,`background ${n}`,`padding calc(${n} + 0.1s) ${i}`].join(","),[`${t}-item-icon, ${l}`]:{minWidth:a,fontSize:a,transition:[`font-size ${o} ${r}`,`margin ${n} ${i}`,`color ${n}`].join(","),"+ span":{marginInlineStart:s,opacity:1,transition:[`opacity ${n} ${i}`,`margin ${n}`,`color ${n}`].join(",")}},[`${t}-item-icon`]:Object.assign({},(0,S.Nk)()),[`&${t}-item-only-child`]:{[`> ${l}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},H=e=>{const{componentCls:t,motionDurationSlow:n,motionEaseInOut:o,borderRadius:i,menuArrowSize:r,menuArrowOffset:l}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:r,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${n} ${o}, opacity ${n}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(r).mul(.6).equal(),height:e.calc(r).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:i,transition:[`background ${n} ${o}`,`transform ${n} ${o}`,`top ${n} ${o}`,`color ${n} ${o}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(${(0,y.zA)(e.calc(l).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${(0,y.zA)(l)})`}}}}},P=e=>{const{antCls:t,componentCls:n,fontSize:o,motionDurationSlow:i,motionDurationMid:r,motionEaseInOut:l,paddingXS:a,padding:s,colorSplit:c,lineWidth:d,zIndexPopup:m,borderRadiusLG:u,subMenuItemBorderRadius:p,menuArrowSize:g,menuArrowOffset:b,lineType:$,groupTitleLineHeight:f,groupTitleFontSize:h}=e;return[{"":{[n]:Object.assign(Object.assign({},(0,S.t6)()),{"&-hidden":{display:"none"}})},[`${n}-submenu-hidden`]:{display:"none"}},{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,S.dF)(e)),(0,S.t6)()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${i} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${n}-item`]:{flex:"none"}},[`${n}-item, ${n}-submenu, ${n}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${n}-item-group-title`]:{padding:`${(0,y.zA)(a)} ${(0,y.zA)(s)}`,fontSize:h,lineHeight:f,transition:`all ${i}`},[`&-horizontal ${n}-submenu`]:{transition:[`border-color ${i} ${l}`,`background ${i} ${l}`].join(",")},[`${n}-submenu, ${n}-submenu-inline`]:{transition:[`border-color ${i} ${l}`,`background ${i} ${l}`,`padding ${r} ${l}`].join(",")},[`${n}-submenu ${n}-sub`]:{cursor:"initial",transition:[`background ${i} ${l}`,`padding ${i} ${l}`].join(",")},[`${n}-title-content`]:{transition:`color ${i}`,"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"},[`${n}-item-extra`]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},[`${n}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${n}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:c,borderStyle:$,borderWidth:0,borderTopWidth:d,marginBlock:d,padding:0,"&-dashed":{borderStyle:"dashed"}}}),E(e)),{[`${n}-item-group`]:{[`${n}-item-group-list`]:{margin:0,padding:0,[`${n}-item, ${n}-submenu-title`]:{paddingInline:`${(0,y.zA)(e.calc(o).mul(2).equal())} ${(0,y.zA)(s)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:m,borderRadius:u,boxShadow:"none",transformOrigin:"0 0",[`&${n}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${n}`]:Object.assign(Object.assign(Object.assign({borderRadius:u},E(e)),H(e)),{[`${n}-item, ${n}-submenu > ${n}-submenu-title`]:{borderRadius:p},[`${n}-submenu-title::after`]:{transition:`transform ${i} ${l}`}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),H(e)),{[`&-inline-collapsed ${n}-submenu-arrow,\n        &-inline ${n}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${(0,y.zA)(b)})`},"&::after":{transform:`rotate(45deg) translateX(${(0,y.zA)(e.calc(b).mul(-1).equal())})`}},[`${n}-submenu-open${n}-submenu-inline > ${n}-submenu-title > ${n}-submenu-arrow`]:{transform:`translateY(${(0,y.zA)(e.calc(g).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${(0,y.zA)(e.calc(b).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${(0,y.zA)(b)})`}}})},{[`${t}-layout-header`]:{[n]:{lineHeight:"inherit"}}}]},N=e=>{var t,n,o;const{colorPrimary:i,colorError:r,colorTextDisabled:l,colorErrorBg:a,colorText:s,colorTextDescription:c,colorBgContainer:d,colorFillAlter:m,colorFillContent:u,lineWidth:p,lineWidthBold:g,controlItemBgActive:b,colorBgTextHover:$,controlHeightLG:f,lineHeight:h,colorBgElevated:v,marginXXS:x,padding:y,fontSize:S,controlHeightSM:I,fontSizeLG:O,colorTextLightSolid:A,colorErrorHover:B}=e,w=null!==(t=e.activeBarWidth)&&void 0!==t?t:0,k=null!==(n=e.activeBarBorderWidth)&&void 0!==n?n:p,z=null!==(o=e.itemMarginInline)&&void 0!==o?o:e.marginXXS,j=new C.Y(A).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:s,itemColor:s,colorItemTextHover:s,itemHoverColor:s,colorItemTextHoverHorizontal:i,horizontalItemHoverColor:i,colorGroupTitle:c,groupTitleColor:c,colorItemTextSelected:i,itemSelectedColor:i,subMenuItemSelectedColor:i,colorItemTextSelectedHorizontal:i,horizontalItemSelectedColor:i,colorItemBg:d,itemBg:d,colorItemBgHover:$,itemHoverBg:$,colorItemBgActive:u,itemActiveBg:b,colorSubItemBg:m,subMenuItemBg:m,colorItemBgSelected:b,itemSelectedBg:b,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:w,colorActiveBarHeight:g,activeBarHeight:g,colorActiveBarBorderSize:p,activeBarBorderWidth:k,colorItemTextDisabled:l,itemDisabledColor:l,colorDangerItemText:r,dangerItemColor:r,colorDangerItemTextHover:r,dangerItemHoverColor:r,colorDangerItemTextSelected:r,dangerItemSelectedColor:r,colorDangerItemBgActive:a,dangerItemActiveBg:a,colorDangerItemBgSelected:a,dangerItemSelectedBg:a,itemMarginInline:z,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:f,groupTitleLineHeight:h,collapsedWidth:2*f,popupBg:v,itemMarginBlock:x,itemPaddingInline:y,horizontalLineHeight:1.15*f+"px",iconSize:S,iconMarginInlineEnd:I-S,collapsedIconSize:O,groupTitleFontSize:S,darkItemDisabledColor:new C.Y(A).setA(.25).toRgbString(),darkItemColor:j,darkDangerItemColor:r,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:A,darkItemSelectedBg:i,darkDangerItemSelectedBg:r,darkItemHoverBg:"transparent",darkGroupTitleColor:j,darkItemHoverColor:A,darkDangerItemHoverColor:B,darkDangerItemSelectedColor:A,darkDangerItemActiveBg:r,itemWidth:w?`calc(100% + ${k}px)`:`calc(100% - ${2*z}px)`}},T=(e,t=e,n=!0)=>(0,O.OF)("Menu",(e=>{const{colorBgElevated:t,controlHeightLG:n,fontSize:o,darkItemColor:i,darkDangerItemColor:r,darkItemBg:l,darkSubMenuItemBg:a,darkItemSelectedColor:s,darkItemSelectedBg:c,darkDangerItemSelectedBg:d,darkItemHoverBg:m,darkGroupTitleColor:u,darkItemHoverColor:p,darkItemDisabledColor:g,darkDangerItemHoverColor:b,darkDangerItemSelectedColor:$,darkDangerItemActiveBg:f,popupBg:h,darkPopupBg:v}=e,x=e.calc(o).div(7).mul(5).equal(),y=(0,O.oX)(e,{menuArrowSize:x,menuHorizontalHeight:e.calc(n).mul(1.15).equal(),menuArrowOffset:e.calc(x).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:h}),C=(0,O.oX)(y,{itemColor:i,itemHoverColor:p,groupTitleColor:u,itemSelectedColor:s,subMenuItemSelectedColor:s,itemBg:l,popupBg:v,subMenuItemBg:a,itemActiveBg:"transparent",itemSelectedBg:c,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:m,itemDisabledColor:g,dangerItemColor:r,dangerItemHoverColor:b,dangerItemSelectedColor:$,dangerItemActiveBg:f,dangerItemSelectedBg:d,menuSubMenuBg:a,horizontalItemSelectedColor:s,horizontalItemSelectedBg:c});return[P(y),A(y),j(y),k(y,"light"),k(C,"dark"),B(y),(0,I.eG)(y),(0,I._j)(y,"slide-up"),(0,I._j)(y,"slide-down"),(0,I.aB)(y,"zoom-big")]}),N,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:n,unitless:{groupTitleLineHeight:!0}})(e,t);var M=n(60275);const R=e=>{var t;const{popupClassName:n,icon:r,title:l,theme:a}=e,c=o.useContext(b),{prefixCls:m,inlineCollapsed:p,theme:g}=c,$=(0,i.Wj)();let f;if(r){const e=o.isValidElement(l)&&"span"===l.type;f=o.createElement(o.Fragment,null,(0,u.Ob)(r,{className:s()(o.isValidElement(r)?null===(t=r.props)||void 0===t?void 0:t.className:"",`${m}-item-icon`)}),e?l:o.createElement("span",{className:`${m}-title-content`},l))}else f=p&&!$.length&&l&&"string"==typeof l?o.createElement("div",{className:`${m}-inline-collapsed-noicon`},l.charAt(0)):o.createElement("span",{className:`${m}-title-content`},l);const h=o.useMemo((()=>Object.assign(Object.assign({},c),{firstLevel:!1})),[c]),[v]=(0,M.YK)("Menu");return o.createElement(b.Provider,{value:h},o.createElement(i.g8,Object.assign({},(0,d.A)(e,["icon"]),{title:f,popupClassName:s()(m,n,`${m}-${a||g}`),popupStyle:Object.assign({zIndex:v},e.popupStyle)})))};function D(e){return null===e||!1===e}const W={item:v,submenu:R,divider:$},L=(0,o.forwardRef)(((e,t)=>{var n;const r=o.useContext(x.h),a=r||{},{getPrefixCls:$,getPopupContainer:f,direction:h,menu:v}=o.useContext(p.QO),y=$(),{prefixCls:C,className:S,style:I,theme:O="light",expandIcon:A,_internalDisableMenuItemTitleTooltip:B,inlineCollapsed:w,siderCollapsed:k,rootClassName:z,mode:j,selectable:E,onClick:H,overflowedIndicatorPopupClassName:P}=e,N=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(o=Object.getOwnPropertySymbols(e);i<o.length;i++)t.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(n[o[i]]=e[o[i]])}return n}(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),M=(0,d.A)(N,["collapsedWidth"]);null===(n=a.validator)||void 0===n||n.call(a,{mode:j});const R=(0,c.A)(((...e)=>{var t;null==H||H.apply(void 0,e),null===(t=a.onClick)||void 0===t||t.call(a)})),L=a.mode||j,F=null!=E?E:a.selectable,G=null!=w?w:k,X={horizontal:{motionName:`${y}-slide-up`},inline:(0,m.A)(y),other:{motionName:`${y}-zoom-big`}},q=$("menu",C||a.prefixCls),Y=(0,g.A)(q),[V,_,Q]=T(q,Y,!r),K=s()(`${q}-${O}`,null==v?void 0:v.className,S),U=o.useMemo((()=>{var e,t;if("function"==typeof A||D(A))return A||null;if("function"==typeof a.expandIcon||D(a.expandIcon))return a.expandIcon||null;if("function"==typeof(null==v?void 0:v.expandIcon)||D(null==v?void 0:v.expandIcon))return(null==v?void 0:v.expandIcon)||null;const n=null!==(e=null!=A?A:null==a?void 0:a.expandIcon)&&void 0!==e?e:null==v?void 0:v.expandIcon;return(0,u.Ob)(n,{className:s()(`${q}-submenu-expand-icon`,o.isValidElement(n)?null===(t=n.props)||void 0===t?void 0:t.className:void 0)})}),[A,null==a?void 0:a.expandIcon,null==v?void 0:v.expandIcon,q]),J=o.useMemo((()=>({prefixCls:q,inlineCollapsed:G||!1,direction:h,firstLevel:!0,theme:O,mode:L,disableMenuItemTitleTooltip:B})),[q,G,h,B,O]);return V(o.createElement(x.h.Provider,{value:null},o.createElement(b.Provider,{value:J},o.createElement(i.Ay,Object.assign({getPopupContainer:f,overflowedIndicator:o.createElement(l.A,null),overflowedIndicatorPopupClassName:s()(q,`${q}-${O}`,P),mode:L,selectable:F,onClick:R},M,{inlineCollapsed:G,style:Object.assign(Object.assign({},null==v?void 0:v.style),I),className:K,prefixCls:q,direction:h,defaultMotions:X,expandIcon:U,ref:t,rootClassName:s()(z,_,a.rootClassName,Q,Y),_internalComponents:W})))))})),F=L,G=(0,o.forwardRef)(((e,t)=>{const n=(0,o.useRef)(null),i=o.useContext(r.P);return(0,o.useImperativeHandle)(t,(()=>({menu:n.current,focus:e=>{var t;null===(t=n.current)||void 0===t||t.focus(e)}}))),o.createElement(F,Object.assign({ref:n},e,i))}));G.Item=v,G.SubMenu=R,G.Divider=$,G.ItemGroup=i.te;const X=G},87959:(e,t,n)=>{n.d(t,{Ay:()=>x});var o=n(60436),i=n(96540),r=n(41240),l=n(38674),a=n(71919),s=n(5903),c=n(89585),d=n(25783);let m=null,u=e=>e(),p=[],g={};function b(){const{getContainer:e,duration:t,rtl:n,maxCount:o,top:i}=g,r=(null==e?void 0:e())||document.body;return{getContainer:()=>r,duration:t,rtl:n,maxCount:o,top:i}}const $=i.forwardRef(((e,t)=>{const{messageConfig:n,sync:o}=e,{getPrefixCls:a}=(0,i.useContext)(l.QO),s=g.prefixCls||a("message"),d=(0,i.useContext)(r.B),[m,u]=(0,c.y)(Object.assign(Object.assign(Object.assign({},n),{prefixCls:s}),d.message));return i.useImperativeHandle(t,(()=>{const e=Object.assign({},m);return Object.keys(e).forEach((t=>{e[t]=(...e)=>(o(),m[t].apply(m,e))})),{instance:e,sync:o}})),u})),f=i.forwardRef(((e,t)=>{const[n,o]=i.useState(b),r=()=>{o(b)};i.useEffect(r,[]);const a=(0,l.cr)(),s=a.getRootPrefixCls(),c=a.getIconPrefixCls(),d=a.getTheme(),m=i.createElement($,{ref:t,sync:r,messageConfig:n});return i.createElement(l.Ay,{prefixCls:s,iconPrefixCls:c,theme:d},a.holderRender?a.holderRender(m):m)}));function h(){if(!m){const e=document.createDocumentFragment(),t={fragment:e};return m=t,void u((()=>{(0,a.L)()(i.createElement(f,{ref:e=>{const{instance:n,sync:o}=e||{};Promise.resolve().then((()=>{!t.instance&&n&&(t.instance=n,t.sync=o,h())}))}}),e)}))}m.instance&&(p.forEach((e=>{const{type:t,skipped:n}=e;if(!n)switch(t){case"open":u((()=>{const t=m.instance.open(Object.assign(Object.assign({},g),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)}));break;case"destroy":u((()=>{null==m||m.instance.destroy(e.key)}));break;default:u((()=>{var n;const i=(n=m.instance)[t].apply(n,(0,o.A)(e.args));null==i||i.then(e.resolve),e.setCloseFn(i)}))}})),p=[])}const v={open:function(e){const t=(0,d.E)((t=>{let n;const o={type:"open",config:e,resolve:t,setCloseFn:e=>{n=e}};return p.push(o),()=>{n?u((()=>{n()})):o.skipped=!0}}));return h(),t},destroy:e=>{p.push({type:"destroy",key:e}),h()},config:function(e){g=Object.assign(Object.assign({},g),e),u((()=>{var e;null===(e=null==m?void 0:m.sync)||void 0===e||e.call(m)}))},useMessage:c.A,_InternalPanelDoNotUseOrYouWillBeFired:s.Ay};["success","info","warning","error","loading"].forEach((e=>{v[e]=(...t)=>function(e,t){(0,l.cr)();const n=(0,d.E)((n=>{let o;const i={type:e,args:t,resolve:n,setCloseFn:e=>{o=e}};return p.push(i),()=>{o?u((()=>{o()})):i.skipped=!0}}));return h(),n}(e,t)}));const x=v},89585:(e,t,n)=>{n.d(t,{A:()=>y,y:()=>x});var o=n(96540),i=n(55886),r=n(46942),l=n.n(r),a=n(22370),s=n(18877),c=n(38674),d=n(20934),m=n(5903),u=n(98889),p=n(25783),g=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(o=Object.getOwnPropertySymbols(e);i<o.length;i++)t.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(n[o[i]]=e[o[i]])}return n};const b=3,$=({children:e,prefixCls:t})=>{const n=(0,d.A)(t),[i,r,s]=(0,u.A)(t,n);return i(o.createElement(a.ph,{classNames:{list:l()(r,s,n)}},e))},f=(e,{prefixCls:t,key:n})=>o.createElement($,{prefixCls:t,key:n},e),h=o.forwardRef(((e,t)=>{const{top:n,prefixCls:r,getContainer:s,maxCount:d,duration:m=b,rtl:u,transitionName:g,onAllRemoved:$}=e,{getPrefixCls:h,getPopupContainer:v,message:x,direction:y}=o.useContext(c.QO),C=r||h("message"),S=o.createElement("span",{className:`${C}-close-x`},o.createElement(i.A,{className:`${C}-close-icon`})),[I,O]=(0,a.hN)({prefixCls:C,style:()=>({left:"50%",transform:"translateX(-50%)",top:null!=n?n:8}),className:()=>l()({[`${C}-rtl`]:null!=u?u:"rtl"===y}),motion:()=>(0,p.V)(C,g),closable:!1,closeIcon:S,duration:m,getContainer:()=>(null==s?void 0:s())||(null==v?void 0:v())||document.body,maxCount:d,onAllRemoved:$,renderNotifications:f});return o.useImperativeHandle(t,(()=>Object.assign(Object.assign({},I),{prefixCls:C,message:x}))),O}));let v=0;function x(e){const t=o.useRef(null);return(0,s.rJ)("Message"),[o.useMemo((()=>{const e=e=>{var n;null===(n=t.current)||void 0===n||n.close(e)},n=n=>{if(!t.current){const e=()=>{};return e.then=()=>{},e}const{open:i,prefixCls:r,message:a}=t.current,s=`${r}-notice`,{content:c,icon:d,type:u,key:b,className:$,style:f,onClose:h}=n,x=g(n,["content","icon","type","key","className","style","onClose"]);let y=b;return null==y&&(v+=1,y=`antd-message-${v}`),(0,p.E)((t=>(i(Object.assign(Object.assign({},x),{key:y,content:o.createElement(m.Mb,{prefixCls:r,type:u,icon:d},c),placement:"top",className:l()(u&&`${s}-${u}`,$,null==a?void 0:a.className),style:Object.assign(Object.assign({},null==a?void 0:a.style),f),onClose:()=>{null==h||h(),t()}})),()=>{e(y)})))},i={open:n,destroy:n=>{var o;void 0!==n?e(n):null===(o=t.current)||void 0===o||o.destroy()}};return["info","success","warning","error","loading"].forEach((e=>{i[e]=(t,o,i)=>{let r,l,a;r=t&&"object"==typeof t&&"content"in t?t:{content:t},"function"==typeof o?a=o:(l=o,a=i);const s=Object.assign(Object.assign({onClose:a,duration:l},r),{type:e});return n(s)}})),i}),[]),o.createElement(h,Object.assign({key:"message-holder"},e,{ref:t}))]}function y(e){return x(e)}},96476:(e,t,n)=>{n.d(t,{A:()=>a,h:()=>s});var o=n(96540),i=n(8719),r=n(62897);const l=o.createContext(null),a=o.forwardRef(((e,t)=>{const{children:n}=e,a=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(o=Object.getOwnPropertySymbols(e);i<o.length;i++)t.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(n[o[i]]=e[o[i]])}return n}(e,["children"]),s=o.useContext(l),c=o.useMemo((()=>Object.assign(Object.assign({},s),a)),[s,a.prefixCls,a.mode,a.selectable,a.rootClassName]),d=(0,i.H3)(n),m=(0,i.xK)(t,d?(0,i.A9)(n):null);return o.createElement(l.Provider,{value:c},o.createElement(r.A,{space:!0},d?o.cloneElement(n,{ref:m}):n))})),s=l},98889:(e,t,n)=>{n.d(t,{A:()=>s});var o=n(36891),i=n(60275),r=n(25905),l=n(51113);const a=e=>{const{componentCls:t,iconCls:n,boxShadow:i,colorText:l,colorSuccess:a,colorError:s,colorWarning:c,colorInfo:d,fontSizeLG:m,motionEaseInOutCirc:u,motionDurationSlow:p,marginXS:g,paddingXS:b,borderRadiusLG:$,zIndexPopup:f,contentPadding:h,contentBg:v}=e,x=`${t}-notice`,y=new o.Mo("MessageMoveIn",{"0%":{padding:0,transform:"translateY(-100%)",opacity:0},"100%":{padding:b,transform:"translateY(0)",opacity:1}}),C=new o.Mo("MessageMoveOut",{"0%":{maxHeight:e.height,padding:b,opacity:1},"100%":{maxHeight:0,padding:0,opacity:0}}),S={padding:b,textAlign:"center",[`${t}-custom-content`]:{display:"flex",alignItems:"center"},[`${t}-custom-content > ${n}`]:{marginInlineEnd:g,fontSize:m},[`${x}-content`]:{display:"inline-block",padding:h,background:v,borderRadius:$,boxShadow:i,pointerEvents:"all"},[`${t}-success > ${n}`]:{color:a},[`${t}-error > ${n}`]:{color:s},[`${t}-warning > ${n}`]:{color:c},[`${t}-info > ${n},\n      ${t}-loading > ${n}`]:{color:d}};return[{[t]:Object.assign(Object.assign({},(0,r.dF)(e)),{color:l,position:"fixed",top:g,width:"100%",pointerEvents:"none",zIndex:f,[`${t}-move-up`]:{animationFillMode:"forwards"},[`\n        ${t}-move-up-appear,\n        ${t}-move-up-enter\n      `]:{animationName:y,animationDuration:p,animationPlayState:"paused",animationTimingFunction:u},[`\n        ${t}-move-up-appear${t}-move-up-appear-active,\n        ${t}-move-up-enter${t}-move-up-enter-active\n      `]:{animationPlayState:"running"},[`${t}-move-up-leave`]:{animationName:C,animationDuration:p,animationPlayState:"paused",animationTimingFunction:u},[`${t}-move-up-leave${t}-move-up-leave-active`]:{animationPlayState:"running"},"&-rtl":{direction:"rtl",span:{direction:"rtl"}}})},{[t]:{[`${x}-wrapper`]:Object.assign({},S)}},{[`${t}-notice-pure-panel`]:Object.assign(Object.assign({},S),{padding:0,textAlign:"start"})}]},s=(0,l.OF)("Message",(e=>{const t=(0,l.oX)(e,{height:150});return[a(t)]}),(e=>({zIndexPopup:e.zIndexPopupBase+i.jH+10,contentBg:e.colorBgElevated,contentPadding:`${(e.controlHeightLG-e.fontSize*e.lineHeight)/2}px ${e.paddingSM}px`})))}}]);