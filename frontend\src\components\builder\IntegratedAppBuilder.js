/**
 * Integrated App Builder
 * 
 * Main App Builder component that integrates all UI/UX improvements
 * with existing features including WebSocket collaboration, template system,
 * code export, tutorial assistant, and AI design suggestions.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Layout, message, Spin, Alert } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import styled from 'styled-components';

// Enhanced UI/UX Components
import ResponsiveAppLayout from '../layout/ResponsiveAppLayout';
import AccessibleComponent from '../common/AccessibleComponent';
import UXEnhancedComponentPalette from './UXEnhancedComponentPalette';
import UXEnhancedPropertyEditor from './UXEnhancedPropertyEditor';
import UXEnhancedPreviewArea from './UXEnhancedPreviewArea';
import { DragDropProvider } from '../dragdrop/EnhancedDragDropSystem';

// Existing Feature Components
import TutorialAssistant from '../tutorial/TutorialAssistant';
import AIDesignSuggestions from '../ai/AIDesignSuggestions';
import TemplateManager from '../templates/TemplateManager';
import CodeExporter from '../export/CodeExporter';
import CollaborationIndicator from '../collaboration/CollaborationIndicator';

// Design System
import { theme, visualHierarchy } from '../../design-system';

// Hooks and Services
import useWebSocket from '../../hooks/useWebSocket';
import useAppBuilder from '../../hooks/useAppBuilder';
import useTutorial from '../../hooks/useTutorial';
import useAIDesignSuggestions from '../../hooks/useAIDesignSuggestions';
import useTemplates from '../../hooks/useTemplates';
import useCodeExport from '../../hooks/useCodeExport';
import useCollaboration from '../../hooks/useCollaboration';

const IntegratedContainer = styled.div`
  height: 100vh;
  background: ${theme.colors.background.default};
  position: relative;
  overflow: hidden;
  
  /* Ensure proper stacking context */
  z-index: 0;
`;

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[4]};
  flex: 1;
  
  .app-title {
    ${visualHierarchy.typography.heading.h4};
    margin: 0;
    color: ${theme.colors.text.primary};
  }
  
  .project-name {
    ${visualHierarchy.typography.body.small};
    color: ${theme.colors.text.secondary};
    margin-left: ${theme.spacing[2]};
  }
`;

const FeatureToggles = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[2]};
  
  ${theme.mediaQueries.maxMd} {
    gap: ${theme.spacing[1]};
  }
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: ${theme.zIndex.modal};
  
  .loading-text {
    margin-top: ${theme.spacing[4]};
    ${visualHierarchy.typography.body.medium};
    color: ${theme.colors.text.secondary};
  }
`;

const ErrorBoundary = styled.div`
  padding: ${theme.spacing[8]};
  text-align: center;
  
  .error-title {
    ${visualHierarchy.typography.heading.h3};
    color: ${theme.colors.error.main};
    margin-bottom: ${theme.spacing[4]};
  }
  
  .error-message {
    ${visualHierarchy.typography.body.medium};
    color: ${theme.colors.text.secondary};
    margin-bottom: ${theme.spacing[6]};
  }
`;

export default function IntegratedAppBuilder({
  projectId,
  initialComponents = [],
  enableFeatures = {
    websocket: true,
    tutorial: true,
    aiSuggestions: true,
    templates: true,
    codeExport: true,
    collaboration: true,
  },
  onSave,
  onLoad,
  onError,
}) {
  // Redux state
  const dispatch = useDispatch();
  const user = useSelector(state => state.auth?.user);
  const project = useSelector(state => state.projects?.current);

  // Local state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [previewMode, setPreviewMode] = useState(false);

  // Feature states
  const [showTutorial, setShowTutorial] = useState(false);
  const [showAISuggestions, setShowAISuggestions] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [showCodeExport, setShowCodeExport] = useState(false);

  // App Builder hook with enhanced features
  const {
    components,
    addComponent,
    updateComponent,
    deleteComponent,
    moveComponent,
    duplicateComponent,
    undoAction,
    redoAction,
    canUndo,
    canRedo,
    saveProject,
    loadProject,
    isModified,
  } = useAppBuilder({
    projectId,
    initialComponents,
    autoSave: true,
    onSave,
    onLoad,
    onError: (err) => {
      setError(err);
      if (onError) onError(err);
    },
  });

  // WebSocket collaboration
  const {
    isConnected: websocketConnected,
    collaborators,
    sendUpdate,
    sendCursor,
  } = useWebSocket({
    enabled: enableFeatures.websocket,
    projectId,
    userId: user?.id,
    onComponentUpdate: updateComponent,
    onComponentAdd: addComponent,
    onComponentDelete: deleteComponent,
    onComponentMove: moveComponent,
  });

  // Tutorial system
  const {
    isActive: tutorialActive,
    currentStep,
    totalSteps,
    nextStep,
    previousStep,
    skipTutorial,
    startTutorial,
  } = useTutorial({
    enabled: enableFeatures.tutorial,
    autoStart: !user?.hasCompletedTutorial,
  });

  // AI Design Suggestions
  const {
    suggestions,
    loading: aiLoading,
    generateSuggestions,
    applySuggestion,
    dismissSuggestion,
  } = useAIDesignSuggestions({
    enabled: enableFeatures.aiSuggestions,
    components,
    selectedComponent,
    autoRefresh: true,
  });

  // Template system
  const {
    templates,
    loading: templatesLoading,
    saveAsTemplate,
    loadTemplate,
    deleteTemplate,
  } = useTemplates({
    enabled: enableFeatures.templates,
    projectId,
  });

  // Code export
  const {
    exportCode,
    exportFormats,
    loading: exportLoading,
    downloadCode,
  } = useCodeExport({
    enabled: enableFeatures.codeExport,
    components,
    projectSettings: project?.settings,
  });

  // Collaboration features
  const {
    activeUsers,
    comments,
    addComment,
    resolveComment,
    shareProject,
  } = useCollaboration({
    enabled: enableFeatures.collaboration,
    projectId,
    websocketConnected,
  });

  // Initialize app
  useEffect(() => {
    const initializeApp = async () => {
      try {
        setLoading(true);
        
        if (projectId) {
          await loadProject(projectId);
        }
        
        // Initialize features
        if (enableFeatures.aiSuggestions) {
          await generateSuggestions();
        }
        
        setLoading(false);
      } catch (err) {
        setError(err);
        setLoading(false);
      }
    };

    initializeApp();
  }, [projectId, loadProject, generateSuggestions, enableFeatures.aiSuggestions]);

  // Handle component selection with collaboration
  const handleSelectComponent = useCallback((component) => {
    setSelectedComponent(component);
    
    // Send cursor position for collaboration
    if (websocketConnected && component) {
      sendCursor({
        componentId: component.id,
        action: 'select',
        timestamp: Date.now(),
      });
    }
  }, [websocketConnected, sendCursor]);

  // Handle component updates with real-time sync
  const handleUpdateComponent = useCallback((componentId, updates) => {
    updateComponent(componentId, updates);
    
    // Send update to collaborators
    if (websocketConnected) {
      sendUpdate({
        type: 'component_update',
        componentId,
        updates,
        userId: user?.id,
        timestamp: Date.now(),
      });
    }
  }, [updateComponent, websocketConnected, sendUpdate, user?.id]);

  // Handle component addition with AI suggestions
  const handleAddComponent = useCallback(async (componentType, position) => {
    const newComponent = await addComponent(componentType, position);
    
    // Generate AI suggestions for the new component
    if (enableFeatures.aiSuggestions && newComponent) {
      setTimeout(() => generateSuggestions(), 500);
    }
    
    // Send update to collaborators
    if (websocketConnected) {
      sendUpdate({
        type: 'component_add',
        component: newComponent,
        userId: user?.id,
        timestamp: Date.now(),
      });
    }
    
    return newComponent;
  }, [addComponent, enableFeatures.aiSuggestions, generateSuggestions, websocketConnected, sendUpdate, user?.id]);

  // Handle component deletion with confirmation
  const handleDeleteComponent = useCallback((componentId) => {
    const component = components.find(c => c.id === componentId);
    
    if (component) {
      deleteComponent(componentId);
      
      // Clear selection if deleted component was selected
      if (selectedComponent?.id === componentId) {
        setSelectedComponent(null);
      }
      
      // Send update to collaborators
      if (websocketConnected) {
        sendUpdate({
          type: 'component_delete',
          componentId,
          userId: user?.id,
          timestamp: Date.now(),
        });
      }
      
      message.success(`Deleted ${component.type} component`);
    }
  }, [components, deleteComponent, selectedComponent, websocketConnected, sendUpdate, user?.id]);

  // Memoized header content
  const headerContent = useMemo(() => (
    <HeaderContent>
      <div>
        <h1 className="app-title">App Builder</h1>
        {project?.name && (
          <span className="project-name">{project.name}</span>
        )}
      </div>
      
      <FeatureToggles>
        {enableFeatures.collaboration && (
          <CollaborationIndicator
            connected={websocketConnected}
            collaborators={collaborators}
            activeUsers={activeUsers}
          />
        )}
        
        {enableFeatures.aiSuggestions && (
          <AIDesignSuggestions
            suggestions={suggestions}
            loading={aiLoading}
            onApply={applySuggestion}
            onDismiss={dismissSuggestion}
            compact
          />
        )}
        
        {enableFeatures.templates && (
          <TemplateManager
            templates={templates}
            loading={templatesLoading}
            onSave={saveAsTemplate}
            onLoad={loadTemplate}
            onDelete={deleteTemplate}
            compact
          />
        )}
        
        {enableFeatures.codeExport && (
          <CodeExporter
            formats={exportFormats}
            loading={exportLoading}
            onExport={exportCode}
            onDownload={downloadCode}
            compact
          />
        )}
      </FeatureToggles>
    </HeaderContent>
  ), [
    project?.name,
    enableFeatures,
    websocketConnected,
    collaborators,
    activeUsers,
    suggestions,
    aiLoading,
    applySuggestion,
    dismissSuggestion,
    templates,
    templatesLoading,
    saveAsTemplate,
    loadTemplate,
    deleteTemplate,
    exportFormats,
    exportLoading,
    exportCode,
    downloadCode,
  ]);

  // Error boundary
  if (error) {
    return (
      <IntegratedContainer>
        <ErrorBoundary>
          <div className="error-title">Something went wrong</div>
          <div className="error-message">{error.message}</div>
          <button onClick={() => window.location.reload()}>
            Reload Application
          </button>
        </ErrorBoundary>
      </IntegratedContainer>
    );
  }

  return (
    <IntegratedContainer>
      <DragDropProvider showOverlay={true}>
        <AccessibleComponent
          role="application"
          ariaLabel="App Builder application for creating user interfaces"
        >
          <ResponsiveAppLayout
            headerContent={headerContent}
            leftPanel={
              <UXEnhancedComponentPalette
                onAddComponent={handleAddComponent}
                selectedComponent={selectedComponent}
                showAISuggestions={enableFeatures.aiSuggestions}
                loading={loading}
              />
            }
            rightPanel={
              <UXEnhancedPropertyEditor
                component={selectedComponent}
                onUpdateComponent={handleUpdateComponent}
                loading={loading}
              />
            }
            showBreakpointIndicator={process.env.NODE_ENV === 'development'}
            enablePanelResize={true}
            persistLayout={true}
          >
            <UXEnhancedPreviewArea
              components={components}
              selectedComponentId={selectedComponent?.id}
              onSelectComponent={handleSelectComponent}
              onDeleteComponent={handleDeleteComponent}
              onUpdateComponent={handleUpdateComponent}
              onMoveComponent={moveComponent}
              previewMode={previewMode}
              websocketConnected={websocketConnected}
              loading={loading}
              onDrop={handleAddComponent}
            />
          </ResponsiveAppLayout>
        </AccessibleComponent>
      </DragDropProvider>

      {/* Tutorial Overlay */}
      {enableFeatures.tutorial && tutorialActive && (
        <TutorialAssistant
          currentStep={currentStep}
          totalSteps={totalSteps}
          onNext={nextStep}
          onPrevious={previousStep}
          onSkip={skipTutorial}
          onComplete={skipTutorial}
        />
      )}

      {/* Loading Overlay */}
      {loading && (
        <LoadingOverlay>
          <Spin size="large" />
          <div className="loading-text">Loading App Builder...</div>
        </LoadingOverlay>
      )}

      {/* Development Tools */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          position: 'fixed',
          bottom: theme.spacing[4],
          left: theme.spacing[4],
          zIndex: theme.zIndex.tooltip,
        }}>
          <Alert
            message={`Components: ${components.length} | Modified: ${isModified ? 'Yes' : 'No'} | Connected: ${websocketConnected ? 'Yes' : 'No'}`}
            type="info"
            size="small"
            showIcon={false}
          />
        </div>
      )}
    </IntegratedContainer>
  );
}
