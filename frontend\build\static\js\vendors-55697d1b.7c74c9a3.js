"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[3592],{9919:(e,t,n)=>{n.d(t,{A:()=>E});var a,o=n(58168),r=n(64467),i=n(89379),l=n(60436),u=n(5544),s=n(53986),c=n(46942),d=n.n(c),f=n(48491),m=n(22489),p=n(11980),g=n(12533),v=n(96540),h=n(82284),b=n(26076),x=n(30981),A=n(25371),y=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","font-variant","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing","word-break","white-space"],w={};var z=["prefixCls","defaultValue","value","autoSize","onResize","className","style","disabled","onChange","onInternalAutoSize"];const S=v.forwardRef((function(e,t){var n=e,l=n.prefixCls,c=n.defaultValue,f=n.value,m=n.autoSize,p=n.onResize,S=n.className,C=n.style,E=n.disabled,R=n.onChange,V=(n.onInternalAutoSize,(0,s.A)(n,z)),F=(0,g.A)(c,{value:f,postState:function(e){return null!=e?e:""}}),N=(0,u.A)(F,2),P=N[0],k=N[1],H=v.useRef();v.useImperativeHandle(t,(function(){return{textArea:H.current}}));var T=v.useMemo((function(){return m&&"object"===(0,h.A)(m)?[m.minRows,m.maxRows]:[]}),[m]),I=(0,u.A)(T,2),L=I[0],O=I[1],B=!!m,D=v.useState(2),K=(0,u.A)(D,2),M=K[0],W=K[1],j=v.useState(),Y=(0,u.A)(j,2),X=Y[0],q=Y[1],G=function(){W(0)};(0,x.A)((function(){B&&G()}),[f,L,O,B]),(0,x.A)((function(){if(0===M)W(1);else if(1===M){var e=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;a||((a=document.createElement("textarea")).setAttribute("tab-index","-1"),a.setAttribute("aria-hidden","true"),a.setAttribute("name","hiddenTextarea"),document.body.appendChild(a)),e.getAttribute("wrap")?a.setAttribute("wrap",e.getAttribute("wrap")):a.removeAttribute("wrap");var r=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.getAttribute("id")||e.getAttribute("data-reactid")||e.getAttribute("name");if(t&&w[n])return w[n];var a=window.getComputedStyle(e),o=a.getPropertyValue("box-sizing")||a.getPropertyValue("-moz-box-sizing")||a.getPropertyValue("-webkit-box-sizing"),r=parseFloat(a.getPropertyValue("padding-bottom"))+parseFloat(a.getPropertyValue("padding-top")),i=parseFloat(a.getPropertyValue("border-bottom-width"))+parseFloat(a.getPropertyValue("border-top-width")),l={sizingStyle:y.map((function(e){return"".concat(e,":").concat(a.getPropertyValue(e))})).join(";"),paddingSize:r,borderSize:i,boxSizing:o};return t&&n&&(w[n]=l),l}(e,t),i=r.paddingSize,l=r.borderSize,u=r.boxSizing,s=r.sizingStyle;a.setAttribute("style","".concat(s,";").concat("\n  min-height:0 !important;\n  max-height:none !important;\n  height:0 !important;\n  visibility:hidden !important;\n  overflow:hidden !important;\n  position:absolute !important;\n  z-index:-1000 !important;\n  top:0 !important;\n  right:0 !important;\n  pointer-events: none !important;\n")),a.value=e.value||e.placeholder||"";var c,d=void 0,f=void 0,m=a.scrollHeight;if("border-box"===u?m+=l:"content-box"===u&&(m-=i),null!==n||null!==o){a.value=" ";var p=a.scrollHeight-i;null!==n&&(d=p*n,"border-box"===u&&(d=d+i+l),m=Math.max(d,m)),null!==o&&(f=p*o,"border-box"===u&&(f=f+i+l),c=m>f?"":"hidden",m=Math.min(f,m))}var g={height:m,overflowY:c,resize:"none"};return d&&(g.minHeight=d),f&&(g.maxHeight=f),g}(H.current,!1,L,O);W(2),q(e)}else!function(){try{if(document.activeElement===H.current){var e=H.current,t=e.selectionStart,n=e.selectionEnd,a=e.scrollTop;H.current.setSelectionRange(t,n),H.current.scrollTop=a}}catch(e){}}()}),[M]);var J=v.useRef(),Q=function(){A.A.cancel(J.current)};v.useEffect((function(){return Q}),[]);var U=B?X:null,Z=(0,i.A)((0,i.A)({},C),U);return 0!==M&&1!==M||(Z.overflowY="hidden",Z.overflowX="hidden"),v.createElement(b.A,{onResize:function(e){2===M&&(null==p||p(e),m&&(Q(),J.current=(0,A.A)((function(){G()}))))},disabled:!(m||p)},v.createElement("textarea",(0,o.A)({},V,{ref:H,style:Z,className:d()(l,S,(0,r.A)({},"".concat(l,"-disabled"),E)),disabled:E,value:P,onChange:function(e){k(e.target.value),null==R||R(e)}})))}));var C=["defaultValue","value","onFocus","onBlur","onChange","allowClear","maxLength","onCompositionStart","onCompositionEnd","suffix","prefixCls","showCount","count","className","style","disabled","hidden","classNames","styles","onResize","onClear","onPressEnter","readOnly","autoSize","onKeyDown"];const E=v.forwardRef((function(e,t){var n,a=e.defaultValue,c=e.value,h=e.onFocus,b=e.onBlur,x=e.onChange,A=e.allowClear,y=e.maxLength,w=e.onCompositionStart,z=e.onCompositionEnd,E=e.suffix,R=e.prefixCls,V=void 0===R?"rc-textarea":R,F=e.showCount,N=e.count,P=e.className,k=e.style,H=e.disabled,T=e.hidden,I=e.classNames,L=e.styles,O=e.onResize,B=e.onClear,D=e.onPressEnter,K=e.readOnly,M=e.autoSize,W=e.onKeyDown,j=(0,s.A)(e,C),Y=(0,g.A)(a,{value:c,defaultValue:a}),X=(0,u.A)(Y,2),q=X[0],G=X[1],J=null==q?"":String(q),Q=v.useState(!1),U=(0,u.A)(Q,2),Z=U[0],$=U[1],_=v.useRef(!1),ee=v.useState(null),te=(0,u.A)(ee,2),ne=te[0],ae=te[1],oe=(0,v.useRef)(null),re=(0,v.useRef)(null),ie=function(){var e;return null===(e=re.current)||void 0===e?void 0:e.textArea},le=function(){ie().focus()};(0,v.useImperativeHandle)(t,(function(){var e;return{resizableTextArea:re.current,focus:le,blur:function(){ie().blur()},nativeElement:(null===(e=oe.current)||void 0===e?void 0:e.nativeElement)||ie()}})),(0,v.useEffect)((function(){$((function(e){return!H&&e}))}),[H]);var ue=v.useState(null),se=(0,u.A)(ue,2),ce=se[0],de=se[1];v.useEffect((function(){var e;ce&&(e=ie()).setSelectionRange.apply(e,(0,l.A)(ce))}),[ce]);var fe,me=(0,m.A)(N,F),pe=null!==(n=me.max)&&void 0!==n?n:y,ge=Number(pe)>0,ve=me.strategy(J),he=!!pe&&ve>pe,be=function(e,t){var n=t;!_.current&&me.exceedFormatter&&me.max&&me.strategy(t)>me.max&&t!==(n=me.exceedFormatter(t,{max:me.max}))&&de([ie().selectionStart||0,ie().selectionEnd||0]),G(n),(0,p.gS)(e.currentTarget,e,x,n)},xe=E;me.show&&(fe=me.showFormatter?me.showFormatter({value:J,count:ve,maxLength:pe}):"".concat(ve).concat(ge?" / ".concat(pe):""),xe=v.createElement(v.Fragment,null,xe,v.createElement("span",{className:d()("".concat(V,"-data-count"),null==I?void 0:I.count),style:null==L?void 0:L.count},fe)));var Ae=!M&&!F&&!A;return v.createElement(f.a,{ref:oe,value:J,allowClear:A,handleReset:function(e){G(""),le(),(0,p.gS)(ie(),e,x)},suffix:xe,prefixCls:V,classNames:(0,i.A)((0,i.A)({},I),{},{affixWrapper:d()(null==I?void 0:I.affixWrapper,(0,r.A)((0,r.A)({},"".concat(V,"-show-count"),F),"".concat(V,"-textarea-allow-clear"),A))}),disabled:H,focused:Z,className:d()(P,he&&"".concat(V,"-out-of-range")),style:(0,i.A)((0,i.A)({},k),ne&&!Ae?{height:"auto"}:{}),dataAttrs:{affixWrapper:{"data-count":"string"==typeof fe?fe:void 0}},hidden:T,readOnly:K,onClear:B},v.createElement(S,(0,o.A)({},j,{autoSize:M,maxLength:y,onKeyDown:function(e){"Enter"===e.key&&D&&D(e),null==W||W(e)},onChange:function(e){be(e,e.target.value)},onFocus:function(e){$(!0),null==h||h(e)},onBlur:function(e){$(!1),null==b||b(e)},onCompositionStart:function(e){_.current=!0,null==w||w(e)},onCompositionEnd:function(e){_.current=!1,be(e,e.currentTarget.value),null==z||z(e)},className:d()(null==I?void 0:I.textarea),style:(0,i.A)((0,i.A)({},null==L?void 0:L.textarea),{},{resize:null==k?void 0:k.resize}),disabled:H,prefixCls:V,onResize:function(e){var t;null==O||O(e),null!==(t=ie())&&void 0!==t&&t.style.height&&ae(!0)},ref:re,readOnly:K})))}))}}]);