"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[605],{4605:(e,t,r)=>{r.r(t),r.d(t,{default:()=>ue});var n=r(4467),a=r(467),c=r(5544),s=r(7528),o=r(4756),l=r.n(o),i=r(6540),u=r(3016),m=r(9356),p=r(677),f=r(7197),d=r(2702),A=r(7152),E=r(6370),g=r(7122),y=r(6552),v=r(6754),b=r(7977),x=r(7355),w=r(5039),h=r(9249),T=r(9029),S=r(1295),k=r(9552),P=r(378),O=r(3567),R=r(9248),j=r(385),L=r(3740),C=r(5132),M=r(8990),z=r(1250),H=r(9740);function U(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?U(Object(r),!0).forEach((function(t){(0,n.A)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var I,J,D,N,W,B,q,G=function(){var e=(0,a.A)(l().mark((function e(t){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e){try{var r=performance.now(),n=new WebSocket(t),a=setTimeout((function(){n.readyState!==WebSocket.OPEN&&(n.close(),e({success:!1,error:"Connection timeout",time:performance.now()-r}))}),5e3);n.onopen=function(){clearTimeout(a);var t=performance.now()-r;n.send(JSON.stringify({type:"ping"}));var c=setTimeout((function(){n.close(),e({success:!0,error:null,connectionTime:t,responseTime:null,message:"Connected but no response to ping"})}),2e3);n.onmessage=function(a){clearTimeout(c);var s=performance.now()-r;n.close(),e({success:!0,error:null,connectionTime:t,responseTime:s,message:"Connection successful"})},n.onerror=function(r){clearTimeout(c),n.close(),e({success:!1,error:"Error after connection: "+r.message,connectionTime:t,responseTime:null})}},n.onerror=function(t){clearTimeout(a),n.close(),e({success:!1,error:"Connection error: "+t.message,time:performance.now()-r})},n.onclose=function(t){clearTimeout(a),1e3!==t.code&&1001!==t.code&&e({success:!1,error:"Connection closed with code ".concat(t.code,": ").concat(t.reason),time:performance.now()-r})}}catch(t){e({success:!1,error:"Exception: "+t.message,time:0})}})));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),$=function(){var e=(0,a.A)(l().mark((function e(t){var r,n,a,c;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,r=performance.now(),e.next=4,fetch(t,{method:"GET",headers:{Accept:"application/json","Content-Type":"application/json"},timeout:5e3});case 4:if(n=e.sent,a=performance.now()-r,!n.ok){e.next=21;break}return e.prev=7,e.next=10,n.json();case 10:c=e.sent,e.next=18;break;case 13:return e.prev=13,e.t0=e.catch(7),e.next=17,n.text();case 17:c=e.sent;case 18:return e.abrupt("return",{success:!0,status:n.status,statusText:n.statusText,time:a,data:c});case 21:return e.abrupt("return",{success:!1,status:n.status,statusText:n.statusText,time:a,error:"HTTP error: ".concat(n.status," ").concat(n.statusText)});case 22:e.next=27;break;case 24:return e.prev=24,e.t1=e.catch(0),e.abrupt("return",{success:!1,error:"Exception: "+e.t1.message,time:0});case 27:case"end":return e.stop()}}),e,null,[[0,24],[7,13]])})));return function(t){return e.apply(this,arguments)}}(),_=function(){try{var e={};if(window.performance&&window.performance.timing){var t=window.performance.timing;e.pageLoad=t.loadEventEnd-t.navigationStart,e.domReady=t.domComplete-t.domLoading,e.networkLatency=t.responseEnd-t.fetchStart,e.processingTime=t.domComplete-t.responseEnd,e.backendTime=t.responseStart-t.navigationStart,e.frontendTime=t.loadEventEnd-t.responseStart}if(window.performance&&window.performance.memory&&(e.memory={jsHeapSizeLimit:window.performance.memory.jsHeapSizeLimit,totalJSHeapSize:window.performance.memory.totalJSHeapSize,usedJSHeapSize:window.performance.memory.usedJSHeapSize}),window.requestAnimationFrame){e.frameRate={current:0,average:0};var r=0,n=performance.now(),a=function(t){r++;var c=t-n;c>=1e3&&(e.frameRate.current=Math.round(1e3*r/c),e.frameRate.average?e.frameRate.average=Math.round((e.frameRate.average+e.frameRate.current)/2):e.frameRate.average=e.frameRate.current,r=0,n=t),window.requestAnimationFrame(a)};window.requestAnimationFrame(a)}return{success:!0,metrics:e}}catch(e){return{success:!1,error:"Exception: "+e.message}}},K=function(){var e=(0,a.A)(l().mark((function e(){var t,r,n=arguments;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=n.length>0&&void 0!==n[0]?n[0]:{},r={websocket:null,api:null,performance:null},e.prev=2,!t.websocketUrl){e.next=9;break}return H.Ay.info("Testing WebSocket connection..."),e.next=7,G(t.websocketUrl);case 7:r.websocket=e.sent,r.websocket.success?H.Ay.success("WebSocket connection successful (".concat(Math.round(r.websocket.connectionTime),"ms)")):H.Ay.error("WebSocket connection failed: ".concat(r.websocket.error));case 9:if(!t.apiUrl){e.next=15;break}return H.Ay.info("Testing API connection..."),e.next=13,$(t.apiUrl);case 13:r.api=e.sent,r.api.success?H.Ay.success("API connection successful (".concat(Math.round(r.api.time),"ms)")):H.Ay.error("API connection failed: ".concat(r.api.error));case 15:return t.testPerformance&&(H.Ay.info("Testing browser performance..."),r.performance=_(),r.performance.success?H.Ay.success("Performance test completed"):H.Ay.error("Performance test failed: ".concat(r.performance.error))),e.abrupt("return",r);case 19:return e.prev=19,e.t0=e.catch(2),H.Ay.error("Test failed: ".concat(e.t0.message)),e.abrupt("return",F(F({},r),{},{error:e.t0.message}));case 23:case"end":return e.stop()}}),e,null,[[2,19]])})));return function(){return e.apply(this,arguments)}}(),Q=r(6390);function V(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function X(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?V(Object(r),!0).forEach((function(t){(0,n.A)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):V(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Y=u.A.Title,Z=u.A.Text,ee=u.A.Paragraph,te=m.A.Panel,re=z.Ay.div(I||(I=(0,s.A)(["\n  padding: 20px;\n"]))),ne=(0,z.Ay)(p.A)(J||(J=(0,s.A)(["\n  margin-bottom: 20px;\n"]))),ae=z.Ay.div(D||(D=(0,s.A)(["\n  margin-bottom: 16px;\n\n  .label {\n    display: block;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n"]))),ce=z.Ay.div(N||(N=(0,s.A)(["\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n"]))),se=(0,z.Ay)(f.A)(W||(W=(0,s.A)(["\n  margin-bottom: 16px;\n"]))),oe=(0,z.Ay)(p.A)(B||(B=(0,s.A)(["\n  margin-top: 16px;\n\n  .ant-card-head {\n    background-color: ",";\n  }\n"])),(function(e){return"success"===e.status?"rgba(82, 196, 26, 0.1)":"error"===e.status?"rgba(245, 34, 45, 0.1)":"warning"===e.status?"rgba(250, 173, 20, 0.1)":"transparent"})),le=(0,z.Ay)(p.A)(q||(q=(0,s.A)(["\n  height: 100%;\n\n  .ant-statistic-title {\n    font-size: 14px;\n  }\n\n  .ant-statistic-content {\n    font-size: 24px;\n  }\n"]))),ie=function(e){var t=e.status;return"success"===t?i.createElement(S.A,{style:{color:"#52c41a"}}):"error"===t?i.createElement(k.A,{style:{color:"#f5222d"}}):"warning"===t?i.createElement(P.A,{style:{color:"#faad14"}}):"loading"===t?i.createElement(O.A,{style:{color:"#1890ff"}}):i.createElement(R.A,{style:{color:"#1890ff"}})};const ue=function(){var e=(0,i.useState)((0,Q.$0)("app_builder")),t=(0,c.A)(e,2),r=t[0],n=t[1],s=(0,i.useState)((0,Q.e9)("health")),o=(0,c.A)(s,2),u=o[0],p=o[1],S=(0,i.useState)(!0),k=(0,c.A)(S,2),P=k[0],O=k[1],z=(0,i.useState)(!1),H=(0,c.A)(z,2),U=H[0],F=H[1],I=(0,i.useState)(null),J=(0,c.A)(I,2),D=J[0],N=J[1],W=function(){var e=(0,a.A)(l().mark((function e(){var t;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return F(!0),N(null),e.prev=2,e.next=5,K({websocketUrl:r,apiUrl:u,testPerformance:P});case 5:t=e.sent,N(t),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(2),console.error("Test error:",e.t0);case 12:return e.prev=12,F(!1),e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[2,9,12,15]])})));return function(){return e.apply(this,arguments)}}(),B=function(){var e=(0,a.A)(l().mark((function e(){var t;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return F(!0),e.prev=1,e.next=4,G(r);case 4:t=e.sent,N((function(e){return X(X({},e),{},{websocket:t})})),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("WebSocket test error:",e.t0);case 11:return e.prev=11,F(!1),e.finish(11);case 14:case"end":return e.stop()}}),e,null,[[1,8,11,14]])})));return function(){return e.apply(this,arguments)}}(),q=function(){var e=(0,a.A)(l().mark((function e(){var t;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return F(!0),e.prev=1,e.next=4,$(u);case 4:t=e.sent,N((function(e){return X(X({},e),{},{api:t})})),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("API test error:",e.t0);case 11:return e.prev=11,F(!1),e.finish(11);case 14:case"end":return e.stop()}}),e,null,[[1,8,11,14]])})));return function(){return e.apply(this,arguments)}}();return i.createElement(re,null,i.createElement(Y,{level:3},"Testing Tools"),i.createElement(ee,null,"Use these tools to test various aspects of the application."),i.createElement(ne,{title:"Test Configuration"},i.createElement(se,{type:"info",message:"Configure Test Parameters",description:"Set up the parameters for the tests you want to run.",icon:i.createElement(R.A,null)}),i.createElement(ae,null,i.createElement("div",{className:"label"},"WebSocket URL:"),i.createElement(x.A,{value:r,onChange:function(e){return n(e.target.value)},placeholder:"Enter WebSocket URL",prefix:i.createElement(j.A,null),disabled:U})),i.createElement(ae,null,i.createElement("div",{className:"label"},"API URL:"),i.createElement(x.A,{value:u,onChange:function(e){return p(e.target.value)},placeholder:"Enter API URL",prefix:i.createElement(L.A,null),disabled:U})),i.createElement(ae,null,i.createElement("div",{className:"label"},"Include Performance Test:"),i.createElement(w.A,{checked:P,onChange:O,disabled:U})),i.createElement(ce,null,i.createElement(h.Ay,{type:"primary",icon:i.createElement(C.A,null),onClick:W,loading:U,disabled:U},"Run All Tests"),i.createElement(h.Ay,{icon:i.createElement(L.A,null),onClick:B,disabled:U||!r},"Test WebSocket"),i.createElement(h.Ay,{icon:i.createElement(j.A,null),onClick:q,disabled:U||!u},"Test API"),i.createElement(h.Ay,{icon:i.createElement(M.A,null),onClick:function(){F(!0);try{var e=_();N((function(t){return X(X({},t),{},{performance:e})}))}catch(e){console.error("Performance test error:",e)}finally{F(!1)}},disabled:U||!P},"Test Performance"))),U&&i.createElement("div",{style:{textAlign:"center",padding:"40px 0"}},i.createElement(T.A,{size:"large"}),i.createElement("div",{style:{marginTop:"16px"}},"Running tests...")),D&&i.createElement(i.Fragment,null,function(){if(!D||!D.websocket)return null;var e=D.websocket,t=e.success?"success":"error";return i.createElement(oe,{title:i.createElement(d.A,null,i.createElement(ie,{status:t}),i.createElement("span",null,"WebSocket Test Results")),status:t},e.success?i.createElement(i.Fragment,null,i.createElement(A.A,{gutter:[16,16]},i.createElement(E.A,{xs:24,sm:12},i.createElement(g.A,{title:"Connection Time",value:e.connectionTime?Math.round(e.connectionTime):"N/A",suffix:"ms"})),i.createElement(E.A,{xs:24,sm:12},i.createElement(g.A,{title:"Response Time",value:e.responseTime?Math.round(e.responseTime-e.connectionTime):"N/A",suffix:"ms"}))),i.createElement(y.A,null),i.createElement(ee,null,i.createElement(Z,{strong:!0},"Status:")," ",e.message||"Connection successful")):i.createElement(f.A,{message:"Connection Failed",description:e.error||"Unknown error",type:"error",showIcon:!0}))}(),function(){if(!D||!D.api)return null;var e=D.api,t=e.success?"success":"error";return i.createElement(oe,{title:i.createElement(d.A,null,i.createElement(ie,{status:t}),i.createElement("span",null,"API Test Results")),status:t},e.success?i.createElement(i.Fragment,null,i.createElement(A.A,{gutter:[16,16]},i.createElement(E.A,{xs:24,sm:8},i.createElement(g.A,{title:"Response Time",value:Math.round(e.time),suffix:"ms"})),i.createElement(E.A,{xs:24,sm:8},i.createElement(g.A,{title:"Status Code",value:e.status,suffix:e.statusText})),i.createElement(E.A,{xs:24,sm:8},i.createElement(g.A,{title:"Response Size",value:e.data?JSON.stringify(e.data).length:0,suffix:"bytes"}))),i.createElement(y.A,null),i.createElement(m.A,null,i.createElement(te,{header:"Response Data",key:"1"},i.createElement("pre",{style:{maxHeight:"200px",overflow:"auto"}},JSON.stringify(e.data,null,2))))):i.createElement(f.A,{message:"HTTP ".concat(e.status||"Error"),description:e.error||"Unknown error",type:"error",showIcon:!0}))}(),function(){if(!D||!D.performance)return null;var e=D.performance;if(e.success,!e.success)return i.createElement(oe,{title:i.createElement(d.A,null,i.createElement(ie,{status:"error"}),i.createElement("span",null,"Performance Test Results")),status:"error"},i.createElement(f.A,{message:"Test Failed",description:e.error||"Unknown error",type:"error",showIcon:!0}));var t=e.metrics,r=0,n=0;if(t.pageLoad&&(r+=Math.max(0,100-t.pageLoad/30),n++),t.frameRate&&t.frameRate.average&&(r+=Math.min(100,t.frameRate.average/60*100),n++),t.memory&&t.memory.usedJSHeapSize&&t.memory.jsHeapSizeLimit){var a=t.memory.usedJSHeapSize/t.memory.jsHeapSizeLimit*100;r+=Math.max(0,100-a),n++}var c=n>0?Math.round(r/n):0,s="success";return c<50?s="error":c<70&&(s="warning"),i.createElement(oe,{title:i.createElement(d.A,null,i.createElement(ie,{status:s}),i.createElement("span",null,"Performance Test Results")),status:s},i.createElement(A.A,{gutter:[16,16],style:{marginBottom:"16px"}},i.createElement(E.A,{xs:24,md:8},i.createElement(le,null,i.createElement(g.A,{title:"Performance Score",value:c,suffix:"/100",valueStyle:{color:c>=70?"#52c41a":c>=50?"#faad14":"#f5222d"}}),i.createElement(v.A,{percent:c,status:c>=70?"success":c>=50?"normal":"exception",showInfo:!1,style:{marginTop:"8px"}}))),t.pageLoad&&i.createElement(E.A,{xs:24,md:8},i.createElement(le,null,i.createElement(g.A,{title:i.createElement(b.A,{title:"Time to fully load the page"},i.createElement("span",null,"Page Load Time ",i.createElement(R.A,null))),value:Math.round(t.pageLoad),suffix:"ms",valueStyle:{color:t.pageLoad<1e3?"#52c41a":t.pageLoad<3e3?"#faad14":"#f5222d"}}))),t.frameRate&&t.frameRate.average&&i.createElement(E.A,{xs:24,md:8},i.createElement(le,null,i.createElement(g.A,{title:i.createElement(b.A,{title:"Average frames per second"},i.createElement("span",null,"Frame Rate ",i.createElement(R.A,null))),value:t.frameRate.average,suffix:"fps",valueStyle:{color:t.frameRate.average>=50?"#52c41a":t.frameRate.average>=30?"#faad14":"#f5222d"}})))),i.createElement(m.A,null,i.createElement(te,{header:"Detailed Metrics",key:"1"},i.createElement(A.A,{gutter:[16,16]},t.domReady&&i.createElement(E.A,{xs:24,md:8},i.createElement(g.A,{title:"DOM Ready Time",value:Math.round(t.domReady),suffix:"ms"})),t.networkLatency&&i.createElement(E.A,{xs:24,md:8},i.createElement(g.A,{title:"Network Latency",value:Math.round(t.networkLatency),suffix:"ms"})),t.processingTime&&i.createElement(E.A,{xs:24,md:8},i.createElement(g.A,{title:"Processing Time",value:Math.round(t.processingTime),suffix:"ms"})),t.backendTime&&i.createElement(E.A,{xs:24,md:8},i.createElement(g.A,{title:"Backend Time",value:Math.round(t.backendTime),suffix:"ms"})),t.frontendTime&&i.createElement(E.A,{xs:24,md:8},i.createElement(g.A,{title:"Frontend Time",value:Math.round(t.frontendTime),suffix:"ms"})),t.memory&&t.memory.usedJSHeapSize&&i.createElement(E.A,{xs:24,md:8},i.createElement(g.A,{title:"Memory Usage",value:(t.memory.usedJSHeapSize/1048576).toFixed(1),suffix:"MB"})),t.memory&&t.memory.totalJSHeapSize&&i.createElement(E.A,{xs:24,md:8},i.createElement(g.A,{title:"Total Heap Size",value:(t.memory.totalJSHeapSize/1048576).toFixed(1),suffix:"MB"})),t.memory&&t.memory.jsHeapSizeLimit&&i.createElement(E.A,{xs:24,md:8},i.createElement(g.A,{title:"Heap Size Limit",value:(t.memory.jsHeapSizeLimit/1048576).toFixed(1),suffix:"MB"}))))))}()))}}}]);