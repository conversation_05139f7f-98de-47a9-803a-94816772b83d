/*! For license information please see vendors-229eafb5.6bf5b2ba.js.LICENSE.txt */
(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[689],{2833:e=>{e.exports=function(e,n,o,r){var s=o?o.call(r,e,n):void 0;if(void 0!==s)return!!s;if(e===n)return!0;if("object"!=typeof e||!e||"object"!=typeof n||!n)return!1;var a=Object.keys(e),t=Object.keys(n);if(a.length!==t.length)return!1;for(var i=Object.prototype.hasOwnProperty.bind(n),p=0;p<a.length;p++){var c=a[p];if(!i(c))return!1;var l=e[c],d=n[c];if(!1===(s=o?o.call(r,l,d,c):void 0)||void 0===s&&l!==d)return!1}return!0}},16426:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var n=document.activeElement,o=[],r=0;r<e.rangeCount;r++)o.push(e.getRangeAt(r));switch(n.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":n.blur();break;default:n=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||o.forEach((function(n){e.addRange(n)})),n&&n.focus()}}},18467:(e,n,o)=>{"use strict";o.d(n,{wE:()=>G,lK:()=>j,As:()=>K});var r="comm",s="rule",a="decl",t="@import",i="@namespace",p="@keyframes",c="@layer",l=Math.abs,d=String.fromCharCode;function _(e){return e.trim()}function m(e,n,o){return e.replace(n,o)}function C(e,n,o){return e.indexOf(n,o)}function P(e,n){return 0|e.charCodeAt(n)}function S(e,n,o){return e.slice(n,o)}function u(e){return e.length}function g(e,n){return n.push(e),e}Object.assign;var A=1,E=1,f=0,D=0,O=0,R="";function h(e,n,o,r,s,a,t,i){return{value:e,root:n,parent:o,type:r,props:s,children:a,line:A,column:E,length:t,return:"",siblings:i}}function I(){return O=D>0?P(R,--D):0,E--,10===O&&(E=1,A--),O}function T(){return O=D<f?P(R,D++):0,E++,10===O&&(E=1,A++),O}function U(){return P(R,D)}function N(){return D}function v(e,n){return S(R,e,n)}function y(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function W(e){return _(v(D-1,w(91===e?e+2:40===e?e+1:e)))}function L(e){for(;(O=U())&&O<33;)T();return y(e)>2||y(O)>3?"":" "}function b(e,n){for(;--n&&T()&&!(O<48||O>102||O>57&&O<65||O>70&&O<97););return v(e,N()+(n<6&&32==U()&&32==T()))}function w(e){for(;T();)switch(O){case e:return D;case 34:case 39:34!==e&&39!==e&&w(O);break;case 40:41===e&&w(e);break;case 92:T()}return D}function F(e,n){for(;T()&&e+O!==57&&(e+O!==84||47!==U()););return"/*"+v(n,D-1)+"*"+d(47===e?e:T())}function M(e){for(;!y(U());)T();return v(e,D)}function G(e){return function(e){return R="",e}(x("",null,null,null,[""],e=function(e){return A=E=1,f=u(R=e),D=0,[]}(e),0,[0],e))}function x(e,n,o,r,s,a,t,i,p){for(var c=0,_=0,A=t,E=0,f=0,D=0,O=1,R=1,h=1,v=0,w="",G=s,j=a,K=r,B=w;R;)switch(D=v,v=T()){case 40:if(108!=D&&58==P(B,A-1)){-1!=C(B+=m(W(v),"&","&\f"),"&\f",l(c?i[c-1]:0))&&(h=-1);break}case 34:case 39:case 91:B+=W(v);break;case 9:case 10:case 13:case 32:B+=L(D);break;case 92:B+=b(N()-1,7);continue;case 47:switch(U()){case 42:case 47:g(k(F(T(),N()),n,o,p),p),5!=y(D||1)&&5!=y(U()||1)||!u(B)||" "===S(B,-1,void 0)||(B+=" ");break;default:B+="/"}break;case 123*O:i[c++]=u(B)*h;case 125*O:case 59:case 0:switch(v){case 0:case 125:R=0;case 59+_:-1==h&&(B=m(B,/\f/g,"")),f>0&&(u(B)-A||0===O&&47===D)&&g(f>32?V(B+";",r,o,A-1,p):V(m(B," ","")+";",r,o,A-2,p),p);break;case 59:B+=";";default:if(g(K=H(B,n,o,c,_,s,i,w,G=[],j=[],A,a),a),123===v)if(0===_)x(B,n,K,K,G,a,A,i,j);else{switch(E){case 99:if(110===P(B,3))break;case 108:if(97===P(B,2))break;default:_=0;case 100:case 109:case 115:}_?x(e,K,K,r&&g(H(e,K,K,0,0,s,i,w,s,G=[],A,j),j),s,j,A,i,r?G:j):x(B,K,K,K,[""],j,0,i,j)}}c=_=f=0,O=h=1,w=B="",A=t;break;case 58:A=1+u(B),f=D;default:if(O<1)if(123==v)--O;else if(125==v&&0==O++&&125==I())continue;switch(B+=d(v),v*O){case 38:h=_>0?1:(B+="\f",-1);break;case 44:i[c++]=(u(B)-1)*h,h=1;break;case 64:45===U()&&(B+=W(T())),E=U(),_=A=u(w=B+=M(N())),v++;break;case 45:45===D&&2==u(B)&&(O=0)}}return a}function H(e,n,o,r,a,t,i,p,c,d,C,P){for(var u=a-1,g=0===a?t:[""],A=function(e){return e.length}(g),E=0,f=0,D=0;E<r;++E)for(var O=0,R=S(e,u+1,u=l(f=i[E])),I=e;O<A;++O)(I=_(f>0?g[O]+" "+R:m(R,/&\f/g,g[O])))&&(c[D++]=I);return h(e,n,o,0===a?s:p,c,d,C,P)}function k(e,n,o,s){return h(e,n,o,r,d(O),S(e,2,-2),0,s)}function V(e,n,o,r,s){return h(e,n,o,a,S(e,0,r),S(e,r+1,-1),r,s)}function j(e,n){for(var o="",r=0;r<e.length;r++)o+=n(e[r],r,e,n)||"";return o}function K(e,n,o,l){switch(e.type){case c:if(e.children.length)break;case t:case i:case a:return e.return=e.return||e.value;case r:return"";case p:return e.return=e.value+"{"+j(e.children,l)+"}";case s:if(!u(e.value=e.props.join(",")))return""}return u(o=j(e.children,l))?e.return=e.value+"{"+o+"}":""}},28028:e=>{e.exports=function(e){return e.replace(/[A-Z]/g,(function(e){return"-"+e.toLowerCase()})).toLowerCase()}},34915:e=>{e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},68499:(e,n,o)=>{"use strict";o.d(n,{A:()=>a});var r=o(69081);const s=e=>!1===e?{block:"end",inline:"nearest"}:(e=>e===Object(e)&&0!==Object.keys(e).length)(e)?e:{block:"start",inline:"nearest"};function a(e,n){if(!e.isConnected||!(e=>{let n=e;for(;n&&n.parentNode;){if(n.parentNode===document)return!0;n=n.parentNode instanceof ShadowRoot?n.parentNode.host:n.parentNode}return!1})(e))return;const o=(e=>{const n=window.getComputedStyle(e);return{top:parseFloat(n.scrollMarginTop)||0,right:parseFloat(n.scrollMarginRight)||0,bottom:parseFloat(n.scrollMarginBottom)||0,left:parseFloat(n.scrollMarginLeft)||0}})(e);if("object"==typeof(a=n)&&"function"==typeof a.behavior)return n.behavior((0,r.O)(e,n));var a;const t="boolean"==typeof n||null==n?void 0:n.behavior;for(const{el:a,top:i,left:p}of(0,r.O)(e,s(n))){const e=i-o.top+o.bottom,n=p-o.left+o.right;a.scroll({top:e,left:n,behavior:t})}}},70572:(e,n,o)=>{"use strict";o.d(n,{NP:()=>xn,DU:()=>Jn,AH:()=>Kn,Ay:()=>Xn,i7:()=>zn});var r=function(){return r=Object.assign||function(e){for(var n,o=1,r=arguments.length;o<r;o++)for(var s in n=arguments[o])Object.prototype.hasOwnProperty.call(n,s)&&(e[s]=n[s]);return e},r.apply(this,arguments)};function s(e,n,o){if(o||2===arguments.length)for(var r,s=0,a=n.length;s<a;s++)!r&&s in n||(r||(r=Array.prototype.slice.call(n,0,s)),r[s]=n[s]);return e.concat(r||Array.prototype.slice.call(n))}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError,o(39316);var a=o(96540),t=o(2833),i=o.n(t),p="-ms-",c="-moz-",l="-webkit-",d="comm",_="rule",m="decl",C="@keyframes",P=Math.abs,S=String.fromCharCode,u=Object.assign;function g(e){return e.trim()}function A(e,n){return(e=n.exec(e))?e[0]:e}function E(e,n,o){return e.replace(n,o)}function f(e,n,o){return e.indexOf(n,o)}function D(e,n){return 0|e.charCodeAt(n)}function O(e,n,o){return e.slice(n,o)}function R(e){return e.length}function h(e){return e.length}function I(e,n){return n.push(e),e}function T(e,n){return e.filter((function(e){return!A(e,n)}))}var U=1,N=1,v=0,y=0,W=0,L="";function b(e,n,o,r,s,a,t,i){return{value:e,root:n,parent:o,type:r,props:s,children:a,line:U,column:N,length:t,return:"",siblings:i}}function w(e,n){return u(b("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},n)}function F(e){for(;e.root;)e=w(e.root,{children:[e]});I(e,e.siblings)}function M(){return W=y>0?D(L,--y):0,N--,10===W&&(N=1,U--),W}function G(){return W=y<v?D(L,y++):0,N++,10===W&&(N=1,U++),W}function x(){return D(L,y)}function H(){return y}function k(e,n){return O(L,e,n)}function V(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function j(e){return g(k(y-1,$(91===e?e+2:40===e?e+1:e)))}function K(e){for(;(W=x())&&W<33;)G();return V(e)>2||V(W)>3?"":" "}function B(e,n){for(;--n&&G()&&!(W<48||W>102||W>57&&W<65||W>70&&W<97););return k(e,H()+(n<6&&32==x()&&32==G()))}function $(e){for(;G();)switch(W){case e:return y;case 34:case 39:34!==e&&39!==e&&$(W);break;case 40:41===e&&$(e);break;case 92:G()}return y}function X(e,n){for(;G()&&e+W!==57&&(e+W!==84||47!==x()););return"/*"+k(n,y-1)+"*"+S(47===e?e:G())}function Y(e){for(;!V(x());)G();return k(e,y)}function J(e){return function(e){return L="",e}(z("",null,null,null,[""],e=function(e){return U=N=1,v=R(L=e),y=0,[]}(e),0,[0],e))}function z(e,n,o,r,s,a,t,i,p){for(var c=0,l=0,d=t,_=0,m=0,C=0,u=1,g=1,A=1,O=0,h="",T=s,U=a,N=r,v=h;g;)switch(C=O,O=G()){case 40:if(108!=C&&58==D(v,d-1)){-1!=f(v+=E(j(O),"&","&\f"),"&\f",P(c?i[c-1]:0))&&(A=-1);break}case 34:case 39:case 91:v+=j(O);break;case 9:case 10:case 13:case 32:v+=K(C);break;case 92:v+=B(H()-1,7);continue;case 47:switch(x()){case 42:case 47:I(q(X(G(),H()),n,o,p),p);break;default:v+="/"}break;case 123*u:i[c++]=R(v)*A;case 125*u:case 59:case 0:switch(O){case 0:case 125:g=0;case 59+l:-1==A&&(v=E(v,/\f/g,"")),m>0&&R(v)-d&&I(m>32?Q(v+";",r,o,d-1,p):Q(E(v," ","")+";",r,o,d-2,p),p);break;case 59:v+=";";default:if(I(N=Z(v,n,o,c,l,s,i,h,T=[],U=[],d,a),a),123===O)if(0===l)z(v,n,N,N,T,a,d,i,U);else switch(99===_&&110===D(v,3)?100:_){case 100:case 108:case 109:case 115:z(e,N,N,r&&I(Z(e,N,N,0,0,s,i,h,s,T=[],d,U),U),s,U,d,i,r?T:U);break;default:z(v,N,N,N,[""],U,0,i,U)}}c=l=m=0,u=A=1,h=v="",d=t;break;case 58:d=1+R(v),m=C;default:if(u<1)if(123==O)--u;else if(125==O&&0==u++&&125==M())continue;switch(v+=S(O),O*u){case 38:A=l>0?1:(v+="\f",-1);break;case 44:i[c++]=(R(v)-1)*A,A=1;break;case 64:45===x()&&(v+=j(G())),_=x(),l=d=R(h=v+=Y(H())),O++;break;case 45:45===C&&2==R(v)&&(u=0)}}return a}function Z(e,n,o,r,s,a,t,i,p,c,l,d){for(var m=s-1,C=0===s?a:[""],S=h(C),u=0,A=0,f=0;u<r;++u)for(var D=0,R=O(e,m+1,m=P(A=t[u])),I=e;D<S;++D)(I=g(A>0?C[D]+" "+R:E(R,/&\f/g,C[D])))&&(p[f++]=I);return b(e,n,o,0===s?_:i,p,c,l,d)}function q(e,n,o,r){return b(e,n,o,d,S(W),O(e,2,-2),0,r)}function Q(e,n,o,r,s){return b(e,n,o,m,O(e,0,r),O(e,r+1,-1),r,s)}function ee(e,n,o){switch(function(e,n){return 45^D(e,0)?(((n<<2^D(e,0))<<2^D(e,1))<<2^D(e,2))<<2^D(e,3):0}(e,n)){case 5103:return l+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return l+e+e;case 4789:return c+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return l+e+c+e+p+e+e;case 5936:switch(D(e,n+11)){case 114:return l+e+p+E(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return l+e+p+E(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return l+e+p+E(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return l+e+p+e+e;case 6165:return l+e+p+"flex-"+e+e;case 5187:return l+e+E(e,/(\w+).+(:[^]+)/,l+"box-$1$2"+p+"flex-$1$2")+e;case 5443:return l+e+p+"flex-item-"+E(e,/flex-|-self/g,"")+(A(e,/flex-|baseline/)?"":p+"grid-row-"+E(e,/flex-|-self/g,""))+e;case 4675:return l+e+p+"flex-line-pack"+E(e,/align-content|flex-|-self/g,"")+e;case 5548:return l+e+p+E(e,"shrink","negative")+e;case 5292:return l+e+p+E(e,"basis","preferred-size")+e;case 6060:return l+"box-"+E(e,"-grow","")+l+e+p+E(e,"grow","positive")+e;case 4554:return l+E(e,/([^-])(transform)/g,"$1"+l+"$2")+e;case 6187:return E(E(E(e,/(zoom-|grab)/,l+"$1"),/(image-set)/,l+"$1"),e,"")+e;case 5495:case 3959:return E(e,/(image-set\([^]*)/,l+"$1$`$1");case 4968:return E(E(e,/(.+:)(flex-)?(.*)/,l+"box-pack:$3"+p+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+l+e+e;case 4200:if(!A(e,/flex-|baseline/))return p+"grid-column-align"+O(e,n)+e;break;case 2592:case 3360:return p+E(e,"template-","")+e;case 4384:case 3616:return o&&o.some((function(e,o){return n=o,A(e.props,/grid-\w+-end/)}))?~f(e+(o=o[n].value),"span",0)?e:p+E(e,"-start","")+e+p+"grid-row-span:"+(~f(o,"span",0)?A(o,/\d+/):+A(o,/\d+/)-+A(e,/\d+/))+";":p+E(e,"-start","")+e;case 4896:case 4128:return o&&o.some((function(e){return A(e.props,/grid-\w+-start/)}))?e:p+E(E(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return E(e,/(.+)-inline(.+)/,l+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(R(e)-1-n>6)switch(D(e,n+1)){case 109:if(45!==D(e,n+4))break;case 102:return E(e,/(.+:)(.+)-([^]+)/,"$1"+l+"$2-$3$1"+c+(108==D(e,n+3)?"$3":"$2-$3"))+e;case 115:return~f(e,"stretch",0)?ee(E(e,"stretch","fill-available"),n,o)+e:e}break;case 5152:case 5920:return E(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,(function(n,o,r,s,a,t,i){return p+o+":"+r+i+(s?p+o+"-span:"+(a?t:+t-+r)+i:"")+e}));case 4949:if(121===D(e,n+6))return E(e,":",":"+l)+e;break;case 6444:switch(D(e,45===D(e,14)?18:11)){case 120:return E(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+l+(45===D(e,14)?"inline-":"")+"box$3$1"+l+"$2$3$1"+p+"$2box$3")+e;case 100:return E(e,":",":"+p)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return E(e,"scroll-","scroll-snap-")+e}return e}function ne(e,n){for(var o="",r=0;r<e.length;r++)o+=n(e[r],r,e,n)||"";return o}function oe(e,n,o,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case m:return e.return=e.return||e.value;case d:return"";case C:return e.return=e.value+"{"+ne(e.children,r)+"}";case _:if(!R(e.value=e.props.join(",")))return""}return R(o=ne(e.children,r))?e.return=e.value+"{"+o+"}":""}function re(e,n,o,r){if(e.length>-1&&!e.return)switch(e.type){case m:return void(e.return=ee(e.value,e.length,o));case C:return ne([w(e,{value:E(e.value,"@","@"+l)})],r);case _:if(e.length)return function(e,n){return e.map(n).join("")}(o=e.props,(function(n){switch(A(n,r=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":F(w(e,{props:[E(n,/:(read-\w+)/,":-moz-$1")]})),F(w(e,{props:[n]})),u(e,{props:T(o,r)});break;case"::placeholder":F(w(e,{props:[E(n,/:(plac\w+)/,":"+l+"input-$1")]})),F(w(e,{props:[E(n,/:(plac\w+)/,":-moz-$1")]})),F(w(e,{props:[E(n,/:(plac\w+)/,p+"input-$1")]})),F(w(e,{props:[n]})),u(e,{props:T(o,r)})}return""}))}}var se={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},ae=o(65606),te=void 0!==ae&&void 0!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}&&({ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.REACT_APP_SC_ATTR||{ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.SC_ATTR)||"data-styled",ie="active",pe="data-styled-version",ce="6.1.18",le="/*!sc*/\n",de="undefined"!=typeof window&&"undefined"!=typeof document,_e=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:void 0!==ae&&void 0!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}&&void 0!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.REACT_APP_SC_DISABLE_SPEEDY&&""!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.REACT_APP_SC_DISABLE_SPEEDY?"false"!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.REACT_APP_SC_DISABLE_SPEEDY&&{ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.REACT_APP_SC_DISABLE_SPEEDY:void 0!==ae&&void 0!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}&&void 0!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.SC_DISABLE_SPEEDY&&""!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.SC_DISABLE_SPEEDY&&"false"!=={ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.SC_DISABLE_SPEEDY&&{ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.SC_DISABLE_SPEEDY),me={},Ce=(new Set,Object.freeze([])),Pe=Object.freeze({});function Se(e,n,o){return void 0===o&&(o=Pe),e.theme!==o.theme&&e.theme||n||o.theme}var ue=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),ge=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,Ae=/(^-|-$)/g;function Ee(e){return e.replace(ge,"-").replace(Ae,"")}var fe=/(a)(d)/gi,De=52,Oe=function(e){return String.fromCharCode(e+(e>25?39:97))};function Re(e){var n,o="";for(n=Math.abs(e);n>De;n=n/De|0)o=Oe(n%De)+o;return(Oe(n%De)+o).replace(fe,"$1-$2")}var he,Ie=function(e,n){for(var o=n.length;o;)e=33*e^n.charCodeAt(--o);return e},Te=function(e){return Ie(5381,e)};function Ue(e){return Re(Te(e)>>>0)}function Ne(e){return"string"==typeof e&&!0}var ve="function"==typeof Symbol&&Symbol.for,ye=ve?Symbol.for("react.memo"):60115,We=ve?Symbol.for("react.forward_ref"):60112,Le={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},be={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},we={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Fe=((he={})[We]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},he[ye]=we,he);function Me(e){return("type"in(n=e)&&n.type.$$typeof)===ye?we:"$$typeof"in e?Fe[e.$$typeof]:Le;var n}var Ge=Object.defineProperty,xe=Object.getOwnPropertyNames,He=Object.getOwnPropertySymbols,ke=Object.getOwnPropertyDescriptor,Ve=Object.getPrototypeOf,je=Object.prototype;function Ke(e,n,o){if("string"!=typeof n){if(je){var r=Ve(n);r&&r!==je&&Ke(e,r,o)}var s=xe(n);He&&(s=s.concat(He(n)));for(var a=Me(e),t=Me(n),i=0;i<s.length;++i){var p=s[i];if(!(p in be||o&&o[p]||t&&p in t||a&&p in a)){var c=ke(n,p);try{Ge(e,p,c)}catch(e){}}}}return e}function Be(e){return"function"==typeof e}function $e(e){return"object"==typeof e&&"styledComponentId"in e}function Xe(e,n){return e&&n?"".concat(e," ").concat(n):e||n||""}function Ye(e,n){if(0===e.length)return"";for(var o=e[0],r=1;r<e.length;r++)o+=n?n+e[r]:e[r];return o}function Je(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function ze(e,n,o){if(void 0===o&&(o=!1),!o&&!Je(e)&&!Array.isArray(e))return n;if(Array.isArray(n))for(var r=0;r<n.length;r++)e[r]=ze(e[r],n[r]);else if(Je(n))for(var r in n)e[r]=ze(e[r],n[r]);return e}function Ze(e,n){Object.defineProperty(e,"toString",{value:n})}function qe(e){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(n.length>0?" Args: ".concat(n.join(", ")):""))}var Qe=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var n=0,o=0;o<e;o++)n+=this.groupSizes[o];return n},e.prototype.insertRules=function(e,n){if(e>=this.groupSizes.length){for(var o=this.groupSizes,r=o.length,s=r;e>=s;)if((s<<=1)<0)throw qe(16,"".concat(e));this.groupSizes=new Uint32Array(s),this.groupSizes.set(o),this.length=s;for(var a=r;a<s;a++)this.groupSizes[a]=0}for(var t=this.indexOfGroup(e+1),i=(a=0,n.length);a<i;a++)this.tag.insertRule(t,n[a])&&(this.groupSizes[e]++,t++)},e.prototype.clearGroup=function(e){if(e<this.length){var n=this.groupSizes[e],o=this.indexOfGroup(e),r=o+n;this.groupSizes[e]=0;for(var s=o;s<r;s++)this.tag.deleteRule(o)}},e.prototype.getGroup=function(e){var n="";if(e>=this.length||0===this.groupSizes[e])return n;for(var o=this.groupSizes[e],r=this.indexOfGroup(e),s=r+o,a=r;a<s;a++)n+="".concat(this.tag.getRule(a)).concat(le);return n},e}(),en=new Map,nn=new Map,on=1,rn=function(e){if(en.has(e))return en.get(e);for(;nn.has(on);)on++;var n=on++;return en.set(e,n),nn.set(n,e),n},sn=function(e,n){on=n+1,en.set(e,n),nn.set(n,e)},an="style[".concat(te,"][").concat(pe,'="').concat(ce,'"]'),tn=new RegExp("^".concat(te,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),pn=function(e,n,o){for(var r,s=o.split(","),a=0,t=s.length;a<t;a++)(r=s[a])&&e.registerName(n,r)},cn=function(e,n){for(var o,r=(null!==(o=n.textContent)&&void 0!==o?o:"").split(le),s=[],a=0,t=r.length;a<t;a++){var i=r[a].trim();if(i){var p=i.match(tn);if(p){var c=0|parseInt(p[1],10),l=p[2];0!==c&&(sn(l,c),pn(e,l,p[3]),e.getTag().insertRules(c,s)),s.length=0}else s.push(i)}}},ln=function(e){for(var n=document.querySelectorAll(an),o=0,r=n.length;o<r;o++){var s=n[o];s&&s.getAttribute(te)!==ie&&(cn(e,s),s.parentNode&&s.parentNode.removeChild(s))}};function dn(){return o.nc}var _n=function(e){var n=document.head,o=e||n,r=document.createElement("style"),s=function(e){var n=Array.from(e.querySelectorAll("style[".concat(te,"]")));return n[n.length-1]}(o),a=void 0!==s?s.nextSibling:null;r.setAttribute(te,ie),r.setAttribute(pe,ce);var t=dn();return t&&r.setAttribute("nonce",t),o.insertBefore(r,a),r},mn=function(){function e(e){this.element=_n(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var n=document.styleSheets,o=0,r=n.length;o<r;o++){var s=n[o];if(s.ownerNode===e)return s}throw qe(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,n){try{return this.sheet.insertRule(n,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var n=this.sheet.cssRules[e];return n&&n.cssText?n.cssText:""},e}(),Cn=function(){function e(e){this.element=_n(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,n){if(e<=this.length&&e>=0){var o=document.createTextNode(n);return this.element.insertBefore(o,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),Pn=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,n){return e<=this.length&&(this.rules.splice(e,0,n),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),Sn=de,un={isServer:!de,useCSSOMInjection:!_e},gn=function(){function e(e,n,o){void 0===e&&(e=Pe),void 0===n&&(n={});var s=this;this.options=r(r({},un),e),this.gs=n,this.names=new Map(o),this.server=!!e.isServer,!this.server&&de&&Sn&&(Sn=!1,ln(this)),Ze(this,(function(){return function(e){for(var n=e.getTag(),o=n.length,r="",s=function(o){var s=function(e){return nn.get(e)}(o);if(void 0===s)return"continue";var a=e.names.get(s),t=n.getGroup(o);if(void 0===a||!a.size||0===t.length)return"continue";var i="".concat(te,".g").concat(o,'[id="').concat(s,'"]'),p="";void 0!==a&&a.forEach((function(e){e.length>0&&(p+="".concat(e,","))})),r+="".concat(t).concat(i,'{content:"').concat(p,'"}').concat(le)},a=0;a<o;a++)s(a);return r}(s)}))}return e.registerId=function(e){return rn(e)},e.prototype.rehydrate=function(){!this.server&&de&&ln(this)},e.prototype.reconstructWithOptions=function(n,o){return void 0===o&&(o=!0),new e(r(r({},this.options),n),this.gs,o&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var n=e.useCSSOMInjection,o=e.target;return e.isServer?new Pn(o):n?new mn(o):new Cn(o)}(this.options),new Qe(e)));var e},e.prototype.hasNameForId=function(e,n){return this.names.has(e)&&this.names.get(e).has(n)},e.prototype.registerName=function(e,n){if(rn(e),this.names.has(e))this.names.get(e).add(n);else{var o=new Set;o.add(n),this.names.set(e,o)}},e.prototype.insertRules=function(e,n,o){this.registerName(e,n),this.getTag().insertRules(rn(e),o)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(rn(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),An=/&/g,En=/^\s*\/\/.*$/gm;function fn(e,n){return e.map((function(e){return"rule"===e.type&&(e.value="".concat(n," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(n," ")),e.props=e.props.map((function(e){return"".concat(n," ").concat(e)}))),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=fn(e.children,n)),e}))}function Dn(e){var n,o,r,s=void 0===e?Pe:e,a=s.options,t=void 0===a?Pe:a,i=s.plugins,p=void 0===i?Ce:i,c=function(e,r,s){return s.startsWith(o)&&s.endsWith(o)&&s.replaceAll(o,"").length>0?".".concat(n):e},l=p.slice();l.push((function(e){e.type===_&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(An,o).replace(r,c))})),t.prefix&&l.push(re),l.push(oe);var d=function(e,s,a,i){void 0===s&&(s=""),void 0===a&&(a=""),void 0===i&&(i="&"),n=i,o=s,r=new RegExp("\\".concat(o,"\\b"),"g");var p=e.replace(En,""),c=J(a||s?"".concat(a," ").concat(s," { ").concat(p," }"):p);t.namespace&&(c=fn(c,t.namespace));var d,_,m,C=[];return ne(c,(d=l.concat((m=function(e){return C.push(e)},function(e){e.root||(e=e.return)&&m(e)})),_=h(d),function(e,n,o,r){for(var s="",a=0;a<_;a++)s+=d[a](e,n,o,r)||"";return s})),C};return d.hash=p.length?p.reduce((function(e,n){return n.name||qe(15),Ie(e,n.name)}),5381).toString():"",d}var On=new gn,Rn=Dn(),hn=a.createContext({shouldForwardProp:void 0,styleSheet:On,stylis:Rn}),In=(hn.Consumer,a.createContext(void 0));function Tn(){return(0,a.useContext)(hn)}function Un(e){var n=(0,a.useState)(e.stylisPlugins),o=n[0],r=n[1],s=Tn().styleSheet,t=(0,a.useMemo)((function(){var n=s;return e.sheet?n=e.sheet:e.target&&(n=n.reconstructWithOptions({target:e.target},!1)),e.disableCSSOMInjection&&(n=n.reconstructWithOptions({useCSSOMInjection:!1})),n}),[e.disableCSSOMInjection,e.sheet,e.target,s]),p=(0,a.useMemo)((function(){return Dn({options:{namespace:e.namespace,prefix:e.enableVendorPrefixes},plugins:o})}),[e.enableVendorPrefixes,e.namespace,o]);(0,a.useEffect)((function(){i()(o,e.stylisPlugins)||r(e.stylisPlugins)}),[e.stylisPlugins]);var c=(0,a.useMemo)((function(){return{shouldForwardProp:e.shouldForwardProp,styleSheet:t,stylis:p}}),[e.shouldForwardProp,t,p]);return a.createElement(hn.Provider,{value:c},a.createElement(In.Provider,{value:p},e.children))}var Nn=function(){function e(e,n){var o=this;this.inject=function(e,n){void 0===n&&(n=Rn);var r=o.name+n.hash;e.hasNameForId(o.id,r)||e.insertRules(o.id,r,n(o.rules,r,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=n,Ze(this,(function(){throw qe(12,String(o.name))}))}return e.prototype.getName=function(e){return void 0===e&&(e=Rn),this.name+e.hash},e}(),vn=function(e){return e>="A"&&e<="Z"};function yn(e){for(var n="",o=0;o<e.length;o++){var r=e[o];if(1===o&&"-"===r&&"-"===e[0])return e;vn(r)?n+="-"+r.toLowerCase():n+=r}return n.startsWith("ms-")?"-"+n:n}var Wn=function(e){return null==e||!1===e||""===e},Ln=function(e){var n,o,r=[];for(var a in e){var t=e[a];e.hasOwnProperty(a)&&!Wn(t)&&(Array.isArray(t)&&t.isCss||Be(t)?r.push("".concat(yn(a),":"),t,";"):Je(t)?r.push.apply(r,s(s(["".concat(a," {")],Ln(t),!1),["}"],!1)):r.push("".concat(yn(a),": ").concat((n=a,null==(o=t)||"boolean"==typeof o||""===o?"":"number"!=typeof o||0===o||n in se||n.startsWith("--")?String(o).trim():"".concat(o,"px")),";")))}return r};function bn(e,n,o,r){return Wn(e)?[]:$e(e)?[".".concat(e.styledComponentId)]:Be(e)?!Be(s=e)||s.prototype&&s.prototype.isReactComponent||!n?[e]:bn(e(n),n,o,r):e instanceof Nn?o?(e.inject(o,r),[e.getName(r)]):[e]:Je(e)?Ln(e):Array.isArray(e)?Array.prototype.concat.apply(Ce,e.map((function(e){return bn(e,n,o,r)}))):[e.toString()];var s}function wn(e){for(var n=0;n<e.length;n+=1){var o=e[n];if(Be(o)&&!$e(o))return!1}return!0}var Fn=Te(ce),Mn=function(){function e(e,n,o){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===o||o.isStatic)&&wn(e),this.componentId=n,this.baseHash=Ie(Fn,n),this.baseStyle=o,gn.registerId(n)}return e.prototype.generateAndInjectStyles=function(e,n,o){var r=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,n,o):"";if(this.isStatic&&!o.hash)if(this.staticRulesId&&n.hasNameForId(this.componentId,this.staticRulesId))r=Xe(r,this.staticRulesId);else{var s=Ye(bn(this.rules,e,n,o)),a=Re(Ie(this.baseHash,s)>>>0);if(!n.hasNameForId(this.componentId,a)){var t=o(s,".".concat(a),void 0,this.componentId);n.insertRules(this.componentId,a,t)}r=Xe(r,a),this.staticRulesId=a}else{for(var i=Ie(this.baseHash,o.hash),p="",c=0;c<this.rules.length;c++){var l=this.rules[c];if("string"==typeof l)p+=l;else if(l){var d=Ye(bn(l,e,n,o));i=Ie(i,d+c),p+=d}}if(p){var _=Re(i>>>0);n.hasNameForId(this.componentId,_)||n.insertRules(this.componentId,_,o(p,".".concat(_),void 0,this.componentId)),r=Xe(r,_)}}return r},e}(),Gn=a.createContext(void 0);function xn(e){var n=a.useContext(Gn),o=(0,a.useMemo)((function(){return function(e,n){if(!e)throw qe(14);if(Be(e))return e(n);if(Array.isArray(e)||"object"!=typeof e)throw qe(8);return n?r(r({},n),e):e}(e.theme,n)}),[e.theme,n]);return e.children?a.createElement(Gn.Provider,{value:o},e.children):null}Gn.Consumer;var Hn={};function kn(e,n,o){var s=$e(e),t=e,i=!Ne(e),p=n.attrs,c=void 0===p?Ce:p,l=n.componentId,d=void 0===l?function(e,n){var o="string"!=typeof e?"sc":Ee(e);Hn[o]=(Hn[o]||0)+1;var r="".concat(o,"-").concat(Ue(ce+o+Hn[o]));return n?"".concat(n,"-").concat(r):r}(n.displayName,n.parentComponentId):l,_=n.displayName,m=void 0===_?function(e){return Ne(e)?"styled.".concat(e):"Styled(".concat(function(e){return e.displayName||e.name||"Component"}(e),")")}(e):_,C=n.displayName&&n.componentId?"".concat(Ee(n.displayName),"-").concat(n.componentId):n.componentId||d,P=s&&t.attrs?t.attrs.concat(c).filter(Boolean):c,S=n.shouldForwardProp;if(s&&t.shouldForwardProp){var u=t.shouldForwardProp;if(n.shouldForwardProp){var g=n.shouldForwardProp;S=function(e,n){return u(e,n)&&g(e,n)}}else S=u}var A=new Mn(o,C,s?t.componentStyle:void 0);function E(e,n){return function(e,n,o){var s=e.attrs,t=e.componentStyle,i=e.defaultProps,p=e.foldedComponentIds,c=e.styledComponentId,l=e.target,d=a.useContext(Gn),_=Tn(),m=e.shouldForwardProp||_.shouldForwardProp,C=Se(n,d,i)||Pe,P=function(e,n,o){for(var s,a=r(r({},n),{className:void 0,theme:o}),t=0;t<e.length;t+=1){var i=Be(s=e[t])?s(a):s;for(var p in i)a[p]="className"===p?Xe(a[p],i[p]):"style"===p?r(r({},a[p]),i[p]):i[p]}return n.className&&(a.className=Xe(a.className,n.className)),a}(s,n,C),S=P.as||l,u={};for(var g in P)void 0===P[g]||"$"===g[0]||"as"===g||"theme"===g&&P.theme===C||("forwardedAs"===g?u.as=P.forwardedAs:m&&!m(g,S)||(u[g]=P[g]));var A=function(e,n){var o=Tn();return e.generateAndInjectStyles(n,o.styleSheet,o.stylis)}(t,P),E=Xe(p,c);return A&&(E+=" "+A),P.className&&(E+=" "+P.className),u[Ne(S)&&!ue.has(S)?"class":"className"]=E,o&&(u.ref=o),(0,a.createElement)(S,u)}(f,e,n)}E.displayName=m;var f=a.forwardRef(E);return f.attrs=P,f.componentStyle=A,f.displayName=m,f.shouldForwardProp=S,f.foldedComponentIds=s?Xe(t.foldedComponentIds,t.styledComponentId):"",f.styledComponentId=C,f.target=s?t.target:e,Object.defineProperty(f,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=s?function(e){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];for(var r=0,s=n;r<s.length;r++)ze(e,s[r],!0);return e}({},t.defaultProps,e):e}}),Ze(f,(function(){return".".concat(f.styledComponentId)})),i&&Ke(f,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),f}function Vn(e,n){for(var o=[e[0]],r=0,s=n.length;r<s;r+=1)o.push(n[r],e[r+1]);return o}new Set;var jn=function(e){return Object.assign(e,{isCss:!0})};function Kn(e){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];if(Be(e)||Je(e))return jn(bn(Vn(Ce,s([e],n,!0))));var r=e;return 0===n.length&&1===r.length&&"string"==typeof r[0]?bn(r):jn(bn(Vn(r,n)))}function Bn(e,n,o){if(void 0===o&&(o=Pe),!n)throw qe(1,n);var a=function(r){for(var a=[],t=1;t<arguments.length;t++)a[t-1]=arguments[t];return e(n,o,Kn.apply(void 0,s([r],a,!1)))};return a.attrs=function(s){return Bn(e,n,r(r({},o),{attrs:Array.prototype.concat(o.attrs,s).filter(Boolean)}))},a.withConfig=function(s){return Bn(e,n,r(r({},o),s))},a}var $n=function(e){return Bn(kn,e)},Xn=$n;ue.forEach((function(e){Xn[e]=$n(e)}));var Yn=function(){function e(e,n){this.rules=e,this.componentId=n,this.isStatic=wn(e),gn.registerId(this.componentId+1)}return e.prototype.createStyles=function(e,n,o,r){var s=r(Ye(bn(this.rules,n,o,r)),""),a=this.componentId+e;o.insertRules(a,a,s)},e.prototype.removeStyles=function(e,n){n.clearRules(this.componentId+e)},e.prototype.renderStyles=function(e,n,o,r){e>2&&gn.registerId(this.componentId+e),this.removeStyles(e,o),this.createStyles(e,n,o,r)},e}();function Jn(e){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];var t=Kn.apply(void 0,s([e],n,!1)),i="sc-global-".concat(Ue(JSON.stringify(t))),p=new Yn(t,i),c=function(e){var n=Tn(),o=a.useContext(Gn),r=a.useRef(n.styleSheet.allocateGSInstance(i)).current;return n.styleSheet.server&&l(r,e,n.styleSheet,o,n.stylis),a.useLayoutEffect((function(){if(!n.styleSheet.server)return l(r,e,n.styleSheet,o,n.stylis),function(){return p.removeStyles(r,n.styleSheet)}}),[r,e,n.styleSheet,o,n.stylis]),null};function l(e,n,o,s,a){if(p.isStatic)p.renderStyles(e,me,o,a);else{var t=r(r({},n),{theme:Se(n,s,c.defaultProps)});p.renderStyles(e,t,o,a)}}return a.memo(c)}function zn(e){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];var r=Ye(Kn.apply(void 0,s([e],n,!1))),a=Ue(r);return new Nn(a,r)}(function(){function e(){var e=this;this._emitSheetCSS=function(){var n=e.instance.toString();if(!n)return"";var o=dn(),r=Ye([o&&'nonce="'.concat(o,'"'),"".concat(te,'="true"'),"".concat(pe,'="').concat(ce,'"')].filter(Boolean)," ");return"<style ".concat(r,">").concat(n,"</style>")},this.getStyleTags=function(){if(e.sealed)throw qe(2);return e._emitSheetCSS()},this.getStyleElement=function(){var n;if(e.sealed)throw qe(2);var o=e.instance.toString();if(!o)return[];var s=((n={})[te]="",n[pe]=ce,n.dangerouslySetInnerHTML={__html:o},n),t=dn();return t&&(s.nonce=t),[a.createElement("style",r({},s,{key:"sc-0-0"}))]},this.seal=function(){e.sealed=!0},this.instance=new gn({isServer:!0}),this.sealed=!1}e.prototype.collectStyles=function(e){if(this.sealed)throw qe(2);return a.createElement(Un,{sheet:this.instance},e)},e.prototype.interleaveWithNodeStream=function(e){throw qe(3)}})(),"__sc-".concat(te,"__")},73700:(e,n,o)=>{"use strict";function r(e,n,o){var r=(o||{}).atBegin;return function(e,n,o){var r,s=o||{},a=s.noTrailing,t=void 0!==a&&a,i=s.noLeading,p=void 0!==i&&i,c=s.debounceMode,l=void 0===c?void 0:c,d=!1,_=0;function m(){r&&clearTimeout(r)}function C(){for(var o=arguments.length,s=new Array(o),a=0;a<o;a++)s[a]=arguments[a];var i=this,c=Date.now()-_;function C(){_=Date.now(),n.apply(i,s)}function P(){r=void 0}d||(p||!l||r||C(),m(),void 0===l&&c>e?p?(_=Date.now(),t||(r=setTimeout(l?P:C,e))):C():!0!==t&&(r=setTimeout(l?P:C,void 0===l?e-c:e)))}return C.cancel=function(e){var n=(e||{}).upcomingOnly,o=void 0!==n&&n;m(),d=!o},C}(e,n,{debounceMode:!1!==(void 0!==r&&r)})}o.d(n,{s:()=>r})},78418:(e,n,o)=>{"use strict";e.exports=o(85160)},79730:(e,n,o)=>{"use strict";o.d(n,{JK:()=>S});try{self["workbox:window:7.2.0"]&&_()}catch(r){}function r(e,n){return new Promise((function(o){var r=new MessageChannel;r.port1.onmessage=function(e){o(e.data)},e.postMessage(n,[r.port2])}))}function s(e){var n=function(e){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,"string");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof n?n:n+""}function a(e,n){for(var o=0;o<n.length;o++){var r=n[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,s(r.key),r)}}function t(e,n){return t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,n){return e.__proto__=n,e},t(e,n)}function i(e,n){(null==n||n>e.length)&&(n=e.length);for(var o=0,r=new Array(n);o<n;o++)r[o]=e[o];return r}try{self["workbox:core:7.2.0"]&&_()}catch(r){}var p=function(){var e=this;this.promise=new Promise((function(n,o){e.resolve=n,e.reject=o}))};function c(e,n){var o=location.href;return new URL(e,o).href===new URL(n,o).href}var l=function(e,n){this.type=e,Object.assign(this,n)};function d(e,n,o){return o?n?n(e):e:(e&&e.then||(e=Promise.resolve(e)),n?e.then(n):e)}function m(){}var C={type:"SKIP_WAITING"};function P(e,n){if(!n)return e&&e.then?e.then(m):Promise.resolve()}var S=function(e){function n(n,o){var r,s;return void 0===o&&(o={}),(r=e.call(this)||this).nn={},r.tn=0,r.rn=new p,r.en=new p,r.on=new p,r.un=0,r.an=new Set,r.cn=function(){var e=r.fn,n=e.installing;r.tn>0||!c(n.scriptURL,r.sn.toString())||performance.now()>r.un+6e4?(r.vn=n,e.removeEventListener("updatefound",r.cn)):(r.hn=n,r.an.add(n),r.rn.resolve(n)),++r.tn,n.addEventListener("statechange",r.ln)},r.ln=function(e){var n=r.fn,o=e.target,s=o.state,a=o===r.vn,t={sw:o,isExternal:a,originalEvent:e};!a&&r.mn&&(t.isUpdate=!0),r.dispatchEvent(new l(s,t)),"installed"===s?r.wn=self.setTimeout((function(){"installed"===s&&n.waiting===o&&r.dispatchEvent(new l("waiting",t))}),200):"activating"===s&&(clearTimeout(r.wn),a||r.en.resolve(o))},r.yn=function(e){var n=r.hn,o=n!==navigator.serviceWorker.controller;r.dispatchEvent(new l("controlling",{isExternal:o,originalEvent:e,sw:n,isUpdate:r.mn})),o||r.on.resolve(n)},r.gn=(s=function(e){var n=e.data,o=e.ports,s=e.source;return d(r.getSW(),(function(){r.an.has(s)&&r.dispatchEvent(new l("message",{data:n,originalEvent:e,ports:o,sw:s}))}))},function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];try{return Promise.resolve(s.apply(this,e))}catch(e){return Promise.reject(e)}}),r.sn=n,r.nn=o,navigator.serviceWorker.addEventListener("message",r.gn),r}var o,s;s=e,(o=n).prototype=Object.create(s.prototype),o.prototype.constructor=o,t(o,s);var i,_,m=n.prototype;return m.register=function(e){var n=(void 0===e?{}:e).immediate,o=void 0!==n&&n;try{var r=this;return d(function(e,n){var o=e();return o&&o.then?o.then(n):n()}((function(){if(!o&&"complete"!==document.readyState)return P(new Promise((function(e){return window.addEventListener("load",e)})))}),(function(){return r.mn=Boolean(navigator.serviceWorker.controller),r.dn=r.pn(),d(r.bn(),(function(e){r.fn=e,r.dn&&(r.hn=r.dn,r.en.resolve(r.dn),r.on.resolve(r.dn),r.dn.addEventListener("statechange",r.ln,{once:!0}));var n=r.fn.waiting;return n&&c(n.scriptURL,r.sn.toString())&&(r.hn=n,Promise.resolve().then((function(){r.dispatchEvent(new l("waiting",{sw:n,wasWaitingBeforeRegister:!0}))})).then((function(){}))),r.hn&&(r.rn.resolve(r.hn),r.an.add(r.hn)),r.fn.addEventListener("updatefound",r.cn),navigator.serviceWorker.addEventListener("controllerchange",r.yn),r.fn}))})))}catch(e){return Promise.reject(e)}},m.update=function(){try{return this.fn?d(P(this.fn.update())):d()}catch(e){return Promise.reject(e)}},m.getSW=function(){return void 0!==this.hn?Promise.resolve(this.hn):this.rn.promise},m.messageSW=function(e){try{return d(this.getSW(),(function(n){return r(n,e)}))}catch(e){return Promise.reject(e)}},m.messageSkipWaiting=function(){this.fn&&this.fn.waiting&&r(this.fn.waiting,C)},m.pn=function(){var e=navigator.serviceWorker.controller;return e&&c(e.scriptURL,this.sn.toString())?e:void 0},m.bn=function(){try{var e=this;return d(function(e,n){try{var o=e()}catch(e){return n(e)}return o&&o.then?o.then(void 0,n):o}((function(){return d(navigator.serviceWorker.register(e.sn,e.nn),(function(n){return e.un=performance.now(),n}))}),(function(e){throw e})))}catch(e){return Promise.reject(e)}},i=n,(_=[{key:"active",get:function(){return this.en.promise}},{key:"controlling",get:function(){return this.on.promise}}])&&a(i.prototype,_),Object.defineProperty(i,"prototype",{writable:!1}),i}(function(){function e(){this.Pn=new Map}var n=e.prototype;return n.addEventListener=function(e,n){this.jn(e).add(n)},n.removeEventListener=function(e,n){this.jn(e).delete(n)},n.dispatchEvent=function(e){e.target=this;for(var n,o=function(e){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,n){if(e){if("string"==typeof e)return i(e,n);var o=Object.prototype.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?i(e,n):void 0}}(e))){n&&(e=n);var o=0;return function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(this.jn(e.type));!(n=o()).done;)(0,n.value)(e)},n.jn=function(e){return this.Pn.has(e)||this.Pn.set(e,new Set),this.Pn.get(e)},e}())},85160:(e,n,o)=>{"use strict";var r=o(96540),s="function"==typeof Object.is?Object.is:function(e,n){return e===n&&(0!==e||1/e==1/n)||e!=e&&n!=n},a=r.useSyncExternalStore,t=r.useRef,i=r.useEffect,p=r.useMemo,c=r.useDebugValue;n.useSyncExternalStoreWithSelector=function(e,n,o,r,l){var d=t(null);if(null===d.current){var _={hasValue:!1,value:null};d.current=_}else _=d.current;d=p((function(){function e(e){if(!i){if(i=!0,a=e,e=r(e),void 0!==l&&_.hasValue){var n=_.value;if(l(n,e))return t=n}return t=e}if(n=t,s(a,e))return n;var o=r(e);return void 0!==l&&l(n,o)?(a=e,n):(a=e,t=o)}var a,t,i=!1,p=void 0===o?null:o;return[function(){return e(n())},null===p?void 0:function(){return e(p())}]}),[n,o,r,l]);var m=a(e,d[0],d[1]);return i((function(){_.hasValue=!0,_.value=m}),[m]),c(m),m}}}]);