"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[8304],{18563:(e,n,t)=>{t.d(n,{AH:()=>r.AH,Ay:()=>o,DU:()=>r.DU,I4:()=>r.Ay,NP:()=>r.NP,i7:()=>r.i7});var r=t(70572);const o=r.Ay},30403:(e,n,t)=>{t.d(n,{A:()=>D});var r,o,i,a,l,c,s=t(23029),d=t(92901),p=t(56822),u=t(53954),m=t(85501),y=t(64467),g=t(57528),h=t(96540),f=t(5556),A=t.n(f),b=t(18563),v=t(86020),x=t(79146),E=t(35346),F=t(17648);function w(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],(function(){})))}catch(e){}return(w=function(){return!!e})()}var S=b.I4.div(r||(r=(0,g.A)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ",";\n  background-color: ",";\n  border-radius: ",";\n  box-shadow: ",";\n  max-width: 800px;\n  margin: 0 auto;\n  text-align: center;\n"])),v.Ay.spacing[6],v.Ay.colors.neutral[100],v.Ay.borderRadius.lg,v.Ay.shadows.md),k=b.I4.div(o||(o=(0,g.A)(["\n  font-size: 48px;\n  color: ",";\n  margin-bottom: ",";\n"])),v.Ay.colors.error.main,v.Ay.spacing[4]),z=b.I4.h2(i||(i=(0,g.A)(["\n  font-size: ",";\n  color: ",";\n  margin-bottom: ",";\n"])),v.Ay.typography.fontSize.xl,v.Ay.colors.neutral[900],v.Ay.spacing[3]),T=b.I4.p(a||(a=(0,g.A)(["\n  font-size: ",";\n  color: ",";\n  margin-bottom: ",";\n"])),v.Ay.typography.fontSize.md,v.Ay.colors.neutral[700],v.Ay.spacing[4]),C=b.I4.div(l||(l=(0,g.A)(["\n  background-color: ",";\n  padding: ",";\n  border-radius: ",";\n  margin-bottom: ",";\n  text-align: left;\n  overflow: auto;\n  max-height: 200px;\n  width: 100%;\n  font-family: ",";\n  font-size: ",";\n"])),v.Ay.colors.neutral[200],v.Ay.spacing[3],v.Ay.borderRadius.md,v.Ay.spacing[4],v.Ay.typography.fontFamily.code,v.Ay.typography.fontSize.sm),O=b.I4.div(c||(c=(0,g.A)(["\n  display: flex;\n  gap: ",";\n  margin-top: ",";\n"])),v.Ay.spacing[3],v.Ay.spacing[4]),I=function(e){function n(e){var t,r,o,i;return(0,s.A)(this,n),r=this,o=n,i=[e],o=(0,u.A)(o),t=(0,p.A)(r,w()?Reflect.construct(o,i||[],(0,u.A)(r).constructor):o.apply(r,i)),(0,y.A)(t,"handleReload",(function(){window.location.reload()})),(0,y.A)(t,"handleReset",(function(){t.setState({hasError:!1,error:null,errorInfo:null})})),(0,y.A)(t,"handleGoHome",(function(){window.location.href="/"})),t.state={hasError:!1,error:null,errorInfo:null,errorCount:0},t}return(0,m.A)(n,e),(0,d.A)(n,[{key:"componentDidCatch",value:function(e,n){console.error("Error caught by ErrorBoundary:",e,n),F.A.trackError(e,{componentStack:n.componentStack,source:"react_error_boundary",component:this.constructor.name,props:JSON.stringify(this.props)}),F.A.addBreadcrumb("Error in component: ".concat(e.message),"error_boundary",{componentStack:n.componentStack}),this.setState((function(e){return{errorInfo:n,errorCount:e.errorCount+1}})),this.props.onError&&this.props.onError(e,n)}},{key:"render",value:function(){var e=this.state,n=e.hasError,t=e.error,r=e.errorInfo,o=e.errorCount,i=this.props,a=i.fallback,l=i.children;return n?a?a(t,r,this.handleReset):h.createElement(S,null,h.createElement(k,null,h.createElement(E.v7y,null)),h.createElement(z,null,"Something went wrong"),h.createElement(T,null,"We're sorry, but an error occurred while rendering this component.",o>1&&h.createElement("div",{style:{marginTop:v.Ay.spacing[2],color:v.Ay.colors.error.main}},"Multiple errors detected (",o,"). You may need to reload the page.")),t&&h.createElement(C,null,h.createElement("strong",null,"Error:")," ",t.toString(),r&&h.createElement("div",{style:{marginTop:v.Ay.spacing[2]}},h.createElement("strong",null,"Component Stack:"),h.createElement("pre",null,r.componentStack))),h.createElement(O,null,h.createElement(x.Button,{onClick:this.handleReset,icon:h.createElement(E.NhG,null),variant:"outline"},"Try Again"),h.createElement(x.Button,{onClick:this.handleReload,icon:h.createElement(E.KF4,null)},"Reload Page"),h.createElement(x.Button,{onClick:this.handleGoHome,icon:h.createElement(E.aod,null),variant:"outline"},"Go to Home"))):l}}],[{key:"getDerivedStateFromError",value:function(e){return{hasError:!0,error:e}}}])}(h.Component);I.propTypes={children:A().node.isRequired,fallback:A().func,onError:A().func};const D=I},49391:(e,n,t)=>{t.d(n,{OJ:()=>p,As:()=>u});var r=t(10467),o=t(5544),i=t(54756),a=t.n(i),l=t(96540),c=t(69477),s=(0,l.createContext)({trackEvent:function(){},trackPageView:function(){},trackError:function(){}}),d=(0,l.createContext)({user:null,isAuthenticated:!1,isLoading:!0,login:function(){},register:function(){},logout:function(){},hasRole:function(){},hasPermission:function(){}}),p=function(e){var n=e.children,t=(0,l.useState)(null),i=(0,o.A)(t,2),p=i[0],u=i[1],m=(0,l.useState)(!0),y=(0,o.A)(m,2),g=y[0],h=y[1],f=(0,l.useContext)(s).trackEvent;(0,l.useEffect)((function(){var e=function(){var e=(0,r.A)(a().mark((function e(){var n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{(0,c.wR)()&&(n=(0,c.wz)(),(0,c.gf)(),n&&(u(n),f("auth_initialized",{userId:n.id||n.username,username:n.username})))}catch(e){console.error("Auth initialization error:",e)}finally{h(!1)}case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();e()}),[f]);var A=function(){var e=(0,r.A)(a().mark((function e(n,t){var r;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return h(!0),e.prev=1,e.next=4,(0,c.iD)(n,t);case 4:if(!(r=e.sent).success){e.next=11;break}return u(r.user),f("auth_login",{userId:r.user.id||r.user.username,username:r.user.username}),e.abrupt("return",r);case 11:throw f("auth_login_error",{error:r.error}),new Error(r.error);case 13:e.next=19;break;case 15:throw e.prev=15,e.t0=e.catch(1),f("auth_login_error",{error:e.t0.message}),e.t0;case 19:return e.prev=19,h(!1),e.finish(19);case 22:case"end":return e.stop()}}),e,null,[[1,15,19,22]])})));return function(n,t){return e.apply(this,arguments)}}(),b=function(){var e=(0,r.A)(a().mark((function e(n){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return h(!0),e.prev=1,e.next=4,(0,c.kz)(n);case 4:if(!(t=e.sent).success){e.next=11;break}return u(t.user),f("auth_register",{userId:t.user.id||t.user.username,username:t.user.username}),e.abrupt("return",t);case 11:throw f("auth_register_error",{error:t.error}),new Error(t.error);case 13:e.next=19;break;case 15:throw e.prev=15,e.t0=e.catch(1),f("auth_register_error",{error:e.t0.message}),e.t0;case 19:return e.prev=19,h(!1),e.finish(19);case 22:case"end":return e.stop()}}),e,null,[[1,15,19,22]])})));return function(n){return e.apply(this,arguments)}}(),v=function(){var e=(0,r.A)(a().mark((function e(){var n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return h(!0),e.prev=1,e.next=4,(0,c.ri)();case 4:return n=e.sent,u(null),f("auth_logout"),e.abrupt("return",!1!==n.success);case 10:return e.prev=10,e.t0=e.catch(1),console.error("Logout error:",e.t0),u(null),e.abrupt("return",!1);case 15:return e.prev=15,h(!1),e.finish(15);case 18:case"end":return e.stop()}}),e,null,[[1,10,15,18]])})));return function(){return e.apply(this,arguments)}}(),x={user:p,isAuthenticated:!!p,isLoading:g,login:A,register:b,logout:v,hasRole:function(e){return p&&p.roles&&p.roles.includes(e)},hasPermission:function(e){return p&&p.permissions&&p.permissions.includes(e)}};return l.createElement(d.Provider,{value:x},n)},u=function(){return(0,l.useContext)(d)}},79146:(e,n,t)=>{t.r(n),t.d(n,{Button:()=>j,Card:()=>ne,Input:()=>De,Select:()=>Me,Text:()=>tn,ThemeProvider:()=>W.NP,VERSION:()=>on,a11yUtils:()=>E,accessibility:()=>r.Ti,animationUtils:()=>F,animations:()=>r.WT,borderRadius:()=>r.Vq,breakpoints:()=>r.fi,colorUtils:()=>b,colors:()=>r.Tj,componentTokens:()=>r.dK,componentUtils:()=>w,config:()=>an,createGlobalStyle:()=>W.DU,css:()=>W.AH,designUtils:()=>k,globalStyles:()=>rn,hierarchyUtils:()=>m,informationArchitecture:()=>u,interactionPatterns:()=>p,keyframes:()=>W.i7,mediaQueries:()=>r.HP,responsiveUtils:()=>S,shadows:()=>r.Eo,spacing:()=>r.YK,spacingHierarchy:()=>s,spacingUtils:()=>v,styled:()=>W.Ay,theme:()=>r.Ay,transitions:()=>r.bm,typography:()=>r.Il,typographyUtils:()=>x,visualGrouping:()=>d,visualHierarchy:()=>c,zIndex:()=>r.fE});var r=t(86020),o=t(60436),i=t(64467);function a(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function l(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?a(Object(t),!0).forEach((function(n){(0,i.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):a(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var c={display:{large:{fontSize:r.Ay.typography.fontSize["6xl"],fontWeight:r.Ay.typography.fontWeight.bold,lineHeight:r.Ay.typography.lineHeight.tight,letterSpacing:"-0.025em",marginBottom:r.Ay.spacing[6],color:r.Ay.colors.text.primary},medium:{fontSize:r.Ay.typography.fontSize["5xl"],fontWeight:r.Ay.typography.fontWeight.bold,lineHeight:r.Ay.typography.lineHeight.tight,letterSpacing:"-0.025em",marginBottom:r.Ay.spacing[5],color:r.Ay.colors.text.primary},small:{fontSize:r.Ay.typography.fontSize["4xl"],fontWeight:r.Ay.typography.fontWeight.semibold,lineHeight:r.Ay.typography.lineHeight.tight,letterSpacing:"-0.025em",marginBottom:r.Ay.spacing[4],color:r.Ay.colors.text.primary}},heading:{h1:{fontSize:r.Ay.typography.fontSize["3xl"],fontWeight:r.Ay.typography.fontWeight.bold,lineHeight:r.Ay.typography.lineHeight.tight,marginBottom:r.Ay.spacing[4],color:r.Ay.colors.text.primary},h2:{fontSize:r.Ay.typography.fontSize["2xl"],fontWeight:r.Ay.typography.fontWeight.semibold,lineHeight:r.Ay.typography.lineHeight.snug,marginBottom:r.Ay.spacing[3],color:r.Ay.colors.text.primary},h3:{fontSize:r.Ay.typography.fontSize.xl,fontWeight:r.Ay.typography.fontWeight.semibold,lineHeight:r.Ay.typography.lineHeight.snug,marginBottom:r.Ay.spacing[3],color:r.Ay.colors.text.primary},h4:{fontSize:r.Ay.typography.fontSize.lg,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.normal,marginBottom:r.Ay.spacing[2],color:r.Ay.colors.text.primary},h5:{fontSize:r.Ay.typography.fontSize.base,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.normal,marginBottom:r.Ay.spacing[2],color:r.Ay.colors.text.primary},h6:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.normal,marginBottom:r.Ay.spacing[1],color:r.Ay.colors.text.secondary,textTransform:"uppercase",letterSpacing:"0.05em"}},body:{large:{fontSize:r.Ay.typography.fontSize.lg,fontWeight:r.Ay.typography.fontWeight.regular,lineHeight:r.Ay.typography.lineHeight.relaxed,marginBottom:r.Ay.spacing[4],color:r.Ay.colors.text.primary},medium:{fontSize:r.Ay.typography.fontSize.base,fontWeight:r.Ay.typography.fontWeight.regular,lineHeight:r.Ay.typography.lineHeight.relaxed,marginBottom:r.Ay.spacing[3],color:r.Ay.colors.text.primary},small:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.regular,lineHeight:r.Ay.typography.lineHeight.normal,marginBottom:r.Ay.spacing[2],color:r.Ay.colors.text.secondary}},label:{large:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.normal,color:r.Ay.colors.text.primary},medium:{fontSize:r.Ay.typography.fontSize.xs,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.normal,color:r.Ay.colors.text.secondary},small:{fontSize:r.Ay.typography.fontSize.xs,fontWeight:r.Ay.typography.fontWeight.regular,lineHeight:r.Ay.typography.lineHeight.normal,color:r.Ay.colors.text.tertiary}},interactive:{button:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.tight,letterSpacing:"0.025em"},link:{fontSize:"inherit",fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:"inherit",color:r.Ay.colors.primary.main,textDecoration:"none"},tab:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.medium,lineHeight:r.Ay.typography.lineHeight.normal}}},s={component:{tight:r.Ay.spacing[1],normal:r.Ay.spacing[2],comfortable:r.Ay.spacing[3],loose:r.Ay.spacing[4]},element:{tight:r.Ay.spacing[2],normal:r.Ay.spacing[3],comfortable:r.Ay.spacing[4],loose:r.Ay.spacing[6]},section:{tight:r.Ay.spacing[6],normal:r.Ay.spacing[8],comfortable:r.Ay.spacing[12],loose:r.Ay.spacing[16]},layout:{tight:r.Ay.spacing[8],normal:r.Ay.spacing[12],comfortable:r.Ay.spacing[16],loose:r.Ay.spacing[24]}},d={card:{minimal:{background:r.Ay.colors.background.paper,border:"1px solid ".concat(r.Ay.colors.border.light),borderRadius:r.Ay.borderRadius.md,padding:r.Ay.spacing[4],boxShadow:"none"},elevated:{background:r.Ay.colors.background.paper,border:"1px solid ".concat(r.Ay.colors.border.light),borderRadius:r.Ay.borderRadius.lg,padding:r.Ay.spacing[6],boxShadow:r.Ay.shadows.md},prominent:{background:r.Ay.colors.background.paper,border:"1px solid ".concat(r.Ay.colors.border.light),borderRadius:r.Ay.borderRadius.lg,padding:r.Ay.spacing[8],boxShadow:r.Ay.shadows.lg}},section:{subtle:{background:r.Ay.colors.background.secondary,borderRadius:r.Ay.borderRadius.md,padding:r.Ay.spacing[4]},defined:{background:r.Ay.colors.background.paper,border:"1px solid ".concat(r.Ay.colors.border.light),borderRadius:r.Ay.borderRadius.md,padding:r.Ay.spacing[6]},prominent:{background:r.Ay.colors.background.paper,border:"2px solid ".concat(r.Ay.colors.primary.light),borderRadius:r.Ay.borderRadius.lg,padding:r.Ay.spacing[8]}},list:{simple:{gap:r.Ay.spacing[2],divider:"none"},divided:{gap:r.Ay.spacing[3],divider:"1px solid ".concat(r.Ay.colors.border.light)},spaced:{gap:r.Ay.spacing[4],divider:"none"}}},p={button:{primary:{default:{background:r.Ay.colors.primary.main,color:r.Ay.colors.primary.contrastText,border:"1px solid ".concat(r.Ay.colors.primary.main),boxShadow:"none"},hover:{background:r.Ay.colors.primary.dark,color:r.Ay.colors.primary.contrastText,border:"1px solid ".concat(r.Ay.colors.primary.dark),boxShadow:r.Ay.shadows.sm,transform:"translateY(-1px)"},active:{background:r.Ay.colors.primary.dark,color:r.Ay.colors.primary.contrastText,border:"1px solid ".concat(r.Ay.colors.primary.dark),boxShadow:r.Ay.shadows.inner,transform:"translateY(0)"},focus:{outline:"2px solid ".concat(r.Ay.colors.primary.main),outlineOffset:"2px"},disabled:{background:r.Ay.colors.neutral[300],color:r.Ay.colors.neutral[500],border:"1px solid ".concat(r.Ay.colors.neutral[300]),cursor:"not-allowed",opacity:.6}},secondary:{default:{background:"transparent",color:r.Ay.colors.primary.main,border:"1px solid ".concat(r.Ay.colors.primary.main),boxShadow:"none"},hover:{background:r.Ay.colors.primary.light,color:r.Ay.colors.primary.dark,border:"1px solid ".concat(r.Ay.colors.primary.main),boxShadow:r.Ay.shadows.sm},active:{background:r.Ay.colors.primary.light,color:r.Ay.colors.primary.dark,border:"1px solid ".concat(r.Ay.colors.primary.dark),boxShadow:r.Ay.shadows.inner},focus:{outline:"2px solid ".concat(r.Ay.colors.primary.main),outlineOffset:"2px"}}},interactive:{default:{cursor:"pointer",transition:r.Ay.transitions.default},hover:{background:r.Ay.colors.interactive.hover,transform:"translateY(-1px)",boxShadow:r.Ay.shadows.sm},active:{background:r.Ay.colors.interactive.pressed,transform:"translateY(0)",boxShadow:r.Ay.shadows.inner},focus:{outline:"2px solid ".concat(r.Ay.colors.primary.main),outlineOffset:"2px"},selected:{background:r.Ay.colors.interactive.selected,border:"1px solid ".concat(r.Ay.colors.primary.main)},disabled:{background:r.Ay.colors.interactive.disabled,cursor:"not-allowed",opacity:.6}},dragDrop:{draggable:{cursor:"grab",transition:r.Ay.transitions.default},dragging:{cursor:"grabbing",opacity:.8,transform:"rotate(2deg) scale(1.02)",boxShadow:r.Ay.shadows.lg,zIndex:r.Ay.zIndex.dragOverlay},dropTarget:{background:r.Ay.colors.success.light,border:"2px dashed ".concat(r.Ay.colors.success.main),borderRadius:r.Ay.borderRadius.md},dropActive:{background:r.Ay.colors.success.light,border:"2px solid ".concat(r.Ay.colors.success.main),borderRadius:r.Ay.borderRadius.md,boxShadow:"0 0 0 4px ".concat(r.Ay.colors.success.main,"20")}}},u={navigation:{primary:{fontSize:r.Ay.typography.fontSize.base,fontWeight:r.Ay.typography.fontWeight.medium,spacing:r.Ay.spacing[6],hierarchy:"horizontal"},secondary:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.regular,spacing:r.Ay.spacing[4],hierarchy:"vertical"},breadcrumb:{fontSize:r.Ay.typography.fontSize.sm,fontWeight:r.Ay.typography.fontWeight.regular,spacing:r.Ay.spacing[2],separator:"/"}},content:{hero:{alignment:"center",spacing:s.layout.comfortable,typography:c.display.large},section:{alignment:"left",spacing:s.section.normal,typography:c.heading.h2},subsection:{alignment:"left",spacing:s.element.comfortable,typography:c.heading.h3},paragraph:{alignment:"left",spacing:s.element.normal,typography:c.body.medium}},form:{fieldGroup:{spacing:s.element.comfortable,grouping:d.section.subtle},field:{spacing:s.element.normal,labelSpacing:r.Ay.spacing[1]},actions:{spacing:s.element.normal,alignment:"right",grouping:"horizontal"}},data:{table:{headerSpacing:s.component.comfortable,cellSpacing:s.component.normal,rowSpacing:r.Ay.spacing[2]},list:{itemSpacing:s.element.normal,groupSpacing:s.section.tight},card:{contentSpacing:s.component.comfortable,actionSpacing:s.element.normal}}},m={getTypography:function(e,n){var t;return(null===(t=c[e])||void 0===t?void 0:t[n])||{}},getSpacing:function(e,n){var t;return(null===(t=s[e])||void 0===t?void 0:t[n])||r.Ay.spacing[4]},getGrouping:function(e,n){var t;return(null===(t=d[e])||void 0===t?void 0:t[n])||{}},getInteraction:function(e,n){var t;return(null===(t=p[e])||void 0===t?void 0:t[n])||{}},createResponsiveTypography:function(e,n,t){return l(l({},m.getTypography.apply(m,(0,o.A)(e))),{},(0,i.A)((0,i.A)({},r.Ay.mediaQueries.maxLg,m.getTypography.apply(m,(0,o.A)(n))),r.Ay.mediaQueries.maxMd,m.getTypography.apply(m,(0,o.A)(t))))},createResponsiveSpacing:function(e,n,t){return(0,i.A)((0,i.A)({margin:m.getSpacing.apply(m,(0,o.A)(e))},r.Ay.mediaQueries.maxLg,{margin:m.getSpacing.apply(m,(0,o.A)(n))}),r.Ay.mediaQueries.maxMd,{margin:m.getSpacing.apply(m,(0,o.A)(t))})}};m.getTypography("heading","h4"),m.getTypography("heading","h5"),m.getTypography("label","medium"),m.getTypography("label","small"),s.section.tight,s.element.comfortable,s.element.normal,m.getTypography("heading","h4"),m.getTypography("heading","h6"),m.getTypography("label","large"),m.getTypography("label","small"),s.section.tight,s.element.comfortable,s.element.normal,m.getTypography("label","medium"),m.getTypography("label","small"),m.getTypography("label","small"),s.component.comfortable,s.component.normal,m.getTypography("heading","h3"),m.getTypography("body","medium"),m.getTypography("interactive","button"),s.section.tight,s.element.comfortable,s.element.normal,m.getTypography("heading","h4"),m.getTypography("body","small"),m.getTypography("body","medium"),m.getTypography("label","small"),s.element.normal,s.element.comfortable,s.element.normal,m.getTypography("heading","h3"),m.getTypography("heading","h5"),m.getTypography("label","large"),m.getTypography("label","small"),l(l({},m.getTypography("label","small")),{},{color:r.Ay.colors.error.main}),s.section.normal,s.element.comfortable,s.element.normal,"2px solid ".concat(r.Ay.colors.primary.main),r.Ay.borderRadius.sm,r.Ay.accessibility.srOnly,r.Ay.spacing[2],r.Ay.colors.primary.main,r.Ay.colors.primary.contrastText,"".concat(r.Ay.spacing[2]," ").concat(r.Ay.spacing[4]),r.Ay.borderRadius.md,r.Ay.typography.fontWeight.medium,r.Ay.zIndex.skipLink,r.Ay.transitions.default,r.Ay.spacing[2];var y=t(5544),g=t(82284);function h(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function f(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?h(Object(t),!0).forEach((function(n){(0,i.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):h(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}function A(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}var b={getColor:function(e){var n,t=e.split("."),o=r.Ay.colors,i=function(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,n){if(e){if("string"==typeof e)return A(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?A(e,n):void 0}}(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,l=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){l=!0,i=e},f:function(){try{a||null==t.return||t.return()}finally{if(l)throw i}}}}(t);try{for(i.s();!(n=i.n()).done;){var a=n.value;if(!o||"object"!==(0,g.A)(o)||!(a in o))return console.warn('Color path "'.concat(e,'" not found in theme')),r.Ay.colors.neutral[500];o=o[a]}}catch(e){i.e(e)}finally{i.f()}return o},hexToRgba:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,t=parseInt(e.slice(1,3),16),r=parseInt(e.slice(3,5),16),o=parseInt(e.slice(5,7),16);return"rgba(".concat(t,", ").concat(r,", ").concat(o,", ").concat(n,")")},lighten:function(e,n){var t=parseInt(e.replace("#",""),16),r=Math.round(2.55*n),o=(t>>16)+r,i=(t>>8&255)+r,a=(255&t)+r;return"#"+(16777216+65536*(o<255?o<1?0:o:255)+256*(i<255?i<1?0:i:255)+(a<255?a<1?0:a:255)).toString(16).slice(1)},darken:function(e,n){return b.lighten(e,-n)},checkContrast:function(e,n){return!0}},v={getSpacing:function(e){return"number"==typeof e?"".concat(.25*e,"rem"):r.Ay.spacing[e]||e},responsive:function(e){var n="";return Object.entries(e).forEach((function(e){var t=(0,y.A)(e,2),o=t[0],i=t[1];n+="base"===o?"".concat(v.getSpacing(i),";"):"".concat(r.Ay.mediaQueries[o]," { ").concat(v.getSpacing(i),"; }")})),n}},x={getTextStyle:function(e){return r.Ay.typography.textStyles[e]||{}},responsiveText:function(e){var n={};return Object.entries(e).forEach((function(e){var t=(0,y.A)(e,2),o=t[0],i=t[1];"base"===o?Object.assign(n,x.getTextStyle(i)):n[r.Ay.mediaQueries[o]]=x.getTextStyle(i)})),n}},E={focusRing:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r.Ay.colors.primary.main;return{outline:"".concat(r.Ay.accessibility.focusRing.width," ").concat(r.Ay.accessibility.focusRing.style," ").concat(e),outlineOffset:r.Ay.accessibility.focusRing.offset}},srOnly:function(){return r.Ay.accessibility.srOnly},touchTarget:function(){return{minWidth:r.Ay.accessibility.minTouchTarget.width,minHeight:r.Ay.accessibility.minTouchTarget.height}},highContrast:function(){return{"@media (prefers-contrast: high)":r.Ay.accessibility.highContrast}}},F={getAnimation:function(e){return r.Ay.animations[e]||e},withReducedMotion:function(e){return(0,i.A)({animation:e},r.Ay.mediaQueries.reducedMotion,{animation:"none"})}},w={getComponentTokens:function(e){return r.Ay.components[e]||{}},getVariantStyles:function(e){var n,t,o,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"primary",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"md",l=w.getComponentTokens(e),c={height:null===(n=l.height)||void 0===n?void 0:n[a],padding:null===(t=l.padding)||void 0===t?void 0:t[a],fontSize:null===(o=l.fontSize)||void 0===o?void 0:o[a],borderRadius:r.Ay.borderRadius[e]||r.Ay.borderRadius.md,transition:r.Ay.transitions.default},s={primary:{backgroundColor:r.Ay.colors.primary.main,color:r.Ay.colors.primary.contrastText,border:"1px solid ".concat(r.Ay.colors.primary.main),"&:hover":{backgroundColor:r.Ay.colors.primary.dark},"&:focus":f({},E.focusRing())},secondary:{backgroundColor:"transparent",color:r.Ay.colors.primary.main,border:"1px solid ".concat(r.Ay.colors.primary.main),"&:hover":{backgroundColor:r.Ay.colors.primary.light},"&:focus":f({},E.focusRing())},ghost:{backgroundColor:"transparent",color:r.Ay.colors.text.primary,border:"1px solid transparent","&:hover":{backgroundColor:r.Ay.colors.interactive.hover},"&:focus":f({},E.focusRing())}};return f(f({},c),s[i])}},S={responsive:function(e){var n={};return Object.entries(e).forEach((function(e){var t=(0,y.A)(e,2),o=t[0],i=t[1];"base"===o?Object.assign(n,i):n[r.Ay.mediaQueries[o]]=i})),n},matchesBreakpoint:function(e){if("undefined"==typeof window)return!1;var n=parseInt(r.Ay.breakpoints[e]);return window.innerWidth>=n}};const k={color:b,spacing:v,typography:x,a11y:E,animation:F,component:w,responsive:S};var z,T=t(58168),C=t(53986),O=t(57528),I=t(96540),D=t(5556),B=t.n(D),W=t(18563),P=["children","variant","size","disabled","fullWidth","startIcon","endIcon","onClick"],R=W.I4.button(z||(z=(0,O.A)(["\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: "," ",";\n  border-radius: ",";\n  font-family: ",";\n  font-weight: ",";\n  font-size: ",";\n  transition: ",";\n  cursor: ",";\n  opacity: ",";\n  width: ",";\n\n  /* Variant styles */\n  background-color: ",";\n\n  color: ",";\n\n  border: ",";\n\n  &:hover {\n    background-color: ",";\n\n    color: ",";\n  }\n\n  &:focus {\n    outline: none;\n    box-shadow: 0 0 0 3px ",";\n  }\n\n  .button-start-icon {\n    margin-right: ",";\n  }\n\n  .button-end-icon {\n    margin-left: ",";\n  }\n"])),(function(e){return"small"===e.size?r.Ay.spacing[2]:"large"===e.size?r.Ay.spacing[4]:r.Ay.spacing[3]}),(function(e){return"small"===e.size?r.Ay.spacing[3]:"large"===e.size?r.Ay.spacing[6]:r.Ay.spacing[4]}),r.Ay.borderRadius.md,r.Ay.typography.fontFamily.primary,r.Ay.typography.fontWeight.medium,(function(e){return"small"===e.size?r.Ay.typography.fontSize.sm:"large"===e.size?r.Ay.typography.fontSize.lg:r.Ay.typography.fontSize.md}),r.Ay.transitions.default,(function(e){return e.disabled?"not-allowed":"pointer"}),(function(e){return e.disabled?.6:1}),(function(e){return e.fullWidth?"100%":"auto"}),(function(e){return"primary"===e.variant?r.Ay.colors.primary.main:"secondary"===e.variant?r.Ay.colors.secondary.main:"outline"===e.variant||"text"===e.variant?"transparent":r.Ay.colors.primary.main}),(function(e){return"outline"===e.variant||"text"===e.variant?r.Ay.colors.primary.main:r.Ay.colors.primary.contrastText}),(function(e){return"outline"===e.variant?"1px solid ".concat(r.Ay.colors.primary.main):"none"}),(function(e){return"primary"===e.variant?r.Ay.colors.primary.dark:"secondary"===e.variant?r.Ay.colors.secondary.dark:"outline"===e.variant||"text"===e.variant?r.Ay.colors.primary.light:r.Ay.colors.primary.dark}),(function(e){return"outline"===e.variant||"text"===e.variant?r.Ay.colors.primary.dark:r.Ay.colors.primary.contrastText}),r.Ay.colors.primary.light,r.Ay.spacing[2],r.Ay.spacing[2]),H=function(e){var n=e.children,t=e.variant,r=void 0===t?"primary":t,o=e.size,i=void 0===o?"medium":o,a=e.disabled,l=void 0!==a&&a,c=e.fullWidth,s=void 0!==c&&c,d=e.startIcon,p=e.endIcon,u=e.onClick,m=(0,C.A)(e,P);return I.createElement(R,(0,T.A)({variant:r,size:i,disabled:l,fullWidth:s,onClick:l?void 0:u},m),d&&I.createElement("span",{className:"button-start-icon"},d),n,p&&I.createElement("span",{className:"button-end-icon"},p))};H.propTypes={children:B().node.isRequired,variant:B().oneOf(["primary","secondary","outline","text"]),size:B().oneOf(["small","medium","large"]),disabled:B().bool,fullWidth:B().bool,startIcon:B().node,endIcon:B().node,onClick:B().func};const j=H;var _,N,q,L,M,U=["children","elevation","radius","fullWidth","fullHeight"],V=["children","divider"],G=["children"],K=["children"],Q=["children","divider","align"],Y=W.I4.div(_||(_=(0,O.A)(["\n  background-color: white;\n  border-radius: ",";\n  box-shadow: ",";\n  overflow: hidden;\n  width: ",";\n  height: ",";\n  display: flex;\n  flex-direction: column;\n"])),(function(e){return r.Ay.borderRadius[e.radius]}),(function(e){return r.Ay.shadows[e.elevation]}),(function(e){return e.fullWidth?"100%":"auto"}),(function(e){return e.fullHeight?"100%":"auto"})),J=W.I4.div(N||(N=(0,O.A)(["\n  padding: ",";\n  border-bottom: ",";\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n"])),r.Ay.spacing[4],(function(e){return e.divider?"1px solid ".concat(r.Ay.colors.neutral[200]):"none"})),$=W.I4.h3(q||(q=(0,O.A)(["\n  margin: 0;\n  font-size: ",";\n  font-weight: ",";\n  color: ",";\n"])),r.Ay.typography.fontSize.lg,r.Ay.typography.fontWeight.semibold,r.Ay.colors.neutral[900]),X=W.I4.div(L||(L=(0,O.A)(["\n  padding: ",";\n  flex: 1;\n"])),r.Ay.spacing[4]),Z=W.I4.div(M||(M=(0,O.A)(["\n  padding: ",";\n  border-top: ",";\n  display: flex;\n  align-items: center;\n  justify-content: ",";\n  gap: ",";\n"])),r.Ay.spacing[4],(function(e){return e.divider?"1px solid ".concat(r.Ay.colors.neutral[200]):"none"}),(function(e){return"right"===e.align?"flex-end":"center"===e.align?"center":"flex-start"}),r.Ay.spacing[2]),ee=function(e){var n=e.children,t=e.elevation,r=void 0===t?"md":t,o=e.radius,i=void 0===o?"md":o,a=e.fullWidth,l=void 0!==a&&a,c=e.fullHeight,s=void 0!==c&&c,d=(0,C.A)(e,U);return I.createElement(Y,(0,T.A)({elevation:r,radius:i,fullWidth:l,fullHeight:s},d),n)};ee.Header=function(e){var n=e.children,t=e.divider,r=void 0!==t&&t,o=(0,C.A)(e,V);return I.createElement(J,(0,T.A)({divider:r},o),n)},ee.Title=function(e){var n=e.children,t=(0,C.A)(e,G);return I.createElement($,t,n)},ee.Content=function(e){var n=e.children,t=(0,C.A)(e,K);return I.createElement(X,t,n)},ee.Footer=function(e){var n=e.children,t=e.divider,r=void 0===t||t,o=e.align,i=void 0===o?"right":o,a=(0,C.A)(e,Q);return I.createElement(Z,(0,T.A)({divider:r,align:i},a),n)},ee.propTypes={children:B().node.isRequired,elevation:B().oneOf(["none","sm","md","lg","xl","2xl"]),radius:B().oneOf(["none","sm","md","lg","xl","2xl","3xl","full"]),fullWidth:B().bool,fullHeight:B().bool},ee.Header.propTypes={children:B().node.isRequired,divider:B().bool},ee.Title.propTypes={children:B().node.isRequired},ee.Content.propTypes={children:B().node.isRequired},ee.Footer.propTypes={children:B().node.isRequired,divider:B().bool,align:B().oneOf(["left","center","right"])};const ne=ee;var te,re,oe,ie,ae,le,ce,se,de,pe,ue,me,ye,ge,he,fe,Ae,be,ve,xe,Ee,Fe=["label","helperText","error","fullWidth","prefix","suffix","disabled"],we=W.I4.div(te||(te=(0,O.A)(["\n  display: flex;\n  flex-direction: column;\n  width: ",";\n"])),(function(e){return e.fullWidth?"100%":"auto"})),Se=W.I4.label(re||(re=(0,O.A)(["\n  font-size: ",";\n  font-weight: ",";\n  color: ",";\n  margin-bottom: ",";\n"])),r.Ay.typography.fontSize.sm,r.Ay.typography.fontWeight.medium,r.Ay.colors.neutral[700],r.Ay.spacing[1]),ke=W.I4.input(oe||(oe=(0,O.A)(["\n  font-family: ",";\n  font-size: ",";\n  color: ",";\n  background-color: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  padding: "," ",";\n  transition: ",";\n  width: 100%;\n\n  &:focus {\n    outline: none;\n    border-color: ",";\n    box-shadow: 0 0 0 3px ",";\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n    opacity: 0.7;\n  }\n\n  &::placeholder {\n    color: ",";\n  }\n"])),(null===r.Ay||void 0===r.Ay||null===(ie=r.Ay.typography)||void 0===ie||null===(ie=ie.fontFamily)||void 0===ie?void 0:ie.primary)||"Inter, sans-serif",(null===r.Ay||void 0===r.Ay||null===(ae=r.Ay.typography)||void 0===ae||null===(ae=ae.fontSize)||void 0===ae?void 0:ae.md)||"16px",(null===r.Ay||void 0===r.Ay||null===(le=r.Ay.colors)||void 0===le||null===(le=le.neutral)||void 0===le?void 0:le[900])||"#111827",(function(e){var n;return e.disabled?(null===r.Ay||void 0===r.Ay||null===(n=r.Ay.colors)||void 0===n||null===(n=n.neutral)||void 0===n?void 0:n[100])||"#F3F4F6":"white"}),(function(e){var n,t,o;return e.error?(null===r.Ay||void 0===r.Ay||null===(n=r.Ay.colors)||void 0===n||null===(n=n.error)||void 0===n?void 0:n.main)||"#DC2626":e.focused?(null===r.Ay||void 0===r.Ay||null===(t=r.Ay.colors)||void 0===t||null===(t=t.primary)||void 0===t?void 0:t.main)||"#2563EB":(null===r.Ay||void 0===r.Ay||null===(o=r.Ay.colors)||void 0===o||null===(o=o.neutral)||void 0===o?void 0:o[300])||"#D1D5DB"}),(null===r.Ay||void 0===r.Ay||null===(ce=r.Ay.borderRadius)||void 0===ce?void 0:ce.md)||"4px",(null===r.Ay||void 0===r.Ay||null===(se=r.Ay.spacing)||void 0===se?void 0:se[2])||"8px",(null===r.Ay||void 0===r.Ay||null===(de=r.Ay.spacing)||void 0===de?void 0:de[3])||"12px",(null===r.Ay||void 0===r.Ay||null===(pe=r.Ay.transitions)||void 0===pe?void 0:pe.default)||"all 0.2s ease-in-out",(null===r.Ay||void 0===r.Ay||null===(ue=r.Ay.colors)||void 0===ue||null===(ue=ue.primary)||void 0===ue?void 0:ue.main)||"#2563EB",(null===r.Ay||void 0===r.Ay||null===(me=r.Ay.colors)||void 0===me||null===(me=me.primary)||void 0===me?void 0:me.light)||"rgba(37, 99, 235, 0.2)",(null===r.Ay||void 0===r.Ay||null===(ye=r.Ay.colors)||void 0===ye||null===(ye=ye.neutral)||void 0===ye?void 0:ye[400])||"#9CA3AF"),ze=W.I4.div(ge||(ge=(0,O.A)(["\n  display: flex;\n  align-items: center;\n  margin-right: ",";\n"])),(null===r.Ay||void 0===r.Ay||null===(he=r.Ay.spacing)||void 0===he?void 0:he[2])||"8px"),Te=W.I4.div(fe||(fe=(0,O.A)(["\n  display: flex;\n  align-items: center;\n  margin-left: ",";\n"])),(null===r.Ay||void 0===r.Ay||null===(Ae=r.Ay.spacing)||void 0===Ae?void 0:Ae[2])||"8px"),Ce=W.I4.div(be||(be=(0,O.A)(["\n  display: flex;\n  align-items: center;\n  position: relative;\n  width: 100%;\n"]))),Oe=W.I4.div(ve||(ve=(0,O.A)(["\n  font-size: ",";\n  margin-top: ",";\n  color: ",";\n"])),(null===r.Ay||void 0===r.Ay||null===(xe=r.Ay.typography)||void 0===xe||null===(xe=xe.fontSize)||void 0===xe?void 0:xe.xs)||"12px",(null===r.Ay||void 0===r.Ay||null===(Ee=r.Ay.spacing)||void 0===Ee?void 0:Ee[1])||"4px",(function(e){var n,t;return e.error?(null===r.Ay||void 0===r.Ay||null===(n=r.Ay.colors)||void 0===n||null===(n=n.error)||void 0===n?void 0:n.main)||"#DC2626":(null===r.Ay||void 0===r.Ay||null===(t=r.Ay.colors)||void 0===t||null===(t=t.neutral)||void 0===t?void 0:t[500])||"#6B7280"})),Ie=(0,I.forwardRef)((function(e,n){var t=e.label,r=e.helperText,o=e.error,i=e.fullWidth,a=void 0!==i&&i,l=e.prefix,c=e.suffix,s=e.disabled,d=void 0!==s&&s,p=(0,C.A)(e,Fe),u=I.useState(!1),m=(0,y.A)(u,2),g=m[0],h=m[1];return I.createElement(we,{fullWidth:a},t&&I.createElement(Se,null,t),I.createElement(Ce,null,l&&I.createElement(ze,null,l),I.createElement(ke,(0,T.A)({ref:n,disabled:d,error:o,focused:g,onFocus:function(e){h(!0),p.onFocus&&p.onFocus(e)},onBlur:function(e){h(!1),p.onBlur&&p.onBlur(e)}},p)),c&&I.createElement(Te,null,c)),r&&I.createElement(Oe,{error:o},r))}));Ie.displayName="Input",Ie.propTypes={label:B().string,helperText:B().string,error:B().bool,fullWidth:B().bool,prefix:B().node,suffix:B().node,disabled:B().bool,onFocus:B().func,onBlur:B().func};const De=Ie;var Be,We,Pe,Re,He=["label","helperText","error","fullWidth","options","disabled"],je=W.I4.div(Be||(Be=(0,O.A)(["\n  display: flex;\n  flex-direction: column;\n  width: ",";\n"])),(function(e){return e.fullWidth?"100%":"auto"})),_e=W.I4.label(We||(We=(0,O.A)(["\n  font-size: ",";\n  font-weight: ",";\n  color: ",";\n  margin-bottom: ",";\n"])),r.Ay.typography.fontSize.sm,r.Ay.typography.fontWeight.medium,r.Ay.colors.neutral[700],r.Ay.spacing[1]),Ne=W.I4.select(Pe||(Pe=(0,O.A)(["\n  font-family: ",";\n  font-size: ",";\n  color: ",";\n  background-color: ",";\n  border: 1px solid ",";\n  border-radius: ",";\n  padding: "," ",";\n  transition: ",";\n  width: 100%;\n  appearance: none;\n  background-image: url(\"data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E\");\n  background-position: right "," center;\n  background-repeat: no-repeat;\n  background-size: 1.5em 1.5em;\n  padding-right: 2.5em;\n\n  &:focus {\n    outline: none;\n    border-color: ",";\n    box-shadow: 0 0 0 3px ",";\n  }\n\n  &:disabled {\n    cursor: not-allowed;\n    opacity: 0.7;\n  }\n"])),r.Ay.typography.fontFamily.primary,r.Ay.typography.fontSize.md,r.Ay.colors.neutral[900],(function(e){return e.disabled?r.Ay.colors.neutral[100]:"white"}),(function(e){return e.error?r.Ay.colors.error.main:e.focused?r.Ay.colors.primary.main:r.Ay.colors.neutral[300]}),r.Ay.borderRadius.md,r.Ay.spacing[2],r.Ay.spacing[3],r.Ay.transitions.default,r.Ay.spacing[2],r.Ay.colors.primary.main,r.Ay.colors.primary.light),qe=W.I4.div(Re||(Re=(0,O.A)(["\n  font-size: ",";\n  margin-top: ",";\n  color: ",";\n"])),r.Ay.typography.fontSize.xs,r.Ay.spacing[1],(function(e){return e.error?r.Ay.colors.error.main:r.Ay.colors.neutral[500]})),Le=(0,I.forwardRef)((function(e,n){var t=e.label,r=e.helperText,o=e.error,i=e.fullWidth,a=void 0!==i&&i,l=e.options,c=void 0===l?[]:l,s=e.disabled,d=void 0!==s&&s,p=(0,C.A)(e,He),u=I.useState(!1),m=(0,y.A)(u,2),g=m[0],h=m[1];return I.createElement(je,{fullWidth:a},t&&I.createElement(_e,null,t),I.createElement(Ne,(0,T.A)({ref:n,disabled:d,error:o,focused:g,onFocus:function(e){h(!0),p.onFocus&&p.onFocus(e)},onBlur:function(e){h(!1),p.onBlur&&p.onBlur(e)}},p),c.map((function(e){return I.createElement("option",{key:e.value,value:e.value},e.label)}))),r&&I.createElement(qe,{error:o},r))}));Le.displayName="Select",Le.propTypes={label:B().string,helperText:B().string,error:B().bool,fullWidth:B().bool,options:B().arrayOf(B().shape({value:B().oneOfType([B().string,B().number]).isRequired,label:B().string.isRequired})),disabled:B().bool,onFocus:B().func,onBlur:B().func};const Me=Le;var Ue,Ve,Ge,Ke,Qe,Ye,Je,$e,Xe,Ze=t(70572),en=Ze.Ay.span(Ue||(Ue=(0,O.A)(["\n  font-family: ",";\n  font-size: ",";\n  font-weight: ",";\n  color: ",";\n  line-height: ",";\n  margin: 0;\n  ","\n"])),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.typography)||void 0===n?void 0:n.fontFamily)||"Inter, sans-serif"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.typography)||void 0===n||null===(n=n.fontSize)||void 0===n?void 0:n[e.size||"md"])||"16px"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.typography)||void 0===n||null===(n=n.fontWeight)||void 0===n?void 0:n[e.weight||"normal"])||400}),(function(e){var n,t;return e.color?(null===(n=e.theme)||void 0===n||null===(n=n.colors)||void 0===n?void 0:n[e.color])||e.color:(null===(t=e.theme)||void 0===t||null===(t=t.colors)||void 0===t||null===(t=t.text)||void 0===t?void 0:t.primary)||"#111827"}),(function(e){var n;return(null===(n=e.theme)||void 0===n||null===(n=n.typography)||void 0===n||null===(n=n.lineHeight)||void 0===n?void 0:n[e.lineHeight||"normal"])||1.5}),(function(e){return e.truncate&&"\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  "})),nn=((0,Ze.Ay)(en)(Ve||(Ve=(0,O.A)(["\n  display: block;\n  margin-bottom: ",";\n"])),(function(e){return e.theme.spacing[3]})),(0,Ze.Ay)(en)(Ge||(Ge=(0,O.A)(["\n  font-weight: ",";\n  line-height: ",";\n  margin-bottom: ",";\n"])),(function(e){return e.theme.typography.fontWeight.bold}),(function(e){return e.theme.typography.lineHeight.tight}),(function(e){return e.theme.spacing[3]})));(0,Ze.Ay)(nn)(Ke||(Ke=(0,O.A)(["\n  font-size: ",";\n"])),(function(e){return e.theme.typography.fontSize.xxxl})),(0,Ze.Ay)(nn)(Qe||(Qe=(0,O.A)(["\n  font-size: ",";\n"])),(function(e){return e.theme.typography.fontSize.xxl})),(0,Ze.Ay)(nn)(Ye||(Ye=(0,O.A)(["\n  font-size: ",";\n"])),(function(e){return e.theme.typography.fontSize.xl})),(0,Ze.Ay)(nn)(Je||(Je=(0,O.A)(["\n  font-size: ",";\n"])),(function(e){return e.theme.typography.fontSize.lg})),(0,Ze.Ay)(nn)($e||($e=(0,O.A)(["\n  font-size: ",";\n"])),(function(e){return e.theme.typography.fontSize.md})),(0,Ze.Ay)(nn)(Xe||(Xe=(0,O.A)(["\n  font-size: ",";\n"])),(function(e){return e.theme.typography.fontSize.sm}));const tn=en;var rn="./global.css",on="2.0.0",an={prefix:"ds-",enableRTL:!1,enableDarkMode:!0,enableHighContrast:!0,enableReducedMotion:!0}},81945:(e,n,t)=>{t.d(n,{Sn:()=>i});var r="http://localhost:8000",o=r;console.log("API Base URL:","http://localhost:8000"),console.log("Development mode:",!1),console.log("API Prefix:",o);var i={CSRF_TOKEN:"".concat(o,"/csrf-token/"),STATUS:["".concat(o,"/status/"),"".concat(o,"/health/"),"".concat(o,"/health-check/")],HEALTH:["".concat(o,"/health/"),"".concat(o,"/health-check/"),"".concat(o,"/status/")],APP_DATA:["".concat(o,"/get_app_data/"),"".concat(o,"/apps/"),"".concat(o,"/v1/apps/"),"".concat(o,"/app-data/")],SAVE_APP_DATA:["".concat(o,"/save_app_data/"),"".concat(o,"/apps/"),"".concat(o,"/v1/apps/"),"".concat(o,"/app-data/")],EXPORT_APP_DATA:["".concat(o,"/api/app-data/export/"),"".concat(o,"/export_app_data/")],IMPORT_APP_DATA:["".concat(o,"/api/app-data/import/"),"".concat(o,"/import_app_data/")],GENERATE_AI_SUGGESTIONS:["".concat(r,"/api/ai/suggestions/"),"".concat(r,"/generate_ai_suggestions/")],GENERATE_IMAGE:["".concat(r,"/api/ai/generate-image/"),"".concat(r,"/generate_image/")],LOGIN:["".concat(r,"/api/auth/login/"),"".concat(r,"/auth/login/")],REGISTER:["".concat(r,"/api/auth/register/"),"".concat(r,"/auth/register/")],USER_PROFILE:["".concat(r,"/api/auth/profile/"),"".concat(r,"/auth/profile/")],UPDATE_PROFILE:["".concat(r,"/api/auth/profile/update/"),"".concat(r,"/auth/profile/update/")],API_KEYS:["".concat(r,"/api/api-keys/"),"".concat(r,"/api/v1/api-keys/")],VALIDATE_API_KEY:["".concat(r,"/api/validate-api-key/"),"".concat(r,"/api/v1/validate-api-key/")],API_V1:"".concat(r,"/api/v1/"),API_V2:"".concat(r,"/api/v2/"),GRAPHQL:"".concat(r,"/graphql/")}},86020:(e,n,t)=>{t.d(n,{Ay:()=>g,Eo:()=>a,HP:()=>p,Il:()=>o,Ti:()=>u,Tj:()=>r,Vq:()=>l,WT:()=>m,YK:()=>i,bm:()=>c,dK:()=>y,fE:()=>s,fi:()=>d});var r={primary:{50:"#EFF6FF",100:"#DBEAFE",200:"#BFDBFE",300:"#93C5FD",400:"#60A5FA",500:"#3B82F6",600:"#2563EB",700:"#1D4ED8",800:"#1E40AF",900:"#1E3A8A",main:"#2563EB",light:"#DBEAFE",dark:"#1E40AF",contrastText:"#FFFFFF"},secondary:{50:"#ECFDF5",100:"#D1FAE5",200:"#A7F3D0",300:"#6EE7B7",400:"#34D399",500:"#10B981",600:"#059669",700:"#047857",800:"#065F46",900:"#064E3B",main:"#10B981",light:"#D1FAE5",dark:"#047857",contrastText:"#FFFFFF"},accent:{50:"#FAF5FF",100:"#F3E8FF",200:"#E9D5FF",300:"#D8B4FE",400:"#C084FC",500:"#A855F7",600:"#9333EA",700:"#7C3AED",800:"#6B21A8",900:"#581C87",main:"#8B5CF6",light:"#EDE9FE",dark:"#6D28D9",contrastText:"#FFFFFF"},neutral:{0:"#FFFFFF",50:"#F9FAFB",100:"#F3F4F6",200:"#E5E7EB",300:"#D1D5DB",400:"#9CA3AF",500:"#6B7280",600:"#4B5563",700:"#374151",800:"#1F2937",900:"#111827",950:"#030712"},success:{50:"#F0FDF4",100:"#DCFCE7",200:"#BBF7D0",300:"#86EFAC",400:"#4ADE80",500:"#22C55E",600:"#16A34A",700:"#15803D",800:"#166534",900:"#14532D",main:"#16A34A",light:"#DCFCE7",dark:"#15803D",contrastText:"#FFFFFF"},warning:{50:"#FFFBEB",100:"#FEF3C7",200:"#FDE68A",300:"#FCD34D",400:"#FBBF24",500:"#F59E0B",600:"#D97706",700:"#B45309",800:"#92400E",900:"#78350F",main:"#D97706",light:"#FEF3C7",dark:"#B45309",contrastText:"#FFFFFF"},error:{50:"#FEF2F2",100:"#FEE2E2",200:"#FECACA",300:"#FCA5A5",400:"#F87171",500:"#EF4444",600:"#DC2626",700:"#B91C1C",800:"#991B1B",900:"#7F1D1D",main:"#DC2626",light:"#FEE2E2",dark:"#B91C1C",contrastText:"#FFFFFF"},info:{50:"#EFF6FF",100:"#DBEAFE",200:"#BFDBFE",300:"#93C5FD",400:"#60A5FA",500:"#3B82F6",600:"#2563EB",700:"#1D4ED8",800:"#1E40AF",900:"#1E3A8A",main:"#2563EB",light:"#DBEAFE",dark:"#1D4ED8",contrastText:"#FFFFFF"},background:{default:"#FFFFFF",paper:"#FFFFFF",secondary:"#F9FAFB",tertiary:"#F3F4F6",overlay:"rgba(0, 0, 0, 0.5)",disabled:"#F3F4F6"},text:{primary:"#111827",secondary:"#4B5563",tertiary:"#6B7280",disabled:"#9CA3AF",hint:"#9CA3AF",inverse:"#FFFFFF",link:"#2563EB",linkHover:"#1D4ED8"},border:{default:"#E5E7EB",light:"#F3F4F6",medium:"#D1D5DB",dark:"#9CA3AF",focus:"#2563EB",error:"#DC2626",success:"#16A34A",warning:"#D97706"},interactive:{hover:"rgba(37, 99, 235, 0.04)",pressed:"rgba(37, 99, 235, 0.08)",focus:"rgba(37, 99, 235, 0.12)",selected:"rgba(37, 99, 235, 0.08)",disabled:"rgba(0, 0, 0, 0.04)"}},o={fontFamily:{primary:'"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',secondary:'"Inter", system-ui, sans-serif',code:'"Fira Code", "JetBrains Mono", "Roboto Mono", "Courier New", monospace',display:'"Inter", system-ui, sans-serif'},fontWeight:{thin:100,extralight:200,light:300,regular:400,medium:500,semibold:600,bold:700,extrabold:800,black:900},fontSize:{xs:"0.75rem",sm:"0.875rem",base:"1rem",md:"1rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem","6xl":"3.75rem","7xl":"4.5rem","8xl":"6rem","9xl":"8rem"},lineHeight:{none:1,tight:1.25,snug:1.375,normal:1.5,relaxed:1.625,loose:2,3:.75,4:1,5:1.25,6:1.5,7:1.75,8:2,9:2.25,10:2.5},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0",wide:"0.025em",wider:"0.05em",widest:"0.1em"},textStyles:{h1:{fontSize:"2.25rem",fontWeight:700,lineHeight:1.25,letterSpacing:"-0.025em"},h2:{fontSize:"1.875rem",fontWeight:600,lineHeight:1.25,letterSpacing:"-0.025em"},h3:{fontSize:"1.5rem",fontWeight:600,lineHeight:1.375},h4:{fontSize:"1.25rem",fontWeight:600,lineHeight:1.375},h5:{fontSize:"1.125rem",fontWeight:600,lineHeight:1.375},h6:{fontSize:"1rem",fontWeight:600,lineHeight:1.375},body1:{fontSize:"1rem",fontWeight:400,lineHeight:1.5},body2:{fontSize:"0.875rem",fontWeight:400,lineHeight:1.5},caption:{fontSize:"0.75rem",fontWeight:400,lineHeight:1.375},overline:{fontSize:"0.75rem",fontWeight:600,lineHeight:1.375,letterSpacing:"0.1em",textTransform:"uppercase"},button:{fontSize:"0.875rem",fontWeight:500,lineHeight:1.25,letterSpacing:"0.025em"},code:{fontFamily:'"Fira Code", "JetBrains Mono", "Roboto Mono", monospace',fontSize:"0.875rem",fontWeight:400,lineHeight:1.5}}},i={px:"1px",0:"0",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},a={none:"none",xs:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",sm:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",xl:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)","2xl":"0 25px 50px -12px rgba(0, 0, 0, 0.25)","3xl":"0 35px 60px -12px rgba(0, 0, 0, 0.35)",inner:"inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)",primary:"0 4px 14px 0 rgba(37, 99, 235, 0.15)",secondary:"0 4px 14px 0 rgba(16, 185, 129, 0.15)",error:"0 4px 14px 0 rgba(220, 38, 38, 0.15)",warning:"0 4px 14px 0 rgba(217, 119, 6, 0.15)",success:"0 4px 14px 0 rgba(22, 163, 74, 0.15)",focus:"0 0 0 3px rgba(37, 99, 235, 0.1)",focusError:"0 0 0 3px rgba(220, 38, 38, 0.1)",focusSuccess:"0 0 0 3px rgba(22, 163, 74, 0.1)",focusWarning:"0 0 0 3px rgba(217, 119, 6, 0.1)"},l={none:"0",xs:"0.0625rem",sm:"0.125rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem","4xl":"2rem",full:"9999px",button:"0.375rem",card:"0.5rem",input:"0.375rem",modal:"0.75rem",tooltip:"0.25rem"},c={none:"none",all:"all 150ms cubic-bezier(0.4, 0, 0.2, 1)",default:"all 150ms cubic-bezier(0.4, 0, 0.2, 1)",fast:"all 100ms cubic-bezier(0.4, 0, 0.2, 1)",slow:"all 300ms cubic-bezier(0.4, 0, 0.2, 1)",colors:"color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1), border-color 150ms cubic-bezier(0.4, 0, 0.2, 1)",opacity:"opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)",shadow:"box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1)",transform:"transform 150ms cubic-bezier(0.4, 0, 0.2, 1)",easing:{linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)",inOut:"cubic-bezier(0.4, 0, 0.2, 1)"}},s={hide:-1,auto:"auto",base:0,docked:10,dropdown:1e3,sticky:1100,banner:1200,overlay:1300,modal:1400,popover:1500,skipLink:1600,toast:1700,tooltip:1800,appHeader:100,appSidebar:200,componentPalette:300,propertyEditor:300,previewArea:100,dragOverlay:1e3,tutorialOverlay:1500,aiSuggestions:1200},d={xs:"0px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px","3xl":"1920px",mobile:"640px",tablet:"768px",desktop:"1024px",wide:"1280px",ultrawide:"1536px"},p={sm:"@media (min-width: ".concat(d.sm,")"),md:"@media (min-width: ".concat(d.md,")"),lg:"@media (min-width: ".concat(d.lg,")"),xl:"@media (min-width: ".concat(d.xl,")"),"2xl":"@media (min-width: ".concat(d["2xl"],")"),"3xl":"@media (min-width: ".concat(d["3xl"],")"),maxSm:"@media (max-width: ".concat(parseInt(d.sm)-1,"px)"),maxMd:"@media (max-width: ".concat(parseInt(d.md)-1,"px)"),maxLg:"@media (max-width: ".concat(parseInt(d.lg)-1,"px)"),maxXl:"@media (max-width: ".concat(parseInt(d.xl)-1,"px)"),smToMd:"@media (min-width: ".concat(d.sm,") and (max-width: ").concat(parseInt(d.md)-1,"px)"),mdToLg:"@media (min-width: ".concat(d.md,") and (max-width: ").concat(parseInt(d.lg)-1,"px)"),lgToXl:"@media (min-width: ".concat(d.lg,") and (max-width: ").concat(parseInt(d.xl)-1,"px)"),reducedMotion:"@media (prefers-reduced-motion: reduce)",highContrast:"@media (prefers-contrast: high)",darkMode:"@media (prefers-color-scheme: dark)",lightMode:"@media (prefers-color-scheme: light)",touch:"@media (hover: none) and (pointer: coarse)",mouse:"@media (hover: hover) and (pointer: fine)",retina:"@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)"},u={focusRing:{width:"2px",style:"solid",color:r.primary.main,offset:"2px"},minTouchTarget:{width:"44px",height:"44px"},srOnly:{position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",border:"0"},highContrast:{border:"1px solid",outline:"1px solid"}},m={fadeIn:"fadeIn 150ms cubic-bezier(0.4, 0, 0.2, 1)",fadeOut:"fadeOut 150ms cubic-bezier(0.4, 0, 0.2, 1)",slideInUp:"slideInUp 200ms cubic-bezier(0.4, 0, 0.2, 1)",slideInDown:"slideInDown 200ms cubic-bezier(0.4, 0, 0.2, 1)",slideInLeft:"slideInLeft 200ms cubic-bezier(0.4, 0, 0.2, 1)",slideInRight:"slideInRight 200ms cubic-bezier(0.4, 0, 0.2, 1)",scaleIn:"scaleIn 150ms cubic-bezier(0.4, 0, 0.2, 1)",scaleOut:"scaleOut 150ms cubic-bezier(0.4, 0, 0.2, 1)",bounce:"bounce 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55)",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",spin:"spin 1s linear infinite"},y={button:{height:{sm:"32px",md:"40px",lg:"48px"},padding:{sm:"0 12px",md:"0 16px",lg:"0 24px"},fontSize:{sm:o.fontSize.sm,md:o.fontSize.base,lg:o.fontSize.lg}},input:{height:{sm:"32px",md:"40px",lg:"48px"},padding:{sm:"0 8px",md:"0 12px",lg:"0 16px"}},card:{padding:{sm:i[4],md:i[6],lg:i[8]}},modal:{maxWidth:{sm:"400px",md:"600px",lg:"800px",xl:"1200px"}}};const g={colors:r,typography:o,spacing:i,shadows:a,borderRadius:l,transitions:c,zIndex:s,breakpoints:d,mediaQueries:p,accessibility:u,animations:m,components:y}},93385:(e,n,t)=>{t.d(n,{HP:()=>f,NP:()=>g,rV:()=>A});var r=t(5544),o=t(64467),i=t(96540),a=(t(71468),t(1807)),l=t(35346);function c(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?c(Object(t),!0).forEach((function(n){(0,o.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var d="light",p="dark",u="system",m=(0,o.A)((0,o.A)({},d,{primary:"#1976d2",secondary:"#f50057",background:"#ffffff",surface:"#f5f5f5",text:"#212121",textSecondary:"#757575",border:"#e0e0e0",error:"#d32f2f",warning:"#f57c00",info:"#0288d1",success:"#388e3c"}),p,{primary:"#90caf9",secondary:"#f48fb1",background:"#121212",surface:"#1e1e1e",text:"#ffffff",textSecondary:"#b0b0b0",border:"#333333",error:"#f44336",warning:"#ff9800",info:"#29b6f6",success:"#66bb6a"}),y=(0,i.createContext)({theme:d,colors:m[d],setTheme:function(){},setCustomColors:function(){}}),g=function(e){var n=e.children,t=e.initialTheme,o=void 0===t?u:t,a=(0,i.useState)(o),l=(0,r.A)(a,2),c=l[0],g=l[1],h=(0,i.useState)({}),f=(0,r.A)(h,2),A=f[0],b=f[1],v=(0,i.useState)(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?p:d),x=(0,r.A)(v,2),E=x[0],F=x[1],w=function(){return c===u?m[E]:"custom"===c?s(s({},m[d]),A):m[c]};return(0,i.useEffect)((function(){var e=w();Object.entries(e).forEach((function(e){var n=(0,r.A)(e,2),t=n[0],o=n[1];document.documentElement.style.setProperty("--color-".concat(t),o)}));var n=c===u?E:c;document.documentElement.setAttribute("data-theme",n),n===p?document.body.classList.add("dark-theme"):document.body.classList.remove("dark-theme")}),[c,E,A]),(0,i.useEffect)((function(){var e=window.matchMedia("(prefers-color-scheme: dark)"),n=function(e){F(e.matches?p:d)};e.addEventListener?e.addEventListener("change",n):e.addListener(n);var t=localStorage.getItem("app_theme");return t&&g(t),function(){e.removeEventListener?e.removeEventListener("change",n):e.removeListener(n)}}),[]),i.createElement(y.Provider,{value:{theme:c,colors:w(),setTheme:function(e){g(e),localStorage.setItem("app_theme",e)},setCustomColors:b}},n)},h=function(){var e=(0,i.useContext)(y);if(!e)throw new Error("useTheme must be used within a ThemeProvider");return e},f=function(e){var n=e.position,t=void 0===n?"right":n,r=h(),o=r.theme,c=r.setTheme;return i.createElement("div",{className:"theme-switcher ".concat(t)},i.createElement(a.$n,{type:"text",icon:i.createElement(l.Ebl,null),onClick:function(){c(o===p?d:p)},"aria-label":"Switch to ".concat(o===p?"light":"dark"," theme")}))},A=function(e){var n=e.onSave,t=h(),o=t.colors,l=t.setCustomColors,c=a.lV.useForm(),s=(0,r.A)(c,1)[0];return(0,i.useEffect)((function(){s.setFieldsValue({primaryColor:o.primary,secondaryColor:o.secondary,backgroundColor:o.background,textColor:o.text})}),[o,s]),i.createElement("div",{className:"theme-customizer"},i.createElement(a.lV,{form:s,layout:"vertical",onFinish:function(e){l(e),n&&n(e)}},i.createElement(a.lV.Item,{name:"primaryColor",label:"Primary Color"},i.createElement(a.pd,{type:"color"})),i.createElement(a.lV.Item,{name:"secondaryColor",label:"Secondary Color"},i.createElement(a.pd,{type:"color"})),i.createElement(a.lV.Item,{name:"backgroundColor",label:"Background Color"},i.createElement(a.pd,{type:"color"})),i.createElement(a.lV.Item,{name:"textColor",label:"Text Color"},i.createElement(a.pd,{type:"color"})),i.createElement(a.lV.Item,null,i.createElement(a.$n,{type:"primary",htmlType:"submit"},"Apply Theme"))))}},96012:(e,n,t)=>{t.d(n,{A:()=>ie});var r,o,i,a,l,c,s,d=t(96540),p=t(11080),u=t(57528),m=t(70572),y=t(93385),g=m.Ay.div(r||(r=(0,u.A)(["\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: var(--background-color);\n  color: var(--text-color);\n  transition: background-color 0.3s ease, color 0.3s ease;\n"]))),h=m.Ay.header(o||(o=(0,u.A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 20px;\n  height: 64px;\n  background-color: var(--background-secondary, #f5f5f5);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n"]))),f=m.Ay.div(i||(i=(0,u.A)(["\n  font-size: 1.5rem;\n  font-weight: bold;\n\n  a {\n    color: var(--primary-color);\n    text-decoration: none;\n\n    &:hover {\n      text-decoration: none;\n      opacity: 0.9;\n    }\n  }\n"]))),A=m.Ay.nav(a||(a=(0,u.A)(["\n  ul {\n    display: flex;\n    list-style: none;\n    margin: 0;\n    padding: 0;\n\n    li {\n      margin-left: 20px;\n\n      a {\n        color: var(--text-color);\n        text-decoration: none;\n        padding: 8px 12px;\n        border-radius: 4px;\n        transition: background-color 0.3s ease;\n\n        &:hover {\n          background-color: rgba(0, 0, 0, 0.05);\n        }\n\n        &.active {\n          color: var(--primary-color);\n          font-weight: 500;\n        }\n      }\n    }\n  }\n"]))),b=m.Ay.main(l||(l=(0,u.A)(["\n  flex: 1;\n  padding: 20px;\n"]))),v=m.Ay.footer(c||(c=(0,u.A)(["\n  padding: 20px;\n  text-align: center;\n  background-color: var(--background-secondary, #f5f5f5);\n  border-top: 1px solid var(--border-color, #e8e8e8);\n"]))),x=m.Ay.div(s||(s=(0,u.A)(["\n  display: flex;\n  align-items: center;\n"])));const E=function(e){var n=e.children;return d.createElement(g,{className:"app-layout"},d.createElement(h,{className:"app-header"},d.createElement(f,{className:"logo"},d.createElement(p.N_,{to:"/"},"App Builder")),d.createElement(A,{className:"main-nav"},d.createElement("ul",null,d.createElement("li",null,d.createElement(p.N_,{to:"/"},"Home")),d.createElement("li",null,d.createElement(p.N_,{to:"/app-builder"},"App Builder")),d.createElement("li",null,d.createElement(p.N_,{to:"/templates"},"Templates")),d.createElement("li",null,d.createElement(p.N_,{to:"/websocket"},"WebSocket")))),d.createElement(x,null,d.createElement(y.HP,null))),d.createElement(b,{className:"app-main"},n),d.createElement(v,{className:"app-footer"},d.createElement("p",null,"© ",(new Date).getFullYear()," App Builder")))};var F=t(64467),w=t(1807),S=t(35346);function k(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function z(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?k(Object(t),!0).forEach((function(n){(0,F.A)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):k(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}const T=function(e){var n=e.tip,t=void 0===n?"Loading...":n,r=e.size,o=void 0===r?"large":r,i=e.fullScreen,a=void 0!==i&&i,l=e.backgroundColor,c=void 0===l?"rgba(255, 255, 255, 0.8)":l,s=e.icon,p=void 0===s?null:s,u=e.className,m=void 0===u?"":u,y=e.style,g=void 0===y?{}:y,h=p||d.createElement(S.NKq,{style:{fontSize:"large"===o?40:24},spin:!0});return a?d.createElement("div",{className:"loading-container ".concat(m),style:z({backgroundColor:c},g),"aria-live":"polite","aria-busy":"true"},d.createElement("div",{className:"loading-content"},d.createElement(w.tK,{className:"loading-spinner",indicator:h,size:o}),t&&d.createElement("div",{className:"loading-text",role:"status"},t))):d.createElement("div",{style:z({textAlign:"center",padding:"20px"},g),className:m,"aria-live":"polite","aria-busy":"true"},d.createElement(w.tK,{indicator:h,size:o}),t&&d.createElement("div",{style:{marginTop:"12px"},role:"status"},t))};var C,O=t(5544),I=t(11606),D=w.o5.Text,B=m.Ay.div(C||(C=(0,u.A)(["\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  width: 100%;\n  position: fixed;\n  top: 0;\n  left: 0;\n  background: rgba(255, 255, 255, 0.95);\n  z-index: 1000;\n"])));const W=function(e){var n=e.loading,t=void 0===n||n,r=e.message,o=void 0===r?"Loading...":r,i=e.description,a=void 0===i?"":i,l=e.size,c=void 0===l?"default":l,s=e.fullPage,p=void 0!==s&&s,u=e.children,m=void 0===u?null:u,y=e.error,g=void 0===y?null:y,h=e.retry,f=void 0===h?null:h;if(!t&&!g)return d.createElement(d.Fragment,null,m);var A=d.createElement(S.NKq,{style:{fontSize:"large"===c?40:24},spin:!0}),b=g?d.createElement(w.Fc,{message:g.title||"Error",description:d.createElement(w.$x,{direction:"vertical"},d.createElement(D,null,g.message||"An error occurred"),f&&d.createElement("a",{onClick:f},"Try again")),type:"error",showIcon:!0}):d.createElement(w.tK,{indicator:A,size:c},d.createElement("div",{style:{padding:o?"30px":"50px"}},o&&d.createElement("div",{style:{marginTop:8}},o),a&&d.createElement("div",{style:{marginTop:4,color:"rgba(0, 0, 0, 0.45)"}},a)));return p?d.createElement(B,null,b):b},P=function(e){var n=e.children,t=e.redirectTo,r=void 0===t?"/login":t,o=e.roles,i=void 0===o?[]:o,a=e.loadingFallback,l=void 0===a?null:a,c=(0,d.useState)(!0),s=(0,O.A)(c,2),u=s[0],m=s[1],y=(0,d.useState)(!1),g=(0,O.A)(y,2),h=g[0],f=g[1],A=(0,d.useState)(!0),b=(0,O.A)(A,2),v=b[0],x=b[1];if((0,d.useEffect)((function(){var e=I.A.isAuthenticated();if(f(e),e&&i.length>0){var n=I.A.getUser(),t=(null==n?void 0:n.roles)||[],r=i.some((function(e){return t.includes(e)}));x(r)}m(!1)}),[i]),(0,d.useEffect)((function(){return I.A.addListener((function(e){if("login"===e||"register"===e){if(f(!0),i.length>0){var n=I.A.getUser(),t=(null==n?void 0:n.roles)||[],r=i.some((function(e){return t.includes(e)}));x(r)}}else"logout"===e&&(f(!1),x(!1))}))}),[i]),u)return l||d.createElement(W,{loading:!0,message:"Checking authentication...",fullPage:!0});if(!h){var E="".concat(r,"?redirect=").concat(encodeURIComponent(window.location.pathname));return d.createElement(p.C5,{to:E,replace:!0})}return v?n:d.createElement(p.C5,{to:"/unauthorized",replace:!0})};var R=t(49391),H=(0,d.lazy)((function(){return Promise.all([t.e(6649),t.e(740),t.e(4186),t.e(8014),t.e(7348),t.e(6488),t.e(7461),t.e(526),t.e(8270),t.e(2819),t.e(2136),t.e(8941),t.e(5351),t.e(6855),t.e(7979),t.e(895),t.e(9538),t.e(6618),t.e(6288),t.e(3913)]).then(t.bind(t,56294))})),j=(0,d.lazy)((function(){return Promise.all([t.e(6649),t.e(740),t.e(4186),t.e(8014),t.e(7348),t.e(6488),t.e(7461),t.e(526),t.e(8270),t.e(2819),t.e(2136),t.e(8941),t.e(5351),t.e(6855),t.e(7979),t.e(895),t.e(9538),t.e(6618),t.e(6288),t.e(3548)]).then(t.bind(t,21167))})),_=(0,d.lazy)((function(){return t.e(8832).then(t.bind(t,18832))})),N=(0,d.lazy)((function(){return Promise.all([t.e(6649),t.e(740),t.e(4186),t.e(8014),t.e(7348),t.e(6488),t.e(7461),t.e(526),t.e(8270),t.e(2819),t.e(2136),t.e(8941),t.e(5351),t.e(6855),t.e(7979),t.e(895),t.e(9538),t.e(6043),t.e(9144),t.e(5849),t.e(6685),t.e(6400),t.e(2045),t.e(4540),t.e(3715),t.e(997),t.e(9184),t.e(5890),t.e(6409),t.e(8849),t.e(9879),t.e(7391),t.e(3876),t.e(8540),t.e(9862),t.e(3061),t.e(4214),t.e(7833),t.e(4673),t.e(1437),t.e(8283),t.e(3592),t.e(2403),t.e(1287),t.e(2382),t.e(1871),t.e(3861),t.e(1854),t.e(489),t.e(689),t.e(6618),t.e(6288),t.e(4651)]).then(t.bind(t,64651))})),q=(0,d.lazy)((function(){return Promise.all([t.e(6649),t.e(740),t.e(4186),t.e(8014),t.e(7348),t.e(6488),t.e(7461),t.e(526),t.e(8270),t.e(2819),t.e(2136),t.e(8941),t.e(5351),t.e(6855),t.e(7979),t.e(895),t.e(9538),t.e(6618),t.e(6288),t.e(1602)]).then(t.bind(t,71602))})),L=(0,d.lazy)((function(){return t.e(253).then(t.bind(t,20253))})),M=(0,d.lazy)((function(){return Promise.all([t.e(6043),t.e(9144),t.e(5849),t.e(6685),t.e(6400),t.e(2045),t.e(4540),t.e(3715),t.e(997),t.e(9184),t.e(5890),t.e(6409),t.e(8849),t.e(9879),t.e(7391),t.e(3876),t.e(8540),t.e(9862),t.e(3061),t.e(4214),t.e(7833),t.e(4673),t.e(1437),t.e(8283),t.e(3592),t.e(2403),t.e(1287),t.e(2382),t.e(1871),t.e(3861),t.e(1854),t.e(489),t.e(689),t.e(368)]).then(t.bind(t,90368))})),U=(0,d.lazy)((function(){return t.e(4067).then(t.bind(t,94067))})),V=(0,d.lazy)((function(){return Promise.all([t.e(6649),t.e(740),t.e(4186),t.e(8014),t.e(7348),t.e(6488),t.e(7461),t.e(526),t.e(8270),t.e(2819),t.e(2136),t.e(8941),t.e(5351),t.e(6855),t.e(7979),t.e(895),t.e(9538),t.e(6043),t.e(9144),t.e(5849),t.e(6685),t.e(6400),t.e(2045),t.e(4540),t.e(3715),t.e(997),t.e(9184),t.e(5890),t.e(6409),t.e(8849),t.e(9879),t.e(7391),t.e(3876),t.e(8540),t.e(9862),t.e(3061),t.e(4214),t.e(7833),t.e(4673),t.e(1437),t.e(8283),t.e(3592),t.e(2403),t.e(1287),t.e(2382),t.e(1871),t.e(3861),t.e(1854),t.e(489),t.e(689),t.e(6618),t.e(6288),t.e(5831)]).then(t.bind(t,65831))})),G=(0,d.lazy)((function(){return t.e(6548).then(t.bind(t,36548))})),K=(0,d.lazy)((function(){return t.e(8552).then(t.bind(t,38552))})),Q=(0,d.lazy)((function(){return Promise.all([t.e(6649),t.e(740),t.e(4186),t.e(8014),t.e(7348),t.e(6488),t.e(7461),t.e(526),t.e(8270),t.e(2819),t.e(2136),t.e(8941),t.e(5351),t.e(6855),t.e(7979),t.e(895),t.e(9538),t.e(6043),t.e(9144),t.e(5849),t.e(6685),t.e(6400),t.e(2045),t.e(4540),t.e(3715),t.e(997),t.e(9184),t.e(5890),t.e(6409),t.e(8849),t.e(9879),t.e(7391),t.e(3876),t.e(8540),t.e(9862),t.e(3061),t.e(4214),t.e(7833),t.e(4673),t.e(1437),t.e(8283),t.e(3592),t.e(2403),t.e(1287),t.e(2382),t.e(1871),t.e(3861),t.e(1854),t.e(489),t.e(689),t.e(6618),t.e(6288),t.e(7435)]).then(t.bind(t,37435))})),Y=(0,d.lazy)((function(){return t.e(3730).then(t.bind(t,43730))})),J=(0,d.lazy)((function(){return Promise.all([t.e(6618),t.e(6288),t.e(1934)]).then(t.bind(t,91934))})),$=(0,d.lazy)((function(){return t.e(1375).then(t.bind(t,91375))})),X=(0,d.lazy)((function(){return Promise.all([t.e(6618),t.e(6288),t.e(4905)]).then(t.bind(t,24905))})),Z=(0,d.lazy)((function(){return Promise.all([t.e(6649),t.e(740),t.e(4186),t.e(8014),t.e(7348),t.e(6488),t.e(7461),t.e(526),t.e(8270),t.e(2819),t.e(2136),t.e(8941),t.e(5351),t.e(6855),t.e(7979),t.e(895),t.e(9538),t.e(6618),t.e(6288),t.e(1177)]).then(t.bind(t,11177))})),ee=(0,d.lazy)((function(){return Promise.all([t.e(6649),t.e(740),t.e(4186),t.e(8014),t.e(7348),t.e(6488),t.e(7461),t.e(526),t.e(8270),t.e(2819),t.e(2136),t.e(8941),t.e(5351),t.e(6855),t.e(7979),t.e(895),t.e(9538),t.e(6618),t.e(6288),t.e(1160)]).then(t.bind(t,61160))})),ne=(0,d.lazy)((function(){return Promise.all([t.e(6649),t.e(740),t.e(4186),t.e(8014),t.e(7348),t.e(6488),t.e(7461),t.e(526),t.e(8270),t.e(2819),t.e(2136),t.e(8941),t.e(5351),t.e(6855),t.e(7979),t.e(895),t.e(9538),t.e(6618),t.e(6288),t.e(2553)]).then(t.bind(t,2553))})),te=(0,d.lazy)((function(){return t.e(7772).then(t.bind(t,87772))})),re=(0,d.lazy)((function(){return t.e(5202).then(t.bind(t,75202))})),oe=function(){return d.createElement(T,{tip:"Loading page...",fullScreen:!0,backgroundColor:"rgba(255, 255, 255, 0.9)"})};const ie=function(){var e=(0,R.As)().isAuthenticated;return d.createElement(E,null,d.createElement(d.Suspense,{fallback:d.createElement(oe,null)},d.createElement(p.BV,null,d.createElement(p.qh,{path:"/",element:d.createElement(j,null)}),d.createElement(p.qh,{path:"/home",element:d.createElement(H,null)}),d.createElement(p.qh,{path:"/home-mvp",element:d.createElement(j,null)}),d.createElement(p.qh,{path:"/mvp",element:d.createElement(_,null)}),d.createElement(p.qh,{path:"/templates",element:d.createElement(re,null)}),d.createElement(p.qh,{path:"/login",element:e?d.createElement(p.C5,{to:"/dashboard"}):d.createElement(U,null)}),d.createElement(p.qh,{path:"/register",element:e?d.createElement(p.C5,{to:"/dashboard"}):d.createElement(V,null)}),d.createElement(p.qh,{path:"/forgot-password",element:e?d.createElement(p.C5,{to:"/dashboard"}):d.createElement(G,null)}),d.createElement(p.qh,{path:"/reset-password/:token",element:e?d.createElement(p.C5,{to:"/dashboard"}):d.createElement(K,null)}),d.createElement(p.qh,{path:"/dashboard",element:d.createElement(P,null,d.createElement(J,null))}),d.createElement(p.qh,{path:"/app-builder",element:d.createElement(P,null,d.createElement(N,null))}),d.createElement(p.qh,{path:"/websocket",element:d.createElement(P,null,d.createElement(q,null))}),d.createElement(p.qh,{path:"/profile",element:d.createElement(P,null,d.createElement(Q,null))}),d.createElement(p.qh,{path:"/projects",element:d.createElement(P,null,d.createElement(Y,null))}),d.createElement(p.qh,{path:"/settings",element:d.createElement(P,null,d.createElement($,null))}),d.createElement(p.qh,{path:"/theme-test",element:d.createElement(X,null)}),d.createElement(p.qh,{path:"/dark-mode-test",element:d.createElement(Z,null)}),d.createElement(p.qh,{path:"/contrast-test",element:d.createElement(ee,null)}),d.createElement(p.qh,{path:"/header-contrast-test",element:d.createElement(ne,null)}),d.createElement(p.qh,{path:"/service-worker-test",element:d.createElement(te,null)}),d.createElement(p.qh,{path:"/unauthorized",element:d.createElement(M,null)}),d.createElement(p.qh,{path:"/404",element:d.createElement(L,null)}),d.createElement(p.qh,{path:"*",element:d.createElement(p.C5,{to:"/404"})}))))}}}]);