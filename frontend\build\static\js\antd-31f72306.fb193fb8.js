"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[8941],{28392:(e,t,n)=>{n.d(t,{A:()=>f});var i=n(96540),o=n(46942),a=n.n(o),l=n(82546),r=n(42704),s=n(62279),c=n(76327);const d=i.createContext({latestIndex:0}),m=d.Provider,p=({className:e,index:t,children:n,split:o,style:a})=>{const{latestIndex:l}=i.useContext(d);return null==n?null:i.createElement(i.Fragment,null,i.createElement("div",{className:e,style:a},n),t<l&&o&&i.createElement("span",{className:`${e}-split`},o))};var u=n(85447);const g=i.forwardRef(((e,t)=>{var n;const{getPrefixCls:o,direction:c,size:d,className:g,style:f,classNames:h,styles:y}=(0,s.TP)("space"),{size:v=(null!=d?d:"small"),align:$,className:S,rootClassName:b,children:x,direction:z="horizontal",prefixCls:w,split:C,style:N,wrap:O=!1,classNames:E,styles:k}=e,I=function(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(i=Object.getOwnPropertySymbols(e);o<i.length;o++)t.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(e,i[o])&&(n[i[o]]=e[i[o]])}return n}(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[D,G]=Array.isArray(v)?v:[v,v],j=(0,r.X)(G),M=(0,r.X)(D),P=(0,r.m)(G),L=(0,r.m)(D),X=(0,l.A)(x,{keepEmpty:!0}),q=void 0===$&&"horizontal"===z?"center":$,A=o("space",w),[T,F,H]=(0,u.A)(A),B=a()(A,g,F,`${A}-${z}`,{[`${A}-rtl`]:"rtl"===c,[`${A}-align-${q}`]:q,[`${A}-gap-row-${G}`]:j,[`${A}-gap-col-${D}`]:M},S,b,H),R=a()(`${A}-item`,null!==(n=null==E?void 0:E.item)&&void 0!==n?n:h.item);let W=0;const Q=X.map(((e,t)=>{var n;null!=e&&(W=t);const o=(null==e?void 0:e.key)||`${R}-${t}`;return i.createElement(p,{className:R,key:o,index:t,split:C,style:null!==(n=null==k?void 0:k.item)&&void 0!==n?n:y.item},e)})),K=i.useMemo((()=>({latestIndex:W})),[W]);if(0===X.length)return null;const V={};return O&&(V.flexWrap="wrap"),!M&&L&&(V.columnGap=D),!j&&P&&(V.rowGap=G),T(i.createElement("div",Object.assign({ref:t,className:B,style:Object.assign(Object.assign(Object.assign({},V),f),N)},I),i.createElement(m,{value:K},Q)))}));g.Compact=c.Ay;const f=g},29029:(e,t,n)=>{n.d(t,{A:()=>C});var i=n(96540),o=n(46942),a=n.n(o),l=n(73700),r=(n(18877),n(62279)),s=n(40682),c=n(30981);const d=80*Math.PI,m=e=>{const{dotClassName:t,style:n,hasCircleCls:o}=e;return i.createElement("circle",{className:a()(`${t}-circle`,{[`${t}-circle-bg`]:o}),r:40,cx:50,cy:50,strokeWidth:20,style:n})},p=({percent:e,prefixCls:t})=>{const n=`${t}-dot`,o=`${n}-holder`,l=`${o}-hidden`,[r,s]=i.useState(!1);(0,c.A)((()=>{0!==e&&s(!0)}),[0!==e]);const p=Math.max(Math.min(e,100),0);if(!r)return null;const u={strokeDashoffset:""+d/4,strokeDasharray:`${d*p/100} ${d*(100-p)/100}`};return i.createElement("span",{className:a()(o,`${n}-progress`,p<=0&&l)},i.createElement("svg",{viewBox:"0 0 100 100",role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":p},i.createElement(m,{dotClassName:n,hasCircleCls:!0}),i.createElement(m,{dotClassName:n,style:u})))};function u(e){const{prefixCls:t,percent:n=0}=e,o=`${t}-dot`,l=`${o}-holder`,r=`${l}-hidden`;return i.createElement(i.Fragment,null,i.createElement("span",{className:a()(l,n>0&&r)},i.createElement("span",{className:a()(o,`${t}-dot-spin`)},[1,2,3,4].map((e=>i.createElement("i",{className:`${t}-dot-item`,key:e}))))),i.createElement(p,{prefixCls:t,percent:n}))}function g(e){const{prefixCls:t,indicator:n,percent:o}=e,l=`${t}-dot`;return n&&i.isValidElement(n)?(0,s.Ob)(n,{className:a()(n.props.className,l),percent:o}):i.createElement(u,{prefixCls:t,percent:o})}var f=n(36891),h=n(25905),y=n(51113);const v=new f.Mo("antSpinMove",{to:{opacity:1}}),$=new f.Mo("antRotate",{to:{transform:"rotate(405deg)"}}),S=e=>{const{componentCls:t,calc:n}=e;return{[t]:Object.assign(Object.assign({},(0,h.dF)(e)),{position:"absolute",display:"none",color:e.colorPrimary,fontSize:0,textAlign:"center",verticalAlign:"middle",opacity:0,transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`,"&-spinning":{position:"relative",display:"inline-block",opacity:1},[`${t}-text`]:{fontSize:e.fontSize,paddingTop:n(n(e.dotSize).sub(e.fontSize)).div(2).add(2).equal()},"&-fullscreen":{position:"fixed",width:"100vw",height:"100vh",backgroundColor:e.colorBgMask,zIndex:e.zIndexPopupBase,inset:0,display:"flex",alignItems:"center",flexDirection:"column",justifyContent:"center",opacity:0,visibility:"hidden",transition:`all ${e.motionDurationMid}`,"&-show":{opacity:1,visibility:"visible"},[t]:{[`${t}-dot-holder`]:{color:e.colorWhite},[`${t}-text`]:{color:e.colorTextLightSolid}}},"&-nested-loading":{position:"relative",[`> div > ${t}`]:{position:"absolute",top:0,insetInlineStart:0,zIndex:4,display:"block",width:"100%",height:"100%",maxHeight:e.contentHeight,[`${t}-dot`]:{position:"absolute",top:"50%",insetInlineStart:"50%",margin:n(e.dotSize).mul(-1).div(2).equal()},[`${t}-text`]:{position:"absolute",top:"50%",width:"100%",textShadow:`0 1px 2px ${e.colorBgContainer}`},[`&${t}-show-text ${t}-dot`]:{marginTop:n(e.dotSize).div(2).mul(-1).sub(10).equal()},"&-sm":{[`${t}-dot`]:{margin:n(e.dotSizeSM).mul(-1).div(2).equal()},[`${t}-text`]:{paddingTop:n(n(e.dotSizeSM).sub(e.fontSize)).div(2).add(2).equal()},[`&${t}-show-text ${t}-dot`]:{marginTop:n(e.dotSizeSM).div(2).mul(-1).sub(10).equal()}},"&-lg":{[`${t}-dot`]:{margin:n(e.dotSizeLG).mul(-1).div(2).equal()},[`${t}-text`]:{paddingTop:n(n(e.dotSizeLG).sub(e.fontSize)).div(2).add(2).equal()},[`&${t}-show-text ${t}-dot`]:{marginTop:n(e.dotSizeLG).div(2).mul(-1).sub(10).equal()}}},[`${t}-container`]:{position:"relative",transition:`opacity ${e.motionDurationSlow}`,"&::after":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:10,width:"100%",height:"100%",background:e.colorBgContainer,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'""',pointerEvents:"none"}},[`${t}-blur`]:{clear:"both",opacity:.5,userSelect:"none",pointerEvents:"none","&::after":{opacity:.4,pointerEvents:"auto"}}},"&-tip":{color:e.spinDotDefault},[`${t}-dot-holder`]:{width:"1em",height:"1em",fontSize:e.dotSize,display:"inline-block",transition:`transform ${e.motionDurationSlow} ease, opacity ${e.motionDurationSlow} ease`,transformOrigin:"50% 50%",lineHeight:1,color:e.colorPrimary,"&-hidden":{transform:"scale(0.3)",opacity:0}},[`${t}-dot-progress`]:{position:"absolute",inset:0},[`${t}-dot`]:{position:"relative",display:"inline-block",fontSize:e.dotSize,width:"1em",height:"1em","&-item":{position:"absolute",display:"block",width:n(e.dotSize).sub(n(e.marginXXS).div(2)).div(2).equal(),height:n(e.dotSize).sub(n(e.marginXXS).div(2)).div(2).equal(),background:"currentColor",borderRadius:"100%",transform:"scale(0.75)",transformOrigin:"50% 50%",opacity:.3,animationName:v,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear",animationDirection:"alternate","&:nth-child(1)":{top:0,insetInlineStart:0,animationDelay:"0s"},"&:nth-child(2)":{top:0,insetInlineEnd:0,animationDelay:"0.4s"},"&:nth-child(3)":{insetInlineEnd:0,bottom:0,animationDelay:"0.8s"},"&:nth-child(4)":{bottom:0,insetInlineStart:0,animationDelay:"1.2s"}},"&-spin":{transform:"rotate(45deg)",animationName:$,animationDuration:"1.2s",animationIterationCount:"infinite",animationTimingFunction:"linear"},"&-circle":{strokeLinecap:"round",transition:["stroke-dashoffset","stroke-dasharray","stroke","stroke-width","opacity"].map((t=>`${t} ${e.motionDurationSlow} ease`)).join(","),fillOpacity:0,stroke:"currentcolor"},"&-circle-bg":{stroke:e.colorFillSecondary}},[`&-sm ${t}-dot`]:{"&, &-holder":{fontSize:e.dotSizeSM}},[`&-sm ${t}-dot-holder`]:{i:{width:n(n(e.dotSizeSM).sub(n(e.marginXXS).div(2))).div(2).equal(),height:n(n(e.dotSizeSM).sub(n(e.marginXXS).div(2))).div(2).equal()}},[`&-lg ${t}-dot`]:{"&, &-holder":{fontSize:e.dotSizeLG}},[`&-lg ${t}-dot-holder`]:{i:{width:n(n(e.dotSizeLG).sub(e.marginXXS)).div(2).equal(),height:n(n(e.dotSizeLG).sub(e.marginXXS)).div(2).equal()}},[`&${t}-show-text ${t}-text`]:{display:"block"}})}},b=(0,y.OF)("Spin",(e=>{const t=(0,y.oX)(e,{spinDotDefault:e.colorTextDescription});return[S(t)]}),(e=>{const{controlHeightLG:t,controlHeight:n}=e;return{contentHeight:400,dotSize:t/2,dotSizeSM:.35*t,dotSizeLG:n}})),x=[[30,.05],[70,.03],[96,.01]];let z;const w=e=>{var t;const{prefixCls:n,spinning:o=!0,delay:s=0,className:c,rootClassName:d,size:m="default",tip:p,wrapperClassName:u,style:f,children:h,fullscreen:y=!1,indicator:v,percent:$}=e,S=function(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(i=Object.getOwnPropertySymbols(e);o<i.length;o++)t.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(e,i[o])&&(n[i[o]]=e[i[o]])}return n}(e,["prefixCls","spinning","delay","className","rootClassName","size","tip","wrapperClassName","style","children","fullscreen","indicator","percent"]),{getPrefixCls:w,direction:C,className:N,style:O,indicator:E}=(0,r.TP)("spin"),k=w("spin",n),[I,D,G]=b(k),[j,M]=i.useState((()=>o&&!function(e,t){return!!e&&!!t&&!Number.isNaN(Number(t))}(o,s))),P=function(e,t){const[n,o]=i.useState(0),a=i.useRef(null),l="auto"===t;return i.useEffect((()=>(l&&e&&(o(0),a.current=setInterval((()=>{o((e=>{const t=100-e;for(let n=0;n<x.length;n+=1){const[i,o]=x[n];if(e<=i)return e+t*o}return e}))}),200)),()=>{clearInterval(a.current)})),[l,e]),l?n:t}(j,$);i.useEffect((()=>{if(o){const e=(0,l.s)(s,(()=>{M(!0)}));return e(),()=>{var t;null===(t=null==e?void 0:e.cancel)||void 0===t||t.call(e)}}M(!1)}),[s,o]);const L=i.useMemo((()=>void 0!==h&&!y),[h,y]),X=a()(k,N,{[`${k}-sm`]:"small"===m,[`${k}-lg`]:"large"===m,[`${k}-spinning`]:j,[`${k}-show-text`]:!!p,[`${k}-rtl`]:"rtl"===C},c,!y&&d,D,G),q=a()(`${k}-container`,{[`${k}-blur`]:j}),A=null!==(t=null!=v?v:E)&&void 0!==t?t:z,T=Object.assign(Object.assign({},O),f),F=i.createElement("div",Object.assign({},S,{style:T,className:X,"aria-live":"polite","aria-busy":j}),i.createElement(g,{prefixCls:k,indicator:A,percent:P}),p&&(L||y)?i.createElement("div",{className:`${k}-text`},p):null);return I(L?i.createElement("div",Object.assign({},S,{className:a()(`${k}-nested-loading`,u,D,G)}),j&&i.createElement("div",{key:"loading"},F),i.createElement("div",{className:q,key:"container"},h)):y?i.createElement("div",{className:a()(`${k}-fullscreen`,{[`${k}-fullscreen-show`]:j},d,D,G)},F):F)};w.setDefaultIndicator=e=>{z=e};const C=w},76327:(e,t,n)=>{n.d(t,{Ay:()=>f,K6:()=>u,RQ:()=>p});var i=n(96540),o=n(46942),a=n.n(o),l=n(82546),r=n(38674),s=n(829),c=n(85447),d=function(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(i=Object.getOwnPropertySymbols(e);o<i.length;o++)t.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(e,i[o])&&(n[i[o]]=e[i[o]])}return n};const m=i.createContext(null),p=(e,t)=>{const n=i.useContext(m),o=i.useMemo((()=>{if(!n)return"";const{compactDirection:i,isFirstItem:o,isLastItem:l}=n,r="vertical"===i?"-vertical-":"-";return a()(`${e}-compact${r}item`,{[`${e}-compact${r}first-item`]:o,[`${e}-compact${r}last-item`]:l,[`${e}-compact${r}item-rtl`]:"rtl"===t})}),[e,t,n]);return{compactSize:null==n?void 0:n.compactSize,compactDirection:null==n?void 0:n.compactDirection,compactItemClassnames:o}},u=e=>{const{children:t}=e;return i.createElement(m.Provider,{value:null},t)},g=e=>{const{children:t}=e,n=d(e,["children"]);return i.createElement(m.Provider,{value:i.useMemo((()=>n),[n])},t)},f=e=>{const{getPrefixCls:t,direction:n}=i.useContext(r.QO),{size:o,direction:p,block:u,prefixCls:f,className:h,rootClassName:y,children:v}=e,$=d(e,["size","direction","block","prefixCls","className","rootClassName","children"]),S=(0,s.A)((e=>null!=o?o:e)),b=t("space-compact",f),[x,z]=(0,c.A)(b),w=a()(b,z,{[`${b}-rtl`]:"rtl"===n,[`${b}-block`]:u,[`${b}-vertical`]:"vertical"===p},h,y),C=i.useContext(m),N=(0,l.A)(v),O=i.useMemo((()=>N.map(((e,t)=>{const n=(null==e?void 0:e.key)||`${b}-item-${t}`;return i.createElement(g,{key:n,compactSize:S,compactDirection:p,isFirstItem:0===t&&(!C||(null==C?void 0:C.isFirstItem)),isLastItem:t===N.length-1&&(!C||(null==C?void 0:C.isLastItem))},e)}))),[o,N,C]);return 0===N.length?null:x(i.createElement("div",Object.assign({className:w},$),O))}},85447:(e,t,n)=>{n.d(t,{A:()=>r});var i=n(51113);const o=e=>{const{componentCls:t}=e;return{[t]:{"&-block":{display:"flex",width:"100%"},"&-vertical":{flexDirection:"column"}}}},a=e=>{const{componentCls:t,antCls:n}=e;return{[t]:{display:"inline-flex","&-rtl":{direction:"rtl"},"&-vertical":{flexDirection:"column"},"&-align":{flexDirection:"column","&-center":{alignItems:"center"},"&-start":{alignItems:"flex-start"},"&-end":{alignItems:"flex-end"},"&-baseline":{alignItems:"baseline"}},[`${t}-item:empty`]:{display:"none"},[`${t}-item > ${n}-badge-not-a-wrapper:only-child`]:{display:"block"}}}},l=e=>{const{componentCls:t}=e;return{[t]:{"&-gap-row-small":{rowGap:e.spaceGapSmallSize},"&-gap-row-middle":{rowGap:e.spaceGapMiddleSize},"&-gap-row-large":{rowGap:e.spaceGapLargeSize},"&-gap-col-small":{columnGap:e.spaceGapSmallSize},"&-gap-col-middle":{columnGap:e.spaceGapMiddleSize},"&-gap-col-large":{columnGap:e.spaceGapLargeSize}}}},r=(0,i.OF)("Space",(e=>{const t=(0,i.oX)(e,{spaceGapSmallSize:e.paddingXS,spaceGapMiddleSize:e.padding,spaceGapLargeSize:e.paddingLG});return[a(t),l(t),o(t)]}),(()=>({})),{resetStyle:!1})}}]);