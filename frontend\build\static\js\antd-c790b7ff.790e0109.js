"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[895],{15622:(e,t,n)=>{var o=n(96540),r=n(53425),l=(n(18877),n(95082)),i=n(90124);const{TimePicker:a,RangePicker:s}=l.A,c=o.forwardRef(((e,t)=>o.createElement(s,Object.assign({},e,{picker:"time",mode:void 0,ref:t})))),d=o.forwardRef(((e,t)=>{var{addon:n,renderExtraFooter:r,variant:l,bordered:s}=e,c=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["addon","renderExtraFooter","variant","bordered"]);const[d]=(0,i.A)("timePicker",l,s),u=o.useMemo((()=>r||n||void 0),[n,r]);return o.createElement(a,Object.assign({},c,{mode:void 0,ref:t,renderExtraFooter:u,variant:d}))})),u=(0,r.A)(d,"popupAlign",void 0,"picker");d._InternalPanelDoNotUseOrYouWillBeFired=u,d.RangePicker=c,d._InternalPanelDoNotUseOrYouWillBeFired=u},21102:(e,t,n)=>{var o=n(96540),r=n(46942),l=n.n(r),i=n(14884),a=n(19853),s=n(60275),c=n(23723),d=n(53425),u=n(58182),p=(n(18877),n(38674)),m=n(35128),g=n(98119),b=n(20934),f=n(829),h=n(94241),v=n(90124),y=n(36467),$=n(21560),x=n(26017),O=n(21381),w=n(76327),S=n(51113),C=n(99373),A=n(36891),k=n(77391),E=n(85166);const j=e=>{const{componentCls:t,treePrefixCls:n,colorBgElevated:o}=e,r=`.${n}`;return[{[`${t}-dropdown`]:[{padding:`${(0,A.zA)(e.paddingXS)} ${(0,A.zA)(e.calc(e.paddingXS).div(2).equal())}`},(0,E.k8)(n,(0,S.oX)(e,{colorBgContainer:o}),!1),{[r]:{borderRadius:0,[`${r}-list-holder-inner`]:{alignItems:"stretch",[`${r}-treenode`]:{[`${r}-node-content-wrapper`]:{flex:"auto"}}}}},(0,k.gd)(`${n}-checkbox`,e),{"&-rtl":{direction:"rtl",[`${r}-switcher${r}-switcher_close`]:{[`${r}-switcher-icon svg`]:{transform:"rotate(90deg)"}}}}]}]};var I=n(62279);const N=(e,t)=>{var n,r,d,A,k;const{prefixCls:N,size:P,disabled:z,bordered:R=!0,style:B,className:T,rootClassName:H,treeCheckable:W,multiple:L,listHeight:M=256,listItemHeight:D,placement:F,notFoundContent:K,switcherIcon:X,treeLine:q,getPopupContainer:V,popupClassName:_,dropdownClassName:Y,treeIcon:G=!1,transitionName:Q,choiceTransitionName:U="",status:Z,treeExpandAction:J,builtinPlacements:ee,dropdownMatchSelectWidth:te,popupMatchSelectWidth:ne,allowClear:oe,variant:re,dropdownStyle:le,dropdownRender:ie,popupRender:ae,onDropdownVisibleChange:se,onOpenChange:ce,tagRender:de,maxCount:ue,showCheckedStrategy:pe,treeCheckStrictly:me,styles:ge,classNames:be}=e,fe=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["prefixCls","size","disabled","bordered","style","className","rootClassName","treeCheckable","multiple","listHeight","listItemHeight","placement","notFoundContent","switcherIcon","treeLine","getPopupContainer","popupClassName","dropdownClassName","treeIcon","transitionName","choiceTransitionName","status","treeExpandAction","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","allowClear","variant","dropdownStyle","dropdownRender","popupRender","onDropdownVisibleChange","onOpenChange","tagRender","maxCount","showCheckedStrategy","treeCheckStrictly","styles","classNames"]),{getPopupContainer:he,getPrefixCls:ve,renderEmpty:ye,direction:$e,virtual:xe,popupMatchSelectWidth:Oe,popupOverflow:we}=o.useContext(p.QO),{styles:Se,classNames:Ce}=(0,I.TP)("treeSelect"),[,Ae]=(0,S.rd)(),ke=null!=D?D:(null==Ae?void 0:Ae.controlHeightSM)+(null==Ae?void 0:Ae.paddingXXS),Ee=ve(),je=ve("select",N),Ie=ve("select-tree",N),Ne=ve("tree-select",N),{compactSize:Pe,compactItemClassnames:ze}=(0,w.RQ)(je,$e),Re=(0,b.A)(je),Be=(0,b.A)(Ne),[Te,He,We]=(0,$.A)(je,Re),[Le]=function(e,t,n){return(0,S.OF)("TreeSelect",(e=>{const n=(0,S.oX)(e,{treePrefixCls:t});return[j(n)]}),E.bi)(e,n)}(Ne,Ie,Be),[Me,De]=(0,v.A)("treeSelect",re,R),Fe=l()((null===(n=null==be?void 0:be.popup)||void 0===n?void 0:n.root)||(null===(r=null==Ce?void 0:Ce.popup)||void 0===r?void 0:r.root)||_||Y,`${Ne}-dropdown`,{[`${Ne}-dropdown-rtl`]:"rtl"===$e},H,Ce.root,null==be?void 0:be.root,We,Re,Be,He),Ke=(null===(d=null==ge?void 0:ge.popup)||void 0===d?void 0:d.root)||(null===(A=null==Se?void 0:Se.popup)||void 0===A?void 0:A.root)||le,Xe=ae||ie,qe=ce||se,Ve=!(!W&&!L),_e=o.useMemo((()=>{if(!ue||("SHOW_ALL"!==pe||me)&&"SHOW_PARENT"!==pe)return ue}),[ue,pe,me]),Ye=(0,O.A)(e.suffixIcon,e.showArrow),Ge=null!==(k=null!=ne?ne:te)&&void 0!==k?k:Oe,{status:Qe,hasFeedback:Ue,isFormItemInput:Ze,feedbackIcon:Je}=o.useContext(h.$W),et=(0,u.v)(Qe,Z),{suffixIcon:tt,removeIcon:nt,clearIcon:ot}=(0,x.A)(Object.assign(Object.assign({},fe),{multiple:Ve,showSuffixIcon:Ye,hasFeedback:Ue,feedbackIcon:Je,prefixCls:je,componentName:"TreeSelect"})),rt=!0===oe?{clearIcon:ot}:oe;let lt;lt=void 0!==K?K:(null==ye?void 0:ye("Select"))||o.createElement(m.A,{componentName:"Select"});const it=(0,a.A)(fe,["suffixIcon","removeIcon","clearIcon","itemIcon","switcherIcon","style"]),at=o.useMemo((()=>void 0!==F?F:"rtl"===$e?"bottomRight":"bottomLeft"),[F,$e]),st=(0,f.A)((e=>{var t;return null!==(t=null!=P?P:Pe)&&void 0!==t?t:e})),ct=o.useContext(g.A),dt=null!=z?z:ct,ut=l()(!N&&Ne,{[`${je}-lg`]:"large"===st,[`${je}-sm`]:"small"===st,[`${je}-rtl`]:"rtl"===$e,[`${je}-${Me}`]:De,[`${je}-in-form-item`]:Ze},(0,u.L)(je,et,Ue),ze,T,H,Ce.root,null==be?void 0:be.root,We,Re,Be,He),[pt]=(0,s.YK)("SelectLike",null==Ke?void 0:Ke.zIndex);return Te(Le(o.createElement(i.Ay,Object.assign({virtual:xe,disabled:dt},it,{dropdownMatchSelectWidth:Ge,builtinPlacements:(0,y.A)(ee,we),ref:t,prefixCls:je,className:ut,style:Object.assign(Object.assign({},null==ge?void 0:ge.root),B),listHeight:M,listItemHeight:ke,treeCheckable:W?o.createElement("span",{className:`${je}-tree-checkbox-inner`}):W,treeLine:!!q,suffixIcon:tt,multiple:Ve,placement:at,removeIcon:nt,allowClear:rt,switcherIcon:e=>o.createElement(C.A,{prefixCls:Ie,switcherIcon:X,treeNodeProps:e,showLine:q}),showTreeIcon:G,notFoundContent:lt,getPopupContainer:V||he,treeMotion:null,dropdownClassName:Fe,dropdownStyle:Object.assign(Object.assign({},Ke),{zIndex:pt}),dropdownRender:Xe,onDropdownVisibleChange:qe,choiceTransitionName:(0,c.b)(Ee,"",U),transitionName:(0,c.b)(Ee,"slide-up",Q),treeExpandAction:J,tagRender:Ve?de:void 0,maxCount:_e,showCheckedStrategy:pe,treeCheckStrictly:me}))))},P=o.forwardRef(N),z=(0,d.A)(P,"dropdownAlign",(e=>(0,a.A)(e,["visible"])));P.TreeNode=i.nF,P.SHOW_ALL=i.u6,P.SHOW_PARENT=i.FA,P.SHOW_CHILD=i.vj,P._InternalPanelDoNotUseOrYouWillBeFired=z},23563:(e,t,n)=>{var o=n(96540),r=n(34735),l=n(46942),i=n.n(l),a=n(60275),s=n(13257),c=n(72616),d=n(38674),u=n(51113),p=n(60436),m=n(55886),g=n(72065),b=n(49103),f=n(21282),h=n(8182);function v(e){return null!=e}const y=e=>{var t,n;const{stepProps:r,current:l,type:a,indicatorsRender:s,actionsRender:c}=e,{prefixCls:d,total:u=1,title:y,onClose:$,onPrev:x,onNext:O,onFinish:w,cover:S,description:C,nextButtonProps:A,prevButtonProps:k,type:E,closable:j}=r,I=null!=E?E:a,N=(0,g.A)(null!=j?j:{},!0),[P]=(0,f.Ym)("global",h.A.global),[z]=(0,f.Ym)("Tour",h.A.Tour),R=o.createElement("button",Object.assign({type:"button",onClick:$,className:`${d}-close`,"aria-label":null==P?void 0:P.close},N),(null==j?void 0:j.closeIcon)||o.createElement(m.A,{className:`${d}-close-icon`})),B=l===u-1,T=v(y)?o.createElement("div",{className:`${d}-header`},o.createElement("div",{className:`${d}-title`},y)):null,H=v(C)?o.createElement("div",{className:`${d}-description`},C):null,W=v(S)?o.createElement("div",{className:`${d}-cover`},S):null;let L;L=s?s(l,u):(0,p.A)(Array.from({length:u}).keys()).map(((e,t)=>o.createElement("span",{key:e,className:i()(t===l&&`${d}-indicator-active`,`${d}-indicator`)})));const M="primary"===I?"default":"primary",D={type:"default",ghost:"primary"===I},F=o.createElement(o.Fragment,null,0!==l?o.createElement(b.Ay,Object.assign({size:"small"},D,k,{onClick:()=>{var e;null==x||x(),null===(e=null==k?void 0:k.onClick)||void 0===e||e.call(k)},className:i()(`${d}-prev-btn`,null==k?void 0:k.className)}),null!==(t=null==k?void 0:k.children)&&void 0!==t?t:null==z?void 0:z.Previous):null,o.createElement(b.Ay,Object.assign({size:"small",type:M},A,{onClick:()=>{var e;B?null==w||w():null==O||O(),null===(e=null==A?void 0:A.onClick)||void 0===e||e.call(A)},className:i()(`${d}-next-btn`,null==A?void 0:A.className)}),null!==(n=null==A?void 0:A.children)&&void 0!==n?n:B?null==z?void 0:z.Finish:null==z?void 0:z.Next));return o.createElement("div",{className:`${d}-content`},o.createElement("div",{className:`${d}-inner`},j&&R,W,T,H,o.createElement("div",{className:`${d}-footer`},u>1&&o.createElement("div",{className:`${d}-indicators`},L),o.createElement("div",{className:`${d}-buttons`},c?c(F,{current:l,total:u}):F))))};var $=n(70064),x=n(53425),O=n(40682),w=n(35381),S=n(36891),C=n(77020),A=n(25905),k=n(95201),E=n(20791);const j=e=>{const{componentCls:t,padding:n,paddingXS:o,borderRadius:r,borderRadiusXS:l,colorPrimary:i,colorFill:a,indicatorHeight:s,indicatorWidth:c,boxShadowTertiary:d,zIndexPopup:u,colorBgElevated:p,fontWeightStrong:m,marginXS:g,colorTextLightSolid:b,tourBorderRadius:f,colorWhite:h,primaryNextBtnHoverBg:v,closeBtnSize:y,motionDurationSlow:$,antCls:x,primaryPrevBtnBg:O}=e;return[{[t]:Object.assign(Object.assign({},(0,A.dF)(e)),{position:"absolute",zIndex:u,maxWidth:"fit-content",visibility:"visible",width:520,"--antd-arrow-background-color":p,"&-pure":{maxWidth:"100%",position:"relative"},[`&${t}-hidden`]:{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{textAlign:"start",textDecoration:"none",borderRadius:f,boxShadow:d,position:"relative",backgroundColor:p,border:"none",backgroundClip:"padding-box",[`${t}-close`]:Object.assign({position:"absolute",top:n,insetInlineEnd:n,color:e.colorIcon,background:"none",border:"none",width:y,height:y,borderRadius:e.borderRadiusSM,transition:`background-color ${e.motionDurationMid}, color ${e.motionDurationMid}`,display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer","&:hover":{color:e.colorIconHover,backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},(0,A.K8)(e)),[`${t}-cover`]:{textAlign:"center",padding:`${(0,S.zA)(e.calc(n).add(y).add(o).equal())} ${(0,S.zA)(n)} 0`,img:{width:"100%"}},[`${t}-header`]:{padding:`${(0,S.zA)(n)} ${(0,S.zA)(n)} ${(0,S.zA)(o)}`,width:`calc(100% - ${(0,S.zA)(y)})`,wordBreak:"break-word",[`${t}-title`]:{fontWeight:m}},[`${t}-description`]:{padding:`0 ${(0,S.zA)(n)}`,wordWrap:"break-word"},[`${t}-footer`]:{padding:`${(0,S.zA)(o)} ${(0,S.zA)(n)} ${(0,S.zA)(n)}`,textAlign:"end",borderRadius:`0 0 ${(0,S.zA)(l)} ${(0,S.zA)(l)}`,display:"flex",[`${t}-indicators`]:{display:"inline-block",[`${t}-indicator`]:{width:c,height:s,display:"inline-block",borderRadius:"50%",background:a,"&:not(:last-child)":{marginInlineEnd:s},"&-active":{background:i}}},[`${t}-buttons`]:{marginInlineStart:"auto",[`${x}-btn`]:{marginInlineStart:g}}}},[`${t}-primary, &${t}-primary`]:{"--antd-arrow-background-color":i,[`${t}-inner`]:{color:b,textAlign:"start",textDecoration:"none",backgroundColor:i,borderRadius:r,boxShadow:d,[`${t}-close`]:{color:b},[`${t}-indicators`]:{[`${t}-indicator`]:{background:O,"&-active":{background:b}}},[`${t}-prev-btn`]:{color:b,borderColor:O,backgroundColor:i,"&:hover":{backgroundColor:O,borderColor:"transparent"}},[`${t}-next-btn`]:{color:i,borderColor:"transparent",background:h,"&:hover":{background:v}}}}}),[`${t}-mask`]:{[`${t}-placeholder-animated`]:{transition:`all ${$}`}},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${t}-inner`]:{borderRadius:e.min(f,k.Zs)}}},(0,k.Ay)(e,"var(--antd-arrow-background-color)")]},I=(0,u.OF)("Tour",(e=>{const{borderRadiusLG:t}=e,n=(0,u.oX)(e,{indicatorWidth:6,indicatorHeight:6,tourBorderRadius:t});return[j(n)]}),(e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+70,closeBtnSize:e.fontSize*e.lineHeight,primaryPrevBtnBg:new C.Y(e.colorTextLightSolid).setA(.15).toRgbString(),primaryNextBtnHoverBg:new C.Y(e.colorBgTextHover).onBackground(e.colorWhite).toRgbString()},(0,k.Ke)({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),(0,E.n)(e))));const N=(0,x.U)((e=>{const{prefixCls:t,current:n=0,total:r=6,className:l,style:a,type:s,closable:c,closeIcon:u}=e,p=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["prefixCls","current","total","className","style","type","closable","closeIcon"]),{getPrefixCls:m}=o.useContext(d.QO),g=m("tour",t),[b,f,h]=I(g),[v,x]=(0,$.A)({closable:c,closeIcon:u},null,{closable:!0,closeIconRender:e=>o.isValidElement(e)?(0,O.Ob)(e,{className:i()(e.props.className,`${g}-close-icon`)}):e});return b(o.createElement(w.xn,{prefixCls:g,hashId:f,className:i()(l,`${g}-pure`,s&&`${g}-${s}`,h),style:a},o.createElement(y,{stepProps:Object.assign(Object.assign({},p),{prefixCls:g,total:r,closable:v?{closeIcon:x}:void 0}),current:n,type:s})))}));(e=>{const{prefixCls:t,type:n,rootClassName:l,indicatorsRender:p,actionsRender:m,steps:g,closeIcon:b}=e,f=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["prefixCls","type","rootClassName","indicatorsRender","actionsRender","steps","closeIcon"]),{getPrefixCls:h,direction:v,tour:$}=(0,o.useContext)(d.QO),x=h("tour",t),[O,w,S]=I(x),[,C]=(0,u.rd)(),A=o.useMemo((()=>null==g?void 0:g.map((e=>{var t;return Object.assign(Object.assign({},e),{className:i()(e.className,{[`${x}-primary`]:"primary"===(null!==(t=e.type)&&void 0!==t?t:n)})})}))),[g,n]),k=i()({[`${x}-rtl`]:"rtl"===v},w,S,l),[E,j]=(0,a.YK)("Tour",f.zIndex);return O(o.createElement(c.A.Provider,{value:j},o.createElement(r.A,Object.assign({},f,{closeIcon:null!=b?b:null==$?void 0:$.closeIcon,zIndex:E,rootClassName:k,prefixCls:x,animated:!0,renderPanel:(e,t)=>o.createElement(y,{type:n,stepProps:e,current:t,indicatorsRender:p,actionsRender:m}),builtinPlacements:e=>{var t;return(0,s.A)({arrowPointAtCenter:null===(t=null==e?void 0:e.arrowPointAtCenter)||void 0===t||t,autoAdjustOverflow:!0,offset:C.marginXXS,arrowWidth:C.sizePopupArrow,borderRadius:C.borderRadius})},steps:A}))))})._InternalPanelDoNotUseOrYouWillBeFired=N},33835:(e,t,n)=>{var o=n(96540),r=n(46942),l=n.n(r),i=(n(18877),n(38674)),a=n(20934),s=n(36891),c=n(25905),d=n(51113);const u=e=>{const{componentCls:t,calc:n}=e;return{[t]:Object.assign(Object.assign({},(0,c.dF)(e)),{margin:0,padding:0,listStyle:"none",[`${t}-item`]:{position:"relative",margin:0,paddingBottom:e.itemPaddingBottom,fontSize:e.fontSize,listStyle:"none","&-tail":{position:"absolute",insetBlockStart:e.itemHeadSize,insetInlineStart:n(n(e.itemHeadSize).sub(e.tailWidth)).div(2).equal(),height:`calc(100% - ${(0,s.zA)(e.itemHeadSize)})`,borderInlineStart:`${(0,s.zA)(e.tailWidth)} ${e.lineType} ${e.tailColor}`},"&-pending":{[`${t}-item-head`]:{fontSize:e.fontSizeSM,backgroundColor:"transparent"},[`${t}-item-tail`]:{display:"none"}},"&-head":{position:"absolute",width:e.itemHeadSize,height:e.itemHeadSize,backgroundColor:e.dotBg,border:`${(0,s.zA)(e.dotBorderWidth)} ${e.lineType} transparent`,borderRadius:"50%","&-blue":{color:e.colorPrimary,borderColor:e.colorPrimary},"&-red":{color:e.colorError,borderColor:e.colorError},"&-green":{color:e.colorSuccess,borderColor:e.colorSuccess},"&-gray":{color:e.colorTextDisabled,borderColor:e.colorTextDisabled}},"&-head-custom":{position:"absolute",insetBlockStart:n(e.itemHeadSize).div(2).equal(),insetInlineStart:n(e.itemHeadSize).div(2).equal(),width:"auto",height:"auto",marginBlockStart:0,paddingBlock:e.customHeadPaddingVertical,lineHeight:1,textAlign:"center",border:0,borderRadius:0,transform:"translate(-50%, -50%)"},"&-content":{position:"relative",insetBlockStart:n(n(e.fontSize).mul(e.lineHeight).sub(e.fontSize)).mul(-1).add(e.lineWidth).equal(),marginInlineStart:n(e.margin).add(e.itemHeadSize).equal(),marginInlineEnd:0,marginBlockStart:0,marginBlockEnd:0,wordBreak:"break-word"},"&-last":{[`> ${t}-item-tail`]:{display:"none"},[`> ${t}-item-content`]:{minHeight:n(e.controlHeightLG).mul(1.2).equal()}}},[`&${t}-alternate,\n        &${t}-right,\n        &${t}-label`]:{[`${t}-item`]:{"&-tail, &-head, &-head-custom":{insetInlineStart:"50%"},"&-head":{marginInlineStart:n(e.marginXXS).mul(-1).equal(),"&-custom":{marginInlineStart:n(e.tailWidth).div(2).equal()}},"&-left":{[`${t}-item-content`]:{insetInlineStart:`calc(50% - ${(0,s.zA)(e.marginXXS)})`,width:`calc(50% - ${(0,s.zA)(e.marginSM)})`,textAlign:"start"}},"&-right":{[`${t}-item-content`]:{width:`calc(50% - ${(0,s.zA)(e.marginSM)})`,margin:0,textAlign:"end"}}}},[`&${t}-right`]:{[`${t}-item-right`]:{[`${t}-item-tail,\n            ${t}-item-head,\n            ${t}-item-head-custom`]:{insetInlineStart:`calc(100% - ${(0,s.zA)(n(n(e.itemHeadSize).add(e.tailWidth)).div(2).equal())})`},[`${t}-item-content`]:{width:`calc(100% - ${(0,s.zA)(n(e.itemHeadSize).add(e.marginXS).equal())})`}}},[`&${t}-pending\n        ${t}-item-last\n        ${t}-item-tail`]:{display:"block",height:`calc(100% - ${(0,s.zA)(e.margin)})`,borderInlineStart:`${(0,s.zA)(e.tailWidth)} dotted ${e.tailColor}`},[`&${t}-reverse\n        ${t}-item-last\n        ${t}-item-tail`]:{display:"none"},[`&${t}-reverse ${t}-item-pending`]:{[`${t}-item-tail`]:{insetBlockStart:e.margin,display:"block",height:`calc(100% - ${(0,s.zA)(e.margin)})`,borderInlineStart:`${(0,s.zA)(e.tailWidth)} dotted ${e.tailColor}`},[`${t}-item-content`]:{minHeight:n(e.controlHeightLG).mul(1.2).equal()}},[`&${t}-label`]:{[`${t}-item-label`]:{position:"absolute",insetBlockStart:n(n(e.fontSize).mul(e.lineHeight).sub(e.fontSize)).mul(-1).add(e.tailWidth).equal(),width:`calc(50% - ${(0,s.zA)(e.marginSM)})`,textAlign:"end"},[`${t}-item-right`]:{[`${t}-item-label`]:{insetInlineStart:`calc(50% + ${(0,s.zA)(e.marginSM)})`,width:`calc(50% - ${(0,s.zA)(e.marginSM)})`,textAlign:"start"}}},"&-rtl":{direction:"rtl",[`${t}-item-head-custom`]:{transform:"translate(50%, -50%)"}}})}},p=(0,d.OF)("Timeline",(e=>{const t=(0,d.oX)(e,{itemHeadSize:10,customHeadPaddingVertical:e.paddingXXS,paddingInlineEnd:2});return[u(t)]}),(e=>({tailColor:e.colorSplit,tailWidth:e.lineWidthBold,dotBorderWidth:e.wireframe?e.lineWidthBold:3*e.lineWidth,dotBg:e.colorBgContainer,itemPaddingBottom:1.25*e.padding})));const m=e=>{var{prefixCls:t,className:n,color:r="blue",dot:a,pending:s=!1,position:c,label:d,children:u}=e,p=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["prefixCls","className","color","dot","pending","position","label","children"]);const{getPrefixCls:m}=o.useContext(i.QO),g=m("timeline",t),b=l()(`${g}-item`,{[`${g}-item-pending`]:s},n),f=/blue|red|green|gray/.test(r||"")?void 0:r,h=l()(`${g}-item-head`,{[`${g}-item-head-custom`]:!!a,[`${g}-item-head-${r}`]:!f});return o.createElement("li",Object.assign({},p,{className:b}),d&&o.createElement("div",{className:`${g}-item-label`},d),o.createElement("div",{className:`${g}-item-tail`}),o.createElement("div",{className:h,style:{borderColor:f,color:f}},a),o.createElement("div",{className:`${g}-item-content`},u))};var g=n(60436),b=n(36962),f=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const h=e=>{var{prefixCls:t,className:n,pending:r=!1,children:i,items:a,rootClassName:s,reverse:c=!1,direction:d,hashId:u,pendingDot:p,mode:h=""}=e,v=f(e,["prefixCls","className","pending","children","items","rootClassName","reverse","direction","hashId","pendingDot","mode"]);const y=(e,n)=>"alternate"===h?"right"===e?`${t}-item-right`:"left"===e||n%2==0?`${t}-item-left`:`${t}-item-right`:"left"===h?`${t}-item-left`:"right"===h||"right"===e?`${t}-item-right`:"",$=(0,g.A)(a||[]),x="boolean"==typeof r?null:r;r&&$.push({pending:!!r,dot:p||o.createElement(b.A,null),children:x}),c&&$.reverse();const O=$.length,w=`${t}-item-last`,S=$.filter((e=>!!e)).map(((e,t)=>{var n;const i=t===O-2?w:"",a=t===O-1?w:"",{className:s}=e,d=f(e,["className"]);return o.createElement(m,Object.assign({},d,{className:l()([s,!c&&r?i:a,y(null!==(n=null==e?void 0:e.position)&&void 0!==n?n:"",t)]),key:(null==e?void 0:e.key)||t}))})),C=$.some((e=>!!(null==e?void 0:e.label))),A=l()(t,{[`${t}-pending`]:!!r,[`${t}-reverse`]:!!c,[`${t}-${h}`]:!!h&&!C,[`${t}-label`]:C,[`${t}-rtl`]:"rtl"===d},n,s,u);return o.createElement("ul",Object.assign({},v,{className:A}),S)};var v=n(82546);(e=>{const{getPrefixCls:t,direction:n,timeline:r}=o.useContext(i.QO),{prefixCls:s,children:c,items:d,className:u,style:m}=e,g=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["prefixCls","children","items","className","style"]),b=t("timeline",s),f=(0,a.A)(b),[y,$,x]=p(b,f),O=function(e,t){return e&&Array.isArray(e)?e:(0,v.A)(t).map((e=>{var t,n;return Object.assign({children:null!==(n=null===(t=null==e?void 0:e.props)||void 0===t?void 0:t.children)&&void 0!==n?n:""},e.props)}))}(d,c);return y(o.createElement(h,Object.assign({},g,{className:l()(null==r?void 0:r.className,u,x,f),style:Object.assign(Object.assign({},null==r?void 0:r.style),m),prefixCls:b,direction:n,items:O,hashId:$})))}).Item=m},37977:(e,t,n)=>{n.d(t,{A:()=>j});var o=n(96540),r=n(46942),l=n.n(r),i=n(80427),a=n(12533),s=n(62897),c=n(60275),d=n(23723),u=n(13257),p=n(40682),m=n(18877),g=n(72616),b=n(62279),f=n(51113),h=n(38674),v=n(36891),y=n(25905),$=n(38328),x=n(95201),O=n(20791);const w=e=>{const{calc:t,componentCls:n,tooltipMaxWidth:o,tooltipColor:r,tooltipBg:l,tooltipBorderRadius:i,zIndexPopup:a,controlHeight:s,boxShadowSecondary:c,paddingSM:d,paddingXS:u,arrowOffsetHorizontal:p,sizePopupArrow:m}=e,g=t(i).add(m).add(p).equal(),b=t(i).mul(2).add(m).equal();return[{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,y.dF)(e)),{position:"absolute",zIndex:a,display:"block",width:"max-content",maxWidth:o,visibility:"visible","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"&-hidden":{display:"none"},"--antd-arrow-background-color":l,[`${n}-inner`]:{minWidth:b,minHeight:s,padding:`${(0,v.zA)(e.calc(d).div(2).equal())} ${(0,v.zA)(u)}`,color:r,textAlign:"start",textDecoration:"none",wordWrap:"break-word",backgroundColor:l,borderRadius:i,boxShadow:c,boxSizing:"border-box"},[["&-placement-topLeft","&-placement-topRight","&-placement-bottomLeft","&-placement-bottomRight"].join(",")]:{minWidth:g},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${n}-inner`]:{borderRadius:e.min(i,x.Zs)}},[`${n}-content`]:{position:"relative"}}),(0,f.nP)(e,((e,{darkColor:t})=>({[`&${n}-${e}`]:{[`${n}-inner`]:{backgroundColor:t},[`${n}-arrow`]:{"--antd-arrow-background-color":t}}})))),{"&-rtl":{direction:"rtl"}})},(0,x.Ay)(e,"var(--antd-arrow-background-color)"),{[`${n}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow}}]},S=e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+70},(0,x.Ke)({contentRadius:e.borderRadius,limitVerticalRadius:!0})),(0,O.n)((0,f.oX)(e,{borderRadiusOuter:Math.min(e.borderRadiusOuter,4)}))),C=(e,t=!0)=>(0,f.OF)("Tooltip",(e=>{const{borderRadius:t,colorTextLightSolid:n,colorBgSpotlight:o}=e,r=(0,f.oX)(e,{tooltipMaxWidth:250,tooltipColor:n,tooltipBorderRadius:t,tooltipBg:o});return[w(r),(0,$.aB)(e,"zoom-big-fast")]}),S,{resetStyle:!1,injectStyle:t})(e);var A=n(54121);function k(e,t){const n=(0,A.nP)(t),o=l()({[`${e}-${t}`]:t&&n}),r={},i={};return t&&!n&&(r.background=t,i["--antd-arrow-background-color"]=t),{className:o,overlayStyle:r,arrowStyle:i}}const E=o.forwardRef(((e,t)=>{var n,r;const{prefixCls:h,openClassName:v,getTooltipContainer:y,color:$,overlayInnerStyle:x,children:O,afterOpenChange:w,afterVisibleChange:S,destroyTooltipOnHide:A,destroyOnHidden:E,arrow:j=!0,title:I,overlay:N,builtinPlacements:P,arrowPointAtCenter:z=!1,autoAdjustOverflow:R=!0,motion:B,getPopupContainer:T,placement:H="top",mouseEnterDelay:W=.1,mouseLeaveDelay:L=.1,overlayStyle:M,rootClassName:D,overlayClassName:F,styles:K,classNames:X}=e,q=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["prefixCls","openClassName","getTooltipContainer","color","overlayInnerStyle","children","afterOpenChange","afterVisibleChange","destroyTooltipOnHide","destroyOnHidden","arrow","title","overlay","builtinPlacements","arrowPointAtCenter","autoAdjustOverflow","motion","getPopupContainer","placement","mouseEnterDelay","mouseLeaveDelay","overlayStyle","rootClassName","overlayClassName","styles","classNames"]),V=!!j,[,_]=(0,f.rd)(),{getPopupContainer:Y,getPrefixCls:G,direction:Q,className:U,style:Z,classNames:J,styles:ee}=(0,b.TP)("tooltip"),te=(0,m.rJ)("Tooltip"),ne=o.useRef(null),oe=()=>{var e;null===(e=ne.current)||void 0===e||e.forceAlign()};o.useImperativeHandle(t,(()=>{var e,t;return{forceAlign:oe,forcePopupAlign:()=>{te.deprecated(!1,"forcePopupAlign","forceAlign"),oe()},nativeElement:null===(e=ne.current)||void 0===e?void 0:e.nativeElement,popupElement:null===(t=ne.current)||void 0===t?void 0:t.popupElement}}));const[re,le]=(0,a.A)(!1,{value:null!==(n=e.open)&&void 0!==n?n:e.visible,defaultValue:null!==(r=e.defaultOpen)&&void 0!==r?r:e.defaultVisible}),ie=!I&&!N&&0!==I,ae=o.useMemo((()=>{var e,t;let n=z;return"object"==typeof j&&(n=null!==(t=null!==(e=j.pointAtCenter)&&void 0!==e?e:j.arrowPointAtCenter)&&void 0!==t?t:z),P||(0,u.A)({arrowPointAtCenter:n,autoAdjustOverflow:R,arrowWidth:V?_.sizePopupArrow:0,borderRadius:_.borderRadius,offset:_.marginXXS,visibleFirst:!0})}),[z,j,P,_]),se=o.useMemo((()=>0===I?I:N||I||""),[N,I]),ce=o.createElement(s.A,{space:!0},"function"==typeof se?se():se),de=G("tooltip",h),ue=G(),pe=e["data-popover-inject"];let me=re;"open"in e||"visible"in e||!ie||(me=!1);const ge=o.isValidElement(O)&&!(0,p.zv)(O)?O:o.createElement("span",null,O),be=ge.props,fe=be.className&&"string"!=typeof be.className?be.className:l()(be.className,v||`${de}-open`),[he,ve,ye]=C(de,!pe),$e=k(de,$),xe=$e.arrowStyle,Oe=l()(F,{[`${de}-rtl`]:"rtl"===Q},$e.className,D,ve,ye,U,J.root,null==X?void 0:X.root),we=l()(J.body,null==X?void 0:X.body),[Se,Ce]=(0,c.YK)("Tooltip",q.zIndex),Ae=o.createElement(i.A,Object.assign({},q,{zIndex:Se,showArrow:V,placement:H,mouseEnterDelay:W,mouseLeaveDelay:L,prefixCls:de,classNames:{root:Oe,body:we},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},xe),ee.root),Z),M),null==K?void 0:K.root),body:Object.assign(Object.assign(Object.assign(Object.assign({},ee.body),x),null==K?void 0:K.body),$e.overlayStyle)},getTooltipContainer:T||y||Y,ref:ne,builtinPlacements:ae,overlay:ce,visible:me,onVisibleChange:t=>{var n,o;le(!ie&&t),ie||(null===(n=e.onOpenChange)||void 0===n||n.call(e,t),null===(o=e.onVisibleChange)||void 0===o||o.call(e,t))},afterVisibleChange:null!=w?w:S,arrowContent:o.createElement("span",{className:`${de}-arrow-content`}),motion:{motionName:(0,d.b)(ue,"zoom-big-fast",e.transitionName),motionDeadline:1e3},destroyTooltipOnHide:null!=E?E:!!A}),me?(0,p.Ob)(ge,{className:fe}):ge);return he(o.createElement(g.A.Provider,{value:Ce},Ae))}));E._InternalPanelDoNotUseOrYouWillBeFired=e=>{const{prefixCls:t,className:n,placement:r="top",title:a,color:s,overlayInnerStyle:c}=e,{getPrefixCls:d}=o.useContext(h.QO),u=d("tooltip",t),[p,m,g]=C(u),b=k(u,s),f=b.arrowStyle,v=Object.assign(Object.assign({},c),b.overlayStyle),y=l()(m,g,u,`${u}-pure`,`${u}-placement-${r}`,n,b.className);return p(o.createElement("div",{className:y,style:f},o.createElement("div",{className:`${u}-arrow`}),o.createElement(i.z,Object.assign({},e,{className:m,prefixCls:u,overlayInnerStyle:v}),a)))};const j=E},65341:(e,t,n)=>{n.d(t,{A:()=>o});const o={placeholder:"Select time",rangePlaceholder:["Start time","End time"]}},75475:(e,t,n)=>{n.d(t,{A:()=>pe});var o=n(96540),r=(n(18877),n(94824)),l=n(46942),i=n.n(l),a=n(26076),s=n(82546),c=n(30981),d=n(12533),u=n(19853),p=n(8719),m=n(75945),g=n(38674),b=n(19155),f=n(37977),h=n(77188),v=n(16928),y=n(40682),$=n(82322),x=n(25905),O=n(51113),w=n(45748),S=n(36891);const C=e=>{const t={};return[1,2,3,4,5].forEach((n=>{t[`\n      h${n}&,\n      div&-h${n},\n      div&-h${n} > textarea,\n      h${n}\n    `]=((e,t,n,o)=>{const{titleMarginBottom:r,fontWeightStrong:l}=o;return{marginBottom:r,color:n,fontWeight:l,fontSize:e,lineHeight:t}})(e[`fontSizeHeading${n}`],e[`lineHeightHeading${n}`],e.colorTextHeading,e)})),t},A=e=>{const{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},(0,x.Y1)(e)),{userSelect:"text",[`&[disabled], &${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},k=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:w.bK[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),E=e=>{const{componentCls:t,paddingSM:n}=e,o=n;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(o).mul(-1).equal(),marginBottom:`calc(1em - ${(0,S.zA)(o)})`},[`${t}-edit-content-confirm`]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorIcon,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},j=e=>({[`${e.componentCls}-copy-success`]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},[`${e.componentCls}-copy-icon-only`]:{marginInlineStart:0}}),I=e=>{const{componentCls:t,titleMarginTop:n}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,[`&${t}-secondary`]:{color:e.colorTextDescription},[`&${t}-success`]:{color:e.colorSuccessText},[`&${t}-warning`]:{color:e.colorWarningText},[`&${t}-danger`]:{color:e.colorErrorText,"a&:active, a&:focus":{color:e.colorErrorTextActive},"a&:hover":{color:e.colorErrorTextHover}},[`&${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},C(e)),{[`\n      & + h1${t},\n      & + h2${t},\n      & + h3${t},\n      & + h4${t},\n      & + h5${t}\n      `]:{marginTop:n},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:n}}}),k(e)),A(e)),{[`\n        ${t}-expand,\n        ${t}-collapse,\n        ${t}-edit,\n        ${t}-copy\n      `]:Object.assign(Object.assign({},(0,x.Y1)(e)),{marginInlineStart:e.marginXXS})}),E(e)),j(e)),{"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),{"&-rtl":{direction:"rtl"}})}},N=(0,O.OF)("Typography",(e=>[I(e)]),(()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"}))),P=e=>{const{prefixCls:t,"aria-label":n,className:r,style:l,direction:a,maxLength:s,autoSize:c=!0,value:d,onSave:u,onCancel:p,onEnd:m,component:g,enterIcon:b=o.createElement(h.A,null)}=e,f=o.useRef(null),x=o.useRef(!1),O=o.useRef(null),[w,S]=o.useState(d);o.useEffect((()=>{S(d)}),[d]),o.useEffect((()=>{var e;if(null===(e=f.current)||void 0===e?void 0:e.resizableTextArea){const{textArea:e}=f.current.resizableTextArea;e.focus();const{length:t}=e.value;e.setSelectionRange(t,t)}}),[]);const C=()=>{u(w.trim())},[A,k,E]=N(t),j=i()(t,`${t}-edit-content`,{[`${t}-rtl`]:"rtl"===a,[`${t}-${g}`]:!!g},r,k,E);return A(o.createElement("div",{className:j,style:l},o.createElement($.A,{ref:f,maxLength:s,value:w,onChange:({target:e})=>{S(e.value.replace(/[\n\r]/g,""))},onKeyDown:({keyCode:e})=>{x.current||(O.current=e)},onKeyUp:({keyCode:e,ctrlKey:t,altKey:n,metaKey:o,shiftKey:r})=>{O.current!==e||x.current||t||n||o||r||(e===v.A.ENTER?(C(),null==m||m()):e===v.A.ESC&&p())},onCompositionStart:()=>{x.current=!0},onCompositionEnd:()=>{x.current=!1},onBlur:()=>{C()},"aria-label":n,rows:1,autoSize:c}),null!==b?(0,y.Ob)(b,{className:`${t}-edit-content-confirm`}):null))};var z=n(17965),R=n.n(z),B=n(26956),T=n(81168);const H=({copyConfig:e,children:t})=>{const[n,r]=o.useState(!1),[l,i]=o.useState(!1),a=o.useRef(null),s=()=>{a.current&&clearTimeout(a.current)},c={};return e.format&&(c.format=e.format),o.useEffect((()=>s),[]),{copied:n,copyLoading:l,onClick:(0,B.A)((n=>{return o=void 0,l=void 0,u=function*(){var o;null==n||n.preventDefault(),null==n||n.stopPropagation(),i(!0);try{const l="function"==typeof e.text?yield e.text():e.text;R()(l||(0,T.A)(t,!0).join("")||"",c),i(!1),r(!0),s(),a.current=setTimeout((()=>{r(!1)}),3e3),null===(o=e.onCopy)||void 0===o||o.call(e,n)}catch(e){throw i(!1),e}},new((d=void 0)||(d=Promise))((function(e,t){function n(e){try{i(u.next(e))}catch(e){t(e)}}function r(e){try{i(u.throw(e))}catch(e){t(e)}}function i(t){var o;t.done?e(t.value):(o=t.value,o instanceof d?o:new d((function(e){e(o)}))).then(n,r)}i((u=u.apply(o,l||[])).next())}));var o,l,d,u}))}};function W(e,t){return o.useMemo((()=>{const n=!!e;return[n,Object.assign(Object.assign({},t),n&&"object"==typeof e?e:null)]}),[e])}var L=n(62279);const M=o.forwardRef(((e,t)=>{const{prefixCls:n,component:r="article",className:l,rootClassName:a,setContentRef:s,children:c,direction:d,style:u}=e,m=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:g,direction:b,className:f,style:h}=(0,L.TP)("typography"),v=null!=d?d:b,y=s?(0,p.K4)(t,s):t,$=g("typography",n),[x,O,w]=N($),S=i()($,f,{[`${$}-rtl`]:"rtl"===v},l,a,O,w),C=Object.assign(Object.assign({},h),u);return x(o.createElement(r,Object.assign({className:S,style:C,ref:y},m),c))})),D=M;var F=n(77906),K=n(30513),X=n(36962);function q(e){return!1===e?[!1,!1]:Array.isArray(e)?e:[e]}function V(e,t,n){return!0===e||void 0===e?t:e||n&&t}const _=e=>["string","number"].includes(typeof e),Y=({prefixCls:e,copied:t,locale:n,iconOnly:r,tooltips:l,icon:a,tabIndex:s,onCopy:c,loading:d})=>{const u=q(l),p=q(a),{copied:m,copy:g}=null!=n?n:{},b=t?m:g,h=V(u[t?1:0],b),v="string"==typeof h?h:b;return o.createElement(f.A,{title:h},o.createElement("button",{type:"button",className:i()(`${e}-copy`,{[`${e}-copy-success`]:t,[`${e}-copy-icon-only`]:r}),onClick:c,"aria-label":v,tabIndex:s},t?V(p[1],o.createElement(F.A,null),!0):V(p[0],d?o.createElement(X.A,null):o.createElement(K.A,null),!0)))};var G=n(60436);const Q=o.forwardRef((({style:e,children:t},n)=>{const r=o.useRef(null);return o.useImperativeHandle(n,(()=>({isExceed:()=>{const e=r.current;return e.scrollHeight>e.clientHeight},getHeight:()=>r.current.clientHeight}))),o.createElement("span",{"aria-hidden":!0,ref:r,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},e)},t)}));function U(e,t){let n=0;const o=[];for(let r=0;r<e.length;r+=1){if(n===t)return o;const l=e[r],i=n+(_(l)?String(l).length:1);if(i>t){const e=t-n;return o.push(String(l).slice(0,e)),o}o.push(l),n=i}return e}const Z=0,J=4,ee={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function te(e){const{enableMeasure:t,width:n,text:r,children:l,rows:i,expanded:a,miscDeps:d,onEllipsis:u}=e,p=o.useMemo((()=>(0,s.A)(r)),[r]),m=o.useMemo((()=>(e=>e.reduce(((e,t)=>e+(_(t)?String(t).length:1)),0))(p)),[r]),g=o.useMemo((()=>l(p,!1)),[r]),[b,f]=o.useState(null),h=o.useRef(null),v=o.useRef(null),y=o.useRef(null),$=o.useRef(null),x=o.useRef(null),[O,w]=o.useState(!1),[S,C]=o.useState(Z),[A,k]=o.useState(0),[E,j]=o.useState(null);(0,c.A)((()=>{C(t&&n&&m?1:Z)}),[n,r,i,t,p]),(0,c.A)((()=>{var e,t,n,o;if(1===S){C(2);const e=v.current&&getComputedStyle(v.current).whiteSpace;j(e)}else if(2===S){const r=!!(null===(e=y.current)||void 0===e?void 0:e.isExceed());C(r?3:J),f(r?[0,m]:null),w(r);const l=(null===(t=y.current)||void 0===t?void 0:t.getHeight())||0,a=1===i?0:(null===(n=$.current)||void 0===n?void 0:n.getHeight())||0,s=(null===(o=x.current)||void 0===o?void 0:o.getHeight())||0,c=Math.max(l,a+s);k(c+1),u(r)}}),[S]);const I=b?Math.ceil((b[0]+b[1])/2):0;(0,c.A)((()=>{var e;const[t,n]=b||[0,0];if(t!==n){const o=((null===(e=h.current)||void 0===e?void 0:e.getHeight())||0)>A;let r=I;n-t===1&&(r=o?t:n),f(o?[t,r]:[r,n])}}),[b,I]);const N=o.useMemo((()=>{if(!t)return l(p,!1);if(3!==S||!b||b[0]!==b[1]){const e=l(p,!1);return[J,Z].includes(S)?e:o.createElement("span",{style:Object.assign(Object.assign({},ee),{WebkitLineClamp:i})},e)}return l(a?p:U(p,b[0]),O)}),[a,S,b,p].concat((0,G.A)(d))),P={width:n,margin:0,padding:0,whiteSpace:"nowrap"===E?"normal":"inherit"};return o.createElement(o.Fragment,null,N,2===S&&o.createElement(o.Fragment,null,o.createElement(Q,{style:Object.assign(Object.assign(Object.assign({},P),ee),{WebkitLineClamp:i}),ref:y},g),o.createElement(Q,{style:Object.assign(Object.assign(Object.assign({},P),ee),{WebkitLineClamp:i-1}),ref:$},g),o.createElement(Q,{style:Object.assign(Object.assign(Object.assign({},P),ee),{WebkitLineClamp:1}),ref:x},l([],!0))),3===S&&b&&b[0]!==b[1]&&o.createElement(Q,{style:Object.assign(Object.assign({},P),{top:400}),ref:h},l(U(p,I),!0)),1===S&&o.createElement("span",{style:{whiteSpace:"inherit"},ref:v}))}const ne=({enableEllipsis:e,isEllipsis:t,children:n,tooltipProps:r})=>(null==r?void 0:r.title)&&e?o.createElement(f.A,Object.assign({open:!!t&&void 0},r),n):n;const oe=o.forwardRef(((e,t)=>{var n;const{prefixCls:l,className:h,style:v,type:y,disabled:$,children:x,ellipsis:O,editable:w,copyable:S,component:C,title:A}=e,k=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:E,direction:j}=o.useContext(g.QO),[I]=(0,b.A)("Text"),N=o.useRef(null),z=o.useRef(null),R=E("typography",l),B=(0,u.A)(k,["mark","code","delete","underline","strong","keyboard","italic"]),[T,L]=W(w),[M,F]=(0,d.A)(!1,{value:L.editing}),{triggerType:K=["icon"]}=L,X=e=>{var t;e&&(null===(t=L.onStart)||void 0===t||t.call(L)),F(e)},q=(e=>{const t=(0,o.useRef)(void 0);return(0,o.useEffect)((()=>{t.current=e})),t.current})(M);(0,c.A)((()=>{var e;!M&&q&&(null===(e=z.current)||void 0===e||e.focus())}),[M]);const V=e=>{null==e||e.preventDefault(),X(!0)},[G,Q]=W(S),{copied:U,copyLoading:Z,onClick:J}=H({copyConfig:Q,children:x}),[ee,oe]=o.useState(!1),[re,le]=o.useState(!1),[ie,ae]=o.useState(!1),[se,ce]=o.useState(!1),[de,ue]=o.useState(!0),[pe,me]=W(O,{expandable:!1,symbol:e=>e?null==I?void 0:I.collapse:null==I?void 0:I.expand}),[ge,be]=(0,d.A)(me.defaultExpanded||!1,{value:me.expanded}),fe=pe&&(!ge||"collapsible"===me.expandable),{rows:he=1}=me,ve=o.useMemo((()=>fe&&(void 0!==me.suffix||me.onEllipsis||me.expandable||T||G)),[fe,me,T,G]);(0,c.A)((()=>{pe&&!ve&&(oe((0,m.F)("webkitLineClamp")),le((0,m.F)("textOverflow")))}),[ve,pe]);const[ye,$e]=o.useState(fe),xe=o.useMemo((()=>!ve&&(1===he?re:ee)),[ve,re,ee]);(0,c.A)((()=>{$e(xe&&fe)}),[xe,fe]);const Oe=fe&&(ye?se:ie),we=fe&&1===he&&ye,Se=fe&&he>1&&ye,[Ce,Ae]=o.useState(0),ke=e=>{var t;ae(e),ie!==e&&(null===(t=me.onEllipsis)||void 0===t||t.call(me,e))};o.useEffect((()=>{const e=N.current;if(pe&&ye&&e){const t=function(e){const t=document.createElement("em");e.appendChild(t);const n=e.getBoundingClientRect(),o=t.getBoundingClientRect();return e.removeChild(t),n.left>o.left||o.right>n.right||n.top>o.top||o.bottom>n.bottom}(e);se!==t&&ce(t)}}),[pe,ye,x,Se,de,Ce]),o.useEffect((()=>{const e=N.current;if("undefined"==typeof IntersectionObserver||!e||!ye||!fe)return;const t=new IntersectionObserver((()=>{ue(!!e.offsetParent)}));return t.observe(e),()=>{t.disconnect()}}),[ye,fe]);const Ee=((e,t,n)=>(0,o.useMemo)((()=>!0===e?{title:null!=t?t:n}:(0,o.isValidElement)(e)?{title:e}:"object"==typeof e?Object.assign({title:null!=t?t:n},e):{title:e}),[e,t,n]))(me.tooltip,L.text,x),je=o.useMemo((()=>{if(pe&&!ye)return[L.text,x,A,Ee.title].find(_)}),[pe,ye,A,Ee.title,Oe]);if(M)return o.createElement(P,{value:null!==(n=L.text)&&void 0!==n?n:"string"==typeof x?x:"",onSave:e=>{var t;null===(t=L.onChange)||void 0===t||t.call(L,e),X(!1)},onCancel:()=>{var e;null===(e=L.onCancel)||void 0===e||e.call(L),X(!1)},onEnd:L.onEnd,prefixCls:R,className:h,style:v,direction:j,component:C,maxLength:L.maxLength,autoSize:L.autoSize,enterIcon:L.enterIcon});const Ie=()=>{const{expandable:e,symbol:t}=me;return e?o.createElement("button",{type:"button",key:"expand",className:`${R}-${ge?"collapse":"expand"}`,onClick:e=>((e,t)=>{var n;be(t.expanded),null===(n=me.onExpand)||void 0===n||n.call(me,e,t)})(e,{expanded:!ge}),"aria-label":ge?I.collapse:null==I?void 0:I.expand},"function"==typeof t?t(ge):t):null},Ne=()=>{if(!T)return;const{icon:e,tooltip:t,tabIndex:n}=L,l=(0,s.A)(t)[0]||(null==I?void 0:I.edit),i="string"==typeof l?l:"";return K.includes("icon")?o.createElement(f.A,{key:"edit",title:!1===t?"":l},o.createElement("button",{type:"button",ref:z,className:`${R}-edit`,onClick:V,"aria-label":i,tabIndex:n},e||o.createElement(r.A,{role:"button"}))):null},Pe=e=>[e&&Ie(),Ne(),G?o.createElement(Y,Object.assign({key:"copy"},Q,{prefixCls:R,copied:U,locale:I,onCopy:J,loading:Z,iconOnly:null==x})):null];return o.createElement(a.A,{onResize:({offsetWidth:e})=>{Ae(e)},disabled:!fe},(n=>o.createElement(ne,{tooltipProps:Ee,enableEllipsis:fe,isEllipsis:Oe},o.createElement(D,Object.assign({className:i()({[`${R}-${y}`]:y,[`${R}-disabled`]:$,[`${R}-ellipsis`]:pe,[`${R}-ellipsis-single-line`]:we,[`${R}-ellipsis-multiple-line`]:Se},h),prefixCls:l,style:Object.assign(Object.assign({},v),{WebkitLineClamp:Se?he:void 0}),component:C,ref:(0,p.K4)(n,N,t),direction:j,onClick:K.includes("text")?V:void 0,"aria-label":null==je?void 0:je.toString(),title:A},B),o.createElement(te,{enableMeasure:fe&&!ye,text:x,rows:he,width:Ce,onEllipsis:ke,expanded:ge,miscDeps:[U,ge,Z,T,G,I]},((t,n)=>function({mark:e,code:t,underline:n,delete:r,strong:l,keyboard:i,italic:a},s){let c=s;function d(e,t){t&&(c=o.createElement(e,{},c))}return d("strong",l),d("u",n),d("del",r),d("code",t),d("mark",e),d("kbd",i),d("i",a),c}(e,o.createElement(o.Fragment,null,t.length>0&&n&&!ge&&je?o.createElement("span",{key:"show-content","aria-hidden":!0},t):t,(e=>[e&&!ge&&o.createElement("span",{"aria-hidden":!0,key:"ellipsis"},"..."),me.suffix,Pe(e)])(n)))))))))})),re=oe;const le=o.forwardRef(((e,t)=>{var{ellipsis:n,rel:r}=e,l=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["ellipsis","rel"]);const i=Object.assign(Object.assign({},l),{rel:void 0===r&&"_blank"===l.target?"noopener noreferrer":r});return delete i.navigate,o.createElement(re,Object.assign({},i,{ref:t,ellipsis:!!n,component:"a"}))})),ie=o.forwardRef(((e,t)=>o.createElement(re,Object.assign({ref:t},e,{component:"div"}))));const ae=(e,t)=>{var{ellipsis:n}=e,r=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["ellipsis"]);const l=o.useMemo((()=>n&&"object"==typeof n?(0,u.A)(n,["expandable","rows"]):n),[n]);return o.createElement(re,Object.assign({ref:t},r,{ellipsis:l,component:"span"}))},se=o.forwardRef(ae);const ce=[1,2,3,4,5],de=o.forwardRef(((e,t)=>{const{level:n=1}=e,r=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["level"]),l=ce.includes(n)?`h${n}`:"h1";return o.createElement(re,Object.assign({ref:t},r,{component:l}))})),ue=D;ue.Text=se,ue.Link=le,ue.Title=de,ue.Paragraph=ie;const pe=ue},80296:(e,t,n)=>{var o=n(60436),r=n(96540),l=n(46942),i=n.n(l),a=n(27681),s=n(58182),c=n(24075),d=(n(18877),n(38674)),u=n(35128),p=n(94241),m=n(21282),g=n(8182);var b=n(81470);const f=[];function h(e,t){const n=e.filter((e=>t.has(e)));return e.length===n.length?e:n}function v(e){return Array.from(e).join(";")}var y=n(73964),$=n(19853),x=n(91196),O=n(88603),w=n(12533),S=n(44485),C=n(59499);const A=e=>{const{renderedText:t,renderedEl:n,item:o,checked:l,disabled:a,prefixCls:s,onClick:c,onRemove:d,showRemove:u}=e,p=i()(`${s}-content-item`,{[`${s}-content-item-disabled`]:a||o.disabled,[`${s}-content-item-checked`]:l&&!o.disabled});let b;"string"!=typeof t&&"number"!=typeof t||(b=String(t));const[f]=(0,m.Ym)("Transfer",g.A.Transfer),h={className:p,title:b},v=r.createElement("span",{className:`${s}-content-item-text`},n);return u?r.createElement("li",Object.assign({},h),v,r.createElement("button",{type:"button",disabled:a||o.disabled,className:`${s}-content-item-remove`,"aria-label":null==f?void 0:f.remove,onClick:()=>null==d?void 0:d(o)},r.createElement(C.A,null))):(h.onClick=a||o.disabled?void 0:e=>c(o,e),r.createElement("li",Object.assign({},h),r.createElement(x.A,{className:`${s}-checkbox`,checked:l,disabled:a||o.disabled}),v))},k=r.memo(A),E=["handleFilter","handleClear","checkedKeys"],j=(e,t)=>{const{prefixCls:n,filteredRenderItems:o,selectedKeys:l,disabled:a,showRemove:s,pagination:c,onScroll:d,onItemSelect:u,onItemRemove:p}=e,[m,g]=r.useState(1),b=r.useMemo((()=>c?(e=>Object.assign(Object.assign({},{simple:!0,showSizeChanger:!1,showLessItems:!1}),e))("object"==typeof c?c:{}):null),[c]),[f,h]=(0,w.A)(10,{value:null==b?void 0:b.pageSize});r.useEffect((()=>{if(b){const e=Math.ceil(o.length/f);g(Math.min(m,e))}}),[o,b,f]);const v=(e,t)=>{u(e.key,!l.includes(e.key),t)},y=e=>{null==p||p([e.key])},$=r.useMemo((()=>b?o.slice((m-1)*f,m*f):o),[m,o,b,f]);r.useImperativeHandle(t,(()=>({items:$})));const x=b?r.createElement(S.A,{size:"small",disabled:a,simple:b.simple,pageSize:f,showLessItems:b.showLessItems,showSizeChanger:b.showSizeChanger,className:`${n}-pagination`,total:o.length,current:m,onChange:e=>{g(e)},onShowSizeChange:(e,t)=>{g(e),h(t)}}):null,O=i()(`${n}-content`,{[`${n}-content-show-remove`]:s});return r.createElement(r.Fragment,null,r.createElement("ul",{className:O,onScroll:d},($||[]).map((({renderedEl:e,renderedText:t,item:o})=>r.createElement(k,{key:o.key,item:o,renderedText:t,renderedEl:e,prefixCls:n,showRemove:s,onClick:v,onRemove:y,checked:l.includes(o.key),disabled:a||o.disabled})))),x)},I=r.forwardRef(j);var N=n(20736),P=n(18017);const z=e=>{const{placeholder:t="",value:n,prefixCls:o,disabled:l,onChange:i,handleClear:a}=e,s=r.useCallback((e=>{null==i||i(e),""===e.target.value&&(null==a||a())}),[i]);return r.createElement(P.A,{placeholder:t,className:o,value:n,onChange:s,disabled:l,allowClear:!0,prefix:r.createElement(N.A,null)})},R=()=>null;function B(e){return e.filter((e=>!e.disabled)).map((e=>e.key))}const T=e=>{const{prefixCls:t,dataSource:n=[],titleText:o="",checkedKeys:l,disabled:a,showSearch:s=!1,style:d,searchPlaceholder:u,notFoundContent:p,selectAll:m,deselectAll:g,selectCurrent:b,selectInvert:f,removeAll:h,removeCurrent:v,showSelectAll:w=!0,showRemove:S,pagination:C,direction:A,itemsUnit:k,itemUnit:j,selectAllLabel:N,selectionsIcon:P,footer:T,renderList:H,onItemSelectAll:W,onItemRemove:L,handleFilter:M,handleClear:D,filterOption:F,render:K=R}=e,X=(e=>e&&"object"==typeof e?Object.assign(Object.assign({},e),{defaultValue:e.defaultValue||""}):{defaultValue:"",placeholder:""})(s),[q,V]=(0,r.useState)(X.defaultValue),_=(0,r.useRef)({}),Y=e=>{V(e.target.value),M(e)},G=()=>{V(""),D()},Q=(0,r.useMemo)((()=>Array.isArray(p)?p["left"===A?0:1]:p),[p,A]),[U,Z]=(0,r.useMemo)((()=>{const e=[],t=[];return n.forEach((n=>{const o=(e=>{const t=K(e),n=!(!(o=t)||r.isValidElement(o)||"[object Object]"!==Object.prototype.toString.call(o));var o;return{item:e,renderedEl:n?t.label:t,renderedText:n?t.value:t}})(n);q&&!((e,t)=>F?F(q,t,A):e.includes(q))(o.renderedText,n)||(e.push(n),t.push(o))})),[e,t]}),[n,q]),J=(0,r.useMemo)((()=>U.filter((e=>l.includes(e.key)&&!e.disabled))),[l,U]),ee=(0,r.useMemo)((()=>{if(0===J.length)return"none";const e=(0,c.W)(l);return U.every((t=>e.has(t.key)||!!t.disabled))?"all":"part"}),[l,J]),te=(0,r.useMemo)((()=>{const n=s?r.createElement("div",{className:`${t}-body-search-wrapper`},r.createElement(z,{prefixCls:`${t}-search`,onChange:Y,handleClear:G,placeholder:X.placeholder||u,value:q,disabled:a})):null,{customize:o,bodyContent:c}=(e=>{let t=H?H(Object.assign(Object.assign({},e),{onItemSelect:(t,n)=>e.onItemSelect(t,n)})):null;const n=!!t;return n||(t=r.createElement(I,Object.assign({ref:_},e))),{customize:n,bodyContent:t}})(Object.assign(Object.assign({},(0,$.A)(e,E)),{filteredItems:U,filteredRenderItems:Z,selectedKeys:l}));let d;return d=o?r.createElement("div",{className:`${t}-body-customize-wrapper`},c):U.length?c:r.createElement("div",{className:`${t}-body-not-found`},Q),r.createElement("div",{className:i()(`${t}-body`,{[`${t}-body-with-search`]:s})},n,d)}),[s,t,u,q,a,l,U,Z,Q]),ne=r.createElement(x.A,{disabled:0===n.filter((e=>!e.disabled)).length||a,checked:"all"===ee,indeterminate:"part"===ee,className:`${t}-checkbox`,onChange:()=>{null==W||W(U.filter((e=>!e.disabled)).map((({key:e})=>e)),"all"!==ee)}}),oe=T&&(T.length<2?T(e):T(e,{direction:A})),re=i()(t,{[`${t}-with-pagination`]:!!C,[`${t}-with-footer`]:!!oe}),le=oe?r.createElement("div",{className:`${t}-footer`},oe):null,ie=!S&&!C&&ne;let ae;ae=S?[C?{key:"removeCurrent",label:v,onClick(){var e;const t=B(((null===(e=_.current)||void 0===e?void 0:e.items)||[]).map((e=>e.item)));null==L||L(t)}}:null,{key:"removeAll",label:h,onClick(){null==L||L(B(U))}}].filter(Boolean):[{key:"selectAll",label:"all"===ee?g:m,onClick(){const e=B(U);null==W||W(e,e.length!==l.length)}},C?{key:"selectCurrent",label:b,onClick(){var e;const t=(null===(e=_.current)||void 0===e?void 0:e.items)||[];null==W||W(B(t.map((e=>e.item))),!0)}}:null,{key:"selectInvert",label:f,onClick(){var e;const t=B(((null===(e=_.current)||void 0===e?void 0:e.items)||[]).map((e=>e.item))),n=new Set(l),o=new Set(n);t.forEach((e=>{n.has(e)?o.delete(e):o.add(e)})),null==W||W(Array.from(o),"replace")}}];const se=r.createElement(O.A,{className:`${t}-header-dropdown`,menu:{items:ae},disabled:a},void 0!==P?P:r.createElement(y.A,null));return r.createElement("div",{className:re,style:d},r.createElement("div",{className:`${t}-header`},w?r.createElement(r.Fragment,null,ie,se):null,r.createElement("span",{className:`${t}-header-selected`},((e,t)=>{if(N)return"function"==typeof N?N({selectedCount:e,totalCount:t}):N;const n=t>1?k:j;return r.createElement(r.Fragment,null,(e>0?`${e}/`:"")+t," ",n)})(J.length,U.length)),r.createElement("span",{className:`${t}-header-title`},o)),te,le)};var H=n(26557),W=n(14588),L=n(49103);const M=e=>{const{disabled:t,moveToLeft:n,moveToRight:o,leftArrowText:l="",rightArrowText:i="",leftActive:a,rightActive:s,className:c,style:d,direction:u,oneWay:p}=e;return r.createElement("div",{className:c,style:d},r.createElement(L.Ay,{type:"primary",size:"small",disabled:t||!s,onClick:o,icon:"rtl"!==u?r.createElement(W.A,null):r.createElement(H.A,null)},i),!p&&r.createElement(L.Ay,{type:"primary",size:"small",disabled:t||!a,onClick:n,icon:"rtl"!==u?r.createElement(H.A,null):r.createElement(W.A,null)},l))};var D=n(36891),F=n(25905),K=n(51113);const X=e=>{const{antCls:t,componentCls:n,listHeight:o,controlHeightLG:r}=e,l=`${t}-table`,i=`${t}-input`;return{[`${n}-customize-list`]:{[`${n}-list`]:{flex:"1 1 50%",width:"auto",height:"auto",minHeight:o,minWidth:0},[`${l}-wrapper`]:{[`${l}-small`]:{border:0,borderRadius:0,[`${l}-selection-column`]:{width:r,minWidth:r}},[`${l}-pagination${l}-pagination`]:{margin:0,padding:e.paddingXS}},[`${i}[disabled]`]:{backgroundColor:"transparent"}}}},q=(e,t)=>{const{componentCls:n,colorBorder:o}=e;return{[`${n}-list`]:{borderColor:t,"&-search:not([disabled])":{borderColor:o}}}},V=e=>{const{componentCls:t}=e;return{[`${t}-status-error`]:Object.assign({},q(e,e.colorError)),[`${t}-status-warning`]:Object.assign({},q(e,e.colorWarning))}},_=e=>{const{componentCls:t,colorBorder:n,colorSplit:o,lineWidth:r,itemHeight:l,headerHeight:i,transferHeaderVerticalPadding:a,itemPaddingBlock:s,controlItemBgActive:c,colorTextDisabled:d,colorTextSecondary:u,listHeight:p,listWidth:m,listWidthLG:g,fontSizeIcon:b,marginXS:f,paddingSM:h,lineType:v,antCls:y,iconCls:$,motionDurationSlow:x,controlItemBgHover:O,borderRadiusLG:w,colorBgContainer:S,colorText:C,controlItemBgActiveHover:A}=e,k=(0,D.zA)(e.calc(w).sub(r).equal());return{display:"flex",flexDirection:"column",width:m,height:p,border:`${(0,D.zA)(r)} ${v} ${n}`,borderRadius:e.borderRadiusLG,"&-with-pagination":{width:g,height:"auto"},"&-search":{[`${$}-search`]:{color:d}},"&-header":{display:"flex",flex:"none",alignItems:"center",height:i,padding:`${(0,D.zA)(e.calc(a).sub(r).equal())} ${(0,D.zA)(h)} ${(0,D.zA)(a)}`,color:C,background:S,borderBottom:`${(0,D.zA)(r)} ${v} ${o}`,borderRadius:`${(0,D.zA)(w)} ${(0,D.zA)(w)} 0 0`,"> *:not(:last-child)":{marginInlineEnd:4},"> *":{flex:"none"},"&-title":Object.assign(Object.assign({},F.L9),{flex:"auto",textAlign:"end"}),"&-dropdown":Object.assign(Object.assign({},(0,F.Nk)()),{fontSize:b,transform:"translateY(10%)",cursor:"pointer","&[disabled]":{cursor:"not-allowed"}})},"&-body":{display:"flex",flex:"auto",flexDirection:"column",fontSize:e.fontSize,minHeight:0,"&-search-wrapper":{position:"relative",flex:"none",padding:h}},"&-content":{flex:"auto",margin:0,padding:0,overflow:"auto",listStyle:"none",borderRadius:`0 0 ${k} ${k}`,"&-item":{display:"flex",alignItems:"center",minHeight:l,padding:`${(0,D.zA)(s)} ${(0,D.zA)(h)}`,transition:`all ${x}`,"> *:not(:last-child)":{marginInlineEnd:f},"> *":{flex:"none"},"&-text":Object.assign(Object.assign({},F.L9),{flex:"auto"}),"&-remove":Object.assign(Object.assign({},(0,F.Y1)(e)),{color:n,"&:hover, &:focus":{color:u}}),[`&:not(${t}-list-content-item-disabled)`]:{"&:hover":{backgroundColor:O,cursor:"pointer"},[`&${t}-list-content-item-checked:hover`]:{backgroundColor:A}},"&-checked":{backgroundColor:c},"&-disabled":{color:d,cursor:"not-allowed"}},[`&-show-remove ${t}-list-content-item:not(${t}-list-content-item-disabled):hover`]:{background:"transparent",cursor:"default"}},"&-pagination":{padding:e.paddingXS,textAlign:"end",borderTop:`${(0,D.zA)(r)} ${v} ${o}`,[`${y}-pagination-options`]:{paddingInlineEnd:e.paddingXS}},"&-body-not-found":{flex:"none",width:"100%",margin:"auto 0",color:d,textAlign:"center"},"&-footer":{borderTop:`${(0,D.zA)(r)} ${v} ${o}`},"&-checkbox":{lineHeight:1}}},Y=e=>{const{antCls:t,iconCls:n,componentCls:o,marginXS:r,marginXXS:l,fontSizeIcon:i,colorBgContainerDisabled:a}=e;return{[o]:Object.assign(Object.assign({},(0,F.dF)(e)),{position:"relative",display:"flex",alignItems:"stretch",[`${o}-disabled`]:{[`${o}-list`]:{background:a}},[`${o}-list`]:_(e),[`${o}-operation`]:{display:"flex",flex:"none",flexDirection:"column",alignSelf:"center",margin:`0 ${(0,D.zA)(r)}`,verticalAlign:"middle",gap:l,[`${t}-btn ${n}`]:{fontSize:i}}})}},G=e=>{const{componentCls:t}=e;return{[`${t}-rtl`]:{direction:"rtl"}}},Q=(0,K.OF)("Transfer",(e=>{const t=(0,K.oX)(e);return[Y(t),X(t),V(t),G(t)]}),(e=>{const{fontSize:t,lineHeight:n,controlHeight:o,controlHeightLG:r,lineWidth:l}=e,i=Math.round(t*n);return{listWidth:180,listHeight:200,listWidthLG:250,headerHeight:r,itemHeight:o,itemPaddingBlock:(o-i)/2,transferHeaderVerticalPadding:Math.ceil((r-l-i)/2)}})),U=e=>{const{dataSource:t,targetKeys:n=[],selectedKeys:l,selectAllLabels:y=[],operations:$=[],style:x={},listStyle:O={},locale:w={},titles:S,disabled:C,showSearch:A=!1,operationStyle:k,showSelectAll:E,oneWay:j,pagination:I,status:N,prefixCls:P,className:z,rootClassName:R,selectionsIcon:B,filterOption:H,render:W,footer:L,children:D,rowKey:F,onScroll:K,onChange:X,onSearch:q,onSelectChange:V}=e,{getPrefixCls:_,renderEmpty:Y,direction:G,transfer:U}=(0,r.useContext)(d.QO),Z=_("transfer",P),[J,ee,te]=Q(Z),[ne,oe,re]=((e,t,n)=>{const o=r.useMemo((()=>(e||[]).map((e=>t?Object.assign(Object.assign({},e),{key:t(e)}):e))),[e,t]),[l,i]=r.useMemo((()=>{var e;const t=[],r=Array.from({length:null!==(e=null==n?void 0:n.length)&&void 0!==e?e:0}),l=(0,c.W)(n||[]);return o.forEach((e=>{if(l.has(e.key)){const t=l.get(e.key);r[t]=e}else t.push(e)})),[t,r]}),[o,n]);return[o,l.filter(Boolean),i.filter(Boolean)]})(t,F,n),[le,ie,ae,se]=function(e,t,n){const[l,i]=r.useMemo((()=>[new Set(e.map((e=>null==e?void 0:e.key))),new Set(t.map((e=>null==e?void 0:e.key)))]),[e,t]),[a,s]=(0,b.vz)(f,{value:n}),c=r.useMemo((()=>h(a,l)),[a,l]),d=r.useMemo((()=>h(a,i)),[a,i]);r.useEffect((()=>{s([].concat((0,o.A)(h(a,l)),(0,o.A)(h(a,i))))}),[v(l),v(i)]);const u=(0,b._q)((e=>{s([].concat((0,o.A)(e),(0,o.A)(d)))})),p=(0,b._q)((e=>{s([].concat((0,o.A)(c),(0,o.A)(e)))}));return[c,d,u,p]}(oe,re,l),[ce,de]=(0,a.A)((e=>e.key)),[ue,pe]=(0,a.A)((e=>e.key)),me=(0,r.useCallback)(((e,t)=>{if("left"===e){const e="function"==typeof t?t(le||[]):t;ae(e)}else{const e="function"==typeof t?t(ie||[]):t;se(e)}}),[le,ie]),ge=(e,t)=>{("left"===e?de:pe)(t)},be=(0,r.useCallback)(((e,t)=>{"left"===e?null==V||V(t,ie):null==V||V(le,t)}),[le,ie]),fe=e=>{const t="right"===e?le:ie,o=(0,c.a)(ne),r=t.filter((e=>!o.has(e))),l=(0,c.W)(r),i="right"===e?r.concat(n):n.filter((e=>!l.has(e))),a="right"===e?"left":"right";me(a,[]),be(a,[]),null==X||X(i,e,r)},he=(e,t,n)=>{me(e,(r=>{let l=[];if("replace"===n)l=t;else if(n)l=Array.from(new Set([].concat((0,o.A)(r),(0,o.A)(t))));else{const e=(0,c.W)(t);l=r.filter((t=>!e.has(t)))}return be(e,l),l})),ge(e,null)},ve=(t,n,r,l)=>{const i="left"===t,a=(0,o.A)(i?le:ie),s=new Set(a),c=(0,o.A)(i?oe:re).filter((e=>!(null==e?void 0:e.disabled))),d=c.findIndex((e=>e.key===n));l&&a.length>0?((e,t,n,o)=>{("left"===e?ce:ue)(o,t,n)})(t,c,s,d):((e,t,n,o,r)=>{t.has(n)&&(t.delete(n),ge(e,null)),o&&(t.add(n),ge(e,r))})(t,s,n,r,d);const u=Array.from(s);be(t,u),e.selectedKeys||me(t,u)},ye=e=>"function"==typeof O?O({direction:e}):O||{},$e=(0,r.useContext)(p.$W),{hasFeedback:xe,status:Oe}=$e,we=(0,s.v)(Oe,N),Se=!D&&I,Ce=re.filter((e=>ie.includes(e.key)&&!e.disabled)).length>0,Ae=oe.filter((e=>le.includes(e.key)&&!e.disabled)).length>0,ke=i()(Z,{[`${Z}-disabled`]:C,[`${Z}-customize-list`]:!!D,[`${Z}-rtl`]:"rtl"===G},(0,s.L)(Z,we,xe),null==U?void 0:U.className,z,R,ee,te),[Ee]=(0,m.Ym)("Transfer",g.A.Transfer),je=(Ie=Ee,Object.assign(Object.assign(Object.assign({},Ie),{notFoundContent:(null==Y?void 0:Y("Transfer"))||r.createElement(u.A,{componentName:"Transfer"})}),w));var Ie;const[Ne,Pe]=(e=>{var t;return null!==(t=null!=S?S:e.titles)&&void 0!==t?t:[]})(je),ze=null!=B?B:null==U?void 0:U.selectionsIcon;return J(r.createElement("div",{className:ke,style:Object.assign(Object.assign({},null==U?void 0:U.style),x)},r.createElement(T,Object.assign({prefixCls:`${Z}-list`,titleText:Ne,dataSource:oe,filterOption:H,style:ye("left"),checkedKeys:le,handleFilter:e=>null==q?void 0:q("left",e.target.value),handleClear:()=>null==q?void 0:q("left",""),onItemSelect:(e,t,n)=>{ve("left",e,t,null==n?void 0:n.shiftKey)},onItemSelectAll:(e,t)=>{he("left",e,t)},render:W,showSearch:A,renderList:D,footer:L,onScroll:e=>{null==K||K("left",e)},disabled:C,direction:"rtl"===G?"right":"left",showSelectAll:E,selectAllLabel:y[0],pagination:Se,selectionsIcon:ze},je)),r.createElement(M,{className:`${Z}-operation`,rightActive:Ae,rightArrowText:$[0],moveToRight:()=>{fe("right"),ge("right",null)},leftActive:Ce,leftArrowText:$[1],moveToLeft:()=>{fe("left"),ge("left",null)},style:k,disabled:C,direction:G,oneWay:j}),r.createElement(T,Object.assign({prefixCls:`${Z}-list`,titleText:Pe,dataSource:re,filterOption:H,style:ye("right"),checkedKeys:ie,handleFilter:e=>null==q?void 0:q("right",e.target.value),handleClear:()=>null==q?void 0:q("right",""),onItemSelect:(e,t,n)=>{ve("right",e,t,null==n?void 0:n.shiftKey)},onItemSelectAll:(e,t)=>{he("right",e,t)},onItemRemove:e=>{me("right",[]),null==X||X(n.filter((t=>!e.includes(t))),"left",(0,o.A)(e))},render:W,showSearch:A,renderList:D,footer:L,onScroll:e=>{null==K||K("right",e)},disabled:C,direction:"rtl"===G?"left":"right",showSelectAll:E,selectAllLabel:y[1],showRemove:j,pagination:Se,selectionsIcon:ze},je))))};U.List=T,U.Search=z,U.Operation=M},85166:(e,t,n)=>{n.d(t,{Ay:()=>b,k8:()=>m,bi:()=>g});var o=n(36891),r=n(77391),l=n(25905),i=n(38328),a=n(51113);const s=({treeCls:e,treeNodeCls:t,directoryNodeSelectedBg:n,directoryNodeSelectedColor:o,motionDurationMid:r,borderRadius:l,controlItemBgHover:i})=>({[`${e}${e}-directory ${t}`]:{[`${e}-node-content-wrapper`]:{position:"static",[`> *:not(${e}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${r}`,content:'""',borderRadius:l},"&:hover:before":{background:i}},[`${e}-switcher, ${e}-checkbox, ${e}-draggable-icon`]:{zIndex:1},"&-selected":{[`${e}-switcher, ${e}-draggable-icon`]:{color:o},[`${e}-node-content-wrapper`]:{color:o,background:"transparent","&:before, &:hover:before":{background:n}}}}}),c=new o.Mo("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),d=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),u=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${(0,o.zA)(t.lineWidthBold)} solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),p=(e,t)=>{const{treeCls:n,treeNodeCls:r,treeNodePadding:i,titleHeight:a,indentSize:s,nodeSelectedBg:p,nodeHoverBg:m,colorTextQuaternary:g,controlItemBgActiveDisabled:b}=t;return{[n]:Object.assign(Object.assign({},(0,l.dF)(t)),{background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${n}-rtl ${n}-switcher_close ${n}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${n}-active-focused)`]:Object.assign({},(0,l.jk)(t)),[`${n}-list-holder-inner`]:{alignItems:"flex-start"},[`&${n}-block-node`]:{[`${n}-list-holder-inner`]:{alignItems:"stretch",[`${n}-node-content-wrapper`]:{flex:"auto"},[`${r}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:c,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:t.borderRadius}}},[r]:{display:"flex",alignItems:"flex-start",marginBottom:i,lineHeight:(0,o.zA)(a),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:i},[`&-disabled ${n}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${n}-checkbox-disabled + ${n}-node-selected,&${r}-disabled${r}-selected ${n}-node-content-wrapper`]:{backgroundColor:b},[`${n}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${r}-disabled)`]:{[`${n}-node-content-wrapper`]:{"&:hover":{color:t.nodeHoverColor}}},[`&-active ${n}-node-content-wrapper`]:{background:t.controlItemBgHover},[`&:not(${r}-disabled).filter-node ${n}-title`]:{color:t.colorPrimary,fontWeight:500},"&-draggable":{cursor:"grab",[`${n}-draggable-icon`]:{flexShrink:0,width:a,textAlign:"center",visibility:"visible",color:g},[`&${r}-disabled ${n}-draggable-icon`]:{visibility:"hidden"}}},[`${n}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:s}},[`${n}-draggable-icon`]:{visibility:"hidden"},[`${n}-switcher, ${n}-checkbox`]:{marginInlineEnd:t.calc(t.calc(a).sub(t.controlInteractiveSize)).div(2).equal()},[`${n}-switcher`]:Object.assign(Object.assign({},d(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:a,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${t.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:a,height:a,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:t.borderRadius,transition:`all ${t.motionDurationSlow}`},[`&:not(${n}-switcher-noop):hover:before`]:{backgroundColor:t.colorBgTextHover},[`&_close ${n}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(a).div(2).equal(),bottom:t.calc(i).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:t.calc(t.calc(a).div(2).equal()).mul(.8).equal(),height:t.calc(a).div(2).equal(),borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${n}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:a,paddingBlock:0,paddingInline:t.paddingXS,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},u(e,t)),{"&:hover":{backgroundColor:m},[`&${n}-node-selected`]:{color:t.nodeSelectedColor,backgroundColor:p},[`${n}-iconEle`]:{display:"inline-block",width:a,height:a,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${n}-unselectable ${n}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${r}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${t.colorPrimary}`},"&-show-line":{[`${n}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(a).div(2).equal(),bottom:t.calc(i).mul(-1).equal(),borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${n}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${r}-leaf-last ${n}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${(0,o.zA)(t.calc(a).div(2).equal())} !important`}})}},m=(e,t,n=!0)=>{const o=`.${e}`,r=`${o}-treenode`,l=t.calc(t.paddingXS).div(2).equal(),i=(0,a.oX)(t,{treeCls:o,treeNodeCls:r,treeNodePadding:l});return[p(e,i),n&&s(i)].filter(Boolean)},g=e=>{const{controlHeightSM:t,controlItemBgHover:n,controlItemBgActive:o}=e;return{titleHeight:t,indentSize:t,nodeHoverBg:n,nodeHoverColor:e.colorText,nodeSelectedBg:o,nodeSelectedColor:e.colorText}},b=(0,a.OF)("Tree",((e,{prefixCls:t})=>[{[e.componentCls]:(0,r.gd)(`${t}-checkbox`,e)},m(t,e),(0,i.eG)(e)]),(e=>{const{colorTextLightSolid:t,colorPrimary:n}=e;return Object.assign(Object.assign({},g(e)),{directoryNodeSelectedColor:t,directoryNodeSelectedBg:n})}))},87937:(e,t,n)=>{n.d(t,{A:()=>I});var o=n(1444),r=n(60436),l=n(96540),i=n(41710),a=n(81048),s=n(31380),c=n(46942),d=n.n(c),u=n(84036),p=n(7974),m=n(38674),g=n(48346),b=n(23723),f=n(51113),h=n(85166);const v=function(e){const{dropPosition:t,dropLevelOffset:n,prefixCls:o,indent:r,direction:i="ltr"}=e,a="ltr"===i?"left":"right",s="ltr"===i?"right":"left",c={[a]:-n*r+4,[s]:0};switch(t){case-1:c.top=-3;break;case 1:c.bottom=-3;break;default:c.bottom=-3,c[a]=r+4}return l.createElement("div",{style:c,className:`${o}-drop-indicator`})};var y=n(99373);const $=l.forwardRef(((e,t)=>{var n;const{getPrefixCls:r,direction:i,virtual:a,tree:s}=l.useContext(m.QO),{prefixCls:c,className:u,showIcon:p=!1,showLine:$,switcherIcon:x,switcherLoadingIcon:O,blockNode:w=!1,children:S,checkable:C=!1,selectable:A=!0,draggable:k,motion:E,style:j}=e,I=r("tree",c),N=r(),P=null!=E?E:Object.assign(Object.assign({},(0,b.A)(N)),{motionAppear:!1}),z=Object.assign(Object.assign({},e),{checkable:C,selectable:A,showIcon:p,motion:P,blockNode:w,showLine:Boolean($),dropIndicatorRender:v}),[R,B,T]=(0,h.Ay)(I),[,H]=(0,f.rd)(),W=H.paddingXS/2+((null===(n=H.Tree)||void 0===n?void 0:n.titleHeight)||H.controlHeightSM),L=l.useMemo((()=>{if(!k)return!1;let e={};switch(typeof k){case"function":e.nodeDraggable=k;break;case"object":e=Object.assign({},k)}return!1!==e.icon&&(e.icon=e.icon||l.createElement(g.A,null)),e}),[k]);return R(l.createElement(o.Ay,Object.assign({itemHeight:W,ref:t,virtual:a},z,{style:Object.assign(Object.assign({},null==s?void 0:s.style),j),prefixCls:I,className:d()({[`${I}-icon-hide`]:!p,[`${I}-block-node`]:w,[`${I}-unselectable`]:!A,[`${I}-rtl`]:"rtl"===i},null==s?void 0:s.className,u,B,T),direction:i,checkable:C?l.createElement("span",{className:`${I}-checkbox-inner`}):C,selectable:A,switcherIcon:e=>l.createElement(y.A,{prefixCls:I,switcherIcon:x,switcherLoadingIcon:O,treeNodeProps:e,showLine:$}),draggable:L}),S))})),x=$;function O(e,t,n){const{key:o,children:r}=n;e.forEach((function(e){const l=e[o],i=e[r];!1!==t(l,e)&&O(i||[],t,n)}))}function w(e,t,n){const o=(0,r.A)(t),l=[];return O(e,((e,t)=>{const n=o.indexOf(e);return-1!==n&&(l.push(t),o.splice(n,1)),!!o.length}),(0,p.AZ)(n)),l}var S=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function C(e){const{isLeaf:t,expanded:n}=e;return t?l.createElement(i.A,null):n?l.createElement(a.A,null):l.createElement(s.A,null)}function A({treeData:e,children:t}){return e||(0,p.vH)(t)}const k=(e,t)=>{var{defaultExpandAll:n,defaultExpandParent:o,defaultExpandedKeys:i}=e,a=S(e,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const s=l.useRef(null),c=l.useRef(null),[g,b]=l.useState(a.selectedKeys||a.defaultSelectedKeys||[]),[f,h]=l.useState((()=>(()=>{const{keyEntities:e}=(0,p.cG)(A(a));let t;return t=n?Object.keys(e):o?(0,u.hr)(a.expandedKeys||i||[],e):a.expandedKeys||i||[],t})()));l.useEffect((()=>{"selectedKeys"in a&&b(a.selectedKeys)}),[a.selectedKeys]),l.useEffect((()=>{"expandedKeys"in a&&h(a.expandedKeys)}),[a.expandedKeys]);const{getPrefixCls:v,direction:y}=l.useContext(m.QO),{prefixCls:$,className:k,showIcon:E=!0,expandAction:j="click"}=a,I=S(a,["prefixCls","className","showIcon","expandAction"]),N=v("tree",$),P=d()(`${N}-directory`,{[`${N}-directory-rtl`]:"rtl"===y},k);return l.createElement(x,Object.assign({icon:C,ref:t,blockNode:!0},I,{showIcon:E,expandAction:j,prefixCls:N,className:P,expandedKeys:f,selectedKeys:g,onSelect:(e,t)=>{var n;const{multiple:o,fieldNames:l}=a,{node:i,nativeEvent:d}=t,{key:u=""}=i,m=A(a),g=Object.assign(Object.assign({},t),{selected:!0}),h=(null==d?void 0:d.ctrlKey)||(null==d?void 0:d.metaKey),v=null==d?void 0:d.shiftKey;let y;o&&h?(y=e,s.current=u,c.current=y,g.selectedNodes=w(m,y,l)):o&&v?(y=Array.from(new Set([].concat((0,r.A)(c.current||[]),(0,r.A)(function({treeData:e,expandedKeys:t,startKey:n,endKey:o,fieldNames:r}){const l=[];let i=0;return n&&n===o?[n]:n&&o?(O(e,(e=>{if(2===i)return!1;if(function(e){return e===n||e===o}(e)){if(l.push(e),0===i)i=1;else if(1===i)return i=2,!1}else 1===i&&l.push(e);return t.includes(e)}),(0,p.AZ)(r)),l):[]}({treeData:m,expandedKeys:f,startKey:u,endKey:s.current,fieldNames:l}))))),g.selectedNodes=w(m,y,l)):(y=[u],s.current=u,c.current=y,g.selectedNodes=w(m,y,l)),null===(n=a.onSelect)||void 0===n||n.call(a,y,g),"selectedKeys"in a||b(y)},onExpand:(e,t)=>{var n;return"expandedKeys"in a||h(e),null===(n=a.onExpand)||void 0===n?void 0:n.call(a,e,t)}}))},E=l.forwardRef(k),j=x;j.DirectoryTree=E,j.TreeNode=o.nF;const I=j},99373:(e,t,n)=>{n.d(t,{A:()=>p});var o=n(96540),r=n(22471),l=n(41710),i=n(36962),a=n(33285),s=n(22087),c=n(46942),d=n.n(c),u=n(40682);const p=e=>{const{prefixCls:t,switcherIcon:n,treeNodeProps:c,showLine:p,switcherLoadingIcon:m}=e,{isLeaf:g,expanded:b,loading:f}=c;if(f)return o.isValidElement(m)?m:o.createElement(i.A,{className:`${t}-switcher-loading-icon`});let h;if(p&&"object"==typeof p&&(h=p.showLeafIcon),g){if(!p)return null;if("boolean"!=typeof h&&h){const e="function"==typeof h?h(c):h,n=`${t}-switcher-line-custom-icon`;return o.isValidElement(e)?(0,u.Ob)(e,{className:d()(e.props.className||"",n)}):e}return h?o.createElement(l.A,{className:`${t}-switcher-line-icon`}):o.createElement("span",{className:`${t}-switcher-leaf-line`})}const v=`${t}-switcher-icon`,y="function"==typeof n?n(c):n;return o.isValidElement(y)?(0,u.Ob)(y,{className:d()(y.props.className||"",v)}):void 0!==y?y:p?b?o.createElement(a.A,{className:`${t}-switcher-line-icon`}):o.createElement(s.A,{className:`${t}-switcher-line-icon`}):o.createElement(r.A,{className:v})}}}]);