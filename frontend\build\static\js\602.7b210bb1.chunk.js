"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[602],{1602:(e,t,n)=>{n.r(t),n.d(t,{default:()=>Z});var r,a,o=n(4467),c=n(436),i=n(5544),l=n(7528),s=n(6540),m=n(1468),u=n(5331),d=n(3016),p=n(2395),g=n(2652),v=n(2120),f=n(2702),y=n(7977),E=n(5039),h=n(7142),b=n(8226),S=n(581),C=n(7345),x=n(5850),A=n(9248),w=n(1250),O=n(7749),k=(w.Ay.div(r||(r=(0,l.A)(["\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n"]))),s.createContext({announce:function(){},announcePolite:function(){},announceAssertive:function(){}}));(0,w.DU)(a||(a=(0,l.A)(["\n  /* Base focus styles */\n  :focus {\n    outline: none;\n  }\n\n  /* Focus visible styles for keyboard navigation */\n  :focus-visible {\n    outline: 2px solid ",";\n    outline-offset: 2px;\n    box-shadow: 0 0 0 4px ",";\n  }\n\n  /* Specific focus styles for different elements */\n  a:focus-visible,\n  button:focus-visible,\n  input:focus-visible,\n  textarea:focus-visible,\n  select:focus-visible,\n  [tabindex]:focus-visible {\n    outline: 2px solid ",";\n    outline-offset: 2px;\n    box-shadow: 0 0 0 4px ",";\n  }\n\n  /* High contrast mode focus styles */\n  @media (forced-colors: active) {\n    :focus-visible {\n      outline: 3px solid CanvasText;\n      outline-offset: 3px;\n    }\n  }\n"])),(function(e){var t,n,r;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.primary?e.theme.colorPalette.primary:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.primary)&&void 0!==n&&n.main?e.theme.colors.primary.main:null!==(r=e.theme)&&void 0!==r&&r.primaryColor?e.theme.primaryColor:"#2563EB"}),(function(e){var t,n;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.primaryLight?e.theme.colorPalette.primaryLight:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.primary)&&void 0!==n&&n.light?e.theme.colors.primary.light:"rgba(37, 99, 235, 0.2)"}),(function(e){var t,n,r;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.primary?e.theme.colorPalette.primary:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.primary)&&void 0!==n&&n.main?e.theme.colors.primary.main:null!==(r=e.theme)&&void 0!==r&&r.primaryColor?e.theme.primaryColor:"#2563EB"}),(function(e){var t,n;return null!==(t=e.theme)&&void 0!==t&&null!==(t=t.colorPalette)&&void 0!==t&&t.primaryLight?e.theme.colorPalette.primaryLight:null!==(n=e.theme)&&void 0!==n&&null!==(n=n.colors)&&void 0!==n&&null!==(n=n.primary)&&void 0!==n&&n.light?e.theme.colors.primary.light:"rgba(37, 99, 235, 0.2)"}));var N,I=n(8168),D=n(3986),P=["children","as"],R=w.Ay.span(N||(N=(0,l.A)(["\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n"])));const T=function(e){var t=e.children,n=e.as,r=void 0===n?"span":n,a=(0,D.A)(e,P);return s.createElement(R,(0,I.A)({as:r},a),t)};n(6413);var W,L,M,j,B,G,F=n(6390),z=n(7177);function J(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function U(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?J(Object(n),!0).forEach((function(t){(0,o.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):J(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var K=d.A.Paragraph,H=d.A.Text,Q=p.A.TabPane,$=w.Ay.div(W||(W=(0,l.A)(["\n  height: 300px;\n  overflow-y: auto;\n  margin-bottom: 16px;\n  border: 1px solid ",";\n  padding: 8px;\n  border-radius: ",";\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.border)||"#D1D5DB"}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.borderRadius)||void 0===t?void 0:t.md)||"4px"})),_=(0,w.Ay)(g.A.Item)(L||(L=(0,l.A)(["\n  padding: 8px;\n  background-color: ",";\n  margin-bottom: 8px;\n  border-radius: ",";\n"])),(function(e){var t,n,r,a;switch(e.messageType){case"sent":return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.infoLight)||"#DBEAFE";case"received":return(null===(n=e.theme)||void 0===n||null===(n=n.colorPalette)||void 0===n?void 0:n.successLight)||"#D1FAE5";case"error":return(null===(r=e.theme)||void 0===r||null===(r=r.colorPalette)||void 0===r?void 0:r.errorLight)||"#FEE2E2";default:return(null===(a=e.theme)||void 0===a||null===(a=a.colorPalette)||void 0===a?void 0:a.warningLight)||"#FEF3C7"}}),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.borderRadius)||void 0===t?void 0:t.md)||"4px"})),q=w.Ay.div(M||(M=(0,l.A)(["\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 4px;\n"]))),V=w.Ay.pre(j||(j=(0,l.A)(["\n  margin: 0;\n  white-space: pre-wrap;\n  word-break: break-word;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n"]))),X=w.Ay.div(B||(B=(0,l.A)(["\n  text-align: center;\n  color: ",";\n  margin-top: 120px;\n"])),(function(e){var t;return(null===(t=e.theme)||void 0===t||null===(t=t.colorPalette)||void 0===t?void 0:t.textSecondary)||"#4B5563"})),Y=w.Ay.div(G||(G=(0,l.A)(["\n  margin-bottom: 16px;\n"])));const Z=function(){var e=(0,m.wA)(),t=s.useContext(k).announce,n=(0,s.useState)((0,F.$0)("test")),r=(0,i.A)(n,2),a=r[0],o=r[1],l=(0,s.useState)(!1),d=(0,i.A)(l,2),w=(d[0],d[1]),N=(0,s.useState)(z.ConnectionState.CLOSED),I=(0,i.A)(N,2),D=I[0],P=I[1],R=(0,s.useState)([]),W=(0,i.A)(R,2),L=W[0],M=W[1],j=(0,s.useState)(""),B=(0,i.A)(j,2),G=B[0],J=B[1],Z=(0,s.useState)("1"),ee=(0,i.A)(Z,2),te=ee[0],ne=ee[1],re=(0,s.useState)(!1),ae=(0,i.A)(re,2),oe=ae[0],ce=ae[1],ie=(0,s.useRef)(null),le=(0,s.useState)({autoReconnect:!0,debug:!0,batchInterval:50,maxBatchSize:100,heartbeatInterval:3e4,enableCompression:!0,persistOfflineMessages:!0,reconnectInterval:3e3,maxReconnectAttempts:10}),se=(0,i.A)(le,2),me=se[0],ue=se[1];(0,s.useEffect)((function(){return e((0,u.tI)("websocket")),function(){ie.current&&(ie.current.destroy(),ie.current=null)}}),[e]);var de=function(){if(ie.current&&ie.current.connectionState===z.ConnectionState.OPEN&&G.trim())try{var e;try{e=JSON.parse(G)}catch(t){e=G}ie.current.send(e),M((function(t){return[].concat((0,c.A)(t),[{type:"sent",text:"string"==typeof e?e:JSON.stringify(e,null,2),timestamp:new Date}])})),J(""),t("Message sent")}catch(e){t("Failed to send message: ".concat(e.message),"assertive"),console.error("Error sending message:",e)}else t("Cannot send message: WebSocket is not connected","assertive")};return s.createElement(O.mc,null,s.createElement(O.sT,{level:2},s.createElement(b.A,{style:{marginRight:"8px"}}),"WebSocket Manager"),s.createElement(p.A,{activeKey:te,onChange:ne},s.createElement(Q,{tab:"Messages",key:"1"},s.createElement(O.ee,{title:s.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},s.createElement("span",null,"Enhanced WebSocket Connection"),s.createElement(v.A,{status:D===z.ConnectionState.OPEN?"success":D===z.ConnectionState.CONNECTING?"processing":D===z.ConnectionState.RECONNECTING||D===z.ConnectionState.CLOSING?"warning":"error",text:D===z.ConnectionState.OPEN?"Connected":D===z.ConnectionState.CONNECTING?"Connecting":D===z.ConnectionState.RECONNECTING?"Reconnecting":D===z.ConnectionState.CLOSING?"Closing":"Disconnected"}),s.createElement(T,null,D===z.ConnectionState.OPEN?"WebSocket is connected":D===z.ConnectionState.CONNECTING?"WebSocket is connecting":D===z.ConnectionState.RECONNECTING?"WebSocket is reconnecting":D===z.ConnectionState.CLOSING?"WebSocket is closing":"WebSocket is disconnected")),extra:s.createElement(f.A,null,s.createElement(O.jn,{type:"primary",onClick:function(){ie.current&&ie.current.destroy(),M((function(e){return[].concat((0,c.A)(e),[{type:"system",text:"Attempting to connect to ".concat(a),timestamp:new Date}])})),t("Attempting to connect to ".concat(a)),ce(!0);try{ie.current=new z.default({url:a,autoConnect:!1,debug:me.debug,autoReconnect:me.autoReconnect,reconnectInterval:me.reconnectInterval,maxReconnectAttempts:me.maxReconnectAttempts,batchInterval:me.batchInterval,maxBatchSize:me.maxBatchSize,heartbeatInterval:me.heartbeatInterval,enableCompression:me.enableCompression,persistOfflineMessages:me.persistOfflineMessages}),ie.current.addEventListener("open",(function(e){w(!0),P(z.ConnectionState.OPEN),ce(!1);var n="Connected to WebSocket server at ".concat(a);M((function(e){return[].concat((0,c.A)(e),[{type:"system",text:n,timestamp:new Date}])})),t("WebSocket connection established"),setTimeout((function(){if(ie.current&&ie.current.connectionState===z.ConnectionState.OPEN)try{var e={type:"ping",timestamp:(new Date).toISOString(),client:"EnhancedWebSocketPage"};ie.current.send(e,!0,{urgent:!0}),M((function(t){return[].concat((0,c.A)(t),[{type:"sent",text:JSON.stringify(e,null,2),timestamp:new Date,isTest:!0}])})),t("Sent automatic test message")}catch(e){console.error("Failed to send test message:",e)}}),1e3)})),ie.current.addEventListener("message",(function(e){try{var n,r=e.data;n="string"==typeof r?r:JSON.stringify(r,null,2),M((function(t){var r;return[].concat((0,c.A)(t),[{type:"received",text:n,timestamp:new Date,raw:null===(r=e.originalEvent)||void 0===r?void 0:r.data}])})),t("Message received from server")}catch(n){M((function(t){return[].concat((0,c.A)(t),[{type:"received",text:String(e.data),timestamp:new Date,parseError:n.message}])})),t("Message received but could not be processed")}})),ie.current.addEventListener("close",(function(e){w(!1),P(z.ConnectionState.CLOSED),ce(!1);var n="Disconnected from server: ".concat(e.reason||"Connection closed"," (code: ").concat(e.code,")");M((function(t){return[].concat((0,c.A)(t),[{type:"system",text:n,timestamp:new Date,wasClean:e.wasClean,code:e.code}])})),t("WebSocket connection closed")})),ie.current.addEventListener("error",(function(e){ce(!1);var n="WebSocket error: ".concat(e.message||"Unknown error");M((function(t){return[].concat((0,c.A)(t),[{type:"error",text:n,timestamp:new Date,error:e}])})),t("WebSocket error occurred","assertive"),console.error("WebSocket connection error:",{url:a,error:e,connectionState:e.connectionState,timestamp:(new Date).toISOString()})})),ie.current.addEventListener("reconnect_attempt",(function(e){P(z.ConnectionState.RECONNECTING);var n="Reconnecting to WebSocket server (attempt ".concat(e.attempt,")...");M((function(e){return[].concat((0,c.A)(e),[{type:"system",text:n,timestamp:new Date}])})),t(n)})),ie.current.open(),P(z.ConnectionState.CONNECTING)}catch(e){ce(!1),M((function(t){return[].concat((0,c.A)(t),[{type:"error",text:"Failed to connect: ".concat(e.message),timestamp:new Date,stack:e.stack}])})),t("Connection error: ".concat(e.message)),console.error("Failed to create WebSocket:",e)}},disabled:D!==z.ConnectionState.CLOSED,loading:oe,icon:s.createElement(S.A,null),"aria-label":"Connect to WebSocket"},"Connect"),s.createElement(O.tA,{danger:!0,onClick:function(){ie.current&&(ie.current.close(),P(z.ConnectionState.CLOSING),t("Disconnecting from WebSocket server"))},disabled:D===z.ConnectionState.CLOSED||D===z.ConnectionState.CLOSING,"aria-label":"Disconnect from WebSocket"},"Disconnect"),s.createElement(O.K0,{onClick:function(){M([]),t("Messages cleared")},icon:s.createElement(C.A,null),"aria-label":"Clear all messages"},"Clear"))},s.createElement(O.gE,null,s.createElement(O.sQ,{value:a,onChange:function(e){return o(e.target.value)},placeholder:"WebSocket URL",addonBefore:"URL"})),s.createElement($,null,0===L.length?s.createElement(X,null,s.createElement(H,{type:"secondary"},"No messages yet")):s.createElement(g.A,{dataSource:L,"aria-label":"WebSocket messages",renderItem:function(e){return s.createElement(_,{messageType:e.type},s.createElement("div",{style:{width:"100%"}},s.createElement(q,null,s.createElement(H,{strong:!0},"sent"===e.type?"Sent":"received"===e.type?"Received":"error"===e.type?"Error":"System"),s.createElement(H,{type:"secondary"},(t=e.timestamp,new Date(t).toLocaleTimeString()))),s.createElement(V,null,e.text)));var t}})),s.createElement(O.gE,null,s.createElement(O.aQ,{value:G,onChange:function(e){return J(e.target.value)},placeholder:"Enter message to send (JSON or plain text)",rows:4,disabled:D!==z.ConnectionState.OPEN,onPressEnter:function(e){e.shiftKey||(e.preventDefault(),de())}})),s.createElement(f.A,null,s.createElement(O.jn,{type:"primary",onClick:de,disabled:D!==z.ConnectionState.OPEN||!G.trim(),icon:s.createElement(x.A,null),"aria-label":"Send message"},"Send Message"),s.createElement(y.A,{title:"Send a test ping message"},s.createElement(O.tA,{onClick:function(){if(ie.current&&ie.current.connectionState===z.ConnectionState.OPEN)try{var e={type:"ping",timestamp:(new Date).toISOString(),client:"EnhancedWebSocketPage"};ie.current.send(e,!0,{urgent:!0}),M((function(t){return[].concat((0,c.A)(t),[{type:"sent",text:JSON.stringify(e,null,2),timestamp:new Date,isTest:!0}])})),t("Sent test ping message")}catch(e){console.error("Failed to send test message:",e)}},disabled:D!==z.ConnectionState.OPEN,icon:s.createElement(A.A,null),"aria-label":"Send test ping message"},"Send Ping"))))),s.createElement(Q,{tab:"Settings",key:"2"},s.createElement(O.ee,{title:"Enhanced WebSocket Settings"},s.createElement(Y,null,s.createElement(H,{strong:!0},"Auto Reconnect"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(E.A,{checked:me.autoReconnect,onChange:function(e){return ue(U(U({},me),{},{autoReconnect:e}))},style:{marginRight:"8px"}}),s.createElement(H,{type:"secondary"},me.autoReconnect?"Enabled - Will automatically try to reconnect when disconnected":"Disabled - Will not automatically reconnect"))),s.createElement(Y,null,s.createElement(H,{strong:!0},"Debug Mode"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(E.A,{checked:me.debug,onChange:function(e){return ue(U(U({},me),{},{debug:e}))},style:{marginRight:"8px"}}),s.createElement(H,{type:"secondary"},me.debug?"Enabled - Detailed logs will be shown in the console":"Disabled - Minimal logging"))),s.createElement(Y,null,s.createElement(H,{strong:!0},"Persist Offline Messages"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(E.A,{checked:me.persistOfflineMessages,onChange:function(e){return ue(U(U({},me),{},{persistOfflineMessages:e}))},style:{marginRight:"8px"}}),s.createElement(H,{type:"secondary"},me.persistOfflineMessages?"Enabled - Messages will be saved when offline":"Disabled - Messages may be lost when offline"))),s.createElement(Y,null,s.createElement(H,{strong:!0},"Enable Compression"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(E.A,{checked:me.enableCompression,onChange:function(e){return ue(U(U({},me),{},{enableCompression:e}))},style:{marginRight:"8px"}}),s.createElement(H,{type:"secondary"},me.enableCompression?"Enabled - Large messages will be compressed":"Disabled - No compression"))),s.createElement(Y,null,s.createElement(H,{strong:!0},"Reconnect Interval (ms)"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(h.A,{min:1e3,max:1e4,step:500,value:me.reconnectInterval,onChange:function(e){return ue(U(U({},me),{},{reconnectInterval:e}))},style:{marginRight:"8px"}}),s.createElement(H,{type:"secondary"},"Time to wait before attempting to reconnect"))),s.createElement(Y,null,s.createElement(H,{strong:!0},"Max Reconnect Attempts"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(h.A,{min:1,max:50,value:me.maxReconnectAttempts,onChange:function(e){return ue(U(U({},me),{},{maxReconnectAttempts:e}))},style:{marginRight:"8px"}}),s.createElement(H,{type:"secondary"},"Maximum number of reconnection attempts"))),s.createElement(Y,null,s.createElement(H,{strong:!0},"Batch Interval (ms)"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(h.A,{min:0,max:500,value:me.batchInterval,onChange:function(e){return ue(U(U({},me),{},{batchInterval:e}))},style:{marginRight:"8px"}}),s.createElement(H,{type:"secondary"},"Time to wait before sending batched messages (0 to disable batching)"))),s.createElement(Y,null,s.createElement(H,{strong:!0},"Max Batch Size"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(h.A,{min:1,max:1e3,value:me.maxBatchSize,onChange:function(e){return ue(U(U({},me),{},{maxBatchSize:e}))},style:{marginRight:"8px"}}),s.createElement(H,{type:"secondary"},"Maximum number of messages in a batch"))),s.createElement(Y,null,s.createElement(H,{strong:!0},"Heartbeat Interval (ms)"),s.createElement("div",{style:{display:"flex",alignItems:"center",marginTop:"8px"}},s.createElement(h.A,{min:5e3,max:6e4,step:5e3,value:me.heartbeatInterval,onChange:function(e){return ue(U(U({},me),{},{heartbeatInterval:e}))},style:{marginRight:"8px"}}),s.createElement(H,{type:"secondary"},"Time between heartbeat messages to keep the connection alive"))),s.createElement(K,null,s.createElement(H,{type:"secondary"},"These settings control the behavior of the Enhanced WebSocket client. Changes will take effect on the next connection."))))))}}}]);