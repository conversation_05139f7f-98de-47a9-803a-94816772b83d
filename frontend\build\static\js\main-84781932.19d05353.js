"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[5185],{4318:(e,t,n)=>{n.d(t,{AS:()=>d,D:()=>b,H2:()=>u,Kg:()=>r,Pe:()=>v,RH:()=>c,Te:()=>a,U_:()=>_,WD:()=>p,YG:()=>s,ZH:()=>l,_E:()=>C,co:()=>o,ei:()=>m,gV:()=>E,gk:()=>S,oz:()=>g,uV:()=>i,vs:()=>f,wH:()=>y,xS:()=>h});var r="WEBSOCKET_CONNECTED",o="WEBSOCKET_DISCONNECTED",s="WS_CONNECT",a="WS_CONNECTED",i="WS_DISCONNECT",c="WS_DISCONNECTED",u="WS_MESSAGE",l="WS_MESSAGE_RECEIVED",p="WS_SEND_MESSAGE",d="WS_ERROR",g="ADD_COMPONENT",m="UPDATE_COMPONENT",h="REMOVE_COMPONENT",f="ADD_LAYOUT",v="UPDATE_LAYOUT",E="REMOVE_LAYOUT",_="ADD_THEME",S="UPDATE_THEME",b="REMOVE_THEME",y="SET_ACTIVE_THEME",C="TOGGLE_AUTO_APPLY_THEME"},11606:(e,t,n)=>{n.d(t,{A:()=>p});var r=n(10467),o=n(23029),s=n(92901),a=n(54756),i=n.n(a),c=n(84447),u="app_builder_auth_token",l="app_builder_user";const p=new(function(){return(0,s.A)((function e(){(0,o.A)(this,e),this.token=localStorage.getItem(u),this.user=JSON.parse(localStorage.getItem(l)||"null"),this.listeners=[],this.initInterceptors()}),[{key:"initInterceptors",value:function(){var e=this;c.Ay.interceptors.request.use((function(t){return e.token&&(t.headers.Authorization="Bearer ".concat(e.token)),t}),(function(e){return Promise.reject(e)})),c.Ay.interceptors.response.use((function(e){return e}),(function(t){return t.response&&401===t.response.status&&e.logout(),Promise.reject(t)}))}},{key:"login",value:(t=(0,r.A)(i().mark((function e(t,n){var r,o,s,a;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,c.Ay.post("/api/auth/login",{email:t,password:n});case 3:return r=e.sent,o=r.data,s=o.token,a=o.user,this.setToken(s),this.setUser(a),this.notifyListeners("login",a),e.abrupt("return",a);case 11:throw e.prev=11,e.t0=e.catch(0),console.error("Login error:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,this,[[0,11]])}))),function(e,n){return t.apply(this,arguments)})},{key:"register",value:(e=(0,r.A)(i().mark((function e(t,n,r){var o,s,a,u;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,c.Ay.post("/api/auth/register",{name:t,email:n,password:r});case 3:return o=e.sent,s=o.data,a=s.token,u=s.user,this.setToken(a),this.setUser(u),this.notifyListeners("register",u),e.abrupt("return",u);case 11:throw e.prev=11,e.t0=e.catch(0),console.error("Register error:",e.t0),e.t0;case 15:case"end":return e.stop()}}),e,this,[[0,11]])}))),function(t,n,r){return e.apply(this,arguments)})},{key:"logout",value:function(){this.setToken(null),this.setUser(null),this.notifyListeners("logout")}},{key:"getUser",value:function(){return this.user}},{key:"isAuthenticated",value:function(){return!!this.token}},{key:"setToken",value:function(e){this.token=e,e?localStorage.setItem(u,e):localStorage.removeItem(u)}},{key:"setUser",value:function(e){this.user=e,e?localStorage.setItem(l,JSON.stringify(e)):localStorage.removeItem(l)}},{key:"addListener",value:function(e){var t=this;return this.listeners.push(e),function(){t.listeners=t.listeners.filter((function(t){return t!==e}))}}},{key:"notifyListeners",value:function(e,t){this.listeners.forEach((function(n){try{n(e,t)}catch(e){console.error("Error in auth listener:",e)}}))}}]);var e,t}())},17053:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(60436),o=n(82284),s=n(64467),a=n(23029),i=n(92901);function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){(0,s.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const l=new(function(){function e(){(0,a.A)(this,e),this.socket=null,this.connected=!1,this.connecting=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=10,this.reconnectInterval=2e3,this.maxReconnectInterval=3e4,this.reconnectDecay=1.5,this.eventListeners={},this.offlineQueue=[],this.isReconnecting=!1,this.isClosing=!1,this.isSuspended=!1,this.messageQueue=[],this.lastError=null,this.heartbeatInterval=3e4,this.heartbeatTimeoutId=null,this.missedHeartbeats=0,this.maxMissedHeartbeats=3,this.connectionTimeoutId=null,this.debug=!1,this.instance=null}return(0,i.A)(e,[{key:"init",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.options=u({autoConnect:!1,autoReconnect:!0,reconnectInterval:2e3,maxReconnectAttempts:10,debug:!1,queueOfflineMessages:!0},e),this.connected=!1,this.connecting=!1,this.debug=this.options.debug,this.options.autoConnect&&this.connect(),this}},{key:"setReconnectOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.maxReconnectAttempts=e.maxAttempts||this.maxReconnectAttempts,this.reconnectInterval=e.initialDelay||this.reconnectInterval,this.maxReconnectInterval=e.maxDelay||this.maxReconnectInterval,this.reconnectDecay=e.useExponentialBackoff?1.5:1,this}},{key:"configureSecurityOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.securityOptions=u({validateMessages:!0,sanitizeMessages:!0,rateLimiting:{enabled:!0,maxMessagesPerSecond:20,burstSize:50}},e),this}},{key:"configurePerformanceOptions",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.performanceOptions=u({compression:{enabled:!0,threshold:1024,level:6},batchingEnabled:!0,batchInterval:50,maxBatchSize:20,offlineQueueEnabled:!0,offlineStorage:{enabled:!0,persistKey:"websocket_offline_queue"}},e),this}},{key:"isConnected",value:function(){try{return null!==this.socket&&void 0!==this.socket&&this.socket.readyState===WebSocket.OPEN}catch(e){return console.error("Error checking connection status:",e),!1}}},{key:"connect",value:function(e){var t=this;return new Promise((function(n,r){if(t.isConnected())n(t);else if(t.connecting)r(new Error("Connection already in progress"));else{var o;t.connecting=!0,e&&(t.endpoint=e),o=t.url?t.url:"ws://localhost:8765",t.debug&&console.log("Connecting to WebSocket at ".concat(o));try{t.socket=new WebSocket(o),t.socket.onopen=function(e){t.connected=!0,t.connecting=!1,t.reconnectAttempts=0,t.debug&&console.log("WebSocket connection established"),t.startHeartbeat(),t.processOfflineQueue(),t.triggerEvent("open",e),t.triggerEvent("connect",e),n(t)},t.socket.onclose=function(e){t.connected=!1,t.connecting=!1,t.debug&&console.log("WebSocket connection closed: ".concat(e.code," ").concat(e.reason)),t.stopHeartbeat(),t.triggerEvent("close",e),t.triggerEvent("disconnect",e),!t.isClosing&&t.options.autoReconnect&&t.reconnect(),t.isClosing=!1},t.socket.onerror=function(e){console.error("WebSocket error occurred:",e),t.debug&&(console.log("WebSocket readyState:",t.socket.readyState),console.log("Connection URL:",o),console.log("Browser:",navigator.userAgent));var n=t._handleConnectionError(e,t.reconnectAttempts);t.connecting&&(t.connecting=!1,r(n)),t.triggerEvent("error",{originalEvent:e,url:o,timestamp:Date.now(),reconnectAttempts:t.reconnectAttempts,error:n})},t.socket.onmessage=function(e){var n=e.data;try{if("string"==typeof n&&n.startsWith("C")){var r=n.substring(1);n=t._decompressMessage(r)}if("string"==typeof n&&(n=JSON.parse(n)),t.debug&&console.log("WebSocket message received:",n),"pong"===n.type)return t.missedHeartbeats=0,void t.triggerEvent("pong",n);if("batch"===n.type&&Array.isArray(n.messages))return n.messages.forEach((function(e){t.triggerEvent("message",e),e.type&&t.triggerEvent(e.type,e)})),void t.triggerEvent("batch",n);t.triggerEvent("message",n),n.type&&t.triggerEvent(n.type,n)}catch(n){t._handleMessageError(n,e.data)}}}catch(e){t.connecting=!1,t.lastError=e,t.debug&&console.error("Error creating WebSocket:",e),r(e)}}}))}},{key:"disconnect",value:function(){if(this.socket){this.isClosing=!0;try{this.stopHeartbeat(),this.socket.close(1e3,"Normal closure"),this.debug&&console.log("WebSocket disconnected")}catch(e){console.error("Error disconnecting WebSocket:",e)}}}},{key:"close",value:function(){return this.disconnect()}},{key:"reconnect",value:function(){var e=this;if(!this.isReconnecting){if(this.isReconnecting=!0,this.reconnectAttempts>=this.maxReconnectAttempts)return this.debug&&console.log("Maximum reconnect attempts (".concat(this.maxReconnectAttempts,") reached")),this.triggerEvent("max_retries_reached",{attempts:this.reconnectAttempts,max:this.maxReconnectAttempts}),void(this.isReconnecting=!1);this.reconnectAttempts++;var t=this.reconnectInterval*Math.pow(this.reconnectDecay,this.reconnectAttempts-1),n=.8*(t=Math.min(t,this.maxReconnectInterval)),r=1.2*t;t=Math.floor(n+Math.random()*(r-n)),this.debug&&console.log("Reconnecting in ".concat(t,"ms (attempt ").concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,")")),this.triggerEvent("reconnecting",{attempt:this.reconnectAttempts,maxAttempts:this.maxReconnectAttempts,delay:t}),setTimeout((function(){e.isReconnecting=!1,e.connect().catch((function(e){console.error("Reconnection failed:",e)}))}),t)}}},{key:"suspend",value:function(){if(!this.isSuspended)return this.isSuspended=!0,this.debug&&console.log("WebSocket connection suspended"),this.wasPreviouslyConnected=this.isConnected(),this.socket&&(this.isClosing=!0,this.stopHeartbeat(),this.socket.close(1e3,"Connection suspended")),this.triggerEvent("suspended",{timestamp:Date.now(),wasPreviouslyConnected:this.wasPreviouslyConnected}),this}},{key:"resume",value:function(){if(this.isSuspended)return this.isSuspended=!1,this.debug&&console.log("Resuming WebSocket connection"),this.wasPreviouslyConnected&&this.reconnect(),this.triggerEvent("resumed",{timestamp:Date.now(),reconnecting:this.wasPreviouslyConnected}),this}},{key:"handleOnline",value:function(){return this.debug&&console.log("Network is online"),this.wasPreviouslyConnected&&this.reconnect(),this.triggerEvent("network_status_change",{status:"online",timestamp:Date.now()}),this}},{key:"handleOffline",value:function(){return this.debug&&console.log("Network is offline"),this.wasPreviouslyConnected=this.isConnected(),this.triggerEvent("network_status_change",{status:"offline",timestamp:Date.now()}),this}},{key:"send",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(r,s){if(t.isConnected())try{var a,i,c,l,p,d;if(null!==(a=t.securityOptions)&&void 0!==a&&null!==(a=a.rateLimiting)&&void 0!==a&&a.enabled){var g=Date.now();t._rateLimitState||(t._rateLimitState={messageCount:0,windowStart:g,burstCount:0,burstStart:g,queue:[]});var m=t._rateLimitState,h=t.securityOptions.rateLimiting,f=h.maxMessagesPerSecond,v=h.burstSize;if(g-m.windowStart>1e3&&(m.messageCount=0,m.windowStart=g),g-m.burstStart>100&&(m.burstCount=0,m.burstStart=g),m.messageCount>=f||m.burstCount>=v)return n.important?m.queue.unshift({message:e,options:n,timestamp:g}):m.queue.push({message:e,options:n,timestamp:g}),t.triggerEvent("rate_limited",{queueLength:m.queue.length,messageCount:m.messageCount,burstCount:m.burstCount}),m.processingQueue||(m.processingQueue=!0,setTimeout((function(){return t._processRateLimitQueue()}),100)),void r({queued:!0,rateLimited:!0});m.messageCount++,m.burstCount++}var E,_=e;if("object"!==(0,o.A)(_)||_.timestamp||(_=u(u({},_),{},{timestamp:Date.now()})),"object"!==(0,o.A)(_)||_.id||(_=u(u({},_),{},{id:t._generateMessageId()})),null!==(i=t.securityOptions)&&void 0!==i&&i.validateMessages){var S=t._validateMessage(_);if(!S.valid)return void s(new Error("Invalid message: ".concat(S.reason)))}if(null!==(c=t.securityOptions)&&void 0!==c&&c.sanitizeMessages&&(_=t._sanitizeMessage(_)),null!==(l=t.securityOptions)&&void 0!==l&&l.authToken&&(_=u(u({},_),{},{auth:t.securityOptions.authToken})),null!==(p=t.performanceOptions)&&void 0!==p&&p.batchingEnabled&&!n.immediate)return t._addToBatch(_,n),void r({queued:!0,batched:!0});var b=!1;if(null!==(d=t.performanceOptions)&&void 0!==d&&null!==(d=d.compression)&&void 0!==d&&d.enabled&&(n.compress||t.performanceOptions.compression.enabled)){var y="string"==typeof _?_:JSON.stringify(_);if(y.length>t.performanceOptions.compression.threshold)try{E=t._compressMessage(y),b=!0,E="C".concat(E)}catch(e){console.error("Error compressing message:",e),E=y}else E=y}else E="string"==typeof _?_:JSON.stringify(_);t.socket.send(E),t.debug&&console.log("WebSocket message sent".concat(b?" (compressed)":"",":"),_),t.triggerEvent("message_sent",{message:_,compressed:b,size:E.length}),r({sent:!0,compressed:b})}catch(e){console.error("Error sending WebSocket message:",e),s(e)}else t.options.queueOfflineMessages?(t.queueMessage(e,n),r({queued:!0})):s(new Error("WebSocket is not connected"))}))}},{key:"_generateMessageId",value:function(){return"".concat(Date.now(),"-").concat(Math.random().toString(36).substring(2,11))}},{key:"_validateMessage",value:function(e){return e?"object"!==(0,o.A)(e)?{valid:!1,reason:"Message must be an object"}:e.type?{valid:!0}:{valid:!1,reason:"Message must have a type"}:{valid:!1,reason:"Message is empty"}}},{key:"_sanitizeMessage",value:function(e){var t=JSON.parse(JSON.stringify(e)),n=function(e){return"string"==typeof e?e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/\//g,"&#x2F;"):e},r=function(e){if(!e||"object"!==(0,o.A)(e))return e;if(Array.isArray(e))return e.map((function(e){return r(e)}));var t={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){var a=e[s];"object"===(0,o.A)(a)&&null!==a?t[s]=r(a):t[s]=n(a)}return t};return r(t)}},{key:"_compressMessage",value:function(e){try{return btoa(e)}catch(t){return console.error("Error compressing message:",t),e}}},{key:"_decompressMessage",value:function(e){try{return atob(e)}catch(t){return console.error("Error decompressing message:",t),e}}},{key:"_addToBatch",value:function(e,t){var n=this;this._batch||(this._batch={messages:[],timer:null}),this._batch.messages.push({message:e,options:t}),this._batch.timer||(this._batch.timer=setTimeout((function(){n._sendBatch()}),this.performanceOptions.batchInterval)),this._batch.messages.length>=this.performanceOptions.maxBatchSize&&(clearTimeout(this._batch.timer),this._batch.timer=null,this._sendBatch())}},{key:"_sendBatch",value:function(){var e=this;if(this._batch&&0!==this._batch.messages.length){var t={type:"batch",messages:this._batch.messages.map((function(e){return e.message})),timestamp:Date.now(),count:this._batch.messages.length},n=this._batch.messages;this._batch.messages=[],this._batch.timer=null,this.send(t,{immediate:!0}).catch((function(t){console.error("Error sending batch:",t),n.forEach((function(t){e.queueMessage(t.message,t.options)}))}))}}},{key:"_processRateLimitQueue",value:function(){var e=this;if(this._rateLimitState&&0!==this._rateLimitState.queue.length){var t=Date.now(),n=this._rateLimitState,r=this.securityOptions.rateLimiting.maxMessagesPerSecond;if(t-n.windowStart>1e3&&(n.messageCount=0,n.windowStart=t),n.messageCount<r){var o=n.queue.shift();this.send(o.message,u(u({},o.options),{},{immediate:!0})).catch((function(e){console.error("Error sending queued message:",e)})),n.messageCount++,n.queue.length>0?setTimeout((function(){return e._processRateLimitQueue()}),50):n.processingQueue=!1}else setTimeout((function(){return e._processRateLimitQueue()}),100)}else this._rateLimitState&&(this._rateLimitState.processingQueue=!1)}},{key:"sendMessage",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.send(e,t)}},{key:"queueMessage",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.debug&&console.log("Queuing message for later delivery:",e),this.offlineQueue.push({message:e,options:t}),this.triggerEvent("message_queued_offline",{message:e,queueLength:this.offlineQueue.length}),this.offlineQueue.length}},{key:"processOfflineQueue",value:function(){var e=this;if(0!==this.offlineQueue.length){this.debug&&console.log("Processing offline queue (".concat(this.offlineQueue.length," messages)"));var t=(0,r.A)(this.offlineQueue);this.offlineQueue=[],t.forEach((function(t){var n=t.message,r=t.options;e.send(n,r).catch((function(e){console.error("Error sending queued message:",e)}))})),this.triggerEvent("offline_queue_processed",{processedCount:t.length})}}},{key:"startHeartbeat",value:function(){var e=this;this.stopHeartbeat(),this.missedHeartbeats=0,this.heartbeatTimeoutId=setInterval((function(){if(e.isConnected()){if(e.missedHeartbeats++,e.missedHeartbeats>=e.maxMissedHeartbeats)return e.debug&&console.warn("Missed ".concat(e.missedHeartbeats," heartbeats, reconnecting...")),e.triggerEvent("heartbeat_timeout",{missed:e.missedHeartbeats,max:e.maxMissedHeartbeats}),void e.reconnect();e.send({type:"ping",timestamp:Date.now()}).catch((function(e){console.error("Error sending heartbeat:",e)}))}else e.stopHeartbeat()}),this.heartbeatInterval)}},{key:"stopHeartbeat",value:function(){this.heartbeatTimeoutId&&(clearInterval(this.heartbeatTimeoutId),this.heartbeatTimeoutId=null)}},{key:"addEventListener",value:function(e,t){return this.eventListeners[e]||(this.eventListeners[e]=[]),this.eventListeners[e].push(t),this}},{key:"removeEventListener",value:function(e,t){return this.eventListeners[e]?(this.eventListeners[e]=this.eventListeners[e].filter((function(e){return e!==t})),this):this}},{key:"on",value:function(e,t){return this.addEventListener(e,t)}},{key:"off",value:function(e,t){return this.removeEventListener(e,t)}},{key:"triggerEvent",value:function(e,t){this.eventListeners[e]&&this.eventListeners[e].forEach((function(n){try{n(t)}catch(t){console.error("Error in ".concat(e," event listener:"),t)}}))}},{key:"getConnectionState",value:function(){var e="CLOSED";if(this.socket)switch(this.socket.readyState){case WebSocket.CONNECTING:e="CONNECTING";break;case WebSocket.OPEN:e="OPEN";break;case WebSocket.CLOSING:e="CLOSING";break;case WebSocket.CLOSED:e="CLOSED"}return{connected:this.isConnected(),connecting:this.connecting,readyState:this.socket?this.socket.readyState:WebSocket.CLOSED,readyStateText:e,reconnectAttempts:this.reconnectAttempts,maxReconnectAttempts:this.maxReconnectAttempts,url:this.url,lastError:this.lastError}}},{key:"getOfflineQueueStatus",value:function(){return{count:this.offlineQueue.length,enabled:this.options.queueOfflineMessages}}},{key:"clearOfflineQueue",value:function(){var e=this.offlineQueue.length;return this.offlineQueue=[],this.triggerEvent("offline_queue_cleared",{count:e}),e}},{key:"_getDefaultWebSocketUrl",value:function(){var e="https:"===window.location.protocol?"wss:":"ws:",t=window.location.host;return"".concat(e,"//").concat(t,"/ws")}},{key:"_createError",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=new Error(t);return r.type=e,r.details=n,r.timestamp=Date.now(),this.debug&&console.error("WebSocketError [".concat(e,"]: ").concat(t),n),this.triggerEvent("error",r),r}},{key:"_handleConnectionError",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;this.lastError=e;var r="CONNECTION_ERROR",o="Failed to connect to WebSocket server";if(e.code)switch(e.code){case 1e3:r="NORMAL_CLOSURE",o="Connection closed normally";break;case 1001:r="GOING_AWAY",o="Server is going away";break;case 1002:r="PROTOCOL_ERROR",o="Protocol error";break;case 1003:r="UNSUPPORTED_DATA",o="Unsupported data";break;case 1005:r="NO_STATUS",o="No status code was provided";break;case 1006:r="ABNORMAL_CLOSURE",o="Connection closed abnormally";break;case 1007:r="INVALID_FRAME_PAYLOAD",o="Invalid frame payload data";break;case 1008:r="POLICY_VIOLATION",o="Policy violation";break;case 1009:r="MESSAGE_TOO_BIG",o="Message too big";break;case 1010:r="MISSING_EXTENSION",o="Required extension is missing";break;case 1011:r="INTERNAL_ERROR",o="Internal server error";break;case 1012:r="SERVICE_RESTART",o="Service is restarting";break;case 1013:r="TRY_AGAIN_LATER",o="Try again later";break;case 1014:r="BAD_GATEWAY",o="Bad gateway";break;case 1015:r="TLS_HANDSHAKE_FAILURE",o="TLS handshake failure";break;default:r="CODE_".concat(e.code),o="WebSocket error code ".concat(e.code)}else e.message&&(e.message.includes("timeout")?(r="CONNECTION_TIMEOUT",o="Connection timed out"):e.message.includes("refused")?(r="CONNECTION_REFUSED",o="Connection refused"):e.message.includes("ENOTFOUND")&&(r="HOST_NOT_FOUND",o="Host not found"));var s=this._createError(r,o,{originalError:e,attempt:n,url:this.url});switch(r){case"NORMAL_CLOSURE":break;case"GOING_AWAY":case"SERVICE_RESTART":this.options.autoReconnect&&setTimeout((function(){return t.reconnect()}),5e3);break;case"TRY_AGAIN_LATER":this.options.autoReconnect&&setTimeout((function(){return t.reconnect()}),1e4);break;case"CONNECTION_REFUSED":case"HOST_NOT_FOUND":if(this.options.fallbackUrls&&this.options.fallbackUrls.length>0){var a=this.options.fallbackUrls[n%this.options.fallbackUrls.length];this.debug&&console.log("Trying fallback URL: ".concat(a)),this.url=a,this.options.autoReconnect&&setTimeout((function(){return t.reconnect()}),1e3)}else this.options.autoReconnect&&this.reconnect();break;case"ABNORMAL_CLOSURE":case"INTERNAL_ERROR":case"BAD_GATEWAY":if(this.options.autoReconnect){var i=Math.min(1e3*Math.pow(2,n),3e4);setTimeout((function(){return t.reconnect()}),i)}break;default:this.options.autoReconnect&&this.reconnect()}return s}},{key:"_handleMessageError",value:function(e,t){var n="MESSAGE_ERROR",r="Failed to process WebSocket message";return e.message&&(e.message.includes("JSON")?(n="INVALID_JSON",r="Invalid JSON in message"):e.message.includes("timeout")?(n="MESSAGE_TIMEOUT",r="Message processing timed out"):e.message.includes("rate limit")&&(n="RATE_LIMITED",r="Rate limit exceeded")),this._createError(n,r,{originalError:e,message:t})}}],[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e,t&&e.instance.init(t)),e.instance}}])}())},17648:(e,t,n)=>{n.d(t,{A:()=>_});var r=n(64467),o=n(60436),s=n(10467),a=n(54756),i=n.n(a);function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){(0,r.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={enabled:!0,samplingRate:1,errorLimit:100,breadcrumbLimit:50,ignoredErrors:[/ResizeObserver loop limit exceeded/,/Loading chunk \d+ failed/,/Network request failed/,/Script error/,/Extension context invalidated/,/Failed to report error/,/Error reporting failed/,/TypeError: Failed to fetch/,/TypeError: NetworkError when attempting to fetch resource/,/AbortError/,/Request aborted/,/Request timed out/,/Load failed/],reportingEndpoint:"/api/errors",logToConsole:!0,captureConsoleErrors:!0,captureNetworkErrors:!0,captureUnhandledRejections:!0,captureBreadcrumbs:!0},p={state:"CLOSED",failureCount:0,lastFailureTime:null,failureThreshold:5,timeout:6e4,successThreshold:2},d={errors:[],breadcrumbs:[],sessionId:"".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),startTime:(new Date).toISOString(),reportingQueue:[],lastReportAttempt:null,reportingInProgress:!1};function g(e){e.preventDefault(),h({type:"uncaught_error",message:e.message||"Unknown error",stack:e.error?e.error.stack:null,source:e.filename,line:e.lineno,column:e.colno,timestamp:(new Date).toISOString()}),l.logToConsole&&console.error("Uncaught error:",e.message)}function m(e){e.preventDefault();var t=e.reason,n=t instanceof Error?t.message:String(t);h({type:"unhandled_rejection",message:n||"Unhandled promise rejection",stack:t instanceof Error?t.stack:null,timestamp:(new Date).toISOString()}),l.logToConsole&&console.error("Unhandled rejection:",n)}function h(e){l.enabled&&(Math.random()>l.samplingRate||function(e){return l.ignoredErrors.some((function(t){return t instanceof RegExp?t.test(e.message):e.message.includes(t)}))}(e)||(e.sessionId=d.sessionId,e.userAgent=navigator.userAgent,e.url=window.location.href,e.breadcrumbs=(0,o.A)(d.breadcrumbs),d.errors.push(e),d.errors.length>l.errorLimit&&d.errors.shift(),v(e)))}function f(e){l.captureBreadcrumbs&&(d.breadcrumbs.push(e),d.breadcrumbs.length>l.breadcrumbLimit&&d.breadcrumbs.shift())}function v(e){var t,n,r,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;if(l.reportingEndpoint)if(r=Date.now(),"OPEN"!==p.state||r-p.lastFailureTime>=p.timeout&&(p.state="HALF_OPEN",1)){if(!("error_reporting_failure"===e.type||null!==(t=e.message)&&void 0!==t&&t.includes("Failed to report error")||null!==(n=e.url)&&void 0!==n&&n.includes(l.reportingEndpoint)))if(d.reportingInProgress)d.reportingQueue.push(e);else{d.reportingInProgress=!0,d.lastReportAttempt=Date.now();var a=Math.min(1e3*Math.pow(2,o),3e4),i=function(){(window._originalFetch||window.fetch)(l.reportingEndpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e),keepalive:!0}).then((function(e){if(!e.ok)throw new Error("HTTP ".concat(e.status,": ").concat(e.statusText));if("HALF_OPEN"===p.state?(p.successThreshold--,p.successThreshold<=0&&(p.state="CLOSED",p.failureCount=0,p.successThreshold=2)):"CLOSED"===p.state&&(p.failureCount=0),"CLOSED"===p.state&&d.reportingQueue.length>0){var t=d.reportingQueue.shift();setTimeout((function(){return v(t)}),100)}})).catch((function(t){p.failureCount++,p.lastFailureTime=Date.now(),p.failureCount>=p.failureThreshold&&(p.state="OPEN"),o<s?setTimeout((function(){v(e,o+1,s)}),a):(function(e){try{var t="errorTracker_failedReports",n=localStorage.getItem(t),r=n?JSON.parse(n):[];r.push(u(u({},e),{},{storedAt:(new Date).toISOString()})),r.length>50&&r.splice(0,r.length-50),localStorage.setItem(t,JSON.stringify(r))}catch(e){l.logToConsole}}(e),l.logToConsole)})).finally((function(){d.reportingInProgress=!1}))};o>0?setTimeout(i,a):i()}}else d.reportingQueue.push(e)}function E(){return{trackError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};h(e instanceof Error?u(u({type:"manual",message:e.message,stack:e.stack},t),{},{timestamp:(new Date).toISOString()}):u(u({type:"manual",message:String(e)},t),{},{timestamp:(new Date).toISOString()}))},addBreadcrumb:function(e){f({type:"manual",category:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"manual",message:e,data:arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},timestamp:(new Date).toISOString()})},getErrors:function(){return(0,o.A)(d.errors)},getBreadcrumbs:function(){return(0,o.A)(d.breadcrumbs)},clearErrors:function(){d.errors=[]},clearBreadcrumbs:function(){d.breadcrumbs=[]},getConfig:function(){return u({},l)},updateConfig:function(e){Object.assign(l,e)},getCircuitBreakerStatus:function(){return{state:p.state,failureCount:p.failureCount,lastFailureTime:p.lastFailureTime,queueLength:d.reportingQueue.length}},retryQueuedErrors:function(){return new Promise((function(e){0!==d.reportingQueue.length?("OPEN"===p.state&&(p.state="HALF_OPEN"),v(d.reportingQueue.shift()),setTimeout(e,1e3)):e()}))},getLocallyStoredErrors:function(){try{var e=localStorage.getItem("errorTracker_failedReports");return e?JSON.parse(e):[]}catch(e){return[]}},clearLocallyStoredErrors:function(){try{localStorage.removeItem("errorTracker_failedReports")}catch(e){}}}}const _=function(){var e,t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.assign(l,n),l.enabled?(window.addEventListener("error",g),window.addEventListener("unhandledrejection",m),l.captureConsoleErrors&&(e=console.error,t=console.warn,console.error=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];e.apply(console,n),h({type:"console_error",message:n.map((function(e){return"string"==typeof e?e:JSON.stringify(e)})).join(" "),timestamp:(new Date).toISOString()})},console.warn=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t.apply(console,n),l.captureBreadcrumbs&&f({type:"console_warn",message:n.map((function(e){return"string"==typeof e?e:JSON.stringify(e)})).join(" "),timestamp:(new Date).toISOString()})}),l.captureNetworkErrors&&function(){window._originalFetch||(window._originalFetch=window.fetch);var e=window._originalFetch;window.fetch=(0,s.A)(i().mark((function t(){var n,r,o,s,a,c,u,p=arguments;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:for(n=p.length,r=new Array(n),o=0;o<n;o++)r[o]=p[o];return t.prev=1,t.next=4,e.apply(window,r);case 4:return s=t.sent,l.captureBreadcrumbs&&f({type:"network",category:"fetch",data:{url:"string"==typeof r[0]?r[0]:r[0].url,method:(null===(a=r[1])||void 0===a?void 0:a.method)||"GET",status:s.status},timestamp:(new Date).toISOString()}),s.ok||h({type:"network_error",message:"Fetch error: ".concat(s.status," ").concat(s.statusText),data:{url:"string"==typeof r[0]?r[0]:r[0].url,method:(null===(c=r[1])||void 0===c?void 0:c.method)||"GET",status:s.status,statusText:s.statusText},timestamp:(new Date).toISOString()}),t.abrupt("return",s);case 10:throw t.prev=10,t.t0=t.catch(1),h({type:"network_error",message:"Fetch failed: ".concat(t.t0.message),stack:t.t0.stack,data:{url:"string"==typeof r[0]?r[0]:null===(u=r[0])||void 0===u?void 0:u.url},timestamp:(new Date).toISOString()}),t.t0;case 14:case"end":return t.stop()}}),t,null,[[1,10]])})));var t=XMLHttpRequest.prototype.open,n=XMLHttpRequest.prototype.send;XMLHttpRequest.prototype.open=function(e,n){return this._errorTracking={method:e,url:n},t.apply(this,arguments)},XMLHttpRequest.prototype.send=function(){return this.addEventListener("load",(function(){var e,t,n,r;l.captureBreadcrumbs&&f({type:"network",category:"xhr",data:{url:null===(e=this._errorTracking)||void 0===e?void 0:e.url,method:null===(t=this._errorTracking)||void 0===t?void 0:t.method,status:this.status},timestamp:(new Date).toISOString()}),this.status>=400&&h({type:"network_error",message:"XHR error: ".concat(this.status," ").concat(this.statusText),data:{url:null===(n=this._errorTracking)||void 0===n?void 0:n.url,method:null===(r=this._errorTracking)||void 0===r?void 0:r.method,status:this.status,statusText:this.statusText},timestamp:(new Date).toISOString()})})),this.addEventListener("error",(function(){var e,t;h({type:"network_error",message:"XHR failed",data:{url:null===(e=this._errorTracking)||void 0===e?void 0:e.url,method:null===(t=this._errorTracking)||void 0===t?void 0:t.method},timestamp:(new Date).toISOString()})})),n.apply(this,arguments)}}(),l.captureBreadcrumbs&&(document.addEventListener("click",(function(e){var t,n=e.target,r=n.tagName.toLowerCase(),o=n.id?"#".concat(n.id):"",s=Array.from(n.classList).map((function(e){return".".concat(e)})).join(""),a=null===(t=n.innerText)||void 0===t?void 0:t.substring(0,50);f({type:"user",category:"click",data:{element:"".concat(r).concat(o).concat(s),text:a},timestamp:(new Date).toISOString()})})),window.addEventListener("popstate",(function(){f({type:"navigation",data:{from:document.referrer,to:window.location.href},timestamp:(new Date).toISOString()})}))),console.log("Error tracking initialized"),E()):(console.log("Error tracking is disabled"),E())}()},48035:(e,t,n)=>{n.d(t,{A:()=>v});var r=n(20038),o=n(60436),s=n(64467),a=n(81616);function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){(0,s.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u={app:{components:[],layouts:[],styles:{},data:{}},websocket:{connected:!1,connecting:!1,url:null,error:null,lastMessage:null,reconnectAttempts:0},loading:!1,error:null,themes:[],activeTheme:"default"};const l=function(){var e,t,n,r,s,i,l,p,d,g,m,h,f=arguments.length>0&&void 0!==arguments[0]?arguments[0]:u,v=arguments.length>1?arguments[1]:void 0;switch(v.type){case a.Q3.WS_CONNECT:return c(c({},f),{},{websocket:c(c({},f.websocket),{},{connecting:!0,url:(null===(e=v.payload)||void 0===e?void 0:e.url)||f.websocket.url,error:null})});case a.Q3.WS_CONNECTED:return c(c({},f),{},{websocket:c(c({},f.websocket),{},{connected:!0,connecting:!1,error:null,reconnectAttempts:0})});case a.Q3.WS_DISCONNECTED:return c(c({},f),{},{websocket:c(c({},f.websocket),{},{connected:!1,connecting:!1,error:(null===(t=v.payload)||void 0===t?void 0:t.error)||null})});case a.Q3.WS_MESSAGE_RECEIVED:var E=v.payload;return"app_data"===E.type?c(c({},f),{},{websocket:c(c({},f.websocket),{},{lastMessage:E}),app:c(c({},f.app),{},{components:E.data.components||[],layouts:E.data.layouts||[],styles:E.data.styles||{},data:E.data.data||{}})}):c(c({},f),{},{websocket:c(c({},f.websocket),{},{lastMessage:E})});case a.Q3.WS_ERROR:return c(c({},f),{},{websocket:c(c({},f.websocket),{},{error:v.payload,connected:!1,connecting:!1})});case a.Q3.ADD_COMPONENT:return c(c({},f),{},{app:c(c({},f.app),{},{components:[].concat((0,o.A)((null===(n=f.app)||void 0===n?void 0:n.components)||[]),[v.payload])})});case a.Q3.UPDATE_COMPONENT:return c(c({},f),{},{app:c(c({},f.app),{},{components:((null===(r=f.app)||void 0===r?void 0:r.components)||[]).map((function(e,t){return t===v.payload.index?c(c({},e),v.payload.updates):e}))})});case a.Q3.DELETE_COMPONENT:return c(c({},f),{},{app:c(c({},f.app),{},{components:((null===(s=f.app)||void 0===s?void 0:s.components)||[]).filter((function(e,t){return t!==v.payload.index}))})});case a.Q3.ADD_LAYOUT:return c(c({},f),{},{app:c(c({},f.app),{},{layouts:[].concat((0,o.A)((null===(i=f.app)||void 0===i?void 0:i.layouts)||[]),[v.payload])})});case a.Q3.UPDATE_LAYOUT:return c(c({},f),{},{app:c(c({},f.app),{},{layouts:((null===(l=f.app)||void 0===l?void 0:l.layouts)||[]).map((function(e,t){return t===v.payload.index?c(c({},e),v.payload.updates):e}))})});case a.Q3.DELETE_LAYOUT:return c(c({},f),{},{app:c(c({},f.app),{},{layouts:((null===(p=f.app)||void 0===p?void 0:p.layouts)||[]).filter((function(e,t){return t!==v.payload.index}))})});case a.Q3.SAVE_APP_DATA:return c(c({},f),{},{app:c(c({},f.app),{},{components:v.payload.components||(null===(d=f.app)||void 0===d?void 0:d.components)||[],layouts:v.payload.layouts||(null===(g=f.app)||void 0===g?void 0:g.layouts)||[],styles:v.payload.styles||(null===(m=f.app)||void 0===m?void 0:m.styles)||{},data:v.payload.data||(null===(h=f.app)||void 0===h?void 0:h.data)||{}})});case a.Q3.LOAD_APP_DATA:return c(c({},f),{},{app:c(c({},f.app),{},{components:v.payload.components||[],layouts:v.payload.layouts||[],styles:v.payload.styles||{},data:v.payload.data||{}})});case a.Q3.SET_LOADING:return c(c({},f),{},{loading:v.payload});case a.Q3.SET_ERROR:return c(c({},f),{},{error:v.payload});case a.Q3.CLEAR_ERROR:return c(c({},f),{},{error:null});case"ADD_THEME":return c(c({},f),{},{themes:[].concat((0,o.A)(f.themes),[v.payload])});case"UPDATE_THEME":return c(c({},f),{},{themes:f.themes.map((function(e){return e.id===v.payload.id?v.payload:e}))});case"REMOVE_THEME":return c(c({},f),{},{themes:f.themes.filter((function(e){return e.id!==v.payload.id}))});case"SET_ACTIVE_THEME":return c(c({},f),{},{activeTheme:v.payload});default:return f}};var p=n(17053),d=n(4318),g=function(){return{type:d.Te}},m=function(e){return{type:d.AS,payload:{error:e}}};function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var f=function(e){return function(t){return function(n){var r=performance.now(),o=t(n),s=performance.now()-r;return"undefined"!=typeof window&&window.dispatchEvent(new CustomEvent("redux-action",{detail:{type:n.type,duration:s,timestamp:(new Date).toISOString(),state:e.getState()}})),o}}};const v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,r.U1)({reducer:l,preloadedState:e,middleware:function(e){return e({serializableCheck:{ignoredActions:["WS_MESSAGE_RECEIVED","WS_ERROR","WEBSOCKET_MESSAGE_RECEIVED","WEBSOCKET_ERROR"],ignoredPaths:["websocket.socket","error.originalError","webSocketClient.socket"]},thunk:!0}).concat((t=!1,function(e){return function(n){return function(r){switch(r.type){case d.YG:if(!t){var o=p.A.getInstance();o.connect().then((function(){t=!0,e.dispatch(g())})).catch((function(t){e.dispatch(m(t))})),o.addEventListener("open",(function(){t=!0,e.dispatch(g())})),o.addEventListener("close",(function(){t=!1,e.dispatch({type:d.RH})})),o.addEventListener("error",(function(t){e.dispatch(m(t))}));var a=["app_data","app_data_update","component_added","component_updated","component_deleted","layout_added","layout_updated","layout_deleted","error"];o.addEventListener("message",(function(t){var n;t&&t.type&&a.includes(t.type)&&(e.dispatch((n={type:t.type,data:t},{type:d.H2,payload:n})),e.dispatch({type:"WS_".concat(t.type.toUpperCase()),payload:t}))}))}break;case d.uV:t&&(p.A.getInstance().close(),t=!1);break;case d.WD:if(t){var i=r.payload,c=i.messageType,u=i.data;p.A.getInstance().sendMessage(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){(0,s.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({type:c},u)).catch((function(t){console.error("Error sending message:",t),e.dispatch(m(t))}))}else console.warn("Cannot send message: WebSocket is not connected"),e.dispatch(m(new Error("WebSocket is not connected")))}return n(r)}}}),f);var t},devTools:{name:"App Builder 201",trace:!0,traceLimit:25}})}()},69477:(e,t,n)=>{n.d(t,{ec:()=>L,gf:()=>b,wz:()=>k,VM:()=>I,wR:()=>w,iD:()=>P,ri:()=>D,Be:()=>R,kz:()=>T,eg:()=>N});var r=n(10467),o=n(54756),s=n.n(o),a=n(64467),i=n(23029),c=n(92901),u=n(84447),l=n(81945);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){(0,a.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const g=new(function(){return(0,c.A)((function e(){(0,i.A)(this,e),this.token=null,this.tokenPromise=null}),[{key:"getTokenFromCookie",value:function(){var e="; ".concat(document.cookie).split("; ".concat("csrftoken","="));return 2===e.length?e.pop().split(";").shift():null}},{key:"fetchToken",value:(a=(0,r.A)(s().mark((function e(){var t,n;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch(l.Sn.CSRF_TOKEN,{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});case 3:if((t=e.sent).ok){e.next=6;break}throw new Error("Failed to fetch CSRF token: ".concat(t.status));case 6:return e.next=8,t.json();case 8:return n=e.sent,this.token=n.csrfToken,e.abrupt("return",this.token);case 13:throw e.prev=13,e.t0=e.catch(0),console.error("Error fetching CSRF token:",e.t0),e.t0;case 17:case"end":return e.stop()}}),e,this,[[0,13]])}))),function(){return a.apply(this,arguments)})},{key:"getToken",value:(o=(0,r.A)(s().mark((function e(){var t,n;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.token){e.next=2;break}return e.abrupt("return",this.token);case 2:if(!this.tokenPromise){e.next=4;break}return e.abrupt("return",this.tokenPromise);case 4:if(!(t=this.getTokenFromCookie())){e.next=8;break}return this.token=t,e.abrupt("return",this.token);case 8:return this.tokenPromise=this.fetchToken(),e.prev=9,e.next=12,this.tokenPromise;case 12:return n=e.sent,this.tokenPromise=null,e.abrupt("return",n);case 17:throw e.prev=17,e.t0=e.catch(9),this.tokenPromise=null,e.t0;case 21:case"end":return e.stop()}}),e,this,[[9,17]])}))),function(){return o.apply(this,arguments)})},{key:"clearToken",value:function(){this.token=null,this.tokenPromise=null}},{key:"getHeaders",value:(n=(0,r.A)(s().mark((function e(){var t,n,r=arguments;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.length>0&&void 0!==r[0]?r[0]:{},e.prev=1,e.next=4,this.getToken();case 4:return n=e.sent,e.abrupt("return",d({"X-CSRFToken":n,"Content-Type":"application/json"},t));case 8:return e.prev=8,e.t0=e.catch(1),console.warn("Failed to get CSRF token, proceeding without it:",e.t0),e.abrupt("return",d({"Content-Type":"application/json"},t));case 12:case"end":return e.stop()}}),e,this,[[1,8]])}))),function(){return n.apply(this,arguments)})},{key:"request",value:(t=(0,r.A)(s().mark((function e(t){var n,r,o=arguments;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},e.next=3,this.getHeaders(n.headers);case 3:return r=e.sent,e.abrupt("return",fetch(t,d(d({},n),{},{headers:r,credentials:"include"})));case 5:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"initialize",value:(e=(0,r.A)(s().mark((function e(){return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.getToken();case 3:console.log("CSRF service initialized successfully"),e.next=9;break;case 6:e.prev=6,e.t0=e.catch(0),console.warn("Failed to initialize CSRF service:",e.t0);case 9:case"end":return e.stop()}}),e,this,[[0,6]])}))),function(){return e.apply(this,arguments)})}]);var e,t,n,o,a}());function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){(0,a.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f={baseURL:"http://localhost:8000",timeout:1e4,headers:{"Content-Type":"application/json",Accept:"application/json"}};const v=new(function(){return(0,c.A)((function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,i.A)(this,e),this.config=h(h({},f),n),this.client=u.Ay.create(this.config),this.endpoints=[],this.initialized=!1,this.client.interceptors.request.use(function(){var e=(0,r.A)(s().mark((function e(t){var n,r;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((n=b())&&(t.headers.Authorization="Bearer ".concat(n)),!t.method||"get"===t.method.toLowerCase()){e.next=13;break}return e.prev=3,e.next=6,g.getHeaders();case 6:r=e.sent,t.headers=h(h({},t.headers),r),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(3),console.warn("Failed to get CSRF token:",e.t0);case 13:return t.withCredentials=!0,e.abrupt("return",t);case 15:case"end":return e.stop()}}),e,null,[[3,10]])})));return function(t){return e.apply(this,arguments)}}(),(function(e){return Promise.reject(e)})),this.client.interceptors.response.use((function(e){return e.data}),function(){var e=(0,r.A)(s().mark((function e(n){var r,o;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.config,!n.response||401!==n.response.status||r._retry){e.next=18;break}return r._retry=!0,e.prev=3,e.next=6,R();case 6:if(!(o=e.sent)){e.next=11;break}return t.client.defaults.headers.common.Authorization="Bearer ".concat(o),r.headers.Authorization="Bearer ".concat(o),e.abrupt("return",t.client(r));case 11:e.next=18;break;case 13:return e.prev=13,e.t0=e.catch(3),console.error("Token refresh failed:",e.t0),window.location.href="/login",e.abrupt("return",Promise.reject(e.t0));case 18:return e.abrupt("return",Promise.reject(n));case 19:case"end":return e.stop()}}),e,null,[[3,13]])})));return function(t){return e.apply(this,arguments)}}())}),[{key:"initServices",value:(p=(0,r.A)(s().mark((function e(){return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.discoverEndpoints();case 3:return this.initialized=!0,e.abrupt("return",{initialized:!0,endpoints:this.endpoints});case 7:e.prev=7,e.t0=e.catch(0),console.warn("API client initialization failed:",e.t0),e.next=14;break;case 14:throw e.t0;case 15:case"end":return e.stop()}}),e,this,[[0,7]])}))),function(){return p.apply(this,arguments)})},{key:"discoverEndpoints",value:(l=(0,r.A)(s().mark((function e(){var t;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.client.get("/");case 3:if(!(t=e.sent)||!t.endpoints){e.next=7;break}return this.endpoints=t.endpoints,e.abrupt("return",this.endpoints);case 7:return this.endpoints=[{path:"/status",method:"GET",description:"Get API status"},{path:"/app-data",method:"GET",description:"Get app data"},{path:"/components",method:"GET",description:"Get components"},{path:"/templates",method:"GET",description:"Get templates"},{path:"/projects",method:"GET",description:"Get projects"},{path:"/users",method:"GET",description:"Get users"},{path:"/auth",method:"POST",description:"Authenticate user"}],e.abrupt("return",this.endpoints);case 11:return e.prev=11,e.t0=e.catch(0),console.warn("API endpoint discovery failed:",e.t0),this.endpoints=[{path:"/status",method:"GET",description:"Get API status"},{path:"/app-data",method:"GET",description:"Get app data"},{path:"/components",method:"GET",description:"Get components"},{path:"/templates",method:"GET",description:"Get templates"},{path:"/projects",method:"GET",description:"Get projects"},{path:"/users",method:"GET",description:"Get users"},{path:"/auth",method:"POST",description:"Authenticate user"}],e.abrupt("return",this.endpoints);case 16:case"end":return e.stop()}}),e,this,[[0,11]])}))),function(){return l.apply(this,arguments)})},{key:"get",value:(a=(0,r.A)(s().mark((function e(t){var n,r=arguments;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:{},e.abrupt("return",this.client.get(t,n));case 2:case"end":return e.stop()}}),e,this)}))),function(e){return a.apply(this,arguments)})},{key:"post",value:(o=(0,r.A)(s().mark((function e(t){var n,r,o=arguments;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r=o.length>2&&void 0!==o[2]?o[2]:{},e.abrupt("return",this.client.post(t,n,r));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})},{key:"put",value:(n=(0,r.A)(s().mark((function e(t){var n,r,o=arguments;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r=o.length>2&&void 0!==o[2]?o[2]:{},e.abrupt("return",this.client.put(t,n,r));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"patch",value:(t=(0,r.A)(s().mark((function e(t){var n,r,o=arguments;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r=o.length>2&&void 0!==o[2]?o[2]:{},e.abrupt("return",this.client.patch(t,n,r));case 3:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"delete",value:(e=(0,r.A)(s().mark((function e(t){var n,r=arguments;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:{},e.abrupt("return",this.client.delete(t,n));case 2:case"end":return e.stop()}}),e,this)}))),function(t){return e.apply(this,arguments)})},{key:"hasEndpoint",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET";return this.endpoints.some((function(n){return n.path===e&&n.method===t}))}},{key:"getEndpointDescription",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET",n=this.endpoints.find((function(n){return n.path===e&&n.method===t}));return n?n.description:""}}]);var e,t,n,o,a,l,p}());var E="app_auth_token",_="app_refresh_token",S="app_user",b=function(){return localStorage.getItem(E)},y=function(e){localStorage.setItem(E,e)},C=function(e){localStorage.setItem(_,e)},k=function(){var e=localStorage.getItem(S);if(e)try{return JSON.parse(e)}catch(e){return console.error("Error parsing user data:",e),null}return null},O=function(e){localStorage.setItem(S,JSON.stringify(e))},A=function(){localStorage.removeItem(E),localStorage.removeItem(_),localStorage.removeItem(S)},w=function(){return!!b()},P=function(){var e=(0,r.A)(s().mark((function e(t,n){var r,o,a;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,v.post("/auth/login/",{username:t,password:n});case 3:if(!(r=e.sent).access){e.next=20;break}return y(r.access),r.refresh&&C(r.refresh),e.prev=7,e.next=10,v.get("/auth/profile/");case 10:(o=e.sent)&&O(o),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(7),console.warn("Failed to fetch user profile:",e.t0);case 17:return e.abrupt("return",{success:!0,user:r.user||{username:t}});case 20:if(!r.token){e.next=24;break}return y(r.token),r.user&&O(r.user),e.abrupt("return",{success:!0,user:r.user});case 24:return e.abrupt("return",{success:!1,error:"Invalid response from server"});case 27:return e.prev=27,e.t1=e.catch(0),console.error("Login error:",e.t1),e.abrupt("return",{success:!1,error:(null===(a=e.t1.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.detail)||e.t1.message||"Login failed"});case 31:case"end":return e.stop()}}),e,null,[[0,27],[7,14]])})));return function(t,n){return e.apply(this,arguments)}}(),T=function(){var e=(0,r.A)(s().mark((function e(t){var n,r,o,a;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,v.post("/auth/register/",t);case 3:if(!(n=e.sent).access){e.next=20;break}return y(n.access),n.refresh&&C(n.refresh),e.prev=7,e.next=10,v.get("/auth/profile/");case 10:(r=e.sent)&&O(r),e.next=17;break;case 14:e.prev=14,e.t0=e.catch(7),console.warn("Failed to fetch user profile:",e.t0);case 17:return e.abrupt("return",{success:!0,user:n.user||{username:t.username}});case 20:if(!n.token){e.next=26;break}return y(n.token),n.user&&O(n.user),e.abrupt("return",{success:!0,user:n.user});case 26:if(!n.success){e.next=28;break}return e.abrupt("return",{success:!0,user:n.user});case 28:return e.abrupt("return",{success:!1,error:n.error||"Registration failed"});case 31:return e.prev=31,e.t1=e.catch(0),console.error("Registration error:",e.t1),e.abrupt("return",{success:!1,error:(null===(o=e.t1.response)||void 0===o||null===(o=o.data)||void 0===o?void 0:o.detail)||(null===(a=e.t1.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||e.t1.message||"Registration failed"});case 35:case"end":return e.stop()}}),e,null,[[0,31],[7,14]])})));return function(t){return e.apply(this,arguments)}}(),D=function(){var e=(0,r.A)(s().mark((function e(){return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,v.post("/auth/logout");case 3:e.next=8;break;case 5:e.prev=5,e.t0=e.catch(0),console.warn("Logout notification failed:",e.t0);case 8:return e.prev=8,A(),e.finish(8);case 11:return e.abrupt("return",{success:!0});case 12:case"end":return e.stop()}}),e,null,[[0,5,8,11]])})));return function(){return e.apply(this,arguments)}}(),R=function(){var e=(0,r.A)(s().mark((function e(){var t,n;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,t=localStorage.getItem(_)){e.next=4;break}throw new Error("No refresh token available");case 4:return e.next=6,v.post("/auth/token/refresh/",{refresh:t});case 6:if(!(n=e.sent).access){e.next=13;break}return y(n.access),n.refresh&&C(n.refresh),e.abrupt("return",n.access);case 13:if(!n.token){e.next=17;break}return y(n.token),n.refreshToken&&C(n.refreshToken),e.abrupt("return",n.token);case 17:return e.abrupt("return",null);case 20:return e.prev=20,e.t0=e.catch(0),console.error("Token refresh error:",e.t0),A(),e.abrupt("return",null);case 25:case"end":return e.stop()}}),e,null,[[0,20]])})));return function(){return e.apply(this,arguments)}}(),I=function(){var e=(0,r.A)(s().mark((function e(){return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,v.get("/auth/profile");case 3:return e.abrupt("return",e.sent);case 6:return e.prev=6,e.t0=e.catch(0),console.error("Get user profile error:",e.t0),e.abrupt("return",null);case 10:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(){return e.apply(this,arguments)}}(),N=function(){var e=(0,r.A)(s().mark((function e(t){var n;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,v.put("/auth/profile",t);case 3:if(!(n=e.sent).user){e.next=7;break}return O(n.user),e.abrupt("return",{success:!0,user:n.user});case 7:return e.abrupt("return",{success:!1,error:"Invalid response from server"});case 10:return e.prev=10,e.t0=e.catch(0),console.error("Update profile error:",e.t0),e.abrupt("return",{success:!1,error:e.t0.message||"Update profile failed"});case 14:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(t){return e.apply(this,arguments)}}(),L=function(){var e=(0,r.A)(s().mark((function e(t,n){var r;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,v.post("/auth/change-password",{currentPassword:t,newPassword:n});case 3:return r=e.sent,e.abrupt("return",{success:r.success,error:r.error});case 7:return e.prev=7,e.t0=e.catch(0),console.error("Change password error:",e.t0),e.abrupt("return",{success:!1,error:e.t0.message||"Change password failed"});case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t,n){return e.apply(this,arguments)}}()},77362:(e,t,n)=>{n.d(t,{hl:()=>p,jY:()=>g,kz:()=>l});var r=n(10467),o=n(54756),s=n.n(o),a=n(79730);function i(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return c(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,i=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){i=!0,s=e},f:function(){try{a||null==n.return||n.return()}finally{if(i)throw s}}}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var u=Boolean("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function l(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("serviceWorker"in navigator){try{if("true"===localStorage.getItem("disable_sw_temp"))return console.log("Service worker registration skipped: temporarily disabled"),void localStorage.removeItem("disable_sw_temp")}catch(e){}if(new URL({ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.PUBLIC_URL||"",window.location.href).origin!==window.location.origin)return void console.log("Service worker registration skipped: different origin");window.addEventListener("load",(function(){var t="".concat({ALLUSERSPROFILE:"C:\\ProgramData",API_TARGET:"http://localhost:8000",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",ChocolateyInstall:"C:\\ProgramData\\chocolatey",ChocolateyLastPathUpdate:"133841632699909501",CHOKIDAR_USEPOLLING:"false",CHROME_CRASHPAD_PIPE_NAME:"\\\\.\\pipe\\crashpad_15200_BYZFKFUCNLHDCDUU",COLOR:"1",COLORTERM:"truecolor",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"LAPTOP-E9FOD0GS",ComSpec:"C:\\WINDOWS\\system32\\cmd.exe",DISABLE_ESLINT_PLUGIN:"false",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",EDITOR:"C:\\WINDOWS\\notepad.exe",EFC_11848_1592913036:"1",FAST_REFRESH:"true",GENERATE_SOURCEMAP:"true",GIT_ASKPASS:"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh",GIT_PAGER:"",HOME:"C:\\Users\\<USER>\\Users\\danie",INIT_CWD:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",LANG:"en_US.UTF-8",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\LAPTOP-E9FOD0GS",NODE:"C:\\Program Files\\nodejs\\node.exe",NODE_ENV:"development",NODE_EXE:"C:\\Program Files\\nodejs\\\\node.exe",NPM_CLI_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js",npm_command:"run-script",npm_config_audit:"",npm_config_auto_install_peers:"true",npm_config_cache:"C:\\Users\\<USER>\\AppData\\Local\\npm-cache",npm_config_fund:"",npm_config_globalconfig:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc",npm_config_global_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_init_module:"C:\\Users\\<USER>\\.npm-init.js",npm_config_legacy_peer_deps:"true",npm_config_local_prefix:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend",npm_config_loglevel:"warn",npm_config_node_gyp:"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_node_version:"18.x",npm_config_noproxy:"",npm_config_npm_version:"10.9.2",npm_config_prefer_offline:"true",npm_config_prefix:"C:\\Users\\<USER>\\AppData\\Roaming\\npm",npm_config_progress:"",npm_config_save_exact:"true",npm_config_strict_peer_dependencies:"",npm_config_userconfig:"C:\\Users\\<USER>\\.npmrc",npm_config_user_agent:"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false",npm_execpath:"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js",npm_lifecycle_event:"build",npm_lifecycle_script:"webpack --mode production",npm_node_execpath:"C:\\Program Files\\nodejs\\node.exe",npm_package_json:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json",npm_package_name:"frontend",npm_package_version:"1.0.0",NPM_PREFIX_JS:"C:\\Program Files\\nodejs\\\\node_modules\\npm\\bin\\npm-prefix.js",NPM_PREFIX_NPM_CLI_JS:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js",NUMBER_OF_PROCESSORS:"12",OneDrive:"C:\\Users\\<USER>\\OneDrive",OneDriveConsumer:"C:\\Users\\<USER>\\OneDrive",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",OS:"Windows_NT",Path:"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"a502",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\WINDOWS",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TERM_PROGRAM:"vscode",TERM_PROGRAM_VERSION:"1.101.1",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",USERDOMAIN:"LAPTOP-E9FOD0GS",USERDOMAIN_ROAMINGPROFILE:"LAPTOP-E9FOD0GS",USERNAME:"danie",USERPROFILE:"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js",VSCODE_GIT_ASKPASS_NODE:"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe",VSCODE_GIT_IPC_HANDLE:"\\\\.\\pipe\\vscode-git-898c7e110b-sock",VSCODE_INJECTION:"1",WATCHPACK_POLLING:"false",WDS_SOCKET_HOST:"localhost",WDS_SOCKET_PATH:"/sockjs-node",WDS_SOCKET_PORT:"3000",WEBSOCKET_TARGET:"ws://localhost:8000",windir:"C:\\WINDOWS"}.PUBLIC_URL||"","/service-worker.js");console.log("Service Worker: Using ".concat(t)),Array.from(document.querySelectorAll("script")).some((function(e){return e.src&&(e.src.includes("socket.io")||e.src.includes("websocket")||e.src.includes("ws"))}))&&console.log("Service Worker: Detected active WebSocket scripts, registering with caution"),u?(function(e,t){fetch(e,{headers:{"Service-Worker":"script"},cache:"reload"}).then((function(n){var r=n.headers.get("content-type");404===n.status||null!=r&&-1===r.indexOf("javascript")?(console.warn("Service Worker: Invalid service worker detected. Attempting to unregister."),navigator.serviceWorker.getRegistrations().then((function(e){var t,n=i(e);try{for(n.s();!(t=n.n()).done;){var r=t.value;console.log("Unregistering service worker:",r.scope),r.unregister()}}catch(e){n.e(e)}finally{n.f()}window.location.reload()}))):(console.log("Service Worker: Valid service worker found. Registering..."),function(e,t){navigator.serviceWorker.register(e).then((function(e){e.onupdatefound=function(){var n=e.installing;null!=n&&(n.onstatechange=function(){"installed"===n.state&&(navigator.serviceWorker.controller?(console.log("Service Worker: New content is available and will be used when all tabs for this page are closed"),t&&t.onUpdate&&t.onUpdate(e)):(console.log("Service Worker: Content is cached for offline use"),t&&t.onSuccess&&t.onSuccess(e)))})}})).catch((function(e){console.error("Service Worker: Error during registration:",e)}))}(e,t))})).catch((function(e){console.log("Service Worker: No internet connection or error occurred:",e),console.log("App is running in offline mode.")}))}(t,e),navigator.serviceWorker.ready.then((function(){console.log("Service Worker: Ready and running in development mode")}))):function(e,t){try{var n=new a.JK(e);"function"!=typeof n.messageSkipWaiting&&(n.messageSkipWaiting=function(){console.log("Service Worker: Custom messageSkipWaiting called"),n.controlling?n.controlling.postMessage("skipWaiting"):navigator.serviceWorker.ready.then((function(e){e.waiting&&e.waiting.postMessage("skipWaiting")}))}),n.addEventListener("installed",(function(e){if(e.isUpdate){if(console.log("Service Worker: Updated service worker installed"),t&&t.onUpdate)try{t.onUpdate(n)}catch(e){console.error("Service Worker: Error in onUpdate callback:",e)}}else if(console.log("Service Worker: New service worker installed"),t&&t.onSuccess)try{t.onSuccess(n)}catch(e){console.error("Service Worker: Error in onSuccess callback:",e)}})),n.addEventListener("activated",(function(e){e.isUpdate?console.log("Service Worker: Updated service worker activated"):console.log("Service Worker: New service worker activated")})),n.addEventListener("waiting",(function(e){if(console.log("Service Worker: New version waiting to be activated"),t&&t.onWaiting)try{t.onWaiting(n)}catch(e){console.error("Service Worker: Error in onWaiting callback:",e)}})),n.addEventListener("controlling",(function(e){console.log("Service Worker: Controlling the page")})),n.addEventListener("redundant",(function(e){console.warn("Service Worker: The installing service worker became redundant")})),n.addEventListener("error",(function(e){console.error("Service Worker: Error during operation:",e)})),n.register().catch((function(e){console.error("Service Worker: Registration failed:",e)}))}catch(e){console.error("Service Worker: Error during registration:",e)}}(t,e)}))}else console.log("Service Worker: Registration skipped - not supported")}function p(){return d.apply(this,arguments)}function d(){return(d=(0,r.A)(s().mark((function e(){var t,n,r,o,a,c,u,l;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("serviceWorker"in navigator){e.next=2;break}return e.abrupt("return",!1);case 2:return e.prev=2,e.next=5,navigator.serviceWorker.getRegistrations();case 5:if(0!==(t=e.sent).length){e.next=9;break}return console.log("No service workers to clean"),e.abrupt("return",!1);case 9:console.log("Forcefully cleaning ".concat(t.length," service workers")),n=i(t),e.prev=11,n.s();case 13:if((r=n.n()).done){e.next=26;break}return o=r.value,e.prev=15,e.next=18,o.unregister();case 18:console.log("Unregistered service worker:",o.scope),e.next=24;break;case 21:e.prev=21,e.t0=e.catch(15),console.error("Failed to unregister service worker:",e.t0);case 24:e.next=13;break;case 26:e.next=31;break;case 28:e.prev=28,e.t1=e.catch(11),n.e(e.t1);case 31:return e.prev=31,n.f(),e.finish(31);case 34:if(!("caches"in window)){e.next=62;break}return e.prev=35,e.next=38,caches.keys();case 38:a=e.sent,c=i(a),e.prev=40,c.s();case 42:if((u=c.n()).done){e.next=49;break}return l=u.value,e.next=46,caches.delete(l);case 46:console.log("Deleted cache:",l);case 47:e.next=42;break;case 49:e.next=54;break;case 51:e.prev=51,e.t2=e.catch(40),c.e(e.t2);case 54:return e.prev=54,c.f(),e.finish(54);case 57:e.next=62;break;case 59:e.prev=59,e.t3=e.catch(35),console.error("Failed to clear caches:",e.t3);case 62:try{localStorage.setItem("disable_sw_temp","true")}catch(e){}return e.abrupt("return",!0);case 66:return e.prev=66,e.t4=e.catch(2),console.error("Error cleaning service workers:",e.t4),e.abrupt("return",!1);case 70:case"end":return e.stop()}}),e,null,[[2,66],[11,28,31,34],[15,21],[35,59],[40,51,54,57]])})))).apply(this,arguments)}function g(){return"serviceWorker"in navigator?(console.log("Fixing WebSocket issues by checking service workers..."),new Promise((function(e){navigator.serviceWorker.getRegistrations().then(function(){var t=(0,r.A)(s().mark((function t(n){var r,o,a,c,u,l,p;return s().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(n.length>0)){t.next=63;break}console.log("Found ".concat(n.length," service worker registrations that might affect WebSockets")),r=i(n),t.prev=3,r.s();case 5:if((o=r.n()).done){t.next=19;break}return a=o.value,console.log("Unregistering service worker to fix WebSocket issues:",a.scope),t.prev=8,t.next=11,a.unregister();case 11:console.log("Successfully unregistered service worker"),t.next=17;break;case 14:t.prev=14,t.t0=t.catch(8),console.error("Failed to unregister service worker:",t.t0);case 17:t.next=5;break;case 19:t.next=24;break;case 21:t.prev=21,t.t1=t.catch(3),r.e(t.t1);case 24:return t.prev=24,r.f(),t.finish(24);case 27:if(!("caches"in window)){t.next=57;break}return t.prev=28,t.next=31,caches.keys();case 31:c=t.sent,u=i(c),t.prev=33,u.s();case 35:if((l=u.n()).done){t.next=43;break}if(!((p=l.value).includes("api")||p.includes("ws")||p.includes("socket")||p.includes("workbox"))){t.next=41;break}return console.log("Deleting potentially problematic cache:",p),t.next=41,caches.delete(p);case 41:t.next=35;break;case 43:t.next=48;break;case 45:t.prev=45,t.t2=t.catch(33),u.e(t.t2);case 48:return t.prev=48,u.f(),t.finish(48);case 51:console.log("Cache cleanup completed"),t.next=57;break;case 54:t.prev=54,t.t3=t.catch(28),console.error("Error cleaning caches:",t.t3);case 57:try{localStorage.setItem("disable_sw_temp","true"),console.log("Temporarily disabled service worker for next page load")}catch(e){console.error("Failed to set localStorage flag:",e)}console.log("WebSocket issues should be fixed. Reloading page..."),setTimeout((function(){window.location.reload()}),1e3),e(!0),t.next=65;break;case 63:console.log("No service workers found that might affect WebSockets"),e(!1);case 65:case"end":return t.stop()}}),t,null,[[3,21,24,27],[8,14],[28,54],[33,45,48,51]])})));return function(e){return t.apply(this,arguments)}}()).catch((function(t){console.error("Error while fixing WebSocket issues:",t),e(!1)}))}))):Promise.resolve(!1)}},81616:(e,t,n)=>{n.d(t,{$5:()=>E,Ic:()=>w,Q3:()=>d,Qo:()=>A,RT:()=>_,S7:()=>m,V_:()=>O,X8:()=>g,ZL:()=>f,ZP:()=>v,eL:()=>S,gA:()=>y,vr:()=>b,xx:()=>C,zp:()=>k});var r=n(10467),o=n(54756),s=n.n(o),a="ADD_COMPONENT",i="ADD_LAYOUT",c="FETCH_APP_DATA_SUCCESS",u="FETCH_APP_DATA_ERROR",l="UPDATE_COMPONENT",p="DELETE_COMPONENT",d={ADD_COMPONENT:a,ADD_LAYOUT:i,ADD_STYLE:"ADD_STYLE",ADD_DATA:"ADD_DATA",FETCH_APP_DATA_SUCCESS:c,FETCH_APP_DATA_ERROR:u,WS_CONNECT:"WS_CONNECT",WS_CONNECTED:"WS_CONNECTED",WS_DISCONNECTED:"WS_DISCONNECTED",WS_MESSAGE_RECEIVED:"WS_MESSAGE_RECEIVED",WS_ERROR:"WS_ERROR",UPDATE_COMPONENT:l,DELETE_COMPONENT:p,UPDATE_LAYOUT:"UPDATE_LAYOUT",DELETE_LAYOUT:"DELETE_LAYOUT",SAVE_APP_DATA:"SAVE_APP_DATA",LOAD_APP_DATA:"LOAD_APP_DATA",SET_LOADING:"SET_LOADING",SET_ERROR:"SET_ERROR",CLEAR_ERROR:"CLEAR_ERROR"},g=function(e){return{type:a,payload:e}},m=function(e){return{type:i,payload:{type:e,components:arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],styles:arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}}}},h=function(e){return{type:c,payload:e}},f=function(e){return function(){var t=(0,r.A)(s().mark((function t(n){var r,o;return s().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,fetch("".concat("http://localhost:8000","/api/app-data/"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});case 3:return r=t.sent,t.next=6,r.json();case 6:o=t.sent,n(h(o)),t.next=14;break;case 10:t.prev=10,t.t0=t.catch(0),console.error("Error saving app data:",t.t0),n((s=t.t0.message,{type:u,payload:s}));case 14:case"end":return t.stop()}var s}),t,null,[[0,10]])})));return function(e){return t.apply(this,arguments)}}()},v=function(e,t){return{type:l,payload:{index:e,updates:t}}},E=function(e){return{type:p,payload:{index:e}}},_=function(){return{type:"LOAD_PROJECTS"}},S=function(e){return{type:"SET_ACTIVE_PROJECT",payload:e}},b=function(e){return{type:"UPDATE_PROJECT",payload:e}},y=function(e){return{type:"CREATE_PROJECT",payload:e}},C=function(e){return{type:"DELETE_PROJECT",payload:e}},k=function(e){return{type:"ADD_THEME",payload:e}},O=function(e){return{type:"UPDATE_THEME",payload:e}},A=function(e){return{type:"REMOVE_THEME",payload:{id:e}}},w=function(e){return{type:"SET_ACTIVE_THEME",payload:e}}},93590:(e,t,n)=>{var r=n(96540),o=n(5338),s=n(71468),a=n(48035),i=n(11080),c=n(96012),u=n(49391),l=n(93385),p=n(30403),d=n(77362),g=n(23029),m=n(92901),h=n(56822),f=n(53954),v=n(85501),E=n(73437),_=n(64467);function S(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(S=function(){return!!e})()}var b=function(e){function t(e){var n,r,o,s;return(0,g.A)(this,t),r=this,o=t,o=(0,f.A)(o),(n=(0,h.A)(r,S()?Reflect.construct(o,[],(0,f.A)(r).constructor):o.apply(r,s))).url=e,n.readyState=t.CONNECTING,n.protocol="",n.extensions="",n.bufferedAmount=0,n.binaryType="blob",setTimeout((function(){n.readyState=t.OPEN;var r=new Event("open");n.dispatchEvent(r),"function"==typeof n.onopen&&n.onopen(r),console.log("Mock WebSocket connected to ".concat(e))}),500),n}return(0,v.A)(t,e),(0,m.A)(t,[{key:"send",value:function(e){var n,r=this;if(this.readyState!==t.OPEN)throw new Error("WebSocket is not open");console.log("Mock WebSocket sending data:",e);try{n="string"==typeof e?JSON.parse(e):e}catch(t){n=e}setTimeout((function(){"ping"===n.type?r._handlePing(n):"request_app_data"===n.type?r._handleAppDataRequest(n):r._handleGenericMessage(n)}),200)}},{key:"close",value:function(){var e=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";this.readyState!==t.CLOSED&&(this.readyState=t.CLOSING,setTimeout((function(){e.readyState=t.CLOSED;var o=new CloseEvent("close",{code:n,reason:r,wasClean:1e3===n});e.dispatchEvent(o),"function"==typeof e.onclose&&e.onclose(o),console.log("Mock WebSocket closed: ".concat(n," ").concat(r))}),100))}},{key:"_handlePing",value:function(e){var t={type:"pong",timestamp:(new Date).toISOString(),originalTimestamp:e.timestamp,server:"MockWebSocketServer"};this._sendMessage(t)}},{key:"_handleAppDataRequest",value:function(e){var t={type:"app_data",data:{app:{name:"App Builder",version:"1.0.0",components:[{id:1,type:"Button",props:{text:"Click Me",variant:"primary"}},{id:2,type:"Input",props:{placeholder:"Enter text",label:"Name"}},{id:3,type:"Text",props:{content:"Hello World",style:{fontWeight:"bold"}}}],layouts:[{id:1,type:"Grid",components:[1,2],styles:{gap:"10px"}},{id:2,type:"Flex",components:[3],styles:{justifyContent:"center"}}],styles:{".container":{display:"flex",flexDirection:"column",gap:"20px"},".header":{fontSize:"24px",fontWeight:"bold",marginBottom:"16px"}},status:"online"},_meta:{source:"mock-websocket",timestamp:(new Date).toISOString(),requestId:e.id||e.timestamp}},timestamp:(new Date).toISOString()};this._sendMessage(t)}},{key:"_handleGenericMessage",value:function(e){var t={type:"echo",originalMessage:e,timestamp:(new Date).toISOString(),server:"MockWebSocketServer"};this._sendMessage(t)}},{key:"_sendMessage",value:function(e){var t=JSON.stringify(e),n=new MessageEvent("message",{data:t,origin:this.url,lastEventId:"",source:null,ports:[]});this.dispatchEvent(n),"function"==typeof this.onmessage&&this.onmessage(n),console.log("Mock WebSocket received data:",e)}}])}((0,E.A)(EventTarget));(0,_.A)(b,"CONNECTING",0),(0,_.A)(b,"OPEN",1),(0,_.A)(b,"CLOSING",2),(0,_.A)(b,"CLOSED",3);var y,C=document.createElement("style");C.textContent="\n  body {\n    margin: 0;\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n      sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n\n  * {\n    box-sizing: border-box;\n  }\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n",document.head.appendChild(C);var k=document.getElementById("root");if(!k)throw new Error("Root element not found");var O=(0,o.H)(k),A=function(){return r.createElement(s.Kq,{store:a.A},r.createElement(u.OJ,null,r.createElement(p.A,null,r.createElement(l.NP,{initialTheme:"light"},r.createElement(i.Kd,null,r.createElement(c.A,null))))))};console.log("🔌 FORCING REAL WEBSOCKET MODE - Disabling all mock WebSocket functionality..."),window.WebSocket!==window._originalWebSocket&&window._originalWebSocket&&(console.log("🔌 Mock WebSocket detected - restoring real WebSocket..."),window._originalWebSocket&&(window.WebSocket=window._originalWebSocket,console.log("Mock WebSocket server disabled"))),window.WebSocket&&"MockWebSocket"===window.WebSocket.name&&(console.log("🔌 MockWebSocket class detected - forcing native WebSocket..."),window._originalWebSocket&&(window.WebSocket=window._originalWebSocket)),window.USE_REAL_API=!0,window.MOCK_SERVERS_ENABLED=!1,window.FORCE_REAL_WEBSOCKET=!0,console.log("🔌 Current WebSocket class:",(null===(y=window.WebSocket)||void 0===y?void 0:y.name)||"WebSocket"),console.log("🔌 Real API mode:",window.USE_REAL_API),console.log("🔌 Mock servers disabled:",!window.MOCK_SERVERS_ENABLED);try{console.log("🚀 Starting App Builder 201..."),console.log("🔌 Using real WebSocket connections to backend"),O.render(r.createElement(r.StrictMode,null,r.createElement(A,null))),console.log("✅ App Builder 201 loaded successfully!"),(0,d.kz)({onSuccess:function(e){console.log("✅ Service Worker registered successfully:",e)},onUpdate:function(e){console.log("🔄 Service Worker updated:",e)},onWaiting:function(){console.log("⏳ Service Worker waiting for activation")}})}catch(e){console.error("❌ Failed to load App Builder 201:",e),O.render(r.createElement("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh",fontFamily:"Arial, sans-serif",background:"#f8fafc",color:"#1f2937",textAlign:"center",padding:"2rem"}},r.createElement("div",{style:{background:"white",padding:"2rem",borderRadius:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",maxWidth:"500px"}},r.createElement("h1",{style:{margin:"0 0 1rem",color:"#dc2626"}},"App Loading Error"),r.createElement("p",{style:{margin:"0 0 1rem"}},"There was an error loading the App Builder application."),r.createElement("button",{onClick:function(){return window.location.reload()},style:{background:"#3b82f6",color:"white",border:"none",padding:"0.75rem 1.5rem",borderRadius:"6px",cursor:"pointer",fontSize:"1rem"}},"Reload Page"))))}}},e=>{e.O(0,[6649,740,4186,8014,7348,6488,7461,526,8270,2819,2136,8941,5351,6855,7979,895,9538,6756,1686,3509,7754,4270,7849,4191,4627,7005,7474,1583,773,4644,6043,9144,5849,6685,6400,2045,4540,3715,997,9184,5890,6409,8849,9879,7391,3876,8540,9862,3061,4214,7833,4673,1437,8283,3592,2403,1287,2382,1871,3861,1854,489,689,8304,286],(()=>e(e.s=93590))),e.O()}]);