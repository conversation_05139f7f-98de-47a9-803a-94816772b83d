import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import authService from '../../services/AuthService';
import LoadingState from '../LoadingState';

// Check if we're in development mode
const DEV_MODE = process.env.NODE_ENV === 'development';

/**
 * ProtectedRoute component
 * Protects routes that require authentication
 */
const ProtectedRoute = ({
  children,
  redirectTo = '/login',
  roles = [],
  loadingFallback = null
}) => {
  const [loading, setLoading] = useState(true);
  const [authenticated, setAuthenticated] = useState(false);
  const [hasRequiredRole, setHasRequiredRole] = useState(true);

  useEffect(() => {
    // In development mode, always allow access
    if (DEV_MODE) {
      console.log('Development mode: Bypassing authentication for protected route');
      setAuthenticated(true);
      setHasRequiredRole(true);
      setLoading(false);
      return;
    }

    // Check if the user is authenticated
    const isAuthenticated = authService.isAuthenticated();
    setAuthenticated(isAuthenticated);

    if (isAuthenticated && roles.length > 0) {
      // Check if the user has the required role
      const user = authService.getUser();
      const userRoles = user?.roles || [];

      // Check if the user has at least one of the required roles
      const hasRole = roles.some(role => userRoles.includes(role));
      setHasRequiredRole(hasRole);
    }

    setLoading(false);
  }, [roles]);

  // Add a listener for auth changes
  useEffect(() => {
    // Skip auth listener in development mode
    if (DEV_MODE) {
      return;
    }

    const removeListener = authService.addListener((event) => {
      if (event === 'login' || event === 'register') {
        setAuthenticated(true);

        // Check roles if needed
        if (roles.length > 0) {
          const user = authService.getUser();
          const userRoles = user?.roles || [];

          // Check if the user has at least one of the required roles
          const hasRole = roles.some(role => userRoles.includes(role));
          setHasRequiredRole(hasRole);
        }
      } else if (event === 'logout') {
        setAuthenticated(false);
        setHasRequiredRole(false);
      }
    });

    return removeListener;
  }, [roles]);

  // Show loading state
  if (loading) {
    return loadingFallback || (
      <LoadingState
        loading={true}
        message="Checking authentication..."
        fullPage={true}
      />
    );
  }

  // Redirect if not authenticated
  if (!authenticated) {
    // Add the current URL as a redirect parameter
    const redirectUrl = `${redirectTo}?redirect=${encodeURIComponent(window.location.pathname)}`;
    return <Navigate to={redirectUrl} replace />;
  }

  // Redirect if missing required role
  if (!hasRequiredRole) {
    return <Navigate to="/unauthorized" replace />;
  }

  // Render children if authenticated and has required role
  return children;
};

export default ProtectedRoute;
