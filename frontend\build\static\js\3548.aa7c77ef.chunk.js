"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[3548],{21167:(e,t,n)=>{n.r(t),n.d(t,{default:()=>m});var r=n(96540),l=n(11080),a=n(16918),i=n(35346),o=a.o5.Title,c=a.o5.Paragraph;const m=function(){return r.createElement("div",{style:{padding:"40px 20px",maxWidth:"1200px",margin:"0 auto"}},r.createElement("div",{style:{textAlign:"center",marginBottom:"60px"}},r.createElement(o,{level:1,style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",marginBottom:"20px"}},"App Builder 201 - MVP"),r.createElement(c,{style:{fontSize:"20px",color:"#666",maxWidth:"600px",margin:"0 auto"}},"Build your application with minimal setup. Start with our MVP version and create amazing apps in minutes."),r.createElement(a.$x,{size:"large",style:{marginTop:"30px"}},r.createElement(l.N_,{to:"/mvp"},r.createElement(a.$n,{type:"primary",size:"large",icon:r.createElement(i.PKb,null),style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"none",height:"50px",padding:"0 30px",fontSize:"16px"}},"Start Building Now")),r.createElement(l.N_,{to:"/app-builder"},r.createElement(a.$n,{size:"large",style:{height:"50px",padding:"0 30px"}},"Full Version")))),r.createElement(a.Fc,{message:"🚀 MVP Version Available",description:"Try our streamlined MVP version for quick app prototyping. Perfect for getting started with core features.",type:"info",showIcon:!0,style:{marginBottom:"40px"},action:r.createElement(l.N_,{to:"/mvp"},r.createElement(a.$n,{size:"small",type:"primary"},"Try MVP"))}),r.createElement(a.fI,{gutter:[32,32],justify:"center"},r.createElement(a.fv,{xs:24,sm:12,lg:8},r.createElement(a.Zp,{hoverable:!0,style:{textAlign:"center",height:"100%"},cover:r.createElement("div",{style:{padding:"40px",backgroundColor:"#f0f2f5"}},r.createElement(i.CwG,{style:{fontSize:"48px",color:"#1890ff"}}))},r.createElement(o,{level:3},"MVP Builder"),r.createElement(c,null,"Quick and simple app builder with essential features. Perfect for rapid prototyping and MVP development."),r.createElement(l.N_,{to:"/mvp"},r.createElement(a.$n,{type:"primary",size:"large"},"Start MVP")))),r.createElement(a.fv,{xs:24,sm:12,lg:8},r.createElement(a.Zp,{hoverable:!0,style:{textAlign:"center",height:"100%"},cover:r.createElement("div",{style:{padding:"40px",backgroundColor:"#f0f2f5"}},r.createElement(i.rS9,{style:{fontSize:"48px",color:"#52c41a"}}))},r.createElement(o,{level:3},"Full App Builder"),r.createElement(c,null,"Complete app builder with advanced features, themes, collaboration, and comprehensive tooling."),r.createElement(l.N_,{to:"/app-builder"},r.createElement(a.$n,{type:"primary",size:"large"},"Full Builder")))),r.createElement(a.fv,{xs:24,sm:12,lg:8},r.createElement(a.Zp,{hoverable:!0,style:{textAlign:"center",height:"100%"},cover:r.createElement("div",{style:{padding:"40px",backgroundColor:"#f0f2f5"}},r.createElement(i.xuD,{style:{fontSize:"48px",color:"#fa8c16"}}))},r.createElement(o,{level:3},"WebSocket Testing"),r.createElement(c,null,"Test real-time communication features and WebSocket connections for your applications."),r.createElement(l.N_,{to:"/websocket"},r.createElement(a.$n,{type:"primary",size:"large"},"Test WebSocket"))))),r.createElement("div",{style:{textAlign:"center",marginTop:"60px"}},r.createElement(o,{level:2},"MVP Features"),r.createElement(c,{style:{fontSize:"16px",color:"#666",marginBottom:"40px"}},"Everything you need to build your first app"),r.createElement(a.fI,{gutter:[24,24],style:{marginTop:"40px"}},r.createElement(a.fv,{xs:24,md:6},r.createElement("div",{style:{textAlign:"center"}},r.createElement(i.L0Y,{style:{fontSize:"32px",color:"#1890ff",marginBottom:"16px"}}),r.createElement(o,{level:4},"Component Creation"),r.createElement(c,null,"Add buttons, text, inputs, images, cards, and lists with simple configuration."))),r.createElement(a.fv,{xs:24,md:6},r.createElement("div",{style:{textAlign:"center"}},r.createElement(i.L0Y,{style:{fontSize:"32px",color:"#52c41a",marginBottom:"16px"}}),r.createElement(o,{level:4},"Layout Design"),r.createElement(c,null,"Organize components using Grid, Flex, and Stack layouts with responsive options."))),r.createElement(a.fv,{xs:24,md:6},r.createElement("div",{style:{textAlign:"center"}},r.createElement(i.L0Y,{style:{fontSize:"32px",color:"#fa8c16",marginBottom:"16px"}}),r.createElement(o,{level:4},"Real-time Updates"),r.createElement(c,null,"See changes instantly with WebSocket-powered live updates and collaboration."))),r.createElement(a.fv,{xs:24,md:6},r.createElement("div",{style:{textAlign:"center"}},r.createElement(i.L0Y,{style:{fontSize:"32px",color:"#722ed1",marginBottom:"16px"}}),r.createElement(o,{level:4},"Export & Save"),r.createElement(c,null,"Save your app configuration and export as JSON for further development."))))),r.createElement("div",{style:{textAlign:"center",marginTop:"60px",padding:"40px",background:"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",borderRadius:"12px"}},r.createElement(o,{level:2},"Ready to Build Your App?"),r.createElement(c,{style:{fontSize:"18px",marginBottom:"30px"}},"Start with our MVP version and create your first app in minutes."),r.createElement(l.N_,{to:"/mvp"},r.createElement(a.$n,{type:"primary",size:"large",icon:r.createElement(i.PKb,null),style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"none",height:"50px",padding:"0 40px",fontSize:"16px"}},"Launch MVP Builder"))))}}}]);