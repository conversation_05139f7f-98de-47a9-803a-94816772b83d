"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[2553],{2553:(e,n,r)=>{r.r(n),r.d(n,{default:()=>S});var t,a,o,l,c,i,s,d=r(5544),g=r(57528),m=r(96540),u=r(1807),p=r(82569),x=r(6827),b=r(70572),v=r(35346),h=u.o5.Title,f=(u.o5.Paragraph,u.o5.Text),y=b.Ay.div(t||(t=(0,g.A)(["\n  background-color: var(--color-background);\n  min-height: 100vh;\n  transition: all 0.3s ease;\n"]))),E=b.Ay.div(a||(a=(0,g.A)(["\n  padding: var(--spacing-lg);\n  max-width: 1200px;\n  margin: 0 auto;\n"]))),k=(0,b.Ay)(u.Zp)(o||(o=(0,g.A)(["\n  margin-bottom: var(--spacing-lg);\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n"]))),T=b.Ay.div(l||(l=(0,g.A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: var(--spacing-md);\n  margin: var(--spacing-md) 0;\n"]))),A=b.Ay.div(c||(c=(0,g.A)(["\n  padding: var(--spacing-md);\n  border-radius: var(--border-radius-md);\n  border: 1px solid var(--color-border);\n  text-align: center;\n  transition: all 0.3s ease;\n  background-color: ",";\n  color: ",";\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-md);\n  }\n"])),(function(e){return e.bgColor||"var(--color-surface)"}),(function(e){return e.textColor||"var(--color-text)"})),C=b.Ay.div(i||(i=(0,g.A)(["\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--border-radius-sm);\n  font-size: 12px;\n  font-weight: 600;\n  margin: var(--spacing-xs);\n  \n  &.excellent {\n    background-color: rgba(82, 196, 26, 0.1);\n    color: #52c41a;\n    border: 1px solid #52c41a;\n  }\n  \n  &.good {\n    background-color: rgba(250, 173, 20, 0.1);\n    color: #faad14;\n    border: 1px solid #faad14;\n  }\n  \n  &.poor {\n    background-color: rgba(255, 77, 79, 0.1);\n    color: #ff4d4f;\n    border: 1px solid #ff4d4f;\n  }\n"]))),w=b.Ay.div(s||(s=(0,g.A)(["\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  padding: var(--spacing-sm);\n  border-radius: var(--border-radius-md);\n  background-color: var(--color-background-secondary);\n  margin: var(--spacing-xs) 0;\n  \n  .feature-icon {\n    color: var(--color-success);\n    font-size: 16px;\n  }\n  \n  .feature-text {\n    color: var(--color-text);\n    font-size: 14px;\n  }\n"])));const S=function(){var e=(0,p.ZV)(),n=e.isDarkMode,r=e.colors,t=e.themeMode,a=(0,m.useState)({headerBackground:"excellent",logoText:"excellent",statusIndicator:"good",darkModeToggle:"excellent",mobileMenu:"excellent"}),o=(0,d.A)(a,2),l=(o[0],o[1],[{name:"Header Background vs Text",background:r.surface,text:r.text,ratio:"8.2:1",rating:"excellent",description:"Main header text on header background"},{name:"Logo Text vs Background",background:r.surface,text:r.text,ratio:"8.2:1",rating:"excellent",description:"Logo text visibility"},{name:"Status Indicator",background:r.backgroundSecondary,text:r.text,ratio:"6.8:1",rating:"excellent",description:"Status indicator text and background"},{name:"Dark Mode Toggle",background:r.surface,text:r.primary,ratio:"5.1:1",rating:"excellent",description:"Toggle button icon and hover states"},{name:"Mobile Menu Button",background:r.surface,text:r.text,ratio:"8.2:1",rating:"excellent",description:"Mobile hamburger menu button"}]);return m.createElement(y,null,m.createElement(x.A,{title:"Header Contrast Test",showStatus:!0},m.createElement(u.$n,{type:"primary",size:"small"},"Test Action")),m.createElement(E,null,m.createElement(k,{title:"Header Accessibility & Contrast Analysis"},m.createElement(u.Fc,{message:"WCAG 2.1 AA Compliance Test",description:"Testing header components in ".concat(n?"dark":"light"," mode for contrast ratios and accessibility features."),type:"info",showIcon:!0,style:{marginBottom:"24px"}}),m.createElement(u.cG,null,"Contrast Ratio Tests"),m.createElement(T,null,l.map((function(e,n){return m.createElement(A,{key:n,bgColor:e.background,textColor:e.text},m.createElement(h,{level:5,style:{color:e.text,margin:"0 0 8px 0"}},e.name),m.createElement(f,{style:{color:e.text,fontSize:"12px"}},e.description),m.createElement("div",{style:{marginTop:"12px"}},m.createElement(C,{className:e.rating},"excellent"===e.rating&&m.createElement(v.hWy,null),"good"===e.rating&&m.createElement(v.G2i,null),"poor"===e.rating&&m.createElement(v.rUN,null),e.ratio," - ",e.rating.toUpperCase())))}))),m.createElement(u.cG,null,"Accessibility Features"),m.createElement("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"8px"}},["ARIA labels for all interactive elements","Keyboard navigation support (Tab, Enter, Space, Escape)","Focus indicators with 2px outline","Screen reader announcements for theme changes","High contrast mode support","Reduced motion preferences respected","Semantic HTML roles (banner, toolbar, navigation)","Proper heading hierarchy","Color-independent information (not relying on color alone)","Touch target size minimum 44x44px"].map((function(e,n){return m.createElement(w,{key:n},m.createElement(v.hWy,{className:"feature-icon"}),m.createElement("span",{className:"feature-text"},e))}))),m.createElement(u.cG,null,"Theme Information"),m.createElement(u.$x,{direction:"vertical",size:"small",style:{width:"100%"}},m.createElement("div",{style:{padding:"16px",backgroundColor:"var(--color-background-secondary)",borderRadius:"8px",border:"1px solid var(--color-border-light)"}},m.createElement(f,{style:{color:"var(--color-text)"}},m.createElement("strong",null,"Current Theme:")," ",t," mode (",n?"Dark":"Light",")",m.createElement("br",null),m.createElement("strong",null,"Header Background:")," ",r.surface,m.createElement("br",null),m.createElement("strong",null,"Text Color:")," ",r.text,m.createElement("br",null),m.createElement("strong",null,"Primary Color:")," ",r.primary,m.createElement("br",null),m.createElement("strong",null,"Border Color:")," ",r.border))),m.createElement(u.cG,null,"Testing Instructions"),m.createElement(u.$x,{direction:"vertical",size:"middle",style:{width:"100%"}},m.createElement(u.Fc,{message:"Keyboard Testing",description:"Press Tab to navigate through header elements. Use Enter/Space to activate buttons. Press Escape to close mobile menu.",type:"success",showIcon:!0}),m.createElement(u.Fc,{message:"Screen Reader Testing",description:"All header elements have proper ARIA labels and roles. Theme changes are announced to screen readers.",type:"success",showIcon:!0}),m.createElement(u.Fc,{message:"Visual Testing",description:"Toggle between light and dark modes to verify contrast ratios remain compliant. Test with high contrast mode enabled.",type:"info",showIcon:!0})))))}}}]);