version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    volumes:
      - ./backend:/usr/src/app
      - ./frontend/build:/usr/src/app/frontend/build:ro
    command: bash -c "/usr/src/app/start.sh"
    environment:
      - DJANGO_SETTINGS_MODULE=app_builder_201.settings
    depends_on:
      - db
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    mem_limit: 512m
    memswap_limit: 512m
    ports:
      - "8000:8000"
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8000/health/" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    volumes:
      - ./frontend:/app:cached
      - frontend_node_modules:/app/node_modules
    environment:
      - NODE_ENV=development
      - REACT_APP_API_BASE_URL=http://localhost:8000
      - REACT_APP_API_URL=
      - REACT_APP_BACKEND_HOST=backend
      - API_TARGET=http://backend:8000
      - WEBSOCKET_TARGET=http://backend:8000
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
      - FAST_REFRESH=false
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:3000/" ]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 60s

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: myappuser
      POSTGRES_PASSWORD: myapppassword
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U myappuser -d myapp" ]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
  frontend_node_modules:
