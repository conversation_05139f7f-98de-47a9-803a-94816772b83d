import * as types from '../actionTypes';
import api from '../../services/apiService';
import webSocketService from '../../services/WebSocketService';

// App initialization
export const initializeApp = () => async (dispatch) => {
  dispatch({ type: types.APP_INITIALIZATION_START });

  try {
    // Check API status
    const statusData = await api.checkStatus();
    dispatch({
      type: types.API_STATUS_SUCCESS,
      payload: statusData
    });

    // Initialize WebSocket if API is available
    dispatch(initializeWebSocket());

    // Fetch initial app data
    dispatch(fetchAppData());

    dispatch({ type: types.APP_INITIALIZATION_SUCCESS });
  } catch (error) {
    console.error('App initialization error:', error);
    dispatch({
      type: types.APP_INITIALIZATION_ERROR,
      payload: error.message
    });

    // Try to load from cache if available
    dispatch(loadFromCache());
  }
};

// App data actions
export const loadAppData = () => (dispatch) => {
  // Alias for fetchAppData for backward compatibility
  return fetchAppData()(dispatch);
};

export const fetchAppData = () => (dispatch) => {
  dispatch({ type: types.FETCH_APP_DATA_REQUEST });

  // First check if we have cached data to show immediately
  const cachedData = localStorage.getItem('app_data_cache');
  if (cachedData) {
    try {
      const parsedData = JSON.parse(cachedData);
      dispatch({
        type: types.FETCH_APP_DATA_SUCCESS,
        payload: parsedData
      });
      // Mark as using cached data
      dispatch({ type: types.USING_CACHED_DATA, payload: true });
    } catch (e) {
      console.warn('Failed to parse cached data');
    }
  }

  // Try WebSocket if connected - with null check
  if (webSocketService && typeof webSocketService.isConnected === 'function' && webSocketService.isConnected()) {
    webSocketService.send({
      type: 'request_app_data'
    });

    // Set a timeout to fall back to HTTP if WebSocket doesn't respond
    const timeoutId = setTimeout(() => {
      console.log('WebSocket request timed out, falling back to HTTP');
      fetchAppDataHttp()(dispatch);
    }, 3000);

    dispatch({
      type: types.WEBSOCKET_REQUEST_SENT,
      payload: { requestType: 'app_data', timeoutId }
    });
  } else {
    // Fall back to HTTP if WebSocket is not connected
    fetchAppDataHttp()(dispatch);
  }
};

// Fallback HTTP method for fetching app data
const fetchAppDataHttp = () => async (dispatch) => {
  try {
    dispatch({ type: types.FETCH_APP_DATA_HTTP_REQUEST });

    const data = await api.getAppData();

    dispatch({
      type: types.FETCH_APP_DATA_SUCCESS,
      payload: data
    });
  } catch (error) {
    console.error('Error fetching app data via HTTP:', error);
    dispatch({
      type: types.FETCH_APP_DATA_ERROR,
      payload: error.message
    });
  }
};

// Load from cache if available
const loadFromCache = () => (dispatch) => {
  try {
    const cachedData = localStorage.getItem('app_data_cache');

    if (cachedData) {
      const data = JSON.parse(cachedData);
      const cacheTimestamp = localStorage.getItem('app_data_cache_timestamp');

      dispatch({
        type: types.LOAD_FROM_CACHE_SUCCESS,
        payload: {
          data,
          timestamp: cacheTimestamp ? new Date(parseInt(cacheTimestamp)) : null
        }
      });

      return true;
    }

    return false;
  } catch (error) {
    console.error('Error loading from cache:', error);
    dispatch({
      type: types.LOAD_FROM_CACHE_ERROR,
      payload: error.message
    });

    return false;
  }
};

export const saveAppData = (appData) => (dispatch) => {
  dispatch({ type: types.SAVE_APP_DATA_REQUEST });

  // Try WebSocket first
  if (webSocketService.isConnected) {
    webSocketService.updateAppData(appData);
    dispatch({
      type: types.SAVE_APP_DATA_SUCCESS,
      payload: appData
    });
  } else {
    // Fall back to HTTP if WebSocket is not connected
    saveAppDataHttp(appData)(dispatch);
  }
};

// Fallback HTTP method for saving app data
const saveAppDataHttp = (appData) => async (dispatch) => {
  try {
    const response = await fetch('/api/save_app_data/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(appData)
    });

    if (!response.ok) {
      throw new Error('Failed to save app data');
    }

    const data = await response.json();

    dispatch({
      type: types.SAVE_APP_DATA_SUCCESS,
      payload: data
    });
  } catch (error) {
    dispatch({
      type: types.SAVE_APP_DATA_FAILURE,
      payload: error.message
    });
  }
};

// Component actions
export const addComponent = (component) => (dispatch) => {
  // Send via WebSocket if connected
  if (webSocketService.isConnected) {
    webSocketService.addComponent(component);
  }

  // Update local state immediately
  dispatch({
    type: types.ADD_COMPONENT,
    payload: component
  });
};

export const updateComponent = (componentId, props) => ({
  type: types.UPDATE_COMPONENT,
  payload: { id: componentId, props }
});

export const removeComponent = (componentId) => ({
  type: types.REMOVE_COMPONENT,
  payload: { id: componentId }
});

// Alias for backward compatibility
export const deleteComponent = removeComponent;

// Layout actions
export const addLayout = (layoutType, components = [], styles = {}) => (dispatch) => {
  const layout = { type: layoutType, components, styles };

  // Send via WebSocket if connected
  if (webSocketService.isConnected) {
    webSocketService.addLayout(layout);
  }

  // Update local state immediately
  dispatch({
    type: types.ADD_LAYOUT,
    payload: layout
  });
};

export const updateLayout = (layoutId, data) => ({
  type: types.UPDATE_LAYOUT,
  payload: { id: layoutId, ...data }
});

export const removeLayout = (layoutId) => ({
  type: types.REMOVE_LAYOUT,
  payload: { id: layoutId }
});

// Alias for backward compatibility
export const deleteLayout = removeLayout;

// Style actions
export const addStyle = (selector, style) => (dispatch) => {
  const styleData = { selector, style };

  // Send via WebSocket if connected
  if (webSocketService.isConnected) {
    webSocketService.addStyle(styleData);
  }

  // Update local state immediately
  dispatch({
    type: types.ADD_STYLE,
    payload: styleData
  });
};

export const updateStyle = (selector, style) => ({
  type: types.UPDATE_STYLE,
  payload: { selector, style }
});

export const removeStyle = (selector) => ({
  type: types.REMOVE_STYLE,
  payload: { selector }
});

// Data actions
export const addData = (key, value) => ({
  type: types.ADD_DATA,
  payload: { key, value }
});

export const updateData = (key, value) => ({
  type: types.UPDATE_DATA,
  payload: { key, value }
});

export const removeData = (key) => ({
  type: types.REMOVE_DATA,
  payload: { key }
});

// Theme actions
export const addTheme = (theme) => ({
  type: types.ADD_THEME,
  payload: theme
});

export const updateTheme = (theme) => ({
  type: types.UPDATE_THEME,
  payload: theme
});

export const removeTheme = (themeId) => ({
  type: types.REMOVE_THEME,
  payload: { id: themeId }
});

export const setActiveTheme = (themeId) => ({
  type: types.SET_ACTIVE_THEME,
  payload: themeId
});

// WebSocket actions
export const websocketConnected = () => ({
  type: types.WEBSOCKET_CONNECTED
});

export const websocketDisconnected = () => ({
  type: types.WEBSOCKET_DISCONNECTED
});

export const websocketMessageReceived = (message) => ({
  type: types.WS_MESSAGE_RECEIVED,
  payload: message
});

// API Keys actions
export const fetchApiKeys = () => async (dispatch) => {
  dispatch({ type: types.FETCH_API_KEYS_REQUEST });

  try {
    const response = await fetch('/api/api-keys/', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error('Failed to fetch API keys');
    }

    const data = await response.json();

    dispatch({
      type: types.FETCH_API_KEYS_SUCCESS,
      payload: data.keys
    });

    return data.keys;
  } catch (error) {
    dispatch({
      type: types.FETCH_API_KEYS_FAILURE,
      payload: error.message
    });

    throw error;
  }
};

export const validateApiKey = (keyName, keyValue) => async (dispatch) => {
  dispatch({ type: types.VALIDATE_API_KEY_REQUEST });

  try {
    const response = await fetch('/api/validate-api-key/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ key_name: keyName, key_value: keyValue }),
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error('Failed to validate API key');
    }

    const data = await response.json();

    dispatch({
      type: types.VALIDATE_API_KEY_SUCCESS,
      payload: { keyName, valid: data.valid }
    });

    return data.valid;
  } catch (error) {
    dispatch({
      type: types.VALIDATE_API_KEY_FAILURE,
      payload: error.message
    });

    throw error;
  }
};

// AI actions
export const generateAiSuggestions = (prompt) => async (dispatch) => {
  dispatch({ type: types.GENERATE_AI_SUGGESTIONS_REQUEST });

  try {
    const response = await fetch('/api/generate_ai_suggestions/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ prompt })
    });

    if (!response.ok) {
      throw new Error('Failed to generate AI suggestions');
    }

    const data = await response.json();

    dispatch({
      type: types.GENERATE_AI_SUGGESTIONS_SUCCESS,
      payload: data.suggestions
    });

    return data.suggestions;
  } catch (error) {
    dispatch({
      type: types.GENERATE_AI_SUGGESTIONS_FAILURE,
      payload: error.message
    });

    throw error;
  }
};

export const generateImage = (prompt) => async (dispatch) => {
  dispatch({ type: types.GENERATE_IMAGE_REQUEST });

  try {
    const response = await fetch('/api/generate_image/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ prompt })
    });

    if (!response.ok) {
      throw new Error('Failed to generate image');
    }

    const data = await response.json();

    dispatch({
      type: types.GENERATE_IMAGE_SUCCESS,
      payload: data.image_url
    });

    return data.image_url;
  } catch (error) {
    dispatch({
      type: types.GENERATE_IMAGE_FAILURE,
      payload: error.message
    });

    throw error;
  }
};

// Add a function to cache app data for offline use
export const cacheAppData = (data) => {
  try {
    localStorage.setItem('app_data_cache', JSON.stringify(data));
    localStorage.setItem('app_data_cache_timestamp', Date.now().toString());
  } catch (error) {
    console.error('Error caching app data:', error);
  }
};





